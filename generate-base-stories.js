#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Get all base component names from the directory listing
const allBaseNames = [
  'accordian', 'autocomplete', 'autocomplete-item', 'avatar', 'breadcrumb',
  'button', 'button-action', 'button-close', 'button-icon', 'card',
  'checkbox', 'checkbox-animated', 'container', 'datepicker', 'divider',
  'dropdown', 'dropdown-divider', 'dropdown-item', 'heading', 'icon',
  'image', 'input', 'kbd', 'link', 'list', 'listbox', 'logo', 'message',
  'mobile', 'object', 'pagination', 'paragraph', 'picture', 'placeholder-page',
  'placeload', 'progress', 'prose', 'radio', 'select', 'snack',
  'switch-ball', 'switch-thin', 'table', 'tabs', 'tag', 'text', 'textarea'
];

// Component categorization and priority mapping
const baseCategories = {
  // Form Components - High Priority
  'autocomplete': { category: 'form', complexity: 6, priority: 'High' },
  'autocomplete-item': { category: 'form', complexity: 3, priority: 'High' },
  'checkbox': { category: 'form', complexity: 3, priority: 'High' },
  'checkbox-animated': { category: 'form', complexity: 4, priority: 'Medium' },
  'datepicker': { category: 'form', complexity: 7, priority: 'High' },
  'input': { category: 'form', complexity: 4, priority: 'High' },
  'radio': { category: 'form', complexity: 3, priority: 'High' },
  'select': { category: 'form', complexity: 5, priority: 'High' },
  'switch-ball': { category: 'form', complexity: 4, priority: 'Medium' },
  'switch-thin': { category: 'form', complexity: 4, priority: 'Medium' },
  'textarea': { category: 'form', complexity: 4, priority: 'High' },

  // Button Components - High Priority
  'button': { category: 'interactive', complexity: 3, priority: 'High' },
  'button-action': { category: 'interactive', complexity: 4, priority: 'High' },
  'button-close': { category: 'interactive', complexity: 2, priority: 'High' },
  'button-icon': { category: 'interactive', complexity: 3, priority: 'High' },

  // Navigation Components
  'breadcrumb': { category: 'navigation', complexity: 4, priority: 'High' },
  'dropdown': { category: 'navigation', complexity: 6, priority: 'High' },
  'dropdown-divider': { category: 'navigation', complexity: 2, priority: 'Medium' },
  'dropdown-item': { category: 'navigation', complexity: 3, priority: 'High' },
  'link': { category: 'navigation', complexity: 3, priority: 'High' },
  'pagination': { category: 'navigation', complexity: 5, priority: 'Medium' },
  'tabs': { category: 'navigation', complexity: 5, priority: 'High' },

  // Layout Components
  'accordian': { category: 'layout', complexity: 5, priority: 'Medium' },
  'card': { category: 'layout', complexity: 4, priority: 'High' },
  'container': { category: 'layout', complexity: 3, priority: 'High' },
  'divider': { category: 'layout', complexity: 2, priority: 'Medium' },
  'list': { category: 'layout', complexity: 4, priority: 'High' },
  'listbox': { category: 'layout', complexity: 4, priority: 'Medium' },
  'table': { category: 'layout', complexity: 6, priority: 'High' },

  // Display Components
  'avatar': { category: 'display', complexity: 3, priority: 'High' },
  'heading': { category: 'display', complexity: 3, priority: 'High' },
  'icon': { category: 'display', complexity: 3, priority: 'High' },
  'image': { category: 'display', complexity: 4, priority: 'High' },
  'kbd': { category: 'display', complexity: 2, priority: 'Low' },
  'logo': { category: 'display', complexity: 3, priority: 'Medium' },
  'message': { category: 'display', complexity: 4, priority: 'Medium' },
  'paragraph': { category: 'display', complexity: 3, priority: 'High' },
  'picture': { category: 'display', complexity: 4, priority: 'Medium' },
  'placeholder-page': { category: 'display', complexity: 3, priority: 'Low' },
  'placeload': { category: 'display', complexity: 3, priority: 'Low' },
  'progress': { category: 'display', complexity: 4, priority: 'Medium' },
  'prose': { category: 'display', complexity: 4, priority: 'Medium' },
  'snack': { category: 'display', complexity: 4, priority: 'Medium' },
  'tag': { category: 'display', complexity: 3, priority: 'High' },
  'text': { category: 'display', complexity: 3, priority: 'High' },

  // Special Components
  'mobile': { category: 'special', complexity: 5, priority: 'Low' },
  'object': { category: 'special', complexity: 4, priority: 'Low' }
};

// Story template generator
function generateStoryContent(componentName, index) {
  const storyNumber = String(index + 1).padStart(3, '0');
  const titleCase = componentName.split('-').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');
  
  const component = baseCategories[componentName] || { category: 'interactive', complexity: 3, priority: 'Medium' };

  return `# Story MCB-${storyNumber}: Enhance ${titleCase} Base Component

## User Story
As a developer using the LP-GO builder, I want the ${titleCase} base component to have full Tailwind class customization and proper input properties so that I can configure and style it dynamically.

## Current State Analysis
- Component needs comprehensive enhancement for dynamic usage
- Missing proper @Input() properties for data binding
- Lacks Tailwind class customization inputs
- Requires default values for visual preview
- Category: ${component.category}

## Acceptance Criteria
- [ ] Add comprehensive @Input() properties for all configurable data
- [ ] Add \`className\` input for custom Tailwind classes
- [ ] Add \`size\` input with appropriate size options
- [ ] Add \`variant\` input for color/style schemes
- [ ] Add \`rounded\` input for border radius options
- [ ] Implement meaningful default values for preview
- [ ] Add proper TypeScript typing for all inputs
- [ ] Update component template with computed classes
- [ ] Ensure component is standalone
- [ ] Add proper module registration if needed

## Required Standard Inputs
\`\`\`typescript
@Input() className: string = '';
@Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
@Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
@Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
// Additional component-specific inputs to be defined during implementation
\`\`\`

## Component-Specific Requirements
- Analyze existing component template and functionality
- Define appropriate data inputs based on component purpose
- Implement component-specific logic and features
- Add event outputs where user interaction is expected
- Ensure accessibility standards are met

## Default Visual Appearance
- Component should render with meaningful default data
- All styling should have sensible defaults
- Visual preview should represent component's purpose clearly

## Implementation Tasks
1. Convert to standalone component (if not already)
2. Add all required @Input() properties
3. Implement computed classes for Tailwind customization
4. Update template with proper data binding
5. Add default values for all inputs
6. Update TypeScript types and interfaces
7. Test component rendering and functionality
8. Update module registration if needed

## Definition of Done
- Component uses @Input() decorators exclusively
- All Tailwind class inputs are functional
- Default values provide meaningful visual preview
- TypeScript compilation successful without errors
- Component renders correctly in isolation
- Component follows established patterns from enhanced components
- Accessibility requirements met
- Unit tests pass

---
**Story Points**: ${component.complexity}  
**Priority**: ${component.priority}  
**Category**: ${component.category}  
**Component Path**: \`/projects/mobile-components/src/lib/base/${componentName}/\``;
}

// Create stories directory
const storiesDir = '/Users/<USER>/Projects/clients/lp-angular/docs/stories/base-enhancement';

if (!fs.existsSync(storiesDir)) {
  fs.mkdirSync(storiesDir, { recursive: true });
}

// Generate all story files
allBaseNames.forEach((componentName, index) => {
  const storyNumber = String(index + 1).padStart(3, '0');
  const filename = `MCB-${storyNumber}-${componentName}.md`;
  const filepath = path.join(storiesDir, filename);
  const content = generateStoryContent(componentName, index);
  
  try {
    fs.writeFileSync(filepath, content);
    console.log(`Created story: ${filename}`);
  } catch (error) {
    console.error(`Error creating ${filename}:`, error.message);
  }
});

console.log(`\n✅ Generated ${allBaseNames.length} base component enhancement stories`);
console.log(`📁 Stories saved to: ${storiesDir}`);
console.log(`\n📊 Story Distribution:`);

const categories = {};
allBaseNames.forEach(name => {
  const component = baseCategories[name] || { category: 'interactive' };
  categories[component.category] = (categories[component.category] || 0) + 1;
});

Object.entries(categories).forEach(([category, count]) => {
  console.log(`   ${category}: ${count} stories`);
});