#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Get all widget names from the directory listing
const allWidgetNames = [
  'account-balance', 'action-text', 'authors-list-compact', 'avatar-group-id', 
  'avatar-group', 'button-group', 'card-filters', 'comment-list-compact',
  'company-overview', 'datepicker', 'dynamic-list', 'features', 'file-list-tabbed',
  'filter', 'flex-table-cell', 'flex-table-heading', 'flex-table-row',
  'flex-table-start', 'flex-table-wrapper', 'flex-table', 'focus-loop',
  'followers-compact', 'fullscreen-dropfile', 'icon-box', 'icon-text',
  'image-gallery', 'image-links', 'inbox-message', 'info-badges', 'info-image',
  'list-item', 'listbox-item', 'map-marker', 'menu-icon-list', 'modal-footer',
  'modal-large-tier', 'modal-medium-tier', 'modal-small-tier', 'placeholder-compact',
  'placeholder-minimal', 'progress-circle', 'quill', 'search-compact', 'search-tag',
  'search', 'select-multi', 'tab-slider', 'tabbed-content', 'tag-list-compact',
  'tags', 'timeline-compact', 'tree-select-item', 'tree-select', 'upload-avatar',
  'upload-input', 'upload', 'vcard-right', 'video-compact', 'welcome'
];

// Widget categorization and priority mapping
const widgetCategories = {
  // Form Components - High Priority
  'account-balance': { category: 'form', complexity: 3, priority: 'High' },
  'action-text': { category: 'form', complexity: 3, priority: 'High' },
  'datepicker': { category: 'form', complexity: 5, priority: 'High' },
  'filter': { category: 'form', complexity: 4, priority: 'Medium' },
  'quill': { category: 'form', complexity: 8, priority: 'Medium' },
  'search': { category: 'form', complexity: 4, priority: 'High' },
  'search-compact': { category: 'form', complexity: 3, priority: 'High' },
  'search-tag': { category: 'form', complexity: 4, priority: 'Medium' },
  'select-multi': { category: 'form', complexity: 5, priority: 'High' },
  'tree-select': { category: 'form', complexity: 8, priority: 'Medium' },
  'tree-select-item': { category: 'form', complexity: 5, priority: 'Medium' },
  'upload': { category: 'form', complexity: 6, priority: 'Medium' },
  'upload-avatar': { category: 'form', complexity: 5, priority: 'Medium' },
  'upload-input': { category: 'form', complexity: 4, priority: 'Medium' },
  'fullscreen-dropfile': { category: 'form', complexity: 6, priority: 'Low' },

  // Display Components
  'authors-list-compact': { category: 'display', complexity: 5, priority: 'High' },
  'avatar-group': { category: 'display', complexity: 4, priority: 'High' },
  'avatar-group-id': { category: 'display', complexity: 4, priority: 'Medium' },
  'card-filters': { category: 'display', complexity: 5, priority: 'Medium' },
  'comment-list-compact': { category: 'display', complexity: 6, priority: 'Medium' },
  'company-overview': { category: 'display', complexity: 5, priority: 'Medium' },
  'dynamic-list': { category: 'display', complexity: 7, priority: 'High' },
  'features': { category: 'display', complexity: 4, priority: 'Medium' },
  'file-list-tabbed': { category: 'display', complexity: 6, priority: 'Medium' },
  'followers-compact': { category: 'display', complexity: 4, priority: 'Medium' },
  'icon-box': { category: 'display', complexity: 3, priority: 'High' },
  'icon-text': { category: 'display', complexity: 3, priority: 'High' },
  'info-badges': { category: 'display', complexity: 4, priority: 'Medium' },
  'info-image': { category: 'display', complexity: 3, priority: 'Medium' },
  'list-item': { category: 'display', complexity: 4, priority: 'High' },
  'listbox-item': { category: 'display', complexity: 4, priority: 'Medium' },
  'placeholder-compact': { category: 'display', complexity: 2, priority: 'Low' },
  'placeholder-minimal': { category: 'display', complexity: 2, priority: 'Low' },
  'tag-list-compact': { category: 'display', complexity: 4, priority: 'Medium' },
  'timeline-compact': { category: 'display', complexity: 5, priority: 'Medium' },

  // Navigation Components
  'button-group': { category: 'navigation', complexity: 4, priority: 'High' },
  'menu-icon-list': { category: 'navigation', complexity: 5, priority: 'Medium' },
  'tab-slider': { category: 'navigation', complexity: 5, priority: 'High' },
  'tabbed-content': { category: 'navigation', complexity: 6, priority: 'High' },
  'tags': { category: 'navigation', complexity: 4, priority: 'Medium' },
  'focus-loop': { category: 'navigation', complexity: 7, priority: 'Low' },
  'map-marker': { category: 'navigation', complexity: 4, priority: 'Low' },
  'progress-circle': { category: 'navigation', complexity: 3, priority: 'Medium' },

  // Layout Components
  'flex-table': { category: 'layout', complexity: 6, priority: 'High' },
  'flex-table-cell': { category: 'layout', complexity: 3, priority: 'High' },
  'flex-table-heading': { category: 'layout', complexity: 3, priority: 'High' },
  'flex-table-row': { category: 'layout', complexity: 3, priority: 'High' },
  'flex-table-start': { category: 'layout', complexity: 3, priority: 'High' },
  'flex-table-wrapper': { category: 'layout', complexity: 3, priority: 'High' },
  'modal-footer': { category: 'layout', complexity: 3, priority: 'Medium' },
  'modal-large-tier': { category: 'layout', complexity: 4, priority: 'Medium' },
  'modal-medium-tier': { category: 'layout', complexity: 4, priority: 'Medium' },
  'modal-small-tier': { category: 'layout', complexity: 4, priority: 'Medium' },
  'inbox-message': { category: 'layout', complexity: 5, priority: 'Medium' },
  'welcome': { category: 'layout', complexity: 3, priority: 'Low' },

  // Media Components
  'image-gallery': { category: 'media', complexity: 6, priority: 'Medium' },
  'image-links': { category: 'media', complexity: 4, priority: 'Medium' },
  'video-compact': { category: 'media', complexity: 5, priority: 'Medium' },
  'vcard-right': { category: 'media', complexity: 4, priority: 'Low' }
};

// Story template generator
function generateStoryContent(widgetName, index) {
  const storyNumber = String(index + 1).padStart(3, '0');
  const titleCase = widgetName.split('-').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');
  
  const widget = widgetCategories[widgetName] || { category: 'interactive', complexity: 3, priority: 'Medium' };

  return `# Story MC-${storyNumber}: Enhance ${titleCase} Widget

## User Story
As a developer using the LP-GO builder, I want the ${titleCase} widget to have full Tailwind class customization and proper input properties so that I can configure and style it dynamically.

## Current State Analysis
- Component needs comprehensive enhancement for dynamic usage
- Missing proper @Input() properties for data binding
- Lacks Tailwind class customization inputs
- Requires default values for visual preview
- Category: ${widget.category}

## Acceptance Criteria
- [ ] Add comprehensive @Input() properties for all configurable data
- [ ] Add \`className\` input for custom Tailwind classes
- [ ] Add \`size\` input with appropriate size options
- [ ] Add \`variant\` input for color/style schemes
- [ ] Add \`rounded\` input for border radius options
- [ ] Implement meaningful default values for preview
- [ ] Add proper TypeScript typing for all inputs
- [ ] Update component template with computed classes
- [ ] Ensure component is standalone
- [ ] Add proper module registration if needed

## Required Standard Inputs
\`\`\`typescript
@Input() className: string = '';
@Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
@Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
@Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
// Additional component-specific inputs to be defined during implementation
\`\`\`

## Component-Specific Requirements
- Analyze existing component template and functionality
- Define appropriate data inputs based on component purpose
- Implement component-specific logic and features
- Add event outputs where user interaction is expected
- Ensure accessibility standards are met

## Default Visual Appearance
- Component should render with meaningful default data
- All styling should have sensible defaults
- Visual preview should represent component's purpose clearly

## Implementation Tasks
1. Convert to standalone component (if not already)
2. Add all required @Input() properties
3. Implement computed classes for Tailwind customization
4. Update template with proper data binding
5. Add default values for all inputs
6. Update TypeScript types and interfaces
7. Test component rendering and functionality
8. Update module registration if needed

## Definition of Done
- Component uses @Input() decorators exclusively
- All Tailwind class inputs are functional
- Default values provide meaningful visual preview
- TypeScript compilation successful without errors
- Component renders correctly in isolation
- Component follows established patterns from enhanced components
- Accessibility requirements met
- Unit tests pass

---
**Story Points**: ${widget.complexity}  
**Priority**: ${widget.priority}  
**Category**: ${widget.category}  
**Component Path**: \`/projects/mobile-components/src/lib/widgets/${widgetName}/\``;
}

// Create stories directory
const storiesDir = '/Users/<USER>/Projects/clients/lp-angular/docs/stories/mobile-components-enhancement';

if (!fs.existsSync(storiesDir)) {
  fs.mkdirSync(storiesDir, { recursive: true });
}

// Generate all story files
allWidgetNames.forEach((widgetName, index) => {
  const storyNumber = String(index + 1).padStart(3, '0');
  const filename = `MC-${storyNumber}-${widgetName}.md`;
  const filepath = path.join(storiesDir, filename);
  const content = generateStoryContent(widgetName, index);
  
  try {
    fs.writeFileSync(filepath, content);
    console.log(`Created story: ${filename}`);
  } catch (error) {
    console.error(`Error creating ${filename}:`, error.message);
  }
});

console.log(`\n✅ Generated ${allWidgetNames.length} widget enhancement stories`);
console.log(`📁 Stories saved to: ${storiesDir}`);
console.log(`\n📊 Story Distribution:`);

const categories = {};
allWidgetNames.forEach(name => {
  const widget = widgetCategories[name] || { category: 'interactive' };
  categories[widget.category] = (categories[widget.category] || 0) + 1;
});

Object.entries(categories).forEach(([category, count]) => {
  console.log(`   ${category}: ${count} stories`);
});