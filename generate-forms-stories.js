#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Get all forms component names from the directory listing
const allFormsNames = [
  'address', 'checkbox-headless', 'countries-select', 'industry-select',
  'input-file', 'input-file-headless', 'input-file-path', 'input-number',
  'otp', 'otp-validator', 'radio-headless', 'signup', 'validate'
];

// Component categorization and priority mapping
const formsCategories = {
  // Complex Form Components - High Priority
  'address': { category: 'complex-form', complexity: 7, priority: 'High' },
  'countries-select': { category: 'complex-form', complexity: 6, priority: 'High' },
  'industry-select': { category: 'complex-form', complexity: 5, priority: 'Medium' },
  'otp': { category: 'complex-form', complexity: 6, priority: 'High' },
  'otp-validator': { category: 'complex-form', complexity: 7, priority: 'High' },
  'signup': { category: 'complex-form', complexity: 8, priority: 'High' },
  'validate': { category: 'complex-form', complexity: 6, priority: 'Medium' },

  // Input Components - High Priority
  'input-file': { category: 'input', complexity: 5, priority: 'High' },
  'input-file-headless': { category: 'input', complexity: 6, priority: 'Medium' },
  'input-file-path': { category: 'input', complexity: 4, priority: 'Medium' },
  'input-number': { category: 'input', complexity: 4, priority: 'High' },

  // Headless Components - Medium Priority
  'checkbox-headless': { category: 'headless', complexity: 4, priority: 'Medium' },
  'radio-headless': { category: 'headless', complexity: 4, priority: 'Medium' }
};

// Story template generator
function generateStoryContent(componentName, index) {
  const storyNumber = String(index + 1).padStart(3, '0');
  const titleCase = componentName.split('-').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');
  
  const component = formsCategories[componentName] || { category: 'form', complexity: 4, priority: 'Medium' };

  return `# Story MCFO-${storyNumber}: Enhance ${titleCase} Form Component

## User Story
As a developer using the LP-GO builder, I want the ${titleCase} form component to have full Tailwind class customization and proper input properties so that I can configure and style it dynamically.

## Current State Analysis
- Component needs comprehensive enhancement for dynamic usage
- Missing proper @Input() properties for data binding
- Lacks Tailwind class customization inputs
- Requires default values for visual preview
- Category: ${component.category}

## Acceptance Criteria
- [ ] Add comprehensive @Input() properties for all configurable data
- [ ] Add \`className\` input for custom Tailwind classes
- [ ] Add \`size\` input with appropriate size options
- [ ] Add \`variant\` input for color/style schemes
- [ ] Add \`rounded\` input for border radius options
- [ ] Implement meaningful default values for preview
- [ ] Add proper TypeScript typing for all inputs
- [ ] Update component template with computed classes
- [ ] Ensure component is standalone
- [ ] Add proper module registration if needed
- [ ] Add proper form validation and error handling
- [ ] Implement accessibility features (ARIA labels, keyboard navigation)

## Required Standard Inputs
\`\`\`typescript
@Input() className: string = '';
@Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
@Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
@Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
@Input() disabled: boolean = false;
@Input() required: boolean = false;
@Input() placeholder: string = '';
@Input() label: string = '';
@Input() helpText: string = '';
@Input() errorMessage: string = '';
// Additional component-specific inputs to be defined during implementation
\`\`\`

## Form-Specific Requirements
- Implement proper form validation patterns
- Add error state management and display
- Support for reactive forms and template-driven forms
- Proper value binding and change detection
- Form state indicators (valid, invalid, pristine, dirty)
- Accessibility compliance (WCAG 2.1 AA)

## Component-Specific Requirements
- Analyze existing component template and functionality
- Define appropriate data inputs based on component purpose
- Implement component-specific logic and features
- Add event outputs for form interactions
- Ensure proper form integration patterns

## Default Visual Appearance
- Component should render with meaningful default data
- All styling should have sensible defaults
- Visual preview should represent component's purpose clearly
- Error and validation states should be clearly visible

## Implementation Tasks
1. Convert to standalone component (if not already)
2. Add all required @Input() properties
3. Implement computed classes for Tailwind customization
4. Update template with proper data binding
5. Add default values for all inputs
6. Update TypeScript types and interfaces
7. Implement form validation logic
8. Add accessibility features
9. Test component rendering and functionality
10. Update module registration if needed

## Definition of Done
- Component uses @Input() decorators exclusively
- All Tailwind class inputs are functional
- Default values provide meaningful visual preview
- TypeScript compilation successful without errors
- Component renders correctly in isolation
- Form validation works correctly
- Accessibility requirements met
- Unit tests pass
- Component follows established patterns from enhanced components

---
**Story Points**: ${component.complexity}  
**Priority**: ${component.priority}  
**Category**: ${component.category}  
**Component Path**: \`/projects/mobile-components/src/lib/forms/${componentName}/\``;
}

// Create stories directory
const storiesDir = '/Users/<USER>/Projects/clients/lp-angular/docs/stories/forms-enhancement';

if (!fs.existsSync(storiesDir)) {
  fs.mkdirSync(storiesDir, { recursive: true });
}

// Generate all story files
allFormsNames.forEach((componentName, index) => {
  const storyNumber = String(index + 1).padStart(3, '0');
  const filename = `MCFO-${storyNumber}-${componentName}.md`;
  const filepath = path.join(storiesDir, filename);
  const content = generateStoryContent(componentName, index);
  
  try {
    fs.writeFileSync(filepath, content);
    console.log(`Created story: ${filename}`);
  } catch (error) {
    console.error(`Error creating ${filename}:`, error.message);
  }
});

console.log(`\n✅ Generated ${allFormsNames.length} forms component enhancement stories`);
console.log(`📁 Stories saved to: ${storiesDir}`);
console.log(`\n📊 Story Distribution:`);

const categories = {};
allFormsNames.forEach(name => {
  const component = formsCategories[name] || { category: 'form' };
  categories[component.category] = (categories[component.category] || 0) + 1;
});

Object.entries(categories).forEach(([category, count]) => {
  console.log(`   ${category}: ${count} stories`);
});