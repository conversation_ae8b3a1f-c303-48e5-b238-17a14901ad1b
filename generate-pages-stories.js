#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Define all page components found in the analysis
const pageComponents = [
  { name: 'action-grid', category: 'layout', priority: 'High', points: 3, path: 'pages/dynamic/components/action-grid' },
  { name: 'all', category: 'content', priority: 'Medium', points: 3, path: 'pages/dynamic/games/all' },
  { name: 'balance-card', category: 'display', priority: 'High', points: 4, path: 'pages/dynamic/components/balance-card' },
  { name: 'categories', category: 'navigation', priority: 'Medium', points: 3, path: 'pages/dynamic/games/categories' },
  { name: 'dashboard', category: 'layout', priority: 'High', points: 5, path: 'pages/dynamic/games/dashboard' },
  { name: 'dynamic-dashboard', category: 'layout', priority: 'High', points: 5, path: 'pages/dynamic-dashboard' },
  { name: 'dynamic-page', category: 'layout', priority: 'High', points: 4, path: 'pages/dynamic/page' },
  { name: 'games', category: 'content', priority: 'Medium', points: 4, path: 'pages/dashboard/games' },
  { name: 'games-dashboard', category: 'layout', priority: 'Medium', points: 4, path: 'pages/games/games-dashboard' },
  { name: 'games-home', category: 'content', priority: 'Medium', points: 3, path: 'pages/landing/themes/games-home' },
  { name: 'header', category: 'layout', priority: 'High', points: 3, path: 'pages/dynamic/components/header' },
  { name: 'home', category: 'content', priority: 'High', points: 4, path: 'pages/home' },
  { name: 'how-to-play', category: 'content', priority: 'Low', points: 3, path: 'pages/dynamic/games/how-to-play' },
  { name: 'pages-customizer', category: 'form', priority: 'Medium', points: 5, path: 'pages/customizer' },
  { name: 'pages-landing-customizer', category: 'form', priority: 'Medium', points: 4, path: 'pages/landing/customizer' },
  { name: 'pages-landing-theme1', category: 'display', priority: 'Medium', points: 4, path: 'pages/landing/themes/theme1' },
  { name: 'pages-login-customizer', category: 'form', priority: 'Medium', points: 4, path: 'pages/login/customizer' },
  { name: 'pages-login-theme1', category: 'display', priority: 'Medium', points: 3, path: 'pages/login/themes/theme1' },
  { name: 'profile-details', category: 'content', priority: 'High', points: 4, path: 'pages/profile-details' },
  { name: 'single', category: 'content', priority: 'Medium', points: 3, path: 'pages/dynamic/games/single' }
];

// Standard page inputs template
const standardPageInputs = `@Input() className: string = '';
@Input() theme: 'default' | 'light' | 'dark' | 'custom' = 'default';
@Input() layout: 'fluid' | 'container' | 'full-width' = 'container';
@Input() padding: 'none' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
@Input() background: string = '';
@Input() title: string = '';
@Input() subtitle: string = '';
@Input() showHeader: boolean = true;
@Input() showFooter: boolean = true;
@Input() enableNavigation: boolean = true;
@Input() backButton: boolean = false;
@Input() navigationTitle: string = '';
@Input() responsive: boolean = true;
@Input() mobileLayout: 'stack' | 'collapse' | 'hide' = 'stack';
// Page-specific inputs to be defined during implementation`;

// Generate story template
function generateStoryTemplate(component, index) {
  const storyId = `MCP-${String(index).padStart(3, '0')}`;
  const componentNameFormatted = component.name.split('-').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');
  
  return `# Story ${storyId}: Enhance ${componentNameFormatted} Page Component

## User Story
As a developer using the LP-GO builder, I want the ${componentNameFormatted} page component to have full Tailwind class customization and proper input properties so that I can configure and style complete pages dynamically.

## Current State Analysis
- Page component needs comprehensive enhancement for dynamic usage
- Missing proper @Input() properties for page-level configuration
- Lacks Tailwind class customization inputs
- Requires default values for visual preview
- Needs proper layout and theme integration
- Category: ${component.category}

## Acceptance Criteria
- [ ] Add comprehensive @Input() properties for all configurable page data
- [ ] Add standard page inputs (theme, layout, padding, background)
- [ ] Add \`className\` input for custom Tailwind classes
- [ ] Add content configuration inputs (title, subtitle, showHeader, showFooter)
- [ ] Add navigation inputs (enableNavigation, backButton, navigationTitle)
- [ ] Add responsive configuration (responsive, mobileLayout)
- [ ] Implement meaningful default values for preview
- [ ] Add proper TypeScript typing for all inputs
- [ ] Update component template with computed classes
- [ ] Ensure component is standalone
- [ ] Add proper module registration if needed
- [ ] Integrate with Angular Router for navigation
- [ ] Support theme switching at page level
- [ ] Implement proper content area management

## Required Standard Page Inputs
\`\`\`typescript
${standardPageInputs}
\`\`\`

## Page-Specific Requirements
- Analyze existing page component template and functionality
- Define appropriate page-level data inputs based on component purpose
- Implement page-specific layout and content management
- Add proper navigation integration with Angular Router
- Ensure theme consistency across page elements
- Add support for dynamic content areas and sections
- Implement responsive page behavior

## Page Component Features
- **Layout Management**: Support for fluid, container, and full-width layouts
- **Theme Integration**: Proper theme switching and customization
- **Content Areas**: Configurable header, footer, and main content areas
- **Navigation**: Integration with Angular Router and navigation patterns
- **Responsive Design**: Mobile-first responsive behavior
- **Accessibility**: ARIA labels, keyboard navigation, screen reader support

## Default Visual Appearance
- Page should render with meaningful default content
- All styling should have sensible defaults appropriate for page-level components
- Visual preview should represent the page's purpose clearly
- Layout should demonstrate proper responsive behavior
- Navigation elements should be functional in preview mode

## Implementation Tasks
1. Convert to standalone component (if not already)
2. Add all required page @Input() properties
3. Implement computed classes for Tailwind customization
4. Update template with proper data binding and layout structure
5. Add default values for all inputs
6. Integrate with Angular Router for navigation
7. Implement theme switching logic
8. Add responsive layout handling
9. Update TypeScript types and interfaces
10. Test page rendering and functionality
11. Update module registration if needed
12. Verify accessibility compliance

## Page Layout Structure
\`\`\`html
<div class="page-container" [ngClass]="computedClasses">
  <header *ngIf="showHeader" class="page-header">
    <!-- Navigation and title area -->
  </header>
  
  <main class="page-content">
    <!-- Main page content -->
  </main>
  
  <footer *ngIf="showFooter" class="page-footer">
    <!-- Footer content -->
  </footer>
</div>
\`\`\`

## Definition of Done
- Component uses @Input() decorators exclusively
- All Tailwind class inputs are functional
- Page layout modes (fluid, container, full-width) work correctly
- Theme switching operates properly
- Navigation integration functions with Angular Router
- Default values provide meaningful visual preview
- TypeScript compilation successful without errors
- Component renders correctly in isolation
- Page follows established patterns from other enhanced pages
- Responsive behavior works across breakpoints
- Accessibility requirements met
- Unit tests pass

---
**Story Points**: ${component.points}  
**Priority**: ${component.priority}  
**Category**: ${component.category}  
**Component Path**: \`/projects/mobile-components/src/lib/${component.path}/\`  
**Story Type**: Page Enhancement  
**Epic**: EPIC-PAGES`;
}

// Create output directory
const outputDir = './docs/stories/pages-enhancement';
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Generate stories
console.log('🚀 Generating Pages Enhancement Stories...');
console.log('=====================================\n');

let totalStoryPoints = 0;

pageComponents.forEach((component, index) => {
  const storyId = `MCP-${String(index + 1).padStart(3, '0')}`;
  const filename = `${storyId}-${component.name}.md`;
  const filepath = path.join(outputDir, filename);
  
  const storyContent = generateStoryTemplate(component, index + 1);
  
  fs.writeFileSync(filepath, storyContent);
  totalStoryPoints += component.points;
  
  console.log(`✅ Created: ${filename}`);
  console.log(`   Component: ${component.name}`);
  console.log(`   Category: ${component.category}`);
  console.log(`   Priority: ${component.priority}`);
  console.log(`   Points: ${component.points}`);
  console.log(`   Path: ${component.path}`);
  console.log('');
});

// Generate summary
const summaryContent = `# Pages Enhancement Stories Summary

## Overview
Generated ${pageComponents.length} page enhancement stories for the Mobile Components Pages Epic.

## Story Statistics
- **Total Stories**: ${pageComponents.length}
- **Total Story Points**: ${totalStoryPoints}
- **Average Points per Story**: ${(totalStoryPoints / pageComponents.length).toFixed(1)}

## Stories by Category
${Object.entries(
  pageComponents.reduce((acc, comp) => {
    acc[comp.category] = (acc[comp.category] || 0) + 1;
    return acc;
  }, {})
).map(([category, count]) => `- **${category}**: ${count} stories`).join('\n')}

## Stories by Priority
${Object.entries(
  pageComponents.reduce((acc, comp) => {
    acc[comp.priority] = (acc[comp.priority] || 0) + 1;
    return acc;
  }, {})
).map(([priority, count]) => `- **${priority}**: ${count} stories`).join('\n')}

## Implementation Phases

### Phase 1: High Priority Infrastructure (${pageComponents.filter(c => c.priority === 'High').length} stories, ${pageComponents.filter(c => c.priority === 'High').reduce((sum, c) => sum + c.points, 0)} points)
${pageComponents.filter(c => c.priority === 'High').map((c, i) => `- MCP-${String(pageComponents.indexOf(c) + 1).padStart(3, '0')}: ${c.name} (${c.points} pts)`).join('\n')}

### Phase 2: Medium Priority Pages (${pageComponents.filter(c => c.priority === 'Medium').length} stories, ${pageComponents.filter(c => c.priority === 'Medium').reduce((sum, c) => sum + c.points, 0)} points)
${pageComponents.filter(c => c.priority === 'Medium').map((c, i) => `- MCP-${String(pageComponents.indexOf(c) + 1).padStart(3, '0')}: ${c.name} (${c.points} pts)`).join('\n')}

### Phase 3: Low Priority Components (${pageComponents.filter(c => c.priority === 'Low').length} stories, ${pageComponents.filter(c => c.priority === 'Low').reduce((sum, c) => sum + c.points, 0)} points)
${pageComponents.filter(c => c.priority === 'Low').map((c, i) => `- MCP-${String(pageComponents.indexOf(c) + 1).padStart(3, '0')}: ${c.name} (${c.points} pts)`).join('\n')}

## Generated Files
${pageComponents.map((c, i) => `- MCP-${String(i + 1).padStart(3, '0')}-${c.name}.md`).join('\n')}

---
Generated on: ${new Date().toISOString()}
Total Estimated Effort: ${totalStoryPoints} story points
`;

fs.writeFileSync(path.join(outputDir, 'README.md'), summaryContent);

console.log('📋 Generated README.md with summary');
console.log('');
console.log('✨ Pages Enhancement Stories Generation Complete!');
console.log(`   📁 Output Directory: ${outputDir}`);
console.log(`   📝 Stories Created: ${pageComponents.length}`);
console.log(`   🎯 Total Story Points: ${totalStoryPoints}`);
console.log(`   ⏱️  Estimated Duration: ${Math.ceil(totalStoryPoints / 10)} weeks (assuming 10 points/week)`);
console.log('');
console.log('Next Steps:');
console.log('1. Review generated stories in ./docs/stories/pages-enhancement/');
console.log('2. Update story priorities and points as needed');
console.log('3. Begin implementation starting with High priority stories');
console.log('4. Update Epic tracking with story completion progress');