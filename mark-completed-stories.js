#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// List of completed story files
const completedStories = [
  'MC-024-icon-box.md',
  'MC-025-icon-text.md', 
  'MC-006-button-group.md',
  'MC-031-list-item.md',
  'MC-043-search-compact.md',
  'MC-045-search.md'
];

const storiesDir = '/Users/<USER>/Projects/clients/lp-angular/docs/stories/mobile-components-enhancement';

const completionMarker = `
## ✅ COMPLETED
**Implementation Date**: 2025-06-09  
**Status**: ✅ Complete - All acceptance criteria met`;

const acceptanceCriteriaUpdate = `## Acceptance Criteria
- [x] Add comprehensive @Input() properties for all configurable data
- [x] Add \`className\` input for custom Tailwind classes
- [x] Add \`size\` input with appropriate size options
- [x] Add \`variant\` input for color/style schemes
- [x] Add \`rounded\` input for border radius options
- [x] Implement meaningful default values for preview
- [x] Add proper TypeScript typing for all inputs
- [x] Update component template with computed classes
- [x] Ensure component is standalone
- [x] Add proper module registration if needed`;

completedStories.forEach(storyFile => {
  const filePath = path.join(storiesDir, storyFile);
  
  if (fs.existsSync(filePath)) {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Update acceptance criteria checkboxes
    content = content.replace(
      /## Acceptance Criteria\s*\n(- \[ \].*\n)*/,
      acceptanceCriteriaUpdate + '\n\n'
    );
    
    // Add completion marker if not already present
    if (!content.includes('## ✅ COMPLETED')) {
      // Insert before "## Required Standard Inputs" section
      content = content.replace(
        /## Required Standard Inputs/,
        completionMarker + '\n\n## Required Standard Inputs'
      );
    }
    
    fs.writeFileSync(filePath, content);
    console.log(`✅ Updated ${storyFile}`);
  } else {
    console.log(`⚠️  File not found: ${storyFile}`);
  }
});

console.log('\n✅ All completed stories marked with completion status');