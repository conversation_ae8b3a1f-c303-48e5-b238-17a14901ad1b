#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Complete inventory of all feature components organized by module
const featureComponents = [
  // Authentication (6 components)
  { name: 'agents-chat', module: 'agents', path: 'agents/agents/chat', category: 'authentication', complexity: 5, priority: 'High' },
  { name: 'password', module: 'authentication', path: 'authentication/password', category: 'authentication', complexity: 4, priority: 'High' },
  { name: 'pin', module: 'authentication', path: 'authentication/pin', category: 'authentication', complexity: 4, priority: 'High' },
  { name: 'secure', module: 'authentication', path: 'authentication/secure', category: 'authentication', complexity: 3, priority: 'High' },
  { name: 'secure-dashboard', module: 'authentication', path: 'authentication/secure/dashboard', category: 'authentication', complexity: 6, priority: 'Medium' },
  { name: 'secure-home', module: 'authentication', path: 'authentication/secure/home', category: 'authentication', complexity: 5, priority: 'Medium' },
  { name: 'security', module: 'authentication', path: 'authentication/security', category: 'authentication', complexity: 5, priority: 'High' },

  // E-commerce (12 components)
  { name: 'shopping-cart-compact', module: 'cart', path: 'cart/shopping-cart-compact', category: 'ecommerce', complexity: 6, priority: 'High' },
  { name: 'product-compact', module: 'products', path: 'products/product-compact', category: 'ecommerce', complexity: 4, priority: 'High' },
  { name: 'products', module: 'products', path: 'products/products', category: 'ecommerce', complexity: 7, priority: 'High' },
  { name: 'credit-card', module: 'payments', path: 'payments/credit-card', category: 'ecommerce', complexity: 6, priority: 'High' },
  { name: 'credit-card-real', module: 'payments', path: 'payments/credit-card-real', category: 'ecommerce', complexity: 7, priority: 'High' },
  { name: 'credit-card-small', module: 'payments', path: 'payments/credit-card-small', category: 'ecommerce', complexity: 4, priority: 'Medium' },
  { name: 'store-detail', module: 'stores', path: 'stores/store-detail', category: 'ecommerce', complexity: 6, priority: 'Medium' },
  { name: 'stores', module: 'stores', path: 'stores/stores', category: 'ecommerce', complexity: 7, priority: 'Medium' },
  { name: 'virtual', module: 'virtual-card', path: 'virtual-card/virtual', category: 'ecommerce', complexity: 5, priority: 'Medium' },
  { name: 'virtualcard', module: 'virtual-card', path: 'virtual-card/virtualcard', category: 'ecommerce', complexity: 6, priority: 'Medium' },
  { name: 'offer-collapse', module: 'offers', path: 'offers/offer-collapse', category: 'ecommerce', complexity: 4, priority: 'Medium' },
  { name: 'popular-cryptos', module: 'crypto', path: 'crypto/popular-cryptos', category: 'ecommerce', complexity: 5, priority: 'Medium' },

  // Gaming (42 components)
  { name: 'games-dashboard', module: 'games', path: 'games/games/dashboard', category: 'gaming', complexity: 8, priority: 'High' },
  { name: 'games-categories', module: 'games', path: 'games/games/categories', category: 'gaming', complexity: 6, priority: 'High' },
  { name: 'games-favourites', module: 'games', path: 'games/games/favourites', category: 'gaming', complexity: 5, priority: 'Medium' },
  { name: 'games-all', module: 'games', path: 'games/games/all', category: 'gaming', complexity: 7, priority: 'High' },
  { name: 'games-single', module: 'games', path: 'games/games/single', category: 'gaming', complexity: 6, priority: 'Medium' },
  { name: 'games-home', module: 'games', path: 'games/games/home', category: 'gaming', complexity: 7, priority: 'Medium' },
  { name: 'games-how-to-play', module: 'games', path: 'games/games/how-to-play', category: 'gaming', complexity: 4, priority: 'Low' },
  
  // Individual Games (25)
  { name: 'game-2048', module: 'games', path: 'games/games/2048', category: 'gaming', complexity: 8, priority: 'Low' },
  { name: 'game-blackjack', module: 'games', path: 'games/games/blackjack', category: 'gaming', complexity: 9, priority: 'Low' },
  { name: 'game-breakout', module: 'games', path: 'games/games/breakout', category: 'gaming', complexity: 8, priority: 'Low' },
  { name: 'game-candy-crush', module: 'games', path: 'games/games/candy-crush', category: 'gaming', complexity: 9, priority: 'Low' },
  { name: 'game-check-in', module: 'games', path: 'games/games/check-in', category: 'gaming', complexity: 4, priority: 'Medium' },
  { name: 'game-chess', module: 'games', path: 'games/games/chess', category: 'gaming', complexity: 10, priority: 'Low' },
  { name: 'game-crossword', module: 'games', path: 'games/games/crossword', category: 'gaming', complexity: 9, priority: 'Low' },
  { name: 'game-flappy-bird', module: 'games', path: 'games/games/flappy-bird', category: 'gaming', complexity: 8, priority: 'Low' },
  { name: 'game-hangman', module: 'games', path: 'games/games/hangman', category: 'gaming', complexity: 7, priority: 'Low' },
  { name: 'game-hot-or-cold', module: 'games', path: 'games/games/hot-or-cold', category: 'gaming', complexity: 6, priority: 'Low' },
  { name: 'game-memory', module: 'games', path: 'games/games/memory', category: 'gaming', complexity: 7, priority: 'Low' },
  { name: 'game-minesweeper', module: 'games', path: 'games/games/minesweeper', category: 'gaming', complexity: 8, priority: 'Low' },
  { name: 'game-pac-man', module: 'games', path: 'games/games/pac-man', category: 'gaming', complexity: 9, priority: 'Low' },
  { name: 'game-platformer', module: 'games', path: 'games/games/platformer', category: 'gaming', complexity: 9, priority: 'Low' },
  { name: 'game-poker', module: 'games', path: 'games/games/poker', category: 'gaming', complexity: 10, priority: 'Low' },
  { name: 'game-pong', module: 'games', path: 'games/games/pong', category: 'gaming', complexity: 7, priority: 'Low' },
  { name: 'game-product-scan', module: 'games', path: 'games/games/product-scan', category: 'gaming', complexity: 6, priority: 'Medium' },
  { name: 'game-puzzle', module: 'games', path: 'games/games/puzzle', category: 'gaming', complexity: 8, priority: 'Low' },
  { name: 'game-quiz', module: 'games', path: 'games/games/quiz', category: 'gaming', complexity: 6, priority: 'Medium' },
  { name: 'game-racing', module: 'games', path: 'games/games/racing', category: 'gaming', complexity: 9, priority: 'Low' },
  { name: 'game-rock-paper-scissors', module: 'games', path: 'games/games/rock-paper-scissors', category: 'gaming', complexity: 5, priority: 'Low' },
  { name: 'game-simon-says', module: 'games', path: 'games/games/simon-says', category: 'gaming', complexity: 7, priority: 'Low' },
  { name: 'game-snake', module: 'games', path: 'games/games/snake', category: 'gaming', complexity: 8, priority: 'Low' },
  { name: 'game-solitaire', module: 'games', path: 'games/games/solitaire', category: 'gaming', complexity: 9, priority: 'Low' },
  { name: 'game-sudoku', module: 'games', path: 'games/games/sudoku', category: 'gaming', complexity: 8, priority: 'Low' },
  { name: 'game-tetris', module: 'games', path: 'games/games/tetris', category: 'gaming', complexity: 9, priority: 'Low' },
  { name: 'game-tic-tac-toe', module: 'games', path: 'games/games/tic-tac-toe', category: 'gaming', complexity: 5, priority: 'Low' },
  { name: 'game-tower-defense', module: 'games', path: 'games/games/tower-defense', category: 'gaming', complexity: 10, priority: 'Low' },
  { name: 'game-treasure-hunt', module: 'games', path: 'games/games/treasure-hunt', category: 'gaming', complexity: 7, priority: 'Medium' },
  { name: 'game-trivia', module: 'games', path: 'games/games/trivia', category: 'gaming', complexity: 6, priority: 'Medium' },
  { name: 'game-wheel-spin', module: 'games', path: 'games/games/wheel-spin', category: 'gaming', complexity: 6, priority: 'Medium' },
  { name: 'game-wordle', module: 'games', path: 'games/games/wordle', category: 'gaming', complexity: 7, priority: 'Low' },
  { name: 'submit-selfie', module: 'games', path: 'games/games/submit-selfie', category: 'gaming', complexity: 5, priority: 'Medium' },

  // Game Components (12)
  { name: 'games-card', module: 'games', path: 'games/games/components/games-card', category: 'gaming', complexity: 4, priority: 'High' },
  { name: 'games-card-small', module: 'games', path: 'games/games/components/games-card-small', category: 'gaming', complexity: 3, priority: 'High' },
  { name: 'games-header', module: 'games', path: 'games/games/components/games-header', category: 'gaming', complexity: 3, priority: 'High' },
  { name: 'games-leaderboard', module: 'games', path: 'games/games/components/games-leaderboard', category: 'gaming', complexity: 6, priority: 'Medium' },
  { name: 'games-leaderboard-footer', module: 'games', path: 'games/games/components/games-leaderboard-footer', category: 'gaming', complexity: 3, priority: 'Medium' },
  { name: 'games-leaderboard-header', module: 'games', path: 'games/games/components/games-leaderboard-header', category: 'gaming', complexity: 3, priority: 'Medium' },
  { name: 'games-leaderboard-item', module: 'games', path: 'games/games/components/games-leaderboard-item', category: 'gaming', complexity: 4, priority: 'Medium' },
  { name: 'games-name', module: 'games', path: 'games/games/components/games-name', category: 'gaming', complexity: 2, priority: 'Medium' },
  { name: 'games-play-button', module: 'games', path: 'games/games/components/games-play-button', category: 'gaming', complexity: 3, priority: 'High' },
  { name: 'games-reset-button', module: 'games', path: 'games/games/components/games-reset-button', category: 'gaming', complexity: 3, priority: 'Medium' },
  { name: 'games-score', module: 'games', path: 'games/games/components/games-score', category: 'gaming', complexity: 3, priority: 'Medium' },
  { name: 'games-template', module: 'games', path: 'games/games/components/games-template', category: 'gaming', complexity: 5, priority: 'High' },
  { name: 'win-lose-overlay', module: 'games', path: 'games/games/components/win-lose-overlay', category: 'gaming', complexity: 4, priority: 'Medium' },

  // Competitions
  { name: 'selfie-competition', module: 'competitions', path: 'competitions/competitions/selfie', category: 'gaming', complexity: 6, priority: 'Medium' },

  // Business (15 components)
  { name: 'project-list-compact', module: 'projects', path: 'projects/project-list-compact', category: 'business', complexity: 5, priority: 'High' },
  { name: 'invest', module: 'investments', path: 'investments/invest', category: 'business', complexity: 7, priority: 'Medium' },
  { name: 'league-list-compact', module: 'leagues', path: 'leagues/league-list-compact', category: 'business', complexity: 5, priority: 'Medium' },
  { name: 'team-list-compact', module: 'teams', path: 'teams/team-list-compact', category: 'business', complexity: 5, priority: 'Medium' },
  { name: 'team-search-compact', module: 'teams', path: 'teams/team-search-compact', category: 'business', complexity: 6, priority: 'Medium' },
  { name: 'trending-skills', module: 'skills', path: 'skills/trending-skills', category: 'business', complexity: 4, priority: 'Medium' },
  { name: 'calendar-event', module: 'calendar', path: 'calendar/calendar-event', category: 'business', complexity: 5, priority: 'Medium' },
  { name: 'calendar-event-pending', module: 'calendar', path: 'calendar/calendar-event-pending', category: 'business', complexity: 4, priority: 'Medium' },
  { name: 'days-square', module: 'calendar', path: 'calendar/days-square', category: 'business', complexity: 3, priority: 'Medium' },
  { name: 'customizer', module: 'customizer', path: 'customizer/customizer', category: 'business', complexity: 8, priority: 'Low' },
  { name: 'geo', module: 'geolocation', path: 'geolocation/geo', category: 'business', complexity: 6, priority: 'Medium' },
  { name: 'geo-location', module: 'geolocation', path: 'geolocation/geo-location', category: 'business', complexity: 7, priority: 'Medium' },
  { name: 'notifications', module: 'notifications', path: 'notifications/notifications', category: 'business', complexity: 6, priority: 'High' },
  { name: 'notifications-compact', module: 'notifications', path: 'notifications/notifications-compact', category: 'business', complexity: 4, priority: 'High' },
  { name: 'profile', module: 'profile', path: 'profile/profile', category: 'business', complexity: 7, priority: 'High' },
  { name: 'profileremove', module: 'profile', path: 'profile/profileremove', category: 'business', complexity: 4, priority: 'Low' },

  // Communication (8 components)
  { name: 'chat', module: 'chat', path: 'chat/chat', category: 'communication', complexity: 8, priority: 'High' },
  { name: 'contact', module: 'support', path: 'support/contact', category: 'communication', complexity: 5, priority: 'Medium' },
  { name: 'contactus', module: 'support', path: 'support/contactus', category: 'communication', complexity: 4, priority: 'Medium' },
  { name: 'info', module: 'support', path: 'support/info', category: 'communication', complexity: 3, priority: 'Medium' },
  { name: 'pending-tickets', module: 'support', path: 'support/pending-tickets', category: 'communication', complexity: 6, priority: 'Medium' },
  { name: 'filter-invite', module: 'invites', path: 'invites/filter-invite', category: 'communication', complexity: 5, priority: 'Medium' },
  { name: 'social-links', module: 'socials', path: 'socials/social-links', category: 'communication', complexity: 4, priority: 'Medium' },
  { name: 'socials-customizer', module: 'socials', path: 'socials/socials/customizer', category: 'communication', complexity: 6, priority: 'Low' },
  { name: 'socials-plain', module: 'socials', path: 'socials/socials/plain', category: 'communication', complexity: 3, priority: 'Medium' },

  // Utility (15 components)
  { name: 'settings', module: 'settings', path: 'settings/settings', category: 'utility', complexity: 6, priority: 'High' },
  { name: 'theme-switch', module: 'settings', path: 'settings/theme-switch', category: 'utility', complexity: 4, priority: 'High' },
  { name: 'theme-toggle', module: 'settings', path: 'settings/theme-toggle', category: 'utility', complexity: 4, priority: 'High' },
  { name: 'app-search', module: 'search', path: 'search/app-search', category: 'utility', complexity: 6, priority: 'High' },
  { name: 'app-search-result', module: 'search', path: 'search/app-search-result', category: 'utility', complexity: 4, priority: 'Medium' },
  { name: 'todo-list-compact', module: 'todos', path: 'todos/todo-list-compact', category: 'utility', complexity: 5, priority: 'Medium' },
  { name: 'todo-list-tabbed', module: 'todos', path: 'todos/todo-list-tabbed', category: 'utility', complexity: 6, priority: 'Medium' },
  { name: 'topic-list-compact', module: 'topics', path: 'topics/topic-list-compact', category: 'utility', complexity: 4, priority: 'Medium' },
  { name: 'money-in', module: 'transactions', path: 'transactions/money-in', category: 'utility', complexity: 5, priority: 'High' },
  { name: 'money-out', module: 'transactions', path: 'transactions/money-out', category: 'utility', complexity: 5, priority: 'High' },
  { name: 'statements', module: 'transactions', path: 'transactions/statements', category: 'utility', complexity: 6, priority: 'High' },
  { name: 'transaction-compact', module: 'transactions', path: 'transactions/transaction-compact', category: 'utility', complexity: 4, priority: 'High' },
  { name: 'transaction-summary', module: 'transactions', path: 'transactions/transaction-summary', category: 'utility', complexity: 5, priority: 'High' },
  { name: 'transactions', module: 'transactions', path: 'transactions/transactions', category: 'utility', complexity: 7, priority: 'High' },
  { name: 'transactions-filters', module: 'transactions', path: 'transactions/transactions-filters', category: 'utility', complexity: 6, priority: 'Medium' },
  { name: 'transactions-list-placeload', module: 'transactions', path: 'transactions/transactions-list-placeload', category: 'utility', complexity: 3, priority: 'Low' },
  { name: 'user-list', module: 'users', path: 'users/user-list', category: 'utility', complexity: 5, priority medium' }
];

// Story template generator for feature components
function generateFeatureStoryContent(component, index) {
  const storyNumber = String(index + 1).padStart(3, '0');
  const titleCase = component.name.split('-').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');

  // Special handling for game components
  const isGameComponent = component.name.startsWith('game-');
  const gameTitle = isGameComponent ? 
    titleCase.replace('Game ', '') + ' Game' : titleCase;

  const finalTitle = isGameComponent ? gameTitle : titleCase;

  return `# Story MCF-${storyNumber}: Enhance ${finalTitle} Component

## User Story
As a developer using the LP-GO builder, I want the ${finalTitle} component to have full Tailwind class customization and proper input properties so that I can configure and style it dynamically within feature modules.

## Current State Analysis
- Feature component needs comprehensive enhancement for dynamic usage
- Missing proper @Input() properties for data binding
- Lacks Tailwind class customization inputs
- Requires default values with mock data for visual preview
- Category: ${component.category}
- Module: ${component.module}
- Complexity: ${component.complexity}/10

## Acceptance Criteria
- [ ] Add comprehensive @Input() properties for all configurable data
- [ ] Add \`className\` input for custom Tailwind classes
- [ ] Add \`size\` input with appropriate size options
- [ ] Add \`variant\` input for color/style schemes
- [ ] Add \`rounded\` input for border radius options
- [ ] Implement meaningful default values with mock data for preview
- [ ] Add proper TypeScript typing for all inputs
- [ ] Update component template with computed classes
- [ ] Ensure component is standalone
- [ ] Add proper module registration if needed
- [ ] Preserve existing business logic and functionality

## Required Standard Inputs
\`\`\`typescript
@Input() className: string = '';
@Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
@Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
@Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
// Additional component-specific inputs to be defined during implementation
\`\`\`

## Component-Specific Requirements
- Analyze existing component template and business logic
- Define appropriate data inputs based on feature functionality
- Implement component-specific logic while maintaining modularity
- Add event outputs where user interaction is expected
- Ensure accessibility standards are met
- Preserve existing service integrations and data flows
${isGameComponent ? '- Maintain game logic and canvas/WebGL compatibility where applicable' : ''}
${component.category === 'ecommerce' ? '- Ensure secure data handling for financial information' : ''}
${component.category === 'authentication' ? '- Maintain security patterns and token handling' : ''}

## Default Visual Appearance with Mock Data
- Component should render with meaningful mock data that demonstrates functionality
- All styling should have sensible defaults
- Visual preview should clearly represent the component's business purpose
- Mock data should be realistic but safe (no real user data)

## Implementation Tasks
1. Convert to standalone component (if not already)
2. Add all required @Input() properties
3. Implement computed classes for Tailwind customization
4. Update template with proper data binding
5. Add default values and mock data for all inputs
6. Update TypeScript types and interfaces
7. Preserve existing business logic and service integrations
8. Test component rendering and functionality
9. Update module registration if needed
10. Add proper error handling and loading states

## Definition of Done
- Component uses @Input() decorators exclusively
- All Tailwind class inputs are functional
- Default values with mock data provide meaningful visual preview
- TypeScript compilation successful without errors
- Component renders correctly in isolation
- Component follows established patterns from enhanced components
- Business logic and existing functionality preserved
- Accessibility requirements met
- Unit tests pass
- Component properly integrates with feature module architecture

## Special Considerations
${component.category === 'gaming' ? '- Gaming components may require canvas/WebGL compatibility\n- Performance optimization for game loops and animations\n- Proper state management for game data' : ''}
${component.category === 'ecommerce' ? '- Payment components need security validation\n- Financial data handling must be secure\n- Integration with payment processing services' : ''}
${component.category === 'authentication' ? '- Authentication components require proper token handling\n- Security patterns must be maintained\n- Session management considerations' : ''}
${component.category === 'communication' ? '- Real-time data handling for chat and notifications\n- Privacy and permission handling\n- WebSocket or polling integration' : ''}
${component.category === 'business' ? '- Complex business logic encapsulation\n- Data validation and error handling\n- Integration with business services' : ''}

---
**Story Points**: ${component.complexity}  
**Priority**: ${component.priority}  
**Category**: ${component.category}  
**Module**: ${component.module}  
**Component Path**: \`/projects/mobile-components/src/lib/features/${component.path}/\``;
}

// Create stories directory
const storiesDir = '/Users/<USER>/Projects/clients/lp-angular/docs/stories/features-enhancement';

if (!fs.existsSync(storiesDir)) {
  fs.mkdirSync(storiesDir, { recursive: true });
}

// Generate all story files
featureComponents.forEach((component, index) => {
  const storyNumber = String(index + 1).padStart(3, '0');
  const filename = `MCF-${storyNumber}-${component.name}.md`;
  const filepath = path.join(storiesDir, filename);
  const content = generateFeatureStoryContent(component, index);
  
  try {
    fs.writeFileSync(filepath, content);
    console.log(`Created story: ${filename}`);
  } catch (error) {
    console.error(`Error creating ${filename}:`, error.message);
  }
});

console.log(`\n✅ Generated ${featureComponents.length} feature component enhancement stories`);
console.log(`📁 Stories saved to: ${storiesDir}`);
console.log(`\n📊 Story Distribution by Category:`);

const categories = {};
featureComponents.forEach(component => {
  categories[component.category] = (categories[component.category] || 0) + 1;
});

Object.entries(categories).forEach(([category, count]) => {
  console.log(`   ${category}: ${count} stories`);
});

console.log(`\n📊 Story Distribution by Module:`);
const modules = {};
featureComponents.forEach(component => {
  modules[component.module] = (modules[component.module] || 0) + 1;
});

Object.entries(modules).sort(([,a], [,b]) => b - a).forEach(([module, count]) => {
  console.log(`   ${module}: ${count} stories`);
});

console.log(`\n📊 Priority Distribution:`);
const priorities = {};
featureComponents.forEach(component => {
  priorities[component.priority] = (priorities[component.priority] || 0) + 1;
});

Object.entries(priorities).forEach(([priority, count]) => {
  console.log(`   ${priority}: ${count} stories`);
});

console.log(`\n📊 Complexity Analysis:`);
const complexities = featureComponents.map(c => c.complexity);
const avgComplexity = (complexities.reduce((a, b) => a + b, 0) / complexities.length).toFixed(1);
const maxComplexity = Math.max(...complexities);
const minComplexity = Math.min(...complexities);

console.log(`   Average Complexity: ${avgComplexity}/10`);
console.log(`   Max Complexity: ${maxComplexity}/10`);
console.log(`   Min Complexity: ${minComplexity}/10`);