#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Define all game components with their categories and specific requirements
const gameComponents = [
  // Arcade Games (11 components)
  {
    id: 'MCFG-001',
    name: '2048',
    folderName: '2048',
    category: 'arcade',
    description: 'Number puzzle game with grid-based sliding tile mechanics',
    complexity: 'high',
    specificInputs: [
      'gridSize: number = 4',
      'winningTile: number = 2048',
      'animationSpeed: number = 200',
      'allowUndo: boolean = false',
      'showBestScore: boolean = true'
    ],
    gameEvents: ['tileMove', 'tilesMerge', 'gameWin', 'gameOver'],
    performance: 'Canvas-based rendering for smoothiletile animations'
  },
  {
    id: 'MCFG-002',
    name: 'Breakout',
    folderName: 'breakout',
    category: 'arcade',
    description: 'Classic ball-and-paddle arcade game with brick destruction',
    complexity: 'high',
    specificInputs: [
      'paddleSpeed: number = 5',
      'ballSpeed: number = 3',
      'brickRows: number = 5',
      'brickColumns: number = 10',
      'ballCount: number = 3'
    ],
    gameEvents: ['paddleMove', 'ballBounce', 'brickDestroy', 'levelComplete'],
    performance: 'Collision detection optimization for smooth gameplay'
  },
  {
    id: 'MCFG-003',
    name: 'Flappy Bird',
    folderName: 'flappy-bird',
    category: 'arcade',
    description: 'Side-scrolling obstacle avoidance game with tap controls',
    complexity: 'medium',
    specificInputs: [
      'gravity: number = 0.5',
      'jumpStrength: number = -8',
      'pipeGap: number = 150',
      'scrollSpeed: number = 2',
      'pipeSpacing: number = 200'
    ],
    gameEvents: ['birdJump', 'pipePass', 'collision', 'scoreIncrease'],
    performance: 'Efficient sprite management and physics calculations'
  },
  {
    id: 'MCFG-004',
    name: 'Pac-Man',
    folderName: 'pac-man',
    category: 'arcade',
    description: 'Maze-based collectible game with ghost AI',
    complexity: 'high',
    specificInputs: [
      'mazeLayout: string[][] = []',
      'ghostCount: number = 4',
      'pelletValue: number = 10',
      'powerPelletDuration: number = 10000',
      'ghostSpeed: number = 1'
    ],
    gameEvents: ['pelletCollect', 'powerPelletCollect', 'ghostCollision', 'mazeComplete'],
    performance: 'Pathfinding algorithms for ghost AI'
  },
  {
    id: 'MCFG-005',
    name: 'Platformer',
    folderName: 'platformer',
    category: 'arcade',
    description: 'Side-scrolling jump-and-run game with physics',
    complexity: 'high',
    specificInputs: [
      'gravity: number = 0.8',
      'jumpHeight: number = 15',
      'moveSpeed: number = 5',
      'levelData: any[] = []',
      'collectibleCount: number = 0'
    ],
    gameEvents: ['playerJump', 'collectItem', 'enemyCollision', 'levelComplete'],
    performance: 'Physics engine optimization for smooth movement'
  },
  {
    id: 'MCFG-006',
    name: 'Pong',
    folderName: 'pong',
    category: 'arcade',
    description: 'Classic two-player paddle game with ball physics',
    complexity: 'medium',
    specificInputs: [
      'paddleHeight: number = 80',
      'ballSpeed: number = 4',
      'aiDifficulty: number = 0.5',
      'winningScore: number = 11',
      'enableAI: boolean = true'
    ],
    gameEvents: ['paddleMove', 'ballBounce', 'scorePoint', 'gameWin'],
    performance: 'Smooth ball physics and paddle collision detection'
  },
  {
    id: 'MCFG-007',
    name: 'Racing',
    folderName: 'racing',
    category: 'arcade',
    description: 'Top-down or side-view racing game with track navigation',
    complexity: 'high',
    specificInputs: [
      'trackData: any[] = []',
      'carSpeed: number = 5',
      'lapCount: number = 3',
      'opponentCount: number = 3',
      'enableNitro: boolean = true'
    ],
    gameEvents: ['lapComplete', 'checkpointPass', 'collision', 'raceFinish'],
    performance: '60fps rendering for smooth racing experience'
  },
  {
    id: 'MCFG-008',
    name: 'Snake',
    folderName: 'snake',
    category: 'arcade',
    description: 'Classic snake growth game with food collection',
    complexity: 'medium',
    specificInputs: [
      'boardWidth: number = 20',
      'boardHeight: number = 20',
      'gameSpeed: number = 150',
      'foodValue: number = 1',
      'enableWalls: boolean = true'
    ],
    gameEvents: ['foodEat', 'snakeGrow', 'wallCollision', 'selfCollision'],
    performance: 'Efficient grid-based collision detection'
  },
  {
    id: 'MCFG-009',
    name: 'Tetris',
    folderName: 'tetris',
    category: 'arcade',
    description: 'Falling block puzzle game with line clearing',
    complexity: 'high',
    specificInputs: [
      'boardWidth: number = 10',
      'boardHeight: number = 20',
      'fallSpeed: number = 1000',
      'previewPieces: number = 3',
      'enableHold: boolean = true'
    ],
    gameEvents: ['piecePlace', 'linesClear', 'levelUp', 'gameOver'],
    performance: 'Optimized line detection and block clearing animations'
  },
  {
    id: 'MCFG-010',
    name: 'Tower Defense',
    folderName: 'tower-defense',
    category: 'arcade',
    description: 'Strategy-based defense game with tower placement',
    complexity: 'high',
    specificInputs: [
      'mapData: any[] = []',
      'waveCount: number = 10',
      'towerTypes: any[] = []',
      'enemyTypes: any[] = []',
      'startingMoney: number = 500'
    ],
    gameEvents: ['towerBuild', 'enemyDestroy', 'waveComplete', 'baseAttack'],
    performance: 'Efficient pathfinding and multiple entity management'
  },
  {
    id: 'MCFG-011',
    name: 'Treasure Hunt',
    folderName: 'treasure-hunt',
    category: 'arcade',
    description: 'Location-based treasure finding game with map integration',
    complexity: 'medium',
    specificInputs: [
      'treasureLocations: any[] = []',
      'searchRadius: number = 100',
      'hintSystem: boolean = true',
      'timeLimit: number = 0',
      'difficultyLevel: "easy" | "medium" | "hard" = "medium"'
    ],
    gameEvents: ['treasureFound', 'hintUsed', 'locationReached', 'timeUp'],
    performance: 'GPS integration and offline map caching'
  },

  // Card & Board Games (8 components)
  {
    id: 'MCFG-012',
    name: 'Blackjack',
    folderName: 'blackjack',
    category: 'card',
    description: 'Casino-style card game with betting mechanics',
    complexity: 'medium',
    specificInputs: [
      'deckCount: number = 1',
      'dealerStandsOn: number = 17',
      'enableSplit: boolean = true',
      'enableDoubleDown: boolean = true',
      'enableInsurance: boolean = true'
    ],
    gameEvents: ['cardDeal', 'playerHit', 'playerStand', 'handComplete'],
    performance: 'Card animation and game logic optimization'
  },
  {
    id: 'MCFG-013',
    name: 'Chess',
    folderName: 'chess',
    category: 'board',
    description: 'Strategic board game with piece movement validation',
    complexity: 'high',
    specificInputs: [
      'enableAI: boolean = true',
      'aiDifficulty: number = 3',
      'timeControl: number = 0',
      'enableUndo: boolean = true',
      'showLegalMoves: boolean = true'
    ],
    gameEvents: ['pieceMove', 'pieceCaptured', 'check', 'checkmate'],
    performance: 'Chess engine optimization and move validation'
  },
  {
    id: 'MCFG-014',
    name: 'Poker',
    folderName: 'poker',
    category: 'card',
    description: 'Card game with betting mechanics and hand evaluation',
    complexity: 'high',
    specificInputs: [
      'gameVariant: "texas" | "omaha" | "stud" = "texas"',
      'playerCount: number = 4',
      'blindStructure: any = {}',
      'enableBetting: boolean = true',
      'showHandStrength: boolean = false'
    ],
    gameEvents: ['cardDeal', 'playerBet', 'handComplete', 'showdown'],
    performance: 'Hand evaluation algorithms and betting logic'
  },
  {
    id: 'MCFG-015',
    name: 'Rock Paper Scissors',
    folderName: 'rock-paper-scissors',
    category: 'board',
    description: 'Simple choice-based game with opponent AI',
    complexity: 'low',
    specificInputs: [
      'enableAI: boolean = true',
      'aiStrategy: "random" | "adaptive" | "pattern" = "random"',
      'roundsToWin: number = 3',
      'showOpponentChoice: boolean = true',
      'animationSpeed: number = 500'
    ],
    gameEvents: ['choiceMade', 'roundComplete', 'gameWin', 'gameLose'],
    performance: 'Simple game state management with smooth animations'
  },
  {
    id: 'MCFG-016',
    name: 'Solitaire',
    folderName: 'solitaire',
    category: 'card',
    description: 'Single-player card game with drag-and-drop mechanics',
    complexity: 'medium',
    specificInputs: [
      'gameVariant: "klondike" | "spider" | "freecell" = "klondike"',
      'drawCount: number = 3',
      'enableUndo: boolean = true',
      'enableHints: boolean = true',
      'autoComplete: boolean = true'
    ],
    gameEvents: ['cardMove', 'stackComplete', 'gameWin', 'undoMove'],
    performance: 'Drag-and-drop optimization and move validation'
  },
  {
    id: 'MCFG-017',
    name: 'Tic Tac Toe',
    folderName: 'tic-tac-toe',
    category: 'board',
    description: 'Strategic grid game with AI opponent',
    complexity: 'low',
    specificInputs: [
      'boardSize: number = 3',
      'enableAI: boolean = true',
      'aiDifficulty: "easy" | "medium" | "hard" = "medium"',
      'firstPlayer: "human" | "ai" | "random" = "human"',
      'winCondition: number = 3'
    ],
    gameEvents: ['cellSelect', 'gameWin', 'gameDraw', 'aiMove'],
    performance: 'Minimax algorithm for AI decision making'
  },
  {
    id: 'MCFG-018',
    name: 'Memory',
    folderName: 'memory',
    category: 'board',
    description: 'Card matching memory game with concentration mechanics',
    complexity: 'medium',
    specificInputs: [
      'cardCount: number = 16',
      'flipTime: number = 1000',
      'maxMisses: number = 0',
      'cardTheme: string = "default"',
      'enableTimer: boolean = true'
    ],
    gameEvents: ['cardFlip', 'matchFound', 'matchMissed', 'gameComplete'],
    performance: 'Card flip animations and matching logic optimization'
  },
  {
    id: 'MCFG-019',
    name: 'Minesweeper',
    folderName: 'minesweeper',
    category: 'board',
    description: 'Logic-based puzzle game with mine detection',
    complexity: 'medium',
    specificInputs: [
      'boardWidth: number = 9',
      'boardHeight: number = 9',
      'mineCount: number = 10',
      'enableFlags: boolean = true',
      'enableQuestionMarks: boolean = true'
    ],
    gameEvents: ['cellReveal', 'flagPlace', 'mineHit', 'gameWin'],
    performance: 'Efficient flood-fill algorithm for cell revelation'
  },

  // Puzzle Games (6 components)
  {
    id: 'MCFG-020',
    name: 'Crossword',
    folderName: 'crossword',
    category: 'puzzle',
    description: 'Word-based puzzle game with clue system',
    complexity: 'high',
    specificInputs: [
      'puzzleData: any = {}',
      'showIncorrect: boolean = false',
      'enableHints: boolean = true',
      'checkOnComplete: boolean = true',
      'enableTimer: boolean = false'
    ],
    gameEvents: ['wordComplete', 'letterInput', 'hintUsed', 'puzzleComplete'],
    performance: 'Word validation and grid rendering optimization'
  },
  {
    id: 'MCFG-021',
    name: 'Hangman',
    folderName: 'hangman',
    category: 'puzzle',
    description: 'Word guessing game with progressive drawing',
    complexity: 'medium',
    specificInputs: [
      'wordList: string[] = []',
      'maxWrongGuesses: number = 6',
      'showWordLength: boolean = true',
      'enableHints: boolean = false',
      'category: string = "general"'
    ],
    gameEvents: ['letterGuess', 'wordGuess', 'gameWin', 'gameLose'],
    performance: 'SVG drawing animation for hangman progression'
  },
  {
    id: 'MCFG-022',
    name: 'Puzzle',
    folderName: 'puzzle',
    category: 'puzzle',
    description: 'Image or pattern assembly game with drag mechanics',
    complexity: 'medium',
    specificInputs: [
      'imageUrl: string = ""',
      'pieceCount: number = 16',
      'enableRotation: boolean = false',
      'showPreview: boolean = true',
      'snapDistance: number = 20'
    ],
    gameEvents: ['pieceMove', 'piecePlaced', 'puzzleComplete', 'hintUsed'],
    performance: 'Canvas-based piece rendering and collision detection'
  },
  {
    id: 'MCFG-023',
    name: 'Sudoku',
    folderName: 'sudoku',
    category: 'puzzle',
    description: 'Number logic puzzle with constraint validation',
    complexity: 'high',
    specificInputs: [
      'difficulty: "easy" | "medium" | "hard" | "expert" = "medium"',
      'showMistakes: boolean = true',
      'enableNotes: boolean = true',
      'enableHints: boolean = true',
      'enableAutoValidation: boolean = true'
    ],
    gameEvents: ['numberPlace', 'mistakeMade', 'hintUsed', 'puzzleComplete'],
    performance: 'Constraint validation algorithms and efficient grid updates'
  },
  {
    id: 'MCFG-024',
    name: 'Trivia',
    folderName: 'trivia',
    category: 'puzzle',
    description: 'Question-based knowledge game with scoring',
    complexity: 'medium',
    specificInputs: [
      'questionBank: any[] = []',
      'questionCount: number = 10',
      'timePerQuestion: number = 30',
      'enableCategories: boolean = true',
      'difficultyProgression: boolean = false'
    ],
    gameEvents: ['questionAnswered', 'timeUp', 'categoryComplete', 'gameComplete'],
    performance: 'Question randomization and timer management'
  },
  {
    id: 'MCFG-025',
    name: 'Wordle',
    folderName: 'wordle',
    category: 'puzzle',
    description: 'Word guessing game with letter feedback system',
    complexity: 'medium',
    specificInputs: [
      'wordLength: number = 5',
      'maxGuesses: number = 6',
      'wordList: string[] = []',
      'enableHardMode: boolean = false',
      'showKeyboard: boolean = true'
    ],
    gameEvents: ['wordSubmit', 'letterFeedback', 'gameWin', 'gameLose'],
    performance: 'Word validation and keyboard feedback optimization'
  },

  // Interactive Games (5 components)
  {
    id: 'MCFG-026',
    name: 'Candy Crush',
    folderName: 'candy-crush',
    category: 'interactive',
    description: 'Match-3 style puzzle game with cascading effects',
    complexity: 'high',
    specificInputs: [
      'boardWidth: number = 8',
      'boardHeight: number = 10',
      'candyTypes: number = 6',
      'enablePowerUps: boolean = true',
      'movesLimit: number = 20'
    ],
    gameEvents: ['candyMatch', 'cascadeComplete', 'powerUpActivated', 'levelComplete'],
    performance: 'Match detection algorithms and cascade animation optimization'
  },
  {
    id: 'MCFG-027',
    name: 'Quiz',
    folderName: 'quiz',
    category: 'interactive',
    description: 'Multi-question knowledge testing with progress tracking',
    complexity: 'medium',
    specificInputs: [
      'questions: any[] = []',
      'enableTimer: boolean = true',
      'timeLimit: number = 60',
      'enableSkip: boolean = false',
      'showProgress: boolean = true'
    ],
    gameEvents: ['questionAnswered', 'quizComplete', 'timeUp', 'skipQuestion'],
    performance: 'Question management and progress tracking optimization'
  },
  {
    id: 'MCFG-028',
    name: 'Simon Says',
    folderName: 'simon-says',
    category: 'interactive',
    description: 'Memory sequence game with audio-visual patterns',
    complexity: 'medium',
    specificInputs: [
      'buttonCount: number = 4',
      'sequenceLength: number = 1',
      'playbackSpeed: number = 600',
      'enableSounds: boolean = true',
      'difficultyIncrease: boolean = true'
    ],
    gameEvents: ['sequencePlay', 'buttonPress', 'sequenceComplete', 'mistakeMade'],
    performance: 'Audio synchronization and visual feedback optimization'
  },
  {
    id: 'MCFG-029',
    name: 'Wheel Spin',
    folderName: 'wheel-spin',
    category: 'interactive',
    description: 'Chance-based spinning game with customizable segments',
    complexity: 'medium',
    specificInputs: [
      'segments: any[] = []',
      'spinDuration: number = 3000',
      'enableSound: boolean = true',
      'autoSpin: boolean = false',
      'enableAnimation: boolean = true'
    ],
    gameEvents: ['spinStart', 'spinEnd', 'segmentSelect', 'prizeWon'],
    performance: 'Smooth wheel rotation animation and physics simulation'
  },
  {
    id: 'MCFG-030',
    name: 'Hot or Cold',
    folderName: 'hot-or-cold',
    category: 'interactive',
    description: 'Temperature-based guessing game with proximity feedback',
    complexity: 'low',
    specificInputs: [
      'targetRange: number = 100',
      'maxGuesses: number = 10',
      'enableHints: boolean = true',
      'difficultyLevel: "easy" | "medium" | "hard" = "medium"',
      'feedbackStyle: "temperature" | "distance" = "temperature"'
    ],
    gameEvents: ['guessMade', 'targetFound', 'hintUsed', 'gameComplete'],
    performance: 'Simple calculation logic with responsive feedback'
  },

  // Game Components (11 components)
  {
    id: 'MCFG-031',
    name: 'Games Card',
    folderName: 'components/games-card',
    category: 'component',
    description: 'Individual game display card with action buttons',
    complexity: 'low',
    specificInputs: [
      'gameTitle: string = ""',
      'gameDescription: string = ""',
      'gameImage: string = ""',
      'playCount: number = 0',
      'rating: number = 0'
    ],
    gameEvents: ['gameSelect', 'favoriteToggle', 'shareGame', 'cardClick'],
    performance: 'Efficient image loading and hover effects'
  },
  {
    id: 'MCFG-032',
    name: 'Games Card Small',
    folderName: 'components/games-card-small',
    category: 'component',
    description: 'Compact game card variant for dense layouts',
    complexity: 'low',
    specificInputs: [
      'gameTitle: string = ""',
      'gameIcon: string = ""',
      'isNew: boolean = false',
      'isPopular: boolean = false',
      'quickPlay: boolean = true'
    ],
    gameEvents: ['quickPlay', 'cardHover', 'gameSelect'],
    performance: 'Minimal DOM footprint for list rendering'
  },
  {
    id: 'MCFG-033',
    name: 'Games Header',
    folderName: 'components/games-header',
    category: 'component',
    description: 'Game section header with branding and navigation',
    complexity: 'low',
    specificInputs: [
      'title: string = "Games"',
      'subtitle: string = ""',
      'showSearch: boolean = true',
      'showCategories: boolean = true',
      'enableFilters: boolean = true'
    ],
    gameEvents: ['searchInput', 'categorySelect', 'filterChange'],
    performance: 'Debounced search input and efficient filtering'
  },
  {
    id: 'MCFG-034',
    name: 'Games Leaderboard',
    folderName: 'components/games-leaderboard',
    category: 'component',
    description: 'Player ranking display with pagination',
    complexity: 'medium',
    specificInputs: [
      'players: any[] = []',
      'currentUserId: string = ""',
      'showRank: boolean = true',
      'showAvatar: boolean = true',
      'pageSize: number = 10'
    ],
    gameEvents: ['playerSelect', 'pageChange', 'refreshData'],
    performance: 'Virtual scrolling for large leaderboards'
  },
  {
    id: 'MCFG-035',
    name: 'Games Leaderboard Header',
    folderName: 'components/games-leaderboard-header',
    category: 'component',
    description: 'Leaderboard section header with filtering options',
    complexity: 'low',
    specificInputs: [
      'title: string = "Leaderboard"',
      'timeFilter: "daily" | "weekly" | "monthly" | "all" = "weekly"',
      'enableTimeFilter: boolean = true',
      'showTotal: boolean = true'
    ],
    gameEvents: ['timeFilterChange', 'sortChange', 'refreshRequest'],
    performance: 'Efficient filter state management'
  },
  {
    id: 'MCFG-036',
    name: 'Games Leaderboard Footer',
    folderName: 'components/games-leaderboard-footer',
    category: 'component',
    description: 'Leaderboard pagination and action footer',
    complexity: 'low',
    specificInputs: [
      'totalPages: number = 1',
      'currentPage: number = 1',
      'showPageInfo: boolean = true',
      'enableJumpTo: boolean = false'
    ],
    gameEvents: ['pageChange', 'jumpToPage', 'firstPage', 'lastPage'],
    performance: 'Optimized pagination controls'
  },
  {
    id: 'MCFG-037',
    name: 'Games Leaderboard Item',
    folderName: 'components/games-leaderboard-item',
    category: 'component',
    description: 'Individual leaderboard entry with player details',
    complexity: 'low',
    specificInputs: [
      'player: any = {}',
      'rank: number = 0',
      'showAvatar: boolean = true',
      'showBadges: boolean = true',
      'isCurrentUser: boolean = false'
    ],
    gameEvents: ['playerClick', 'badgeClick', 'challengePlayer'],
    performance: 'Efficient avatar loading and highlight effects'
  },
  {
    id: 'MCFG-038',
    name: 'Games Name',
    folderName: 'components/games-name',
    category: 'component',
    description: 'Game title display component with styling options',
    complexity: 'low',
    specificInputs: [
      'gameName: string = ""',
      'showIcon: boolean = true',
      'iconPosition: "left" | "right" | "top" = "left"',
      'truncate: boolean = false'
    ],
    gameEvents: ['nameClick', 'iconClick'],
    performance: 'Text truncation and icon optimization'
  },
  {
    id: 'MCFG-039',
    name: 'Games Play Button',
    folderName: 'components/games-play-button',
    category: 'component',
    description: 'Game start/action button with state management',
    complexity: 'low',
    specificInputs: [
      'label: string = "Play"',
      'state: "ready" | "playing" | "paused" | "disabled" = "ready"',
      'showIcon: boolean = true',
      'enablePulse: boolean = false'
    ],
    gameEvents: ['playClick', 'stateChange'],
    performance: 'Smooth state transitions and button animations'
  },
  {
    id: 'MCFG-040',
    name: 'Games Reset Button',
    folderName: 'components/games-reset-button',
    category: 'component',
    description: 'Game reset functionality with confirmation',
    complexity: 'low',
    specificInputs: [
      'label: string = "Reset"',
      'requireConfirmation: boolean = true',
      'confirmationMessage: string = "Are you sure?"',
      'showIcon: boolean = true'
    ],
    gameEvents: ['resetClick', 'confirmReset', 'cancelReset'],
    performance: 'Modal confirmation optimization'
  },
  {
    id: 'MCFG-041',
    name: 'Games Score',
    folderName: 'components/games-score',
    category: 'component',
    description: 'Real-time score display with animations',
    complexity: 'low',
    specificInputs: [
      'currentScore: number = 0',
      'highScore: number = 0',
      'showHighScore: boolean = true',
      'animateChanges: boolean = true',
      'prefix: string = ""'
    ],
    gameEvents: ['scoreChange', 'highScoreBeaten'],
    performance: 'Number animation and count-up effects'
  },
  {
    id: 'MCFG-042',
    name: 'Games Template',
    folderName: 'components/games-template',
    category: 'component',
    description: 'Base game layout template with common elements',
    complexity: 'medium',
    specificInputs: [
      'showHeader: boolean = true',
      'showFooter: boolean = true',
      'showSidebar: boolean = false',
      'enableFullscreen: boolean = true',
      'theme: "light" | "dark" | "game" = "game"'
    ],
    gameEvents: ['fullscreenToggle', 'themeChange', 'layoutChange'],
    performance: 'Responsive layout optimization'
  },
  {
    id: 'MCFG-043',
    name: 'Win Lose Overlay',
    folderName: 'components/win-lose-overlay',
    category: 'component',
    description: 'Game completion overlay with results and actions',
    complexity: 'medium',
    specificInputs: [
      'gameResult: "win" | "lose" | "draw" = "win"',
      'finalScore: number = 0',
      'showPlayAgain: boolean = true',
      'showShare: boolean = true',
      'customMessage: string = ""'
    ],
    gameEvents: ['playAgain', 'shareResult', 'closeOverlay', 'viewStats'],
    performance: 'Modal animation and social sharing optimization'
  },

  // Game Pages (7 components)
  {
    id: 'MCFG-044',
    name: 'All Games',
    folderName: 'all',
    category: 'page',
    description: 'Complete games library view with search and filtering',
    complexity: 'medium',
    specificInputs: [
      'games: any[] = []',
      'enableSearch: boolean = true',
      'enableFilters: boolean = true',
      'viewMode: "grid" | "list" = "grid"',
      'itemsPerPage: number = 12'
    ],
    gameEvents: ['gameSelect', 'searchInput', 'filterChange', 'viewModeChange'],
    performance: 'Virtual scrolling and efficient filtering'
  },
  {
    id: 'MCFG-045',
    name: 'Categories',
    folderName: 'categories',
    category: 'page',
    description: 'Games organized by category with navigation',
    complexity: 'medium',
    specificInputs: [
      'categories: any[] = []',
      'selectedCategory: string = ""',
      'showGameCount: boolean = true',
      'enableSubcategories: boolean = false'
    ],
    gameEvents: ['categorySelect', 'subcategorySelect', 'gameSelect'],
    performance: 'Category navigation and game loading optimization'
  },
  {
    id: 'MCFG-046',
    name: 'Dashboard',
    folderName: 'dashboard',
    category: 'page',
    description: 'Personal gaming dashboard with stats and recent games',
    complexity: 'high',
    specificInputs: [
      'userStats: any = {}',
      'recentGames: any[] = []',
      'achievements: any[] = []',
      'showProgress: boolean = true',
      'enableGoals: boolean = true'
    ],
    gameEvents: ['gameSelect', 'achievementView', 'goalSet', 'statsView'],
    performance: 'Dashboard data aggregation and chart rendering'
  },
  {
    id: 'MCFG-047',
    name: 'Favourites',
    folderName: 'favourites',
    category: 'page',
    description: 'User\'s favorite games collection with management',
    complexity: 'medium',
    specificInputs: [
      'favouriteGames: any[] = []',
      'enableReorder: boolean = true',
      'showLastPlayed: boolean = true',
      'enableRemove: boolean = true'
    ],
    gameEvents: ['gameSelect', 'gameRemove', 'gameReorder', 'listClear'],
    performance: 'Drag-and-drop reordering and list management'
  },
  {
    id: 'MCFG-048',
    name: 'Home',
    folderName: 'home',
    category: 'page',
    description: 'Main games landing page with featured content',
    complexity: 'medium',
    specificInputs: [
      'featuredGames: any[] = []',
      'newGames: any[] = []',
      'popularGames: any[] = []',
      'showCarousel: boolean = true',
      'autoRotate: boolean = true'
    ],
    gameEvents: ['gameSelect', 'carouselChange', 'sectionView'],
    performance: 'Image carousel optimization and lazy loading'
  },
  {
    id: 'MCFG-049',
    name: 'How to Play',
    folderName: 'how-to-play',
    category: 'page',
    description: 'Game instructions and tutorials with interactive elements',
    complexity: 'medium',
    specificInputs: [
      'instructions: any[] = []',
      'enableVideo: boolean = true',
      'enableInteractiveTutorial: boolean = false',
      'showTips: boolean = true'
    ],
    gameEvents: ['stepComplete', 'videoPlay', 'tutorialStart'],
    performance: 'Video loading optimization and interactive tutorial management'
  },
  {
    id: 'MCFG-050',
    name: 'Single Game',
    folderName: 'single',
    category: 'page',
    description: 'Individual game detail view with play options',
    complexity: 'medium',
    specificInputs: [
      'gameData: any = {}',
      'showScreenshots: boolean = true',
      'showReviews: boolean = true',
      'enableShare: boolean = true',
      'showSimilar: boolean = true'
    ],
    gameEvents: ['gamePlay', 'gameShare', 'reviewSubmit', 'similarGameSelect'],
    performance: 'Image gallery optimization and review loading'
  },

  // Game Utilities (3 components)
  {
    id: 'MCFG-051',
    name: 'Check In',
    folderName: 'check-in',
    category: 'utility',
    description: 'Daily/periodic game access with streak tracking',
    complexity: 'medium',
    specificInputs: [
      'checkInType: "daily" | "weekly" | "monthly" = "daily"',
      'currentStreak: number = 0',
      'maxStreak: number = 0',
      'rewards: any[] = []',
      'enableNotifications: boolean = true'
    ],
    gameEvents: ['checkInComplete', 'streakBroken', 'rewardClaimed'],
    performance: 'Date calculation optimization and notification scheduling'
  },
  {
    id: 'MCFG-052',
    name: 'Product Scan',
    folderName: 'product-scan',
    category: 'utility',
    description: 'QR/barcode scanning for rewards with camera integration',
    complexity: 'high',
    specificInputs: [
      'scanType: "qr" | "barcode" | "both" = "both"',
      'enableCamera: boolean = true',
      'enableUpload: boolean = true',
      'scanTimeout: number = 30000',
      'autoProcess: boolean = true'
    ],
    gameEvents: ['scanStart', 'scanSuccess', 'scanFailed', 'rewardEarned'],
    performance: 'Camera optimization and image processing efficiency'
  },
  {
    id: 'MCFG-053',
    name: 'Submit Selfie',
    folderName: 'submit-selfie',
    category: 'utility',
    description: 'Photo capture for challenges with filters and validation',
    complexity: 'medium',
    specificInputs: [
      'enableFilters: boolean = true',
      'requiredFaceCount: number = 1',
      'enableRetake: boolean = true',
      'maxFileSize: number = 5000000',
      'enableGeolocation: boolean = false'
    ],
    gameEvents: ['photoCapture', 'photoSubmit', 'photoRetake', 'challengeComplete'],
    performance: 'Camera stream optimization and image compression'
  }
];

// Story template function
function generateStoryContent(component) {
  const storyPoints = component.complexity === 'high' ? 8 : component.complexity === 'medium' ? 5 : 3;
  const componentPath = `/projects/mobile-components/src/lib/features/games/games/${component.folderName}/`;
  
  return `# Story ${component.id}: Enhance ${component.name} Game Component

## User Story
As a developer using the LP-GO builder, I want the ${component.name} game component to have comprehensive dynamic configuration, optimized performance, and seamless platform integration so that I can create engaging gamification experiences for loyalty program users.

## Current State Analysis
- Component: ${component.name} (${component.category} game)
- Complexity: ${component.complexity}
- Description: ${component.description}
- Path: \`${componentPath}\`
- Current implementation may lack full dynamic configuration for builder integration

## Acceptance Criteria
- [ ] Add comprehensive @Input() properties for game configuration
- [ ] Implement games-specific standard inputs (className, size, variant, etc.)
- [ ] Add game-specific configuration inputs for mechanics and behavior
- [ ] Implement proper TypeScript typing for all inputs and game state
- [ ] Add game event outputs for platform integration
- [ ] Optimize performance for smooth mobile gaming experience
- [ ] Ensure responsive design for all screen sizes
- [ ] Add proper error handling and game state management
- [ ] Implement accessibility features for inclusive gaming
- [ ] Add integration points for LP loyalty platform features

## Required Standard Game Inputs
\`\`\`typescript
// Base styling and configuration
@Input() className: string = '';
@Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
@Input() variant: 'default' | 'primary' | 'secondary' | 'accent' = 'default';
@Input() theme: 'light' | 'dark' | 'auto' = 'auto';
@Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

// Game mechanics
@Input() difficulty: 'easy' | 'medium' | 'hard' | 'expert' = 'medium';
@Input() gameLevel: number = 1;
@Input() maxLevel: number = 10;
@Input() timeLimit: number = 0; // 0 = no limit
@Input() lives: number = 3;

// Scoring and rewards
@Input() score: number = 0;
@Input() highScore: number = 0;
@Input() targetScore: number = 0;
@Input() rewardPoints: number = 10;

// Game state
@Input() gameState: 'idle' | 'playing' | 'paused' | 'completed' | 'failed' = 'idle';
@Input() autoStart: boolean = false;
@Input() autoReset: boolean = false;
@Input() saveProgress: boolean = true;

// Audio and effects
@Input() soundEnabled: boolean = true;
@Input() effectsEnabled: boolean = true;
@Input() vibrationEnabled: boolean = true;

// Performance and accessibility
@Input() frameRate: number = 60;
@Input() reducedMotion: boolean = false;
@Input() keyboardControls: boolean = true;
@Input() screenReaderSupport: boolean = true;
\`\`\`

## Game-Specific Configuration Inputs
\`\`\`typescript
${component.specificInputs.map(input => `@Input() ${input};`).join('\n')}
\`\`\`

## Required Game Event Outputs
\`\`\`typescript
// Standard game events
@Output() gameStart = new EventEmitter<GameEvent>();
@Output() gameEnd = new EventEmitter<GameEvent>();
@Output() gameScore = new EventEmitter<GameScoreEvent>();
@Output() gamePause = new EventEmitter<GameEvent>();
@Output() gameError = new EventEmitter<GameErrorEvent>();

// Game-specific events
${component.gameEvents.map(event => `@Output() ${event} = new EventEmitter<any>();`).join('\n')}
\`\`\`

## Performance Requirements
- ${component.performance}
- Maintain 60fps during active gameplay
- Efficient memory management for extended sessions
- Touch gesture optimization for mobile devices
- Battery usage optimization for mobile gaming
- Reduced motion support for accessibility

## Component-Specific Requirements

### Game Mechanics Implementation
- Implement core game logic with proper state management
- Add configurable difficulty levels and game parameters
- Ensure consistent game behavior across different devices
- Add proper win/lose condition handling
- Implement scoring system with bonus mechanics

### Platform Integration
- Integrate with LP loyalty points system for rewards
- Add achievement system connectivity
- Implement leaderboard data submission
- Add social sharing capabilities for game results
- Enable progress tracking across user sessions

### Mobile Optimization
- Touch-friendly controls with proper hit targets
- Responsive layout for various screen sizes
- Orientation change handling
- Safe area considerations for notched displays
- Gesture recognition for game controls

### Accessibility Features
- Keyboard navigation support
- Screen reader announcements for game state
- High contrast mode support
- Configurable text size options
- Alternative input methods for motor impairments

## Default Visual Appearance
- Component should render with meaningful game preview
- All styling should have sensible gaming-appropriate defaults
- Visual preview should clearly represent the game type
- Interactive elements should be clearly identifiable
- Game state should be visually obvious to users

## Implementation Tasks
1. Analyze existing component structure and game logic
2. Add all required @Input() properties with proper TypeScript typing
3. Implement game-specific configuration options
4. Add comprehensive @Output() events for platform integration
5. Optimize rendering performance for mobile devices
6. Implement responsive design patterns
7. Add accessibility features and ARIA attributes
8. Add error handling and recovery mechanisms
9. Implement game state persistence
10. Add integration points for LP platform features
11. Create comprehensive unit tests
12. Update component documentation

## Performance Benchmarks
- Initial load time: < 2 seconds
- Gameplay framerate: 60fps minimum
- Memory usage: < 50MB for extended sessions
- Battery impact: Minimal for casual gaming sessions
- Touch response time: < 100ms
- Game state save/load: < 500ms

## Testing Requirements
- Unit tests for all game logic and state management
- Performance testing on various device types
- Accessibility testing with screen readers
- Cross-platform compatibility testing
- Stress testing for extended gameplay sessions
- Integration testing with LP platform features

## Definition of Done
- Component uses @Input() decorators for all configuration
- All game-specific inputs are functional and properly typed
- Game events are properly emitted for platform integration
- Performance benchmarks are met on target devices
- Responsive design works on all supported screen sizes
- Accessibility requirements are fully implemented
- Component renders correctly in isolation and within builder
- Integration with LP loyalty platform is functional
- Comprehensive tests pass with > 90% coverage
- Documentation is complete and accurate

---
**Story Points**: ${storyPoints}  
**Priority**: High  
**Category**: ${component.category}  
**Component Path**: \`${componentPath}\`  
**Complexity**: ${component.complexity}`;
}

// Create the output directory
const outputDir = path.join(__dirname, 'docs', 'stories', 'features-games-enhancement');

function createDirectoryIfNotExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`Created directory: ${dirPath}`);
  }
}

function generateAllStories() {
  // Create the output directory
  createDirectoryIfNotExists(outputDir);
  
  console.log('Generating Games Features Enhancement Stories...');
  console.log(`Total components to process: ${gameComponents.length}`);
  
  let successCount = 0;
  let failureCount = 0;
  
  // Generate individual story files
  gameComponents.forEach((component, index) => {
    try {
      const storyContent = generateStoryContent(component);
      const fileName = `${component.id}-${component.folderName.replace(/\/.*/, '').replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}.md`;
      const filePath = path.join(outputDir, fileName);
      
      fs.writeFileSync(filePath, storyContent, 'utf8');
      console.log(`✓ Generated: ${fileName}`);
      successCount++;
    } catch (error) {
      console.error(`✗ Failed to generate story for ${component.name}: ${error.message}`);
      failureCount++;
    }
  });
  
  // Generate the summary README
  generateSummaryReadme();
  
  console.log(`\n=== Generation Complete ===`);
  console.log(`Successful: ${successCount}`);
  console.log(`Failed: ${failureCount}`);
  console.log(`Total: ${gameComponents.length}`);
  console.log(`Output directory: ${outputDir}`);
}

function generateSummaryReadme() {
  // Calculate story points
  const totalPoints = gameComponents.reduce((total, c) => total + (c.complexity === 'high' ? 8 : c.complexity === 'medium' ? 5 : 3), 0);
  const lowCount = gameComponents.filter(c => c.complexity === 'low').length;
  const mediumCount = gameComponents.filter(c => c.complexity === 'medium').length;
  const highCount = gameComponents.filter(c => c.complexity === 'high').length;

  // Generate category sections
  const arcadeGames = gameComponents.filter(c => c.category === 'arcade')
    .map(c => `- [${c.id}](./MCFG-${c.id.split('-')[1]}-${c.folderName.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}.md): ${c.name} - ${c.description}`)
    .join('\n');
  
  const cardBoardGames = gameComponents.filter(c => c.category === 'card' || c.category === 'board')
    .map(c => `- [${c.id}](./MCFG-${c.id.split('-')[1]}-${c.folderName.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}.md): ${c.name} - ${c.description}`)
    .join('\n');
    
  const puzzleGames = gameComponents.filter(c => c.category === 'puzzle')
    .map(c => `- [${c.id}](./MCFG-${c.id.split('-')[1]}-${c.folderName.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}.md): ${c.name} - ${c.description}`)
    .join('\n');
    
  const interactiveGames = gameComponents.filter(c => c.category === 'interactive')
    .map(c => `- [${c.id}](./MCFG-${c.id.split('-')[1]}-${c.folderName.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}.md): ${c.name} - ${c.description}`)
    .join('\n');
    
  const gameComponents_filtered = gameComponents.filter(c => c.category === 'component')
    .map(c => `- [${c.id}](./MCFG-${c.id.split('-')[1]}-${c.folderName.replace(/.*\//, '').replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}.md): ${c.name} - ${c.description}`)
    .join('\n');
    
  const gamePages = gameComponents.filter(c => c.category === 'page')
    .map(c => `- [${c.id}](./MCFG-${c.id.split('-')[1]}-${c.folderName.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}.md): ${c.name} - ${c.description}`)
    .join('\n');
    
  const gameUtilities = gameComponents.filter(c => c.category === 'utility')
    .map(c => `- [${c.id}](./MCFG-${c.id.split('-')[1]}-${c.folderName.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}.md): ${c.name} - ${c.description}`)
    .join('\n');

  const readmeContent = `# Games Features Enhancement Stories

## Overview
This directory contains 53 individual stories for enhancing all Games feature components in the Mobile Components library. Each story focuses on making a specific game component fully dynamic, performant, and integrated with the LP-GO builder system.

## Epic Reference
- **Epic**: [Features Games Enhancement](../../epics/epic-features-games-enhancement.md)
- **Story Prefix**: MCFG (Mobile Components Features Games)
- **Total Stories**: 53

## Component Categories

### Arcade Games (11 stories)
${arcadeGames}

### Card & Board Games (8 stories)
${cardBoardGames}

### Puzzle Games (6 stories)
${puzzleGames}

### Interactive Games (5 stories)
${interactiveGames}

### Game Components (11 stories)
${gameComponents_filtered}

### Game Pages (7 stories)
${gamePages}

### Game Utilities (3 stories)
${gameUtilities}

## Implementation Standards

### Core Requirements
All game components must implement:
- Comprehensive @Input() properties for dynamic configuration
- Game-specific mechanics and behavior customization
- Performance optimization for mobile gaming (60fps minimum)
- Responsive design for all screen sizes
- LP loyalty platform integration points
- Accessibility compliance (WCAG 2.1 AA standards)

### Games-Specific Features
- Real-time scoring and achievement systems
- Configurable difficulty levels and game parameters
- Touch-optimized controls for mobile devices
- Game state persistence across sessions
- Social sharing and leaderboard integration
- Error handling and recovery mechanisms

### Performance Targets
- Initial load time: < 2 seconds
- Gameplay framerate: 60fps minimum
- Memory usage: < 50MB for extended sessions
- Touch response time: < 100ms
- Battery optimization for mobile gaming

## Story Point Distribution
- **Low Complexity** (3 points): ${lowCount} stories
- **Medium Complexity** (5 points): ${mediumCount} stories
- **High Complexity** (8 points): ${highCount} stories
- **Total Estimated Points**: ${totalPoints}

## Implementation Phases

### Phase 1: Core Game Engine & Arcade Games (MCFG-001 to MCFG-011)
Focus on the most complex arcade games that require advanced game engines and physics.

### Phase 2: Card & Board Games (MCFG-012 to MCFG-019)
Implement strategic games with rule-based logic and AI components.

### Phase 3: Puzzle & Interactive Games (MCFG-020 to MCFG-030)
Add word games, puzzles, and interactive experiences.

### Phase 4: Game Components & UI (MCFG-031 to MCFG-043)
Build the supporting UI components for game display and interaction.

### Phase 5: Game Pages & Navigation (MCFG-044 to MCFG-050)
Create the page-level components for game organization and navigation.

### Phase 6: Game Utilities & Integration (MCFG-051 to MCFG-053)
Implement utility components for platform integration and advanced features.

## Success Metrics
- All 53 game components support comprehensive dynamic configuration
- Performance benchmarks met on target mobile devices
- Complete integration with LP loyalty platform features
- Accessibility compliance achieved across all components
- User engagement metrics show improvement in gaming features

---
*Generated for LP Angular Project - Games Features Enhancement Initiative*
*Total Stories: 53 | Categories: 7 | Estimated Story Points: ${totalPoints}*`;

  const readmePath = path.join(outputDir, 'README.md');
  fs.writeFileSync(readmePath, readmeContent, 'utf8');
  console.log(`✓ Generated: README.md`);
}

// Run the generation
if (require.main === module) {
  generateAllStories();
}

module.exports = {
  generateAllStories,
  gameComponents
};