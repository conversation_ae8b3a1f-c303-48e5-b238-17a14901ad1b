import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ErrorRoutingModule } from './error-routing.module';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { AngularSvgIconModule } from 'angular-svg-icon';


@NgModule({ declarations: [], imports: [ErrorRoutingModule, AngularSvgIconModule, CommonModule], providers: [provideHttpClient(withInterceptorsFromDi())] })
export class ErrorModule { }
