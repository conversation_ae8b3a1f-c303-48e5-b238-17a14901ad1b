import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DashboardComponent } from './dashboard.component';
import { NftComponent } from './pages/nft/nft.component';

const routes: Routes = [
  {
    path: '',
    component: DashboardComponent,
    children: [
      { path: '', redirectTo: 'nfts', pathMatch: 'full' },
      { path: 'nfts', component: NftComponent },
      {
        path: 'app-builder',
        loadComponent: () => import('./pages/app-builder/app-builder-config.component')
          .then(m => m.AppBuilderConfigComponent)
      },
      {
        path: 'notifications',
        loadChildren: () => import('./pages/notifications/notification-management.module')
          .then(m => m.NotificationManagementModule)
      },
      { path: '**', redirectTo: 'errors/404' },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class DashboardRoutingModule {}
