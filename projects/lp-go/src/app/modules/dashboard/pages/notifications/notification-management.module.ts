import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';

// Components
import { NotificationDashboardComponent } from './notification-dashboard/notification-dashboard.component';
import { NotificationTemplatesComponent } from './notification-templates/notification-templates.component';
import { NotificationGroupsComponent } from './notification-groups/notification-groups.component';
import { NotificationHistoryComponent } from './notification-history/notification-history.component';
import { NotificationAnalyticsComponent } from './notification-analytics/notification-analytics.component';
import { NotificationTemplateFormComponent } from './notification-template-form/notification-template-form.component';
import { NotificationGroupFormComponent } from './notification-group-form/notification-group-form.component';
import { NotificationSendComponent } from './notification-send/notification-send.component';

// Import mobile-components module wrapper
import { MobileComponentsWrapperModule } from '../../../../shared/modules/mobile-components-wrapper.module';

const routes: Routes = [
  {
    path: '',
    component: NotificationDashboardComponent,
    children: [
      { path: '', redirectTo: 'templates', pathMatch: 'full' },
      { path: 'templates', component: NotificationTemplatesComponent },
      { path: 'templates/new', component: NotificationTemplateFormComponent },
      { path: 'templates/edit/:id', component: NotificationTemplateFormComponent },
      { path: 'groups', component: NotificationGroupsComponent },
      { path: 'groups/new', component: NotificationGroupFormComponent },
      { path: 'groups/edit/:id', component: NotificationGroupFormComponent },
      { path: 'history', component: NotificationHistoryComponent },
      { path: 'analytics', component: NotificationAnalyticsComponent },
      { path: 'send', component: NotificationSendComponent },
    ]
  }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    HttpClientModule,
    RouterModule.forChild(routes),
    MobileComponentsWrapperModule,
    NotificationDashboardComponent,
    NotificationTemplatesComponent,
    NotificationGroupsComponent,
    NotificationHistoryComponent,
    NotificationAnalyticsComponent,
    NotificationTemplateFormComponent,
    NotificationGroupFormComponent,
    NotificationSendComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class NotificationManagementModule { }
