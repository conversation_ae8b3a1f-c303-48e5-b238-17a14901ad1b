import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-notification-dashboard',
  templateUrl: './notification-dashboard.component.html',
  styleUrls: ['./notification-dashboard.component.scss'],
  standalone: true,
  imports: [CommonModule, RouterModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class NotificationDashboardComponent implements OnInit {
  navItems = [
    { label: 'Templates', route: './templates', icon: 'document-text-outline' },
    { label: 'Groups', route: './groups', icon: 'layers-outline' },
    { label: 'History', route: './history', icon: 'time-outline' },
    { label: 'Analytics', route: './analytics', icon: 'bar-chart-outline' },
    { label: 'Send', route: './send', icon: 'paper-plane-outline' }
  ];

  constructor(private router: Router) { }

  ngOnInit(): void {
  }

  isActive(route: string): boolean {
    return this.router.url.includes(route);
  }
}
