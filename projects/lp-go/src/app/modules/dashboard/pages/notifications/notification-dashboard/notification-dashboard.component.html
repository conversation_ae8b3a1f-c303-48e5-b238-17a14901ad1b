<div class="container mx-auto px-4 py-6">
  <div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-800 dark:text-white">Push Notification Management</h1>
    <p class="text-gray-600 dark:text-gray-300">Manage and send push notifications to your users</p>
  </div>

  <div class="flex flex-col md:flex-row gap-6">
    <!-- Navigation Sidebar -->
    <div class="w-full md:w-64 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
      <nav>
        <ul class="space-y-2">
          <li *ngFor="let item of navItems">
            <a 
              [routerLink]="[item.route]" 
              routerLinkActive="bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300"
              class="flex items-center px-4 py-3 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              [class.bg-blue-100]="isActive(item.route)"
              [class.dark:bg-blue-900]="isActive(item.route)"
              [class.text-blue-600]="isActive(item.route)"
              [class.dark:text-blue-300]="isActive(item.route)"
            >
              <ion-icon [name]="item.icon" class="mr-3 text-lg"></ion-icon>
              <span>{{ item.label }}</span>
            </a>
          </li>
        </ul>
      </nav>
    </div>

    <!-- Content Area -->
    <div class="flex-1 bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <router-outlet></router-outlet>
    </div>
  </div>
</div>
