<div class="container">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Notification Analytics</h2>
    <button 
      (click)="loadAnalytics()" 
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
    >
      <ion-icon name="refresh-outline" class="mr-2"></ion-icon>
      Refresh
    </button>
  </div>

  <!-- Error Message -->
  <div *ngIf="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
    <p>{{ error }}</p>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="loading" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
  </div>

  <!-- Analytics Content -->
  <div *ngIf="!loading && analytics" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <!-- Notification Counts -->
    <div class="bg-white dark:bg-gray-700 rounded-lg shadow-md p-6">
      <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4">Notification Counts</h3>
      <div class="space-y-4">
        <div class="flex justify-between items-center">
          <span class="text-gray-600 dark:text-gray-300">Sent</span>
          <span class="text-gray-800 dark:text-white font-medium">{{ analytics.counts.sent }}</span>
        </div>
        <div class="flex justify-between items-center">
          <span class="text-gray-600 dark:text-gray-300">Delivered</span>
          <span class="text-gray-800 dark:text-white font-medium">{{ analytics.counts.delivered }}</span>
        </div>
        <div class="flex justify-between items-center">
          <span class="text-gray-600 dark:text-gray-300">Opened</span>
          <span class="text-gray-800 dark:text-white font-medium">{{ analytics.counts.opened }}</span>
        </div>
        <div class="flex justify-between items-center">
          <span class="text-gray-600 dark:text-gray-300">Dismissed</span>
          <span class="text-gray-800 dark:text-white font-medium">{{ analytics.counts.dismissed }}</span>
        </div>
        <div class="flex justify-between items-center">
          <span class="text-gray-600 dark:text-gray-300">Actions Clicked</span>
          <span class="text-gray-800 dark:text-white font-medium">{{ analytics.counts.actionClicked }}</span>
        </div>
      </div>
    </div>

    <!-- Notification Rates -->
    <div class="bg-white dark:bg-gray-700 rounded-lg shadow-md p-6">
      <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4">Performance Rates</h3>
      <div class="space-y-6">
        <div>
          <div class="flex justify-between items-center mb-2">
            <span class="text-gray-600 dark:text-gray-300">Delivery Rate</span>
            <span [ngClass]="getDeliveryRateColor()" class="font-medium">{{ formatPercentage(analytics.rates.delivery) }}</span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
            <div class="bg-blue-600 h-2.5 rounded-full" [style.width]="formatPercentage(analytics.rates.delivery)"></div>
          </div>
        </div>
        <div>
          <div class="flex justify-between items-center mb-2">
            <span class="text-gray-600 dark:text-gray-300">Open Rate</span>
            <span [ngClass]="getOpenRateColor()" class="font-medium">{{ formatPercentage(analytics.rates.open) }}</span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
            <div class="bg-green-600 h-2.5 rounded-full" [style.width]="formatPercentage(analytics.rates.open)"></div>
          </div>
        </div>
        <div>
          <div class="flex justify-between items-center mb-2">
            <span class="text-gray-600 dark:text-gray-300">Dismiss Rate</span>
            <span class="text-gray-800 dark:text-white font-medium">{{ formatPercentage(analytics.rates.dismiss) }}</span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
            <div class="bg-yellow-600 h-2.5 rounded-full" [style.width]="formatPercentage(analytics.rates.dismiss)"></div>
          </div>
        </div>
        <div>
          <div class="flex justify-between items-center mb-2">
            <span class="text-gray-600 dark:text-gray-300">Action Rate</span>
            <span [ngClass]="getActionRateColor()" class="font-medium">{{ formatPercentage(analytics.rates.action) }}</span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
            <div class="bg-purple-600 h-2.5 rounded-full" [style.width]="formatPercentage(analytics.rates.action)"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance Insights -->
    <div class="bg-white dark:bg-gray-700 rounded-lg shadow-md p-6 lg:col-span-1 md:col-span-2 lg:col-span-1">
      <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4">Performance Insights</h3>
      <div class="space-y-4">
        <div *ngIf="analytics.rates.delivery < 0.9" class="p-3 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 rounded-md">
          <div class="flex items-start">
            <ion-icon name="alert-circle-outline" class="mr-2 mt-0.5"></ion-icon>
            <div>
              <p class="font-medium">Low Delivery Rate</p>
              <p class="text-sm mt-1">Your delivery rate is below 90%. Check device token validity and network issues.</p>
            </div>
          </div>
        </div>
        <div *ngIf="analytics.rates.open < 0.15" class="p-3 bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 rounded-md">
          <div class="flex items-start">
            <ion-icon name="warning-outline" class="mr-2 mt-0.5"></ion-icon>
            <div>
              <p class="font-medium">Low Open Rate</p>
              <p class="text-sm mt-1">Your open rate is below 15%. Consider improving notification titles and timing.</p>
            </div>
          </div>
        </div>
        <div *ngIf="analytics.rates.action < 0.1" class="p-3 bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 rounded-md">
          <div class="flex items-start">
            <ion-icon name="warning-outline" class="mr-2 mt-0.5"></ion-icon>
            <div>
              <p class="font-medium">Low Action Rate</p>
              <p class="text-sm mt-1">Your action rate is below 10%. Try more compelling calls-to-action.</p>
            </div>
          </div>
        </div>
        <div *ngIf="analytics.rates.delivery >= 0.95" class="p-3 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-md">
          <div class="flex items-start">
            <ion-icon name="checkmark-circle-outline" class="mr-2 mt-0.5"></ion-icon>
            <div>
              <p class="font-medium">Excellent Delivery Rate</p>
              <p class="text-sm mt-1">Your delivery rate is above 95%. Great job!</p>
            </div>
          </div>
        </div>
        <div *ngIf="analytics.rates.open >= 0.3" class="p-3 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-md">
          <div class="flex items-start">
            <ion-icon name="checkmark-circle-outline" class="mr-2 mt-0.5"></ion-icon>
            <div>
              <p class="font-medium">Excellent Open Rate</p>
              <p class="text-sm mt-1">Your open rate is above 30%. Your notifications are engaging!</p>
            </div>
          </div>
        </div>
        <div *ngIf="analytics.rates.action >= 0.2" class="p-3 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-md">
          <div class="flex items-start">
            <ion-icon name="checkmark-circle-outline" class="mr-2 mt-0.5"></ion-icon>
            <div>
              <p class="font-medium">Excellent Action Rate</p>
              <p class="text-sm mt-1">Your action rate is above 20%. Your calls-to-action are effective!</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- No Data State -->
  <div *ngIf="!loading && (!analytics || (analytics.counts.sent === 0))" class="text-center py-8">
    <div class="text-gray-400 dark:text-gray-500 text-6xl mb-4">
      <ion-icon name="bar-chart-outline"></ion-icon>
    </div>
    <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">No Analytics Data Available</h3>
    <p class="text-gray-500 dark:text-gray-400 mb-4">Start sending notifications to see analytics data.</p>
  </div>
</div>
