<div class="container">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Notification History</h2>
  </div>

  <!-- Error Message -->
  <div *ngIf="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
    <p>{{ error }}</p>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="loading" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!loading && notificationHistory.length === 0" class="text-center py-8">
    <div class="text-gray-400 dark:text-gray-500 text-6xl mb-4">
      <ion-icon name="time-outline"></ion-icon>
    </div>
    <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">No Notification History</h3>
    <p class="text-gray-500 dark:text-gray-400">No notifications have been sent yet.</p>
  </div>

  <!-- History Table -->
  <div *ngIf="!loading && notificationHistory.length > 0" class="bg-white dark:bg-gray-700 rounded-lg shadow-md overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
        <thead class="bg-gray-50 dark:bg-gray-800">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Title
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Group
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Sent At
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Status
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Success/Failure
            </th>
            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
          <tr *ngFor="let notification of notificationHistory" class="hover:bg-gray-50 dark:hover:bg-gray-600">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900 dark:text-white">{{ notification.title }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="px-2 py-1 text-xs rounded-full" [ngClass]="getGroupClass(notification.group)">
                {{ notification.group }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-500 dark:text-gray-300">{{ formatDate(notification.sentAt) }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="px-2 py-1 text-xs rounded-full" [ngClass]="getStatusClass(notification.status)">
                {{ notification.status }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-500 dark:text-gray-300">
                {{ notification.successCount }} / {{ notification.failureCount }}
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <button 
                (click)="viewDetails(notification)" 
                class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
              >
                View Details
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Notification Details Modal -->
  <div *ngIf="selectedNotification" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
      <div class="p-6">
        <div class="flex justify-between items-start mb-4">
          <h3 class="text-xl font-semibold text-gray-800 dark:text-white">Notification Details</h3>
          <button 
            (click)="closeDetails()" 
            class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            <ion-icon name="close-outline" class="text-2xl"></ion-icon>
          </button>
        </div>

        <div class="space-y-4">
          <!-- Preview -->
          <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
            <div class="bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden">
              <div *ngIf="selectedNotification.imageUrl" class="w-full h-40 bg-gray-200 dark:bg-gray-700">
                <img 
                  [src]="selectedNotification.imageUrl" 
                  alt="Notification Image" 
                  class="w-full h-full object-cover"
                  onerror="this.src='assets/icons/image-outline.svg'; this.onerror=null;"
                >
              </div>
              <div class="p-4">
                <div class="flex items-start">
                  <div class="flex-shrink-0 mr-3">
                    <div class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white">
                      <ion-icon name="notifications-outline"></ion-icon>
                    </div>
                  </div>
                  <div>
                    <h4 class="font-semibold text-gray-800 dark:text-white">{{ selectedNotification.title }}</h4>
                    <p class="text-gray-600 dark:text-gray-300 text-sm mt-1">{{ selectedNotification.body }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Details -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Group</p>
              <p class="mt-1 text-sm text-gray-800 dark:text-white">
                <span class="px-2 py-1 text-xs rounded-full" [ngClass]="getGroupClass(selectedNotification.group)">
                  {{ selectedNotification.group }}
                </span>
              </p>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</p>
              <p class="mt-1 text-sm text-gray-800 dark:text-white">
                <span class="px-2 py-1 text-xs rounded-full" [ngClass]="getStatusClass(selectedNotification.status)">
                  {{ selectedNotification.status }}
                </span>
              </p>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Sent At</p>
              <p class="mt-1 text-sm text-gray-800 dark:text-white">{{ formatDate(selectedNotification.sentAt) }}</p>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Sent By</p>
              <p class="mt-1 text-sm text-gray-800 dark:text-white">{{ selectedNotification.sentBy || 'System' }}</p>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Success Count</p>
              <p class="mt-1 text-sm text-gray-800 dark:text-white">{{ selectedNotification.successCount }}</p>
            </div>
            <div>
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Failure Count</p>
              <p class="mt-1 text-sm text-gray-800 dark:text-white">{{ selectedNotification.failureCount }}</p>
            </div>
            <div *ngIf="selectedNotification.actionUrl" class="col-span-2">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Action URL</p>
              <p class="mt-1 text-sm text-gray-800 dark:text-white">{{ selectedNotification.actionUrl }}</p>
            </div>
            <div *ngIf="selectedNotification.data" class="col-span-2">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Additional Data</p>
              <pre class="mt-1 text-sm text-gray-800 dark:text-white bg-gray-100 dark:bg-gray-700 p-2 rounded overflow-x-auto">{{ selectedNotification.data | json }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
