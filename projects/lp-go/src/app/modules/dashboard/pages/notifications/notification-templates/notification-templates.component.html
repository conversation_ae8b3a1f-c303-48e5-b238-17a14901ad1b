<div class="container">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Notification Templates</h2>
    <button 
      (click)="createTemplate()" 
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
    >
      <ion-icon name="add-outline" class="mr-2"></ion-icon>
      Create Template
    </button>
  </div>

  <!-- Error Message -->
  <div *ngIf="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
    <p>{{ error }}</p>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="loading" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!loading && templates.length === 0" class="text-center py-8">
    <div class="text-gray-400 dark:text-gray-500 text-6xl mb-4">
      <ion-icon name="document-text-outline"></ion-icon>
    </div>
    <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">No Templates Found</h3>
    <p class="text-gray-500 dark:text-gray-400 mb-4">Create your first notification template to get started.</p>
    <button 
      (click)="createTemplate()" 
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
    >
      Create Template
    </button>
  </div>

  <!-- Templates List -->
  <div *ngIf="!loading && templates.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    <div *ngFor="let template of templates" class="bg-white dark:bg-gray-700 rounded-lg shadow-md overflow-hidden border border-gray-500 dark:border-gray-600">
      <div class="p-4">
        <div class="flex justify-between items-start mb-2">
          <h3 class="text-lg font-medium text-gray-800 dark:text-white">{{ template.title }}</h3>
          <span class="px-2 py-1 text-xs rounded-full" 
            [ngClass]="{
              'bg-green-100 text-green-800': template.group === 'general',
              'bg-blue-100 text-blue-800': template.group === 'promotions',
              'bg-purple-100 text-purple-800': template.group === 'transactions',
              'bg-yellow-100 text-yellow-800': template.group === 'games',
              'bg-gray-100 text-gray-800': !['general', 'promotions', 'transactions', 'games'].includes(template.group)
            }"
          >
            {{ template.group }}
          </span>
        </div>
        <p class="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">{{ template.body }}</p>
        
        <div class="flex justify-between items-center">
          <div class="flex space-x-2">
            <button 
              (click)="editTemplate(template.id)" 
              class="p-2 text-blue-600 hover:bg-blue-100 rounded-full transition-colors"
              title="Edit Template"
            >
              <ion-icon name="create-outline"></ion-icon>
            </button>
            <button 
              (click)="deleteTemplate(template.id)" 
              class="p-2 text-red-600 hover:bg-red-100 rounded-full transition-colors"
              title="Delete Template"
            >
              <ion-icon name="trash-outline"></ion-icon>
            </button>
          </div>
          <button 
            (click)="useTemplate(template)" 
            class="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
          >
            Use Template
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
