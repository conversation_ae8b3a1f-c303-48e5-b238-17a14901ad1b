<div class="container">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Notification Groups</h2>
    <button 
      (click)="createGroup()" 
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
    >
      <ion-icon name="add-outline" class="mr-2"></ion-icon>
      Create Group
    </button>
  </div>

  <!-- Error Message -->
  <div *ngIf="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
    <p>{{ error }}</p>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="loading" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!loading && groups.length === 0" class="text-center py-8">
    <div class="text-gray-400 dark:text-gray-500 text-6xl mb-4">
      <ion-icon name="layers-outline"></ion-icon>
    </div>
    <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">No Notification Groups</h3>
    <p class="text-gray-500 dark:text-gray-400 mb-4">Create your first notification group to categorize your notifications.</p>
    <button 
      (click)="createGroup()" 
      class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
    >
      Create Group
    </button>
  </div>

  <!-- Groups Grid -->
  <div *ngIf="!loading && groups.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    <div *ngFor="let group of groups" class="bg-white dark:bg-gray-700 rounded-lg shadow-md overflow-hidden border border-gray-500 dark:border-gray-600">
      <div class="p-4">
        <div class="flex items-start mb-4">
          <div class="flex-shrink-0 mr-3">
            <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex items-center justify-center">
              <ion-icon [name]="getIconClass(group.icon)"></ion-icon>
            </div>
          </div>
          <div>
            <h3 class="text-lg font-medium text-gray-800 dark:text-white">{{ group.name }}</h3>
            <p class="text-gray-600 dark:text-gray-300 text-sm">{{ group.description }}</p>
          </div>
        </div>
        
        <div class="flex items-center justify-between mt-4">
          <div class="flex items-center">
            <span class="text-sm text-gray-500 dark:text-gray-400 mr-2">Default:</span>
            <span 
              class="px-2 py-1 text-xs rounded-full"
              [ngClass]="group.defaultEnabled ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'"
            >
              {{ group.defaultEnabled ? 'Enabled' : 'Disabled' }}
            </span>
          </div>
          <div class="flex space-x-2">
            <button 
              (click)="editGroup(group.id)" 
              class="p-2 text-blue-600 hover:bg-blue-100 rounded-full transition-colors"
              title="Edit Group"
            >
              <ion-icon name="create-outline"></ion-icon>
            </button>
            <button 
              (click)="deleteGroup(group.id)" 
              class="p-2 text-red-600 hover:bg-red-100 rounded-full transition-colors"
              title="Delete Group"
              [disabled]="['general', 'promotions', 'transactions', 'games'].includes(group.id)"
            >
              <ion-icon name="trash-outline"></ion-icon>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
