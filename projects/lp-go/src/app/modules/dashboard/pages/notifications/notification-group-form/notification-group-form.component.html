<div class="container">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-xl font-semibold text-gray-800 dark:text-white">
      {{ isEditMode ? 'Edit' : 'Create' }} Notification Group
    </h2>
    <button 
      (click)="cancel()" 
      class="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors flex items-center"
    >
      <ion-icon name="arrow-back-outline" class="mr-2"></ion-icon>
      Back to Groups
    </button>
  </div>

  <!-- Error Message -->
  <div *ngIf="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
    <p>{{ error }}</p>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="loading" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
  </div>

  <!-- Group Form -->
  <form [formGroup]="groupForm" (ngSubmit)="onSubmit()" *ngIf="!loading" class="bg-white dark:bg-gray-700 rounded-lg shadow-md p-6">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- ID -->
      <div class="col-span-2 md:col-span-1">
        <label for="id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Group ID</label>
        <input 
          type="text" 
          id="id" 
          formControlName="id" 
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
          placeholder="group-id"
          [disabled]="isEditMode"
        >
        <div *ngIf="groupForm.get('id')?.invalid && groupForm.get('id')?.touched" class="text-red-500 text-sm mt-1">
          <div *ngIf="groupForm.get('id')?.errors?.['required']">ID is required</div>
          <div *ngIf="groupForm.get('id')?.errors?.['pattern']">ID must contain only lowercase letters, numbers, hyphens, and underscores</div>
        </div>
        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Used as a unique identifier for this group</p>
      </div>

      <!-- Name -->
      <div class="col-span-2 md:col-span-1">
        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Group Name</label>
        <input 
          type="text" 
          id="name" 
          formControlName="name" 
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
          placeholder="Group Name"
        >
        <div *ngIf="groupForm.get('name')?.invalid && groupForm.get('name')?.touched" class="text-red-500 text-sm mt-1">
          <div *ngIf="groupForm.get('name')?.errors?.['required']">Name is required</div>
          <div *ngIf="groupForm.get('name')?.errors?.['maxlength']">Name cannot exceed 50 characters</div>
        </div>
      </div>

      <!-- Description -->
      <div class="col-span-2">
        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description</label>
        <textarea 
          id="description" 
          formControlName="description" 
          rows="2"
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
          placeholder="Group Description"
        ></textarea>
        <div *ngIf="groupForm.get('description')?.invalid && groupForm.get('description')?.touched" class="text-red-500 text-sm mt-1">
          <div *ngIf="groupForm.get('description')?.errors?.['required']">Description is required</div>
          <div *ngIf="groupForm.get('description')?.errors?.['maxlength']">Description cannot exceed 100 characters</div>
        </div>
      </div>

      <!-- Icon Selection -->
      <div class="col-span-2">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Icon</label>
        <div class="grid grid-cols-6 sm:grid-cols-8 md:grid-cols-12 gap-2 mt-2">
          <div 
            *ngFor="let icon of icons" 
            class="w-10 h-10 rounded-full flex items-center justify-center cursor-pointer transition-colors"
            [ngClass]="groupForm.get('icon')?.value === icon ? 
              'bg-blue-500 text-white' : 
              'bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-500'"
            (click)="selectIcon(icon)"
          >
            <ion-icon [name]="icon"></ion-icon>
          </div>
        </div>
      </div>

      <!-- Default Enabled -->
      <div class="col-span-2">
        <div class="flex items-center">
          <input 
            type="checkbox" 
            id="defaultEnabled" 
            formControlName="defaultEnabled" 
            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          >
          <label for="defaultEnabled" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
            Enabled by default for new users
          </label>
        </div>
        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 ml-6">
          If checked, users will receive notifications from this group by default
        </p>
      </div>
    </div>

    <!-- Preview -->
    <div class="mt-8 mb-6">
      <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4">Preview</h3>
      <div class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg border border-gray-500 dark:border-gray-700">
        <div class="max-w-sm mx-auto bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden p-4">
          <div class="flex items-start">
            <div class="flex-shrink-0 mr-3">
              <div class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white">
                <ion-icon [name]="groupForm.get('icon')?.value"></ion-icon>
              </div>
            </div>
            <div>
              <h4 class="font-semibold text-gray-800 dark:text-white">{{ groupForm.get('name')?.value || 'Group Name' }}</h4>
              <p class="text-gray-600 dark:text-gray-300 text-sm mt-1">{{ groupForm.get('description')?.value || 'Group description will appear here' }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="flex justify-end space-x-4 mt-6">
      <button 
        type="button"
        (click)="cancel()" 
        class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
      >
        Cancel
      </button>
      <button 
        type="submit" 
        [disabled]="groupForm.invalid || saving"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
      >
        <div *ngIf="saving" class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
        {{ isEditMode ? 'Update' : 'Create' }} Group
      </button>
    </div>
  </form>
</div>
