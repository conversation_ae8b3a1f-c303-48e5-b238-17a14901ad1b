import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import {
  NotificationManagementService,
  NotificationTemplate,
  NotificationGroup,
  NotificationRequest,
  NotificationResponse
} from '../../../../../shared/services/notification-management.service';

@Component({
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule],
  selector: 'app-notification-send',
  templateUrl: './notification-send.component.html',
  styleUrls: ['./notification-send.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class NotificationSendComponent implements OnInit {
  sendForm: FormGroup;
  templates: NotificationTemplate[] = [];
  groups: NotificationGroup[] = [];
  loading = false;
  sending = false;
  error = '';
  success = '';
  selectedTemplate: NotificationTemplate | null = null;

  // Target options
  targetOptions = [
    { id: 'all', label: 'All Users' },
    { id: 'groups', label: 'Specific Groups' },
    { id: 'topic', label: 'Topic' }
  ];

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private notificationService: NotificationManagementService
  ) {
    this.sendForm = this.fb.group({
      templateId: ['', Validators.required],
      target: ['all', Validators.required],
      groups: [[]],
      topic: [''],
      customTitle: [''],
      customBody: [''],
      scheduleTime: ['']
    });

    // Check if a template was passed via router state
    const navigation = this.router.getCurrentNavigation();
    if (navigation?.extras.state?.['template']) {
      this.selectedTemplate = navigation.extras.state['template'];
    }
  }

  ngOnInit(): void {
    this.loadTemplates();
    this.loadGroups();

    // Set up form value changes listener
    this.sendForm.get('templateId')?.valueChanges.subscribe(templateId => {
      if (templateId) {
        this.loadTemplateDetails(templateId);
      } else {
        this.selectedTemplate = null;
      }
    });

    this.sendForm.get('target')?.valueChanges.subscribe(target => {
      if (target === 'groups') {
        this.sendForm.get('groups')?.setValidators([Validators.required]);
        this.sendForm.get('topic')?.clearValidators();
      } else if (target === 'topic') {
        this.sendForm.get('topic')?.setValidators([Validators.required]);
        this.sendForm.get('groups')?.clearValidators();
      } else {
        this.sendForm.get('groups')?.clearValidators();
        this.sendForm.get('topic')?.clearValidators();
      }

      this.sendForm.get('groups')?.updateValueAndValidity();
      this.sendForm.get('topic')?.updateValueAndValidity();
    });

    // If a template was passed, select it
    if (this.selectedTemplate && this.selectedTemplate.id) {
      this.sendForm.patchValue({
        templateId: this.selectedTemplate.id
      });
    }
  }

  loadTemplates(): void {
    this.loading = true;
    this.notificationService.getTemplates().subscribe({
      next: (templates) => {
        this.templates = templates;
        this.loading = false;
      },
      error: (err) => {
        console.error('Error loading templates:', err);
        this.error = 'Failed to load notification templates. Please try again.';
        this.loading = false;
      }
    });
  }

  loadGroups(): void {
    this.notificationService.getGroups().subscribe({
      next: (groups) => {
        this.groups = groups;
      },
      error: (err) => {
        console.error('Error loading notification groups:', err);
        this.error = 'Failed to load notification groups. Please try again.';
      }
    });
  }

  loadTemplateDetails(templateId: string): void {
    // First check if we already have the template in our list
    const template = this.templates.find(t => t.id === templateId);
    if (template) {
      this.selectedTemplate = template;
      return;
    }

    // Otherwise load it from the service
    this.notificationService.getTemplate(templateId).subscribe({
      next: (template) => {
        if (template) {
          this.selectedTemplate = template;
        }
      },
      error: (err) => {
        console.error('Error loading template details:', err);
        this.error = 'Failed to load template details. Please try again.';
      }
    });
  }

  onSubmit(): void {
    if (this.sendForm.invalid || !this.selectedTemplate) {
      return;
    }

    this.sending = true;
    this.error = '';
    this.success = '';

    const formValues = this.sendForm.value;

    // Prepare notification data
    const notification = {
      title: formValues.customTitle || this.selectedTemplate.title,
      body: formValues.customBody || this.selectedTemplate.body,
      imageUrl: this.selectedTemplate.imageUrl,
      actionUrl: this.selectedTemplate.actionUrl,
      group: this.selectedTemplate.group,
      data: this.selectedTemplate.data || {}
    };

    // Prepare request based on target
    const request: NotificationRequest = {
      notification
    };

    if (formValues.target === 'groups') {
      request.groups = formValues.groups;
    } else if (formValues.target === 'topic') {
      request.topic = formValues.topic;
    }

    // Send notification
    this.notificationService.sendNotification(request).subscribe({
      next: (response: NotificationResponse) => {
        this.sending = false;
        if (response.success) {
          this.success = 'Notification sent successfully!';
          // Reset form after success
          this.sendForm.get('customTitle')?.reset();
          this.sendForm.get('customBody')?.reset();
          this.sendForm.get('scheduleTime')?.reset();
        } else {
          this.error = response.error || 'Failed to send notification. Please try again.';
        }
      },
      error: (err) => {
        console.error('Error sending notification:', err);
        this.error = 'Failed to send notification. Please try again.';
        this.sending = false;
      }
    });
  }
}
