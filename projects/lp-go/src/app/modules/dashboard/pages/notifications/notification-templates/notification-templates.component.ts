import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { NotificationManagementService, NotificationTemplate } from '../../../../../shared/services/notification-management.service';

@Component({
  selector: 'app-notification-templates',
  templateUrl: './notification-templates.component.html',
  styleUrls: ['./notification-templates.component.scss'],
  standalone: true,
  imports: [CommonModule, RouterModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class NotificationTemplatesComponent implements OnInit {
  templates: NotificationTemplate[] = [];
  loading = true;
  error = '';

  constructor(
    private notificationService: NotificationManagementService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.loadTemplates();
  }

  loadTemplates(): void {
    this.loading = true;
    this.notificationService.getTemplates().subscribe({
      next: (templates) => {
        this.templates = templates;
        this.loading = false;
      },
      error: (err) => {
        console.error('Error loading templates:', err);
        this.error = 'Failed to load notification templates. Please try again.';
        this.loading = false;
      }
    });
  }

  createTemplate(): void {
    this.router.navigate(['dashboard', 'notifications', 'templates', 'new']);
  }

  editTemplate(id: string | undefined): void {
    if (id) {
      this.router.navigate(['dashboard', 'notifications', 'templates', 'edit', id]);
    }
  }

  deleteTemplate(id: string | undefined): void {
    if (!id) return;

    if (confirm('Are you sure you want to delete this template?')) {
      this.notificationService.deleteTemplate(id).subscribe({
        next: () => {
          this.templates = this.templates.filter(t => t.id !== id);
        },
        error: (err) => {
          console.error('Error deleting template:', err);
          this.error = 'Failed to delete template. Please try again.';
        }
      });
    }
  }

  useTemplate(template: NotificationTemplate): void {
    this.router.navigate(['dashboard', 'notifications', 'send'], {
      state: { template }
    });
  }
}
