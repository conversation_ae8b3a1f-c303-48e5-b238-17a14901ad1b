<div class="container">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-xl font-semibold text-gray-800 dark:text-white">
      {{ isEditMode ? 'Edit' : 'Create' }} Notification Template
    </h2>
    <button 
      (click)="cancel()" 
      class="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors flex items-center"
    >
      <ion-icon name="arrow-back-outline" class="mr-2"></ion-icon>
      Back to Templates
    </button>
  </div>

  <!-- Error Message -->
  <div *ngIf="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
    <p>{{ error }}</p>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="loading" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
  </div>

  <!-- Template Form -->
  <form [formGroup]="templateForm" (ngSubmit)="onSubmit()" *ngIf="!loading" class="bg-white dark:bg-gray-700 rounded-lg shadow-md p-6">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Title -->
      <div class="col-span-2">
        <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Title</label>
        <input 
          type="text" 
          id="title" 
          formControlName="title" 
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
          placeholder="Notification Title"
        >
        <div *ngIf="templateForm.get('title')?.invalid && templateForm.get('title')?.touched" class="text-red-500 text-sm mt-1">
          <div *ngIf="templateForm.get('title')?.errors?.['required']">Title is required</div>
          <div *ngIf="templateForm.get('title')?.errors?.['maxlength']">Title cannot exceed 100 characters</div>
        </div>
      </div>

      <!-- Body -->
      <div class="col-span-2">
        <label for="body" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Body</label>
        <textarea 
          id="body" 
          formControlName="body" 
          rows="4"
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
          placeholder="Notification Message"
        ></textarea>
        <div *ngIf="templateForm.get('body')?.invalid && templateForm.get('body')?.touched" class="text-red-500 text-sm mt-1">
          <div *ngIf="templateForm.get('body')?.errors?.['required']">Body is required</div>
          <div *ngIf="templateForm.get('body')?.errors?.['maxlength']">Body cannot exceed 255 characters</div>
        </div>
      </div>

      <!-- Image URL -->
      <div class="col-span-2 md:col-span-1">
        <label for="imageUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Image URL (Optional)</label>
        <input 
          type="url" 
          id="imageUrl" 
          formControlName="imageUrl" 
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
          placeholder="https://example.com/image.jpg"
        >
        <div *ngIf="templateForm.get('imageUrl')?.invalid && templateForm.get('imageUrl')?.touched" class="text-red-500 text-sm mt-1">
          <div *ngIf="templateForm.get('imageUrl')?.errors?.['pattern']">Please enter a valid URL</div>
        </div>
      </div>

      <!-- Action URL -->
      <div class="col-span-2 md:col-span-1">
        <label for="actionUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Action URL (Optional)</label>
        <input 
          type="text" 
          id="actionUrl" 
          formControlName="actionUrl" 
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
          placeholder="app://open/screen or https://example.com"
        >
      </div>

      <!-- Group -->
      <div class="col-span-2">
        <label for="group" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Notification Group</label>
        <select 
          id="group" 
          formControlName="group" 
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
        >
          <option *ngFor="let group of groups" [value]="group.id">{{ group.name }}</option>
        </select>
        <div *ngIf="templateForm.get('group')?.invalid && templateForm.get('group')?.touched" class="text-red-500 text-sm mt-1">
          <div *ngIf="templateForm.get('group')?.errors?.['required']">Group is required</div>
        </div>
      </div>
    </div>

    <!-- Preview -->
    <div class="mt-8 mb-6">
      <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4">Preview</h3>
      <div class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg border border-gray-500 dark:border-gray-700">
        <div class="max-w-sm mx-auto bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden">
          <div *ngIf="templateForm.get('imageUrl')?.value" class="w-full h-40 bg-gray-200 dark:bg-gray-700">
            <img 
              [src]="templateForm.get('imageUrl')?.value" 
              alt="Notification Image" 
              class="w-full h-full object-cover"
              onerror="this.src='assets/icons/image-outline.svg'; this.onerror=null;"
            >
          </div>
          <div class="p-4">
            <div class="flex items-start">
              <div class="flex-shrink-0 mr-3">
                <div class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white">
                  <ion-icon name="notifications-outline"></ion-icon>
                </div>
              </div>
              <div>
                <h4 class="font-semibold text-gray-800 dark:text-white">{{ templateForm.get('title')?.value || 'Notification Title' }}</h4>
                <p class="text-gray-600 dark:text-gray-300 text-sm mt-1">{{ templateForm.get('body')?.value || 'Notification message will appear here' }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="flex justify-end space-x-4 mt-6">
      <button 
        type="button"
        (click)="cancel()" 
        class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
      >
        Cancel
      </button>
      <button 
        type="submit" 
        [disabled]="templateForm.invalid || saving"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
      >
        <div *ngIf="saving" class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
        {{ isEditMode ? 'Update' : 'Create' }} Template
      </button>
    </div>
  </form>
</div>
