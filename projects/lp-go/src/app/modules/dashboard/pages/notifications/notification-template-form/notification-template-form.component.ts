import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { NotificationManagementService, NotificationTemplate, NotificationGroup } from '../../../../../shared/services/notification-management.service';

@Component({
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule],
  selector: 'app-notification-template-form',
  templateUrl: './notification-template-form.component.html',
  styleUrls: ['./notification-template-form.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class NotificationTemplateFormComponent implements OnInit {
  templateForm: FormGroup;
  templateId: string | null = null;
  isEditMode = false;
  loading = false;
  saving = false;
  error = '';
  groups: NotificationGroup[] = [];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private notificationService: NotificationManagementService
  ) {
    this.templateForm = this.fb.group({
      title: ['', [Validators.required, Validators.maxLength(100)]],
      body: ['', [Validators.required, Validators.maxLength(255)]],
      imageUrl: ['', Validators.pattern('https?://.+')],
      actionUrl: [''],
      group: ['general', Validators.required],
      data: this.fb.group({})
    });
  }

  ngOnInit(): void {
    this.loadGroups();

    this.templateId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.templateId;

    if (this.isEditMode && this.templateId) {
      this.loadTemplate(this.templateId);
    }
  }

  loadGroups(): void {
    this.notificationService.getGroups().subscribe({
      next: (groups) => {
        this.groups = groups;

        // If no groups exist, create default ones
        if (groups.length === 0) {
          this.createDefaultGroups();
        }
      },
      error: (err) => {
        console.error('Error loading notification groups:', err);
        this.error = 'Failed to load notification groups. Please try again.';
      }
    });
  }

  createDefaultGroups(): void {
    const defaultGroups: NotificationGroup[] = [
      {
        id: 'general',
        name: 'General Notifications',
        description: 'Important updates and announcements',
        icon: 'notifications-outline',
        defaultEnabled: true
      },
      {
        id: 'promotions',
        name: 'Promotions & Offers',
        description: 'Special offers, discounts and promotions',
        icon: 'gift-outline',
        defaultEnabled: true
      },
      {
        id: 'transactions',
        name: 'Transactions',
        description: 'Updates about your points and transactions',
        icon: 'card-outline',
        defaultEnabled: true
      },
      {
        id: 'games',
        name: 'Games',
        description: 'Game-related notifications and rewards',
        icon: 'game-controller-outline',
        defaultEnabled: true
      }
    ];

    // Create each default group
    defaultGroups.forEach(group => {
      this.notificationService.createGroup(group).subscribe({
        next: (id) => {
          console.log(`Created default group: ${group.name} with ID: ${id}`);
          this.groups.push({ ...group, id });
        },
        error: (err) => {
          console.error(`Error creating default group ${group.name}:`, err);
        }
      });
    });
  }

  loadTemplate(id: string): void {
    this.loading = true;
    this.notificationService.getTemplate(id).subscribe({
      next: (template) => {
        if (template) {
          this.templateForm.patchValue({
            title: template.title,
            body: template.body,
            imageUrl: template.imageUrl || '',
            actionUrl: template.actionUrl || '',
            group: template.group || 'general',
            data: template.data || {}
          });
        } else {
          this.error = 'Template not found';
          this.router.navigate(['dashboard', 'notifications', 'templates']);
        }
        this.loading = false;
      },
      error: (err) => {
        console.error('Error loading template:', err);
        this.error = 'Failed to load template. Please try again.';
        this.loading = false;
      }
    });
  }

  onSubmit(): void {
    if (this.templateForm.invalid) {
      return;
    }

    this.saving = true;
    const templateData: NotificationTemplate = this.templateForm.value;

    if (this.isEditMode && this.templateId) {
      this.notificationService.updateTemplate(this.templateId, templateData).subscribe({
        next: () => {
          this.saving = false;
          this.router.navigate(['dashboard', 'notifications', 'templates']);
        },
        error: (err) => {
          console.error('Error updating template:', err);
          this.error = 'Failed to update template. Please try again.';
          this.saving = false;
        }
      });
    } else {
      this.notificationService.createTemplate(templateData).subscribe({
        next: () => {
          this.saving = false;
          this.router.navigate(['dashboard', 'notifications', 'templates']);
        },
        error: (err) => {
          console.error('Error creating template:', err);
          this.error = 'Failed to create template. Please try again.';
          this.saving = false;
        }
      });
    }
  }

  cancel(): void {
    this.router.navigate(['dashboard', 'notifications', 'templates']);
  }
}
