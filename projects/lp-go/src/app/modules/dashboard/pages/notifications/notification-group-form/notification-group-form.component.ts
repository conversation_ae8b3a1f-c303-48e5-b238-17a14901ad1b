import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { NotificationManagementService, NotificationGroup } from '../../../../../shared/services/notification-management.service';

@Component({
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule],
  selector: 'app-notification-group-form',
  templateUrl: './notification-group-form.component.html',
  styleUrls: ['./notification-group-form.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class NotificationGroupFormComponent implements OnInit {
  groupForm: FormGroup;
  groupId: string | null = null;
  isEditMode = false;
  loading = false;
  saving = false;
  error = '';

  // Available icons
  icons = [
    'notifications-outline',
    'alert-outline',
    'gift-outline',
    'card-outline',
    'game-controller-outline',
    'megaphone-outline',
    'mail-outline',
    'calendar-outline',
    'star-outline',
    'heart-outline',
    'trophy-outline',
    'rocket-outline'
  ];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private notificationService: NotificationManagementService
  ) {
    this.groupForm = this.fb.group({
      id: ['', [Validators.required, Validators.pattern('[a-z0-9-_]+')]],
      name: ['', [Validators.required, Validators.maxLength(50)]],
      description: ['', [Validators.required, Validators.maxLength(100)]],
      icon: ['notifications-outline'],
      defaultEnabled: [true]
    });
  }

  ngOnInit(): void {
    this.groupId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.groupId;

    if (this.isEditMode && this.groupId) {
      this.loadGroup(this.groupId);

      // Disable ID field in edit mode
      this.groupForm.get('id')?.disable();
    }
  }

  loadGroup(id: string): void {
    this.loading = true;
    this.notificationService.getGroup(id).subscribe({
      next: (group) => {
        if (group) {
          this.groupForm.patchValue({
            id: group.id,
            name: group.name,
            description: group.description,
            icon: group.icon || 'notifications-outline',
            defaultEnabled: group.defaultEnabled
          });
        } else {
          this.error = 'Group not found';
          this.router.navigate(['dashboard', 'notifications', 'groups']);
        }
        this.loading = false;
      },
      error: (err) => {
        console.error('Error loading group:', err);
        this.error = 'Failed to load group. Please try again.';
        this.loading = false;
      }
    });
  }

  onSubmit(): void {
    if (this.groupForm.invalid) {
      return;
    }

    this.saving = true;
    const groupData: NotificationGroup = {
      ...this.groupForm.getRawValue() // Use getRawValue to include disabled fields
    };

    if (this.isEditMode && this.groupId) {
      this.notificationService.updateGroup(this.groupId, groupData).subscribe({
        next: () => {
          this.saving = false;
          this.router.navigate(['dashboard', 'notifications', 'groups']);
        },
        error: (err) => {
          console.error('Error updating group:', err);
          this.error = 'Failed to update group. Please try again.';
          this.saving = false;
        }
      });
    } else {
      this.notificationService.createGroup(groupData).subscribe({
        next: () => {
          this.saving = false;
          this.router.navigate(['dashboard', 'notifications', 'groups']);
        },
        error: (err) => {
          console.error('Error creating group:', err);
          this.error = 'Failed to create group. Please try again.';
          this.saving = false;
        }
      });
    }
  }

  cancel(): void {
    this.router.navigate(['dashboard', 'notifications', 'groups']);
  }

  selectIcon(icon: string): void {
    this.groupForm.patchValue({ icon });
  }
}
