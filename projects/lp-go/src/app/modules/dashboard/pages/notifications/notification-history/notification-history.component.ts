import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

import { NotificationManagementService, NotificationHistory } from '../../../../../shared/services/notification-management.service';

@Component({
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule],
  selector: 'app-notification-history',
  templateUrl: './notification-history.component.html',
  styleUrls: ['./notification-history.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class NotificationHistoryComponent implements OnInit {
  notificationHistory: NotificationHistory[] = [];
  loading = true;
  error = '';
  selectedNotification: NotificationHistory | null = null;

  constructor(private notificationService: NotificationManagementService) { }

  ngOnInit(): void {
    this.loadHistory();
  }

  loadHistory(): void {
    this.loading = true;
    this.notificationService.getNotificationHistory().subscribe({
      next: (history) => {
        this.notificationHistory = history;
        this.loading = false;
      },
      error: (err) => {
        console.error('Error loading notification history:', err);
        this.error = 'Failed to load notification history. Please try again.';
        this.loading = false;
      }
    });
  }

  viewDetails(notification: NotificationHistory): void {
    this.selectedNotification = notification;
  }

  closeDetails(): void {
    this.selectedNotification = null;
  }

  formatDate(date: any): string {
    if (!date) return 'N/A';

    if (typeof date === 'object' && date.seconds) {
      // Handle Firestore Timestamp
      return new Date(date.seconds * 1000).toLocaleString();
    }

    return new Date(date).toLocaleString();
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'sent':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getGroupClass(group: string): string {
    switch (group) {
      case 'general':
        return 'bg-blue-100 text-blue-800';
      case 'promotions':
        return 'bg-purple-100 text-purple-800';
      case 'transactions':
        return 'bg-green-100 text-green-800';
      case 'games':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }
}
