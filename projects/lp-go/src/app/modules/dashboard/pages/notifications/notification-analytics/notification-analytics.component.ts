import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

import { NotificationManagementService, NotificationAnalytics } from '../../../../../shared/services/notification-management.service';

@Component({
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule],
  selector: 'app-notification-analytics',
  templateUrl: './notification-analytics.component.html',
  styleUrls: ['./notification-analytics.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class NotificationAnalyticsComponent implements OnInit {
  analytics: NotificationAnalytics | null = null;
  loading = true;
  error = '';

  constructor(private notificationService: NotificationManagementService) { }

  ngOnInit(): void {
    this.loadAnalytics();
  }

  loadAnalytics(): void {
    this.loading = true;
    this.notificationService.getAnalytics().subscribe({
      next: (analytics) => {
        this.analytics = analytics;
        this.loading = false;
      },
      error: (err) => {
        console.error('Error loading notification analytics:', err);
        this.error = 'Failed to load notification analytics. Please try again.';
        this.loading = false;
      }
    });
  }

  formatPercentage(value: number): string {
    return (value * 100).toFixed(1) + '%';
  }

  getDeliveryRateColor(): string {
    if (!this.analytics) return 'text-gray-500';

    const rate = this.analytics.rates.delivery;
    if (rate >= 0.95) return 'text-green-500';
    if (rate >= 0.9) return 'text-yellow-500';
    return 'text-red-500';
  }

  getOpenRateColor(): string {
    if (!this.analytics) return 'text-gray-500';

    const rate = this.analytics.rates.open;
    if (rate >= 0.3) return 'text-green-500';
    if (rate >= 0.15) return 'text-yellow-500';
    return 'text-red-500';
  }

  getActionRateColor(): string {
    if (!this.analytics) return 'text-gray-500';

    const rate = this.analytics.rates.action;
    if (rate >= 0.2) return 'text-green-500';
    if (rate >= 0.1) return 'text-yellow-500';
    return 'text-red-500';
  }
}
