import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { NotificationManagementService, NotificationGroup } from '../../../../../shared/services/notification-management.service';

@Component({
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, RouterModule],
  selector: 'app-notification-groups',
  templateUrl: './notification-groups.component.html',
  styleUrls: ['./notification-groups.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class NotificationGroupsComponent implements OnInit {
  groups: NotificationGroup[] = [];
  loading = true;
  error = '';

  constructor(
    private notificationService: NotificationManagementService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.loadGroups();
  }

  loadGroups(): void {
    this.loading = true;
    this.notificationService.getGroups().subscribe({
      next: (groups) => {
        this.groups = groups;
        this.loading = false;

        // If no groups exist, create default ones
        if (groups.length === 0) {
          this.createDefaultGroups();
        }
      },
      error: (err) => {
        console.error('Error loading notification groups:', err);
        this.error = 'Failed to load notification groups. Please try again.';
        this.loading = false;
      }
    });
  }

  createDefaultGroups(): void {
    const defaultGroups: NotificationGroup[] = [
      {
        id: 'general',
        name: 'General Notifications',
        description: 'Important updates and announcements',
        icon: 'notifications-outline',
        defaultEnabled: true
      },
      {
        id: 'promotions',
        name: 'Promotions & Offers',
        description: 'Special offers, discounts and promotions',
        icon: 'gift-outline',
        defaultEnabled: true
      },
      {
        id: 'transactions',
        name: 'Transactions',
        description: 'Updates about your points and transactions',
        icon: 'card-outline',
        defaultEnabled: true
      },
      {
        id: 'games',
        name: 'Games',
        description: 'Game-related notifications and rewards',
        icon: 'game-controller-outline',
        defaultEnabled: true
      }
    ];

    // Create each default group
    defaultGroups.forEach(group => {
      this.notificationService.createGroup(group).subscribe({
        next: (id) => {
          console.log(`Created default group: ${group.name} with ID: ${id}`);
          this.groups.push({ ...group, id });
        },
        error: (err) => {
          console.error(`Error creating default group ${group.name}:`, err);
        }
      });
    });
  }

  createGroup(): void {
    this.router.navigate(['dashboard', 'notifications', 'groups', 'new']);
  }

  editGroup(id: string): void {
    this.router.navigate(['dashboard', 'notifications', 'groups', 'edit', id]);
  }

  deleteGroup(id: string): void {
    if (confirm('Are you sure you want to delete this group? This will affect all notifications in this group.')) {
      this.notificationService.deleteGroup(id).subscribe({
        next: () => {
          this.groups = this.groups.filter(g => g.id !== id);
        },
        error: (err) => {
          console.error('Error deleting group:', err);
          this.error = 'Failed to delete group. Please try again.';
        }
      });
    }
  }

  getIconClass(icon: string | undefined): string {
    return icon || 'notifications-outline';
  }
}
