<div class="container">
  <div class="flex justify-between items-center mb-6">
    <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Send Notification</h2>
  </div>

  <!-- Error Message -->
  <div *ngIf="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
    <p>{{ error }}</p>
  </div>

  <!-- Success Message -->
  <div *ngIf="success" class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4" role="alert">
    <p>{{ success }}</p>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="loading" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
  </div>

  <!-- Send Form -->
  <form [formGroup]="sendForm" (ngSubmit)="onSubmit()" *ngIf="!loading" class="bg-white dark:bg-gray-700 rounded-lg shadow-md p-6">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Template Selection -->
      <div class="col-span-2">
        <label for="templateId" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Notification Template</label>
        <select 
          id="templateId" 
          formControlName="templateId" 
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
        >
          <option value="">Select a template</option>
          <option *ngFor="let template of templates" [value]="template.id">{{ template.title }}</option>
        </select>
        <div *ngIf="sendForm.get('templateId')?.invalid && sendForm.get('templateId')?.touched" class="text-red-500 text-sm mt-1">
          <div *ngIf="sendForm.get('templateId')?.errors?.['required']">Please select a template</div>
        </div>
      </div>

      <!-- Target Selection -->
      <div class="col-span-2">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Target Audience</label>
        <div class="flex flex-wrap gap-4">
          <div *ngFor="let option of targetOptions" class="flex items-center">
            <input 
              type="radio" 
              [id]="'target-' + option.id" 
              [value]="option.id" 
              formControlName="target" 
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600"
            >
            <label [for]="'target-' + option.id" class="ml-2 text-sm text-gray-700 dark:text-gray-300">{{ option.label }}</label>
          </div>
        </div>
      </div>

      <!-- Groups Selection (shown when target is 'groups') -->
      <div class="col-span-2" *ngIf="sendForm.get('target')?.value === 'groups'">
        <label for="groups" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Select Groups</label>
        <select 
          id="groups" 
          formControlName="groups" 
          multiple
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
          size="4"
        >
          <option *ngFor="let group of groups" [value]="group.id">{{ group.name }}</option>
        </select>
        <div *ngIf="sendForm.get('groups')?.invalid && sendForm.get('groups')?.touched" class="text-red-500 text-sm mt-1">
          <div *ngIf="sendForm.get('groups')?.errors?.['required']">Please select at least one group</div>
        </div>
      </div>

      <!-- Topic Input (shown when target is 'topic') -->
      <div class="col-span-2" *ngIf="sendForm.get('target')?.value === 'topic'">
        <label for="topic" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Topic Name</label>
        <input 
          type="text" 
          id="topic" 
          formControlName="topic" 
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
          placeholder="Enter topic name"
        >
        <div *ngIf="sendForm.get('topic')?.invalid && sendForm.get('topic')?.touched" class="text-red-500 text-sm mt-1">
          <div *ngIf="sendForm.get('topic')?.errors?.['required']">Topic name is required</div>
        </div>
      </div>

      <!-- Custom Title (Optional) -->
      <div class="col-span-2 md:col-span-1">
        <label for="customTitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Custom Title (Optional)</label>
        <input 
          type="text" 
          id="customTitle" 
          formControlName="customTitle" 
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
          placeholder="Override template title"
        >
      </div>

      <!-- Schedule Time (Optional) -->
      <div class="col-span-2 md:col-span-1">
        <label for="scheduleTime" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Schedule Time (Optional)</label>
        <input 
          type="datetime-local" 
          id="scheduleTime" 
          formControlName="scheduleTime" 
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
        >
      </div>

      <!-- Custom Body (Optional) -->
      <div class="col-span-2">
        <label for="customBody" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Custom Body (Optional)</label>
        <textarea 
          id="customBody" 
          formControlName="customBody" 
          rows="3"
          class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
          placeholder="Override template body"
        ></textarea>
      </div>
    </div>

    <!-- Preview -->
    <div class="mt-8 mb-6" *ngIf="selectedTemplate">
      <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-4">Preview</h3>
      <div class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg border border-gray-500 dark:border-gray-700">
        <div class="max-w-sm mx-auto bg-white dark:bg-gray-900 rounded-lg shadow-md overflow-hidden">
          <div *ngIf="selectedTemplate.imageUrl" class="w-full h-40 bg-gray-200 dark:bg-gray-700">
            <img 
              [src]="selectedTemplate.imageUrl" 
              alt="Notification Image" 
              class="w-full h-full object-cover"
              onerror="this.src='assets/icons/image-outline.svg'; this.onerror=null;"
            >
          </div>
          <div class="p-4">
            <div class="flex items-start">
              <div class="flex-shrink-0 mr-3">
                <div class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white">
                  <ion-icon name="notifications-outline"></ion-icon>
                </div>
              </div>
              <div>
                <h4 class="font-semibold text-gray-800 dark:text-white">
                  {{ sendForm.get('customTitle')?.value || selectedTemplate.title }}
                </h4>
                <p class="text-gray-600 dark:text-gray-300 text-sm mt-1">
                  {{ sendForm.get('customBody')?.value || selectedTemplate.body }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="flex justify-end space-x-4 mt-6">
      <button 
        type="submit" 
        [disabled]="sendForm.invalid || sending || !selectedTemplate"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
      >
        <div *ngIf="sending" class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
        <ion-icon name="paper-plane-outline" class="mr-2"></ion-icon>
        Send Notification
      </button>
    </div>
  </form>
</div>
