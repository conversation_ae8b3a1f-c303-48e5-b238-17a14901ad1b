import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { finalize, of, catchError, timeout } from 'rxjs';

@Component({
  selector: 'app-app-builder-config',
  templateUrl: './app-builder-config.component.html',
  styleUrls: ['./app-builder-config.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule]
})
export class AppBuilderConfigComponent {
  configParams = {
    client: '',
    env: 'DEV',
    version: '0'
  };

  environments = [
    { id: 'DEV', name: 'Development' },
    { id: 'QA', name: 'Quality Assurance' },
    { id: 'PROD', name: 'Production' }
  ];

  isLoading = false;
  errorMessage = '';
  useMockData = true; // Set to true to use mock data for development

  // We're not using the router to avoid the forRoot guard issue
  private http: HttpClient = inject(HttpClient);

  onSubmit() {
    if (!this.configParams.client) {
      alert('Please enter a client ID');
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    // Generate a config ID based on the parameters
    const configId = `${this.configParams.client}_${this.configParams.env}_${this.configParams.version}`;
    console.log('Generated config ID:', configId);

    if (this.useMockData) {
      // Use mock data for development to avoid API calls
      setTimeout(() => {
        const mockConfig = this.generateMockConfig(this.configParams);
        console.log('Using mock configuration:', mockConfig);

        // Store the mock config
        localStorage.setItem(`app_config_${configId}`, JSON.stringify(mockConfig));

        this.isLoading = false;

        // Navigate to the builder with this config
        console.log('Navigating to builder with config ID:', configId);
        try {
          // Add a delay to see if there are any console errors
          console.log('About to navigate to:', `/builder-app/${configId}`);
          
          // Add a flag to detect navigation cycles and to help with debugging
          sessionStorage.setItem('last_config', configId);
          sessionStorage.setItem('navigation_timestamp', Date.now().toString());
          
          // Set a longer delay to make sure console is visible
          setTimeout(() => {
            console.log('DEBUG: About to navigate with configId:', configId);
            // Use direct window.location navigation to avoid router issues
            try {
              // Store debug info in case we need to recover
              localStorage.setItem('debug_last_location', window.location.href);
              localStorage.setItem('debug_target_location', `/builder-app/${configId}`);
              
              // Handle navigation with error trapping
              window.location.href = `/builder-app/${configId}`;
            } catch (navError) {
              console.error('Navigation execution error:', navError);
              alert(`Navigation error: ${navError?.toString() || 'Unknown error'}`);
            }
          }, 5000); // 5 second delay to see console output
        } catch (error: any) {
          console.error('Navigation error:', error);
          this.errorMessage = `Navigation error: ${error?.message || 'Unknown error'}`;
          this.isLoading = false;
        }
      }, 1000);
    } else {
      // Fetch configuration from the API with proper headers
      const url = `https://esbdev.lp.run/internal/loyaltyapi/1.0.0/mobile/config?version=${this.configParams.version}&env=${this.configParams.env}&client=${this.configParams.client}`;
      console.log('Loading configuration from:', url);

      const headers = new HttpHeaders({
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      });

      try {
        this.http.get(url, { headers })
          .pipe(
            timeout(5000), // 5 second timeout
            catchError(error => {
              console.error('API Error, using mock data instead:', error);
              return of(this.generateMockConfig(this.configParams));
            }),
            finalize(() => this.isLoading = false)
          )
          .subscribe({
            next: (config: any) => {
              console.log('Configuration loaded successfully:', config);

              // Store the config in localStorage for builder to access
              localStorage.setItem(`app_config_${configId}`, JSON.stringify(config));

              // Navigate to the builder with this config
              console.log('Navigating to builder with config ID:', configId);
              try {
                // Add a delay to see if there are any console errors
                console.log('About to navigate to:', `/builder-app/${configId}`);
                setTimeout(() => {
                  // Use direct window.location navigation to avoid router issues
                  console.log('DEBUG: About to redirect to builder app in 2 seconds');
                  
                  // Add error handling to the URL navigation
                  try {
                    console.log(`DEBUG: Setting window.location.href to /builder-app/${configId}`);
                    // Use session storage to detect navigation cycles
                    sessionStorage.setItem('redirect_attempt', 'true');
                    window.location.href = `/builder-app/${configId}`;
                  } catch (e) {
                    console.error('Navigation error during redirect:', e);
                  }
                }, 2000); // 2 second delay
              } catch (error: any) {
                console.error('Navigation error:', error);
                this.errorMessage = `Navigation error: ${error?.message || 'Unknown error'}`;
                this.isLoading = false;
              }
            },
            error: (error) => {
              console.error('Error loading configuration:', error);
              this.errorMessage = `Failed to load configuration: ${error.message || 'Unknown error'}`;
            }
          });
      } catch (error: any) {
        console.error('Error loading configuration:', error);
        this.errorMessage = `Failed to load configuration: ${error.message || 'Unknown error'}`;
      }
    }
  }

  /**
   * Generate a mock configuration for development
   */
  private generateMockConfig(params: {client: string, env: string, version: string}) {
    const timestamp = new Date().toISOString();

    return {
      id: `${params.client}_${params.env}_${params.version}`,
      name: `${params.client} Configuration`,
      version: params.version,
      client: params.client,
      environment: params.env,
      createdAt: timestamp,
      updatedAt: timestamp,
      owner: 'current_user',
      pages: {
        'home': {
          id: 'home',
          name: 'Home Page',
          components: ['root_container'],
          type: 'builder',
          layout: 'fluid',
          styles: {
            backgroundColor: 'bg-white'
          }
        },
        'about': {
          id: 'about',
          name: 'About Page',
          components: ['about_container'],
          type: 'builder',
          layout: 'fluid',
          styles: {
            backgroundColor: 'bg-gray-50'
          }
        }
      },
      components: [
        {
          id: 'root_container',
          type: 'container',
          parentId: null,
          isContainer: true,
          properties: {
            tailwindClasses: 'flex flex-col p-4 border border-gray-500 rounded-md bg-white'
          },
          children: ['text_component', 'button_component']
        },
        {
          id: 'text_component',
          type: 'text',
          parentId: 'root_container',
          isContainer: false,
          properties: {
            tailwindClasses: 'text-gray-800 text-base',
            content: 'This is a sample text for the app builder.'
          },
          children: []
        },
        {
          id: 'button_component',
          type: 'button',
          parentId: 'root_container',
          isContainer: false,
          properties: {
            tailwindClasses: 'px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600',
            text: 'Click Me'
          },
          children: []
        },
        {
          id: 'about_container',
          type: 'container',
          parentId: null,
          isContainer: true,
          properties: {
            tailwindClasses: 'flex flex-col p-4 border border-gray-500 rounded-md bg-gray-50'
          },
          children: ['about_text']
        },
        {
          id: 'about_text',
          type: 'text',
          parentId: 'about_container',
          isContainer: false,
          properties: {
            tailwindClasses: 'text-gray-800 text-xl font-bold',
            content: 'About Us Page'
          },
          children: []
        }
      ],
      settings: {
        theme: 'default'
      }
    };
  }
}
