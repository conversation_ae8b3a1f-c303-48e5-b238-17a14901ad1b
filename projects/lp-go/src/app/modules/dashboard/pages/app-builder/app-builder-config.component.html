<div class="app-builder-config">
  <div class="card p-6 shadow-lg">
    <h2 class="text-2xl font-bold mb-6">App Builder Configuration</h2>
    
    <form (ngSubmit)="onSubmit()" class="space-y-6">
      <div class="form-group">
        <label for="client" class="block text-sm font-medium text-gray-700 mb-1">Client ID</label>
        <input 
          type="text" 
          id="client" 
          name="client"
          [(ngModel)]="configParams.client" 
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Enter client ID"
          [disabled]="isLoading"
          required>
      </div>
      
      <div class="form-group">
        <label for="env" class="block text-sm font-medium text-gray-700 mb-1">Environment</label>
        <select 
          id="env" 
          name="env"
          [(ngModel)]="configParams.env" 
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          [disabled]="isLoading">
          <option *ngFor="let env of environments" [value]="env.id">{{env.name}}</option>
        </select>
      </div>
      
      <div class="form-group">
        <label for="version" class="block text-sm font-medium text-gray-700 mb-1">Version</label>
        <input 
          type="text" 
          id="version" 
          name="version"
          [(ngModel)]="configParams.version" 
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Enter version (e.g. 1.0.0)"
          [disabled]="isLoading">
      </div>
      
      <!-- Error message -->
      <div *ngIf="errorMessage" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
        {{ errorMessage }}
      </div>
      
      <div class="form-group mt-4">
        <label class="flex items-center">
          <input 
            type="checkbox" 
            name="useMockData"
            [(ngModel)]="useMockData" 
            class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
          <span class="ml-2 text-sm text-gray-700">Use mock data (avoid API calls)</span>
        </label>
        <p class="text-xs text-gray-500 mt-1">Enable this option if you're having connection issues with the API</p>
      </div>
      
      <div class="form-actions mt-8">
        <button 
          type="submit" 
          class="w-full px-4 py-2 bg-blue-500 text-white font-medium rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          [disabled]="isLoading">
          <span *ngIf="isLoading" class="inline-block mr-2">
            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          {{ isLoading ? 'Loading Configuration...' : (useMockData ? 'Open App Builder with Mock Data' : 'Open App Builder') }}
        </button>
      </div>
    </form>
  </div>
</div>
