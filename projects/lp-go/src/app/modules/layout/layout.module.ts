import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AngularSvgIconModule } from 'angular-svg-icon';
import { LayoutComponent } from './layout.component';
import { layoutRoutes } from './layout-routing.module';
import { SidebarComponent } from './components/sidebar/sidebar.component';
import { NavbarComponent } from './components/navbar/navbar.component';
import { FooterComponent } from './components/footer/footer.component';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    RouterModule.forChild(layoutRoutes),
    AngularSvgIconModule.forRoot(),
    LayoutComponent,
    FooterComponent,
    SidebarComponent, // Standalone, import here
    NavbarComponent   // Standalone, import here
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [],
  exports: [
    LayoutComponent,
    FooterComponent,
    SidebarComponent, // Standalone, export here
    NavbarComponent   // Standalone, export here
  ]
})
export class LayoutModule { }
