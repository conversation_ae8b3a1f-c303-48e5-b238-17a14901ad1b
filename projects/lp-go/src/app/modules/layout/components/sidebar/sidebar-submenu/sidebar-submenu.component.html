<div
  class="max-h-0 overflow-hidden pt-1 transition-all duration-500 ltr:pl-4 rtl:pr-4"
  [ngClass]="{ hidden: !menuService.showSideBar, 'max-h-screen': submenu.expanded }">
  <ul
    class="border-border text-muted-foreground flex flex-col border-dashed ltr:border-l ltr:pl-2 rtl:border-r rtl:pr-2">
    <li *ngFor="let sub of submenu.children">
      <div class="text-muted-foreground hover:text-primary relative flex" (click)="toggleMenu(sub)">
        <!-- Condition -->
        <ng-container
          [ngTemplateOutlet]="sub.children ? childMenu : parentMenu"
          [ngTemplateOutletContext]="{ sub: sub }">
        </ng-container>

        <!-- Parent Menu -->
        <ng-template #parentMenu let-sub="sub">
          <a
            [routerLink]="sub.route"
            routerLinkActive="text-primary"
            [routerLinkActiveOptions]="{ exact: true }"
            class="hover:bg-card inline-block w-full rounded-lg px-4 py-2 text-xs">
            {{ sub.label }}
          </a>
          @if(menuService.isActive(sub.route)){
          <span
            class="size-1.5 bg-primary absolute flex translate-y-3.5 items-center rounded-full ltr:-translate-x-[11.5px] rtl:translate-x-[11.5px]">
          </span>
          }
        </ng-template>

        <!-- Child Menu -->
        <ng-template #childMenu let-sub="sub">
          <a class="inline-block w-full cursor-pointer px-4 py-2 text-xs">
            {{ sub.label }}
          </a>
          <button
            [ngClass]="{ hidden: !menuService.showSideBar }"
            class="text-foreground/50 flex cursor-pointer items-center justify-end px-2 transition-all duration-500">
            @if(!sub.expanded){
            <svg-icon src="assets/icons/heroicons/outline/plus.svg" svgClass="h-4 w-4"> </svg-icon>
            }@else {
            <svg-icon src="assets/icons/heroicons/outline/minus.svg" svgClass="h-4 w-4"> </svg-icon>
            }
          </button>
        </ng-template>
      </div>
      <!-- Submenu items -->
      @if(sub.children?.length){
      <app-sidebar-submenu [submenu]="sub"></app-sidebar-submenu>
      }
    </li>
  </ul>
</div>
