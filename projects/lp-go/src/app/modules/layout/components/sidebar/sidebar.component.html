<nav
  [ngClass]="menuService.showSideBar ? 'w-[210px] xl:w-[280px]' : 'w-[70px]'"
  class="bg-background hidden h-full flex-col justify-between pt-3 transition-all duration-300 lg:flex">
  <div class="overflow-hidden">
    <!-- Logo -->
    <div
      [ngClass]="menuService.showSideBar ? 'justify-between' : 'justify-center'"
      class="mx-4 mb-2 flex h-9 items-center">
      <div class="flex items-center" *ngIf="menuService.showSideBar">
        <a
          (click)="toggleSidebar()"
          class="bg-primary flex cursor-pointer items-center justify-center rounded-sm p-2 focus:outline-hidden focus:ring-1">
          <svg-icon src="assets/icons/logo.svg"> </svg-icon>
        </a>
        <b class="text-foreground ps-2 ml-1 grow text-sm font-bold">
          {{ appJson.displayName }}
        </b>
      </div>
      <button
        (click)="toggleSidebar()"
        class="text-muted-foreground/50 hover:text-muted-foreground flex items-center justify-center rounded-md p-2 transition-all duration-200 focus:outline-hidden"
        [ngClass]="{ 'rotate-180': !menuService.showSideBar }">
        <svg-icon src="assets/icons/heroicons/solid/chevron-double-left.svg"> </svg-icon>
      </button>
    </div>

    <!-- Menu Items -->
    <div
      class="scrollbar-thumb--sm scrollbar-track-rounded scrollbar-thin scrollbar-track-transparent scrollbar-thumb-card h-full overflow-auto px-4">
      <app-sidebar-menu></app-sidebar-menu>
    </div>
  </div>

  <div class="mx-4 my-4 space-y-1">
    <!-- Version -->
    <a
      target="_blank"
      href="https://github.com/luciano-work/angular-tailwind"
      class="-sm hover:bg-card group flex h-9 cursor-pointer items-center justify-start p-2">
      <svg-icon
        src="assets/icons/heroicons/outline/information-circle.svg"
        [svgClass]="'h-5 w-5 text-muted-foreground/50'">
      </svg-icon>

      <div class="ml-3 truncate text-[10px] font-semibold tracking-wide focus:outline-hidden">
        <span class="bg-primary/10 text-primary px-2 font-semibold">v{{ appJson.version }}</span>
      </div>

      <div class="fixed w-full" *ngIf="!menuService.showSideBar">
        <span
          class="z-1 bg-foreground text-background absolute left-12 -top-4 w-auto min-w-max origin-left scale-0 p-2 text-xs font-bold shadow-md transition-all duration-200 group-hover:scale-100">
          v{{ appJson.version }}
        </span>
      </div>
    </a>
  </div>
</nav>
