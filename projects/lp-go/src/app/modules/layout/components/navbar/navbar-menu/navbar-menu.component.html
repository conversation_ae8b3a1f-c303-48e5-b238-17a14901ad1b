<div class="dropdown relative inline-block" *ngFor="let menu of menuService.pagesMenu">
  <!-- Button -->
  <button
    [ngClass]="
      menu.selected || menu.active
        ? 'bg-primary text-primary-foreground'
        : 'text-muted-foreground/50 hover:bg-card hover:text-muted-foreground '
    "
    class="mr-2 inline-flex rounded-md px-3 py-2 text-sm font-medium">
    <span>{{ menu.group }}</span>
  </button>
  <!-- Dropdown  -->
  <div
    class="dropdown-content absolute top-[100%] min-w-[200px] origin-top-left z-10"
    navbar-submenu
    [submenu]="menu.items"></div>
</div>
