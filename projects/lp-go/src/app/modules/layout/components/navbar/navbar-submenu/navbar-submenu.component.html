<ul #submenuRef class="bg-background shadow-custom mt-2 space-y-0.5 rounded-md py-3">
  <li class="flex font-semibold" *ngFor="let item of submenu">
    <!-- Condition -->
    <ng-container
      [ngTemplateOutlet]="item.children ? childMenu : parentMenu"
      [ngTemplateOutletContext]="{ item: item }">
    </ng-container>

    <!-- Parent Menu -->
    <ng-template #parentMenu let-item="item">
      <button
        [routerLink]="item.route"
        routerLinkActive="text-primary"
        [routerLinkActiveOptions]="{ exact: true }"
        class="text-muted-foreground hover:bg-card hover:text-foreground mx-3 flex w-full items-center justify-between rounded-md py-2 px-2 text-xs font-semibold">
        <div class="flex items-center justify-start">
          <span class="text-muted-foreground/50 ltr:mr-2 rtl:ml-2" *ngIf="item.icon">
            <svg-icon src="{{ item.icon }}" [svgClass]="'h-5 w-5'"> </svg-icon>
          </span>
          <span class="ml-1">{{ item.label }}</span>
        </div>
      </button>
    </ng-template>

    <!-- Child Menu -->
    <ng-template #childMenu let-item="item">
      <div class="dropdown relative flex w-full">
        <button
          class="text-muted-foreground hover:bg-card hover:text-foreground mx-3 flex w-full items-center justify-between rounded-md py-2 px-2 text-xs font-semibold">
          <div class="flex items-center justify-start">
            <span class="text-muted-foreground/50 ltr:mr-2 rtl:ml-2" *ngIf="item.icon">
              <svg-icon src="{{ item.icon }}" [svgClass]="'h-5 w-5'"> </svg-icon>
            </span>
            <span class="ml-1">{{ item.label }}</span>
          </div>
          <span class="rtl:rotate-180">
            <svg-icon
              src="assets/icons/heroicons/solid/chevron-right.svg"
              [svgClass]="'h-5 w-5 text-muted-foreground/50'">
            </svg-icon>
          </span>
        </button>
        <!-- Submenu Dropdown -->
        <div
          class="dropdown-content absolute top-0 min-w-[200px] ltr:left-[100%] ltr:origin-top-left rtl:right-[100%] rtl:origin-top-right"
          navbar-submenu
          [submenu]="item.children"></div>
      </div>
    </ng-template>
  </li>
</ul>
