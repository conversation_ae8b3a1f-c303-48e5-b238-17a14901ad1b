<ng-container>
  <td class="text-center">
    <input
      [(ngModel)]="user.selected"
      class="checkbox checkbox-sm"
      data-datatable-row-check="true"
      type="checkbox"
      value="28" />
  </td>
  <td>
    <div class="flex items-center gap-2.5">
      <img
        alt="user avatar"
        class="h-9 w-9 shrink-0 rounded-full"
        src="https://ui-avatars.com/api/?name={{ user.name }}&background=random" />
      <div class="flex flex-col items-start">
        <a class="text-foreground hover:text-primary text-sm font-semibold" href="#"> {{ user.name }} </a>
        <a class="text-muted-foreground/70 hover:text-primary text-xs font-medium" href="#">
          {{ user.email }}
        </a>
      </div>
    </div>
  </td>
  <td>{{ user.username }}</td>
  <td class="space-x-1">
    @for (hobbie of user.hobbies; track $index) {
    <span class="rounded-[30px] bg-yellow-500/10 px-2 py-0.5 text-xs font-medium text-yellow-800">
      {{ hobbie }}
    </span>
    }
  </td>
  <td>
    {{ user.occupation }}
  </td>
  <td>{{ user.phone }}</td>
  <td class="text-center">
    <button
      class="text-muted-foreground hover:bg-card hover:text-foreground flex h-7 w-7 items-center justify-center rounded-md">
      <svg-icon src="./assets/icons/heroicons/outline/ellipsis-vertical.svg" [svgClass]="'h-4 w-4'"> </svg-icon>
    </button>
  </td>
</ng-container>
