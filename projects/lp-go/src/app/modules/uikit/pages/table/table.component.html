<div class="mb-4 flex justify-between">
  <div class="inline-block">
    <h3 class="text-foreground font-semibold">Team Members</h3>
    <div class="text-muted-foreground space-x-1 text-xs font-medium">
      <a href="" class="hover:text-primary">All Members:</a>
      <span class="text-foreground">49,053</span>
    </div>
  </div>
  <div class="inline-block space-x-4">
    <button
      class="bg-muted text-muted-foreground hover:text-foreground flex-none rounded-md px-4 py-2.5 text-xs font-semibold">
      Import CSV
    </button>
    <button class="bg-primary text-primary-foreground flex-none rounded-md px-4 py-2.5 text-xs font-semibold">
      Add Member
    </button>
  </div>
</div>

<div class="border-muted/20 bg-background flex min-w-full flex-col rounded-xl border p-2">
  <app-table-action></app-table-action>
  <div
    class="scrollbar-thumb-rounded scrollbar-track-rounded scrollbar-thin scrollbar-track-transparent scrollbar-thumb-muted grow overflow-x-auto px-5">
    <table
      class="text-muted-foreground table w-full table-auto border-collapse border-0 text-left align-middle leading-5">
      <thead class="border-muted/20 text-muted-foreground border text-xs">
        <tr app-table-header (onCheck)="toggleUsers($event)"></tr>
      </thead>
      <tbody>
        @for (user of filteredUsers(); track $index) {
        <tr class="hover:bg-card/50" app-table-row [user]="user"></tr>
        } @empty {
        <tr>
          <td class="py-4 text-center text-sm" colspan="7">No users found</td>
        </tr>
        }
      </tbody>
    </table>
  </div>
  <app-table-footer></app-table-footer>
</div>
