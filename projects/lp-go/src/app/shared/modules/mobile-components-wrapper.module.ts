import { NgModule, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

/**
 * This module serves as a wrapper for mobile-components to ensure they are properly
 * loaded and configured in the application. It helps prevent router duplicate forRoot guard errors
 * by isolating the mobile-components imports from the main application router.
 *
 * Note: When importing notification components from mobile-components library,
 * make sure to import them directly in the component where they are used,
 * or add them to this module's imports and exports arrays.
 */
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule.forRoot() // Use forRoot here to avoid multiple instances
  ],
  exports: [
    CommonModule,
    IonicModule,
    FormsModule,
    ReactiveFormsModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class MobileComponentsWrapperModule { }
