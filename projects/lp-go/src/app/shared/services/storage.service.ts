import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface UserPreferences {
  recentComponents: string[];
  favoriteComponents: string[];
  lastUsedCategories: string[];
}

export interface PropertyGroupState {
  [componentType: string]: {
    [groupId: string]: boolean; // expanded state
  };
}

export interface PropertyPreferences {
  groupStates: PropertyGroupState;
  searchHistory: string[];
  showAdvanced: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class StorageService {
  private readonly STORAGE_KEYS = {
    RECENT_COMPONENTS: 'lp-go-recent-components',
    FAVORITE_COMPONENTS: 'lp-go-favorite-components',
    LAST_USED_CATEGORIES: 'lp-go-last-used-categories',
    PROPERTY_GROUP_STATES: 'lp-go-property-groups-state',
    PROPERTY_PREFERENCES: 'lp-go-property-preferences'
  } as const;

  private readonly MAX_RECENT_ITEMS = 10;
  private readonly MAX_CATEGORIES = 5;

  // Reactive subjects for real-time updates
  private recentComponents$ = new BehaviorSubject<string[]>([]);
  private favoriteComponents$ = new BehaviorSubject<string[]>([]);

  constructor() {
    this.loadInitialData();
  }

  /**
   * Load initial data from localStorage
   */
  private loadInitialData(): void {
    const recent = this.getRecentComponents();
    const favorites = this.getFavoriteComponents();
    
    this.recentComponents$.next(recent);
    this.favoriteComponents$.next(favorites);
  }

  /**
   * Get recent components as observable
   */
  getRecentComponents$(): Observable<string[]> {
    return this.recentComponents$.asObservable();
  }

  /**
   * Get favorite components as observable
   */
  getFavoriteComponents$(): Observable<string[]> {
    return this.favoriteComponents$.asObservable();
  }

  /**
   * Get recent components from storage
   */
  getRecentComponents(): string[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEYS.RECENT_COMPONENTS);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.warn('[StorageService] Error loading recent components:', error);
      return [];
    }
  }

  /**
   * Get favorite components from storage
   */
  getFavoriteComponents(): string[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEYS.FAVORITE_COMPONENTS);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.warn('[StorageService] Error loading favorite components:', error);
      return [];
    }
  }

  /**
   * Get last used categories from storage
   */
  getLastUsedCategories(): string[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEYS.LAST_USED_CATEGORIES);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.warn('[StorageService] Error loading last used categories:', error);
      return [];
    }
  }

  /**
   * Add component to recent list
   */
  addToRecentComponents(componentType: string): void {
    if (!componentType) return;

    const current = this.getRecentComponents();
    
    // Remove if already exists to avoid duplicates
    const filtered = current.filter(item => item !== componentType);
    
    // Add to beginning and limit size
    const updated = [componentType, ...filtered].slice(0, this.MAX_RECENT_ITEMS);
    
    this.saveToStorage(this.STORAGE_KEYS.RECENT_COMPONENTS, updated);
    this.recentComponents$.next(updated);
  }

  /**
   * Toggle component in favorites
   */
  toggleFavoriteComponent(componentType: string): boolean {
    if (!componentType) return false;

    const current = this.getFavoriteComponents();
    const isFavorite = current.includes(componentType);
    
    let updated: string[];
    if (isFavorite) {
      // Remove from favorites
      updated = current.filter(item => item !== componentType);
    } else {
      // Add to favorites
      updated = [...current, componentType];
    }
    
    this.saveToStorage(this.STORAGE_KEYS.FAVORITE_COMPONENTS, updated);
    this.favoriteComponents$.next(updated);
    
    return !isFavorite; // Return new favorite status
  }

  /**
   * Check if component is in favorites
   */
  isFavoriteComponent(componentType: string): boolean {
    return this.getFavoriteComponents().includes(componentType);
  }

  /**
   * Add category to last used list
   */
  addToLastUsedCategories(category: string): void {
    if (!category) return;

    const current = this.getLastUsedCategories();
    
    // Remove if already exists to avoid duplicates
    const filtered = current.filter(item => item !== category);
    
    // Add to beginning and limit size
    const updated = [category, ...filtered].slice(0, this.MAX_CATEGORIES);
    
    this.saveToStorage(this.STORAGE_KEYS.LAST_USED_CATEGORIES, updated);
  }

  /**
   * Get complete user preferences
   */
  getUserPreferences(): UserPreferences {
    return {
      recentComponents: this.getRecentComponents(),
      favoriteComponents: this.getFavoriteComponents(),
      lastUsedCategories: this.getLastUsedCategories()
    };
  }

  /**
   * Clear all user preferences
   */
  clearAllPreferences(): void {
    Object.values(this.STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
    
    // Reset reactive subjects
    this.recentComponents$.next([]);
    this.favoriteComponents$.next([]);
  }

  /**
   * Clear specific preference type
   */
  clearRecentComponents(): void {
    localStorage.removeItem(this.STORAGE_KEYS.RECENT_COMPONENTS);
    this.recentComponents$.next([]);
  }

  clearFavoriteComponents(): void {
    localStorage.removeItem(this.STORAGE_KEYS.FAVORITE_COMPONENTS);
    this.favoriteComponents$.next([]);
  }

  /**
   * Generic storage helper
   */
  private saveToStorage(key: string, data: any): void {
    try {
      localStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
      console.warn(`[StorageService] Error saving to storage (${key}):`, error);
    }
  }

  /**
   * Generic storage getter
   */
  get<T>(key: string, defaultValue: T): T {
    try {
      const stored = localStorage.getItem(key);
      return stored ? JSON.parse(stored) : defaultValue;
    } catch (error) {
      console.warn(`[StorageService] Error getting from storage (${key}):`, error);
      return defaultValue;
    }
  }

  /**
   * Generic storage setter
   */
  set(key: string, value: any): void {
    this.saveToStorage(key, value);
  }

  /**
   * Remove item from storage
   */
  remove(key: string): void {
    localStorage.removeItem(key);
  }

  // Property Group State Management

  /**
   * Get property group states for all component types
   */
  getPropertyGroupStates(): PropertyGroupState {
    return this.get<PropertyGroupState>(this.STORAGE_KEYS.PROPERTY_GROUP_STATES, {});
  }

  /**
   * Get property group states for a specific component type
   */
  getComponentGroupStates(componentType: string): Record<string, boolean> {
    const allStates = this.getPropertyGroupStates();
    return allStates[componentType] || {};
  }

  /**
   * Set property group state for a specific component and group
   */
  setPropertyGroupState(componentType: string, groupId: string, expanded: boolean): void {
    const allStates = this.getPropertyGroupStates();

    if (!allStates[componentType]) {
      allStates[componentType] = {};
    }

    allStates[componentType][groupId] = expanded;
    this.set(this.STORAGE_KEYS.PROPERTY_GROUP_STATES, allStates);
  }

  /**
   * Get property preferences
   */
  getPropertyPreferences(): PropertyPreferences {
    return this.get<PropertyPreferences>(this.STORAGE_KEYS.PROPERTY_PREFERENCES, {
      groupStates: {},
      searchHistory: [],
      showAdvanced: false
    });
  }

  /**
   * Update property preferences
   */
  updatePropertyPreferences(preferences: Partial<PropertyPreferences>): void {
    const current = this.getPropertyPreferences();
    const updated = { ...current, ...preferences };
    this.set(this.STORAGE_KEYS.PROPERTY_PREFERENCES, updated);
  }

  /**
   * Add search term to history
   */
  addToSearchHistory(searchTerm: string): void {
    if (!searchTerm.trim()) return;

    const preferences = this.getPropertyPreferences();
    const history = preferences.searchHistory || [];

    // Remove if already exists to avoid duplicates
    const filtered = history.filter(term => term !== searchTerm);

    // Add to beginning and limit to 10 items
    const updated = [searchTerm, ...filtered].slice(0, 10);

    this.updatePropertyPreferences({ searchHistory: updated });
  }

  /**
   * Clear property group states
   */
  clearPropertyGroupStates(): void {
    this.remove(this.STORAGE_KEYS.PROPERTY_GROUP_STATES);
  }

  /**
   * Clear property preferences
   */
  clearPropertyPreferences(): void {
    this.remove(this.STORAGE_KEYS.PROPERTY_PREFERENCES);
  }
}
