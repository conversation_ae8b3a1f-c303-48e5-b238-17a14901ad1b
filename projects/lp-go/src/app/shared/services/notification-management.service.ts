import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, from } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

// Firebase imports
import { initializeApp } from 'firebase/app';
import { 
  getFirestore, 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  Timestamp,
  DocumentData
} from 'firebase/firestore';

// Initialize Firebase
const app = initializeApp(environment.firebase);
const db = getFirestore(app);

export interface NotificationTemplate {
  id?: string;
  title: string;
  body: string;
  imageUrl?: string;
  actionUrl?: string;
  group: string;
  data?: Record<string, any>;
  createdAt?: Timestamp | Date;
  updatedAt?: Timestamp | Date;
}

export interface NotificationGroup {
  id: string;
  name: string;
  description: string;
  icon?: string;
  defaultEnabled: boolean;
  createdAt?: Timestamp | Date;
  updatedAt?: Timestamp | Date;
}

export interface NotificationHistory {
  id?: string;
  title: string;
  body: string;
  imageUrl?: string;
  actionUrl?: string;
  group: string;
  data?: Record<string, any>;
  sentAt: Timestamp | Date;
  successCount: number;
  failureCount: number;
  status: 'sent' | 'failed' | 'pending';
  sentBy?: string;
}

export interface NotificationRequest {
  notification: {
    title: string;
    body: string;
    imageUrl?: string;
    actionUrl?: string;
    group?: string;
    data?: Record<string, any>;
  };
  tokens?: string[];
  userIds?: string[];
  groups?: string[];
  topic?: string;
  condition?: string;
}

export interface NotificationResponse {
  success: boolean;
  successCount?: number;
  failureCount?: number;
  successfulTokens?: string[];
  failedTokens?: { token: string; error: string }[];
  error?: string;
  notificationId?: string;
}

export interface NotificationAnalytics {
  counts: {
    sent: number;
    delivered: number;
    opened: number;
    dismissed: number;
    actionClicked: number;
  };
  rates: {
    delivery: number;
    open: number;
    dismiss: number;
    action: number;
  };
}

@Injectable({
  providedIn: 'root'
})
export class NotificationManagementService {
  private baseUrl = environment.notificationApi.baseUrl;

  constructor(private http: HttpClient) {}

  // Template Management
  getTemplates(): Observable<NotificationTemplate[]> {
    return from(getDocs(collection(db, 'notificationTemplates'))).pipe(
      map(snapshot => {
        return snapshot.docs.map(doc => {
          const data = doc.data() as NotificationTemplate;
          return { ...data, id: doc.id };
        });
      }),
      catchError(error => {
        console.error('Error getting notification templates:', error);
        return of([]);
      })
    );
  }

  getTemplate(id: string): Observable<NotificationTemplate | null> {
    return from(getDoc(doc(db, 'notificationTemplates', id))).pipe(
      map(docSnapshot => {
        if (docSnapshot.exists()) {
          const data = docSnapshot.data() as NotificationTemplate;
          return { ...data, id: docSnapshot.id };
        }
        return null;
      }),
      catchError(error => {
        console.error(`Error getting template ${id}:`, error);
        return of(null);
      })
    );
  }

  createTemplate(template: NotificationTemplate): Observable<string> {
    const newTemplate = {
      ...template,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    };

    return from(addDoc(collection(db, 'notificationTemplates'), newTemplate)).pipe(
      map(docRef => docRef.id),
      catchError(error => {
        console.error('Error creating template:', error);
        throw error;
      })
    );
  }

  updateTemplate(id: string, template: Partial<NotificationTemplate>): Observable<void> {
    const updatedTemplate = {
      ...template,
      updatedAt: Timestamp.now()
    };

    return from(updateDoc(doc(db, 'notificationTemplates', id), updatedTemplate)).pipe(
      catchError(error => {
        console.error(`Error updating template ${id}:`, error);
        throw error;
      })
    );
  }

  deleteTemplate(id: string): Observable<void> {
    return from(deleteDoc(doc(db, 'notificationTemplates', id))).pipe(
      catchError(error => {
        console.error(`Error deleting template ${id}:`, error);
        throw error;
      })
    );
  }

  // Group Management
  getGroups(): Observable<NotificationGroup[]> {
    return from(getDocs(collection(db, 'notificationGroups'))).pipe(
      map(snapshot => {
        return snapshot.docs.map(doc => {
          const data = doc.data() as NotificationGroup;
          return { ...data, id: doc.id };
        });
      }),
      catchError(error => {
        console.error('Error getting notification groups:', error);
        return of([]);
      })
    );
  }

  getGroup(id: string): Observable<NotificationGroup | null> {
    return from(getDoc(doc(db, 'notificationGroups', id))).pipe(
      map(docSnapshot => {
        if (docSnapshot.exists()) {
          const data = docSnapshot.data() as NotificationGroup;
          return { ...data, id: docSnapshot.id };
        }
        return null;
      }),
      catchError(error => {
        console.error(`Error getting group ${id}:`, error);
        return of(null);
      })
    );
  }

  createGroup(group: NotificationGroup): Observable<string> {
    const newGroup = {
      ...group,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    };

    return from(addDoc(collection(db, 'notificationGroups'), newGroup)).pipe(
      map(docRef => docRef.id),
      catchError(error => {
        console.error('Error creating group:', error);
        throw error;
      })
    );
  }

  updateGroup(id: string, group: Partial<NotificationGroup>): Observable<void> {
    const updatedGroup = {
      ...group,
      updatedAt: Timestamp.now()
    };

    return from(updateDoc(doc(db, 'notificationGroups', id), updatedGroup)).pipe(
      catchError(error => {
        console.error(`Error updating group ${id}:`, error);
        throw error;
      })
    );
  }

  deleteGroup(id: string): Observable<void> {
    return from(deleteDoc(doc(db, 'notificationGroups', id))).pipe(
      catchError(error => {
        console.error(`Error deleting group ${id}:`, error);
        throw error;
      })
    );
  }

  // Notification History
  getNotificationHistory(): Observable<NotificationHistory[]> {
    return from(
      getDocs(
        query(
          collection(db, 'notificationHistory'),
          orderBy('sentAt', 'desc'),
          limit(100)
        )
      )
    ).pipe(
      map(snapshot => {
        return snapshot.docs.map(doc => {
          const data = doc.data() as NotificationHistory;
          return { ...data, id: doc.id };
        });
      }),
      catchError(error => {
        console.error('Error getting notification history:', error);
        return of([]);
      })
    );
  }

  // Send Notification
  sendNotification(request: NotificationRequest): Observable<NotificationResponse> {
    return this.http.post<NotificationResponse>(`${this.baseUrl}/send`, request).pipe(
      switchMap(response => {
        if (response.success) {
          // Save to history in Firestore
          const historyEntry: NotificationHistory = {
            title: request.notification.title,
            body: request.notification.body,
            imageUrl: request.notification.imageUrl,
            actionUrl: request.notification.actionUrl,
            group: request.notification.group || 'general',
            data: request.notification.data,
            sentAt: Timestamp.now(),
            successCount: response.successCount || 0,
            failureCount: response.failureCount || 0,
            status: response.success ? 'sent' : 'failed',
            sentBy: 'admin' // This would be the actual user in a real implementation
          };

          return from(addDoc(collection(db, 'notificationHistory'), historyEntry)).pipe(
            map(() => response),
            catchError(error => {
              console.error('Error saving notification history:', error);
              return of(response);
            })
          );
        }
        return of(response);
      }),
      catchError(error => {
        console.error('Error sending notification:', error);
        return of({
          success: false,
          error: error.message || 'Failed to send notification'
        });
      })
    );
  }

  // Analytics
  getAnalytics(): Observable<NotificationAnalytics> {
    return this.http.get<NotificationAnalytics>(`${this.baseUrl}/analytics`).pipe(
      catchError(error => {
        console.error('Error getting notification analytics:', error);
        return of({
          counts: {
            sent: 0,
            delivered: 0,
            opened: 0,
            dismissed: 0,
            actionClicked: 0
          },
          rates: {
            delivery: 0,
            open: 0,
            dismiss: 0,
            action: 0
          }
        });
      })
    );
  }
}
