import { TestBed } from '@angular/core/testing';
import { StorageService } from './storage.service';

describe('StorageService', () => {
  let service: StorageService;
  let mockLocalStorage: { [key: string]: string };

  beforeEach(() => {
    // Mock localStorage
    mockLocalStorage = {};
    
    spyOn(localStorage, 'getItem').and.callFake((key: string) => {
      return mockLocalStorage[key] || null;
    });
    
    spyOn(localStorage, 'setItem').and.callFake((key: string, value: string) => {
      mockLocalStorage[key] = value;
    });
    
    spyOn(localStorage, 'removeItem').and.callFake((key: string) => {
      delete mockLocalStorage[key];
    });

    TestBed.configureTestingModule({
      providers: [StorageService]
    });
    
    service = TestBed.inject(StorageService);
  });

  afterEach(() => {
    mockLocalStorage = {};
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Recent Components', () => {
    it('should start with empty recent components', () => {
      expect(service.getRecentComponents()).toEqual([]);
    });

    it('should add component to recent list', () => {
      service.addToRecentComponents('test-component');
      expect(service.getRecentComponents()).toEqual(['test-component']);
    });

    it('should move existing component to top of recent list', () => {
      service.addToRecentComponents('component-1');
      service.addToRecentComponents('component-2');
      service.addToRecentComponents('component-1'); // Should move to top
      
      expect(service.getRecentComponents()).toEqual(['component-1', 'component-2']);
    });

    it('should limit recent components to maximum count', () => {
      // Add more than the maximum (10) components
      for (let i = 1; i <= 12; i++) {
        service.addToRecentComponents(`component-${i}`);
      }
      
      const recent = service.getRecentComponents();
      expect(recent.length).toBe(10);
      expect(recent[0]).toBe('component-12'); // Most recent first
      expect(recent[9]).toBe('component-3'); // Oldest kept
    });

    it('should emit recent components changes', (done) => {
      service.getRecentComponents$().subscribe(recent => {
        if (recent.length > 0) {
          expect(recent).toEqual(['test-component']);
          done();
        }
      });
      
      service.addToRecentComponents('test-component');
    });
  });

  describe('Favorite Components', () => {
    it('should start with empty favorites', () => {
      expect(service.getFavoriteComponents()).toEqual([]);
    });

    it('should add component to favorites', () => {
      const isFavorite = service.toggleFavoriteComponent('test-component');
      expect(isFavorite).toBe(true);
      expect(service.getFavoriteComponents()).toEqual(['test-component']);
    });

    it('should remove component from favorites when toggled again', () => {
      service.toggleFavoriteComponent('test-component'); // Add
      const isFavorite = service.toggleFavoriteComponent('test-component'); // Remove
      
      expect(isFavorite).toBe(false);
      expect(service.getFavoriteComponents()).toEqual([]);
    });

    it('should check if component is favorite', () => {
      expect(service.isFavoriteComponent('test-component')).toBe(false);
      
      service.toggleFavoriteComponent('test-component');
      expect(service.isFavoriteComponent('test-component')).toBe(true);
    });

    it('should emit favorite components changes', (done) => {
      service.getFavoriteComponents$().subscribe(favorites => {
        if (favorites.length > 0) {
          expect(favorites).toEqual(['test-component']);
          done();
        }
      });
      
      service.toggleFavoriteComponent('test-component');
    });
  });

  describe('Last Used Categories', () => {
    it('should start with empty categories', () => {
      expect(service.getLastUsedCategories()).toEqual([]);
    });

    it('should add category to last used list', () => {
      service.addToLastUsedCategories('Layout');
      expect(service.getLastUsedCategories()).toEqual(['Layout']);
    });

    it('should move existing category to top', () => {
      service.addToLastUsedCategories('Layout');
      service.addToLastUsedCategories('Forms');
      service.addToLastUsedCategories('Layout'); // Should move to top
      
      expect(service.getLastUsedCategories()).toEqual(['Layout', 'Forms']);
    });

    it('should limit categories to maximum count', () => {
      // Add more than the maximum (5) categories
      for (let i = 1; i <= 7; i++) {
        service.addToLastUsedCategories(`Category-${i}`);
      }
      
      const categories = service.getLastUsedCategories();
      expect(categories.length).toBe(5);
      expect(categories[0]).toBe('Category-7'); // Most recent first
    });
  });

  describe('User Preferences', () => {
    it('should return complete user preferences', () => {
      service.addToRecentComponents('recent-1');
      service.toggleFavoriteComponent('favorite-1');
      service.addToLastUsedCategories('Layout');
      
      const preferences = service.getUserPreferences();
      
      expect(preferences).toEqual({
        recentComponents: ['recent-1'],
        favoriteComponents: ['favorite-1'],
        lastUsedCategories: ['Layout']
      });
    });

    it('should clear all preferences', () => {
      service.addToRecentComponents('recent-1');
      service.toggleFavoriteComponent('favorite-1');
      service.addToLastUsedCategories('Layout');
      
      service.clearAllPreferences();
      
      expect(service.getRecentComponents()).toEqual([]);
      expect(service.getFavoriteComponents()).toEqual([]);
      expect(service.getLastUsedCategories()).toEqual([]);
    });
  });

  describe('Generic Storage Methods', () => {
    it('should store and retrieve generic data', () => {
      const testData = { test: 'value' };
      service.set('test-key', testData);
      
      expect(service.get('test-key', null)).toEqual(testData as any);
    });

    it('should return default value for missing key', () => {
      expect(service.get('missing-key', 'default')).toBe('default');
    });

    it('should remove data from storage', () => {
      service.set('test-key', 'test-value');
      service.remove('test-key');
      
      expect(service.get('test-key', null)).toBe(null);
    });
  });

  describe('Error Handling', () => {
    it('should handle localStorage errors gracefully', () => {
      // Mock localStorage to throw an error
      (localStorage.getItem as jasmine.Spy).and.throwError('Storage error');
      
      expect(() => service.getRecentComponents()).not.toThrow();
      expect(service.getRecentComponents()).toEqual([]);
    });

    it('should handle JSON parse errors gracefully', () => {
      // Mock localStorage to return invalid JSON
      (localStorage.getItem as jasmine.Spy).and.returnValue('invalid-json');
      
      expect(() => service.getRecentComponents()).not.toThrow();
      expect(service.getRecentComponents()).toEqual([]);
    });
  });
});
