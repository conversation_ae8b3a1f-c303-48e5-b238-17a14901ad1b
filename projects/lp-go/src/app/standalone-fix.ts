import { Component, Directive, Type } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DragDropModule } from '@angular/cdk/drag-drop';

/**
 * Helper function to fix standalone components by adding required imports
 * @param componentType The component class to decorate
 * @param additionalImports Any additional imports beyond the standard ones
 * @returns The decorated component
 */
export function fixStandaloneComponent<T>(
  componentType: Type<T>,
  additionalImports: any[] = []
): Type<T> {
  const annotations = getComponentAnnotations(componentType);
  
  if (annotations) {
    const metadata = annotations.find(a => a instanceof Component);
    if (metadata) {
      (metadata as any).standalone = true;
      
      // Standard modules every component needs
      const standardImports = [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        DragDropModule
      ];
      
      // Add all standard and additional imports
      (metadata as any).imports = [
        ...(metadata.imports || []),
        ...standardImports,
        ...additionalImports
      ];
    }
  }
  
  return componentType;
}

/**
 * Get component annotations
 */
function getComponentAnnotations(cls: any): any[] {
  return cls.__annotations__ || [];
}
