<div class="toolbar">
  <div class="toolbar-left">
    <div class="logo">LP</div>
    <h1 class="page-title">{{ pageTitle }}</h1>
  </div>
  
  <div class="toolbar-center">
    <!-- Canvas Controls -->
    <div class="canvas-controls">
      <!-- Zoom Controls -->
      <div class="zoom-controls">
        <button
          type="button"
          class="zoom-btn"
          (click)="zoomOut()"
          [disabled]="!canZoomOut()"
          title="Zoom Out (Ctrl/Cmd + -)">
          <span class="zoom-icon">🔍-</span>
        </button>

        <select
          class="zoom-select"
          [value]="currentZoom"
          (change)="setZoom(+$any($event.target).value)"
          title="Zoom Level">
          <option *ngFor="let level of zoomLevels" [value]="level.value">
            {{ level.label }}
          </option>
        </select>

        <button
          type="button"
          class="zoom-btn"
          (click)="zoomIn()"
          [disabled]="!canZoomIn()"
          title="Zoom In (Ctrl/Cmd + +)">
          <span class="zoom-icon">🔍+</span>
        </button>

        <button
          type="button"
          class="zoom-btn actual-size"
          (click)="actualSize()"
          title="Actual Size (Ctrl/Cmd + 0)">
          <span class="zoom-icon">🎯</span>
        </button>
      </div>

      <!-- Grid Toggle -->
      <button
        type="button"
        class="grid-btn"
        [class.active]="gridEnabled"
        (click)="toggleGrid()"
        title="Toggle Grid (Ctrl/Cmd + G)">
        <span class="grid-icon">⚏</span>
        <span class="grid-label">Grid</span>
      </button>
    </div>

    <!-- Viewport Controls -->
    <div class="viewport-selector">
      <button
        *ngFor="let viewport of viewports"
        [class.active]="selectedViewport === viewport.id"
        (click)="setViewport(viewport.id)"
        class="viewport-button"
        [title]="viewport.label">
        <!-- Icon placeholder -->
        <span class="viewport-icon">{{ viewport.icon.charAt(0) }}</span>
      </button>
    </div>
  </div>
  
  <div class="toolbar-right">
    <button class="toolbar-button" (click)="onTogglePalette()" title="Toggle Component Palette">
      <span class="button-icon">[]</span>
      <span class="button-label">Components</span>
    </button>
    
    <button class="toolbar-button" (click)="onToggleProperties()" title="Toggle Properties Panel">
      <span class="button-icon">{{ '{' }}{{ '}' }}</span>
      <span class="button-label">Properties</span>
    </button>
    
    <button class="toolbar-button preview" (click)="onPreview()" title="Preview">
      <span class="button-icon">👁️</span>
      <span class="button-label">Preview</span>
    </button>
    
    <app-save-status></app-save-status>

    <button class="toolbar-button primary" (click)="onSave()" title="Save">
      <span class="button-icon">💾</span>
      <span class="button-label">Save</span>
    </button>
  </div>
</div>
