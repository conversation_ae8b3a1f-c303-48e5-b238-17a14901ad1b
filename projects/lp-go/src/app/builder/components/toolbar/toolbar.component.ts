import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { SaveStatusComponent } from '../save-status/save-status.component';
import { ComponentStoreService } from '../../services/component-store.service';
import { CanvasState, ZoomLevel } from '../../models/builder-component.interface';

@Component({
  selector: 'app-toolbar',
  templateUrl: './toolbar.component.html',
  styleUrls: ['./toolbar.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    SaveStatusComponent
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ToolbarComponent implements OnInit, OnDestroy {
  @Input() pageTitle = 'App Builder';
  
  @Output() save = new EventEmitter<void>();
  @Output() preview = new EventEmitter<void>();
  @Output() togglePalette = new EventEmitter<void>();
  @Output() toggleProperties = new EventEmitter<void>();
  @Output() viewportChanged = new EventEmitter<string>();
  
  // Viewport options
  viewports = [
    { id: 'mobile', label: 'Mobile', width: '375px', icon: 'smartphone' },
    { id: 'tablet', label: 'Tablet', width: '768px', icon: 'tablet' },
    { id: 'desktop', label: 'Desktop', width: '100%', icon: 'monitor' }
  ];
  
  selectedViewport = 'desktop';

  // Canvas state properties
  canvasState: CanvasState | null = null;
  zoomLevels: ZoomLevel[] = [];
  currentZoom: number = 1;
  gridEnabled: boolean = false;

  private destroy$ = new Subject<void>();

  constructor(
    private componentStore: ComponentStoreService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    // Subscribe to canvas state changes
    this.componentStore.getCanvasState()
      .pipe(takeUntil(this.destroy$))
      .subscribe(canvasState => {
        this.canvasState = canvasState;
        this.currentZoom = canvasState.zoom;
        this.gridEnabled = canvasState.gridEnabled;
        this.cdr.markForCheck();
      });

    // Get available zoom levels
    this.zoomLevels = this.componentStore.getZoomLevels();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Set active viewport
   */
  setViewport(viewportId: string): void {
    this.selectedViewport = viewportId;
    this.viewportChanged.emit(viewportId);
  }
  
  /**
   * Handle save button click
   */
  onSave(): void {
    this.save.emit();
  }
  
  /**
   * Handle preview button click
   */
  onPreview(): void {
    this.preview.emit();
  }
  
  /**
   * Toggle component palette
   */
  onTogglePalette(): void {
    this.togglePalette.emit();
  }
  
  /**
   * Toggle properties panel
   */
  onToggleProperties(): void {
    this.toggleProperties.emit();
  }

  // Canvas Control Methods

  /**
   * Zoom in
   */
  zoomIn(): void {
    this.componentStore.zoomIn();
  }

  /**
   * Zoom out
   */
  zoomOut(): void {
    this.componentStore.zoomOut();
  }

  /**
   * Set zoom to actual size (100%)
   */
  actualSize(): void {
    this.componentStore.actualSize();
  }

  /**
   * Set specific zoom level
   */
  setZoom(zoom: number): void {
    this.componentStore.setZoom(zoom);
  }

  /**
   * Toggle grid overlay
   */
  toggleGrid(): void {
    this.componentStore.toggleGrid();
  }

  /**
   * Get current zoom percentage for display
   */
  getZoomPercentage(): string {
    return Math.round(this.currentZoom * 100) + '%';
  }

  /**
   * Get zoom level label
   */
  getZoomLabel(zoom: number): string {
    const level = this.zoomLevels.find(level => level.value === zoom);
    return level ? level.label : Math.round(zoom * 100) + '%';
  }

  /**
   * Check if zoom in is available
   */
  canZoomIn(): boolean {
    return this.currentZoom < 3;
  }

  /**
   * Check if zoom out is available
   */
  canZoomOut(): boolean {
    return this.currentZoom > 0.25;
  }

  /**
   * Handle keyboard shortcuts
   */
  onKeyboardShortcut(event: KeyboardEvent): void {
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case '=':
        case '+':
          event.preventDefault();
          this.zoomIn();
          break;
        case '-':
          event.preventDefault();
          this.zoomOut();
          break;
        case '0':
          event.preventDefault();
          this.actualSize();
          break;
        case 'g':
          event.preventDefault();
          this.toggleGrid();
          break;
      }
    }
  }
}
