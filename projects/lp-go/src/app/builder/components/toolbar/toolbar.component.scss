.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  background-color: white;
  border-bottom: 1px solid #e2e8f0;
  height: 3.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.toolbar-left {
  display: flex;
  align-items: center;
  
  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    background-color: #3b82f6;
    color: white;
    border-radius: 0.25rem;
    font-weight: bold;
    margin-right: 0.75rem;
  }
  
  .page-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #334155;
    margin: 0;
  }
}

.toolbar-center {
  display: flex;
  align-items: center;
  gap: 1rem;

  .canvas-controls {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.25rem 0.75rem;
    background-color: #f8fafc;
    border-radius: 0.375rem;
    border: 1px solid #e2e8f0;

    .zoom-controls {
      display: flex;
      align-items: center;
      gap: 0.25rem;

      .zoom-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 1.75rem;
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 0.25rem;
        color: #64748b;
        cursor: pointer;
        transition: all 0.15s ease;
        font-size: 0.75rem;

        &:hover:not(:disabled) {
          background-color: #f1f5f9;
          border-color: #cbd5e1;
          color: #475569;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        &.actual-size {
          background-color: #eff6ff;
          border-color: #bfdbfe;
          color: #3b82f6;

          &:hover:not(:disabled) {
            background-color: #dbeafe;
          }
        }

        .zoom-icon {
          font-size: 0.875rem;
        }
      }

      .zoom-select {
        min-width: 4rem;
        height: 1.75rem;
        padding: 0 0.5rem;
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 0.25rem;
        color: #374151;
        font-size: 0.75rem;
        cursor: pointer;
        transition: all 0.15s ease;

        &:hover {
          border-color: #cbd5e1;
        }

        &:focus {
          outline: 2px solid #3b82f6;
          outline-offset: -2px;
          border-color: #3b82f6;
        }
      }
    }

    .grid-btn {
      display: flex;
      align-items: center;
      gap: 0.375rem;
      padding: 0.25rem 0.5rem;
      background: white;
      border: 1px solid #e2e8f0;
      border-radius: 0.25rem;
      color: #64748b;
      cursor: pointer;
      transition: all 0.15s ease;
      font-size: 0.75rem;

      &:hover {
        background-color: #f1f5f9;
        border-color: #cbd5e1;
        color: #475569;
      }

      &.active {
        background-color: #eff6ff;
        border-color: #bfdbfe;
        color: #3b82f6;

        &:hover {
          background-color: #dbeafe;
        }
      }

      .grid-icon {
        font-size: 0.875rem;
      }

      .grid-label {
        font-weight: 500;
      }
    }
  }

  .viewport-selector {
    display: flex;
    background-color: #f1f5f9;
    border-radius: 0.25rem;
    padding: 0.25rem;

    .viewport-button {
      display: flex;
      align-items: center;
      justify-content: center;
      background: none;
      border: none;
      cursor: pointer;
      width: 2.5rem;
      height: 2rem;
      border-radius: 0.25rem;
      color: #64748b;

      &.active {
        background-color: white;
        color: #3b82f6;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      }

      &:hover:not(.active) {
        background-color: rgba(255, 255, 255, 0.5);
      }

      .viewport-icon {
        font-size: 1.125rem;
      }
    }
  }
}

.toolbar-right {
  display: flex;
  gap: 0.5rem;
  
  .toolbar-button {
    display: flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 0.25rem;
    color: #334155;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background-color: #f8fafc;
      border-color: #cbd5e1;
    }
    
    &.primary {
      background-color: #3b82f6;
      border-color: #2563eb;
      color: white;
      
      &:hover {
        background-color: #2563eb;
      }
    }
    
    &.preview {
      background-color: #f1f5f9;
      
      &:hover {
        background-color: #e2e8f0;
      }
    }
    
    .button-icon {
      margin-right: 0.5rem;
      font-size: 1rem;
    }
    
    @media (max-width: 768px) {
      .button-label {
        display: none;
      }

      .button-icon {
        margin-right: 0;
      }
    }
  }
}

// Responsive design for canvas controls
@media (max-width: 1024px) {
  .toolbar-center {
    gap: 0.75rem;

    .canvas-controls {
      padding: 0.25rem 0.5rem;
      gap: 0.5rem;

      .zoom-controls {
        gap: 0.1875rem;

        .zoom-btn {
          width: 1.75rem;
          height: 1.5rem;
          font-size: 0.6875rem;
        }

        .zoom-select {
          min-width: 3.5rem;
          height: 1.5rem;
          font-size: 0.6875rem;
        }
      }

      .grid-btn {
        padding: 0.1875rem 0.375rem;
        font-size: 0.6875rem;

        .grid-label {
          display: none;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .toolbar-center {
    gap: 0.5rem;

    .canvas-controls {
      padding: 0.1875rem 0.375rem;
      gap: 0.375rem;

      .zoom-controls {
        .zoom-select {
          min-width: 3rem;
        }
      }
    }

    .viewport-selector {
      padding: 0.1875rem;

      .viewport-button {
        width: 2rem;
        height: 1.75rem;

        .viewport-icon {
          font-size: 1rem;
        }
      }
    }
  }
}

@media (max-width: 640px) {
  .toolbar {
    padding: 0.375rem 0.75rem;
    height: 3rem;
  }

  .toolbar-center {
    .canvas-controls {
      .zoom-controls {
        .zoom-btn:not(.actual-size) {
          display: none;
        }

        .zoom-select {
          min-width: 2.5rem;
          font-size: 0.625rem;
        }
      }
    }
  }
}
