import { Injectable, Type, Component, Injector } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

// Import the MobileComponentsModule
import { MobileComponentsModule } from './mobile-components.module';

// Import the component properties provider
import { componentPropertiesProvider } from './component-properties.provider';

// Import property registry service for integration
import { PropertyRegistryService } from '../services/property-registry.service';

// Import actual components from mobile-components - Basic set for fallback
import {
  ButtonComponent,
  ParagraphComponent,
  HeadingComponent,
  CardComponent,
  ContainerComponent,
  ImageComponent,
  InputComponent,
  SelectComponent,
  CheckboxComponent,
  TextareaComponent
} from 'mobile-components';

/**
 * Interface for component registration metadata
 */
export interface ComponentRegistration {
  name: string;
  component: Type<any>;
  category: string;
  description?: string;
  icon?: string;
  properties?: any;
  tags?: string[];
  registeredAt: Date;
  source: 'static' | 'auto-discovered' | 'manual';
  validated: boolean;
}

/**
 * Enhanced service to register and retrieve component types for the dynamic component system
 * Supports auto-discovery of all mobile-components with categorization and validation
 */
@Injectable({
  providedIn: 'root'
})
export class ComponentRegistryService {
  private componentTypes = new Map<string, Type<any>>();
  private componentRegistrations = new Map<string, ComponentRegistration>();
  private registrationSubject = new BehaviorSubject<ComponentRegistration[]>([]);
  private autoDiscoveryComplete = false;
  private debug = true; // Enable debug logging

  constructor(
    private injector: Injector,
    private propertyRegistry: PropertyRegistryService
  ) {
    this.initializeRegistry();
  }

  /**
   * Initialize the component registry with auto-discovery
   */
  private async initializeRegistry(): Promise<void> {
    console.log('[ComponentRegistry] Initializing enhanced component registry...');

    // First register basic components for immediate availability
    this.registerBasicComponents();

    // Then perform auto-discovery of all mobile-components
    await this.autoDiscoverComponents();

    this.autoDiscoveryComplete = true;
    console.log(`[ComponentRegistry] Registry initialization complete. Total components: ${this.componentRegistrations.size}`);
  }

  /**
   * Register basic components for immediate availability
   */
  private registerBasicComponents(): void {
    const basicComponents = [
      { type: 'container', component: ContainerComponent, category: 'Layout', description: 'Flexible container for organizing content' },
      { type: 'text', component: ParagraphComponent, category: 'Basic', description: 'Text content with customizable styles' },
      { type: 'heading', component: HeadingComponent, category: 'Basic', description: 'Heading with various levels (h1-h6)' },
      { type: 'button', component: ButtonComponent, category: 'Basic', description: 'Interactive button element' },
      { type: 'image', component: ImageComponent, category: 'Basic', description: 'Display images with various styles' },
      { type: 'card', component: CardComponent, category: 'Layout', description: 'Card container for content grouping' },
      { type: 'input', component: InputComponent, category: 'Form', description: 'Text input field' },
      { type: 'select', component: SelectComponent, category: 'Form', description: 'Dropdown select control' },
      { type: 'checkbox', component: CheckboxComponent, category: 'Form', description: 'Checkbox input' },
      { type: 'textarea', component: TextareaComponent, category: 'Form', description: 'Multiline text input' }
    ];

    basicComponents.forEach(({ type, component, category, description }) => {
      this.registerComponent(type, component, category, {
        description,
        source: 'static',
        properties: componentPropertiesProvider
      });
    });

    if (this.debug) {
      console.log(`[ComponentRegistry] Registered ${basicComponents.length} basic components`);
    }
  }

  /**
   * Auto-discover all components from mobile-components library
   */
  private async autoDiscoverComponents(): Promise<void> {
    try {
      if (this.debug) console.log('[ComponentRegistry] Starting auto-discovery of mobile-components...');

      // Import the entire mobile-components library
      const mobileComponents = await import('mobile-components');

      // Define comprehensive component lists by category
      const componentCategories = {
        'Base': [
          'AccordianComponent', 'AutocompleteComponent', 'AvatarComponent', 'BreadcrumbComponent',
          'ButtonActionComponent', 'ButtonCloseComponent', 'ButtonIconComponent', 'CheckboxAnimatedComponent',
          'DatepickerComponent', 'DividerComponent', 'DropdownComponent', 'DropdownDividerComponent',
          'DropdownItemComponent', 'IconComponent', 'KbdComponent', 'LinkComponent', 'ListComponent',
          'ListboxComponent', 'LogoComponent', 'MessageComponent', 'MobileComponent', 'PaginationComponent',
          'PictureComponent', 'PlaceholderPageComponent', 'BasePlaceloadComponent', 'ProgressComponent',
          'ProseComponent', 'RadioComponent', 'SnackComponent', 'SwitchBallComponent', 'SwitchThinComponent',
          'TableComponent', 'TabsComponent', 'TagComponent', 'TextComponent'
        ],
        'Layout': [
          'AppLayoutSwitcherComponent', 'HeadLogoComponent', 'LayoutComponent', 'NavigationTopComponent'
        ],
        'Widgets': [
          'CompanyOverviewComponent', 'ButtonGroupComponent', 'FlexTableStartComponent', 'ImageGalleryComponent',
          'FilterComponent', 'TabbedContentComponent', 'IconTextComponent', 'FlexTableCellComponent',
          'IconBoxComponent', 'SearchCompactComponent', 'ModalMediumTierComponent', 'AuthorsListCompactComponent',
          'AvatarGroupComponent', 'ImageLinksComponent', 'MenuIconListComponent', 'ListItemComponent',
          'MapMarkerComponent', 'CardFiltersComponent', 'ActionTextComponent', 'TimelineCompactComponent',
          'ModalFooterComponent', 'WelcomeComponent', 'InfoImageComponent', 'ModalLargeTierComponent',
          'PlaceholderMinimalComponent', 'InfoBadgesComponent', 'FeaturesComponent', 'VcardRightComponent',
          'ModalSmallTierComponent', 'FlexTableHeadingComponent', 'FlexTableRowComponent', 'FlexTableWrapperComponent',
          'FollowersCompactComponent', 'InboxMessageComponent', 'VideoCompactComponent', 'TagListCompactComponent',
          'FileListTabbedComponent', 'AvatarGroupIdComponent', 'CommentListCompactComponent',
          'FullscreenDropfileComponent', 'PlaceholderCompactComponent', 'ProgressCircleComponent',
          'AccountBalanceComponent', 'DynamicListComponent', 'FlexTableComponent', 'FocusLoopComponent',
          'ListboxItemComponent', 'QuillComponent', 'SearchComponent', 'SearchTagComponent'
        ],
        'Page Sections': [
          'HomeHeaderComponent', 'DashboardHeaderComponent', 'ProfileHeaderComponent',
          'HomeNavigationComponent', 'ProfileSettingsComponent', 'HomeActionsComponent',
          'DashboardQuickActionsComponent', 'DashboardWelcomeComponent', 'DashboardSummaryComponent',
          'ProfileFormComponent', 'ProfileHelpComponent'
        ],
        'Forms': [
          'RadioHeadlessComponent', 'CheckboxHeadlessComponent', 'InputFilePathComponent',
          'InputFileComponent', 'InputNumberComponent', 'OtpValidatorComponent', 'AddressComponent',
          'SignupComponent', 'ValidateComponent', 'CountriesSelectComponent', 'IndustrySelectComponent',
          'InputFileHeadlessComponent', 'OtpComponent'
        ],
        'Features': [
          'PasswordComponent', 'PinComponent', 'SecurityComponent', 'CalendarComponent',
          'ShoppingCartCompactComponent', 'ChatComponent', 'CryptoComponent', 'CustomizerComponent',
          'GeolocationComponent', 'InvestmentsComponent', 'InvitesComponent', 'LeaguesComponent',
          'NotificationsComponent', 'OfferCollapseComponent', 'PaymentsComponent', 'ProductsComponent',
          'ProfileComponent', 'ProfileremoveComponent', 'ProjectListCompactComponent', 'AppSearchComponent',
          'AppSearchResultComponent', 'SettingsComponent', 'ThemeSwitchComponent', 'ThemeToggleComponent',
          'TrendingSkillsComponent', 'SocialLinksComponent', 'ContactComponent', 'ContactusComponent',
          'InfoComponent', 'PendingTicketsComponent', 'TeamListCompactComponent', 'TeamSearchCompactComponent',
          'TodoListCompactComponent', 'TodoListTabbedComponent', 'TopicsComponent', 'TransactionsComponent',
          'TransactionsListPlaceloadComponent', 'UserListComponent', 'VirtualComponent', 'VirtualcardComponent'
        ],
        'Games': [
          'TetrisComponent', 'SnakeComponent', 'WordleComponent', 'MemoryComponent', 'MinesweeperComponent',
          'SudokuComponent', 'TowerDefenseComponent', 'WheelSpinComponent', 'SimonSaysComponent',
          'CandyCrushComponent', 'FlappyBirdComponent', 'ChessComponent', 'BlackjackComponent',
          'SolitaireComponent', 'TreasureHuntComponent', 'SubmitSelfieComponent'
        ],
        'Pages': [
          'PagesCustomizerComponent', 'DynamicDashboardComponent', 'HomeComponent', 'ProfileDetailsComponent',
          'PagesLoginTheme1Component', 'PagesLandingTheme1Component', 'GamesComponent', 'DashboardComponent',
          'GamesSingleComponent', 'AllGamesComponent', 'GamesCategoriesComponent'
        ]
      };

      let totalRegistered = 0;
      let totalFailed = 0;

      // Register components by category
      for (const [category, componentNames] of Object.entries(componentCategories)) {
        let categoryRegistered = 0;

        for (const componentName of componentNames) {
          if ((mobileComponents as any)[componentName]) {
            try {
              const componentType = (mobileComponents as any)[componentName];
              const kebabName = this.componentNameToKebab(componentName);

              this.registerComponent(kebabName, componentType, category, {
                description: this.generateComponentDescription(componentName, category),
                source: 'auto-discovered',
                icon: this.getComponentIcon(componentName, category)
              });

              categoryRegistered++;
              totalRegistered++;
            } catch (error) {
              if (this.debug) console.warn(`[ComponentRegistry] Failed to register ${componentName}:`, error);
              totalFailed++;
            }
          } else {
            if (this.debug) console.warn(`[ComponentRegistry] Component ${componentName} not found in mobile-components`);
            totalFailed++;
          }
        }

        if (this.debug && categoryRegistered > 0) {
          console.log(`[ComponentRegistry] Registered ${categoryRegistered} components in category: ${category}`);
        }
      }

      console.log(`[ComponentRegistry] Auto-discovery complete. Registered: ${totalRegistered}, Failed: ${totalFailed}`);

      // Notify subscribers of the updated registrations
      this.notifyRegistrationUpdate();

    } catch (error) {
      console.error('[ComponentRegistry] Auto-discovery failed:', error);
    }
  }

  /**
   * Enhanced component registration with metadata
   */
  registerComponent(
    type: string,
    component: Type<any>,
    category: string,
    options: {
      description?: string;
      icon?: string;
      properties?: any;
      source?: 'static' | 'auto-discovered' | 'manual';
    } = {}
  ): ComponentRegistration {
    const registration: ComponentRegistration = {
      name: type,
      component,
      category,
      description: options.description,
      icon: options.icon,
      properties: options.properties,
      registeredAt: new Date(),
      source: options.source || 'manual',
      validated: this.validateComponent(component)
    };

    this.componentTypes.set(type, component);
    this.componentRegistrations.set(type, registration);

    // Register component properties in the property registry
    this.registerComponentProperties(type, category);

    if (this.debug) {
      console.log(`[ComponentRegistry] Registered ${type} (${category}) - ${registration.validated ? 'Valid' : 'Invalid'}`);
    }

    return registration;
  }

  /**
   * Legacy method for backward compatibility
   */
  registerComponentType(type: string, component: Type<any> | string, providers: any = null): void {
    if (typeof component === 'string') {
      console.warn(`[ComponentRegistry] String components not supported: ${type}`);
      return;
    }

    this.registerComponent(type, component, 'Legacy', {
      properties: providers,
      source: 'static'
    });
  }

  /**
   * Get a component type by name
   */
  getComponentType(type: string): Type<any> | undefined {
    return this.componentTypes.get(type);
  }

  /**
   * Get component registration with metadata
   */
  getComponentRegistration(type: string): ComponentRegistration | undefined {
    return this.componentRegistrations.get(type);
  }

  /**
   * Check if a component type is registered
   */
  hasComponentType(type: string): boolean {
    return this.componentTypes.has(type);
  }

  /**
   * Get all registered component types (legacy)
   */
  getAllComponentTypes(): Map<string, Type<any>> {
    return new Map(this.componentTypes);
  }

  /**
   * Get all component registrations
   */
  getAllRegistrations(): ComponentRegistration[] {
    return Array.from(this.componentRegistrations.values());
  }

  /**
   * Get registrations by category
   */
  getRegistrationsByCategory(category: string): ComponentRegistration[] {
    return this.getAllRegistrations().filter(reg => reg.category === category);
  }

  /**
   * Get all categories
   */
  getCategories(): string[] {
    const categories = new Set(this.getAllRegistrations().map(reg => reg.category));
    return Array.from(categories).sort();
  }

  /**
   * Get registrations as observable
   */
  getRegistrations(): Observable<ComponentRegistration[]> {
    return this.registrationSubject.asObservable();
  }

  /**
   * Check if auto-discovery is complete
   */
  isAutoDiscoveryComplete(): boolean {
    return this.autoDiscoveryComplete;
  }

  /**
   * Wait for auto-discovery to complete
   */
  waitForAutoDiscovery(): Promise<void> {
    if (this.autoDiscoveryComplete) {
      return Promise.resolve();
    }

    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (this.autoDiscoveryComplete) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);
    });
  }

  /**
   * Utility: Convert component name to kebab-case
   */
  private componentNameToKebab(componentName: string): string {
    return componentName
      .replace(/Component$/, '') // Remove 'Component' suffix
      .replace(/([a-z])([A-Z])/g, '$1-$2') // Add hyphens between camelCase
      .toLowerCase();
  }

  /**
   * Utility: Generate component description
   */
  private generateComponentDescription(componentName: string, category: string): string {
    const baseName = componentName.replace(/Component$/, '');
    const descriptions: Record<string, string> = {
      'Base': `${baseName} base component for fundamental UI elements`,
      'Layout': `${baseName} layout component for page structure`,
      'Widgets': `${baseName} widget for enhanced functionality`,
      'Page Sections': `${baseName} page section for structured content`,
      'Features': `${baseName} feature component for application functionality`,
      'Games': `${baseName} game component for interactive entertainment`,
      'Pages': `${baseName} page component for complete page layouts`
    };

    return descriptions[category] || `${baseName} component`;
  }

  /**
   * Utility: Get component icon
   */
  private getComponentIcon(componentName: string, category: string): string {
    const iconMap: Record<string, string> = {
      'Base': 'cube',
      'Layout': 'grid',
      'Widgets': 'extension',
      'Page Sections': 'view-module',
      'Features': 'star',
      'Games': 'sports-esports',
      'Pages': 'web'
    };

    return iconMap[category] || 'component';
  }

  /**
   * Utility: Validate component
   */
  private validateComponent(component: Type<any>): boolean {
    try {
      // Check if it's a valid Angular component
      return component && typeof component === 'function' && component.hasOwnProperty('ɵcmp');
    } catch {
      return false;
    }
  }

  /**
   * Register component properties in the property registry
   */
  private registerComponentProperties(componentType: string, category: string): void {
    // Check if properties are already registered
    if (this.propertyRegistry.isComponentRegistered(componentType)) {
      return;
    }

    // For now, register generic properties for auto-discovered components
    // This could be enhanced to analyze component inputs/outputs automatically
    if (!this.propertyRegistry.isComponentRegistered(componentType)) {
      // The property registry will handle generic component registration
      // when getComponentProperties is called for an unknown component
      if (this.debug) {
        console.log(`[ComponentRegistry] Component ${componentType} will use generic property schema`);
      }
    }
  }

  /**
   * Notify subscribers of registration updates
   */
  private notifyRegistrationUpdate(): void {
    this.registrationSubject.next(this.getAllRegistrations());
  }
}
