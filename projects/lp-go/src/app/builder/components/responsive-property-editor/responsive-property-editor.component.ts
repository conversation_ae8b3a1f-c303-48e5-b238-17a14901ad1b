import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

import { ViewportService } from '../../services/viewport.service';
import { 
  ResponsiveProperty, 
  Breakpoint, 
  ViewportState, 
  DeviceType 
} from '../../models/viewport.interface';

/**
 * Responsive Property Editor Component
 * Allows editing properties with different values for different breakpoints
 */
@Component({
  selector: 'app-responsive-property-editor',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './responsive-property-editor.component.html',
  styleUrls: ['./responsive-property-editor.component.scss']
})
export class ResponsivePropertyEditorComponent implements OnInit, OnDestroy {
  @Input() propertyKey = '';
  @Input() propertyLabel = '';
  @Input() propertyType: 'text' | 'number' | 'select' | 'color' | 'textarea' = 'text';
  @Input() propertyValue: ResponsiveProperty<any> | any = null;
  @Input() selectOptions: { value: any; label: string }[] = [];
  @Input() placeholder = '';
  @Input() disabled = false;

  @Output() valueChange = new EventEmitter<ResponsiveProperty<any>>();

  // Component state
  viewportState: ViewportState | null = null;
  breakpoints: Breakpoint[] = [];
  currentValue: any = '';
  isResponsive = false;
  showBreakpointTabs = false;
  activeBreakpoint: Breakpoint | null = null;

  // Expose enum to template
  DeviceType = DeviceType;

  private destroy$ = new Subject<void>();

  constructor(private viewportService: ViewportService) {}

  ngOnInit(): void {
    this.subscribeToViewportChanges();
    this.initializeProperty();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Subscribe to viewport service changes
   */
  private subscribeToViewportChanges(): void {
    this.viewportService.getViewportState().pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (state) => {
        this.viewportState = state;
        this.activeBreakpoint = state.currentBreakpoint;
        this.updateCurrentValue();
      },
      error: (error) => {
        console.error('Error loading viewport state:', error);
      }
    });

    this.viewportService.getBreakpoints().pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (breakpoints) => {
        this.breakpoints = breakpoints;
      },
      error: (error) => {
        console.error('Error loading breakpoints:', error);
      }
    });
  }

  /**
   * Initialize property value
   */
  private initializeProperty(): void {
    this.isResponsive = this.isResponsiveProperty(this.propertyValue);
    this.updateCurrentValue();
  }

  /**
   * Check if value is a responsive property
   */
  private isResponsiveProperty(value: any): boolean {
    return value && typeof value === 'object' && 'default' in value;
  }

  /**
   * Update current value based on active breakpoint
   */
  private updateCurrentValue(): void {
    if (!this.activeBreakpoint) return;

    if (this.isResponsive) {
      this.currentValue = this.viewportService.getResponsivePropertyValue(this.propertyValue);
    } else {
      this.currentValue = this.propertyValue;
    }
  }

  /**
   * Handle value change
   */
  onValueChange(newValue: any): void {
    if (!this.activeBreakpoint) return;

    let updatedProperty: ResponsiveProperty<any>;

    if (this.isResponsive) {
      // Update existing responsive property
      updatedProperty = this.viewportService.setResponsivePropertyValue(
        this.propertyValue,
        newValue,
        this.activeBreakpoint
      );
    } else {
      // Convert to responsive property
      updatedProperty = this.createResponsiveProperty(newValue);
    }

    this.propertyValue = updatedProperty;
    this.currentValue = newValue;
    this.valueChange.emit(updatedProperty);
  }

  /**
   * Handle input change events
   */
  onInputChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.onValueChange(target.value || '');
  }

  /**
   * Handle number input change events
   */
  onNumberChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.onValueChange(+(target.value || 0));
  }

  /**
   * Handle select change events
   */
  onSelectChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    this.onValueChange(target.value || '');
  }

  /**
   * Handle textarea change events
   */
  onTextareaChange(event: Event): void {
    const target = event.target as HTMLTextAreaElement;
    this.onValueChange(target.value || '');
  }

  /**
   * Create a new responsive property
   */
  private createResponsiveProperty(value: any): ResponsiveProperty<any> {
    const responsiveProperty: ResponsiveProperty<any> = {
      default: this.propertyValue || value
    };

    if (this.activeBreakpoint) {
      switch (this.activeBreakpoint.deviceType) {
        case DeviceType.MOBILE:
          responsiveProperty.mobile = value;
          break;
        case DeviceType.TABLET:
          responsiveProperty.tablet = value;
          break;
        case DeviceType.DESKTOP:
          responsiveProperty.desktop = value;
          break;
      }
    }

    return responsiveProperty;
  }

  /**
   * Toggle responsive mode for this property
   */
  toggleResponsiveMode(): void {
    if (this.isResponsive) {
      // Convert back to simple value
      const simpleValue = this.viewportService.getResponsivePropertyValue(this.propertyValue);
      this.propertyValue = simpleValue;
      this.currentValue = simpleValue;
      this.isResponsive = false;
      this.valueChange.emit(simpleValue);
    } else {
      // Convert to responsive property
      const responsiveProperty = this.createResponsiveProperty(this.currentValue);
      this.propertyValue = responsiveProperty;
      this.isResponsive = true;
      this.valueChange.emit(responsiveProperty);
    }
  }

  /**
   * Switch to specific breakpoint
   */
  switchToBreakpoint(breakpoint: Breakpoint): void {
    this.activeBreakpoint = breakpoint;
    this.updateCurrentValue();
  }

  /**
   * Toggle breakpoint tabs visibility
   */
  toggleBreakpointTabs(): void {
    this.showBreakpointTabs = !this.showBreakpointTabs;
  }

  /**
   * Get value for specific breakpoint
   */
  getBreakpointValue(breakpoint: Breakpoint): any {
    if (!this.isResponsive) return this.propertyValue;

    const property = this.propertyValue as ResponsiveProperty<any>;
    
    switch (breakpoint.deviceType) {
      case DeviceType.MOBILE:
        return property.mobile ?? property.default;
      case DeviceType.TABLET:
        return property.tablet ?? property.default;
      case DeviceType.DESKTOP:
        return property.desktop ?? property.default;
      default:
        return property.custom?.[breakpoint.id] ?? property.default;
    }
  }

  /**
   * Check if breakpoint has custom value
   */
  hasBreakpointValue(breakpoint: Breakpoint): boolean {
    if (!this.isResponsive) return false;

    const property = this.propertyValue as ResponsiveProperty<any>;
    
    switch (breakpoint.deviceType) {
      case DeviceType.MOBILE:
        return property.mobile !== undefined;
      case DeviceType.TABLET:
        return property.tablet !== undefined;
      case DeviceType.DESKTOP:
        return property.desktop !== undefined;
      default:
        return property.custom?.[breakpoint.id] !== undefined;
    }
  }

  /**
   * Clear breakpoint-specific value (inherit from default)
   */
  clearBreakpointValue(breakpoint: Breakpoint): void {
    if (!this.isResponsive) return;

    const property = { ...this.propertyValue } as ResponsiveProperty<any>;
    
    switch (breakpoint.deviceType) {
      case DeviceType.MOBILE:
        delete property.mobile;
        break;
      case DeviceType.TABLET:
        delete property.tablet;
        break;
      case DeviceType.DESKTOP:
        delete property.desktop;
        break;
      default:
        if (property.custom) {
          delete property.custom[breakpoint.id];
        }
    }

    this.propertyValue = property;
    this.updateCurrentValue();
    this.valueChange.emit(property);
  }

  /**
   * Get device type icon
   */
  getDeviceTypeIcon(deviceType: DeviceType): string {
    switch (deviceType) {
      case DeviceType.MOBILE:
        return '📱';
      case DeviceType.TABLET:
        return '📱';
      case DeviceType.DESKTOP:
        return '🖥️';
      default:
        return '📱';
    }
  }

  /**
   * Check if current breakpoint is active
   */
  isBreakpointActive(breakpoint: Breakpoint): boolean {
    return this.activeBreakpoint?.id === breakpoint.id;
  }

  /**
   * Get inheritance info for current value
   */
  getInheritanceInfo(): string {
    if (!this.isResponsive || !this.activeBreakpoint) return '';

    const property = this.propertyValue as ResponsiveProperty<any>;
    const currentBreakpoint = this.activeBreakpoint;

    if (this.hasBreakpointValue(currentBreakpoint)) {
      return 'Custom value';
    }

    // Check inheritance chain
    switch (currentBreakpoint.deviceType) {
      case DeviceType.MOBILE:
        if (property.tablet !== undefined) return 'Inherited from tablet';
        if (property.desktop !== undefined) return 'Inherited from desktop';
        return 'Inherited from default';
      case DeviceType.TABLET:
        if (property.desktop !== undefined) return 'Inherited from desktop';
        return 'Inherited from default';
      case DeviceType.DESKTOP:
        return 'Inherited from default';
      default:
        return 'Inherited from default';
    }
  }
}
