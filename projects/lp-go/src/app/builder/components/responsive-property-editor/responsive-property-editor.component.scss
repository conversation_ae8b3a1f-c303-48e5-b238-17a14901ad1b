.responsive-property-editor {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  .property-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .property-label {
      font-size: 0.875rem;
      font-weight: 500;
      color: #374151;
    }

    .responsive-toggle {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 1.5rem;
      height: 1.5rem;
      border: 1px solid #d1d5db;
      background: white;
      border-radius: 3px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: #f9fafb;
        border-color: #9ca3af;
      }

      &.active {
        background: #3b82f6;
        border-color: #3b82f6;
        color: white;

        &:hover {
          background: #2563eb;
        }
      }

      .icon {
        font-size: 0.75rem;
      }
    }
  }

  .breakpoint-tabs {
    display: flex;
    gap: 0.25rem;
    padding: 0.25rem;
    background: #f3f4f6;
    border-radius: 6px;
    overflow-x: auto;

    .breakpoint-tab {
      display: flex;
      align-items: center;
      gap: 0.375rem;
      padding: 0.375rem 0.75rem;
      border: 1px solid transparent;
      background: transparent;
      border-radius: 4px;
      font-size: 0.75rem;
      cursor: pointer;
      transition: all 0.2s;
      white-space: nowrap;
      position: relative;

      &:hover {
        background: #e5e7eb;
      }

      &.active {
        background: white;
        border-color: #d1d5db;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      }

      &.has-value {
        .value-indicator {
          color: #3b82f6;
        }
      }

      .tab-icon {
        font-size: 0.875rem;
      }

      .tab-label {
        font-weight: 500;
        color: #374151;
      }

      .value-indicator {
        font-size: 0.5rem;
        color: #9ca3af;
        position: absolute;
        top: 0.125rem;
        right: 0.125rem;
      }
    }
  }

  .property-input-container {
    position: relative;
    display: flex;
    align-items: center;

    .property-input {
      width: 100%;
      padding: 0.5rem 0.75rem;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      font-size: 0.875rem;
      transition: border-color 0.2s, box-shadow 0.2s;

      &:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      &:disabled {
        background: #f9fafb;
        color: #9ca3af;
        cursor: not-allowed;
      }

      &.color-input {
        width: 3rem;
        height: 2.5rem;
        padding: 0.25rem;
        cursor: pointer;

        &::-webkit-color-swatch-wrapper {
          padding: 0;
        }

        &::-webkit-color-swatch {
          border: none;
          border-radius: 4px;
        }
      }

      &.textarea-input {
        resize: vertical;
        min-height: 4rem;
      }
    }

    .clear-value-button {
      position: absolute;
      right: 0.5rem;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      align-items: center;
      justify-content: center;
      width: 1.25rem;
      height: 1.25rem;
      border: none;
      background: #ef4444;
      color: white;
      border-radius: 50%;
      cursor: pointer;
      font-size: 0.75rem;
      transition: all 0.2s;

      &:hover {
        background: #dc2626;
      }

      .icon {
        line-height: 1;
      }
    }
  }

  .inheritance-info {
    font-size: 0.75rem;
    color: #6b7280;
    font-style: italic;

    .inheritance-text {
      display: flex;
      align-items: center;
      gap: 0.25rem;

      &::before {
        content: "↗";
        font-size: 0.625rem;
        color: #9ca3af;
      }
    }
  }

  .breakpoint-values-preview {
    .toggle-preview-button {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.375rem 0.75rem;
      border: 1px solid #d1d5db;
      background: #f9fafb;
      border-radius: 4px;
      font-size: 0.75rem;
      color: #6b7280;
      cursor: pointer;
      transition: all 0.2s;
      width: 100%;

      &:hover {
        background: #f3f4f6;
        border-color: #9ca3af;
      }

      .icon {
        font-size: 0.875rem;
      }

      .label {
        flex: 1;
        text-align: left;
      }
    }
  }

  .breakpoint-values-detail {
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background: #f9fafb;

    .detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem 1rem;
      border-bottom: 1px solid #e5e7eb;

      h4 {
        font-size: 0.875rem;
        font-weight: 600;
        color: #374151;
        margin: 0;
      }

      .toggle-preview-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 1.5rem;
        height: 1.5rem;
        border: none;
        background: none;
        color: #6b7280;
        cursor: pointer;
        border-radius: 3px;
        transition: all 0.2s;

        &:hover {
          background: #e5e7eb;
        }

        .icon {
          font-size: 0.875rem;
        }
      }
    }

    .breakpoint-values-list {
      padding: 0.5rem;

      .breakpoint-value-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.5rem 0.75rem;
        border-radius: 4px;
        transition: background-color 0.2s;

        &:hover {
          background: #f3f4f6;
        }

        &.active {
          background: #dbeafe;
          border: 1px solid #93c5fd;
        }

        .breakpoint-info {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          min-width: 0;
          flex: 1;

          .breakpoint-icon {
            font-size: 0.875rem;
            flex-shrink: 0;
          }

          .breakpoint-name {
            font-size: 0.75rem;
            font-weight: 500;
            color: #374151;
            flex-shrink: 0;
          }

          .breakpoint-dimensions {
            font-size: 0.625rem;
            color: #9ca3af;
            font-family: monospace;
          }
        }

        .breakpoint-value {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          min-width: 0;
          flex: 1;

          .value-text {
            font-size: 0.75rem;
            color: #374151;
            font-family: monospace;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            &.inherited {
              color: #9ca3af;
              font-style: italic;
            }
          }

          .value-status {
            font-size: 0.625rem;
            color: #9ca3af;
            flex-shrink: 0;
          }
        }

        .breakpoint-actions {
          display: flex;
          gap: 0.25rem;
          flex-shrink: 0;

          .edit-button,
          .clear-button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 1.25rem;
            height: 1.25rem;
            border: none;
            background: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.75rem;
            transition: all 0.2s;

            &:hover {
              background: #e5e7eb;
            }
          }

          .edit-button {
            color: #3b82f6;

            &:hover {
              background: #dbeafe;
            }
          }

          .clear-button {
            color: #ef4444;

            &:hover {
              background: #fee2e2;
            }
          }
        }
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 640px) {
  .responsive-property-editor {
    .breakpoint-tabs {
      .breakpoint-tab {
        padding: 0.25rem 0.5rem;

        .tab-label {
          display: none;
        }
      }
    }

    .breakpoint-values-detail {
      .breakpoint-values-list {
        .breakpoint-value-item {
          flex-direction: column;
          align-items: stretch;
          gap: 0.5rem;

          .breakpoint-info,
          .breakpoint-value {
            justify-content: space-between;
          }

          .breakpoint-actions {
            justify-content: center;
          }
        }
      }
    }
  }
}

// Animation for tabs
.breakpoint-tabs {
  .breakpoint-tab {
    &.active {
      animation: tabActivate 0.2s ease-out;
    }
  }
}

@keyframes tabActivate {
  from {
    transform: scale(0.95);
    opacity: 0.8;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

// Focus styles for accessibility
.responsive-toggle:focus,
.breakpoint-tab:focus,
.property-input:focus,
.toggle-preview-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}
