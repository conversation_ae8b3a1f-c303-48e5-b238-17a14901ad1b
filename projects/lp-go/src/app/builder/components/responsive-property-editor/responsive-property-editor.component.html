<!-- Responsive Property Editor -->
<div class="responsive-property-editor">
  
  <!-- Property Header -->
  <div class="property-header">
    <label class="property-label">{{ propertyLabel }}</label>
    
    <!-- Responsive Toggle -->
    <button
      *ngIf="viewportState?.responsiveModeEnabled"
      class="responsive-toggle"
      [class.active]="isResponsive"
      (click)="toggleResponsiveMode()"
      [title]="isResponsive ? 'Disable responsive values' : 'Enable responsive values'">
      <span class="icon">📐</span>
    </button>
  </div>

  <!-- Breakpoint Tabs (when responsive) -->
  <div class="breakpoint-tabs" *ngIf="isResponsive && viewportState?.responsiveModeEnabled">
    <button
      *ngFor="let breakpoint of breakpoints"
      class="breakpoint-tab"
      [class.active]="isBreakpointActive(breakpoint)"
      [class.has-value]="hasBreakpointValue(breakpoint)"
      (click)="switchToBreakpoint(breakpoint)"
      [title]="breakpoint.name + ' (' + breakpoint.width + '×' + breakpoint.height + ')'">
      <span class="tab-icon">{{ getDeviceTypeIcon(breakpoint.deviceType) }}</span>
      <span class="tab-label">{{ breakpoint.name }}</span>
      <span class="value-indicator" *ngIf="hasBreakpointValue(breakpoint)">●</span>
    </button>
  </div>

  <!-- Property Input -->
  <div class="property-input-container">
    
    <!-- Text Input -->
    <input
      *ngIf="propertyType === 'text'"
      type="text"
      class="property-input"
      [value]="currentValue || ''"
      [placeholder]="placeholder"
      [disabled]="disabled"
      (input)="onInputChange($event)"
      (blur)="onInputChange($event)">

    <!-- Number Input -->
    <input
      *ngIf="propertyType === 'number'"
      type="number"
      class="property-input"
      [value]="currentValue || ''"
      [placeholder]="placeholder"
      [disabled]="disabled"
      (input)="onNumberChange($event)"
      (blur)="onNumberChange($event)">

    <!-- Color Input -->
    <input
      *ngIf="propertyType === 'color'"
      type="color"
      class="property-input color-input"
      [value]="currentValue || '#000000'"
      [disabled]="disabled"
      (input)="onInputChange($event)"
      (change)="onInputChange($event)">

    <!-- Select Input -->
    <select
      *ngIf="propertyType === 'select'"
      class="property-input"
      [value]="currentValue || ''"
      [disabled]="disabled"
      (change)="onSelectChange($event)">
      <option value="" disabled>{{ placeholder || 'Select option' }}</option>
      <option
        *ngFor="let option of selectOptions"
        [value]="option.value">
        {{ option.label }}
      </option>
    </select>

    <!-- Textarea Input -->
    <textarea
      *ngIf="propertyType === 'textarea'"
      class="property-input textarea-input"
      [value]="currentValue || ''"
      [placeholder]="placeholder"
      [disabled]="disabled"
      (input)="onTextareaChange($event)"
      (blur)="onTextareaChange($event)"
      rows="3">
    </textarea>

    <!-- Clear Breakpoint Value Button -->
    <button
      *ngIf="isResponsive && hasBreakpointValue(activeBreakpoint!) && activeBreakpoint"
      class="clear-value-button"
      (click)="clearBreakpointValue(activeBreakpoint)"
      title="Clear custom value for this breakpoint">
      <span class="icon">✕</span>
    </button>
  </div>

  <!-- Inheritance Info -->
  <div class="inheritance-info" *ngIf="isResponsive && viewportState?.responsiveModeEnabled">
    <span class="inheritance-text">{{ getInheritanceInfo() }}</span>
  </div>

  <!-- Breakpoint Values Preview (collapsed) -->
  <div class="breakpoint-values-preview" *ngIf="isResponsive && !showBreakpointTabs">
    <button
      class="toggle-preview-button"
      (click)="toggleBreakpointTabs()"
      title="Show all breakpoint values">
      <span class="icon">👁️</span>
      <span class="label">Show all values</span>
    </button>
  </div>

  <!-- Breakpoint Values Detail (expanded) -->
  <div class="breakpoint-values-detail" *ngIf="isResponsive && showBreakpointTabs">
    <div class="detail-header">
      <h4>Breakpoint Values</h4>
      <button
        class="toggle-preview-button"
        (click)="toggleBreakpointTabs()"
        title="Hide breakpoint values">
        <span class="icon">👁️</span>
      </button>
    </div>
    
    <div class="breakpoint-values-list">
      <div
        *ngFor="let breakpoint of breakpoints"
        class="breakpoint-value-item"
        [class.active]="isBreakpointActive(breakpoint)">
        <div class="breakpoint-info">
          <span class="breakpoint-icon">{{ getDeviceTypeIcon(breakpoint.deviceType) }}</span>
          <span class="breakpoint-name">{{ breakpoint.name }}</span>
          <span class="breakpoint-dimensions">{{ breakpoint.width }}×{{ breakpoint.height }}</span>
        </div>
        <div class="breakpoint-value">
          <span class="value-text" [class.inherited]="!hasBreakpointValue(breakpoint)">
            {{ getBreakpointValue(breakpoint) || 'Not set' }}
          </span>
          <span class="value-status" *ngIf="!hasBreakpointValue(breakpoint)">
            (inherited)
          </span>
        </div>
        <div class="breakpoint-actions">
          <button
            class="edit-button"
            (click)="switchToBreakpoint(breakpoint)"
            title="Edit value for this breakpoint">
            ✏️
          </button>
          <button
            *ngIf="hasBreakpointValue(breakpoint)"
            class="clear-button"
            (click)="clearBreakpointValue(breakpoint)"
            title="Clear custom value">
            ✕
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
