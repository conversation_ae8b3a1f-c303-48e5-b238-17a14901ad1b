import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

import { ResponsiveValidationService, ResponsiveValidationIssue } from '../../services/responsive-validation.service';

/**
 * Responsive Warnings Component
 * Displays responsive design validation issues and warnings
 */
@Component({
  selector: 'app-responsive-warnings',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './responsive-warnings.component.html',
  styleUrls: ['./responsive-warnings.component.scss']
})
export class ResponsiveWarningsComponent implements OnInit, OnDestroy {
  // Component state
  allIssues: ResponsiveValidationIssue[] = [];
  filteredIssues: ResponsiveValidationIssue[] = [];
  
  // UI state
  isExpanded = false;
  selectedSeverity: 'all' | 'error' | 'warning' | 'info' = 'all';
  selectedComponent = '';
  
  // Statistics
  errorCount = 0;
  warningCount = 0;
  infoCount = 0;

  private destroy$ = new Subject<void>();

  constructor(private validationService: ResponsiveValidationService) {}

  ngOnInit(): void {
    this.subscribeToValidationIssues();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Subscribe to validation issues
   */
  private subscribeToValidationIssues(): void {
    this.validationService.getValidationIssues().pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (issues) => {
        this.allIssues = issues;
        this.updateStatistics();
        this.applyFilters();
      },
      error: (error) => {
        console.error('Error loading validation issues:', error);
      }
    });
  }

  /**
   * Update issue statistics
   */
  private updateStatistics(): void {
    this.errorCount = this.allIssues.filter(issue => issue.severity === 'error').length;
    this.warningCount = this.allIssues.filter(issue => issue.severity === 'warning').length;
    this.infoCount = this.allIssues.filter(issue => issue.severity === 'info').length;
  }

  /**
   * Apply filters to issues
   */
  private applyFilters(): void {
    let filtered = [...this.allIssues];

    // Filter by severity
    if (this.selectedSeverity !== 'all') {
      filtered = filtered.filter(issue => issue.severity === this.selectedSeverity);
    }

    // Filter by component
    if (this.selectedComponent) {
      filtered = filtered.filter(issue => issue.componentId === this.selectedComponent);
    }

    this.filteredIssues = filtered;
  }

  /**
   * Toggle panel expansion
   */
  toggleExpanded(): void {
    this.isExpanded = !this.isExpanded;
  }

  /**
   * Set severity filter
   */
  setSeverityFilter(severity: 'all' | 'error' | 'warning' | 'info'): void {
    this.selectedSeverity = severity;
    this.applyFilters();
  }

  /**
   * Set component filter
   */
  setComponentFilter(componentId: string): void {
    this.selectedComponent = componentId;
    this.applyFilters();
  }

  /**
   * Clear all filters
   */
  clearFilters(): void {
    this.selectedSeverity = 'all';
    this.selectedComponent = '';
    this.applyFilters();
  }

  /**
   * Apply auto-fix for an issue
   */
  applyAutoFix(issue: ResponsiveValidationIssue): void {
    this.validationService.applyAutoFix(issue.id);
  }

  /**
   * Dismiss an issue
   */
  dismissIssue(issue: ResponsiveValidationIssue): void {
    this.validationService.dismissIssue(issue.id);
  }

  /**
   * Get severity icon
   */
  getSeverityIcon(severity: 'error' | 'warning' | 'info'): string {
    switch (severity) {
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      default:
        return 'ℹ️';
    }
  }

  /**
   * Get severity color class
   */
  getSeverityColorClass(severity: 'error' | 'warning' | 'info'): string {
    switch (severity) {
      case 'error':
        return 'severity-error';
      case 'warning':
        return 'severity-warning';
      case 'info':
        return 'severity-info';
      default:
        return 'severity-info';
    }
  }

  /**
   * Get device type icon
   */
  getDeviceTypeIcon(deviceType: string): string {
    switch (deviceType) {
      case 'mobile':
        return '📱';
      case 'tablet':
        return '📱';
      case 'desktop':
        return '🖥️';
      default:
        return '📱';
    }
  }

  /**
   * Get unique component IDs
   */
  getUniqueComponentIds(): string[] {
    const componentIds = this.allIssues.map(issue => issue.componentId);
    return [...new Set(componentIds)];
  }

  /**
   * Get total issue count
   */
  getTotalIssueCount(): number {
    return this.allIssues.length;
  }

  /**
   * Get filtered issue count
   */
  getFilteredIssueCount(): number {
    return this.filteredIssues.length;
  }

  /**
   * Check if there are any issues
   */
  hasIssues(): boolean {
    return this.allIssues.length > 0;
  }

  /**
   * Check if there are critical issues (errors)
   */
  hasCriticalIssues(): boolean {
    return this.errorCount > 0;
  }

  /**
   * Get panel status text
   */
  getPanelStatusText(): string {
    if (!this.hasIssues()) {
      return 'No responsive issues found';
    }

    const total = this.getTotalIssueCount();
    const errors = this.errorCount;
    const warnings = this.warningCount;

    if (errors > 0) {
      return `${errors} error${errors !== 1 ? 's' : ''}, ${warnings} warning${warnings !== 1 ? 's' : ''}`;
    } else if (warnings > 0) {
      return `${warnings} warning${warnings !== 1 ? 's' : ''}`;
    } else {
      return `${total} suggestion${total !== 1 ? 's' : ''}`;
    }
  }

  /**
   * Get panel status class
   */
  getPanelStatusClass(): string {
    if (this.hasCriticalIssues()) {
      return 'status-error';
    } else if (this.warningCount > 0) {
      return 'status-warning';
    } else if (this.infoCount > 0) {
      return 'status-info';
    } else {
      return 'status-success';
    }
  }

  /**
   * Format breakpoint info
   */
  formatBreakpointInfo(issue: ResponsiveValidationIssue): string {
    const bp = issue.breakpoint;
    return `${bp.name} (${bp.width}×${bp.height})`;
  }

  /**
   * Track by function for ngFor
   */
  trackByIssueId(_index: number, issue: ResponsiveValidationIssue): string {
    return issue.id;
  }
}
