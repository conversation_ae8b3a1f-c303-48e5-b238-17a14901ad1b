<!-- Responsive Warnings Panel -->
<div class="responsive-warnings-panel" [class]="getPanelStatusClass()">
  
  <!-- Panel Header -->
  <div class="panel-header" (click)="toggleExpanded()">
    <div class="header-content">
      <div class="status-indicator">
        <span class="status-icon">{{ hasIssues() ? (hasCriticalIssues() ? '❌' : '⚠️') : '✅' }}</span>
        <span class="status-text">{{ getPanelStatusText() }}</span>
      </div>
      
      <div class="issue-counts" *ngIf="hasIssues()">
        <span class="count-badge error" *ngIf="errorCount > 0">{{ errorCount }}</span>
        <span class="count-badge warning" *ngIf="warningCount > 0">{{ warningCount }}</span>
        <span class="count-badge info" *ngIf="infoCount > 0">{{ infoCount }}</span>
      </div>
    </div>
    
    <button class="expand-button" [class.expanded]="isExpanded">
      <span class="expand-icon">▼</span>
    </button>
  </div>

  <!-- Panel Content -->
  <div class="panel-content" *ngIf="isExpanded">
    
    <!-- Filters -->
    <div class="filters-section" *ngIf="hasIssues()">
      <div class="filter-group">
        <label class="filter-label">Severity:</label>
        <div class="filter-buttons">
          <button
            class="filter-button"
            [class.active]="selectedSeverity === 'all'"
            (click)="setSeverityFilter('all')">
            All ({{ getTotalIssueCount() }})
          </button>
          <button
            class="filter-button error"
            [class.active]="selectedSeverity === 'error'"
            (click)="setSeverityFilter('error')"
            *ngIf="errorCount > 0">
            Errors ({{ errorCount }})
          </button>
          <button
            class="filter-button warning"
            [class.active]="selectedSeverity === 'warning'"
            (click)="setSeverityFilter('warning')"
            *ngIf="warningCount > 0">
            Warnings ({{ warningCount }})
          </button>
          <button
            class="filter-button info"
            [class.active]="selectedSeverity === 'info'"
            (click)="setSeverityFilter('info')"
            *ngIf="infoCount > 0">
            Info ({{ infoCount }})
          </button>
        </div>
      </div>

      <div class="filter-group" *ngIf="getUniqueComponentIds().length > 1">
        <label class="filter-label">Component:</label>
        <select
          class="filter-select"
          [(ngModel)]="selectedComponent"
          (ngModelChange)="setComponentFilter($event)">
          <option value="">All Components</option>
          <option
            *ngFor="let componentId of getUniqueComponentIds()"
            [value]="componentId">
            {{ componentId }}
          </option>
        </select>
      </div>

      <button
        class="clear-filters-button"
        (click)="clearFilters()"
        *ngIf="selectedSeverity !== 'all' || selectedComponent">
        Clear Filters
      </button>
    </div>

    <!-- Issues List -->
    <div class="issues-list" *ngIf="filteredIssues.length > 0">
      <div
        *ngFor="let issue of filteredIssues; trackBy: trackByIssueId"
        class="issue-item"
        [class]="getSeverityColorClass(issue.severity)">
        
        <!-- Issue Header -->
        <div class="issue-header">
          <div class="issue-info">
            <span class="severity-icon">{{ getSeverityIcon(issue.severity) }}</span>
            <span class="issue-message">{{ issue.message }}</span>
          </div>
          
          <div class="issue-actions">
            <button
              *ngIf="issue.autoFixAvailable"
              class="action-button auto-fix"
              (click)="applyAutoFix(issue)"
              title="Apply automatic fix">
              🔧 Fix
            </button>
            <button
              class="action-button dismiss"
              (click)="dismissIssue(issue)"
              title="Dismiss this issue">
              ✕
            </button>
          </div>
        </div>

        <!-- Issue Details -->
        <div class="issue-details">
          <div class="detail-row">
            <span class="detail-label">Component:</span>
            <span class="detail-value">{{ issue.componentId }}</span>
          </div>
          
          <div class="detail-row">
            <span class="detail-label">Breakpoint:</span>
            <span class="detail-value">
              <span class="device-icon">{{ getDeviceTypeIcon(issue.breakpoint.deviceType) }}</span>
              {{ formatBreakpointInfo(issue) }}
            </span>
          </div>
          
          <div class="detail-row">
            <span class="detail-label">Rule:</span>
            <span class="detail-value">{{ issue.ruleId }}</span>
          </div>
        </div>

        <!-- Suggestion -->
        <div class="issue-suggestion" *ngIf="issue.suggestion">
          <div class="suggestion-header">
            <span class="suggestion-icon">💡</span>
            <span class="suggestion-label">Suggestion:</span>
          </div>
          <p class="suggestion-text">{{ issue.suggestion }}</p>
        </div>
      </div>
    </div>

    <!-- No Issues Message -->
    <div class="no-issues-message" *ngIf="!hasIssues()">
      <div class="success-icon">✅</div>
      <h3>Great job!</h3>
      <p>No responsive design issues found. Your components follow responsive best practices.</p>
    </div>

    <!-- No Filtered Issues Message -->
    <div class="no-filtered-issues-message" *ngIf="hasIssues() && filteredIssues.length === 0">
      <div class="filter-icon">🔍</div>
      <h3>No issues match your filters</h3>
      <p>Try adjusting your filters to see more issues.</p>
      <button class="clear-filters-button" (click)="clearFilters()">
        Clear Filters
      </button>
    </div>

    <!-- Panel Footer -->
    <div class="panel-footer" *ngIf="hasIssues()">
      <div class="footer-info">
        <span class="results-count">
          Showing {{ getFilteredIssueCount() }} of {{ getTotalIssueCount() }} issues
        </span>
      </div>
      
      <div class="footer-actions">
        <button class="footer-button" (click)="clearFilters()">
          Reset Filters
        </button>
      </div>
    </div>
  </div>
</div>
