.responsive-warnings-panel {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;

  &.status-error { border-color: #ef4444; .panel-header { background: #fef2f2; border-bottom-color: #fecaca; } }
  &.status-warning { border-color: #f59e0b; .panel-header { background: #fffbeb; border-bottom-color: #fed7aa; } }
  &.status-info { border-color: #3b82f6; .panel-header { background: #eff6ff; border-bottom-color: #bfdbfe; } }
  &.status-success { border-color: #10b981; .panel-header { background: #ecfdf5; border-bottom-color: #a7f3d0; } }

  .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover { background: #f3f4f6; }

    .header-content {
      display: flex;
      align-items: center;
      gap: 1rem;
      flex: 1;

      .status-indicator {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        .status-icon { font-size: 1rem; }
        .status-text { font-size: 0.875rem; font-weight: 500; color: #374151; }
      }

      .issue-counts {
        display: flex;
        gap: 0.25rem;

        .count-badge {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          min-width: 1.25rem;
          height: 1.25rem;
          padding: 0 0.25rem;
          border-radius: 0.375rem;
          font-size: 0.75rem;
          font-weight: 600;
          color: white;

          &.error { background: #ef4444; }
          &.warning { background: #f59e0b; }
          &.info { background: #3b82f6; }
        }
      }
    }

    .expand-button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 1.5rem;
      height: 1.5rem;
      border: none;
      background: none;
      color: #6b7280;
      cursor: pointer;
      border-radius: 0.25rem;
      transition: all 0.2s;

      &:hover { background: #f3f4f6; color: #374151; }
      &.expanded .expand-icon { transform: rotate(180deg); }

      .expand-icon { font-size: 0.75rem; transition: transform 0.2s; }
    }
  }

  .panel-content {
    padding: 1rem;

    .filters-section {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      margin-bottom: 1rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid #e5e7eb;

      .filter-group {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .filter-label {
          font-size: 0.875rem;
          font-weight: 500;
          color: #374151;
          white-space: nowrap;
        }

        .filter-buttons {
          display: flex;
          gap: 0.25rem;

          .filter-button {
            padding: 0.25rem 0.75rem;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
              background: #f9fafb;
            }

            &.active {
              background: #3b82f6;
              color: white;
              border-color: #3b82f6;
            }

            &.error.active {
              background: #ef4444;
              border-color: #ef4444;
            }

            &.warning.active {
              background: #f59e0b;
              border-color: #f59e0b;
            }

            &.info.active {
              background: #3b82f6;
              border-color: #3b82f6;
            }
          }
        }

        .filter-select {
          padding: 0.25rem 0.5rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          background: white;

          &:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }
        }
      }

      .clear-filters-button {
        padding: 0.25rem 0.75rem;
        border: 1px solid #d1d5db;
        background: #f9fafb;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        color: #6b7280;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          background: #f3f4f6;
          color: #374151;
        }
      }
    }

    .issues-list {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;

      .issue-item {
        border: 1px solid #e5e7eb;
        border-radius: 6px;
        padding: 0.75rem;
        background: #fafafa;

        &.severity-error {
          border-color: #fecaca;
          background: #fef2f2;
        }

        &.severity-warning {
          border-color: #fed7aa;
          background: #fffbeb;
        }

        &.severity-info {
          border-color: #bfdbfe;
          background: #eff6ff;
        }

        .issue-header {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          gap: 1rem;
          margin-bottom: 0.5rem;

          .issue-info {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
            flex: 1;

            .severity-icon {
              font-size: 1rem;
              flex-shrink: 0;
              margin-top: 0.125rem;
            }

            .issue-message {
              font-size: 0.875rem;
              font-weight: 500;
              color: #374151;
              line-height: 1.4;
            }
          }

          .issue-actions {
            display: flex;
            gap: 0.25rem;
            flex-shrink: 0;

            .action-button {
              padding: 0.25rem 0.5rem;
              border: 1px solid #d1d5db;
              background: white;
              border-radius: 0.25rem;
              font-size: 0.75rem;
              cursor: pointer;
              transition: all 0.2s;

              &:hover {
                background: #f9fafb;
              }

              &.auto-fix {
                color: #059669;
                border-color: #a7f3d0;

                &:hover {
                  background: #ecfdf5;
                }
              }

              &.dismiss {
                color: #6b7280;

                &:hover {
                  color: #ef4444;
                  background: #fef2f2;
                }
              }
            }
          }
        }

        .issue-details {
          display: flex;
          flex-direction: column;
          gap: 0.25rem;
          margin-bottom: 0.5rem;

          .detail-row {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.75rem;

            .detail-label {
              font-weight: 500;
              color: #6b7280;
              min-width: 5rem;
            }

            .detail-value {
              color: #374151;
              font-family: monospace;
              display: flex;
              align-items: center;
              gap: 0.25rem;

              .device-icon {
                font-size: 0.875rem;
              }
            }
          }
        }

        .issue-suggestion {
          border-top: 1px solid #e5e7eb;
          padding-top: 0.5rem;

          .suggestion-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.25rem;

            .suggestion-icon {
              font-size: 0.875rem;
            }

            .suggestion-label {
              font-size: 0.75rem;
              font-weight: 600;
              color: #374151;
            }
          }

          .suggestion-text {
            font-size: 0.75rem;
            color: #6b7280;
            line-height: 1.4;
            margin: 0;
            padding-left: 1.375rem;
          }
        }
      }
    }

    .no-issues-message,
    .no-filtered-issues-message {
      text-align: center;
      padding: 2rem 1rem;
      color: #6b7280;

      .success-icon,
      .filter-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
      }

      h3 {
        font-size: 1.125rem;
        font-weight: 600;
        color: #374151;
        margin: 0 0 0.5rem 0;
      }

      p {
        font-size: 0.875rem;
        margin: 0 0 1rem 0;
      }
    }

    .panel-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 1rem;
      border-top: 1px solid #e5e7eb;
      margin-top: 1rem;

      .footer-info {
        .results-count {
          font-size: 0.75rem;
          color: #6b7280;
        }
      }

      .footer-actions {
        .footer-button {
          padding: 0.25rem 0.75rem;
          border: 1px solid #d1d5db;
          background: white;
          border-radius: 0.375rem;
          font-size: 0.75rem;
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            background: #f9fafb;
          }
        }
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .responsive-warnings-panel {
    .panel-content {
      padding: 0.75rem;

      .filters-section {
        flex-direction: column;
        align-items: stretch;

        .filter-group {
          flex-direction: column;
          align-items: stretch;
          gap: 0.25rem;

          .filter-buttons {
            justify-content: center;
          }
        }
      }

      .issues-list {
        .issue-item {
          .issue-header {
            flex-direction: column;
            align-items: stretch;
            gap: 0.5rem;

            .issue-actions {
              justify-content: flex-end;
            }
          }

          .issue-details {
            .detail-row {
              flex-direction: column;
              align-items: stretch;
              gap: 0.125rem;

              .detail-label {
                min-width: auto;
              }
            }
          }
        }
      }

      .panel-footer {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
      }
    }
  }
}

// Animation for panel expansion
.panel-content {
  animation: panelExpand 0.2s ease-out;
}

@keyframes panelExpand {
  from {
    opacity: 0;
    transform: translateY(-0.5rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
