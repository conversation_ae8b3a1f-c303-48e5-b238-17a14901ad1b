import { InjectionToken, Provider } from '@angular/core';

export const COMPONENT_PROPERTIES = new InjectionToken<any>('componentProperties');

/**
 * Component property schemas for mobile-components
 */
const componentPropertySchemas = {
  // Header Components
  'dashboard-header': {
    config: {
      type: 'object',
      label: 'Header Configuration',
      properties: {
        showLogo: { type: 'boolean', label: 'Show Logo', default: true },
        showBalance: { type: 'boolean', label: 'Show Balance', default: true },
        showUserInfo: { type: 'boolean', label: 'Show User Info', default: true },
        showStats: { type: 'boolean', label: 'Show Stats', default: true },
        balanceLabel: { type: 'string', label: 'Balance Label', default: 'Current Balance' },
        balanceUnit: { type: 'string', label: 'Balance Unit', default: 'points' },
        logoUrl: { type: 'string', label: 'Logo URL', default: 'assets/images/logo.png' },
        logoAlt: { type: 'string', label: 'Logo Alt Text', default: 'Logo' }
      }
    },
    profile: {
      type: 'object',
      label: 'User Profile',
      properties: {
        givenNames: { type: 'string', label: 'First Name', default: 'John' },
        surname: { type: 'string', label: 'Last Name', default: 'Doe' },
        currentBalance: { type: 'number', label: 'Current Balance', default: 1250 },
        availRands: { type: 'number', label: 'Available Rands', default: 125.50 },
        availUnits: { type: 'number', label: 'Available Units', default: 1000 },
        newMembershipNumber: { type: 'string', label: 'Membership Number', default: 'MB123456' }
      }
    }
  },

  'home-header': {
    config: {
      type: 'object',
      label: 'Home Header Configuration',
      properties: {
        title: { type: 'string', label: 'Title', default: 'Welcome' },
        subtitle: { type: 'string', label: 'Subtitle', default: 'to your dashboard' },
        showSearch: { type: 'boolean', label: 'Show Search', default: true },
        showNotifications: { type: 'boolean', label: 'Show Notifications', default: true }
      }
    }
  },

  'profile-header': {
    config: {
      type: 'object',
      label: 'Profile Header Configuration',
      properties: {
        showAvatar: { type: 'boolean', label: 'Show Avatar', default: true },
        showCoverImage: { type: 'boolean', label: 'Show Cover Image', default: true },
        editMode: { type: 'boolean', label: 'Edit Mode', default: false }
      }
    },
    profile: {
      type: 'object',
      label: 'Profile Data',
      properties: {
        name: { type: 'string', label: 'Name', default: 'John Doe' },
        email: { type: 'string', label: 'Email', default: '<EMAIL>' },
        avatar: { type: 'string', label: 'Avatar URL', default: 'assets/images/avatar.png' },
        coverImage: { type: 'string', label: 'Cover Image URL', default: 'assets/images/cover.jpg' }
      }
    }
  },

  // Basic Components
  'button': {
    text: { type: 'string', label: 'Button Text', default: 'Click Me' },
    color: { type: 'select', label: 'Color', options: ['primary', 'secondary', 'tertiary', 'success', 'warning', 'danger'], default: 'primary' },
    size: { type: 'select', label: 'Size', options: ['small', 'default', 'large'], default: 'default' },
    disabled: { type: 'boolean', label: 'Disabled', default: false },
    fill: { type: 'select', label: 'Fill', options: ['solid', 'outline', 'clear'], default: 'solid' }
  },

  'heading': {
    content: { type: 'string', label: 'Heading Text', default: 'Heading' },
    level: { type: 'select', label: 'Level', options: [1, 2, 3, 4, 5, 6], default: 2 },
    color: { type: 'string', label: 'Text Color', default: 'inherit' }
  },

  'paragraph': {
    content: { type: 'textarea', label: 'Content', default: 'This is a paragraph of text.' }
  },

  'text': {
    content: { type: 'string', label: 'Text Content', default: 'Sample text' }
  },

  'image': {
    src: { type: 'string', label: 'Image URL', default: 'https://placeholder.pics/svg/300x200' },
    alt: { type: 'string', label: 'Alt Text', default: 'Image description' },
    width: { type: 'string', label: 'Width', default: 'auto' },
    height: { type: 'string', label: 'Height', default: 'auto' }
  },

  'input': {
    placeholder: { type: 'string', label: 'Placeholder', default: 'Enter text...' },
    value: { type: 'string', label: 'Value', default: '' },
    type: { type: 'select', label: 'Input Type', options: ['text', 'email', 'password', 'number', 'tel'], default: 'text' },
    disabled: { type: 'boolean', label: 'Disabled', default: false },
    required: { type: 'boolean', label: 'Required', default: false }
  },

  'textarea': {
    placeholder: { type: 'string', label: 'Placeholder', default: 'Enter text...' },
    value: { type: 'string', label: 'Value', default: '' },
    rows: { type: 'number', label: 'Rows', default: 4 },
    disabled: { type: 'boolean', label: 'Disabled', default: false }
  },

  'select': {
    placeholder: { type: 'string', label: 'Placeholder', default: 'Select option...' },
    value: { type: 'string', label: 'Selected Value', default: '' },
    options: { type: 'array', label: 'Options', default: ['Option 1', 'Option 2', 'Option 3'] },
    disabled: { type: 'boolean', label: 'Disabled', default: false }
  },

  'checkbox': {
    label: { type: 'string', label: 'Label', default: 'Checkbox' },
    checked: { type: 'boolean', label: 'Checked', default: false },
    disabled: { type: 'boolean', label: 'Disabled', default: false }
  },

  'radio': {
    name: { type: 'string', label: 'Name', default: 'radio-group' },
    value: { type: 'string', label: 'Value', default: 'option1' },
    label: { type: 'string', label: 'Label', default: 'Radio Option' },
    checked: { type: 'boolean', label: 'Checked', default: false }
  },

  // Card Components
  'card': {
    title: { type: 'string', label: 'Title', default: 'Card Title' },
    content: { type: 'textarea', label: 'Content', default: 'Card content goes here.' },
    showHeader: { type: 'boolean', label: 'Show Header', default: true },
    showFooter: { type: 'boolean', label: 'Show Footer', default: false }
  },

  // Layout Components
  'container': {
    direction: { type: 'select', label: 'Direction', options: ['row', 'column'], default: 'column' },
    justifyContent: { type: 'select', label: 'Justify Content', options: ['start', 'center', 'end', 'between', 'around'], default: 'start' },
    alignItems: { type: 'select', label: 'Align Items', options: ['start', 'center', 'end', 'stretch'], default: 'start' },
    gap: { type: 'select', label: 'Gap', options: ['0', '1', '2', '4', '6', '8'], default: '4' }
  },

  // Navigation Components
  'home-navigation': {
    config: {
      type: 'object',
      label: 'Navigation Configuration',
      properties: {
        items: { 
          type: 'array', 
          label: 'Navigation Items',
          default: [
            { label: 'Home', path: '/', icon: 'home' },
            { label: 'Dashboard', path: '/dashboard', icon: 'grid' },
            { label: 'Profile', path: '/profile', icon: 'person' }
          ]
        }
      }
    }
  },

  // Default properties for any component
  '*': {
    tailwindClasses: { type: 'string', label: 'CSS Classes', default: '' },
    id: { type: 'string', label: 'ID', default: '' },
    hidden: { type: 'boolean', label: 'Hidden', default: false }
  }
};

export const componentPropertiesProvider: Provider = {
  provide: 'componentProperties',
  useValue: componentPropertySchemas
};
