.breadcrumb-navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875rem;
  min-height: 2.5rem;

  .breadcrumb-list {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
    flex: 1;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .breadcrumb-item {
    display: flex;
    align-items: center;
    white-space: nowrap;
    flex-shrink: 0;

    &.root {
      .breadcrumb-link {
        color: #6b7280;
        font-weight: 500;

        &:hover {
          color: #374151;
          background: #f3f4f6;
        }

        &[aria-current="page"] {
          color: #3b82f6;
          background: #eff6ff;
        }
      }
    }

    &.active {
      .breadcrumb-link,
      .breadcrumb-text.current {
        color: #3b82f6;
        font-weight: 500;
      }
    }

    .breadcrumb-separator {
      margin: 0 0.5rem;
      color: #9ca3af;
      font-size: 0.75rem;
      user-select: none;
    }

    .breadcrumb-link {
      display: flex;
      align-items: center;
      gap: 0.375rem;
      padding: 0.25rem 0.5rem;
      border-radius: 0.375rem;
      color: #6b7280;
      text-decoration: none;
      background: transparent;
      border: none;
      cursor: pointer;
      transition: all 0.15s ease;
      font-size: inherit;
      font-family: inherit;
      max-width: 200px;

      &:hover {
        color: #374151;
        background: #f3f4f6;
      }

      &:focus {
        outline: 2px solid #3b82f6;
        outline-offset: 2px;
      }

      &[aria-current="page"] {
        color: #3b82f6;
        background: #eff6ff;
        cursor: default;
      }

      .breadcrumb-icon {
        font-size: 0.875rem;
        flex-shrink: 0;
      }

      .breadcrumb-text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .breadcrumb-text.current {
      display: flex;
      align-items: center;
      gap: 0.375rem;
      padding: 0.25rem 0.5rem;
      color: #374151;
      font-weight: 500;
      max-width: 200px;

      .breadcrumb-icon {
        font-size: 0.875rem;
        flex-shrink: 0;
      }

      .breadcrumb-text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .component-info {
    display: flex;
    align-items: center;
    margin-left: 1rem;
    padding-left: 1rem;
    border-left: 1px solid #e5e7eb;
    flex-shrink: 0;

    .component-count {
      font-size: 0.75rem;
      color: #6b7280;
      background: #f3f4f6;
      padding: 0.25rem 0.5rem;
      border-radius: 0.375rem;
      font-weight: 500;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .breadcrumb-navigation {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;

    .breadcrumb-item {
      .breadcrumb-link,
      .breadcrumb-text.current {
        padding: 0.1875rem 0.375rem;
        max-width: 150px;
      }

      .breadcrumb-separator {
        margin: 0 0.375rem;
      }
    }

    .component-info {
      margin-left: 0.75rem;
      padding-left: 0.75rem;

      .component-count {
        font-size: 0.6875rem;
        padding: 0.1875rem 0.375rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .breadcrumb-navigation {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    padding: 0.5rem;

    .breadcrumb-list {
      width: 100%;
    }

    .component-info {
      margin-left: 0;
      padding-left: 0;
      border-left: none;
      border-top: 1px solid #e5e7eb;
      padding-top: 0.5rem;
      width: 100%;
    }
  }
}

// Dark mode support (if needed)
@media (prefers-color-scheme: dark) {
  .breadcrumb-navigation {
    background: #1f2937;
    border-bottom-color: #374151;

    .breadcrumb-item {
      &.root .breadcrumb-link {
        color: #9ca3af;

        &:hover {
          color: #d1d5db;
          background: #374151;
        }

        &[aria-current="page"] {
          color: #60a5fa;
          background: #1e3a8a;
        }
      }

      &.active {
        .breadcrumb-link,
        .breadcrumb-text.current {
          color: #60a5fa;
        }
      }

      .breadcrumb-separator {
        color: #6b7280;
      }

      .breadcrumb-link {
        color: #9ca3af;

        &:hover {
          color: #d1d5db;
          background: #374151;
        }

        &[aria-current="page"] {
          color: #60a5fa;
          background: #1e3a8a;
        }
      }

      .breadcrumb-text.current {
        color: #d1d5db;
      }
    }

    .component-info {
      border-left-color: #374151;

      .component-count {
        color: #9ca3af;
        background: #374151;
      }
    }
  }
}
