import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil, combineLatest } from 'rxjs';
import { ComponentStoreService } from '../../../services/component-store.service';
import { BuilderComponent } from '../../../models/builder-component.interface';

export interface BreadcrumbItem {
  id: string;
  label: string;
  type: string;
  isActive: boolean;
  isClickable: boolean;
}

@Component({
  selector: 'app-breadcrumb-navigation',
  standalone: true,
  imports: [CommonModule],
  template: `
    <nav class="breadcrumb-navigation" role="navigation" aria-label="Component hierarchy">
      <ol class="breadcrumb-list">
        <li class="breadcrumb-item root">
          <button 
            type="button"
            class="breadcrumb-link"
            (click)="navigateToRoot()"
            [attr.aria-current]="breadcrumbs.length === 0 ? 'page' : null">
            <span class="breadcrumb-icon">🏠</span>
            <span class="breadcrumb-text">Canvas</span>
          </button>
        </li>
        
        <li 
          class="breadcrumb-item"
          *ngFor="let item of breadcrumbs; let i = index; trackBy: trackByBreadcrumb"
          [class.active]="item.isActive">
          
          <span class="breadcrumb-separator" aria-hidden="true">›</span>
          
          <button 
            type="button"
            class="breadcrumb-link"
            *ngIf="item.isClickable"
            (click)="navigateToComponent(item.id)"
            [attr.aria-current]="item.isActive ? 'page' : null"
            [title]="'Navigate to ' + item.label + ' (' + item.type + ')'">
            <span class="breadcrumb-icon">{{ getComponentIcon(item.type) }}</span>
            <span class="breadcrumb-text">{{ item.label }}</span>
          </button>
          
          <span 
            class="breadcrumb-text current"
            *ngIf="!item.isClickable"
            [attr.aria-current]="'page'"
            [title]="item.label + ' (' + item.type + ')'">
            <span class="breadcrumb-icon">{{ getComponentIcon(item.type) }}</span>
            <span class="breadcrumb-text">{{ item.label }}</span>
          </span>
        </li>
      </ol>
      
      <!-- Component count indicator -->
      <div class="component-info" *ngIf="selectedComponent">
        <span class="component-count">
          {{ getChildrenCount(selectedComponent) }} 
          {{ getChildrenCount(selectedComponent) === 1 ? 'child' : 'children' }}
        </span>
      </div>
    </nav>
  `,
  styleUrls: ['./breadcrumb-navigation.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BreadcrumbNavigationComponent implements OnInit, OnDestroy {
  breadcrumbs: BreadcrumbItem[] = [];
  selectedComponent: BuilderComponent | null = null;
  components = new Map<string, BuilderComponent>();

  private destroy$ = new Subject<void>();

  constructor(
    private componentStore: ComponentStoreService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    // Subscribe to component changes and selection changes
    combineLatest([
      this.componentStore.componentsSubject.asObservable(),
      this.componentStore.getSelectedComponent()
    ]).pipe(takeUntil(this.destroy$))
      .subscribe(([components, selectedComponent]) => {
        this.components = components;
        this.selectedComponent = selectedComponent || null;
        this.updateBreadcrumbs();
        this.cdr.markForCheck();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Update breadcrumb navigation based on selected component
   */
  private updateBreadcrumbs(): void {
    this.breadcrumbs = [];

    if (!this.selectedComponent) {
      return;
    }

    // Build hierarchy path from root to selected component
    const path = this.buildComponentPath(this.selectedComponent.id);
    
    // Convert path to breadcrumb items
    this.breadcrumbs = path.map((component, index) => ({
      id: component.id,
      label: this.getDisplayLabel(component),
      type: component.type,
      isActive: index === path.length - 1,
      isClickable: index < path.length - 1 // Last item is not clickable
    }));
  }

  /**
   * Build component path from root to target component
   */
  private buildComponentPath(componentId: string): BuilderComponent[] {
    const path: BuilderComponent[] = [];
    let currentComponent = this.components.get(componentId);

    // Build path from target to root
    while (currentComponent) {
      path.unshift(currentComponent);
      
      if (currentComponent.parentId) {
        currentComponent = this.components.get(currentComponent.parentId);
      } else {
        break;
      }
    }

    return path;
  }

  /**
   * Get display label for component
   */
  private getDisplayLabel(component: BuilderComponent): string {
    // Use custom label if available, otherwise use type
    if (component.label && component.label !== component.type) {
      return component.label;
    }

    // Format type name for display
    return this.formatTypeName(component.type);
  }

  /**
   * Format type name for better display
   */
  private formatTypeName(type: string): string {
    return type
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .trim();
  }

  /**
   * Get icon for component type
   */
  getComponentIcon(type: string): string {
    const iconMap: { [key: string]: string } = {
      'container': '📦',
      'text': '📝',
      'button': '🔘',
      'image': '🖼️',
      'input': '📝',
      'form': '📋',
      'header': '📄',
      'footer': '📄',
      'section': '📑',
      'div': '📦',
      'span': '📝',
      'card': '🃏',
      'modal': '🪟',
      'dropdown': '📋',
      'navigation': '🧭',
      'sidebar': '📄',
      'grid': '⚏',
      'flex': '📐',
      'list': '📋',
      'table': '📊'
    };

    return iconMap[type.toLowerCase()] || '🔧';
  }

  /**
   * Navigate to root (clear selection)
   */
  navigateToRoot(): void {
    this.componentStore.selectComponent(null);
  }

  /**
   * Navigate to specific component
   */
  navigateToComponent(componentId: string): void {
    this.componentStore.selectComponent(componentId);
  }

  /**
   * Get children count for component
   */
  getChildrenCount(component: BuilderComponent): number {
    return component.children?.length || 0;
  }

  /**
   * Track by function for breadcrumb items
   */
  trackByBreadcrumb(index: number, item: BreadcrumbItem): string {
    return item.id;
  }
}
