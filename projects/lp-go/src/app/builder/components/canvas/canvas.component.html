<div class="canvas-container">
  <!-- Breadcrumb Navigation -->
  <app-breadcrumb-navigation></app-breadcrumb-navigation>

  <!-- Multi-Selection Toolbar -->
  <div class="multi-selection-toolbar" *ngIf="componentStore.getSelectedComponentIds().length > 1">
    <div class="toolbar-content">
      <span class="selection-count">
        {{ componentStore.getSelectedComponentIds().length }} components selected
      </span>
      
      <div class="toolbar-actions">
        <!-- Alignment Actions -->
        <div class="action-group">
          <button 
            class="toolbar-button" 
            (click)="alignSelectedComponents(AlignmentType.LEFT)"
            title="Align Left"
            aria-label="Align selected components to the left">
            <span class="icon">⫷</span>
          </button>
          <button 
            class="toolbar-button" 
            (click)="alignSelectedComponents(AlignmentType.CENTER_HORIZONTAL)"
            title="Align Center"
            aria-label="Align selected components to center">
            <span class="icon">⫸</span>
          </button>
          <button 
            class="toolbar-button" 
            (click)="alignSelectedComponents(AlignmentType.RIGHT)"
            title="Align Right"
            aria-label="Align selected components to the right">
            <span class="icon">⫹</span>
          </button>
          <button 
            class="toolbar-button" 
            (click)="alignSelectedComponents(AlignmentType.TOP)"
            title="Align Top"
            aria-label="Align selected components to the top">
            <span class="icon">⫰</span>
          </button>
          <button 
            class="toolbar-button" 
            (click)="alignSelectedComponents(AlignmentType.CENTER_VERTICAL)"
            title="Align Middle"
            aria-label="Align selected components to middle">
            <span class="icon">⫱</span>
          </button>
          <button 
            class="toolbar-button" 
            (click)="alignSelectedComponents(AlignmentType.BOTTOM)"
            title="Align Bottom"
            aria-label="Align selected components to the bottom">
            <span class="icon">⫲</span>
          </button>
        </div>

        <div class="toolbar-separator"></div>

        <!-- Distribution Actions -->
        <div class="action-group">
          <button 
            class="toolbar-button" 
            (click)="distributeSelectedComponents(DistributionType.HORIZONTAL)"
            title="Distribute Horizontally"
            aria-label="Distribute selected components horizontally">
            <span class="icon">⟷</span>
          </button>
          <button 
            class="toolbar-button" 
            (click)="distributeSelectedComponents(DistributionType.VERTICAL)"
            title="Distribute Vertically"
            aria-label="Distribute selected components vertically">
            <span class="icon">⟶</span>
          </button>
        </div>

        <div class="toolbar-separator"></div>

        <!-- Copy/Paste Actions -->
        <div class="action-group">
          <button 
            class="toolbar-button" 
            (click)="copySelectedComponents()"
            title="Copy (Ctrl+C)"
            aria-label="Copy selected components">
            <span class="icon">📋</span>
          </button>
          <button 
            class="toolbar-button" 
            (click)="cutSelectedComponents()"
            title="Cut (Ctrl+X)"
            aria-label="Cut selected components">
            <span class="icon">✂️</span>
          </button>
          <button 
            class="toolbar-button" 
            (click)="deleteSelectedComponents()"
            title="Delete (Del)"
            aria-label="Delete selected components">
            <span class="icon">🗑️</span>
          </button>
        </div>

        <div class="toolbar-separator"></div>

        <!-- Clear Selection -->
        <button 
          class="toolbar-button clear-selection" 
          (click)="clearSelection()"
          title="Clear Selection (Esc)"
          aria-label="Clear selection">
          <span class="icon">✕</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Canvas Viewport -->
  <div class="canvas-viewport"
       [style.transform]="'scale(' + (canvasState?.zoom || 1) + ')'"
       [style.transform-origin]="'top left'">

    <!-- Grid Overlay -->
    <app-grid-overlay
      [zoom]="canvasState.zoom"
      *ngIf="canvasState">
    </app-grid-overlay>

    <!-- Canvas Content -->
    <!-- Added items-stretch to ensure flex children can take full width -->
    <!-- Connected canvas root to all container drop lists -->
    <div class="canvas-content" [ngClass]="canvasClasses" cdkDropList id="canvas-root" [cdkDropListData]="rootComponents"
      [cdkDropListConnectedTo]="componentStore.getAllContainerIdsSync()"
      (cdkDropListDropped)="onDrop($event)" (cdkDropListEntered)="onDragEnter($event)"
      (cdkDropListExited)="onDragExit($event)" [class.drop-zone-active]="isDropTarget" (click)="onCanvasClick($event)"
      (mousedown)="onCanvasMouseDown($event)"
      cdkDropListOrientation="vertical" [cdkDropListSortingDisabled]="false">
      
      <!-- Drag Selection Rectangle -->
      <div 
        *ngIf="isDragSelecting && dragSelectionRect" 
        class="drag-selection-rectangle"
        [style.left.px]="dragSelectionRect.x"
        [style.top.px]="dragSelectionRect.y"
        [style.width.px]="dragSelectionRect.width"
        [style.height.px]="dragSelectionRect.height">
      </div>

    <!-- Root Components -->
    <ng-container *ngFor="let component of rootComponents; let i = index; trackBy: trackById">
      <!-- Render containers directly -->
      <ng-container [ngSwitch]="component.type">

        <!-- Container Component -->

        <!-- The actual drop zone - No frills, just the basics -->
        <!-- Connected each container to canvas root and all other containers -->
        <div *ngSwitchCase="'container'" cdkDropList [id]="'container-' + component.id"
          [cdkDropListData]="getChildrenOf(component.id)"
          [cdkDropListConnectedTo]="['canvas-root'].concat(componentStore.getAllContainerIdsSync())"
          (cdkDropListDropped)="onDropInContainer($event, component.id)"
          [ngClass]="component.properties.tailwindClasses"
          (click)="selectComponent(component.id, $event)"
          (mouseenter)="onComponentHover(component.id, true)"
          (mouseleave)="onComponentHover(component.id, false)"
          [class.selected]="isComponentSelected(component.id)"
          [class.multi-selected]="componentStore.isComponentSelected(component.id) && componentStore.getSelectedComponentIds().length > 1"
          [class.hovered]="isComponentHovered(component.id)"
          [attr.data-component-type]="component.type"
          [attr.data-component-id]="component.id"
          [attr.aria-label]="'Container: ' + getComponentDisplayName(component)"
          [attr.role]="'button'"
          [attr.tabindex]="0"
          class="component-wrapper"
          cdkDrag [cdkDragData]="{source: 'canvas', componentId: component.id}"
          (cdkDragStarted)="onDragStarted()" (cdkDragEnded)="onDragEnded()"
        >

          <!-- Container Children -->
          <ng-container *ngFor="let child of getChildrenOf(component.id); trackBy: trackById">
            <!-- Wrapper Div for Interaction and Styling -->
            <div class="component-wrapper"
                 (click)="selectComponent(child.id, $event)"
                 (mouseenter)="onComponentHover(child.id, true)"
                 (mouseleave)="onComponentHover(child.id, false)"
                 [class.selected]="isComponentSelected(child.id)"
                 [class.multi-selected]="componentStore.isComponentSelected(child.id) && componentStore.getSelectedComponentIds().length > 1"
                 [class.hovered]="isComponentHovered(child.id)"
                 [attr.data-component-type]="child.type"
                 [attr.data-component-id]="child.id"
                 [attr.aria-label]="getComponentDisplayName(child)"
                 [attr.role]="'button'"
                 [attr.tabindex]="0"
                 cdkDrag [cdkDragData]="{source: 'container', componentId: child.id}"
                 (cdkDragStarted)="onDragStarted()" (cdkDragEnded)="onDragEnded()"
            >
              <app-dynamic-component [component]="child"></app-dynamic-component>
            </div>
          </ng-container>

          <!-- Empty Container Message -->
          <div *ngIf="getChildrenOf(component.id).length === 0"
            style="display: flex; justify-content: center; align-items: center; height: 80px; border: 2px dashed #cbd5e0; border-radius: 4px; background: #f7fafc;">
            <div style="text-align: center; color: #4a5568;">
              <div style="font-size: 24px; margin-bottom: 8px;">+</div>
              <p>Drop components here ({{'{{'}} getChildrenOf(component.id).length {{'}}'}} items)</p>
            </div>
          </div>
        </div>

        <!-- Other Components (Root Level) -->
        <!-- Wrapper Div for Interaction and Styling -->
        <div *ngSwitchDefault
             class="component-wrapper"
             (click)="selectComponent(component.id, $event)"
             (mouseenter)="onComponentHover(component.id, true)"
             (mouseleave)="onComponentHover(component.id, false)"
             [class.selected]="isComponentSelected(component.id)"
             [class.multi-selected]="componentStore.isComponentSelected(component.id) && componentStore.getSelectedComponentIds().length > 1"
             [class.hovered]="isComponentHovered(component.id)"
             [attr.data-component-type]="component.type"
             [attr.data-component-id]="component.id"
             [attr.aria-label]="getComponentDisplayName(component)"
             [attr.role]="'button'"
             [attr.tabindex]="0"
             cdkDrag [cdkDragData]="{source: 'canvas', componentId: component.id}"
             (cdkDragStarted)="onDragStarted()" (cdkDragEnded)="onDragEnded()"
        >
          <app-dynamic-component [component]="component"></app-dynamic-component>
        </div>
      </ng-container>
    </ng-container>

      <!-- Empty state -->
      <div class="empty-canvas" *ngIf="rootComponents.length === 0">
        <div class="empty-canvas-message">
          <h3>Drag components here to build your page</h3>
          <p>Use the component palette on the left to add components</p>
        </div>
      </div>
    </div>

    <!-- Drop indicator while dragging - only if not over a container -->
    <div class="drop-indicator" *ngIf="isDropTarget">
      <span class="indicator-message">Drop to add to canvas</span>
    </div>
  </div>
</div>
