import { ComponentFixture } from '@angular/core/testing';
import { DebugElement } from '@angular/core';
import { By } from '@angular/platform-browser';
import { CanvasComponent } from './canvas.component';
import { ComponentStoreService } from '../../services/component-store.service';
import { ViewportService } from '../../services/viewport.service';
import { 
  ComponentTestHarness,
  MockComponentStoreService,
  MockAppConfigFactory,
  setupComponentTest,
  PerformanceTestUtils
} from '../../../../testing';
import { BuilderComponent } from '../../models/builder-component.interface';

describe('CanvasComponent', () => {
  let component: CanvasComponent;
  let fixture: ComponentFixture<CanvasComponent>;
  let componentStoreService: jasmine.SpyObj<ComponentStoreService>;
  let viewportService: jasmine.SpyObj<ViewportService>;

  beforeEach(async () => {
    const componentStoreSpy = jasmine.createSpyObj('ComponentStoreService', [
      'addComponent', 'updateComponent', 'removeComponent', 'selectComponent', 'getComponent'
    ]);
    const viewportSpy = jasmine.createSpyObj('ViewportService', [
      'setZoom', 'getZoom', 'setViewport', 'getCurrentViewport'
    ]);

    fixture = await setupComponentTest(CanvasComponent, {
      providers: [
        { provide: ComponentStoreService, useValue: componentStoreSpy },
        { provide: ViewportService, useValue: viewportSpy }
      ]
    });

    component = fixture.componentInstance;
    componentStoreService = fixture.debugElement.injector.get(ComponentStoreService) as jasmine.SpyObj<ComponentStoreService>;
    viewportService = fixture.debugElement.injector.get(ViewportService) as jasmine.SpyObj<ViewportService>;

    // Setup default spy returns
    viewportService.getZoom.and.returnValue(1);
    viewportService.getCurrentViewport.and.returnValue('desktop');
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default properties', async () => {
      await ComponentTestHarness.detectChanges(fixture);
      
      expect(component.zoom).toBe(1);
      expect(component.gridEnabled).toBe(true);
      expect(component.snapToGrid).toBe(true);
      expect(component.gridSize).toBe(10);
    });

    it('should render canvas container', async () => {
      await ComponentTestHarness.detectChanges(fixture);
      
      const canvasContainer = ComponentTestHarness.getByTestId(fixture, 'canvas-container');
      expect(canvasContainer).toBeTruthy();
    });

    it('should render grid overlay when enabled', async () => {
      component.gridEnabled = true;
      await ComponentTestHarness.detectChanges(fixture);
      
      const gridOverlay = ComponentTestHarness.getByTestId(fixture, 'grid-overlay');
      expect(gridOverlay).toBeTruthy();
    });
  });

  describe('Component Rendering', () => {
    it('should render components on canvas', async () => {
      const mockComponents = [
        MockAppConfigFactory.createMockBuilderComponent({
          displayName: 'Test Component 1',
          position: { x: 100, y: 100, width: 200, height: 100, zIndex: 1 }
        }),
        MockAppConfigFactory.createMockBuilderComponent({
          displayName: 'Test Component 2',
          position: { x: 300, y: 200, width: 150, height: 80, zIndex: 2 }
        })
      ];

      component.components = mockComponents;
      await ComponentTestHarness.detectChanges(fixture);

      const renderedComponents = ComponentTestHarness.getAllByTestId(fixture, 'canvas-component');
      expect(renderedComponents.length).toBe(2);
    });

    it('should apply correct positioning styles', async () => {
      const mockComponent = MockAppConfigFactory.createMockBuilderComponent({
        position: { x: 150, y: 200, width: 300, height: 150, zIndex: 5 }
      });

      component.components = [mockComponent];
      await ComponentTestHarness.detectChanges(fixture);

      const componentElement = ComponentTestHarness.getByTestId(fixture, 'canvas-component');
      const styles = ComponentTestHarness.getComputedStyle(fixture, 'canvas-component');
      
      expect(styles.position).toBe('absolute');
      expect(styles.left).toBe('150px');
      expect(styles.top).toBe('200px');
      expect(styles.width).toBe('300px');
      expect(styles.height).toBe('150px');
      expect(styles.zIndex).toBe('5');
    });

    it('should handle zoom transformations', async () => {
      component.zoom = 1.5;
      const mockComponent = MockAppConfigFactory.createMockBuilderComponent();
      component.components = [mockComponent];
      
      await ComponentTestHarness.detectChanges(fixture);

      const canvasContainer = ComponentTestHarness.getByTestId(fixture, 'canvas-container');
      const styles = ComponentTestHarness.getComputedStyle(fixture, 'canvas-container');
      
      expect(styles.transform).toContain('scale(1.5)');
    });
  });

  describe('Component Selection', () => {
    it('should select component on click', async () => {
      const mockComponent = MockAppConfigFactory.createMockBuilderComponent();
      component.components = [mockComponent];
      componentStoreService.selectComponent.and.returnValue(Promise.resolve(mockComponent.id));

      await ComponentTestHarness.detectChanges(fixture);

      await ComponentTestHarness.clickByTestId(fixture, 'canvas-component');

      expect(componentStoreService.selectComponent).toHaveBeenCalledWith(mockComponent.id);
    });

    it('should highlight selected component', async () => {
      const mockComponent = MockAppConfigFactory.createMockBuilderComponent();
      component.components = [mockComponent];
      component.selectedComponentId = mockComponent.id;

      await ComponentTestHarness.detectChanges(fixture);

      const hasSelectedClass = ComponentTestHarness.hasClass(fixture, 'canvas-component', 'selected');
      expect(hasSelectedClass).toBe(true);
    });

    it('should support multi-selection with Ctrl+Click', async () => {
      const mockComponents = [
        MockAppConfigFactory.createMockBuilderComponent(),
        MockAppConfigFactory.createMockBuilderComponent()
      ];
      component.components = mockComponents;
      component.multiSelectEnabled = true;

      await ComponentTestHarness.detectChanges(fixture);

      const firstComponent = ComponentTestHarness.getAllByTestId(fixture, 'canvas-component')[0];
      const secondComponent = ComponentTestHarness.getAllByTestId(fixture, 'canvas-component')[1];

      // Simulate Ctrl+Click
      ComponentTestHarness.dispatchKeyboardEvent(fixture, firstComponent, 'click', 17); // Ctrl key
      await ComponentTestHarness.clickElement(fixture, firstComponent);
      
      ComponentTestHarness.dispatchKeyboardEvent(fixture, secondComponent, 'click', 17); // Ctrl key
      await ComponentTestHarness.clickElement(fixture, secondComponent);

      expect(component.selectedComponentIds.length).toBe(2);
    });
  });

  describe('Drag and Drop Operations', () => {
    it('should handle component drag start', async () => {
      const mockComponent = MockAppConfigFactory.createMockBuilderComponent();
      component.components = [mockComponent];

      await ComponentTestHarness.detectChanges(fixture);

      const componentElement = ComponentTestHarness.getByTestId(fixture, 'canvas-component');
      ComponentTestHarness.dispatchDragEvent(fixture, componentElement, componentElement, 'dragstart');

      expect(component.draggedComponent).toBe(mockComponent);
    });

    it('should update component position on drop', async () => {
      const mockComponent = MockAppConfigFactory.createMockBuilderComponent({
        position: { x: 100, y: 100, width: 200, height: 100, zIndex: 1 }
      });
      component.components = [mockComponent];
      componentStoreService.updateComponent.and.returnValue(Promise.resolve(mockComponent));

      await ComponentTestHarness.detectChanges(fixture);

      // Simulate drag and drop to new position
      const newPosition = { x: 200, y: 150 };
      component.onComponentDrop(mockComponent, newPosition);

      expect(componentStoreService.updateComponent).toHaveBeenCalledWith(
        mockComponent.id,
        jasmine.objectContaining({
          position: jasmine.objectContaining({
            x: newPosition.x,
            y: newPosition.y
          })
        })
      );
    });

    it('should snap to grid when enabled', async () => {
      component.snapToGrid = true;
      component.gridSize = 20;

      const mockComponent = MockAppConfigFactory.createMockBuilderComponent();
      const dropPosition = { x: 157, y: 183 }; // Should snap to 160, 180

      const snappedPosition = component.snapPositionToGrid(dropPosition);

      expect(snappedPosition.x).toBe(160);
      expect(snappedPosition.y).toBe(180);
    });

    it('should handle external component drop from palette', async () => {
      const paletteComponent = {
        type: 'text',
        displayName: 'New Text Component',
        properties: { text: 'Hello World' }
      };

      componentStoreService.addComponent.and.returnValue(Promise.resolve(
        MockAppConfigFactory.createMockBuilderComponent(paletteComponent)
      ));

      await ComponentTestHarness.detectChanges(fixture);

      const canvasContainer = ComponentTestHarness.getByTestId(fixture, 'canvas-container');
      
      // Simulate drop event with palette data
      const dropEvent = new DragEvent('drop', {
        dataTransfer: new DataTransfer()
      });
      dropEvent.dataTransfer?.setData('application/json', JSON.stringify(paletteComponent));
      
      canvasContainer.nativeElement.dispatchEvent(dropEvent);

      expect(componentStoreService.addComponent).toHaveBeenCalled();
    });
  });

  describe('Zoom and Pan Operations', () => {
    it('should handle zoom in', () => {
      const initialZoom = component.zoom;
      component.zoomIn();
      
      expect(component.zoom).toBeGreaterThan(initialZoom);
      expect(viewportService.setZoom).toHaveBeenCalledWith(component.zoom);
    });

    it('should handle zoom out', () => {
      component.zoom = 2;
      const initialZoom = component.zoom;
      component.zoomOut();
      
      expect(component.zoom).toBeLessThan(initialZoom);
      expect(viewportService.setZoom).toHaveBeenCalledWith(component.zoom);
    });

    it('should respect zoom limits', () => {
      component.zoom = 0.1; // Below minimum
      component.zoomOut();
      expect(component.zoom).toBe(0.25); // Should be clamped to minimum

      component.zoom = 5; // Above maximum
      component.zoomIn();
      expect(component.zoom).toBe(3); // Should be clamped to maximum
    });

    it('should handle mouse wheel zoom', async () => {
      await ComponentTestHarness.detectChanges(fixture);

      const canvasContainer = ComponentTestHarness.getByTestId(fixture, 'canvas-container');
      const wheelEvent = new WheelEvent('wheel', {
        deltaY: -100, // Zoom in
        ctrlKey: true,
        bubbles: true
      });

      const initialZoom = component.zoom;
      canvasContainer.nativeElement.dispatchEvent(wheelEvent);

      expect(component.zoom).toBeGreaterThan(initialZoom);
    });
  });

  describe('Grid Operations', () => {
    it('should toggle grid visibility', () => {
      const initialState = component.gridEnabled;
      component.toggleGrid();
      
      expect(component.gridEnabled).toBe(!initialState);
    });

    it('should update grid size', async () => {
      component.setGridSize(25);
      await ComponentTestHarness.detectChanges(fixture);
      
      expect(component.gridSize).toBe(25);
      
      const gridOverlay = ComponentTestHarness.getByTestId(fixture, 'grid-overlay');
      if (gridOverlay) {
        const styles = ComponentTestHarness.getComputedStyle(fixture, 'grid-overlay');
        expect(styles.backgroundSize).toContain('25px');
      }
    });

    it('should toggle snap to grid', () => {
      const initialState = component.snapToGrid;
      component.toggleSnapToGrid();
      
      expect(component.snapToGrid).toBe(!initialState);
    });
  });

  describe('Keyboard Navigation', () => {
    it('should handle arrow key movement', async () => {
      const mockComponent = MockAppConfigFactory.createMockBuilderComponent({
        position: { x: 100, y: 100, width: 200, height: 100, zIndex: 1 }
      });
      component.components = [mockComponent];
      component.selectedComponentId = mockComponent.id;
      componentStoreService.updateComponent.and.returnValue(Promise.resolve(mockComponent));

      await ComponentTestHarness.detectChanges(fixture);

      const canvasContainer = ComponentTestHarness.getByTestId(fixture, 'canvas-container');
      
      // Simulate right arrow key
      ComponentTestHarness.dispatchKeyboardEvent(fixture, canvasContainer, 'keydown', 39, 'ArrowRight');

      expect(componentStoreService.updateComponent).toHaveBeenCalledWith(
        mockComponent.id,
        jasmine.objectContaining({
          position: jasmine.objectContaining({ x: 110 }) // Moved 10px right
        })
      );
    });

    it('should handle delete key', async () => {
      const mockComponent = MockAppConfigFactory.createMockBuilderComponent();
      component.selectedComponentId = mockComponent.id;
      componentStoreService.removeComponent.and.returnValue(Promise.resolve(true));

      await ComponentTestHarness.detectChanges(fixture);

      const canvasContainer = ComponentTestHarness.getByTestId(fixture, 'canvas-container');
      ComponentTestHarness.dispatchKeyboardEvent(fixture, canvasContainer, 'keydown', 46, 'Delete');

      expect(componentStoreService.removeComponent).toHaveBeenCalledWith(mockComponent.id);
    });

    it('should handle copy/paste operations', async () => {
      const mockComponent = MockAppConfigFactory.createMockBuilderComponent();
      component.selectedComponentId = mockComponent.id;
      component.components = [mockComponent];

      await ComponentTestHarness.detectChanges(fixture);

      const canvasContainer = ComponentTestHarness.getByTestId(fixture, 'canvas-container');
      
      // Copy (Ctrl+C)
      const copyEvent = new KeyboardEvent('keydown', { 
        key: 'c', 
        ctrlKey: true, 
        bubbles: true 
      });
      canvasContainer.nativeElement.dispatchEvent(copyEvent);

      expect(component.clipboardComponent).toEqual(mockComponent);

      // Paste (Ctrl+V)
      componentStoreService.addComponent.and.returnValue(Promise.resolve(
        MockAppConfigFactory.createMockBuilderComponent()
      ));

      const pasteEvent = new KeyboardEvent('keydown', { 
        key: 'v', 
        ctrlKey: true, 
        bubbles: true 
      });
      canvasContainer.nativeElement.dispatchEvent(pasteEvent);

      expect(componentStoreService.addComponent).toHaveBeenCalled();
    });
  });

  describe('Performance Tests', () => {
    it('should render many components efficiently', async () => {
      const componentCount = 200;
      const mockComponents = Array.from({ length: componentCount }, (_, i) => 
        MockAppConfigFactory.createMockBuilderComponent({
          displayName: `Component ${i}`,
          position: {
            x: (i % 20) * 50,
            y: Math.floor(i / 20) * 50,
            width: 40,
            height: 30,
            zIndex: i
          }
        })
      );

      const { duration } = await PerformanceTestUtils.measureExecutionTime(async () => {
        component.components = mockComponents;
        await ComponentTestHarness.detectChanges(fixture);
      }, `Rendering ${componentCount} components`);

      expect(duration).toBeLessThan(1000); // Should render within 1 second
      
      const renderedComponents = ComponentTestHarness.getAllByTestId(fixture, 'canvas-component');
      expect(renderedComponents.length).toBe(componentCount);
    });

    it('should handle rapid zoom changes efficiently', async () => {
      await ComponentTestHarness.detectChanges(fixture);

      const { duration } = await PerformanceTestUtils.measureExecutionTime(async () => {
        // Perform 50 zoom operations
        for (let i = 0; i < 50; i++) {
          component.zoom = 0.5 + (i * 0.05);
          await ComponentTestHarness.detectChanges(fixture);
        }
      }, 'Rapid zoom changes');

      expect(duration).toBeLessThan(2000); // Should complete within 2 seconds
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', async () => {
      await ComponentTestHarness.detectChanges(fixture);

      const canvasContainer = ComponentTestHarness.getByTestId(fixture, 'canvas-container');
      expect(canvasContainer.nativeElement.getAttribute('aria-label')).toBeTruthy();
      expect(canvasContainer.nativeElement.getAttribute('role')).toBe('application');
    });

    it('should support keyboard navigation', async () => {
      await ComponentTestHarness.detectChanges(fixture);

      const canvasContainer = ComponentTestHarness.getByTestId(fixture, 'canvas-container');
      expect(canvasContainer.nativeElement.getAttribute('tabindex')).toBe('0');
    });

    it('should announce component selection to screen readers', async () => {
      const mockComponent = MockAppConfigFactory.createMockBuilderComponent({
        displayName: 'Test Component'
      });
      component.components = [mockComponent];

      await ComponentTestHarness.detectChanges(fixture);

      component.selectComponent(mockComponent.id);

      const announcement = fixture.debugElement.query(By.css('[aria-live="polite"]'));
      expect(announcement).toBeTruthy();
    });
  });
});