import { Component, Input, OnInit, OnDestroy, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';
import { ComponentStoreService } from '../../../services/component-store.service';
import { GridConfig } from '../../../models/builder-component.interface';

@Component({
  selector: 'app-grid-overlay',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div 
      class="grid-overlay"
      [class.visible]="gridConfig.enabled"
      [style.--grid-size]="gridConfig.size + 'px'"
      [style.--grid-color]="gridConfig.color"
      [style.--grid-opacity]="gridConfig.opacity"
      [style.transform]="'scale(' + zoom + ')'">
      
      <!-- Grid pattern -->
      <div class="grid-pattern"></div>
      
      <!-- Snap indicators (shown when dragging) -->
      <div class="snap-indicators" *ngIf="showSnapIndicators">
        <div 
          class="snap-line vertical" 
          *ngFor="let line of verticalSnapLines"
          [style.left]="line + 'px'">
        </div>
        <div 
          class="snap-line horizontal" 
          *ngFor="let line of horizontalSnapLines"
          [style.top]="line + 'px'">
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./grid-overlay.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class GridOverlayComponent implements OnInit, OnDestroy {
  @Input() zoom: number = 1;
  @Input() showSnapIndicators: boolean = false;
  @Input() verticalSnapLines: number[] = [];
  @Input() horizontalSnapLines: number[] = [];

  gridConfig: GridConfig = {
    enabled: false,
    size: 20,
    color: '#e5e7eb',
    opacity: 0.5,
    snapToGrid: false
  };

  private destroy$ = new Subject<void>();

  constructor(private componentStore: ComponentStoreService) {}

  ngOnInit(): void {
    // Subscribe to canvas state changes
    this.componentStore.getCanvasState()
      .pipe(takeUntil(this.destroy$))
      .subscribe(canvasState => {
        this.gridConfig = canvasState.gridConfig;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Snap coordinates to grid
   */
  snapToGrid(x: number, y: number): { x: number; y: number } {
    if (!this.gridConfig.snapToGrid) {
      return { x, y };
    }

    const gridSize = this.gridConfig.size;
    return {
      x: Math.round(x / gridSize) * gridSize,
      y: Math.round(y / gridSize) * gridSize
    };
  }

  /**
   * Get nearest grid lines for snapping
   */
  getNearestGridLines(x: number, y: number, threshold: number = 10): { x: number | null; y: number | null } {
    if (!this.gridConfig.enabled) {
      return { x: null, y: null };
    }

    const gridSize = this.gridConfig.size;
    const snapThreshold = threshold / this.zoom;

    // Find nearest vertical grid line
    const nearestVertical = Math.round(x / gridSize) * gridSize;
    const verticalDistance = Math.abs(x - nearestVertical);
    const snapX = verticalDistance <= snapThreshold ? nearestVertical : null;

    // Find nearest horizontal grid line
    const nearestHorizontal = Math.round(y / gridSize) * gridSize;
    const horizontalDistance = Math.abs(y - nearestHorizontal);
    const snapY = horizontalDistance <= snapThreshold ? nearestHorizontal : null;

    return { x: snapX, y: snapY };
  }

  /**
   * Check if coordinates are near grid intersection
   */
  isNearGridIntersection(x: number, y: number, threshold: number = 10): boolean {
    const snapLines = this.getNearestGridLines(x, y, threshold);
    return snapLines.x !== null && snapLines.y !== null;
  }
}
