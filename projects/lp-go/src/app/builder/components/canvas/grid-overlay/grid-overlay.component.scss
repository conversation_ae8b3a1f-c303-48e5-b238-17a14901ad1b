.grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.2s ease;
  transform-origin: top left;

  &.visible {
    opacity: 1;
  }

  .grid-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
      linear-gradient(to right, var(--grid-color) 1px, transparent 1px),
      linear-gradient(to bottom, var(--grid-color) 1px, transparent 1px);
    background-size: var(--grid-size) var(--grid-size);
    opacity: var(--grid-opacity);
  }

  .snap-indicators {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;

    .snap-line {
      position: absolute;
      background-color: #3b82f6;
      opacity: 0.8;
      animation: snapPulse 0.3s ease-in-out;

      &.vertical {
        width: 1px;
        height: 100%;
        top: 0;
      }

      &.horizontal {
        width: 100%;
        height: 1px;
        left: 0;
      }
    }
  }
}

@keyframes snapPulse {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 0.8;
    transform: scale(1);
  }
}

// Grid size variations for different zoom levels
.grid-overlay {
  // Small grid for high zoom levels
  &[style*="scale(2)"], &[style*="scale(3)"] {
    .grid-pattern {
      background-size: calc(var(--grid-size) * 0.5) calc(var(--grid-size) * 0.5);
    }
  }

  // Large grid for low zoom levels
  &[style*="scale(0.25)"], &[style*="scale(0.5)"] {
    .grid-pattern {
      background-size: calc(var(--grid-size) * 2) calc(var(--grid-size) * 2);
    }
  }
}

// Responsive grid adjustments
@media (max-width: 768px) {
  .grid-overlay {
    .grid-pattern {
      background-size: calc(var(--grid-size) * 1.5) calc(var(--grid-size) * 1.5);
    }
  }
}

// High DPI display adjustments
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .grid-overlay {
    .grid-pattern {
      background-image: 
        linear-gradient(to right, var(--grid-color) 0.5px, transparent 0.5px),
        linear-gradient(to bottom, var(--grid-color) 0.5px, transparent 0.5px);
    }

    .snap-line {
      &.vertical {
        width: 0.5px;
      }

      &.horizontal {
        height: 0.5px;
      }
    }
  }
}
