import { Component, OnInit, OnD<PERSON>roy, ChangeDetectorRef, HostListener } from '@angular/core';
import { CommonModule, NgClass, NgIf, NgFor } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';
import { 
  DragDropModule, 
  CdkDrag, 
  CdkDropList, 
  CdkDragDrop, 
  CdkDragEnter, 
  CdkDragExit
} from '@angular/cdk/drag-drop';

import { ComponentStoreService } from '../../services/component-store.service';
import { ClipboardService } from '../../services/clipboard.service';
import { BuilderComponent, PageConfig, CanvasState, SelectionState, AlignmentType, DistributionType } from '../../models/builder-component.interface';
import { DynamicComponentComponent } from '../dynamic-component/dynamic-component.component';
import { GridOverlayComponent } from './grid-overlay/grid-overlay.component';
import { BreadcrumbNavigationComponent } from './breadcrumb-navigation/breadcrumb-navigation.component';

@Component({
  selector: 'app-canvas',
  templateUrl: './canvas.component.html',
  styleUrls: ['./canvas.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NgClass,
    NgIf,
    NgFor,
    DragDropModule,
    CdkDrag,
    CdkDropList,
    DynamicComponentComponent,
    GridOverlayComponent,
    BreadcrumbNavigationComponent
  ]
})
export class CanvasComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  // Expose enums for template usage
  AlignmentType = AlignmentType;
  DistributionType = DistributionType;
  
  // Current page being edited
  currentPage: PageConfig | null = null;
  
  // Root components to display (using the interface type)
  rootComponents: BuilderComponent[] = []; 
  
  // Track selected component ID
  selectedComponentId: string | null = null;
  
  // Track if a component is being dragged
  isDragging = false;
  
  // Track if canvas is a drop target
  isDropTarget = false;
  
  // Track which container is a drop target
  isDropTargetFor: string | null = null;

  // Canvas state management
  canvasState: CanvasState | null = null;
  selectionState: SelectionState | null = null;

  // Multi-selection and drag selection properties
  isDragSelecting = false;
  dragSelectionStart: { x: number; y: number } | null = null;
  dragSelectionRect: { x: number; y: number; width: number; height: number } | null = null;

  // Get canvas classes from page properties
  get canvasClasses(): string {
    // Default classes for the canvas
    const defaultClasses = 'bg-gray-100';
    
    // Get tailwind classes from current page
    let pageClasses = '';
    if (this.currentPage?.properties?.tailwindClasses) {
      pageClasses = this.currentPage.properties.tailwindClasses;
    } else if (this.currentPage?.styles?.backgroundColor) {
      pageClasses = this.currentPage.styles.backgroundColor;
    }
    
    // Combine default and page classes
    return `${defaultClasses} ${pageClasses}`;
  }
  
  constructor(
    public componentStore: ComponentStoreService,
    private clipboardService: ClipboardService,
    private cdr: ChangeDetectorRef
  ) {}
  
  ngOnInit(): void {
    console.log('Canvas component initialized');
    
    // Subscribe to active page changes
    this.componentStore.getActivePage().pipe(
      takeUntil(this.destroy$)
    ).subscribe(page => {
      console.log('Active page changed:', page);
      this.currentPage = page;
      this.cdr.detectChanges();
    });
    
    // Subscribe to root components
    this.componentStore.getRootComponents().pipe(
      takeUntil(this.destroy$)
    ).subscribe(components => {
      console.log('CANVAS: Received root components update from store. Count:', components.length);
      console.log('CANVAS: Component IDs and Types:', JSON.stringify(components.map(c => ({id: c.id, type: c.type}))));
      this.rootComponents = components; // Correct type assignment
      console.log('CANVAS: Assigned this.rootComponents. Count:', this.rootComponents.length);
      this.cdr.detectChanges();
    });
    
    // Subscribe to selected component
    this.componentStore.getSelectedComponentId().pipe(
      takeUntil(this.destroy$)
    ).subscribe(id => {
      console.log('Selected component changed:', id);
      this.selectedComponentId = id;
      this.cdr.detectChanges();
    });

    // Subscribe to canvas state changes
    this.componentStore.getCanvasState().pipe(
      takeUntil(this.destroy$)
    ).subscribe(canvasState => {
      console.log('Canvas state changed:', canvasState);
      this.canvasState = canvasState;
      this.cdr.detectChanges();
    });

    // Subscribe to selection state changes
    this.componentStore.getSelectionState().pipe(
      takeUntil(this.destroy$)
    ).subscribe(selectionState => {
      console.log('Selection state changed:', selectionState);
      this.selectionState = selectionState;
      this.cdr.detectChanges();
    });

    // Debug log the initial state
    console.log('Initial state:');
    console.log('Current page:', this.currentPage);
    console.log('Root components:', this.rootComponents);
    console.log('Selected component ID:', this.selectedComponentId);
  }
  
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
  
  // Select component on click, support multi-selection with Ctrl
  selectComponent(id: string, event: MouseEvent): void {
    event.stopPropagation(); // Prevent triggering onCanvasClick
    
    const isCtrlPressed = event.ctrlKey || event.metaKey;
    this.componentStore.toggleComponentSelection(id, isCtrlPressed);
  }

  // Handle canvas click - clear selection when clicking empty space
  onCanvasClick(event: MouseEvent): void {
    // Only clear selection if not doing drag selection and not holding Ctrl
    if (!this.isDragSelecting && !(event.ctrlKey || event.metaKey)) {
      this.componentStore.clearSelection();
    }
  }

  // Handle mouse down on canvas for drag selection
  onCanvasMouseDown(event: MouseEvent): void {
    // Only start drag selection on primary button and not on components
    if (event.button !== 0 || (event.target as HTMLElement).closest('[data-component-id]')) {
      return;
    }

    event.preventDefault();
    this.isDragSelecting = true;
    
    const canvasRect = (event.currentTarget as HTMLElement).getBoundingClientRect();
    this.dragSelectionStart = {
      x: event.clientX - canvasRect.left,
      y: event.clientY - canvasRect.top
    };
    
    this.dragSelectionRect = {
      x: this.dragSelectionStart.x,
      y: this.dragSelectionStart.y,
      width: 0,
      height: 0
    };

    // Clear selection if not holding Ctrl
    if (!(event.ctrlKey || event.metaKey)) {
      this.componentStore.clearSelection();
    }
  }

  // Handle mouse move for drag selection
  @HostListener('mousemove', ['$event'])
  onCanvasMouseMove(event: MouseEvent): void {
    if (!this.isDragSelecting || !this.dragSelectionStart) {
      return;
    }

    const canvasElement = document.querySelector('.canvas-container') as HTMLElement;
    if (!canvasElement) return;

    const canvasRect = canvasElement.getBoundingClientRect();
    const currentX = event.clientX - canvasRect.left;
    const currentY = event.clientY - canvasRect.top;

    this.dragSelectionRect = {
      x: Math.min(this.dragSelectionStart.x, currentX),
      y: Math.min(this.dragSelectionStart.y, currentY),
      width: Math.abs(currentX - this.dragSelectionStart.x),
      height: Math.abs(currentY - this.dragSelectionStart.y)
    };

    // Update selection based on rectangle
    this.updateDragSelection();
  }

  // Handle mouse up for drag selection
  @HostListener('mouseup', ['$event'])
  onCanvasMouseUp(event: MouseEvent): void {
    if (this.isDragSelecting) {
      this.isDragSelecting = false;
      this.dragSelectionStart = null;
      this.dragSelectionRect = null;
    }
  }

  // Update component selection based on drag rectangle
  private updateDragSelection(): void {
    if (!this.dragSelectionRect) return;

    const componentsInSelection: string[] = [];
    
    // Get all component elements
    const componentElements = document.querySelectorAll('[data-component-id]');
    
    componentElements.forEach(element => {
      const componentId = element.getAttribute('data-component-id');
      if (!componentId) return;

      const elementRect = element.getBoundingClientRect();
      const canvasElement = document.querySelector('.canvas-container') as HTMLElement;
      if (!canvasElement) return;

      const canvasRect = canvasElement.getBoundingClientRect();
      
      // Convert element position to canvas coordinates
      const elementCanvasRect = {
        x: elementRect.left - canvasRect.left,
        y: elementRect.top - canvasRect.top,
        width: elementRect.width,
        height: elementRect.height
      };

      // Check if element intersects with selection rectangle
      if (this.rectanglesIntersect(this.dragSelectionRect!, elementCanvasRect)) {
        componentsInSelection.push(componentId);
      }
    });

    // Update selection
    this.componentStore.selectMultipleComponents(componentsInSelection);
  }

  // Check if two rectangles intersect
  private rectanglesIntersect(rect1: { x: number; y: number; width: number; height: number }, 
                             rect2: { x: number; y: number; width: number; height: number }): boolean {
    return !(rect1.x + rect1.width < rect2.x || 
             rect2.x + rect2.width < rect1.x || 
             rect1.y + rect1.height < rect2.y || 
             rect2.y + rect2.height < rect1.y);
  }

  // Keyboard shortcuts
  @HostListener('window:keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    // Only handle shortcuts when canvas has focus or no input is focused
    const activeElement = document.activeElement;
    if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
      return;
    }

    const isCtrl = event.ctrlKey || event.metaKey;

    switch (event.key) {
      case 'a':
        if (isCtrl) {
          event.preventDefault();
          this.componentStore.selectAllComponents();
        }
        break;
      
      case 'c':
        if (isCtrl) {
          event.preventDefault();
          this.copySelectedComponents();
        }
        break;
      
      case 'v':
        if (isCtrl) {
          event.preventDefault();
          // Handle paste through the public toolbar
          const clipboardData = this.clipboardService.getClipboardData();
          if (clipboardData && clipboardData.components.length > 0) {
            this.componentStore.pasteComponents(clipboardData).subscribe({
              next: (newComponentIds) => {
                console.log('Pasted components:', newComponentIds);
                if (newComponentIds.length > 0) {
                  this.componentStore.selectComponent(newComponentIds[0]);
                }
              },
              error: (error) => console.error('Error pasting components:', error)
            });
          }
        }
        break;
      
      case 'x':
        if (isCtrl) {
          event.preventDefault();
          this.cutSelectedComponents();
        }
        break;
      
      case 'Delete':
      case 'Backspace':
        event.preventDefault();
        this.deleteSelectedComponents();
        break;
      
      case 'Escape':
        this.componentStore.clearSelection();
        break;
    }
  }

  // Paste components
  private pasteComponents(): void {
    // Get clipboard data from ClipboardService
    const clipboardData = this.clipboardService.getClipboardData();
    if (clipboardData && clipboardData.components.length > 0) {
      this.componentStore.pasteComponents(clipboardData).subscribe({
        next: (newComponentIds) => {
          console.log('Pasted components:', newComponentIds);
          // Select the first pasted component
          if (newComponentIds.length > 0) {
            this.componentStore.selectComponent(newComponentIds[0]);
          }
        },
        error: (error) => {
          console.error('Error pasting components:', error);
        }
      });
    }
  }

  // Check if component is selected
  isComponentSelected(componentId: string): boolean {
    return this.componentStore.isComponentSelected(componentId);
  }

  /**
   * Get a shortened version of the component ID for display
   */
  getShortId(id: string): string {
    if (!id) return '';
    const parts = id.split('-');
    return parts[0].substring(0, 6) + '...' + parts[parts.length - 1].substring(0, 4);
  }
  
  /**
   * Get child components for a container - SIMPLIFIED
   */
  getChildrenOf(containerId: string): BuilderComponent[] {
    // Get the container directly from the store - no caching
    const container = this.componentStore.getComponentSync(containerId);
    if (!container || !container.children || !Array.isArray(container.children)) {
      console.log(`Container ${containerId} has no children array`);
      return [];
    }
    
    // Get the children directly
    const children = container.children
      .map(childId => {
        const child = this.componentStore.getComponentSync(childId);
        if (!child) {
          console.log(`Child ${childId} not found for container ${containerId}`);
        }
        return child;
      })
      .filter(Boolean) as BuilderComponent[];
    
    console.log(`Container ${containerId} children:`, children.map(c => c.id));
    return children;
  }
  
  /**
   * Handle drop in container - properly typed
   */
  onDropInContainer(event: CdkDragDrop<BuilderComponent[]>, containerId: string): void {
    console.log('🔥 DROP IN CONTAINER', containerId);
    
    // Reset drop targets
    this.isDropTargetFor = null;
    this.isDropTarget = false;
    
    // Get drag data
    const dragData = event.item.data;
    console.log('Drag data:', dragData);
    
    try {
      if (dragData?.source === 'palette') {
        // Create a new component in this container
        console.log('Creating new component in container from palette');
        const componentId = this.createNewComponent(
          dragData.componentType, 
          dragData.properties,
          containerId
        );
        console.log('Created component in container with ID:', componentId);
      } else if (dragData?.source === 'canvas' || dragData?.source === 'container') {
        // Reorder or move component
        if (event.previousContainer === event.container) {
          // Reordering
          console.log('Reordering within container:', containerId);
          this.reorderContainerChildren(containerId, event.previousIndex, event.currentIndex);
        } else {
          // Moving between containers
          console.log('Moving component to container:', containerId);
          
          let sourceContainerId: string | null = null;
          if (event.previousContainer.id !== 'canvas-root') {
            sourceContainerId = event.previousContainer.id.replace('container-', '');
          }
          
          this.componentStore.moveComponent(
            dragData.componentId,
            sourceContainerId,
            containerId,
            event.currentIndex
          );
        }
      }
    } catch (error) {
      console.error('Error handling container drop:', error);
    }
    
    // Force reload and update UI
    this.cdr.detectChanges();
    
    // Extra refresh after a delay
    setTimeout(() => {
      console.log('Refreshing UI after container drop');
      this.cdr.detectChanges();
    }, 200);
  }
  
  /**
   * Helper to reorder children in a container
   */
  reorderContainerChildren(containerId: string, fromIndex: number, toIndex: number): void {
    const container = this.componentStore.getComponentSync(containerId);
    if (container && Array.isArray(container.children)) {
      // Create new children array with reordering
      const newChildren = [...container.children];
      const [moved] = newChildren.splice(fromIndex, 1);
      newChildren.splice(toIndex, 0, moved);
      
      // Update the store
      this.componentStore.reorderChildren(containerId, newChildren);
    }
  }
  
  /**
   * Handle drop on canvas - properly typed
   */
  onDrop(event: CdkDragDrop<BuilderComponent[]>): void {
    console.log('🔄 DROP ON CANVAS', event);
    
    // Reset UI state
    this.isDropTarget = false;
    
    const dragData = event.item.data;
    console.log('Drag data:', JSON.stringify(dragData, null, 2));
    
    if (dragData?.source === 'palette') {
      console.log('Source is palette, creating new component:', dragData.componentType);
      
      try {
        // Create the new component
        const componentId = this.createNewComponent(dragData.componentType, dragData.properties);
        console.log('Component created with ID:', componentId);
        
        // Select the newly created component
        if (componentId) {
          this.componentStore.selectComponent(componentId);
        }
        
        // Force multiple change detection cycles to ensure rendering
        this.cdr.markForCheck();
        this.cdr.detectChanges();
        
        // Add a slight delay for the UI to update
        setTimeout(() => {
          this.cdr.detectChanges();
          console.log('Canvas post-create refresh complete');
        }, 200);
        
      } catch (error) {
        console.error('Failed to create new component on canvas:', error);
      }
    } else if (event.previousContainer !== event.container) {
      // Move component from one container to another
      console.log('Moving component from container to canvas');
      
      const dragData = event.item.data;
      if (dragData?.componentId) {
        let sourceContainerId: string | null = null;
        if (event.previousContainer.id !== 'canvas-root') {
          sourceContainerId = event.previousContainer.id.replace('container-', '');
        }
        
        this.componentStore.moveComponent(
          dragData.componentId,
          sourceContainerId,
          null, // Moving to canvas (root)
          event.currentIndex
        );
      }
    } else {
      // Reordering on canvas
      console.log('Reordering components on canvas');
      this.componentStore.reorderRootComponents(event.previousIndex, event.currentIndex);
    }
  }
  
  /**
   * Create a new component from palette
   */
  private createNewComponent(componentType: string, properties?: Record<string, any>, parentId?: string): string {
    console.log('🎯 CANVAS: createNewComponent called for type:', componentType, 'in parent:', parentId);

    const defaultClasses = this.getDefaultClassesForType(componentType);
    const componentProperties: { [key: string]: any; tailwindClasses: string; } = {
      tailwindClasses: properties?.['tailwindClasses'] || defaultClasses || '',
      ...(properties || {})
    };

    if (!componentProperties['tailwindClasses']) {
      componentProperties['tailwindClasses'] = defaultClasses || '';
    }

    const newComponentData: Partial<BuilderComponent> = {
      type: componentType,
      properties: componentProperties,
      isContainer: componentType === 'container' || componentType === 'row' || componentType === 'column'
    };

    if (newComponentData.isContainer) {
      newComponentData.children = [];
      
      if (!componentProperties['tailwindClasses'].includes('p-') && componentType === 'container') {
        componentProperties['tailwindClasses'] += ' p-2';
      }
    }

    console.log('🎯 CANVAS: Final component data before adding to store:', JSON.stringify(newComponentData, null, 2));

    const id = parentId
      ? this.componentStore.addComponentSync(newComponentData, parentId)
      : this.componentStore.addComponentSync(newComponentData);

    console.log('🎯 CANVAS: Component created successfully with ID:', id);
    return id;
  }

  /**
   * Get default classes for a component type
   */
  private getDefaultClassesForType(type: string): string {
    switch (type) {
      case 'container': return 'flex flex-col p-4 border border-dashed border-gray-300 rounded bg-white min-h-[100px]';
      case 'row': return 'flex flex-row gap-2 w-full p-2 border border-gray-500 rounded bg-white';
      case 'column': return 'flex flex-col gap-2 h-full p-2 border border-gray-500 rounded bg-white';
      case 'text': return 'text-base text-gray-800';
      case 'heading': return 'text-2xl font-bold text-gray-900';
      case 'button': return 'px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600';
      case 'image': return 'w-full h-auto max-w-md object-cover rounded';
      case 'input': return 'w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500';
      case 'form': return 'space-y-4 p-4 border border-gray-300 rounded';
      case 'list': return 'space-y-2';
      case 'card': return 'p-4 border border-gray-300 rounded shadow-sm bg-white';
      case 'section': return 'py-8 px-4';
      default: return 'block';
    }
  }

  /**
   * Handle drag enter event - for drop zones
   */
  onDragEnter(event: CdkDragEnter): void {
    console.log('🔄 DRAG ENTER', event);
    this.isDropTarget = true;
  }

  /**
   * Handle drag exit event - for drop zones
   */
  onDragExit(event: CdkDragExit): void {
    console.log('🔄 DRAG EXIT', event);
    this.isDropTarget = false;
  }

  /**
   * Track by function for ngFor - optimizes rendering
   */
  trackById(index: number, item: BuilderComponent): string {
    return item.id;
  }

  /**
   * Handle drag started event
   */
  onDragStarted(): void {
    console.log('🔄 DRAG STARTED');
    this.isDragging = true;
  }

  /**
   * Handle drag ended event
   */
  onDragEnded(): void {
    console.log('🔄 DRAG ENDED');
    this.isDragging = false;
    this.isDropTarget = false;
    this.isDropTargetFor = null;
  }

  /**
   * Get component display name for accessibility
   */
  getComponentDisplayName(component: BuilderComponent): string {
    if (component.properties && component.properties['text']) {
      return `${component.type}: ${component.properties['text']}`;
    }
    return `${component.type} component`;
  }

  /**
   * Select component with visual feedback
   */
  selectComponentWithFeedback(id: string, event: MouseEvent): void {
    this.selectComponent(id, event);
    // Add visual feedback if needed
  }

  /**
   * Handle component hover for visual feedback
   */
  onComponentHover(componentId: string, isHovering: boolean): void {
    // Add hover state management if needed
    console.log(`Component ${componentId} hover: ${isHovering}`);
  }

  /**
   * Check if component is hovered
   */
  isComponentHovered(componentId: string): boolean {
    // Implement hover state tracking if needed
    return false;
  }

  // Multi-selection toolbar methods

  /**
   * Align selected components
   */
  alignSelectedComponents(alignment: AlignmentType): void {
    const selectedIds = this.componentStore.getSelectedComponentIds();
    if (selectedIds.length > 1) {
      this.componentStore.alignComponents(selectedIds, alignment);
    }
  }

  /**
   * Distribute selected components
   */
  distributeSelectedComponents(distribution: DistributionType): void {
    const selectedIds = this.componentStore.getSelectedComponentIds();
    if (selectedIds.length > 2) { // Need at least 3 components to distribute
      this.componentStore.distributeComponents(selectedIds, distribution);
    }
  }

  /**
   * Copy selected components (public method for toolbar)
   */
  copySelectedComponents(): void {
    const selectedIds = this.componentStore.getSelectedComponentIds();
    if (selectedIds.length > 0) {
      this.componentStore.copyComponents(selectedIds);
    }
  }

  /**
   * Cut selected components (public method for toolbar)
   */
  cutSelectedComponents(): void {
    const selectedIds = this.componentStore.getSelectedComponentIds();
    if (selectedIds.length > 0) {
      this.componentStore.copyComponents(selectedIds);
      this.componentStore.deleteSelectedComponents();
    }
  }

  /**
   * Delete selected components (public method for toolbar)
   */
  deleteSelectedComponents(): void {
    // Get selected IDs and batch remove them
    const selectedIds = this.componentStore.getSelectedComponentIds();
    selectedIds.forEach(id => {
      this.componentStore.removeComponent(id);
    });
    // Clear selection after deletion
    this.componentStore.clearSelection();
  }

  /**
   * Clear selection (public method for toolbar)
   */
  clearSelection(): void {
    this.componentStore.clearSelection();
  }
}
