/* Enhanced container styles */
.enhanced-container {
  display: flex;
  flex-direction: column;
  position: relative;
  min-height: 150px;
  border: 2px solid #ccc;
  border-radius: 8px;
  background-color: white;
  margin: 15px;
  padding: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  /* Header bar */
  .enhanced-container-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    
    .enhanced-container-title {
      font-weight: bold;
      font-size: 14px;
      color: #333;
    }
    
    .enhanced-container-controls {
      display: flex;
      gap: 5px;
      
      button {
        background-color: #f0f0f0;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 2px 5px;
        font-size: 12px;
        cursor: pointer;
        
        &:hover {
          background-color: #e0e0e0;
        }
      }
    }
  }
  
  /* Drop zone */
  .enhanced-container-dropzone {
    flex-grow: 1;
    width: 100%;
    min-height: 100px;
    padding: 15px;
  }
  
  /* Children area */
  .enhanced-container-children {
    width: 100%;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  
  /* Empty state */
  .enhanced-container-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100px;
    opacity: 0.7;
    
    .enhanced-container-empty-message {
      text-align: center;
      color: #666;
      
      .enhanced-container-empty-icon {
        font-size: 24px;
        margin-bottom: 5px;
      }
    }
  }
  
  /* Drop indicator */
  .enhanced-container-drop-indicator {
    position: absolute;
    inset: 0;
    background-color: rgba(16, 185, 129, 0.1);
    border: 2px dashed #10b981;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    pointer-events: none;
    
    .enhanced-container-drop-message {
      background-color: #10b981;
      color: white;
      font-weight: bold;
      padding: 8px 16px;
      border-radius: 4px;
    }
  }
  
  /* States */
  &.enhanced-container-selected {
    border-color: #4f46e5;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.3);
    
    .enhanced-container-header {
      background-color: #4f46e5;
      color: white;
      
      .enhanced-container-title {
        color: white;
      }
    }
  }
  
  &.enhanced-container-drop-target {
    border-color: #10b981;
    background-color: rgba(16, 185, 129, 0.05);
  }
  
  /* Hover state */
  &:hover {
    border-color: #4f46e5;
  }
}

/* Child component styles */
.child-component {
  margin: 5px 0;
}
