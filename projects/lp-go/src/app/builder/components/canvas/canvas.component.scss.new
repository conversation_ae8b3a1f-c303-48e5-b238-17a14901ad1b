.canvas-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f8fafc;
}

.canvas-viewport {
  flex: 1;
  position: relative;
  overflow: auto;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  margin: 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transform-origin: top left;
  transition: transform 0.2s ease;
}

.canvas-content {
  min-height: 100%;
  padding: 24px;
  position: relative;

  &.drop-zone-active {
    background-color: rgba(66, 153, 225, 0.1);
  }
}

.debug-info {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  z-index: 1000;
  max-width: 300px;
  word-wrap: break-word;
}

.drop-indicator {
  position: absolute;
  background: rgba(59, 130, 246, 0.3);
  border: 2px dashed #3b82f6;
  border-radius: 4px;
  pointer-events: none;
  z-index: 999;
}

.component-wrapper {
  position: relative;
  display: inline-block;
  
  &:hover {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }
  
  &.selected {
    outline: 2px solid #ef4444;
    outline-offset: 2px;
  }
}

.zoom-controls {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: 0.5rem;
  z-index: 100;
}

.zoom-button {
  @apply bg-white border border-gray-300 rounded px-3 py-1 text-sm hover:bg-gray-50;
}

// Enhanced component selection styles
.component-wrapper {
  position: relative;
  transition: all 0.2s ease;
  border-radius: 0.25rem;

  &:hover {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
    cursor: pointer;
  }

  &.selected {
    outline: 2px solid #ef4444;
    outline-offset: 2px;
    background-color: rgba(239, 68, 68, 0.05);
  }

  &.dragging {
    opacity: 0.7;
    transform: scale(0.98);
  }
}

// Drag and drop styles
.drop-zone {
  min-height: 100px;
  border: 2px dashed #d1d5db;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  transition: all 0.2s ease;

  &.drag-over {
    border-color: #3b82f6;
    background-color: rgba(59, 130, 246, 0.05);
    color: #3b82f6;
  }

  &.has-components {
    border: none;
    background: transparent;
    min-height: auto;
  }
}

.drop-placeholder {
  height: 4px;
  background-color: #3b82f6;
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.2s ease;

  &.active {
    opacity: 1;
  }
}

// Selection handles
.selection-handles {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  pointer-events: none;
  border: 2px solid #ef4444;
  border-radius: 4px;

  .handle {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #ef4444;
    border: 1px solid white;
    border-radius: 50%;
    pointer-events: all;
    cursor: nw-resize;

    &.top-left { top: -4px; left: -4px; }
    &.top-right { top: -4px; right: -4px; cursor: ne-resize; }
    &.bottom-left { bottom: -4px; left: -4px; cursor: sw-resize; }
    &.bottom-right { bottom: -4px; right: -4px; cursor: se-resize; }
  }
}

// Canvas grid
.canvas-grid {
  background-image: 
    linear-gradient(rgba(0,0,0,.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0,0,0,.1) 1px, transparent 1px);
  background-size: 20px 20px;
  background-position: -1px -1px;
}

// Responsive design helpers
.responsive-indicator {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  z-index: 100;
}

// Component toolbar
.component-toolbar {
  position: absolute;
  top: -40px;
  left: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.25rem;
  padding: 0.25rem;
  display: flex;
  gap: 0.25rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 200;
  opacity: 0;
  transition: opacity 0.2s ease;

  .component-wrapper:hover & {
    opacity: 1;
  }

  button {
    @apply bg-gray-100 hover:bg-gray-200 border-0 rounded px-2 py-1 text-xs;
  }
}

// Animation classes
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-in-top {
  animation: slideInFromTop 0.3s ease-out;
}

// Device frame styles
.device-frame {
  border: 8px solid #374151;
  border-radius: 1rem;
  background: #374151;
  position: relative;

  &.mobile {
    width: 375px;
    min-height: 667px;
  }

  &.tablet {
    width: 768px;
    min-height: 1024px;
  }

  &.desktop {
    width: 100%;
    min-height: 600px;
    border: none;
    border-radius: 0;
    background: transparent;
  }

  .device-screen {
    background: white;
    border-radius: 0.5rem;
    overflow: hidden;
    width: 100%;
    height: 100%;
  }
}

// Loading states
.component-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  color: #6b7280;

  &::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Error states
.component-error {
  border: 2px dashed #ef4444;
  border-radius: 0.5rem;
  padding: 1rem;
  color: #ef4444;
  text-align: center;
  background-color: rgba(239, 68, 68, 0.05);

  .error-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  .error-message {
    font-size: 0.875rem;
    font-weight: 500;
  }
}
