/* Simple container styles */
.simple-container {
  display: flex;
  flex-direction: column;
  border: 2px solid #4a5568;
  border-radius: 8px;
  background-color: white;
  margin: 15px 0;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  
  &.simple-container-selected {
    border-color: #3182ce;
    box-shadow: 0 0 0 2px rgba(49, 130, 206, 0.4);
  }
  
  .simple-container-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: #4a5568;
    color: white;
    font-size: 14px;
    font-weight: 500;
    
    button {
      background-color: #2d3748;
      color: white;
      border: none;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      
      &:hover {
        background-color: #1a202c;
      }
    }
  }
  
  .simple-container-content {
    padding: 16px;
    min-height: 100px;
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    &.cdk-drop-list-dragging {
      background-color: rgba(66, 153, 225, 0.1);
      border: 2px dashed #4299e1;
    }
  }
  
  .simple-container-empty {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
    border: 2px dashed #cbd5e0;
    border-radius: 4px;
    background-color: #f7fafc;
  }
}

/* Drag and drop styling */
.cdk-drag-preview {
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
  opacity: 0.8;
}

.cdk-drag-placeholder {
  opacity: 0.3;
  background: #f7fafc;
  border: 2px dashed #cbd5e0;
  min-height: 50px;
}

.cdk-drop-list-dragging {
  background-color: rgba(66, 153, 225, 0.1) !important;
  border: 2px dashed #4299e1 !important;
}
