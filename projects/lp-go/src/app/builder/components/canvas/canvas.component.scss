// Canvas container styles
.canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: auto;
}

// Canvas viewport with zoom support
.canvas-viewport {
  position: relative;
  min-height: 100%;
  transform-origin: top left;
}

// Canvas content area
.canvas-content {
  position: relative;
  min-height: 500px;
  padding: 20px;
  user-select: none; // Prevent text selection during drag
}

// Component wrapper styling
.component-wrapper {
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;

  // Single selection state
  &.selected {
    outline: 2px solid #3182CE;
    outline-offset: 2px;
  }

  // Multi-selection state - different visual treatment
  &.multi-selected {
    outline: 2px solid #805AD5;
    outline-offset: 2px;
    background-color: rgba(128, 90, 213, 0.1);
  }

  // Hover state
  &.hovered {
    outline: 1px dashed #A0AEC0;
    outline-offset: 1px;
  }

  // Don't show hover when already selected
  &.selected.hovered,
  &.multi-selected.hovered {
    outline: 2px solid #3182CE;
    outline-offset: 2px;
  }

  &.multi-selected.hovered {
    outline: 2px solid #805AD5;
    outline-offset: 2px;
  }
}

// Drag selection rectangle
.drag-selection-rectangle {
  position: absolute;
  border: 2px solid #3182CE;
  background-color: rgba(49, 130, 206, 0.1);
  pointer-events: none;
  z-index: 1000;
}

// Multi-selection toolbar
.multi-selection-toolbar {
  position: sticky;
  top: 0;
  z-index: 100;
  background: white;
  border-bottom: 1px solid #E2E8F0;
  padding: 8px 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .toolbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
  }

  .selection-count {
    font-size: 14px;
    font-weight: 600;
    color: #4A5568;
  }

  .toolbar-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .action-group {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .toolbar-separator {
    width: 1px;
    height: 24px;
    background-color: #E2E8F0;
    margin: 0 4px;
  }

  .toolbar-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: 1px solid #E2E8F0;
    border-radius: 6px;
    background: white;
    color: #4A5568;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #F7FAFC;
      border-color: #CBD5E0;
      color: #2D3748;
    }

    &:active {
      background: #EDF2F7;
      border-color: #A0AEC0;
    }

    .icon {
      font-size: 14px;
      line-height: 1;
    }

    &.clear-selection {
      border-color: #FC8181;
      color: #E53E3E;

      &:hover {
        background: #FED7D7;
        border-color: #F56565;
      }
    }
  }
}

// Empty canvas state
.empty-canvas {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  border: 2px dashed #CBD5E0;
  border-radius: 8px;
  margin: 40px;

  .empty-canvas-message {
    text-align: center;
    color: #718096;

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    p {
      font-size: 14px;
    }
  }
}

// Drop indicator
.drop-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(49, 130, 206, 0.9);
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  font-weight: 600;
  z-index: 1000;
  pointer-events: none;
}

// Drop zone active state
.drop-zone-active {
  background-color: rgba(49, 130, 206, 0.05);
  border: 2px dashed #3182CE;
  border-radius: 8px;
}