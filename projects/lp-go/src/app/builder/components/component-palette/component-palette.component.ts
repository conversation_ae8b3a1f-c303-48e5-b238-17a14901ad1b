import { Component, OnInit, On<PERSON><PERSON>roy, Type } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  CdkDragStart,
  CdkDragEnd,
  DragDropModule
} from '@angular/cdk/drag-drop';
import { Subject, combineLatest } from 'rxjs';
import { takeUntil, map, startWith } from 'rxjs/operators';
import { ComponentRegistryService, ComponentRegistration } from '../component-registry.service';
import { StorageService } from '../../../shared/services/storage.service';
import { ComponentSearchComponent } from './components/component-search.component';
import { ComponentCardComponent, ComponentCardData } from './components/component-card.component';
import { ComponentFilterComponent, FilterCategory, FilterOptions } from './components/component-filter.component';

interface PaletteItem {
  type: string;
  label: string;
  icon?: string;
  description?: string;
  properties?: Record<string, any>;
}

interface PaletteCategory {
  name: string;
  items: PaletteItem[];
}

@Component({
  selector: 'app-component-palette',
  templateUrl: './component-palette.component.html',
  styleUrls: ['./component-palette.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    DragDropModule,
    ComponentSearchComponent,
    ComponentCardComponent,
    ComponentFilterComponent
  ]
})
export class ComponentPaletteComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Track if a component is being dragged
  isDragging = false;

  // Component categories
  categories: PaletteCategory[] = []; // Initialize as empty

  // Loading state
  isLoading = true;

  // Registry status
  registryStatus = 'Initializing...';

  // Enhanced filtering and search
  searchTerm = '';
  filterOptions: FilterOptions = {
    showFavoritesOnly: false,
    showRecentOnly: false,
    selectedCategories: []
  };

  // Component data for new sub-components
  componentCards: ComponentCardData[] = [];
  filterCategories: FilterCategory[] = [];
  favoritesCount = 0;
  recentCount = 0;
  filteredResultsCount = 0;

  // Active category for mobile view
  activeCategory = '';

  constructor(
    private componentRegistry: ComponentRegistryService,
    private storageService: StorageService
  ) {}

  ngOnInit(): void {
    this.initializeFromRegistry();
    this.initializeStorageSubscriptions();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Initialize storage subscriptions for reactive updates
   */
  private initializeStorageSubscriptions(): void {
    // Subscribe to favorites and recent changes
    combineLatest([
      this.storageService.getFavoriteComponents$(),
      this.storageService.getRecentComponents$()
    ])
      .pipe(takeUntil(this.destroy$))
      .subscribe(([favorites, recent]) => {
        this.favoritesCount = favorites.length;
        this.recentCount = recent.length;
        this.updateFilteredComponents();
      });
  }

  /**
   * Initialize component palette from enhanced registry
   */
  private async initializeFromRegistry(): Promise<void> {
    this.registryStatus = 'Loading components from registry...';

    // Wait for auto-discovery to complete
    await this.componentRegistry.waitForAutoDiscovery();

    // Subscribe to registry updates
    this.componentRegistry.getRegistrations()
      .pipe(takeUntil(this.destroy$))
      .subscribe(registrations => {
        this.buildCategoriesFromRegistrations(registrations);
        this.buildComponentCards(registrations);
        this.buildFilterCategories();
        this.updateFilteredComponents();
        this.isLoading = false;
        this.registryStatus = `Loaded ${registrations.length} components`;
      });
  }

  /**
   * Build categories from component registrations
   */
  private buildCategoriesFromRegistrations(registrations: ComponentRegistration[]): void {
    const categoryMap = new Map<string, PaletteItem[]>();

    // Group registrations by category
    registrations.forEach(registration => {
      if (!registration.validated) {
        console.warn(`[ComponentPalette] Skipping invalid component: ${registration.name}`);
        return;
      }

      const category = registration.category;
      if (!categoryMap.has(category)) {
        categoryMap.set(category, []);
      }

      const paletteItem: PaletteItem = {
        type: registration.name,
        label: this.formatLabel(registration.name),
        icon: registration.icon || this.getDefaultIcon(category),
        description: registration.description || `${registration.name} component`,
        properties: this.getDefaultProperties(registration.name, category)
      };

      categoryMap.get(category)!.push(paletteItem);
    });

    // Convert to categories array and sort
    this.categories = Array.from(categoryMap.entries())
      .map(([name, items]) => ({
        name,
        items: items.sort((a, b) => a.label.localeCompare(b.label))
      }))
      .sort((a, b) => this.getCategoryOrder(a.name) - this.getCategoryOrder(b.name));

    // Set active category
    if (this.categories.length > 0) {
      this.activeCategory = this.categories[0].name;
    }

    console.log(`[ComponentPalette] Built ${this.categories.length} categories with ${registrations.length} total components`);
  }

  /**
   * Get category display order
   */
  private getCategoryOrder(category: string): number {
    const order = ['Basic', 'Layout', 'Form', 'Forms', 'Page Sections', 'Widgets', 'Features', 'Games', 'Pages', 'Base', 'Legacy'];
    const index = order.indexOf(category);
    return index === -1 ? 999 : index;
  }

  /**
   * Format component name for display
   */
  private formatLabel(name: string): string {
    return name
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  /**
   * Get default icon for category
   */
  private getDefaultIcon(category: string): string {
    const iconMap: Record<string, string> = {
      'Basic': 'type',
      'Layout': 'grid',
      'Form': 'edit',
      'Page Sections': 'view-module',
      'Widgets': 'extension',
      'Features': 'star',
      'Games': 'sports-esports',
      'Pages': 'web',
      'Base': 'cube',
      'Legacy': 'archive'
    };

    return iconMap[category] || 'component';
  }

  /**
   * Get default properties for component using the property schema
   */
  private getDefaultProperties(name: string, category: string): Record<string, any> {
    // Get property schema from injected properties
    const propertySchemas = this.getPropertySchemaForComponent(name);
    
    // Build defaults from schema
    const defaults: Record<string, any> = {
      tailwindClasses: this.getDefaultClasses(name, category)
    };

    if (propertySchemas) {
      // Extract defaults from schema
      Object.keys(propertySchemas).forEach(key => {
        const property = propertySchemas[key];
        if (property.type === 'object' && property.properties) {
          // Handle nested object properties
          defaults[key] = {};
          Object.keys(property.properties).forEach(subKey => {
            const subProperty = property.properties[subKey];
            if (subProperty.default !== undefined) {
              defaults[key][subKey] = subProperty.default;
            }
          });
        } else if (property.default !== undefined) {
          // Handle simple properties
          defaults[key] = property.default;
        }
      });
    } else {
      // Fallback to previous logic if no schema found
      if (name.includes('container') || name.includes('layout')) {
        defaults['allowedChildTypes'] = [];
      }

      if (name.includes('text') || name.includes('paragraph')) {
        defaults['content'] = 'Sample text';
      }

      if (name.includes('heading')) {
        defaults['content'] = 'Heading';
        defaults['level'] = 2;
      }

      if (name.includes('button')) {
        defaults['text'] = 'Button';
        defaults['color'] = 'primary';
      }

      if (name.includes('image')) {
        defaults['src'] = 'https://placeholder.pics/svg/300x200';
        defaults['alt'] = 'Image placeholder';
      }
    }

    return defaults;
  }

  /**
   * Get property schema for a specific component
   */
  private getPropertySchemaForComponent(componentName: string): any {
    // Try to get schema from properties provider
    try {
      // This would need to be injected properly, for now return null
      // TODO: Inject componentProperties provider properly
      return null;
    } catch {
      return null;
    }
  }

  /**
   * Get default CSS classes for component
   */
  private getDefaultClasses(name: string, category: string): string {
    if (name.includes('container')) {
      return 'flex flex-col p-6 border-2 border-gray-300 rounded-lg bg-white min-w-[300px] min-h-[300px] shadow-sm';
    }

    if (name.includes('text') || name.includes('paragraph')) {
      return 'text-gray-800 text-base';
    }

    if (name.includes('heading')) {
      return 'text-gray-900 text-2xl font-bold';
    }

    if (name.includes('button')) {
      return 'px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600';
    }

    if (name.includes('card')) {
      return 'shadow-lg rounded-lg bg-white p-4';
    }

    return 'block';
  }

  /**
   * Legacy initialization method (kept for fallback)
   */
  private initializeLegacyCategories(): void {
    console.warn('[ComponentPalette] Using legacy categories as fallback');

    // Fallback to basic categories if registry fails
    this.categories = [
      {
        name: 'Basic',
        items: [
          {
            type: 'text',
            label: 'Text',
            icon: 'type',
            description: 'Text content with customizable styles',
            properties: {
              tailwindClasses: 'text-gray-800 text-base',
              content: 'Text component'
            }
          },
          {
            type: 'button',
            label: 'Button',
            icon: 'mouse-pointer',
            description: 'Interactive button element',
            properties: {
              tailwindClasses: 'px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600',
              text: 'Button'
            }
          }
        ]
      }
    ];

    if (this.categories.length > 0) {
      this.activeCategory = this.categories[0].name;
    }
  }

  /**
   * Build component cards from registrations
   */
  private buildComponentCards(registrations: ComponentRegistration[]): void {
    this.componentCards = registrations
      .filter(registration => registration.validated)
      .map(registration => ({
        type: registration.name,
        label: this.formatLabel(registration.name),
        icon: registration.icon || this.getDefaultIcon(registration.category),
        description: registration.description || `${registration.name} component`,
        properties: this.getDefaultProperties(registration.name, registration.category),
        category: registration.category,
        tags: registration.tags || []
      }));
  }

  /**
   * Build filter categories from current categories
   */
  private buildFilterCategories(): void {
    this.filterCategories = this.categories.map(category => ({
      name: category.name,
      count: category.items.length,
      isActive: this.filterOptions.selectedCategories.includes(category.name)
    }));
  }

  /**
   * Update filtered components based on current filters and search
   */
  private updateFilteredComponents(): void {
    let filteredCards = [...this.componentCards];

    // Apply search filter
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase().trim();
      filteredCards = filteredCards.filter(card =>
        card.label.toLowerCase().includes(term) ||
        (card.description || '').toLowerCase().includes(term) ||
        (card.category || '').toLowerCase().includes(term) ||
        (card.tags || []).some(tag => tag.toLowerCase().includes(term))
      );
    }

    // Apply favorites filter
    if (this.filterOptions.showFavoritesOnly) {
      const favorites = this.storageService.getFavoriteComponents();
      filteredCards = filteredCards.filter(card => favorites.includes(card.type));
    }

    // Apply recent filter
    if (this.filterOptions.showRecentOnly) {
      const recent = this.storageService.getRecentComponents();
      filteredCards = filteredCards.filter(card => recent.includes(card.type));
    }

    // Apply category filters
    if (this.filterOptions.selectedCategories.length > 0) {
      filteredCards = filteredCards.filter(card =>
        this.filterOptions.selectedCategories.includes(card.category || '')
      );
    }

    this.filteredResultsCount = filteredCards.length;
  }

  /**
   * Handle search change from search component
   */
  onSearchChange(searchTerm: string): void {
    this.searchTerm = searchTerm;
    this.updateFilteredComponents();
  }

  /**
   * Handle search clear from search component
   */
  onSearchClear(): void {
    this.searchTerm = '';
    this.updateFilteredComponents();
  }

  /**
   * Handle filter change from filter component
   */
  onFilterChange(filterOptions: FilterOptions): void {
    this.filterOptions = { ...filterOptions };
    this.updateFilteredComponents();

    // Update category active states
    this.buildFilterCategories();
  }

  /**
   * Handle component card click
   */
  onComponentCardClick(component: ComponentCardData): void {
    console.log('Component card clicked:', component);
    // Add to recent components
    this.storageService.addToRecentComponents(component.type);
  }

  /**
   * Handle favorite toggle from component card
   */
  onFavoriteToggle(event: { component: ComponentCardData; isFavorite: boolean }): void {
    console.log('Favorite toggled:', event);
    // Storage service handles the toggle, just log for now
  }

  /**
   * Track by function for component cards
   */
  trackByComponentType(index: number, component: ComponentCardData): string {
    return component.type;
  }



  /**
   * Filter components based on search term
   */
  get filteredCategories(): PaletteCategory[] {
    if (!this.searchTerm.trim()) {
      return this.categories;
    }

    const term = this.searchTerm.toLowerCase().trim();

    return this.categories
      .map(category => ({
        name: category.name,
        items: category.items.filter(item =>
          item.label.toLowerCase().includes(term) ||
          (item.description || '').toLowerCase().includes(term)
        )
      }))
      .filter(category => category.items.length > 0);
  }

  /**
   * Get filtered component cards based on current filters and search
   */
  get filteredComponentCards(): ComponentCardData[] {
    let filteredCards = [...this.componentCards];

    // Apply search filter
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase().trim();
      filteredCards = filteredCards.filter(card =>
        card.label.toLowerCase().includes(term) ||
        (card.description || '').toLowerCase().includes(term) ||
        (card.category || '').toLowerCase().includes(term) ||
        (card.tags || []).some(tag => tag.toLowerCase().includes(term))
      );
    }

    // Apply favorites filter
    if (this.filterOptions.showFavoritesOnly) {
      const favorites = this.storageService.getFavoriteComponents();
      filteredCards = filteredCards.filter(card => favorites.includes(card.type));
    }

    // Apply recent filter
    if (this.filterOptions.showRecentOnly) {
      const recent = this.storageService.getRecentComponents();
      filteredCards = filteredCards.filter(card => recent.includes(card.type));
    }

    // Apply category filters
    if (this.filterOptions.selectedCategories.length > 0) {
      filteredCards = filteredCards.filter(card =>
        this.filterOptions.selectedCategories.includes(card.category || '')
      );
    }

    return filteredCards;
  }

  /**
   * Set active category
   */
  setActiveCategory(categoryName: string): void {
    this.activeCategory = categoryName;
  }

  /**
   * Clear search term
   */
  clearSearch(): void {
    this.searchTerm = '';
  }

  /**
   * Handle drag start event
   */
  onDragStarted(event: CdkDragStart): void {
    this.isDragging = true;

    // Wait for the next tick when the preview element has been created
    setTimeout(() => {
      // Instead of accessing the preview directly, find it in the DOM
      const dragElements = document.querySelectorAll('.cdk-drag-preview');
      dragElements.forEach((el) => {
        // Properly cast Element to HTMLElement
        const htmlEl = el as HTMLElement;
        htmlEl.style.zIndex = '9999';
        htmlEl.style.position = 'fixed';
        htmlEl.style.pointerEvents = 'none';
        htmlEl.style.boxShadow = '0 5px 15px rgba(0,0,0,0.2)';
        htmlEl.style.transformOrigin = 'center center';
        htmlEl.style.transform = 'translate3d(0, 0, 0)';
        htmlEl.style.width = 'auto';
        htmlEl.style.height = 'auto';
      });
    }, 0);
  }

  /**
   * Handle drag end event
   */
  onDragEnded(event: CdkDragEnd): void {
    this.isDragging = false;
    console.log('Drag ended from palette component', event);
  }

  /**
   * Get all possible drop targets for palette components
   * This includes the canvas root and all container IDs
   */
  getPossibleDropTargets(): string[] {
    // Start with the standard targets - Removed 'tree-root-components' as it seems invalid
    const targets = ['canvas-root'];

    // Get all container drop lists - do this safely since we might be called before the DOM is ready
    try {
      const containerElements = document.querySelectorAll('[id^="container-"]');
      if (containerElements && containerElements.length > 0) {
        containerElements.forEach(el => {
          if (el.id) targets.push(el.id);
        });
      }
    } catch (e) {
      console.warn('Error getting container elements:', e);
    }

    return targets;
  }

  /**
   * Handle component click (fallback for touch devices)
   */
  onComponentClick(item: PaletteItem): void {
    console.log('Component clicked:', item);
    // Implement direct component creation here as a fallback for when drag and drop doesn't work
  }
}

// Helper function to check if object is an Angular component (has ɵcmp property)
function isAngularComponent(val: any): val is Type<any> {
  return val && typeof val === 'function' && val.hasOwnProperty('ɵcmp');
}
