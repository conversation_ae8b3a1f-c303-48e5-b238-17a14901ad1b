import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DragDropModule, CdkDragStart, CdkDragEnd } from '@angular/cdk/drag-drop';
import { Subject, takeUntil } from 'rxjs';
import { StorageService } from '../../../../shared/services/storage.service';

export interface ComponentCardData {
  type: string;
  label: string;
  icon?: string;
  description?: string;
  properties?: Record<string, any>;
  category?: string;
  tags?: string[];
}

@Component({
  selector: 'app-component-card',
  standalone: true,
  imports: [CommonModule, DragDropModule],
  template: `
    <div 
      class="component-card"
      [class.is-favorite]="isFavorite"
      [class.is-recent]="isRecent"
      cdkDrag
      [cdkDragDisabled]="false"
      [cdkDragData]="dragData"
      (cdkDragStarted)="onDragStarted($event)"
      (cdkDragEnded)="onDragEnded($event)"
      (click)="onCardClick()">
      
      <!-- Drag preview -->
      <div *cdkDragPreview class="drag-preview">
        <div class="preview-icon">
          <ng-container [ngSwitch]="true">
            <svg *ngSwitchCase="component.icon === 'type'" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M5 4v3h5.5v12h3V7H19V4z"/>
            </svg>
            <svg *ngSwitchCase="component.icon === 'grid'" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M3 3v8h8V3H3zm6 6H5V5h4v4zm-6 4v8h8v-8H3zm6 6H5v-4h4v4zm4-16v8h8V3h-8zm6 6h-4V5h4v4zm-6 4v8h8v-8h-8zm6 6h-4v-4h4v4z"/>
            </svg>
            <svg *ngSwitchCase="component.icon === 'edit'" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
            </svg>
            <div *ngSwitchDefault class="icon-placeholder">
              {{ getIconText() }}
            </div>
          </ng-container>
        </div>
        <div class="preview-label">{{ component.label }}</div>
      </div>
      
      <!-- Card content -->
      <div class="card-header">
        <div class="card-icon">
          <ng-container [ngSwitch]="true">
            <svg *ngSwitchCase="component.icon === 'type'" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M5 4v3h5.5v12h3V7H19V4z"/>
            </svg>
            <svg *ngSwitchCase="component.icon === 'grid'" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M3 3v8h8V3H3zm6 6H5V5h4v4zm-6 4v8h8v-8H3zm6 6H5v-4h4v4zm4-16v8h8V3h-8zm6 6h-4V5h4v4zm-6 4v8h8v-8h-8zm6 6h-4v-4h4v4z"/>
            </svg>
            <svg *ngSwitchCase="component.icon === 'edit'" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
            </svg>
            <div *ngSwitchDefault class="icon-placeholder">
              {{ getIconText() }}
            </div>
          </ng-container>
        </div>
        
        <button 
          class="favorite-button"
          (click)="toggleFavorite($event)"
          [class.active]="isFavorite"
          [attr.aria-label]="isFavorite ? 'Remove from favorites' : 'Add to favorites'"
          type="button">
          <svg width="14" height="14" viewBox="0 0 24 24" [attr.fill]="isFavorite ? 'currentColor' : 'none'" stroke="currentColor" stroke-width="2">
            <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"></polygon>
          </svg>
        </button>
      </div>
      
      <div class="card-content">
        <h4 class="card-title">{{ component.label }}</h4>
        <p class="card-description" *ngIf="component.description">
          {{ component.description }}
        </p>
        
        <div class="card-badges" *ngIf="showBadges">
          <span class="badge recent" *ngIf="isRecent">Recent</span>
          <span class="badge favorite" *ngIf="isFavorite">Favorite</span>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .component-card {
      position: relative;
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 0.5rem;
      padding: 0.75rem;
      cursor: grab;
      transition: all 0.2s ease;
      user-select: none;
    }

    .component-card:hover {
      border-color: #d1d5db;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transform: translateY(-1px);
    }

    .component-card:active {
      cursor: grabbing;
    }

    .component-card.is-favorite {
      border-color: #fbbf24;
      background-color: #fffbeb;
    }

    .component-card.is-recent {
      border-color: #3b82f6;
      background-color: #eff6ff;
    }

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 0.5rem;
    }

    .card-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 2rem;
      height: 2rem;
      background-color: #f3f4f6;
      border-radius: 0.375rem;
      color: #6b7280;
    }

    .icon-placeholder {
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
    }

    .favorite-button {
      background: none;
      border: none;
      padding: 0.25rem;
      border-radius: 0.25rem;
      color: #9ca3af;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .favorite-button:hover {
      color: #fbbf24;
      background-color: #fef3c7;
    }

    .favorite-button.active {
      color: #f59e0b;
    }

    .favorite-button:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(251, 191, 36, 0.5);
    }

    .card-content {
      min-height: 3rem;
    }

    .card-title {
      font-size: 0.875rem;
      font-weight: 600;
      color: #111827;
      margin: 0 0 0.25rem 0;
      line-height: 1.25;
    }

    .card-description {
      font-size: 0.75rem;
      color: #6b7280;
      margin: 0;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .card-badges {
      display: flex;
      gap: 0.25rem;
      margin-top: 0.5rem;
    }

    .badge {
      font-size: 0.625rem;
      font-weight: 500;
      padding: 0.125rem 0.375rem;
      border-radius: 0.25rem;
      text-transform: uppercase;
      letter-spacing: 0.025em;
    }

    .badge.recent {
      background-color: #dbeafe;
      color: #1e40af;
    }

    .badge.favorite {
      background-color: #fef3c7;
      color: #92400e;
    }

    /* Drag preview styles */
    .drag-preview {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      background: white;
      border: 1px solid #d1d5db;
      border-radius: 0.5rem;
      padding: 0.5rem 0.75rem;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 9999;
    }

    .preview-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 1.5rem;
      height: 1.5rem;
      background-color: #f3f4f6;
      border-radius: 0.25rem;
      color: #6b7280;
    }

    .preview-label {
      font-size: 0.875rem;
      font-weight: 500;
      color: #111827;
      white-space: nowrap;
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .component-card {
        padding: 0.625rem;
      }
      
      .card-title {
        font-size: 0.8125rem;
      }
      
      .card-description {
        font-size: 0.6875rem;
      }
    }
  `]
})
export class ComponentCardComponent implements OnInit, OnDestroy {
  @Input() component!: ComponentCardData;
  @Input() showBadges = true;
  @Input() connectedDropLists: string[] = [];
  
  @Output() dragStarted = new EventEmitter<CdkDragStart>();
  @Output() dragEnded = new EventEmitter<CdkDragEnd>();
  @Output() cardClick = new EventEmitter<ComponentCardData>();
  @Output() favoriteToggle = new EventEmitter<{ component: ComponentCardData; isFavorite: boolean }>();

  isFavorite = false;
  isRecent = false;
  
  private destroy$ = new Subject<void>();

  constructor(private storageService: StorageService) {}

  ngOnInit(): void {
    this.updateComponentStatus();
    
    // Subscribe to storage changes
    this.storageService.getFavoriteComponents$()
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.updateFavoriteStatus();
      });
      
    this.storageService.getRecentComponents$()
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.updateRecentStatus();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  get dragData() {
    return {
      source: 'palette',
      componentType: this.component.type,
      properties: this.component.properties || {}
    };
  }

  /**
   * Update component status (favorite/recent)
   */
  private updateComponentStatus(): void {
    this.updateFavoriteStatus();
    this.updateRecentStatus();
  }

  private updateFavoriteStatus(): void {
    this.isFavorite = this.storageService.isFavoriteComponent(this.component.type);
  }

  private updateRecentStatus(): void {
    const recentComponents = this.storageService.getRecentComponents();
    this.isRecent = recentComponents.includes(this.component.type);
  }

  /**
   * Get icon text for components without specific icons
   */
  getIconText(): string {
    if (this.component.icon) {
      return this.component.icon.charAt(0).toUpperCase();
    }
    return this.component.label.charAt(0).toUpperCase();
  }

  /**
   * Handle drag start
   */
  onDragStarted(event: CdkDragStart): void {
    // Add to recent components when dragging starts
    this.storageService.addToRecentComponents(this.component.type);
    this.dragStarted.emit(event);
  }

  /**
   * Handle drag end
   */
  onDragEnded(event: CdkDragEnd): void {
    this.dragEnded.emit(event);
  }

  /**
   * Handle card click
   */
  onCardClick(): void {
    this.cardClick.emit(this.component);
  }

  /**
   * Toggle favorite status
   */
  toggleFavorite(event: Event): void {
    event.stopPropagation(); // Prevent card click
    
    const newFavoriteStatus = this.storageService.toggleFavoriteComponent(this.component.type);
    this.favoriteToggle.emit({ 
      component: this.component, 
      isFavorite: newFavoriteStatus 
    });
  }
}
