import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface FilterCategory {
  name: string;
  count: number;
  isActive: boolean;
}

export interface FilterOptions {
  showFavoritesOnly: boolean;
  showRecentOnly: boolean;
  selectedCategories: string[];
}

@Component({
  selector: 'app-component-filter',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="component-filter">
      <!-- Quick filters -->
      <div class="quick-filters" *ngIf="showQuickFilters">
        <h4 class="filter-section-title">Quick Filters</h4>
        <div class="filter-buttons">
          <button
            type="button"
            class="filter-button"
            [class.active]="filterOptions.showFavoritesOnly"
            (click)="toggleFavoritesFilter()"
            [disabled]="!favoritesCount">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" stroke-width="2">
              <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"></polygon>
            </svg>
            Favorites
            <span class="count" *ngIf="favoritesCount">({{ favoritesCount }})</span>
          </button>
          
          <button
            type="button"
            class="filter-button"
            [class.active]="filterOptions.showRecentOnly"
            (click)="toggleRecentFilter()"
            [disabled]="!recentCount">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12,6 12,12 16,14"></polyline>
            </svg>
            Recent
            <span class="count" *ngIf="recentCount">({{ recentCount }})</span>
          </button>
        </div>
      </div>

      <!-- Category filters -->
      <div class="category-filters" *ngIf="categories.length > 0">
        <div class="filter-header">
          <h4 class="filter-section-title">Categories</h4>
          <button
            type="button"
            class="clear-button"
            (click)="clearCategoryFilters()"
            *ngIf="hasActiveCategoryFilters()">
            Clear
          </button>
        </div>
        
        <div class="category-list">
          <button
            *ngFor="let category of categories"
            type="button"
            class="category-button"
            [class.active]="category.isActive"
            (click)="toggleCategory(category.name)"
            [disabled]="category.count === 0">
            <span class="category-name">{{ category.name }}</span>
            <span class="category-count">({{ category.count }})</span>
          </button>
        </div>
      </div>

      <!-- Active filters summary -->
      <div class="active-filters" *ngIf="hasActiveFilters()">
        <div class="filter-header">
          <h4 class="filter-section-title">Active Filters</h4>
          <button
            type="button"
            class="clear-button"
            (click)="clearAllFilters()">
            Clear All
          </button>
        </div>
        
        <div class="active-filter-tags">
          <span class="filter-tag" *ngIf="filterOptions.showFavoritesOnly">
            Favorites
            <button type="button" (click)="toggleFavoritesFilter()" class="remove-tag">×</button>
          </span>
          
          <span class="filter-tag" *ngIf="filterOptions.showRecentOnly">
            Recent
            <button type="button" (click)="toggleRecentFilter()" class="remove-tag">×</button>
          </span>
          
          <span 
            *ngFor="let categoryName of filterOptions.selectedCategories"
            class="filter-tag">
            {{ categoryName }}
            <button type="button" (click)="toggleCategory(categoryName)" class="remove-tag">×</button>
          </span>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .component-filter {
      padding: 1rem 0;
      border-bottom: 1px solid #e5e7eb;
    }

    .filter-section-title {
      font-size: 0.875rem;
      font-weight: 600;
      color: #374151;
      margin: 0 0 0.5rem 0;
    }

    .filter-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 0.5rem;
    }

    .clear-button {
      background: none;
      border: none;
      color: #6b7280;
      font-size: 0.75rem;
      cursor: pointer;
      padding: 0.25rem;
      border-radius: 0.25rem;
      transition: all 0.2s ease;
    }

    .clear-button:hover {
      color: #374151;
      background-color: #f3f4f6;
    }

    .quick-filters {
      margin-bottom: 1.5rem;
    }

    .filter-buttons {
      display: flex;
      gap: 0.5rem;
      flex-wrap: wrap;
    }

    .filter-button {
      display: flex;
      align-items: center;
      gap: 0.375rem;
      padding: 0.5rem 0.75rem;
      background: white;
      border: 1px solid #d1d5db;
      border-radius: 0.375rem;
      font-size: 0.75rem;
      font-weight: 500;
      color: #374151;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .filter-button:hover:not(:disabled) {
      border-color: #9ca3af;
      background-color: #f9fafb;
    }

    .filter-button.active {
      background-color: #3b82f6;
      border-color: #3b82f6;
      color: white;
    }

    .filter-button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .count {
      font-size: 0.6875rem;
      opacity: 0.8;
    }

    .category-filters {
      margin-bottom: 1.5rem;
    }

    .category-list {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
    }

    .category-button {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.5rem 0.75rem;
      background: none;
      border: 1px solid transparent;
      border-radius: 0.375rem;
      font-size: 0.75rem;
      color: #374151;
      cursor: pointer;
      transition: all 0.2s ease;
      text-align: left;
    }

    .category-button:hover:not(:disabled) {
      background-color: #f3f4f6;
      border-color: #e5e7eb;
    }

    .category-button.active {
      background-color: #eff6ff;
      border-color: #3b82f6;
      color: #1e40af;
    }

    .category-button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .category-name {
      font-weight: 500;
    }

    .category-count {
      font-size: 0.6875rem;
      color: #6b7280;
    }

    .active-filters {
      margin-bottom: 0.5rem;
    }

    .active-filter-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 0.375rem;
    }

    .filter-tag {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.25rem 0.5rem;
      background-color: #dbeafe;
      color: #1e40af;
      border-radius: 0.25rem;
      font-size: 0.6875rem;
      font-weight: 500;
    }

    .remove-tag {
      background: none;
      border: none;
      color: inherit;
      font-size: 0.875rem;
      cursor: pointer;
      padding: 0;
      margin-left: 0.125rem;
      border-radius: 0.125rem;
      width: 1rem;
      height: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }

    .remove-tag:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .component-filter {
        padding: 0.75rem 0;
      }
      
      .filter-buttons {
        gap: 0.375rem;
      }
      
      .filter-button {
        padding: 0.375rem 0.625rem;
        font-size: 0.6875rem;
      }
      
      .category-button {
        padding: 0.375rem 0.625rem;
        font-size: 0.6875rem;
      }
    }
  `]
})
export class ComponentFilterComponent implements OnInit {
  @Input() categories: FilterCategory[] = [];
  @Input() favoritesCount = 0;
  @Input() recentCount = 0;
  @Input() showQuickFilters = true;
  @Input() filterOptions: FilterOptions = {
    showFavoritesOnly: false,
    showRecentOnly: false,
    selectedCategories: []
  };

  @Output() filterChange = new EventEmitter<FilterOptions>();

  ngOnInit(): void {
    this.updateCategoryActiveStates();
  }

  /**
   * Update category active states based on selected categories
   */
  private updateCategoryActiveStates(): void {
    this.categories.forEach(category => {
      category.isActive = this.filterOptions.selectedCategories.includes(category.name);
    });
  }

  /**
   * Toggle favorites filter
   */
  toggleFavoritesFilter(): void {
    this.filterOptions.showFavoritesOnly = !this.filterOptions.showFavoritesOnly;
    this.emitFilterChange();
  }

  /**
   * Toggle recent filter
   */
  toggleRecentFilter(): void {
    this.filterOptions.showRecentOnly = !this.filterOptions.showRecentOnly;
    this.emitFilterChange();
  }

  /**
   * Toggle category filter
   */
  toggleCategory(categoryName: string): void {
    const index = this.filterOptions.selectedCategories.indexOf(categoryName);
    
    if (index > -1) {
      this.filterOptions.selectedCategories.splice(index, 1);
    } else {
      this.filterOptions.selectedCategories.push(categoryName);
    }
    
    this.updateCategoryActiveStates();
    this.emitFilterChange();
  }

  /**
   * Clear category filters
   */
  clearCategoryFilters(): void {
    this.filterOptions.selectedCategories = [];
    this.updateCategoryActiveStates();
    this.emitFilterChange();
  }

  /**
   * Clear all filters
   */
  clearAllFilters(): void {
    this.filterOptions = {
      showFavoritesOnly: false,
      showRecentOnly: false,
      selectedCategories: []
    };
    this.updateCategoryActiveStates();
    this.emitFilterChange();
  }

  /**
   * Check if there are active category filters
   */
  hasActiveCategoryFilters(): boolean {
    return this.filterOptions.selectedCategories.length > 0;
  }

  /**
   * Check if there are any active filters
   */
  hasActiveFilters(): boolean {
    return this.filterOptions.showFavoritesOnly ||
           this.filterOptions.showRecentOnly ||
           this.filterOptions.selectedCategories.length > 0;
  }

  /**
   * Emit filter change event
   */
  private emitFilterChange(): void {
    this.filterChange.emit({ ...this.filterOptions });
  }
}
