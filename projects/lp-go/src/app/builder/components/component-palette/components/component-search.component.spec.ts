import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { ComponentSearchComponent } from './component-search.component';
import { DebugElement } from '@angular/core';
import { By } from '@angular/platform-browser';

describe('ComponentSearchComponent', () => {
  let component: ComponentSearchComponent;
  let fixture: ComponentFixture<ComponentSearchComponent>;
  let searchInput: DebugElement;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ComponentSearchComponent, FormsModule]
    }).compileComponents();

    fixture = TestBed.createComponent(ComponentSearchComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    searchInput = fixture.debugElement.query(By.css('.search-input'));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should render search input with placeholder', () => {
    expect(searchInput).toBeTruthy();
    expect(searchInput.nativeElement.placeholder).toBe('Search components...');
  });

  it('should use custom placeholder when provided', () => {
    component.placeholder = 'Custom placeholder';
    fixture.detectChanges();

    expect(searchInput.nativeElement.placeholder).toBe('Custom placeholder');
  });

  it('should show search icon when input is empty', () => {
    const searchIcon = fixture.debugElement.query(By.css('.search-icon'));
    expect(searchIcon).toBeTruthy();
  });

  it('should show clear button when input has value', () => {
    component.searchValue = 'test';
    fixture.detectChanges();

    const clearButton = fixture.debugElement.query(By.css('.clear-button'));
    expect(clearButton).toBeTruthy();
  });

  it('should hide search icon when input has value', () => {
    component.searchValue = 'test';
    fixture.detectChanges();

    const searchIcon = fixture.debugElement.query(By.css('.search-icon'));
    expect(searchIcon).toBeFalsy();
  });

  it('should emit search change with debounce', fakeAsync(() => {
    spyOn(component.searchChange, 'emit');

    // Type in search input
    searchInput.nativeElement.value = 'test search';
    searchInput.nativeElement.dispatchEvent(new Event('input'));
    fixture.detectChanges();

    // Should not emit immediately
    expect(component.searchChange.emit).not.toHaveBeenCalled();

    // Wait for debounce time (default 300ms)
    tick(300);

    // Should emit after debounce
    expect(component.searchChange.emit).toHaveBeenCalledWith('test search');
  }));

  it('should use custom debounce time', fakeAsync(() => {
    component.debounceTime = 500;
    component.ngOnInit(); // Re-initialize with new debounce time
    
    spyOn(component.searchChange, 'emit');

    searchInput.nativeElement.value = 'test';
    searchInput.nativeElement.dispatchEvent(new Event('input'));
    fixture.detectChanges();

    // Should not emit after default time
    tick(300);
    expect(component.searchChange.emit).not.toHaveBeenCalled();

    // Should emit after custom debounce time
    tick(200); // Total 500ms
    expect(component.searchChange.emit).toHaveBeenCalledWith('test');
  }));

  it('should clear search when clear button is clicked', () => {
    spyOn(component.searchChange, 'emit');
    spyOn(component.searchClear, 'emit');

    // Set search value
    component.searchValue = 'test';
    fixture.detectChanges();

    // Click clear button
    const clearButton = fixture.debugElement.query(By.css('.clear-button'));
    clearButton.nativeElement.click();
    fixture.detectChanges();

    expect(component.searchValue).toBe('');
    expect(component.searchClear.emit).toHaveBeenCalled();
  });

  it('should show results count when provided', () => {
    component.resultsCount = 5;
    component.searchValue = 'test';
    fixture.detectChanges();

    const resultsInfo = fixture.debugElement.query(By.css('.search-results-info'));
    expect(resultsInfo).toBeTruthy();
    expect(resultsInfo.nativeElement.textContent).toContain('5 components found');
  });

  it('should show singular form for single result', () => {
    component.resultsCount = 1;
    component.searchValue = 'test';
    fixture.detectChanges();

    const resultsInfo = fixture.debugElement.query(By.css('.search-results-info'));
    expect(resultsInfo.nativeElement.textContent).toContain('1 component found');
  });

  it('should not show results count when search is empty', () => {
    component.resultsCount = 5;
    component.searchValue = '';
    fixture.detectChanges();

    const resultsInfo = fixture.debugElement.query(By.css('.search-results-info'));
    expect(resultsInfo).toBeFalsy();
  });

  it('should handle distinct search values correctly', fakeAsync(() => {
    spyOn(component.searchChange, 'emit');

    // Type same value twice
    component.onSearchChange('test');
    tick(300);
    component.onSearchChange('test');
    tick(300);

    // Should only emit once due to distinctUntilChanged
    expect(component.searchChange.emit).toHaveBeenCalledTimes(1);
  }));

  it('should trim search values', fakeAsync(() => {
    spyOn(component.searchChange, 'emit');

    component.onSearchChange('  test  ');
    tick(300);

    expect(component.searchChange.emit).toHaveBeenCalledWith('test');
  }));

  it('should handle empty search correctly', fakeAsync(() => {
    spyOn(component.searchChange, 'emit');

    component.onSearchChange('   ');
    tick(300);

    expect(component.searchChange.emit).toHaveBeenCalledWith('');
  }));

  it('should clean up subscriptions on destroy', () => {
    spyOn(component['destroy$'], 'next');
    spyOn(component['destroy$'], 'complete');

    component.ngOnDestroy();

    expect(component['destroy$'].next).toHaveBeenCalled();
    expect(component['destroy$'].complete).toHaveBeenCalled();
  });

  describe('Accessibility', () => {
    it('should have proper aria-label for clear button', () => {
      component.searchValue = 'test';
      fixture.detectChanges();

      const clearButton = fixture.debugElement.query(By.css('.clear-button'));
      expect(clearButton.nativeElement.getAttribute('aria-label')).toBe('Clear search');
    });

    it('should have proper button type for clear button', () => {
      component.searchValue = 'test';
      fixture.detectChanges();

      const clearButton = fixture.debugElement.query(By.css('.clear-button'));
      expect(clearButton.nativeElement.type).toBe('button');
    });
  });

  describe('Responsive Design', () => {
    it('should apply responsive classes', () => {
      const searchContainer = fixture.debugElement.query(By.css('.search-container'));
      expect(searchContainer).toBeTruthy();
      
      // Check that the component has responsive styling
      const styles = getComputedStyle(searchContainer.nativeElement);
      expect(styles).toBeTruthy();
    });
  });
});
