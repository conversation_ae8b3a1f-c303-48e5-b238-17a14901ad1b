import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';

@Component({
  selector: 'app-component-search',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="search-container">
      <div class="search-input-wrapper">
        <input
          type="text"
          [(ngModel)]="searchValue"
          (ngModelChange)="onSearchChange($event)"
          [placeholder]="placeholder"
          class="search-input"
          [class.has-value]="searchValue"
          #searchInput>
        
        <div class="search-icon" *ngIf="!searchValue">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="11" cy="11" r="8"></circle>
            <path d="m21 21-4.35-4.35"></path>
          </svg>
        </div>
        
        <button
          *ngIf="searchValue"
          (click)="clearSearch()"
          class="clear-button"
          type="button"
          aria-label="Clear search">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      
      <div class="search-results-info" *ngIf="searchValue && resultsCount !== null">
        <span class="results-count">
          {{ resultsCount }} {{ resultsCount === 1 ? 'component' : 'components' }} found
        </span>
      </div>
    </div>
  `,
  styles: [`
    .search-container {
      margin-bottom: 1rem;
    }

    .search-input-wrapper {
      position: relative;
      display: flex;
      align-items: center;
    }

    .search-input {
      width: 100%;
      padding: 0.75rem 2.5rem 0.75rem 2.5rem;
      border: 1px solid #e5e7eb;
      border-radius: 0.5rem;
      font-size: 0.875rem;
      background-color: #f9fafb;
      transition: all 0.2s ease;
    }

    .search-input:focus {
      outline: none;
      border-color: #3b82f6;
      background-color: white;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .search-input.has-value {
      background-color: white;
      border-color: #d1d5db;
    }

    .search-icon {
      position: absolute;
      left: 0.75rem;
      color: #9ca3af;
      pointer-events: none;
      z-index: 1;
    }

    .clear-button {
      position: absolute;
      right: 0.75rem;
      background: none;
      border: none;
      color: #6b7280;
      cursor: pointer;
      padding: 0.25rem;
      border-radius: 0.25rem;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }

    .clear-button:hover {
      color: #374151;
      background-color: #f3f4f6;
    }

    .clear-button:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
    }

    .search-results-info {
      margin-top: 0.5rem;
      padding: 0 0.25rem;
    }

    .results-count {
      font-size: 0.75rem;
      color: #6b7280;
      font-weight: 500;
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .search-input {
        padding: 0.625rem 2.25rem 0.625rem 2.25rem;
        font-size: 0.875rem;
      }
      
      .search-icon {
        left: 0.625rem;
      }
      
      .clear-button {
        right: 0.625rem;
      }
    }
  `]
})
export class ComponentSearchComponent implements OnInit, OnDestroy {
  @Input() placeholder = 'Search components...';
  @Input() debounceTime = 300;
  @Input() resultsCount: number | null = null;
  
  @Output() searchChange = new EventEmitter<string>();
  @Output() searchClear = new EventEmitter<void>();

  searchValue = '';
  private destroy$ = new Subject<void>();
  private searchSubject = new Subject<string>();

  ngOnInit(): void {
    // Set up debounced search
    this.searchSubject
      .pipe(
        debounceTime(this.debounceTime),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(searchTerm => {
        this.searchChange.emit(searchTerm);
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Handle search input change
   */
  onSearchChange(value: string): void {
    this.searchValue = value;
    this.searchSubject.next(value.trim());
  }

  /**
   * Clear search input
   */
  clearSearch(): void {
    this.searchValue = '';
    this.searchSubject.next('');
    this.searchClear.emit();
  }

  /**
   * Focus the search input
   */
  focus(): void {
    // This would need a ViewChild reference to the input element
    // For now, we'll implement it when needed
  }
}
