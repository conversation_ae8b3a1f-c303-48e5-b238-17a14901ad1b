<div class="component-palette">
  <div class="palette-header">
    <h2 class="palette-title">Components</h2>
    <div class="registry-status" *ngIf="isLoading">
      <small class="status-text">{{ registryStatus }}</small>
    </div>
  </div>

  <!-- Loading state -->
  <div class="loading-state" *ngIf="isLoading">
    <div class="loading-spinner"></div>
    <p>Loading components from mobile-components library...</p>
  </div>

  <!-- Enhanced component palette content -->
  <div class="palette-content" *ngIf="!isLoading">
    <!-- Search component -->
    <app-component-search
      placeholder="Search components by name, category, or description..."
      [debounceTime]="300"
      [resultsCount]="filteredComponentCards.length"
      (searchChange)="onSearchChange($event)"
      (searchClear)="onSearchClear()">
    </app-component-search>

    <!-- Filter component -->
    <app-component-filter
      [categories]="filterCategories"
      [favoritesCount]="favoritesCount"
      [recentCount]="recentCount"
      [filterOptions]="filterOptions"
      (filterChange)="onFilterChange($event)">
    </app-component-filter>

    <!-- Component cards grid -->
    <div class="component-cards-container">
      <div class="component-cards-grid"
           cdkDropList
           id="palette-components"
           [cdkDropListData]="filteredComponentCards"
           [cdkDropListConnectedTo]="getPossibleDropTargets()">

        <app-component-card
          *ngFor="let component of filteredComponentCards; trackBy: trackByComponentType"
          [component]="component"
          [showBadges]="true"
          [connectedDropLists]="getPossibleDropTargets()"
          (dragStarted)="onDragStarted($event)"
          (dragEnded)="onDragEnded($event)"
          (cardClick)="onComponentCardClick($event)"
          (favoriteToggle)="onFavoriteToggle($event)">
        </app-component-card>
      </div>

      <!-- Empty state when no components match filters -->
      <div class="empty-state" *ngIf="filteredComponentCards.length === 0">
        <div class="empty-state-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
            <circle cx="11" cy="11" r="8"></circle>
            <path d="m21 21-4.35-4.35"></path>
          </svg>
        </div>
        <h3 class="empty-state-title">No components found</h3>
        <p class="empty-state-description">
          <span *ngIf="searchTerm">No components match "{{ searchTerm }}"</span>
          <span *ngIf="!searchTerm && (filterOptions.showFavoritesOnly || filterOptions.showRecentOnly || filterOptions.selectedCategories.length > 0)">
            No components match the selected filters
          </span>
          <span *ngIf="!searchTerm && !filterOptions.showFavoritesOnly && !filterOptions.showRecentOnly && filterOptions.selectedCategories.length === 0">
            No components available
          </span>
        </p>
        <div class="empty-state-actions">
          <button
            *ngIf="searchTerm"
            (click)="onSearchClear()"
            class="clear-search-button">
            Clear search
          </button>
          <button
            *ngIf="filterOptions.showFavoritesOnly || filterOptions.showRecentOnly || filterOptions.selectedCategories.length > 0"
            (click)="onFilterChange({ showFavoritesOnly: false, showRecentOnly: false, selectedCategories: [] })"
            class="clear-filters-button">
            Clear filters
          </button>
        </div>
      </div>
    </div>
  </div>
</div>