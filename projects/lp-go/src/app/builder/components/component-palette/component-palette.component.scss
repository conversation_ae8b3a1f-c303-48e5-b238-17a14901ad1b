.component-palette {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f8f9fa;
}

.palette-header {
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;

  h2 {
    margin: 0 0 0.5rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: #334155;
  }

  .registry-status {
    margin-bottom: 0.5rem;

    .status-text {
      color: #6b7280;
      font-style: italic;
      font-size: 0.875rem;
    }
  }
}

.search-container {
  position: relative;
  
  .search-input {
    width: 100%;
    padding: 0.5rem 2rem 0.5rem 0.5rem;
    border: 1px solid #cbd5e1;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    
    &:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.5);
    }
  }
  
  .clear-button {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 0.25rem;
    
    &:hover {
      color: #334155;
    }
  }
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  color: #6b7280;

  .loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  p {
    margin: 0;
    font-size: 0.875rem;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.category-tabs {
  display: flex;
  overflow-x: auto;
  border-bottom: 1px solid #e2e8f0;

  .category-tab {
    padding: 0.5rem 1rem;
    border: none;
    background: none;
    font-size: 0.875rem;
    color: #64748b;
    cursor: pointer;
    white-space: nowrap;

    .category-count {
      margin-left: 0.25rem;
      font-size: 0.75rem;
      color: #9ca3af;
    }

    &.active {
      color: #3b82f6;
      border-bottom: 2px solid #3b82f6;

      .category-count {
        color: #3b82f6;
      }
    }

    &:hover:not(.active) {
      color: #334155;

      .category-count {
        color: #6b7280;
      }
    }
  }
}

.palette-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0;
}

// Enhanced component cards container
.component-cards-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.component-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
  padding: 1rem 0;

  // Responsive adjustments
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  @media (min-width: 1200px) {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

// Empty state styles
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  color: #6b7280;
  min-height: 200px;

  .empty-state-icon {
    margin-bottom: 1rem;
    color: #d1d5db;
  }

  .empty-state-title {
    margin: 0 0 0.5rem 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
  }

  .empty-state-description {
    margin: 0 0 1.5rem 0;
    font-size: 0.875rem;
    color: #6b7280;
    max-width: 400px;
    line-height: 1.5;
  }

  .empty-state-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    justify-content: center;
  }

  .clear-search-button,
  .clear-filters-button {
    background-color: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: #e5e7eb;
      border-color: #9ca3af;
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
}

.category-section {
  margin-bottom: 1.5rem;
  
  h3 {
    margin: 0 0 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
  }
}

.component-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.75rem;
}

.component-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.25rem;
  background-color: white;
  cursor: grab;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #3b82f6;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    cursor: grabbing;
  }
}

.component-icon {
  margin-right: 0.75rem;
  
  .icon-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    background-color: #f1f5f9;
    border-radius: 0.25rem;
    color: #64748b;
    font-weight: 500;
  }
}

.component-info {
  flex: 1;
  
  h4 {
    margin: 0 0 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #334155;
  }
  
  p {
    margin: 0;
    font-size: 0.75rem;
    color: #64748b;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}

.empty-search {
  text-align: center;
  padding: 2rem 1rem;
  color: #64748b;
  
  p {
    margin-bottom: 1rem;
  }
  
  .clear-search-button {
    background-color: #f1f5f9;
    border: 1px solid #cbd5e1;
    border-radius: 0.25rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    color: #334155;
    cursor: pointer;
    
    &:hover {
      background-color: #e2e8f0;
    }
  }
}

// Drag preview styles
.component-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: white;
  border: 1px solid #22c55e; /* Green theme */
  border-radius: 0.25rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  padding: 1rem;
  width: 120px;
  z-index: 9999;
  
  .preview-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background-color: #dcfce7; /* Light green background */
    border-radius: 0.25rem;
    color: #22c55e; /* Green text */
    font-weight: 500;
    margin-bottom: 0.5rem;
  }
  
  .preview-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #334155;
  }
}

// Animation
.cdk-drag-preview {
  z-index: 9999 !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}
