<div class="component-tree">
  <div class="tree-header">
    <h3>Component Tree</h3>
    
    <div class="search-container">
      <input 
        type="text"
        [(ngModel)]="searchTerm"
        placeholder="Search components"
        class="search-input">
      <button 
        *ngIf="searchTerm"
        (click)="clearSearch()"
        class="clear-button">
        ✕
      </button>
    </div>
  </div>
  
  <div class="tree-content">
    <!-- Empty state when no components -->
    <div class="empty-tree" *ngIf="treeNodes.length === 0">
      <p>No components added yet</p>
      <p class="hint">Drag components from the palette to get started</p>
    </div>
    
    <!-- Component tree with click-to-add containers -->
    <div class="tree-nodes" *ngIf="treeNodes.length > 0">
      <!-- Root level components -->
      <div class="tree-list">
        <ng-container *ngFor="let node of filteredNodes">
          <!-- Root node -->
          <div 
            class="tree-node"
            [class.selected]="node.component.id === selectedComponentId"
            [class.container-node]="node.component.isContainer"
            [style.padding-left.px]="node.level * 20"
            (click)="selectComponent(node.component, $event)">
            
            <!-- Expand/collapse button for containers -->
            <button 
              *ngIf="node.children.length > 0 || node.component.isContainer"
              class="expand-button"
              (click)="toggleNode(node); $event.stopPropagation()">
              {{ node.isExpanded ? '▼' : '▶' }}
            </button>
            
            <!-- Component icon and name -->
            <div class="node-content">
              <span class="component-icon">
                {{ getComponentIcon(node.component.type) }}
              </span>
              <span class="component-name">
                {{ getComponentName(node.component) }}
              </span>
              
              <!-- Add to container button -->
              <button 
                *ngIf="node.component.isContainer" 
                class="add-to-container-button" 
                title="Add component to this container"
                (click)="showAddToContainerMenu(node.component.id, $event)">
                +
              </button>
            </div>
          </div>
          
          <!-- Child nodes -->
          <div *ngIf="node.component.isContainer" 
               class="children-container"
               [class.expanded]="node.isExpanded">
            
            <!-- Children -->
            <ng-container *ngFor="let childNode of node.children">
              <div 
                class="tree-node"
                [class.selected]="childNode.component.id === selectedComponentId"
                [class.container-node]="childNode.component.isContainer"
                [style.padding-left.px]="childNode.level * 20"
                (click)="selectComponent(childNode.component, $event)">
                
                <!-- Expand/collapse button for container children -->
                <button 
                  *ngIf="childNode.children.length > 0 || childNode.component.isContainer"
                  class="expand-button"
                  (click)="toggleNode(childNode); $event.stopPropagation()">
                  {{ childNode.isExpanded ? '▼' : '▶' }}
                </button>
                
                <!-- Child component icon and name -->
                <div class="node-content">
                  <span class="component-icon">
                    {{ getComponentIcon(childNode.component.type) }}
                  </span>
                  <span class="component-name">
                    {{ getComponentName(childNode.component) }}
                  </span>
                  
                  <!-- Add to container button -->
                  <button 
                    *ngIf="childNode.component.isContainer" 
                    class="add-to-container-button" 
                    title="Add component to this container"
                    (click)="showAddToContainerMenu(childNode.component.id, $event)">
                    +
                  </button>
                </div>
              </div>
              
              <!-- Nested child container -->
              <div *ngIf="childNode.component.isContainer" 
                   class="children-container"
                   [class.expanded]="childNode.isExpanded">
                
                <!-- Recursively render deeper nested children -->
                <ng-container *ngTemplateOutlet="recursiveTree; context: { nodes: childNode.children }"></ng-container>
              </div>
            </ng-container>
            
            <!-- Empty container message -->
            <div *ngIf="node.children.length === 0 && node.isExpanded" class="empty-container-message">
              Empty container. Click + to add components.
            </div>
          </div>
        </ng-container>
      </div>
    </div>
    
    <!-- Template for recursive tree rendering -->
    <ng-template #recursiveTree let-nodes="nodes">
      <ng-container *ngFor="let node of nodes">
        <div 
          class="tree-node"
          [class.selected]="node.component.id === selectedComponentId"
          [class.container-node]="node.component.isContainer"
          [style.padding-left.px]="node.level * 20"
          (click)="selectComponent(node.component, $event)">
          
          <button 
            *ngIf="node.children.length > 0 || node.component.isContainer"
            class="expand-button"
            (click)="toggleNode(node); $event.stopPropagation()">
            {{ node.isExpanded ? '▼' : '▶' }}
          </button>
          
          <div class="node-content">
            <span class="component-icon">
              {{ getComponentIcon(node.component.type) }}
            </span>
            <span class="component-name">
              {{ getComponentName(node.component) }}
            </span>
            
            <!-- Add to container button -->
            <button 
              *ngIf="node.component.isContainer" 
              class="add-to-container-button" 
              title="Add component to this container"
              (click)="showAddToContainerMenu(node.component.id, $event)">
              +
            </button>
          </div>
        </div>
        
        <!-- Nested child container -->
        <div *ngIf="node.component.isContainer" 
             class="children-container"
             [class.expanded]="node.isExpanded">
          
          <ng-container *ngTemplateOutlet="recursiveTree; context: { nodes: node.children }"></ng-container>
          
          <!-- Empty container message -->
          <div *ngIf="node.children.length === 0 && node.isExpanded" class="empty-container-message">
            Empty container. Click + to add components.
          </div>
        </div>
      </ng-container>
    </ng-template>
  </div>
  
  <!-- Component menu popup -->
  <div *ngIf="showComponentMenu" 
       class="component-menu" 
       [style.top.px]="menuPosition.top" 
       [style.left.px]="menuPosition.left">
    <div class="menu-header">Add component to container</div>
    <div class="menu-items">
      <div class="menu-category" *ngFor="let category of componentCategories">
        <div class="category-name">{{ category.name }}</div>
        <div class="category-items">
          <button 
            *ngFor="let item of category.items" 
            class="menu-item"
            (click)="addComponentToContainer(item.type, item.properties)">
            <span class="item-icon">{{ item.icon.charAt(0) }}</span>
            <span class="item-name">{{ item.label }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
