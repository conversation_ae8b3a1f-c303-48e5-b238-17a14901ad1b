.component-tree {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f9fafb;
  border-right: 1px solid #e2e8f0;
  overflow: hidden;
  font-size: 14px;
  position: relative;
  
  .tree-header {
    padding: 16px;
    border-bottom: 1px solid #e2e8f0;
    
    h3 {
      margin: 0 0 12px 0;
      font-weight: 600;
      color: #1a202c;
    }
    
    .search-container {
      position: relative;
      
      .search-input {
        width: 100%;
        padding: 8px 32px 8px 12px;
        border: 1px solid #cbd5e0;
        border-radius: 4px;
        font-size: 14px;
        outline: none;
        
        &:focus {
          border-color: #4299e1;
          box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
        }
      }
      
      .clear-button {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #a0aec0;
        cursor: pointer;
        
        &:hover {
          color: #718096;
        }
      }
    }
  }
  
  .tree-content {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    
    .empty-tree {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      min-height: 200px;
      opacity: 0.7;
      
      p {
        margin: 4px 0;
        color: #718096;
        
        &.hint {
          font-size: 13px;
          color: #a0aec0;
        }
      }
    }
    
    .tree-nodes {
      .tree-list {
        min-height: 40px;
      }
      
      .tree-node {
        display: flex;
        align-items: center;
        padding: 8px 8px 8px 4px;
        cursor: pointer;
        border-radius: 4px;
        transition: background-color 0.2s ease;
        margin-bottom: 2px;
        user-select: none;
        position: relative;
        
        &:hover {
          background-color: #edf2f7;
        }
        
        &.selected {
          background-color: #ebf8ff;
          border-left: 2px solid #4299e1;
        }
        
        &.container-node {
          &:hover {
            .add-to-container-button {
              opacity: 1;
              transform: scale(1.2);
            }
          }
        }
        
        .expand-button {
          margin-right: 8px;
          background: none;
          border: none;
          color: #718096;
          cursor: pointer;
          padding: 2px;
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
          outline: none;
          
          &:hover {
            color: #2d3748;
          }
        }
        
        .node-content {
          display: flex;
          align-items: center;
          flex: 1;
          
          .component-icon {
            margin-right: 8px;
            color: #4a5568;
            font-weight: bold;
          }
          
          .component-name {
            flex: 1;
            color: #2d3748;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
          }
          
          .add-to-container-button {
            margin-left: 8px;
            width: 18px;
            height: 18px;
            background-color: #10b981;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            opacity: 0.7;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            outline: none;
            
            &:hover {
              opacity: 1;
              transform: scale(1.2);
            }
            
            &:active {
              transform: scale(0.95);
            }
          }
        }
      }
      
      .children-container {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
        
        &.expanded {
          max-height: 1000px;
        }
      }
      
      .empty-container-message {
        padding: 8px;
        color: #a0aec0;
        text-align: center;
        font-style: italic;
        border: 1px dashed #e2e8f0;
        border-radius: 4px;
        margin: 4px 0;
      }
    }
  }
  
  // Component menu popup
  .component-menu {
    position: fixed; // Changed from absolute to fixed
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 280px;
    max-height: 400px;
    overflow-y: auto;
    z-index: 10000; // Increased z-index to ensure it's visible
    border: 1px solid #e2e8f0;
    
    .menu-header {
      padding: 12px 16px;
      font-weight: 600;
      color: #2d3748;
      border-bottom: 1px solid #e2e8f0;
      position: sticky;
      top: 0;
      background-color: white;
      border-radius: 8px 8px 0 0;
    }
    
    .menu-items {
      padding: 8px;
      
      .menu-category {
        margin-bottom: 12px;
        
        .category-name {
          font-size: 12px;
          font-weight: 600;
          color: #718096;
          margin-bottom: 4px;
          padding: 0 8px;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
        
        .category-items {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 8px;
          
          .menu-item {
            display: flex;
            align-items: center;
            padding: 8px;
            background-color: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 13px;
            color: #4a5568;
            outline: none;
            
            &:hover {
              background-color: #edf2f7;
              border-color: #cbd5e0;
            }
            
            &:active {
              transform: scale(0.98);
            }
            
            .item-icon {
              margin-right: 8px;
              font-weight: bold;
              color: #4a5568;
            }
            
            .item-name {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }
}
