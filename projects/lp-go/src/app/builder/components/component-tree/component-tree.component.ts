import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDete<PERSON><PERSON><PERSON>, HostListener } from '@angular/core';
import { CommonModule, NgF<PERSON>, Ng<PERSON>f, NgTemplateOutlet } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { ComponentStoreService } from '../../services/component-store.service';
import { BuilderComponent } from '../../models/builder-component.interface';

// Define tree node structure
interface TreeNode {
  component: BuilderComponent;
  isExpanded: boolean;
  level: number;
  children: TreeNode[];
}

// Component menu position
interface MenuPosition {
  top: number;
  left: number;
}

// Menu item structure
interface ComponentMenuItem {
  type: string;
  label: string;
  icon: string;
  properties?: Record<string, any>;
}

// Menu category
interface ComponentMenuCategory {
  name: string;
  items: ComponentMenuItem[];
}

@Component({
  selector: 'app-component-tree',
  templateUrl: './component-tree.component.html',
  styleUrls: ['./component-tree.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NgFor,
    NgIf,
    NgTemplateOutlet
  ]
})
export class ComponentTreeComponent implements OnInit, OnDestroy {
  // Component tree data
  treeNodes: TreeNode[] = [];
  filteredNodes: TreeNode[] = [];
  
  // Selected component ID
  selectedComponentId: string | null = null;
  
  // Search functionality
  searchTerm = '';
  
  // Component menu
  showComponentMenu = false;
  menuPosition: MenuPosition = { top: 0, left: 0 };
  selectedContainerId: string | null = null;
  
  // Component categories for the menu
  componentCategories: ComponentMenuCategory[] = [
    {
      name: 'Layout',
      items: [
        {
          type: 'container',
          label: 'Container',
          icon: '□',
          properties: {
            tailwindClasses: 'flex flex-col p-6 border-2 border-gray-300 rounded-lg bg-white w-full min-h-[200px] shadow-sm'
          }
        },
        {
          type: 'row',
          label: 'Row',
          icon: '↔',
          properties: {
            tailwindClasses: 'flex flex-row gap-4 w-full p-4 border border-gray-500 rounded-md bg-white'
          }
        },
        {
          type: 'column',
          label: 'Column',
          icon: '↕',
          properties: {
            tailwindClasses: 'flex flex-col gap-4 h-full p-4 border border-gray-500 rounded-md bg-white'
          }
        }
      ]
    },
    {
      name: 'Basic',
      items: [
        {
          type: 'text',
          label: 'Text',
          icon: 'T',
          properties: {
            tailwindClasses: 'text-gray-800 text-base',
            content: 'Text component'
          }
        },
        {
          type: 'heading',
          label: 'Heading',
          icon: 'H',
          properties: {
            tailwindClasses: 'text-gray-900 text-2xl font-bold',
            content: 'Heading'
          }
        },
        {
          type: 'button',
          label: 'Button',
          icon: 'B',
          properties: {
            tailwindClasses: 'px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600',
            text: 'Button'
          }
        }
      ]
    }
  ];
  
  // RxJS cleanup
  private destroy$ = new Subject<void>();
  
  constructor(
    private componentStore: ComponentStoreService,
    private cdr: ChangeDetectorRef
  ) {}
  
  ngOnInit(): void {
    this.subscribeToComponentChanges();
    this.subscribeToSelectedComponent();
  }
  
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
  
  /**
   * Subscribe to component changes and rebuild tree
   */
  private subscribeToComponentChanges(): void {
    this.componentStore.getAllComponents().pipe(
      takeUntil(this.destroy$)
    ).subscribe((components: Map<string, BuilderComponent>) => {
      // Convert Map to array
      const componentsArray = Array.from(components.values());
      this.buildComponentTree(componentsArray);
      this.applySearch();
      this.cdr.detectChanges();
    });
  }
  
  /**
   * Subscribe to selected component changes
   */
  private subscribeToSelectedComponent(): void {
    this.componentStore.getSelectedComponentId().pipe(
      takeUntil(this.destroy$)
    ).subscribe(id => {
      this.selectedComponentId = id;
      
      // Auto-expand parents of selected component
      if (id) {
        this.expandParentsOfComponent(id);
      }
      
      this.cdr.detectChanges();
    });
  }
  
  /**
   * Build the component tree structure
   */
  private buildComponentTree(components: BuilderComponent[]): void {
    // Reset tree nodes
    this.treeNodes = [];
    
    // Create a map for quick component lookup
    const componentMap = new Map<string, BuilderComponent>();
    components.forEach(component => {
      componentMap.set(component.id, component);
    });
    
    // Find root components (no parent)
    const rootComponents = components.filter(component => !component.parentId);
    
    // Build tree recursively
    this.treeNodes = rootComponents.map(component => this.createNode(component, componentMap, 0));
  }
  
  /**
   * Create a tree node and its children recursively
   */
  private createNode(component: BuilderComponent, componentMap: Map<string, BuilderComponent>, level: number): TreeNode {
    const node: TreeNode = {
      component,
      isExpanded: this.isComponentExpanded(component.id),
      level,
      children: []
    };
    
    // Add children if this is a container
    if (component.children && component.children.length > 0) {
      component.children.forEach(childId => {
        const childComponent = componentMap.get(childId);
        if (childComponent) {
          node.children.push(this.createNode(childComponent, componentMap, level + 1));
        }
      });
    }
    
    return node;
  }
  
  /**
   * Check if a component should be expanded
   * (keeps expansion state when tree is rebuilt)
   */
  private isComponentExpanded(componentId: string): boolean {
    // Find node in existing tree
    const findNode = (nodes: TreeNode[]): TreeNode | undefined => {
      for (const node of nodes) {
        if (node.component.id === componentId) {
          return node;
        }
        
        const childResult = findNode(node.children);
        if (childResult) {
          return childResult;
        }
      }
      
      return undefined;
    };
    
    const existingNode = findNode(this.treeNodes);
    return existingNode ? existingNode.isExpanded : false;
  }
  
  /**
   * Expand parents of a selected component
   */
  private expandParentsOfComponent(componentId: string): void {
    // Find the component's parent chain
    const findParentChain = (nodes: TreeNode[], targetId: string, chain: TreeNode[] = []): TreeNode[] | null => {
      for (const node of nodes) {
        // Check if this node is the target
        if (node.component.id === targetId) {
          return chain;
        }
        
        // Check if the target is in this node's children
        for (const child of node.children) {
          if (child.component.id === targetId) {
            return [...chain, node];
          }
        }
        
        // Recurse deeper
        const result = findParentChain(node.children, targetId, [...chain, node]);
        if (result) {
          return result;
        }
      }
      
      return null;
    };
    
    const parentChain = findParentChain(this.treeNodes, componentId);
    
    if (parentChain) {
      // Expand all parents in the chain
      parentChain.forEach(node => {
        node.isExpanded = true;
      });
    }
  }
  
  /**
   * Toggle node expansion
   */
  toggleNode(node: TreeNode): void {
    node.isExpanded = !node.isExpanded;
  }
  
  /**
   * Select a component
   */
  selectComponent(component: BuilderComponent, event: MouseEvent): void {
    event.stopPropagation();
    this.componentStore.selectComponent(component.id);
  }
  
  /**
   * Apply search filter to tree
   */
  applySearch(): void {
    if (!this.searchTerm) {
      this.filteredNodes = [...this.treeNodes];
      return;
    }
    
    const searchLower = this.searchTerm.toLowerCase();
    
    // Helper to check if node or its descendants match
    const nodeMatches = (node: TreeNode): boolean => {
      // Check if this node matches
      const nameMatches = this.getComponentName(node.component).toLowerCase().includes(searchLower);
      const typeMatches = node.component.type.toLowerCase().includes(searchLower);
      
      if (nameMatches || typeMatches) {
        return true;
      }
      
      // Check if any child matches
      for (const child of node.children) {
        if (nodeMatches(child)) {
          return true;
        }
      }
      
      return false;
    };
    
    // Filter top-level nodes based on matches
    this.filteredNodes = this.treeNodes.filter(nodeMatches);
    
    // Expand all nodes when searching
    this.expandAllMatchingNodes(this.filteredNodes);
  }
  
  /**
   * Expand all nodes that match search or have matching descendants
   */
  private expandAllMatchingNodes(nodes: TreeNode[]): void {
    for (const node of nodes) {
      node.isExpanded = true;
      this.expandAllMatchingNodes(node.children);
    }
  }
  
  /**
   * Clear search input
   */
  clearSearch(): void {
    this.searchTerm = '';
    this.applySearch();
  }
  
  /**
   * Show the add component menu for a container
   */
  showAddToContainerMenu(containerId: string, event: MouseEvent): void {
    // Important: Stop the event from propagating to prevent other handlers
    event.stopPropagation();
    
    console.log('Showing menu for container:', containerId);
    
    // Store the container ID
    this.selectedContainerId = containerId;
    
    // Calculate position (slightly offset from click)
    const rect = (event.target as HTMLElement).getBoundingClientRect();
    
    // Get viewport dimensions
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;
    
    // Menu dimensions
    const menuWidth = 280;
    const menuHeight = 400; // Approximate max height of menu
    
    // Calculate initial position
    let leftPos = rect.left - menuWidth + 20;
    let topPos = rect.bottom + 5;
    
    // Adjust horizontal position if needed
    if (leftPos < 10) {
      // If off screen to the left, position to the right of the button
      leftPos = rect.right + 10;
    }
    
    // Make sure it stays within right edge
    if (leftPos + menuWidth > viewportWidth - 10) {
      leftPos = viewportWidth - menuWidth - 10;
    }
    
    // Adjust vertical position if needed
    if (topPos + menuHeight > viewportHeight - 10) {
      // If it would go below viewport, position above the button
      topPos = rect.top - menuHeight - 5;
      
      // If that would go above viewport, position it at the top with some margin
      if (topPos < 10) {
        topPos = 10;
      }
    }
    
    this.menuPosition = {
      top: topPos,
      left: leftPos
    };
    
    // Show the menu
    this.showComponentMenu = true;
    this.cdr.detectChanges();
    
    console.log('Menu shown:', this.showComponentMenu, 'at', this.menuPosition);
    
    // Ensure the menu is visible by checking if we need to scroll
    setTimeout(() => {
      const menuElement = document.querySelector('.component-menu');
      if (menuElement) {
        const menuRect = menuElement.getBoundingClientRect();
        if (menuRect.bottom > viewportHeight) {
          window.scrollBy({
            top: menuRect.bottom - viewportHeight + 20,
            behavior: 'smooth'
          });
        }
      }
    }, 0);
  }
  
  /**
   * Hide the component menu
   */
  hideComponentMenu(): void {
    console.log('Hiding component menu');
    this.showComponentMenu = false;
    this.selectedContainerId = null;
    this.cdr.detectChanges();
  }
  
  /**
   * Add a component to the selected container
   */
  addComponentToContainer(componentType: string, properties?: Record<string, any>): void {
    if (!this.selectedContainerId) {
      return;
    }
    
    // Create a new component
    const newComponent: Partial<BuilderComponent> = {
      type: componentType,
      properties: {
        ...(properties || {}),
        tailwindClasses: properties?.['tailwindClasses'] || ''
      }
    };
    
    // Add to store with this container as parent
    const newId = this.componentStore.addComponentSync(newComponent, this.selectedContainerId);
    console.log(`Added ${componentType} to container ${this.selectedContainerId} with ID ${newId}`);

    // Force refresh the container to ensure the new component is displayed
    this.componentStore.refreshContainerChildren(this.selectedContainerId);

    // Auto-expand the container
    this.expandContainer(this.selectedContainerId);

    // Hide the menu
    this.hideComponentMenu();
  }
  
  /**
   * Auto-expand a container
   */
  private expandContainer(containerId: string): void {
    const findNode = (nodes: TreeNode[]): TreeNode | null => {
      for (const node of nodes) {
        if (node.component.id === containerId) {
          return node;
        }
        
        if (node.children.length > 0) {
          const result = findNode(node.children);
          if (result) {
            return result;
          }
        }
      }
      
      return null;
    };
    
    const containerNode = findNode(this.treeNodes);
    if (containerNode) {
      containerNode.isExpanded = true;
      this.cdr.detectChanges();
    }
  }
  
  /**
   * Close menu when clicking outside
   */
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    // Check if click was on an add button or the menu
    const isAddButton = (event.target as HTMLElement).classList.contains('add-to-container-button');
    const menuElement = document.querySelector('.component-menu');
    const isOnMenu = menuElement && menuElement.contains(event.target as Node);
    
    // If menu is open and click wasn't on the menu or add button
    if (this.showComponentMenu && !isAddButton && !isOnMenu) {
      console.log('Click outside detected, hiding menu');
      this.hideComponentMenu();
    }
  }
  
  /**
   * Get a component icon based on its type
   */
  getComponentIcon(type: string): string {
    switch (type) {
      case 'container':
        return '□';
      case 'row':
        return '↔';
      case 'column':
        return '↕';
      case 'text':
        return 'T';
      case 'heading':
        return 'H';
      case 'button':
        return 'B';
      case 'image':
        return 'I';
      default:
        return '○';
    }
  }
  
  /**
   * Get a friendly name for a component
   */
  getComponentName(component: BuilderComponent): string {
    // If the component has content, use that for text components
    if (component.type === 'text' || component.type === 'heading') {
      const content = component.properties?.content;
      if (content && typeof content === 'string') {
        // Truncate to 20 chars
        const truncated = content.length > 20 
          ? content.substring(0, 20) + '...' 
          : content;
        return truncated;
      }
    }
    
    // For buttons, use button text
    if (component.type === 'button') {
      const buttonText = component.properties?.['text'];
      if (buttonText && typeof buttonText === 'string') {
        return buttonText;
      }
    }
    
    // Default to the label or type + ID
    return component.label || `${component.type} ${component.id.substring(0, 4)}`;
  }
}
