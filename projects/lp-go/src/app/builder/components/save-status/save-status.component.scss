.save-status-container {
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  border: 1px solid transparent;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
  
  &.saving {
    background-color: #fef3c7;
    border-color: #f59e0b;
    color: #92400e;
    
    .status-icon {
      animation: pulse 1.5s infinite;
    }
  }
  
  &.saved {
    background-color: #d1fae5;
    border-color: #10b981;
    color: #065f46;
  }
  
  &.dirty {
    background-color: #fef2f2;
    border-color: #f87171;
    color: #991b1b;
  }
  
  &.error {
    background-color: #fef2f2;
    border-color: #ef4444;
    color: #dc2626;
  }
  
  &.clean {
    background-color: #f9fafb;
    border-color: #d1d5db;
    color: #6b7280;
  }
}

.status-icon {
  font-size: 1rem;
  line-height: 1;
}

.status-text {
  font-weight: 500;
}

.last-saved {
  font-size: 0.75rem;
  opacity: 0.8;
  margin-left: 0.25rem;
}

.expand-icon {
  margin-left: auto;
  font-size: 0.75rem;
  opacity: 0.6;
  transition: transform 0.2s ease;
}

.details-panel {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1000;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  min-width: 280px;
  margin-top: 0.5rem;
  
  &::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 1rem;
    width: 12px;
    height: 12px;
    background: white;
    border: 1px solid #e5e7eb;
    border-bottom: none;
    border-right: none;
    transform: rotate(45deg);
  }
}

.save-actions {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  
  button {
    flex: 1;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid;
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    &.primary {
      background: #3b82f6;
      color: white;
      border-color: #3b82f6;
      
      &:hover:not(:disabled) {
        background: #2563eb;
      }
    }
    
    &.secondary {
      background: white;
      color: #374151;
      border-color: #d1d5db;
      
      &:hover:not(:disabled) {
        background: #f9fafb;
      }
    }
  }
}

.auto-save-section {
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.auto-save-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  
  input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    accent-color: #3b82f6;
  }
  
  .toggle-text {
    color: #374151;
  }
}

.status-details {
  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
    font-size: 0.875rem;
    
    &.error {
      color: #dc2626;
    }
    
    .detail-label {
      font-weight: 500;
      color: #6b7280;
    }
    
    .detail-value {
      color: #374151;
      text-align: right;
      max-width: 60%;
      word-break: break-word;
      
      &.saving {
        color: #92400e;
      }
      
      &.saved {
        color: #065f46;
      }
      
      &.dirty {
        color: #991b1b;
      }
      
      &.error {
        color: #dc2626;
      }
      
      &.clean {
        color: #6b7280;
      }
    }
  }
}

.progress-section {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: #3b82f6;
  border-radius: 2px;
  animation: progress 2s infinite;
}

.progress-text {
  font-size: 0.75rem;
  color: #6b7280;
  text-align: center;
  display: block;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes progress {
  0% {
    width: 0%;
    margin-left: 0%;
  }
  50% {
    width: 75%;
    margin-left: 25%;
  }
  100% {
    width: 0%;
    margin-left: 100%;
  }
}
