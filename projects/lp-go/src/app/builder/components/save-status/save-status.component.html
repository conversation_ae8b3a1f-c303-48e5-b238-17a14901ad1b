<div class="save-status-container">
  <!-- Main Status Indicator -->
  <div 
    class="status-indicator"
    [class]="statusClass"
    [title]="getStatusTooltip()"
    (click)="toggleDetails()">
    
    <span class="status-icon">{{ statusIcon }}</span>
    <span class="status-text">{{ statusText }}</span>
    
    <!-- Last saved time -->
    <span class="last-saved" *ngIf="lastSavedText && statusClass === 'saved'">
      {{ lastSavedText }}
    </span>
    
    <!-- Expand/collapse icon -->
    <span class="expand-icon" *ngIf="persistenceState">
      {{ showDetails ? '▼' : '▶' }}
    </span>
  </div>

  <!-- Details Panel -->
  <div class="details-panel" *ngIf="showDetails && persistenceState">
    
    <!-- Save Actions -->
    <div class="save-actions">
      <button 
        class="save-btn primary"
        [disabled]="!canSave()"
        (click)="save()"
        title="Save changes now">
        💾 Save Now
      </button>
      
      <button 
        class="backup-btn secondary"
        (click)="createBackup()"
        title="Create backup">
        📦 Backup
      </button>
    </div>

    <!-- Auto-save Toggle -->
    <div class="auto-save-section">
      <label class="auto-save-toggle">
        <input 
          type="checkbox"
          [checked]="isAutoSaveEnabled()"
          (change)="toggleAutoSave()">
        <span class="toggle-text">{{ getAutoSaveStatus() }}</span>
      </label>
    </div>

    <!-- Status Details -->
    <div class="status-details">
      
      <!-- Last Saved -->
      <div class="detail-item" *ngIf="persistenceState.lastSaved">
        <span class="detail-label">Last Saved:</span>
        <span class="detail-value">{{ persistenceState.lastSaved | date:'medium' }}</span>
      </div>

      <!-- Pending Changes -->
      <div class="detail-item" *ngIf="persistenceState.hasUnsavedChanges">
        <span class="detail-label">Pending Changes:</span>
        <span class="detail-value">{{ getPendingChangesCount() || 'Yes' }}</span>
      </div>

      <!-- Error Details -->
      <div class="detail-item error" *ngIf="persistenceState.lastError">
        <span class="detail-label">Error:</span>
        <span class="detail-value">{{ getErrorDetails() }}</span>
      </div>

      <!-- Save Status -->
      <div class="detail-item">
        <span class="detail-label">Status:</span>
        <span class="detail-value" [class]="statusClass">
          {{ statusText }}
        </span>
      </div>
    </div>

    <!-- Progress Indicator -->
    <div class="progress-section" *ngIf="persistenceState.isSaving">
      <div class="progress-bar">
        <div class="progress-fill"></div>
      </div>
      <span class="progress-text">Saving configuration...</span>
    </div>

  </div>
</div>
