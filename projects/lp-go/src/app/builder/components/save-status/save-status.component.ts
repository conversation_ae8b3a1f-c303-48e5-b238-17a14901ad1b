import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil, combineLatest } from 'rxjs';

import { BuilderConfigService } from '../../services/builder-config.service';
import { PersistenceState } from '../../services/configuration-persistence.service';

@Component({
  selector: 'app-save-status',
  templateUrl: './save-status.component.html',
  styleUrls: ['./save-status.component.scss'],
  standalone: true,
  imports: [CommonModule]
})
export class SaveStatusComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // State
  persistenceState: PersistenceState | null = null;
  isDirty = false;
  isLoading = false;

  // UI state
  showDetails = false;
  lastSavedText = '';
  statusIcon = '';
  statusText = '';
  statusClass = '';

  constructor(private configService: BuilderConfigService) {}

  ngOnInit(): void {
    this.subscribeToState();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Subscribe to configuration and persistence state
   */
  private subscribeToState(): void {
    // Combine persistence state and dirty state
    combineLatest([
      this.configService.getPersistenceState(),
      this.configService.getIsDirty(),
      this.configService.getIsLoading()
    ]).pipe(
      takeUntil(this.destroy$)
    ).subscribe(([persistenceState, isDirty, isLoading]) => {
      this.persistenceState = persistenceState;
      this.isDirty = isDirty;
      this.isLoading = isLoading;
      
      this.updateUI();
    });
  }

  /**
   * Update UI based on current state
   */
  private updateUI(): void {
    if (this.isLoading || this.persistenceState?.isSaving) {
      this.statusIcon = '⏳';
      this.statusText = 'Saving...';
      this.statusClass = 'saving';
    } else if (this.persistenceState?.lastError) {
      this.statusIcon = '❌';
      this.statusText = 'Save failed';
      this.statusClass = 'error';
    } else if (this.isDirty || this.persistenceState?.hasUnsavedChanges) {
      this.statusIcon = '●';
      this.statusText = 'Unsaved changes';
      this.statusClass = 'dirty';
    } else if (this.persistenceState?.lastSaved) {
      this.statusIcon = '✓';
      this.statusText = 'Saved';
      this.statusClass = 'saved';
      this.updateLastSavedText();
    } else {
      this.statusIcon = '○';
      this.statusText = 'No changes';
      this.statusClass = 'clean';
    }
  }

  /**
   * Update last saved text with relative time
   */
  private updateLastSavedText(): void {
    if (!this.persistenceState?.lastSaved) {
      this.lastSavedText = '';
      return;
    }

    const now = new Date();
    const saved = new Date(this.persistenceState.lastSaved);
    const diffMs = now.getTime() - saved.getTime();
    const diffMinutes = Math.floor(diffMs / 60000);
    const diffSeconds = Math.floor((diffMs % 60000) / 1000);

    if (diffMinutes > 0) {
      this.lastSavedText = `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    } else if (diffSeconds > 5) {
      this.lastSavedText = `${diffSeconds} seconds ago`;
    } else {
      this.lastSavedText = 'Just now';
    }
  }

  /**
   * Toggle details panel
   */
  toggleDetails(): void {
    this.showDetails = !this.showDetails;
  }

  /**
   * Manual save trigger
   */
  save(): void {
    this.configService.saveCurrentState().subscribe({
      next: (success) => {
        if (success) {
          console.log('[SaveStatus] Manual save successful');
        }
      },
      error: (error) => {
        console.error('[SaveStatus] Manual save failed:', error);
      }
    });
  }

  /**
   * Toggle auto-save
   */
  toggleAutoSave(): void {
    if (this.persistenceState) {
      this.configService.setAutoSaveEnabled(!this.persistenceState.autoSaveEnabled);
    }
  }

  /**
   * Create backup
   */
  createBackup(): void {
    this.configService.createBackup().subscribe({
      next: (backupId) => {
        console.log('[SaveStatus] Backup created:', backupId);
      },
      error: (error) => {
        console.error('[SaveStatus] Backup failed:', error);
      }
    });
  }

  /**
   * Get error details for display
   */
  getErrorDetails(): string {
    return this.persistenceState?.lastError || 'Unknown error';
  }

  /**
   * Get pending changes count
   */
  getPendingChangesCount(): number {
    // This would need to be implemented in the persistence service
    return 0;
  }

  /**
   * Get status tooltip
   */
  getStatusTooltip(): string {
    if (this.isLoading || this.persistenceState?.isSaving) {
      return 'Saving configuration...';
    } else if (this.persistenceState?.lastError) {
      return `Save failed: ${this.persistenceState.lastError}`;
    } else if (this.isDirty || this.persistenceState?.hasUnsavedChanges) {
      return 'You have unsaved changes';
    } else if (this.persistenceState?.lastSaved) {
      return `Last saved: ${this.lastSavedText}`;
    } else {
      return 'No changes to save';
    }
  }

  /**
   * Check if save button should be enabled
   */
  canSave(): boolean {
    return (this.isDirty || (this.persistenceState?.hasUnsavedChanges ?? false)) &&
           !this.isLoading &&
           !(this.persistenceState?.isSaving ?? false);
  }

  /**
   * Check if auto-save is enabled
   */
  isAutoSaveEnabled(): boolean {
    return this.persistenceState?.autoSaveEnabled || false;
  }

  /**
   * Get auto-save status text
   */
  getAutoSaveStatus(): string {
    if (this.isAutoSaveEnabled()) {
      return 'Auto-save enabled';
    } else {
      return 'Auto-save disabled';
    }
  }
}
