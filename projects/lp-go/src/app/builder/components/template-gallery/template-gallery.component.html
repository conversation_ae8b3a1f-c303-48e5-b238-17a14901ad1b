<!-- Template Gallery Container -->
<div class="template-gallery" [style.max-height]="maxHeight">
  
  <!-- Header Section -->
  <div *ngIf="showHeader" class="gallery-header">
    <div class="header-top">
      <h2 class="gallery-title">Template Gallery</h2>
      <div class="view-controls">
        <button
          class="view-button"
          [class.active]="viewMode === 'grid'"
          (click)="changeViewMode('grid')"
          title="Grid View">
          <span class="icon">⊞</span>
        </button>
        <button
          class="view-button"
          [class.active]="viewMode === 'list'"
          (click)="changeViewMode('list')"
          title="List View">
          <span class="icon">☰</span>
        </button>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="filters-section">
      <!-- Search Bar -->
      <div class="search-bar">
        <input
          type="text"
          placeholder="Search templates..."
          [value]="searchQuery"
          (input)="onSearchInput($event)"
          class="search-input">
        <span class="search-icon">🔍</span>
      </div>

      <!-- Category Filter -->
      <div class="filter-group">
        <label class="filter-label">Category:</label>
        <select
          [value]="selectedCategory"
          (change)="onCategoryChange($event)"
          class="filter-select">
          <option value="">All Categories</option>
          <option *ngFor="let category of categories" [value]="category">
            {{ getCategoryDisplayName(category) }}
          </option>
        </select>
      </div>

      <!-- Sort Options -->
      <div class="filter-group">
        <label class="filter-label">Sort by:</label>
        <select
          [value]="sortBy"
          (change)="onSortByChange($event)"
          class="filter-select">
          <option value="name">Name</option>
          <option value="date">Date</option>
          <option value="popularity">Popularity</option>
          <option value="category">Category</option>
        </select>
        <button
          class="sort-direction-button"
          (click)="changeSortBy(sortBy)"
          [title]="sortDirection === 'asc' ? 'Sort Descending' : 'Sort Ascending'">
          {{ sortDirection === 'asc' ? '↑' : '↓' }}
        </button>
      </div>

      <!-- Filter Toggles -->
      <div class="filter-toggles">
        <label class="toggle-label">
          <input
            type="checkbox"
            [checked]="showFavoritesOnly"
            (change)="toggleFavoritesOnly()">
          <span class="toggle-text">Favorites Only</span>
        </label>
        <label class="toggle-label">
          <input
            type="checkbox"
            [checked]="showBuiltInOnly"
            (change)="toggleBuiltInOnly()">
          <span class="toggle-text">Built-in Only</span>
        </label>
      </div>

      <!-- Clear Filters -->
      <button
        class="clear-filters-button"
        (click)="clearFilters()"
        title="Clear all filters">
        Clear Filters
      </button>
    </div>

    <!-- Popular Tags -->
    <div class="tags-section" *ngIf="popularTags.length > 0">
      <label class="tags-label">Popular Tags:</label>
      <div class="tags-container">
        <button
          *ngFor="let tag of popularTags"
          class="tag-button"
          [class.selected]="isTagSelected(tag)"
          (click)="toggleTag(tag)">
          {{ tag }}
        </button>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-state">
    <div class="loading-spinner"></div>
    <p class="loading-text">Loading templates...</p>
  </div>

  <!-- Templates Grid/List -->
  <div *ngIf="!isLoading" class="templates-container" [class.grid-view]="viewMode === 'grid'" [class.list-view]="viewMode === 'list'">
    
    <!-- No Templates Message -->
    <div *ngIf="filteredTemplates.length === 0" class="no-templates">
      <p class="no-templates-text">No templates found matching your criteria.</p>
      <button class="clear-filters-button" (click)="clearFilters()">
        Clear Filters
      </button>
    </div>

    <!-- Template Items -->
    <div
      *ngFor="let template of filteredTemplates; trackBy: trackByTemplateId"
      class="template-item"
      [class.selected]="selectedTemplate?.id === template.id"
      (click)="selectTemplate(template)">
      
      <!-- Template Thumbnail -->
      <div class="template-thumbnail">
        <img
          [src]="template.thumbnail"
          [alt]="template.name + ' thumbnail'"
          class="thumbnail-image"
          loading="lazy">
        
        <!-- Favorite Button -->
        <button
          class="favorite-button"
          [class.favorited]="template.isFavorite"
          (click)="toggleFavorite(template, $event)"
          [title]="template.isFavorite ? 'Remove from favorites' : 'Add to favorites'">
          {{ template.isFavorite ? '★' : '☆' }}
        </button>

        <!-- Built-in Badge -->
        <span *ngIf="template.isBuiltIn" class="built-in-badge">Built-in</span>
      </div>

      <!-- Template Info -->
      <div class="template-info">
        <h3 class="template-name">{{ template.name }}</h3>
        <p class="template-description">{{ template.description }}</p>
        
        <!-- Template Meta -->
        <div class="template-meta">
          <span class="meta-item">
            <span class="meta-label">Category:</span>
            <span class="meta-value">{{ getCategoryDisplayName(template.category) }}</span>
          </span>
          <span class="meta-item">
            <span class="meta-label">Components:</span>
            <span class="meta-value">{{ getComponentCount(template) }}</span>
          </span>
          <span class="meta-item">
            <span class="meta-label">Updated:</span>
            <span class="meta-value">{{ formatDate(template.updatedAt) }}</span>
          </span>
        </div>

        <!-- Template Tags -->
        <div class="template-tags" *ngIf="template.tags.length > 0">
          <span
            *ngFor="let tag of template.tags"
            class="template-tag">
            {{ tag }}
          </span>
        </div>

        <!-- Template Actions -->
        <div class="template-actions">
          <button
            class="action-button primary"
            (click)="applyTemplate(template, { replaceContent: false }); $event.stopPropagation()"
            title="Add template to current page">
            Add to Page
          </button>
          <button
            class="action-button secondary"
            (click)="applyTemplate(template, { replaceContent: true }); $event.stopPropagation()"
            title="Replace current page with template">
            Replace Page
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Selected Template Details (if in selection mode) -->
  <div *ngIf="selectedTemplate && allowSelection" class="selected-template-details">
    <h3 class="details-title">Selected Template</h3>
    <div class="details-content">
      <h4 class="selected-name">{{ selectedTemplate.name }}</h4>
      <p class="selected-description">{{ selectedTemplate.description }}</p>
      
      <!-- Metadata -->
      <div class="selected-metadata" *ngIf="selectedTemplate.metadata">
        <div class="metadata-item" *ngIf="selectedTemplate.metadata.author">
          <strong>Author:</strong> {{ selectedTemplate.metadata.author }}
        </div>
        <div class="metadata-item" *ngIf="selectedTemplate.metadata.difficulty">
          <strong>Difficulty:</strong> {{ selectedTemplate.metadata.difficulty }}
        </div>
        <div class="metadata-item" *ngIf="selectedTemplate.metadata.estimatedTime">
          <strong>Setup Time:</strong> {{ selectedTemplate.metadata.estimatedTime }}
        </div>
      </div>

      <!-- Apply Actions -->
      <div class="apply-actions">
        <button
          class="action-button primary large"
          (click)="applyTemplate(selectedTemplate, { replaceContent: false })">
          Add to Current Page
        </button>
        <button
          class="action-button secondary large"
          (click)="applyTemplate(selectedTemplate, { replaceContent: true })">
          Replace Current Page
        </button>
      </div>
    </div>
  </div>
</div>
