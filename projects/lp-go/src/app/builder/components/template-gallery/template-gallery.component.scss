.template-gallery {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f8fafc;
  border-radius: 8px;
  overflow: hidden;

  .gallery-header {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    padding: 1rem;

    .header-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;

      .gallery-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1a202c;
        margin: 0;
      }

      .view-controls {
        display: flex;
        gap: 0.25rem;

        .view-button {
          padding: 0.5rem;
          border: 1px solid #e2e8f0;
          background: white;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            background: #f7fafc;
          }

          &.active {
            background: #3182ce;
            color: white;
            border-color: #3182ce;
          }

          .icon {
            font-size: 0.875rem;
          }
        }
      }
    }

    .filters-section {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      align-items: center;
      margin-bottom: 1rem;

      .search-bar {
        position: relative;
        flex: 1;
        min-width: 200px;

        .search-input {
          width: 100%;
          padding: 0.5rem 2rem 0.5rem 0.75rem;
          border: 1px solid #e2e8f0;
          border-radius: 4px;
          font-size: 0.875rem;

          &:focus {
            outline: none;
            border-color: #3182ce;
            box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
          }
        }

        .search-icon {
          position: absolute;
          right: 0.75rem;
          top: 50%;
          transform: translateY(-50%);
          color: #a0aec0;
          pointer-events: none;
        }
      }

      .filter-group {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .filter-label {
          font-size: 0.875rem;
          font-weight: 500;
          color: #4a5568;
          white-space: nowrap;
        }

        .filter-select {
          padding: 0.5rem;
          border: 1px solid #e2e8f0;
          border-radius: 4px;
          font-size: 0.875rem;
          background: white;

          &:focus {
            outline: none;
            border-color: #3182ce;
          }
        }

        .sort-direction-button {
          padding: 0.5rem;
          border: 1px solid #e2e8f0;
          background: white;
          border-radius: 4px;
          cursor: pointer;
          font-size: 0.875rem;

          &:hover {
            background: #f7fafc;
          }
        }
      }

      .filter-toggles {
        display: flex;
        gap: 1rem;

        .toggle-label {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-size: 0.875rem;
          color: #4a5568;
          cursor: pointer;

          input[type="checkbox"] {
            margin: 0;
          }

          .toggle-text {
            white-space: nowrap;
          }
        }
      }

      .clear-filters-button {
        padding: 0.5rem 1rem;
        border: 1px solid #e2e8f0;
        background: white;
        border-radius: 4px;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          background: #f7fafc;
        }
      }
    }

    .tags-section {
      .tags-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        color: #4a5568;
        margin-bottom: 0.5rem;
      }

      .tags-container {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;

        .tag-button {
          padding: 0.25rem 0.75rem;
          border: 1px solid #e2e8f0;
          background: white;
          border-radius: 16px;
          font-size: 0.75rem;
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            background: #f7fafc;
          }

          &.selected {
            background: #3182ce;
            color: white;
            border-color: #3182ce;
          }
        }
      }
    }
  }

  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: #718096;

    .loading-spinner {
      width: 2rem;
      height: 2rem;
      border: 2px solid #e2e8f0;
      border-top: 2px solid #3182ce;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 1rem;
    }

    .loading-text {
      font-size: 0.875rem;
      margin: 0;
    }
  }

  .templates-container {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;

    &.grid-view {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 1rem;
    }

    &.list-view {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      .template-item {
        display: flex;
        align-items: center;
        padding: 1rem;

        .template-thumbnail {
          width: 80px;
          height: 60px;
          margin-right: 1rem;
          flex-shrink: 0;
        }

        .template-info {
          flex: 1;

          .template-name {
            font-size: 1rem;
            margin-bottom: 0.25rem;
          }

          .template-description {
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
          }

          .template-meta {
            display: flex;
            gap: 1rem;
            margin-bottom: 0.5rem;
          }

          .template-actions {
            display: flex;
            gap: 0.5rem;
          }
        }
      }
    }

    .no-templates {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 3rem;
      text-align: center;
      color: #718096;

      .no-templates-text {
        font-size: 1rem;
        margin-bottom: 1rem;
      }
    }

    .template-item {
      background: white;
      border-radius: 8px;
      border: 1px solid #e2e8f0;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        border-color: #cbd5e0;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }

      &.selected {
        border-color: #3182ce;
        box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
      }

      .template-thumbnail {
        position: relative;
        width: 100%;
        height: 160px;
        background: #f7fafc;

        .thumbnail-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .favorite-button {
          position: absolute;
          top: 0.5rem;
          right: 0.5rem;
          width: 2rem;
          height: 2rem;
          border: none;
          background: rgba(255, 255, 255, 0.9);
          border-radius: 50%;
          cursor: pointer;
          font-size: 1rem;
          color: #a0aec0;
          transition: all 0.2s;

          &:hover {
            background: white;
            color: #f6ad55;
          }

          &.favorited {
            color: #f6ad55;
          }
        }

        .built-in-badge {
          position: absolute;
          top: 0.5rem;
          left: 0.5rem;
          padding: 0.25rem 0.5rem;
          background: #3182ce;
          color: white;
          font-size: 0.75rem;
          border-radius: 4px;
        }
      }

      .template-info {
        padding: 1rem;

        .template-name {
          font-size: 1.125rem;
          font-weight: 600;
          color: #1a202c;
          margin: 0 0 0.5rem 0;
        }

        .template-description {
          font-size: 0.875rem;
          color: #718096;
          margin: 0 0 1rem 0;
          line-height: 1.4;
        }

        .template-meta {
          display: flex;
          flex-direction: column;
          gap: 0.25rem;
          margin-bottom: 1rem;

          .meta-item {
            font-size: 0.75rem;
            color: #a0aec0;

            .meta-label {
              font-weight: 500;
            }

            .meta-value {
              color: #4a5568;
            }
          }
        }

        .template-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 0.25rem;
          margin-bottom: 1rem;

          .template-tag {
            padding: 0.125rem 0.5rem;
            background: #edf2f7;
            color: #4a5568;
            font-size: 0.75rem;
            border-radius: 12px;
          }
        }

        .template-actions {
          display: flex;
          gap: 0.5rem;

          .action-button {
            flex: 1;
            padding: 0.5rem 1rem;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s;

            &.primary {
              background: #3182ce;
              color: white;
              border-color: #3182ce;

              &:hover {
                background: #2c5aa0;
              }
            }

            &.secondary {
              background: white;
              color: #4a5568;

              &:hover {
                background: #f7fafc;
              }
            }

            &.large {
              padding: 0.75rem 1.5rem;
              font-size: 1rem;
            }
          }
        }
      }
    }
  }

  .selected-template-details {
    background: white;
    border-top: 1px solid #e2e8f0;
    padding: 1rem;

    .details-title {
      font-size: 1rem;
      font-weight: 600;
      color: #1a202c;
      margin: 0 0 1rem 0;
    }

    .details-content {
      .selected-name {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1a202c;
        margin: 0 0 0.5rem 0;
      }

      .selected-description {
        font-size: 0.875rem;
        color: #718096;
        margin: 0 0 1rem 0;
        line-height: 1.4;
      }

      .selected-metadata {
        margin-bottom: 1rem;

        .metadata-item {
          font-size: 0.875rem;
          color: #4a5568;
          margin-bottom: 0.25rem;

          strong {
            color: #1a202c;
          }
        }
      }

      .apply-actions {
        display: flex;
        gap: 0.5rem;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive adjustments
@media (max-width: 768px) {
  .template-gallery {
    .gallery-header {
      .filters-section {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;

        .search-bar {
          min-width: auto;
        }

        .filter-group {
          justify-content: space-between;
        }

        .filter-toggles {
          justify-content: space-between;
        }
      }

      .tags-section {
        .tags-container {
          justify-content: center;
        }
      }
    }

    .templates-container {
      &.grid-view {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      }

      &.list-view {
        .template-item {
          flex-direction: column;
          text-align: center;

          .template-thumbnail {
            margin-right: 0;
            margin-bottom: 1rem;
          }
        }
      }
    }
  }
}
