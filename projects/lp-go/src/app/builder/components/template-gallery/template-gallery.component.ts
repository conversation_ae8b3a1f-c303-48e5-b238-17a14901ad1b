import { Component, OnInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';

import {
  Template,
  TemplateCategory,
  TemplateSearchOptions,
  TemplateApplicationOptions
} from '../../models/template.interface';
import { TemplateService } from '../../services/template.service';

/**
 * Template Gallery Component
 * Displays available templates with search, filtering, and preview capabilities
 */
@Component({
  selector: 'app-template-gallery',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './template-gallery.component.html',
  styleUrls: ['./template-gallery.component.scss']
})
export class TemplateGalleryComponent implements OnInit, OnDestroy {
  @Input() showHeader = true;
  @Input() allowSelection = true;
  @Input() maxHeight = '600px';
  @Output() templateSelected = new EventEmitter<Template>();
  @Output() templateApplied = new EventEmitter<{ template: Template; options: TemplateApplicationOptions }>();

  // Template data
  templates: Template[] = [];
  filteredTemplates: Template[] = [];
  selectedTemplate: Template | null = null;
  
  // Search and filter state
  searchQuery = '';
  selectedCategory: TemplateCategory | '' = '';
  selectedTags: string[] = [];
  showFavoritesOnly = false;
  showBuiltInOnly = false;
  
  // UI state
  isLoading = false;
  viewMode: 'grid' | 'list' = 'grid';
  sortBy: 'name' | 'date' | 'popularity' | 'category' = 'name';
  sortDirection: 'asc' | 'desc' = 'asc';
  
  // Available options
  categories = Object.values(TemplateCategory);
  popularTags: string[] = [];
  
  // Search debouncing
  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();

  constructor(private templateService: TemplateService) {
    // Setup search debouncing
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(query => {
      this.searchQuery = query;
      this.applyFilters();
    });
  }

  ngOnInit(): void {
    this.loadTemplates();
    this.loadPopularTags();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load all templates
   */
  private loadTemplates(): void {
    this.isLoading = true;
    
    this.templateService.getTemplates().pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (templates) => {
        this.templates = templates;
        this.applyFilters();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading templates:', error);
        this.isLoading = false;
      }
    });
  }

  /**
   * Load popular tags
   */
  private loadPopularTags(): void {
    this.templateService.getPopularTags().pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (tags) => {
        this.popularTags = tags;
      },
      error: (error) => {
        console.error('Error loading popular tags:', error);
      }
    });
  }

  /**
   * Handle search input
   */
  onSearchInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchSubject.next(target.value);
  }

  /**
   * Handle category filter change
   */
  onCategoryChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    this.selectedCategory = target.value as TemplateCategory | '';
    this.applyFilters();
  }

  /**
   * Handle sort by change
   */
  onSortByChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    this.changeSortBy(target.value as 'name' | 'date' | 'popularity' | 'category');
  }

  /**
   * Toggle tag filter
   */
  toggleTag(tag: string): void {
    const index = this.selectedTags.indexOf(tag);
    if (index > -1) {
      this.selectedTags.splice(index, 1);
    } else {
      this.selectedTags.push(tag);
    }
    this.applyFilters();
  }

  /**
   * Toggle favorites filter
   */
  toggleFavoritesOnly(): void {
    this.showFavoritesOnly = !this.showFavoritesOnly;
    this.applyFilters();
  }

  /**
   * Toggle built-in filter
   */
  toggleBuiltInOnly(): void {
    this.showBuiltInOnly = !this.showBuiltInOnly;
    this.applyFilters();
  }

  /**
   * Change sort options
   */
  changeSortBy(sortBy: 'name' | 'date' | 'popularity' | 'category'): void {
    if (this.sortBy === sortBy) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortBy = sortBy;
      this.sortDirection = 'asc';
    }
    this.applyFilters();
  }

  /**
   * Change view mode
   */
  changeViewMode(mode: 'grid' | 'list'): void {
    this.viewMode = mode;
  }

  /**
   * Apply current filters and search
   */
  private applyFilters(): void {
    const searchOptions: TemplateSearchOptions = {
      query: this.searchQuery || undefined,
      category: this.selectedCategory || undefined,
      tags: this.selectedTags.length > 0 ? this.selectedTags : undefined,
      favoritesOnly: this.showFavoritesOnly || undefined,
      builtInOnly: this.showBuiltInOnly || undefined,
      sortBy: this.sortBy,
      sortDirection: this.sortDirection
    };

    this.templateService.getTemplates(searchOptions).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (templates) => {
        this.filteredTemplates = templates;
      },
      error: (error) => {
        console.error('Error filtering templates:', error);
      }
    });
  }

  /**
   * Select a template
   */
  selectTemplate(template: Template): void {
    if (!this.allowSelection) return;
    
    this.selectedTemplate = template;
    this.templateSelected.emit(template);
  }

  /**
   * Apply a template
   */
  applyTemplate(template: Template, options?: Partial<TemplateApplicationOptions>): void {
    const defaultOptions: TemplateApplicationOptions = {
      targetPageId: 'current', // This would be set by the parent component
      replaceContent: false,
      generateNewIds: true,
      positionOffset: { x: 0, y: 0 }
    };

    const finalOptions = { ...defaultOptions, ...options };
    
    this.templateApplied.emit({ template, options: finalOptions });
  }

  /**
   * Toggle template favorite status
   */
  toggleFavorite(template: Template, event: Event): void {
    event.stopPropagation();
    
    this.templateService.toggleFavorite(template.id).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: () => {
        // Template will be updated through the service subscription
        console.log(`Toggled favorite for template: ${template.name}`);
      },
      error: (error) => {
        console.error('Error toggling favorite:', error);
      }
    });
  }

  /**
   * Clear all filters
   */
  clearFilters(): void {
    this.searchQuery = '';
    this.selectedCategory = '';
    this.selectedTags = [];
    this.showFavoritesOnly = false;
    this.showBuiltInOnly = false;
    this.applyFilters();
  }

  /**
   * Get category display name
   */
  getCategoryDisplayName(category: TemplateCategory): string {
    const displayNames: Record<TemplateCategory, string> = {
      [TemplateCategory.PAGE]: 'Page',
      [TemplateCategory.COMPONENT]: 'Component',
      [TemplateCategory.LAYOUT]: 'Layout',
      [TemplateCategory.FORM]: 'Form',
      [TemplateCategory.CUSTOM]: 'Custom'
    };
    return displayNames[category];
  }

  /**
   * Check if tag is selected
   */
  isTagSelected(tag: string): boolean {
    return this.selectedTags.includes(tag);
  }

  /**
   * Get template component count
   */
  getComponentCount(template: Template): number {
    return Object.keys(template.components).length;
  }

  /**
   * Format template date
   */
  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  /**
   * Track by function for template list
   */
  trackByTemplateId(_index: number, template: Template): string {
    return template.id;
  }
}
