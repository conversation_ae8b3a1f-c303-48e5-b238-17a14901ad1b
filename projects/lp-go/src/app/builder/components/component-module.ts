import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DragDropModule } from '@angular/cdk/drag-drop';

import { ContainerComponentComponent } from './container-component/container-component.component';
import { DynamicComponentComponent } from './dynamic-component/dynamic-component.component';

/**
 * This module is needed to resolve circular dependencies between components.
 * Angular standalone components still need to import each other, which can cause circular refs.
 * This module acts as an intermediary to help resolve those circular dependencies.
 */
@NgModule({
  imports: [
    CommonModule,
    DragDropModule,
    ContainerComponentComponent,
    DynamicComponentComponent
  ],
  exports: [
    ContainerComponentComponent,
    DynamicComponentComponent
  ]
})
export class ComponentModule {}
