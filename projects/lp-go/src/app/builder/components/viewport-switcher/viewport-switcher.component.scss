.viewport-switcher {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  background: #f8fafc;
  border-radius: 6px;
  padding: 0.25rem;

  .switcher-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    border: 1px solid #e2e8f0;
    background: white;
    border-radius: 4px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
    white-space: nowrap;

    &:hover {
      background: #f7fafc;
      border-color: #cbd5e0;
    }

    &.active {
      background: #3182ce;
      color: white;
      border-color: #3182ce;

      &:hover {
        background: #2c5aa0;
      }
    }

    .icon {
      font-size: 1rem;
      line-height: 1;
    }

    .label {
      font-weight: 500;
    }

    .dimensions {
      font-size: 0.75rem;
      color: #718096;
      font-family: monospace;

      .active & {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .dropdown-arrow {
      font-size: 0.75rem;
      color: #a0aec0;
      transition: transform 0.2s;

      .active & {
        transform: rotate(180deg);
        color: rgba(255, 255, 255, 0.8);
      }
    }

    &.responsive-toggle {
      .icon {
        color: #3182ce;
      }

      &.active .icon {
        color: white;
      }
    }
  }

  .switcher-group {
    position: relative;
    display: flex;
    align-items: center;
  }

  .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    margin-top: 0.25rem;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    z-index: 1000;
    min-width: 280px;
    max-height: 400px;
    overflow-y: auto;

    .dropdown-header {
      padding: 0.75rem 1rem;
      border-bottom: 1px solid #e2e8f0;
      background: #f8fafc;

      h3 {
        font-size: 0.875rem;
        font-weight: 600;
        color: #374151;
        margin: 0;
      }
    }

    .dropdown-content {
      padding: 0.5rem 0;
    }

    .dropdown-item {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      width: 100%;
      padding: 0.75rem 1rem;
      border: none;
      background: none;
      text-align: left;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background: #f7fafc;
      }

      &.selected {
        background: #ebf8ff;
        color: #3182ce;

        .item-icon {
          color: #3182ce;
        }
      }

      .item-icon {
        font-size: 1.25rem;
        color: #718096;
        flex-shrink: 0;
      }

      .item-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 0.125rem;

        .item-name {
          font-weight: 500;
          color: #374151;
        }

        .item-dimensions {
          font-size: 0.75rem;
          color: #718096;
          font-family: monospace;
        }
      }

      .item-type,
      .item-brand {
        font-size: 0.75rem;
        color: #a0aec0;
        text-transform: capitalize;
      }
    }

    .dropdown-separator {
      height: 1px;
      background: #e2e8f0;
      margin: 0.5rem 0;
    }

    .device-category {
      .category-header {
        padding: 0.5rem 1rem 0.25rem;
        font-size: 0.75rem;
        font-weight: 600;
        color: #718096;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }
    }
  }

  .viewport-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: #edf2f7;
    border-radius: 4px;
    font-size: 0.75rem;
    color: #4a5568;

    .info-label {
      font-weight: 500;
    }

    .info-value {
      font-family: monospace;
      color: #2d3748;
    }

    .info-type {
      color: #718096;
      text-transform: capitalize;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .viewport-switcher {
    flex-wrap: wrap;
    gap: 0.25rem;

    .switcher-button {
      padding: 0.375rem 0.5rem;
      font-size: 0.75rem;

      .label {
        display: none;
      }

      .dimensions {
        display: none;
      }
    }

    .dropdown-menu {
      min-width: 240px;
      left: auto;
      right: 0;
    }

    .viewport-info {
      order: -1;
      width: 100%;
      justify-content: center;
    }
  }
}

@media (max-width: 480px) {
  .viewport-switcher {
    .switcher-button {
      padding: 0.25rem 0.375rem;

      .icon {
        font-size: 0.875rem;
      }
    }

    .dropdown-menu {
      min-width: 200px;
      max-height: 300px;

      .dropdown-item {
        padding: 0.5rem 0.75rem;

        .item-content {
          .item-name {
            font-size: 0.875rem;
          }

          .item-dimensions {
            font-size: 0.6875rem;
          }
        }
      }
    }
  }
}

// Animation for dropdown
.dropdown-menu {
  animation: dropdownFadeIn 0.15s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-0.5rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Focus styles for accessibility
.switcher-button:focus,
.dropdown-item:focus {
  outline: 2px solid #3182ce;
  outline-offset: 2px;
}

// High contrast mode support
@media (prefers-contrast: high) {
  .viewport-switcher {
    .switcher-button {
      border-width: 2px;

      &.active {
        border-color: #000;
        background: #000;
      }
    }

    .dropdown-menu {
      border-width: 2px;
    }
  }
}
