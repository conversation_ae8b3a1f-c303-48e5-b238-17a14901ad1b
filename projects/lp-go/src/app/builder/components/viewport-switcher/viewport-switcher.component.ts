import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil, combineLatest } from 'rxjs';

import { ViewportService } from '../../services/viewport.service';
import { 
  Breakpoint, 
  DevicePreset, 
  ViewportState, 
  DeviceType,
  Orientation 
} from '../../models/viewport.interface';

/**
 * Viewport Switcher Component
 * Provides controls for switching between different viewport sizes and devices
 */
@Component({
  selector: 'app-viewport-switcher',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './viewport-switcher.component.html',
  styleUrls: ['./viewport-switcher.component.scss']
})
export class ViewportSwitcherComponent implements OnInit, OnDestroy {
  // Expose enums to template
  DeviceType = DeviceType;
  Orientation = Orientation;

  // Component state
  viewportState: ViewportState | null = null;
  breakpoints: Breakpoint[] = [];
  devicePresets: DevicePreset[] = [];
  
  // UI state
  showBreakpointDropdown = false;
  showDeviceDropdown = false;
  
  private destroy$ = new Subject<void>();

  constructor(private viewportService: ViewportService) {}

  ngOnInit(): void {
    this.subscribeToViewportChanges();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Subscribe to viewport service changes
   */
  private subscribeToViewportChanges(): void {
    combineLatest([
      this.viewportService.getViewportState(),
      this.viewportService.getBreakpoints(),
      this.viewportService.getDevicePresets()
    ]).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: ([viewportState, breakpoints, devicePresets]) => {
        this.viewportState = viewportState;
        this.breakpoints = breakpoints;
        this.devicePresets = devicePresets;
      },
      error: (error) => {
        console.error('Error loading viewport data:', error);
      }
    });
  }

  /**
   * Select a breakpoint
   */
  selectBreakpoint(breakpoint: Breakpoint): void {
    this.viewportService.setCurrentBreakpoint(breakpoint);
    this.showBreakpointDropdown = false;
  }

  /**
   * Select a device preset
   */
  selectDevice(device: DevicePreset): void {
    this.viewportService.setSelectedDevice(device);
    this.showDeviceDropdown = false;
  }

  /**
   * Clear device selection (use custom breakpoint)
   */
  clearDeviceSelection(): void {
    this.viewportService.setSelectedDevice(null);
    this.showDeviceDropdown = false;
  }

  /**
   * Toggle device orientation
   */
  toggleOrientation(): void {
    this.viewportService.toggleOrientation();
  }

  /**
   * Toggle responsive mode
   */
  toggleResponsiveMode(): void {
    this.viewportService.toggleResponsiveMode();
  }

  /**
   * Toggle device frame
   */
  toggleDeviceFrame(): void {
    this.viewportService.toggleDeviceFrame();
  }

  /**
   * Toggle breakpoint dropdown
   */
  toggleBreakpointDropdown(): void {
    this.showBreakpointDropdown = !this.showBreakpointDropdown;
    this.showDeviceDropdown = false;
  }

  /**
   * Toggle device dropdown
   */
  toggleDeviceDropdown(): void {
    this.showDeviceDropdown = !this.showDeviceDropdown;
    this.showBreakpointDropdown = false;
  }

  /**
   * Close all dropdowns
   */
  closeDropdowns(): void {
    this.showBreakpointDropdown = false;
    this.showDeviceDropdown = false;
  }

  /**
   * Get current viewport dimensions as string
   */
  getCurrentDimensions(): string {
    if (!this.viewportState) return '';
    
    const { width, height } = this.viewportState.currentBreakpoint;
    return `${width} × ${height}`;
  }

  /**
   * Get device dimensions for display
   */
  getDeviceDimensions(device: DevicePreset): string {
    if (!this.viewportState) return '';
    
    const dimensions = this.viewportState.orientation === Orientation.PORTRAIT
      ? device.portrait
      : device.landscape;
    
    return `${dimensions.width} × ${dimensions.height}`;
  }

  /**
   * Check if device is currently selected
   */
  isDeviceSelected(device: DevicePreset): boolean {
    return this.viewportState?.selectedDevice?.id === device.id;
  }

  /**
   * Check if breakpoint is currently selected
   */
  isBreakpointSelected(breakpoint: Breakpoint): boolean {
    return this.viewportState?.currentBreakpoint.id === breakpoint.id;
  }

  /**
   * Get device type icon
   */
  getDeviceTypeIcon(deviceType?: DeviceType): string {
    switch (deviceType) {
      case DeviceType.MOBILE:
        return '📱';
      case DeviceType.TABLET:
        return '📱';
      case DeviceType.DESKTOP:
        return '🖥️';
      default:
        return '📱';
    }
  }

  /**
   * Get orientation icon
   */
  getOrientationIcon(orientation?: Orientation): string {
    return orientation === Orientation.PORTRAIT ? '📱' : '📱';
  }

  /**
   * Get responsive mode icon
   */
  getResponsiveModeIcon(): string {
    return this.viewportState?.responsiveModeEnabled ? '📐' : '📐';
  }

  /**
   * Get device frame icon
   */
  getDeviceFrameIcon(): string {
    return this.viewportState?.deviceFrameEnabled ? '🖼️' : '🖼️';
  }

  /**
   * Handle click outside dropdowns
   */
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.viewport-switcher')) {
      this.closeDropdowns();
    }
  }

  /**
   * Handle escape key
   */
  onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Escape') {
      this.closeDropdowns();
    }
  }

  /**
   * Get current device name for display
   */
  getCurrentDeviceName(): string {
    if (this.viewportState?.selectedDevice) {
      return this.viewportState.selectedDevice.name;
    }
    return this.viewportState?.currentBreakpoint.name || 'Custom';
  }

  /**
   * Get filtered device presets by type
   */
  getDevicesByType(deviceType: DeviceType): DevicePreset[] {
    return this.devicePresets.filter(device => device.deviceType === deviceType);
  }

  /**
   * Check if orientation toggle is available
   */
  canToggleOrientation(): boolean {
    return this.viewportState?.selectedDevice !== null;
  }
}
