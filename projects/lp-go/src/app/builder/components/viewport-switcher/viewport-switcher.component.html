<!-- Viewport Switcher -->
<div class="viewport-switcher" 
     (document:click)="onDocumentClick($event)"
     (keydown)="onKeyDown($event)">

  <!-- Responsive Mode Toggle -->
  <button
    class="switcher-button responsive-toggle"
    [class.active]="viewportState?.responsiveModeEnabled"
    (click)="toggleResponsiveMode()"
    title="Toggle responsive design mode">
    <span class="icon">{{ getResponsiveModeIcon() }}</span>
    <span class="label">Responsive</span>
  </button>

  <!-- Breakpoint Selector -->
  <div class="switcher-group" *ngIf="viewportState?.responsiveModeEnabled">
    <button
      class="switcher-button breakpoint-selector"
      (click)="toggleBreakpointDropdown()"
      [class.active]="showBreakpointDropdown"
      title="Select viewport breakpoint">
      <span class="icon">{{ getDeviceTypeIcon(viewportState?.currentBreakpoint?.deviceType) }}</span>
      <span class="label">{{ viewportState?.currentBreakpoint?.name }}</span>
      <span class="dimensions">{{ getCurrentDimensions() }}</span>
      <span class="dropdown-arrow">▼</span>
    </button>

    <!-- Breakpoint Dropdown -->
    <div class="dropdown-menu breakpoint-dropdown" *ngIf="showBreakpointDropdown">
      <div class="dropdown-header">
        <h3>Breakpoints</h3>
      </div>
      <div class="dropdown-content">
        <button
          *ngFor="let breakpoint of breakpoints"
          class="dropdown-item"
          [class.selected]="isBreakpointSelected(breakpoint)"
          (click)="selectBreakpoint(breakpoint)">
          <span class="item-icon">{{ breakpoint.icon || getDeviceTypeIcon(breakpoint.deviceType) }}</span>
          <div class="item-content">
            <span class="item-name">{{ breakpoint.name }}</span>
            <span class="item-dimensions">{{ breakpoint.width }} × {{ breakpoint.height }}</span>
          </div>
          <span class="item-type">{{ breakpoint.deviceType }}</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Device Selector -->
  <div class="switcher-group" *ngIf="viewportState?.responsiveModeEnabled">
    <button
      class="switcher-button device-selector"
      (click)="toggleDeviceDropdown()"
      [class.active]="showDeviceDropdown"
      title="Select device preset">
      <span class="icon">📱</span>
      <span class="label">{{ getCurrentDeviceName() }}</span>
      <span class="dropdown-arrow">▼</span>
    </button>

    <!-- Device Dropdown -->
    <div class="dropdown-menu device-dropdown" *ngIf="showDeviceDropdown">
      <div class="dropdown-header">
        <h3>Device Presets</h3>
      </div>
      <div class="dropdown-content">
        <!-- Custom Option -->
        <button
          class="dropdown-item"
          [class.selected]="!viewportState?.selectedDevice"
          (click)="clearDeviceSelection()">
          <span class="item-icon">⚙️</span>
          <div class="item-content">
            <span class="item-name">Custom</span>
            <span class="item-dimensions">Use breakpoint dimensions</span>
          </div>
        </button>

        <div class="dropdown-separator"></div>

        <!-- Mobile Devices -->
        <div class="device-category" *ngIf="getDevicesByType(DeviceType.MOBILE).length > 0">
          <div class="category-header">Mobile</div>
          <button
            *ngFor="let device of getDevicesByType(DeviceType.MOBILE)"
            class="dropdown-item"
            [class.selected]="isDeviceSelected(device)"
            (click)="selectDevice(device)">
            <span class="item-icon">📱</span>
            <div class="item-content">
              <span class="item-name">{{ device.name }}</span>
              <span class="item-dimensions">{{ getDeviceDimensions(device) }}</span>
            </div>
            <span class="item-brand">{{ device.brand }}</span>
          </button>
        </div>

        <!-- Tablet Devices -->
        <div class="device-category" *ngIf="getDevicesByType(DeviceType.TABLET).length > 0">
          <div class="category-header">Tablet</div>
          <button
            *ngFor="let device of getDevicesByType(DeviceType.TABLET)"
            class="dropdown-item"
            [class.selected]="isDeviceSelected(device)"
            (click)="selectDevice(device)">
            <span class="item-icon">📱</span>
            <div class="item-content">
              <span class="item-name">{{ device.name }}</span>
              <span class="item-dimensions">{{ getDeviceDimensions(device) }}</span>
            </div>
            <span class="item-brand">{{ device.brand }}</span>
          </button>
        </div>

        <!-- Desktop Devices -->
        <div class="device-category" *ngIf="getDevicesByType(DeviceType.DESKTOP).length > 0">
          <div class="category-header">Desktop</div>
          <button
            *ngFor="let device of getDevicesByType(DeviceType.DESKTOP)"
            class="dropdown-item"
            [class.selected]="isDeviceSelected(device)"
            (click)="selectDevice(device)">
            <span class="item-icon">🖥️</span>
            <div class="item-content">
              <span class="item-name">{{ device.name }}</span>
              <span class="item-dimensions">{{ getDeviceDimensions(device) }}</span>
            </div>
            <span class="item-brand">{{ device.brand }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Orientation Toggle -->
  <button
    *ngIf="viewportState?.responsiveModeEnabled && canToggleOrientation()"
    class="switcher-button orientation-toggle"
    (click)="toggleOrientation()"
    [title]="'Switch to ' + (viewportState?.orientation === Orientation.PORTRAIT ? 'landscape' : 'portrait')">
    <span class="icon">{{ getOrientationIcon(viewportState?.orientation) }}</span>
    <span class="label">{{ viewportState?.orientation }}</span>
  </button>

  <!-- Device Frame Toggle -->
  <button
    *ngIf="viewportState?.responsiveModeEnabled"
    class="switcher-button frame-toggle"
    [class.active]="viewportState?.deviceFrameEnabled"
    (click)="toggleDeviceFrame()"
    title="Toggle device frame">
    <span class="icon">{{ getDeviceFrameIcon() }}</span>
    <span class="label">Frame</span>
  </button>

  <!-- Viewport Info -->
  <div class="viewport-info" *ngIf="viewportState?.responsiveModeEnabled">
    <span class="info-label">Viewport:</span>
    <span class="info-value">{{ getCurrentDimensions() }}</span>
    <span class="info-type">{{ viewportState?.currentBreakpoint?.deviceType }}</span>
  </div>
</div>
