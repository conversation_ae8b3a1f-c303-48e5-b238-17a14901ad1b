<div class="demo-container">
  <h2>Component Demo: Direct Import from mobile-components</h2>

  <div class="demo-section">
    <h3>Drag this component:</h3>
    <div class="drag-container">
      <!-- Using the actual component from mobile-components -->
      <base-button
        [text]="buttonText"
        [type]="buttonType"
        [disabled]="buttonDisabled"
        [className]="buttonClassName">
      </base-button>
      <div class="component-label">
        ⬆️ This is a direct import from &#64;projects/mobile-components/src/lib/base/button
      </div>
    </div>
  </div>

  <div class="demo-section">
    <h3>Drop zone:</h3>
    <div
      class="drop-container"
      (click)="onDrop()">

      <div *ngIf="!isDropped" class="drop-placeholder">
        Click here to show component details
      </div>

      <div *ngIf="isDropped" class="dropped-content">
        <div class="component-info">
          <h4>Component Information:</h4>
          <p><strong>Path:</strong> {{ componentPath }}</p>
          <p><strong>Import:</strong> &#64;projects/mobile-components/src/lib/base/button</p>
          <p><strong>Selector:</strong> base-button</p>
        </div>

        <div class="component-properties">
          <h4>Component Properties:</h4>

          <div class="property-control">
            <label for="buttonText">text:</label>
            <input id="buttonText" type="text" [(ngModel)]="buttonText">
          </div>

          <div class="property-control">
            <label for="buttonType">type:</label>
            <select id="buttonType" [(ngModel)]="buttonType">
              <option value="button">button</option>
              <option value="submit">submit</option>
              <option value="reset">reset</option>
            </select>
          </div>

          <div class="property-control">
            <label for="buttonDisabled">disabled:</label>
            <input id="buttonDisabled" type="checkbox" [(ngModel)]="buttonDisabled">
          </div>

          <div class="property-control">
            <label for="buttonClassName">className:</label>
            <input id="buttonClassName" type="text" [(ngModel)]="buttonClassName">
          </div>
        </div>

        <div class="component-preview">
          <h4>Live Component:</h4>
          <!-- Using the actual component from mobile-components with bound properties -->
          <base-button
            [text]="buttonText"
            [type]="buttonType"
            [disabled]="buttonDisabled"
            [className]="buttonClassName">
          </base-button>
        </div>

        <button class="reset-button" (click)="resetDemo()">Reset Demo</button>
      </div>
    </div>
  </div>
</div>
