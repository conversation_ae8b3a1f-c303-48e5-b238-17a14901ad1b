import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

// Import the component properties provider
import { componentPropertiesProvider } from '../component-properties.provider';

@Component({
  selector: 'app-component-demo',
  templateUrl: './component-demo.component.html',
  styleUrls: ['./component-demo.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
  providers: [
    componentPropertiesProvider
  ]
})
export class ComponentDemoComponent implements OnInit {
  // Component path
  componentPath = '/Users/<USER>/Projects/lp-angular/projects/mobile-components/src/lib/base/button';

  // Button properties
  buttonText = 'Button';
  buttonType: 'button' | 'submit' | 'reset' = 'button';
  buttonDisabled = false;
  buttonClassName = 'bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded';

  // Drop state
  isDropped = false;

  constructor() {}

  ngOnInit(): void {}

  onDrop(): void {
    this.isDropped = true;
  }

  resetDemo(): void {
    this.isDropped = false;
  }
}
