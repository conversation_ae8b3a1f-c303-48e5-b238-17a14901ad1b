.demo-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  font-family: Arial, sans-serif;
}

h2 {
  color: #333;
  border-bottom: 2px solid #3182ce;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.demo-section {
  margin-bottom: 30px;
}

h3 {
  color: #4a5568;
  margin-bottom: 15px;
}

.drag-container {
  padding: 15px;
  border: 2px dashed #3182ce;
  border-radius: 8px;
  display: inline-block;
  background-color: #ebf8ff;
  margin-bottom: 10px;
}

.component-label {
  margin-top: 10px;
  font-size: 12px;
  color: #718096;
}

.drop-container {
  min-height: 200px;
  border: 2px dashed #a0aec0;
  border-radius: 8px;
  padding: 20px;
  background-color: #f7fafc;
}

.drop-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: #a0aec0;
  font-style: italic;
  cursor: pointer;
}

.drop-placeholder:hover {
  background-color: #e6fffa;
  color: #38b2ac;
}

.dropped-content {
  animation: fadeIn 0.5s;
}

.component-info {
  background-color: #e6fffa;
  border-left: 4px solid #38b2ac;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
}

.component-properties {
  background-color: #ebf8ff;
  border-left: 4px solid #3182ce;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
}

.property-control {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.property-control label {
  width: 100px;
  font-weight: bold;
  color: #4a5568;
}

.property-control input[type="text"],
.property-control select {
  flex: 1;
  padding: 8px;
  border: 1px solid #cbd5e0;
  border-radius: 4px;
}

.component-preview {
  background-color: #faf5ff;
  border-left: 4px solid #805ad5;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
}

.reset-button {
  background-color: #e53e3e;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.reset-button:hover {
  background-color: #c53030;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
