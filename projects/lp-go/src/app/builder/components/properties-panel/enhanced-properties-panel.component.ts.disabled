import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

import { ComponentStoreService } from '../../services/component-store.service';
import { PropertyRegistryService } from '../../services/property-registry.service';
import { BuilderConfigService } from '../../services/builder-config.service';
import { StorageService } from '../../../shared/services/storage.service';
import { BuilderComponent, PageConfig, BulkPropertyValue } from '../../models/builder-component.interface';
import {
  PropertyDefinition,
  PropertyGroup,
  PropertyChangeEvent,
  PropertyValidationResult
} from '../../models/property-definition.interface';


import { DynamicPropertyInputComponent } from './dynamic-property-input/dynamic-property-input.component';
import { TailwindClassEditorComponent } from './tailwind-class-editor/tailwind-class-editor.component';

interface PropertyGroupWithProperties {
  group: PropertyGroup;
  properties: PropertyDefinition[];
  expanded: boolean;
}

@Component({
  selector: 'app-enhanced-properties-panel',
  templateUrl: './enhanced-properties-panel.component.html',
  styleUrls: ['./enhanced-properties-panel.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    DynamicPropertyInputComponent,
    TailwindClassEditorComponent
  ]
})
export class EnhancedPropertiesPanelComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Component state
  selectedComponent: BuilderComponent | null = null;
  selectedComponents: BuilderComponent[] = [];
  isMultiSelect = false;
  currentPage: PageConfig | null = null;
  
  // Property state
  propertyGroups: PropertyGroupWithProperties[] = [];
  componentProperties: Record<string, any> = {};
  bulkPropertyState: Record<string, BulkPropertyValue> = {};
  canvasProperties: Record<string, any> = {};
  validationResults: Record<string, PropertyValidationResult> = {};
  
  // UI state
  searchTerm = '';
  showAdvanced = false;
  isLoading = false;

  constructor(
    private componentStore: ComponentStoreService,
    private propertyRegistry: PropertyRegistryService,
    private configService: BuilderConfigService,
    private storageService: StorageService
  ) {}

  ngOnInit(): void {
    this.subscribeToChanges();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Subscribe to component and page changes
   */
  private subscribeToChanges(): void {
    // Subscribe to selected component changes (legacy single selection)
    this.componentStore.getSelectedComponent().pipe(
      takeUntil(this.destroy$)
    ).subscribe((component: BuilderComponent | undefined) => {
      this.selectedComponent = component || null;
      if (!this.isMultiSelect) {
        this.loadComponentProperties();
      }
    });

    // Subscribe to selected component changes (single selection for now)
    this.componentStore.getSelectedComponent().pipe(
      takeUntil(this.destroy$)
    ).subscribe((component: BuilderComponent | undefined) => {
      if (component) {
        this.selectedComponent = component;
        this.selectedComponents = [component];
        this.isMultiSelect = false;
        this.loadComponentProperties();
      } else {
        this.selectedComponent = null;
        this.selectedComponents = [];
        this.isMultiSelect = false;
        this.loadComponentProperties();
      }
    });

    // Subscribe to page changes
    this.componentStore.getActivePage().pipe(
      takeUntil(this.destroy$)
    ).subscribe((page: PageConfig | null) => {
      this.currentPage = page;
      this.loadCanvasProperties();
    });
  }





  /**
   * Load properties for the selected component
   */
  private loadComponentProperties(): void {
    if (!this.selectedComponent) {
      this.propertyGroups = [];
      this.componentProperties = {};
      this.bulkPropertyState = {};
      return;
    }

    this.isLoading = true;

    try {
      // Get component type
      const componentType = this.selectedComponent.type;
      
      // Get property definitions from registry
      const properties = this.propertyRegistry.getComponentProperties(componentType);
      const groups = this.propertyRegistry.getComponentGroups(componentType);
      
      // Load current property values
      this.componentProperties = this.selectedComponent.properties ? { ...this.selectedComponent.properties } : {};
      
      // Clear bulk state for single selection
      this.bulkPropertyState = {};
      
      // Organize properties by groups
      this.organizePropertiesByGroups(properties, groups);
      
      // Validate all properties
      this.validateAllProperties();
      
      console.log(`[EnhancedPropertiesPanel] Loaded ${properties.length} properties for ${componentType}`);
      
    } catch (error) {
      console.error('[EnhancedPropertiesPanel] Error loading component properties:', error);
      this.propertyGroups = [];
      this.componentProperties = {};
      this.bulkPropertyState = {};
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Load canvas/page properties
   */
  private loadCanvasProperties(): void {
    if (!this.currentPage) {
      this.canvasProperties = {};
      return;
    }

    this.canvasProperties = {
      tailwindClasses: this.currentPage.properties?.tailwindClasses || '',
      backgroundColor: this.currentPage.styles?.backgroundColor || 'bg-white',
      ...this.currentPage.properties
    };
  }

  /**
   * Organize properties by groups
   */
  private organizePropertiesByGroups(properties: PropertyDefinition[], groups: PropertyGroup[]): void {
    this.propertyGroups = [];

    // Get saved group states from storage
    const componentType = this.selectedComponent?.type || '';
    const savedGroupStates = this.storageService.getComponentGroupStates(componentType);

    // Create a map of group ID to group
    const groupMap = new Map<string, PropertyGroup>();
    groups.forEach(group => groupMap.set(group.id, group));

    // Group properties
    const groupedProperties = new Map<string, PropertyDefinition[]>();

    properties.forEach(property => {
      const groupId = property.group || 'general';
      if (!groupedProperties.has(groupId)) {
        groupedProperties.set(groupId, []);
      }
      groupedProperties.get(groupId)!.push(property);
    });

    // Create property groups with properties
    groupedProperties.forEach((groupProperties, groupId) => {
      const group = groupMap.get(groupId) || {
        id: groupId,
        label: groupId.charAt(0).toUpperCase() + groupId.slice(1),
        expanded: groupId === 'content' || groupId === 'appearance',
        order: 999
      };

      // Use saved state if available, otherwise use default
      const expanded = savedGroupStates[groupId] !== undefined
        ? savedGroupStates[groupId]
        : (group.expanded || false);

      // Sort properties by order
      groupProperties.sort((a, b) => (a.order || 999) - (b.order || 999));

      this.propertyGroups.push({
        group,
        properties: groupProperties,
        expanded
      });
    });

    // Sort groups by order
    this.propertyGroups.sort((a, b) => (a.group.order || 999) - (b.group.order || 999));
  }

  /**
   * Validate all properties
   */
  private validateAllProperties(): void {
    if (!this.selectedComponent) return;

    this.validationResults = {};

    this.propertyGroups.forEach(groupData => {
      groupData.properties.forEach(property => {
        const value = this.componentProperties[property.key];
        this.validationResults[property.key] = this.propertyRegistry.validatePropertyWithEnhancedMessages(
          this.selectedComponent!.type,
          property.key,
          value
        );
      });
    });
  }

  /**
   * Handle property changes for single or bulk selection
   */
  onPropertyChange(event: PropertyChangeEvent): void {
    if (this.isMultiSelect) {
      this.handleBulkPropertyChange(event);
    } else {
      this.handleSinglePropertyChange(event);
    }
  }

  /**
   * Handle property change for single component
   */
  private handleSinglePropertyChange(event: PropertyChangeEvent): void {
    if (!this.selectedComponent) return;

    // Update local properties
    this.componentProperties[event.key] = event.newValue;

    // Update component in store
    this.componentStore.updateComponentProperties(this.selectedComponent.id, {
      [event.key]: event.newValue
    });

    // Mark config as dirty
    this.configService.markAsDirty();

    console.log(`[EnhancedPropertiesPanel] Property ${event.key} changed:`, event.oldValue, '->', event.newValue);
  }

  /**
   * Handle property change for multiple components (bulk edit)
   */
  private handleBulkPropertyChange(event: PropertyChangeEvent): void {
    if (this.selectedComponents.length === 0) return;

    // Update local properties for all selected components
    this.componentProperties[event.key] = event.newValue;

    // Update bulk property state
    if (this.bulkPropertyState[event.key]) {
      this.bulkPropertyState[event.key] = {
        value: event.newValue,
        isMixed: false,
        affectedComponents: this.selectedComponents.map(comp => comp.id)
      };
    }

    // Use bulk update method for multiple components
    const componentIds = this.selectedComponents.map(comp => comp.id);
    this.componentStore.bulkUpdateProperties(componentIds, {
      [event.key]: event.newValue
    }).subscribe({
      next: () => {
        console.log(`[EnhancedPropertiesPanel] Bulk updated property ${event.key} for ${componentIds.length} components`);
      },
      error: (error: any) => {
        console.error('[EnhancedPropertiesPanel] Error in bulk property update:', error);
      }
    });

    // Mark config as dirty
    this.configService.markAsDirty();

    console.log(`[EnhancedPropertiesPanel] Bulk property ${event.key} changed:`, event.oldValue, '->', event.newValue);
  }

  /**
   * Handle property validation changes
   */
  onPropertyValidation(propertyKey: string, result: PropertyValidationResult): void {
    this.validationResults[propertyKey] = result;
  }

  /**
   * Handle canvas property changes
   */
  onCanvasPropertyChange(key: string, value: any): void {
    if (!this.currentPage) return;

    this.canvasProperties[key] = value;

    // Update page properties
    const updatedPage = {
      ...this.currentPage,
      properties: {
        ...this.currentPage.properties,
        [key]: value
      }
    };

    // Special handling for backgroundColor
    if (key === 'backgroundColor') {
      updatedPage.styles = {
        ...this.currentPage.styles,
        backgroundColor: value
      };
    }

    this.componentStore.setActivePage(updatedPage);
    this.configService.markAsDirty();

    console.log(`[EnhancedPropertiesPanel] Canvas property ${key} changed:`, value);
  }

  /**
   * Toggle group expansion
   */
  toggleGroup(groupData: PropertyGroupWithProperties): void {
    groupData.expanded = !groupData.expanded;

    // Save state to storage
    if (this.selectedComponent) {
      this.storageService.setPropertyGroupState(
        this.selectedComponent.type,
        groupData.group.id,
        groupData.expanded
      );
    }
  }

  /**
   * Get filtered property groups based on search
   */
  getFilteredGroups(): PropertyGroupWithProperties[] {
    if (!this.searchTerm.trim()) {
      return this.propertyGroups;
    }

    const searchLower = this.searchTerm.toLowerCase();
    
    return this.propertyGroups.map(groupData => ({
      ...groupData,
      properties: groupData.properties.filter(property =>
        property.label.toLowerCase().includes(searchLower) ||
        property.key.toLowerCase().includes(searchLower) ||
        (property.description && property.description.toLowerCase().includes(searchLower))
      )
    })).filter(groupData => groupData.properties.length > 0);
  }

  /**
   * Check if property should be shown
   */
  shouldShowProperty(property: PropertyDefinition): boolean {
    // Check advanced filter
    if (!this.showAdvanced && property.group === 'advanced') {
      return false;
    }

    // Check conditional display
    if (property.showWhen) {
      try {
        return property.showWhen(this.componentProperties);
      } catch (error) {
        console.warn('Error evaluating showWhen condition:', error);
        return true;
      }
    }

    return true;
  }

  /**
   * Get property value
   */
  getPropertyValue(property: PropertyDefinition): any {
    return this.componentProperties[property.key] ?? property.defaultValue;
  }

  /**
   * Get component type label for multi-selection
   */
  getComponentTypeLabel(): string {
    if (this.isMultiSelect) {
      const types = [...new Set(this.selectedComponents.map(comp => comp.type))];
      if (types.length === 1) {
        return `${types[0]} (${this.selectedComponents.length} selected)`;
      } else {
        return `Mixed Types (${this.selectedComponents.length} selected)`;
      }
    } else if (this.selectedComponent) {
      return this.selectedComponent.type;
    }
    return 'No Selection';
  }

  /**
   * Check if there are validation errors
   */
  hasValidationErrors(): boolean {
    return Object.values(this.validationResults).some(result => result.errors.length > 0);
  }

  /**
   * Get total error count
   */
  getErrorCount(): number {
    return Object.values(this.validationResults).reduce((count, result) => count + result.errors.length, 0);
  }

  /**
   * Reset properties to defaults
   */
  resetToDefaults(): void {
    if (!this.selectedComponent) return;

    const properties = this.propertyRegistry.getComponentProperties(this.selectedComponent.type);
    const defaultProperties: Record<string, any> = {};

    properties.forEach(property => {
      if (property.defaultValue !== undefined) {
        defaultProperties[property.key] = property.defaultValue;
      }
    });

    this.componentProperties = { ...defaultProperties };
    this.componentStore.updateComponentProperties(this.selectedComponent.id, defaultProperties);
    this.validateAllProperties();
    this.configService.markAsDirty();
  }

  /**
   * Export component properties as JSON
   */
  exportProperties(): string {
    return JSON.stringify(this.componentProperties, null, 2);
  }

  /**
   * Import component properties from JSON
   */
  importProperties(jsonString: string): void {
    try {
      const properties = JSON.parse(jsonString);
      this.componentProperties = { ...properties };

      if (this.selectedComponent) {
        this.componentStore.updateComponentProperties(this.selectedComponent.id, properties);
        this.validateAllProperties();
        this.configService.markAsDirty();
      }
    } catch (error) {
      console.error('Error importing properties:', error);
      // Could show user-friendly error message here
    }
  }

  /**
   * Handle Tailwind classes change for canvas
   */
  onCanvasTailwindClassesChange(classes: string): void {
    this.onCanvasPropertyChange('tailwindClasses', classes);
  }

  /**
   * Get validation status for a property group
   */
  getGroupValidationStatus(groupData: PropertyGroupWithProperties): 'valid' | 'warning' | 'error' {
    let hasErrors = false;
    let hasWarnings = false;

    for (const property of groupData.properties) {
      const result = this.validationResults[property.key];
      if (result) {
        if (result.errors.length > 0) {
          hasErrors = true;
        }
        if (result.warnings.length > 0) {
          hasWarnings = true;
        }
      }
    }

    if (hasErrors) return 'error';
    if (hasWarnings) return 'warning';
    return 'valid';
  }

  /**
   * Get validation message for a property group
   */
  getGroupValidationMessage(groupData: PropertyGroupWithProperties): string {
    const errorCount = this.getGroupErrorCount(groupData);
    const warningCount = this.getGroupWarningCount(groupData);

    if (errorCount > 0 && warningCount > 0) {
      return `${errorCount} error(s) and ${warningCount} warning(s) in this group`;
    } else if (errorCount > 0) {
      return `${errorCount} error(s) in this group`;
    } else if (warningCount > 0) {
      return `${warningCount} warning(s) in this group`;
    }

    return '';
  }

  /**
   * Get error count for a property group
   */
  private getGroupErrorCount(groupData: PropertyGroupWithProperties): number {
    return groupData.properties.reduce((count, property) => {
      const result = this.validationResults[property.key];
      return count + (result ? result.errors.length : 0);
    }, 0);
  }

  /**
   * Get warning count for a property group
   */
  private getGroupWarningCount(groupData: PropertyGroupWithProperties): number {
    return groupData.properties.reduce((count, property) => {
      const result = this.validationResults[property.key];
      return count + (result ? result.warnings.length : 0);
    }, 0);
  }
}
