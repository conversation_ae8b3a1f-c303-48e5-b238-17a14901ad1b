<div class="enhanced-properties-panel">
  <!-- Panel Header -->
  <div class="panel-header">
    <h2>Properties</h2>
    
    <!-- Search and Controls -->
    <div class="panel-controls" *ngIf="selectedComponent || currentPage">
      <div class="search-wrapper">
        <input 
          type="text" 
          [(ngModel)]="searchTerm" 
          placeholder="Search properties..."
          class="search-input">
        <span class="search-icon">🔍</span>
      </div>
      
      <div class="control-buttons">
        <button 
          class="control-btn"
          [class.active]="showAdvanced"
          (click)="showAdvanced = !showAdvanced"
          title="Show advanced properties">
          ⚙️
        </button>
        
        <button 
          class="control-btn"
          (click)="resetToDefaults()"
          [disabled]="!selectedComponent"
          title="Reset to defaults">
          🔄
        </button>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div class="loading-state" *ngIf="isLoading">
    <div class="loading-spinner"></div>
    <span>Loading properties...</span>
  </div>

  <!-- No Selection State -->
  <div class="no-selection" *ngIf="!selectedComponent && !isLoading">
    <div class="no-selection-content">
      <div class="no-selection-icon">📝</div>
      <h3>No Component Selected</h3>
      <p>Select a component to edit its properties</p>
    </div>

    <!-- Canvas Properties -->
    <div class="canvas-section" *ngIf="currentPage">
      <h3>Canvas Properties</h3>
      
      <div class="property-group">
        <label>Background Classes:</label>
        <app-tailwind-class-editor
          [currentClasses]="canvasProperties['tailwindClasses'] || ''"
          (classesChanged)="onCanvasTailwindClassesChange($event)">
        </app-tailwind-class-editor>
      </div>
    </div>
  </div>

  <!-- Component Properties -->
  <div class="component-properties" *ngIf="selectedComponent && !isLoading">
    
    <!-- Component Header -->
    <div class="component-header">
      <div class="component-info">
        <div class="component-type">{{ getComponentTypeLabel() }}</div>
        <div class="component-id">ID: {{ selectedComponent.id }}</div>
      </div>
      
      <!-- Validation Status -->
      <div class="validation-status" *ngIf="hasValidationErrors()">
        <span class="error-count">{{ getErrorCount() }} error(s)</span>
      </div>
    </div>

    <!-- Property Groups -->
    <div class="property-groups">
      <div 
        *ngFor="let groupData of getFilteredGroups()" 
        class="property-group-container">
        
        <!-- Group Header -->
        <div
          class="group-header"
          [id]="'header-' + groupData.group.id"
          (click)="toggleGroup(groupData)"
          (keydown.enter)="toggleGroup(groupData)"
          (keydown.space)="toggleGroup(groupData)"
          [class.expanded]="groupData.expanded"
          [attr.aria-expanded]="groupData.expanded"
          [attr.aria-controls]="'group-' + groupData.group.id"
          role="button"
          tabindex="0">

          <div class="group-info">
            <span class="group-icon" *ngIf="groupData.group.icon">{{ groupData.group.icon }}</span>
            <span class="group-label">{{ groupData.group.label }}</span>
            <span class="property-count">({{ groupData.properties.length }})</span>

            <!-- Validation Status Indicator -->
            <span class="validation-indicator"
                  *ngIf="getGroupValidationStatus(groupData) !== 'valid'"
                  [class]="'status-' + getGroupValidationStatus(groupData)"
                  [title]="getGroupValidationMessage(groupData)">
              <span *ngIf="getGroupValidationStatus(groupData) === 'error'">⚠️</span>
              <span *ngIf="getGroupValidationStatus(groupData) === 'warning'">⚡</span>
            </span>
          </div>

          <span class="expand-icon" [attr.aria-hidden]="true">{{ groupData.expanded ? '▼' : '▶' }}</span>
        </div>

        <!-- Group Description -->
        <div class="group-description" *ngIf="groupData.group.description && groupData.expanded">
          {{ groupData.group.description }}
        </div>

        <!-- Properties -->
        <div
          class="group-properties"
          *ngIf="groupData.expanded"
          [id]="'group-' + groupData.group.id"
          role="region"
          [attr.aria-labelledby]="'header-' + groupData.group.id">
          <app-dynamic-property-input
            *ngFor="let property of groupData.properties"
            [property]="property"
            [value]="getPropertyValue(property)"
            [componentType]="selectedComponent.type"
            [componentId]="selectedComponent.id"
            (propertyChange)="onPropertyChange($event)"
            (validationChange)="onPropertyValidation(property.key, $event)"
            [style.display]="shouldShowProperty(property) ? 'block' : 'none'">
          </app-dynamic-property-input>
        </div>
      </div>
    </div>

    <!-- Property Actions -->
    <div class="property-actions">
      <button class="action-btn secondary" (click)="resetToDefaults()">
        Reset to Defaults
      </button>
      
      <div class="action-group">
        <button class="action-btn secondary" (click)="exportProperties()">
          Export JSON
        </button>
        <button class="action-btn secondary">
          Import JSON
        </button>
      </div>
    </div>
  </div>

  <!-- Error Summary -->
  <div class="error-summary" *ngIf="hasValidationErrors()">
    <h4>Validation Errors</h4>
    <div class="error-list">
      <ng-container *ngFor="let result of validationResults | keyvalue">
        <div
          class="error-item"
          *ngIf="result.value.errors.length > 0">
          <strong>{{ result.key }}:</strong>
          <ul>
            <li *ngFor="let error of result.value.errors">{{ error }}</li>
          </ul>
        </div>
      </ng-container>
    </div>
  </div>
</div>
