.properties-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f8fafc;
}

.panel-header {
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
  
  h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #334155;
  }
}

.panel-content {
  padding: 1rem;
  overflow-y: auto;
  flex: 1;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #64748b;
  padding: 2rem;
  text-align: center;
}

.component-header {
  margin-bottom: 1rem;
  
  .component-type {
    font-size: 1.125rem;
    font-weight: 600;
    color: #334155;
  }
  
  .component-id {
    font-size: 0.75rem;
    color: #64748b;
    margin-top: 0.25rem;
  }
}

.property-section {
  margin-bottom: 2rem;
  
  h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #475569;
    margin: 0 0 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e2e8f0;
  }
}

.property-group {
  margin-bottom: 1rem;
  
  label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #334155;
    margin-bottom: 0.25rem;
  }
  
  .property-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #cbd5e1;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    
    &:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    }
  }
}

.tailwind-input {
  margin-bottom: 1rem;
}

.category-tabs {
  display: flex;
  overflow-x: auto;
  margin-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
  
  .tab-button {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    color: #64748b;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    white-space: nowrap;
    
    &.active {
      color: #3b82f6;
      border-bottom-color: #3b82f6;
    }
    
    &:hover:not(.active) {
      color: #334155;
    }
  }
}

.tailwind-options {
  .option-group {
    margin-bottom: 1.5rem;
    
    h4 {
      font-size: 0.875rem;
      font-weight: 500;
      color: #334155;
      margin: 0 0 0.5rem;
    }
  }
  
  .option-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    
    .option-button {
      padding: 0.25rem 0.5rem;
      font-size: 0.75rem;
      color: #334155;
      background-color: #f1f5f9;
      border: 1px solid #e2e8f0;
      border-radius: 0.25rem;
      cursor: pointer;
      
      &:hover {
        background-color: #e2e8f0;
      }
      
      &.selected {
        background-color: #3b82f6;
        color: white;
        border-color: #2563eb;
      }
    }
  }
  
  .color-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    
    .color-button {
      width: 2rem;
      height: 2rem;
      border-radius: 0.25rem;
      border: none;
      cursor: pointer;
      position: relative;
      
      &.selected::after {
        content: '✓';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
      }
      
      &.bg-white.selected::after {
        color: #334155;
      }
    }
  }
}