import { Component, OnInit, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule, NgFor, NgIf } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';

interface TailwindCategory {
  name: string;
  icon: string;
  classes: TailwindClassGroup[];
  expanded?: boolean;
}

interface TailwindClassGroup {
  name: string;
  prefix: string;
  values: TailwindClassValue[];
}

interface TailwindClassValue {
  name: string;
  value: string;
  preview?: string;
}

@Component({
  selector: 'app-tailwind-class-editor',
  templateUrl: './tailwind-class-editor.component.html',
  styleUrls: ['./tailwind-class-editor.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NgFor,
    NgIf,
    FormsModule,
    ReactiveFormsModule
  ]
})
export class TailwindClassEditorComponent implements OnInit, OnChanges {
  // Helper for template to convert Set to Array
  Array = Array;
  @Input() currentClasses = '';
  @Output() classesChanged = new EventEmitter<string>();
  
  form: FormGroup;
  activeClasses: Set<string> = new Set();
  searchTerm = '';
  
  // Tailwind categories
  categories: TailwindCategory[] = [
    {
      name: 'Layout',
      icon: '⬒',
      expanded: true,
      classes: [
        {
          name: 'Display',
          prefix: '',
          values: [
            { name: 'Block', value: 'block' },
            { name: 'Inline Block', value: 'inline-block' },
            { name: 'Inline', value: 'inline' },
            { name: 'Flex', value: 'flex' },
            { name: 'Inline Flex', value: 'inline-flex' },
            { name: 'Grid', value: 'grid' },
            { name: 'Hidden', value: 'hidden' }
          ]
        },
        {
          name: 'Width',
          prefix: 'w-',
          values: [
            { name: 'Auto', value: 'w-auto' },
            { name: 'Full', value: 'w-full' },
            { name: 'Screen', value: 'w-screen' },
            { name: '1/2', value: 'w-1/2' },
            { name: '1/3', value: 'w-1/3' },
            { name: '2/3', value: 'w-2/3' },
            { name: '1/4', value: 'w-1/4' },
            { name: '3/4', value: 'w-3/4' }
          ]
        },
        {
          name: 'Height',
          prefix: 'h-',
          values: [
            { name: 'Auto', value: 'h-auto' },
            { name: 'Full', value: 'h-full' },
            { name: 'Screen', value: 'h-screen' },
            { name: '1/2', value: 'h-1/2' },
            { name: '1/3', value: 'h-1/3' },
            { name: '2/3', value: 'h-2/3' },
            { name: '1/4', value: 'h-1/4' },
            { name: '3/4', value: 'h-3/4' }
          ]
        }
      ]
    },
    {
      name: 'Spacing',
      icon: '↔',
      classes: [
        {
          name: 'Padding',
          prefix: 'p-',
          values: [
            { name: 'None', value: 'p-0' },
            { name: 'Small', value: 'p-1' },
            { name: 'Medium', value: 'p-2' },
            { name: 'Large', value: 'p-4' },
            { name: 'Extra Large', value: 'p-8' }
          ]
        },
        {
          name: 'Padding X',
          prefix: 'px-',
          values: [
            { name: 'None', value: 'px-0' },
            { name: 'Small', value: 'px-1' },
            { name: 'Medium', value: 'px-2' },
            { name: 'Large', value: 'px-4' },
            { name: 'Extra Large', value: 'px-8' }
          ]
        },
        {
          name: 'Padding Y',
          prefix: 'py-',
          values: [
            { name: 'None', value: 'py-0' },
            { name: 'Small', value: 'py-1' },
            { name: 'Medium', value: 'py-2' },
            { name: 'Large', value: 'py-4' },
            { name: 'Extra Large', value: 'py-8' }
          ]
        },
        {
          name: 'Margin',
          prefix: 'm-',
          values: [
            { name: 'None', value: 'm-0' },
            { name: 'Small', value: 'm-1' },
            { name: 'Medium', value: 'm-2' },
            { name: 'Large', value: 'm-4' },
            { name: 'Extra Large', value: 'm-8' },
            { name: 'Auto', value: 'm-auto' }
          ]
        },
        {
          name: 'Margin X',
          prefix: 'mx-',
          values: [
            { name: 'None', value: 'mx-0' },
            { name: 'Small', value: 'mx-1' },
            { name: 'Medium', value: 'mx-2' },
            { name: 'Large', value: 'mx-4' },
            { name: 'Extra Large', value: 'mx-8' },
            { name: 'Auto', value: 'mx-auto' }
          ]
        },
        {
          name: 'Margin Y',
          prefix: 'my-',
          values: [
            { name: 'None', value: 'my-0' },
            { name: 'Small', value: 'my-1' },
            { name: 'Medium', value: 'my-2' },
            { name: 'Large', value: 'my-4' },
            { name: 'Extra Large', value: 'my-8' },
            { name: 'Auto', value: 'my-auto' }
          ]
        }
      ]
    },
    {
      name: 'Colors',
      icon: '🎨',
      classes: [
        {
          name: 'Background Color',
          prefix: 'bg-',
          values: [
            { name: 'White', value: 'bg-white' },
            { name: 'Gray 100', value: 'bg-gray-100' },
            { name: 'Gray 200', value: 'bg-gray-200' },
            { name: 'Gray 500', value: 'bg-gray-500' },
            { name: 'Blue 500', value: 'bg-blue-500' },
            { name: 'Green 500', value: 'bg-green-500' },
            { name: 'Red 500', value: 'bg-red-500' },
            { name: 'Yellow 500', value: 'bg-yellow-500' }
          ]
        },
        {
          name: 'Border Color',
          prefix: 'border-',
          values: [
            { name: 'White', value: 'border-white' },
            { name: 'Gray 200', value: 'border-gray-500' },
            { name: 'Gray 500', value: 'border-gray-500' }, 
            { name: 'Blue 500', value: 'border-blue-500' },
            { name: 'Green 500', value: 'border-green-500' },
            { name: 'Red 500', value: 'border-red-500' }
          ]
        }
      ]
    },
    {
      name: 'Typography',
      icon: 'T',
      classes: [
        {
          name: 'Font Size',
          prefix: 'text-',
          values: [
            { name: 'Extra Small', value: 'text-xs' },
            { name: 'Small', value: 'text-sm' },
            { name: 'Base', value: 'text-base' },
            { name: 'Large', value: 'text-lg' },
            { name: 'Extra Large', value: 'text-xl' },
            { name: '2x Large', value: 'text-2xl' },
            { name: '3x Large', value: 'text-3xl' },
            { name: '4x Large', value: 'text-4xl' }
          ]
        },
        {
          name: 'Font Weight',
          prefix: 'font-',
          values: [
            { name: 'Thin', value: 'font-thin' },
            { name: 'Normal', value: 'font-normal' },
            { name: 'Medium', value: 'font-medium' },
            { name: 'Bold', value: 'font-bold' },
            { name: 'Extra Bold', value: 'font-extrabold' }
          ]
        },
        {
          name: 'Text Align',
          prefix: 'text-',
          values: [
            { name: 'Left', value: 'text-left' },
            { name: 'Center', value: 'text-center' },
            { name: 'Right', value: 'text-right' },
            { name: 'Justify', value: 'text-justify' }
          ]
        },
        {
          name: 'Text Color',
          prefix: 'text-',
          values: [
            { name: 'Black', value: 'text-black' },
            { name: 'White', value: 'text-white' },
            { name: 'Gray', value: 'text-gray-500' },
            { name: 'Red', value: 'text-red-500' },
            { name: 'Blue', value: 'text-blue-500' },
            { name: 'Green', value: 'text-green-500' },
            { name: 'Yellow', value: 'text-yellow-500' },
            { name: 'Purple', value: 'text-purple-500' }
          ]
        }
      ]
    }
  ];
  
  constructor(private fb: FormBuilder) {
    this.form = this.fb.group({
      classInput: ['']
    });
  }
  
  ngOnInit(): void {
    // Parse initial classes
    this.parseInitialClasses();
    
    // Update form value
    this.form.get('classInput')?.setValue(this.currentClasses);
    
    // Subscribe to form changes
    this.form.get('classInput')?.valueChanges.subscribe(value => {
      this.currentClasses = value;
      this.parseInitialClasses();
      this.classesChanged.emit(value);
    });
  }
  
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['currentClasses']) {
      console.log('Tailwind editor input changed:', this.currentClasses);
      // Parse the new classes and update the form
      this.parseInitialClasses();
      
      // Update the form value without triggering the valueChanges event
      this.form.get('classInput')?.setValue(this.currentClasses, { emitEvent: false });
    }
  }
  
  /**
   * Parse the initial classes string into individual classes
   */
  parseInitialClasses(): void {
    this.activeClasses.clear();
    if (this.currentClasses) {
      const classes = this.currentClasses.split(' ');
      classes.forEach(cls => {
        if (cls.trim()) {
          this.activeClasses.add(cls.trim());
        }
      });
    }
  }
  
  /**
   * Toggle a Tailwind class
   */
  toggleClass(classValue: string): void {
    if (this.activeClasses.has(classValue)) {
      this.activeClasses.delete(classValue);
    } else {
      // Remove conflicting classes (same prefix)
      this.removeConflictingClasses(classValue);
      this.activeClasses.add(classValue);
    }
    
    // Update the form value
    this.updateFormValue();
  }
  
  /**
   * Remove any classes that would conflict with the new class
   */
  removeConflictingClasses(newClass: string): void {
    // Get the prefix of the new class
    const prefix = this.getClassPrefix(newClass);
    if (!prefix) return;
    
    // Check all active classes for conflicts
    this.activeClasses.forEach(activeClass => {
      if (activeClass !== newClass && this.getClassPrefix(activeClass) === prefix) {
        this.activeClasses.delete(activeClass);
      }
    });
  }
  
  /**
   * Get the prefix of a class
   */
  getClassPrefix(className: string): string | null {
    // Find which group this class belongs to
    for (const category of this.categories) {
      for (const group of category.classes) {
        for (const value of group.values) {
          if (value.value === className) {
            return group.prefix;
          }
        }
      }
    }
    return null;
  }
  
  /**
   * Update the form value based on active classes
   */
  updateFormValue(): void {
    this.currentClasses = Array.from(this.activeClasses).join(' ');
    this.form.get('classInput')?.setValue(this.currentClasses, { emitEvent: false });
    this.classesChanged.emit(this.currentClasses);
  }
  
  /**
   * Toggle category expansion
   */
  toggleCategory(category: TailwindCategory): void {
    category.expanded = !category.expanded;
  }
  
  /**
   * Check if a class is active
   */
  isClassActive(classValue: string): boolean {
    return this.activeClasses.has(classValue);
  }
  
  /**
   * Filter categories and classes based on search term
   */
  get filteredCategories(): TailwindCategory[] {
    if (!this.searchTerm.trim()) {
      return this.categories;
    }
    
    const search = this.searchTerm.toLowerCase();
    
    return this.categories
      .map(category => {
        // Deep clone to avoid modifying original
        const newCategory: TailwindCategory = { 
          name: category.name,
          icon: category.icon,
          expanded: category.expanded,
          classes: []
        };
        
        // Filter classes in this category
        newCategory.classes = category.classes
          .map(group => {
            // Deep clone group
            const newGroup: TailwindClassGroup = {
              name: group.name,
              prefix: group.prefix,
              values: []
            };
            
            // Filter values in this group
            newGroup.values = group.values.filter(value => 
              value.name.toLowerCase().includes(search) || 
              value.value.toLowerCase().includes(search)
            );
            
            return newGroup;
          })
          .filter(group => group.values.length > 0);
        
        return newCategory;
      })
      .filter(category => category.classes.length > 0);
  }
  
  /**
   * Clear search
   */
  clearSearch(): void {
    this.searchTerm = '';
  }
}
