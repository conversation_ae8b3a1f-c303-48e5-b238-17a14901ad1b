<div class="tailwind-editor">
  <div class="editor-header">
    <div class="search-container">
      <input 
        type="text"
        [(ngModel)]="searchTerm"
        placeholder="Search Tailwind classes"
        class="search-input">
      <button 
        *ngIf="searchTerm"
        (click)="clearSearch()"
        class="clear-button">
        ✕
      </button>
    </div>
    
    <form [formGroup]="form" class="manual-input">
      <label for="classInput">Classes:</label>
      <input 
        type="text"
        id="classInput"
        formControlName="classInput"
        placeholder="Enter Tailwind classes"
        class="class-input">
    </form>
  </div>
  
  <div class="categories">
    <!-- Empty state when no results -->
    <div class="empty-results" *ngIf="filteredCategories.length === 0">
      <p>No classes matching "{{ searchTerm }}"</p>
    </div>
    
    <!-- Categories -->
    <div 
      *ngFor="let category of filteredCategories"
      class="category">
      
      <div 
        class="category-header"
        (click)="toggleCategory(category)">
        <div class="category-icon">{{ category.icon }}</div>
        <div class="category-name">{{ category.name }}</div>
        <div class="toggle-icon">{{ category.expanded ? '▼' : '▶' }}</div>
      </div>
      
      <div class="category-content" *ngIf="category.expanded">
        <div 
          *ngFor="let group of category.classes"
          class="class-group">
          
          <div class="group-name">{{ group.name }}</div>
          
          <div class="class-values">
            <button 
              *ngFor="let value of group.values"
              class="class-value"
              [class.active]="isClassActive(value.value)"
              (click)="toggleClass(value.value)">
              {{ value.name }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="active-classes" *ngIf="activeClasses.size > 0">
    <div class="section-header">
      <h4>Active Classes:</h4>
    </div>
    
    <div class="class-tags">
      <ng-container *ngFor="let className of Array.from(activeClasses)">
        <div class="class-tag">
          <span class="tag-name">{{ className }}</span>
          <button 
            class="tag-remove"
            (click)="toggleClass(className)">
            ✕
          </button>
        </div>
      </ng-container>
    </div>
  </div>
</div>
