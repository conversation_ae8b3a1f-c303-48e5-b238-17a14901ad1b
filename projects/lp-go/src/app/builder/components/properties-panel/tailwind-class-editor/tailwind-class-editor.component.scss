.tailwind-editor {
  display: flex;
  flex-direction: column;
  max-height: 100%;
  padding: 0.5rem;
  
  .editor-header {
    margin-bottom: 1rem;
    
    .search-container {
      position: relative;
      margin-bottom: 0.5rem;
      
      .search-input {
        width: 100%;
        padding: 0.5rem;
        padding-right: 2rem;
        border-radius: 4px;
        border: 1px solid #ccc;
        font-size: 0.875rem;
      }
      
      .clear-button {
        position: absolute;
        right: 0.5rem;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        font-size: 0.75rem;
        color: #666;
        cursor: pointer;
      }
    }
    
    .manual-input {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
      
      label {
        font-size: 0.75rem;
        color: #666;
      }
      
      .class-input {
        width: 100%;
        padding: 0.5rem;
        border-radius: 4px;
        border: 1px solid #ccc;
        font-size: 0.875rem;
        font-family: monospace;
      }
    }
  }
  
  .categories {
    flex: 1;
    overflow-y: auto;
    border: 1px solid #eee;
    border-radius: 4px;
    
    .empty-results {
      padding: 1rem;
      text-align: center;
      color: #666;
      font-size: 0.875rem;
    }
    
    .category {
      border-bottom: 1px solid #eee;
      
      &:last-child {
        border-bottom: none;
      }
      
      .category-header {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        cursor: pointer;
        transition: background-color 0.2s;
        
        &:hover {
          background-color: #f5f5f5;
        }
        
        .category-icon {
          margin-right: 0.5rem;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f0f0f0;
          border-radius: 4px;
          font-size: 0.875rem;
        }
        
        .category-name {
          flex: 1;
          font-weight: 500;
          font-size: 0.875rem;
        }
        
        .toggle-icon {
          font-size: 0.75rem;
          color: #666;
        }
      }
      
      .category-content {
        padding: 0 0.75rem 0.75rem;
        
        .class-group {
          margin-bottom: 0.75rem;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .group-name {
            font-size: 0.75rem;
            font-weight: 500;
            color: #666;
            margin-bottom: 0.25rem;
          }
          
          .class-values {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            
            .class-value {
              padding: 0.25rem 0.5rem;
              border-radius: 4px;
              border: 1px solid #e0e0e0;
              background-color: #f5f5f5;
              font-size: 0.75rem;
              cursor: pointer;
              transition: all 0.2s;
              
              &:hover {
                background-color: #eee;
              }
              
              &.active {
                background-color: #e6f0ff;
                border-color: #b3d1ff;
                color: #0055cc;
              }
            }
          }
        }
      }
    }
  }
  
  .active-classes {
    margin-top: 1rem;
    
    .section-header {
      margin-bottom: 0.5rem;
      
      h4 {
        margin: 0;
        font-size: 0.875rem;
        font-weight: 500;
        color: #333;
      }
    }
    
    .class-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      
      .class-tag {
        display: flex;
        align-items: center;
        padding: 0.25rem 0.5rem;
        background-color: #e6f0ff;
        border-radius: 4px;
        font-size: 0.75rem;
        font-family: monospace;
        
        .tag-name {
          margin-right: 0.25rem;
        }
        
        .tag-remove {
          width: 16px;
          height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #d1e5ff;
          border-radius: 50%;
          border: none;
          font-size: 0.625rem;
          color: #0055cc;
          cursor: pointer;
          transition: background-color 0.2s;
          
          &:hover {
            background-color: #b3d1ff;
          }
        }
      }
    }
  }
}
