import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule, NgIf } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

import { ComponentStoreService } from '../../services/component-store.service';
import { BuilderComponent, PageConfig } from '../../models/builder-component.interface';
import { BuilderConfigService } from '../../services/builder-config.service';
import { TailwindClassEditorComponent } from './tailwind-class-editor/tailwind-class-editor.component';

interface SpecificProperties {
  [key: string]: any;
  content?: string;
  text?: string;
  type?: string;
  src?: string;
  alt?: string;
  url?: string;
  branch?: string;
  status?: string;
  isBlocked?: boolean;
}

@Component({
  selector: 'app-properties-panel',
  templateUrl: './properties-panel.component.html',
  styleUrls: ['./properties-panel.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    NgIf,
    FormsModule,
    TailwindClassEditorComponent
  ]
})
export class PropertiesPanelComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  // Selected component
  selectedComponent: BuilderComponent | null = null;
  
  // Tailwind classes
  tailwindClasses = '';
  
  // Canvas/Page Tailwind classes
  canvasClasses = '';
  
  // Current page
  currentPage: PageConfig | null = null;
  
  // Component-specific properties
  specificProperties: SpecificProperties = {};
  
  constructor(
    private componentStore: ComponentStoreService,
    private configService: BuilderConfigService
  ) {}
  
  ngOnInit(): void {
    // Subscribe to selected component changes
    this.componentStore.getSelectedComponent().pipe(
      takeUntil(this.destroy$)
    ).subscribe(component => {
      // Clear previous state first
      this.tailwindClasses = '';
      this.specificProperties = {};
      
      // Update state with new component data if available
      this.selectedComponent = component || null;
      
      if (component) {
        console.log('Properties panel selected component:', component.id, component.type);
        
        // Initialize properties if they don't exist
        if (!component.properties) {
          component.properties = { tailwindClasses: '' };
        } else if (!component.properties.tailwindClasses) {
          component.properties.tailwindClasses = '';
        }
        
        // Extract Tailwind classes
        this.tailwindClasses = component.properties.tailwindClasses;
        console.log('Tailwind classes loaded:', this.tailwindClasses);
        
        // Extract component-specific properties
        this.extractSpecificProperties(component);
      }
    });
    
    // Subscribe to current page changes
    this.componentStore.getActivePage().pipe(
      takeUntil(this.destroy$)
    ).subscribe(page => {
      this.currentPage = page;
      
      // Extract Canvas Tailwind classes
      if (page) {
        this.canvasClasses = page.properties?.tailwindClasses || '';
        if (!this.canvasClasses && page.styles?.backgroundColor) {
          this.canvasClasses = page.styles.backgroundColor;
        }
        console.log('Canvas classes loaded:', this.canvasClasses);
      }
    });
  }
  
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
  
  /**
   * Handle classes changed event from TailwindClassEditor
   */
  updateTailwindClassesFromEvent(newClasses: string): void {
    console.log('Updating Tailwind classes:', newClasses);
    this.tailwindClasses = newClasses;
    this.updateTailwindClasses();
  }

  /**
   * Update Tailwind classes
   */
  updateTailwindClasses(): void {
    if (!this.selectedComponent) return;
    
    // Ensure properties object exists with required tailwindClasses
    if (!this.selectedComponent.properties) {
      this.selectedComponent.properties = { tailwindClasses: this.tailwindClasses };
    } else {
      this.selectedComponent.properties = {
        ...this.selectedComponent.properties,
        tailwindClasses: this.tailwindClasses
      };
    }

    console.log('Updating component properties:', this.selectedComponent.properties);
    
    // Update component store with the full properties object
    this.componentStore.updateComponentProperties(this.selectedComponent.id, this.selectedComponent.properties);
  }
  
  /**
   * Update Canvas Tailwind Classes
   */
  updateCanvasTailwindClasses(newClasses: string): void {
    this.canvasClasses = newClasses;
    
    if (!this.currentPage) return;
    
    // Update the page properties
    const updatedPage = {
      ...this.currentPage,
      properties: {
        ...(this.currentPage.properties || {}),
        tailwindClasses: newClasses
      }
    };
    
    // Update the page in the component store
    this.componentStore.setActivePage(updatedPage);
    
    // Mark the config as dirty
    this.configService.markAsDirty();
    
    console.log('Updated canvas classes:', newClasses);
  }
  
  /**
   * Update specific property
   */
  updateProperty(property: string, value: any): void {
    if (!this.selectedComponent) return;
    
    this.componentStore.updateComponentProperties(this.selectedComponent.id, {
      [property]: value
    });
  }
  
  /**
   * Extract component-specific properties
   */
  private extractSpecificProperties(component: BuilderComponent): void {
    this.specificProperties = {};
    
    // Get properties based on component type
    switch (component.type) {
      case 'git':
        this.specificProperties['url'] = component.properties['url'] || '';
        this.specificProperties['branch'] = component.properties['branch'] || 'main';
        this.specificProperties['status'] = component.properties['status'] || 'pending';
        this.specificProperties['isBlocked'] = component.properties['isBlocked'] || false;
        break;
      case 'text':
        this.specificProperties['content'] = component.properties['content'] || '';
        break;
      case 'button':
        this.specificProperties['text'] = component.properties['text'] || '';
        this.specificProperties['type'] = component.properties['type'] || 'button';
        break;
      case 'image':
        this.specificProperties['src'] = component.properties['src'] || '';
        this.specificProperties['alt'] = component.properties['alt'] || '';
        break;
      case 'container':
        // Container-specific properties can be added here
        break;
    }
  }
  
  /**
   * Get component type label for display
   */
  getComponentTypeLabel(): string {
    if (!this.selectedComponent) return '';
    
    const typeMap: Record<string, string> = {
      container: 'Container',
      text: 'Text',
      button: 'Button',
      image: 'Image',
      input: 'Input',
      git: 'Git Repository',
    };
    
    return typeMap[this.selectedComponent.type] || this.selectedComponent.type;
  }
}
