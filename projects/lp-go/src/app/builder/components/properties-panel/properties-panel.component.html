<div class="properties-panel">
  <div class="panel-header">
    <h2>Properties</h2>
  </div>
  
  <!-- No component selected state - show Canvas properties -->
  <div class="canvas-properties" *ngIf="!selectedComponent">
    <div class="component-header">
      <div class="component-type">Canvas</div>
    </div>
    
    <div class="property-section">
      <h3>Canvas Properties</h3>
      
      <!-- Canvas background -->
      <div class="property-group">
        <label>Tailwind Classes:</label>
        <app-tailwind-class-editor
          [currentClasses]="canvasClasses"
          (classesChanged)="updateCanvasTailwindClasses($event)">
        </app-tailwind-class-editor>
      </div>
    </div>
  </div>
  
  <!-- Component properties -->
  <div class="panel-content" *ngIf="selectedComponent">
    <div class="component-header">
      <div class="component-type">{{ getComponentTypeLabel() }}</div>
      <div class="component-id">ID: {{ selectedComponent['id'] }}</div>
    </div>
    
    <!-- Component-specific properties -->
    <div class="property-section">
      <h3>Properties</h3>
      
      <!-- Text component properties -->
      <div *ngIf="selectedComponent['type'] === 'text'" class="property-group">
        <label>Content:</label>
        <textarea 
          [(ngModel)]="specificProperties['content']"
          (blur)="updateProperty('content', specificProperties['content'])"
          rows="3"
          class="property-input">
        </textarea>
      </div>
      
      <!-- Button component properties -->
      <div *ngIf="selectedComponent['type'] === 'button'" class="property-group">
        <label>Button Text:</label>
        <input 
          type="text"
          [(ngModel)]="specificProperties['text']"
          (blur)="updateProperty('text', specificProperties['text'])"
          class="property-input">
          
        <label>Button Type:</label>
        <select 
          [(ngModel)]="specificProperties['type']"
          (change)="updateProperty('type', specificProperties['type'])"
          class="property-input">
          <option value="button">Button</option>
          <option value="submit">Submit</option>
          <option value="reset">Reset</option>
        </select>
      </div>
      
      <!-- Image component properties -->
      <div *ngIf="selectedComponent['type'] === 'image'" class="property-group">
        <label>Image URL:</label>
        <input 
          type="text"
          [(ngModel)]="specificProperties['src']"
          (blur)="updateProperty('src', specificProperties['src'])"
          class="property-input">
          
        <label>Alt Text:</label>
        <input 
          type="text"
          [(ngModel)]="specificProperties['alt']"
          (blur)="updateProperty('alt', specificProperties['alt'])"
          class="property-input">
      </div>
    </div>
    
    <!-- Tailwind classes -->
    <div class="property-section">
      <h3>Styling</h3>
      
      <!-- New TailwindClassEditor component -->
      <app-tailwind-class-editor
        [currentClasses]="tailwindClasses"
        (classesChanged)="updateTailwindClassesFromEvent($event)">
      </app-tailwind-class-editor>
    </div>
  </div>
</div>
