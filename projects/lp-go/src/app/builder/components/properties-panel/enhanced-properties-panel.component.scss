.enhanced-properties-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-left: 1px solid #e5e7eb;
}

.panel-header {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  
  h2 {
    margin: 0 0 1rem 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
  }
}

.panel-controls {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.search-wrapper {
  position: relative;
  flex: 1;
  
  .search-input {
    width: 100%;
    padding: 0.5rem 2rem 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    
    &:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    }
  }
  
  .search-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    pointer-events: none;
  }
}

.control-buttons {
  display: flex;
  gap: 0.5rem;
}

.control-btn {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background: white;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.15s ease;
  
  &:hover {
    background: #f3f4f6;
  }
  
  &.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #6b7280;
  
  .loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.no-selection {
  padding: 1rem;
  
  .no-selection-content {
    text-align: center;
    padding: 2rem 1rem;
    color: #6b7280;
    
    .no-selection-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }
    
    h3 {
      margin: 0 0 0.5rem 0;
      font-size: 1.125rem;
      color: #374151;
    }
    
    p {
      margin: 0;
      font-size: 0.875rem;
    }
  }
}

.canvas-section {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
  
  h3 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
  }
  
  .property-group {
    margin-bottom: 1rem;
    
    label {
      display: block;
      margin-bottom: 0.5rem;
      font-size: 0.875rem;
      font-weight: 500;
      color: #374151;
    }
  }
}

.component-properties {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.component-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
  
  .component-info {
    .component-type {
      font-size: 1.125rem;
      font-weight: 600;
      color: #111827;
      margin-bottom: 0.25rem;
    }
    
    .component-id {
      font-size: 0.75rem;
      color: #6b7280;
      font-family: monospace;
    }
  }
  
  .validation-status {
    .error-count {
      background: #fef2f2;
      color: #dc2626;
      padding: 0.25rem 0.5rem;
      border-radius: 0.25rem;
      font-size: 0.75rem;
      font-weight: 500;
    }
  }
}

.property-groups {
  margin-bottom: 2rem;
}

.property-group-container {
  margin-bottom: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: #f9fafb;
  cursor: pointer;
  transition: background-color 0.15s ease;

  &:hover {
    background: #f3f4f6;
  }

  &:focus {
    outline: 2px solid #3b82f6;
    outline-offset: -2px;
    background: #f3f4f6;
  }

  &.expanded {
    background: #eff6ff;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .group-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    
    .group-icon {
      font-size: 1rem;
    }
    
    .group-label {
      font-weight: 500;
      color: #374151;
    }
    
    .property-count {
      font-size: 0.75rem;
      color: #6b7280;
    }

    .validation-indicator {
      margin-left: 0.5rem;
      font-size: 0.875rem;

      &.status-error {
        color: #dc2626;
      }

      &.status-warning {
        color: #d97706;
      }
    }
  }
  
  .expand-icon {
    color: #6b7280;
    font-size: 0.875rem;
    transition: transform 0.15s ease;
  }
}

.group-description {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
  color: #6b7280;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.group-properties {
  padding: 1rem;
}

.property-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
  
  .action-group {
    display: flex;
    gap: 0.5rem;
  }
}

.action-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  
  &.secondary {
    background: white;
    color: #374151;
    border: 1px solid #d1d5db;
    
    &:hover {
      background: #f9fafb;
    }
  }
  
  &.primary {
    background: #3b82f6;
    color: white;
    border: 1px solid #3b82f6;
    
    &:hover {
      background: #2563eb;
    }
  }
}

.error-summary {
  margin-top: 1rem;
  padding: 1rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.5rem;
  
  h4 {
    margin: 0 0 0.75rem 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: #dc2626;
  }
  
  .error-list {
    .error-item {
      margin-bottom: 0.5rem;
      font-size: 0.75rem;
      
      strong {
        color: #dc2626;
      }
      
      ul {
        margin: 0.25rem 0 0 1rem;
        padding: 0;
        
        li {
          color: #7f1d1d;
        }
      }
    }
  }
}
