import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';

import { TemplateGalleryComponent } from '../template-gallery/template-gallery.component';
import { CreateTemplateDialogComponent } from '../create-template-dialog/create-template-dialog.component';
import {
  Template,
  TemplateApplicationOptions
} from '../../models/template.interface';
import { TemplateService } from '../../services/template.service';
import { ComponentStoreService } from '../../services/component-store.service';
import { BuilderConfigService } from '../../services/builder-config.service';

/**
 * Template Browser Component
 * Main interface for browsing and applying templates in the builder
 */
@Component({
  selector: 'app-template-browser',
  standalone: true,
  imports: [CommonModule, TemplateGalleryComponent, CreateTemplateDialogComponent],
  templateUrl: './template-browser.component.html',
  styleUrls: ['./template-browser.component.scss']
})
export class TemplateBrowserComponent implements OnInit, OnDestroy {
  isVisible = false;
  isLoading = false;
  selectedTemplate: Template | null = null;
  showCreateDialog = false;
  
  private destroy$ = new Subject<void>();

  constructor(
    private templateService: TemplateService,
    private componentStore: ComponentStoreService,
    private configService: BuilderConfigService
  ) {}

  ngOnInit(): void {
    // Component initialization
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Show the template browser
   */
  show(): void {
    this.isVisible = true;
  }

  /**
   * Hide the template browser
   */
  hide(): void {
    this.isVisible = false;
    this.selectedTemplate = null;
  }

  /**
   * Toggle template browser visibility
   */
  toggle(): void {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }

  /**
   * Handle template selection from gallery
   */
  onTemplateSelected(template: Template): void {
    this.selectedTemplate = template;
    console.log('Template selected:', template.name);
  }

  /**
   * Handle template application from gallery
   */
  onTemplateApplied(event: { template: Template; options: TemplateApplicationOptions }): void {
    this.applyTemplate(event.template, event.options);
  }

  /**
   * Apply template to current page
   */
  applyTemplate(template: Template, options: TemplateApplicationOptions): void {
    this.isLoading = true;

    // Get current active page
    this.componentStore.getActivePage().pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (activePage) => {
        if (!activePage) {
          console.error('No active page to apply template to');
          this.isLoading = false;
          return;
        }

        // Set target page ID
        const finalOptions: TemplateApplicationOptions = {
          ...options,
          targetPageId: activePage.id
        };

        // Apply the template
        this.templateService.applyTemplate(template.id, finalOptions).pipe(
          takeUntil(this.destroy$)
        ).subscribe({
          next: () => {
            console.log(`Successfully applied template: ${template.name}`);
            this.isLoading = false;
            
            // Mark config as dirty
            this.configService.markAsDirty();
            
            // Hide browser after successful application
            this.hide();
          },
          error: (error) => {
            console.error('Error applying template:', error);
            this.isLoading = false;
            // Could show error notification here
          }
        });
      },
      error: (error) => {
        console.error('Error getting active page:', error);
        this.isLoading = false;
      }
    });
  }

  /**
   * Create new template from current page
   */
  createTemplateFromCurrentPage(): void {
    this.showCreateDialog = true;
  }

  /**
   * Handle template creation completion
   */
  onTemplateCreated(template: any): void {
    console.log('New template created:', template);
    this.showCreateDialog = false;
    // Could refresh the gallery or show success message
  }

  /**
   * Handle create dialog close
   */
  onCreateDialogClosed(): void {
    this.showCreateDialog = false;
  }

  /**
   * Handle escape key to close browser
   */
  onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Escape') {
      this.hide();
    }
  }

  /**
   * Handle backdrop click to close browser
   */
  onBackdropClick(event: MouseEvent): void {
    if (event.target === event.currentTarget) {
      this.hide();
    }
  }
}
