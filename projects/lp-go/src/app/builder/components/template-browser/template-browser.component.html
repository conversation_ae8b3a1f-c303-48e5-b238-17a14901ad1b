<!-- Template Browser Modal -->
<div 
  *ngIf="isVisible" 
  class="template-browser-overlay"
  (click)="onBackdropClick($event)"
  (keydown)="onKeyDown($event)"
  tabindex="0">
  
  <div class="template-browser-modal">
    <!-- Modal Header -->
    <div class="modal-header">
      <h2 class="modal-title">Template Browser</h2>
      <div class="header-actions">
        <button
          class="action-button secondary"
          (click)="createTemplateFromCurrentPage()"
          title="Save current page as template">
          <span class="icon">💾</span>
          Save as Template
        </button>
        <button
          class="close-button"
          (click)="hide()"
          title="Close template browser">
          <span class="icon">✕</span>
        </button>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div *ngIf="isLoading" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <p class="loading-text">Applying template...</p>
      </div>
    </div>

    <!-- Template Gallery -->
    <div class="modal-body">
      <app-template-gallery
        [showHeader]="true"
        [allowSelection]="true"
        [maxHeight]="'70vh'"
        (templateSelected)="onTemplateSelected($event)"
        (templateApplied)="onTemplateApplied($event)">
      </app-template-gallery>
    </div>

    <!-- Modal Footer -->
    <div class="modal-footer" *ngIf="selectedTemplate">
      <div class="selected-template-info">
        <h3 class="selected-name">{{ selectedTemplate.name }}</h3>
        <p class="selected-description">{{ selectedTemplate.description }}</p>
      </div>
      <div class="footer-actions">
        <button
          class="action-button secondary"
          (click)="applyTemplate(selectedTemplate, { replaceContent: false, generateNewIds: true, targetPageId: 'current' })"
          [disabled]="isLoading">
          Add to Page
        </button>
        <button
          class="action-button primary"
          (click)="applyTemplate(selectedTemplate, { replaceContent: true, generateNewIds: true, targetPageId: 'current' })"
          [disabled]="isLoading">
          Replace Page
        </button>
      </div>
    </div>
  </div>

  <!-- Create Template Dialog -->
  <app-create-template-dialog
    [isVisible]="showCreateDialog"
    [sourcePageId]="'current'"
    (templateCreated)="onTemplateCreated($event)"
    (dialogClosed)="onCreateDialogClosed()">
  </app-create-template-dialog>
</div>
