.template-browser-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;

  .template-browser-modal {
    background: white;
    border-radius: 12px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    width: 100%;
    max-width: 1200px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.5rem 2rem;
      border-bottom: 1px solid #e2e8f0;
      background: #f8fafc;

      .modal-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #1a202c;
        margin: 0;
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 1rem;

        .action-button {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem 1rem;
          border: 1px solid #e2e8f0;
          border-radius: 6px;
          font-size: 0.875rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;

          &.primary {
            background: #3182ce;
            color: white;
            border-color: #3182ce;

            &:hover:not(:disabled) {
              background: #2c5aa0;
            }

            &:disabled {
              background: #a0aec0;
              border-color: #a0aec0;
              cursor: not-allowed;
            }
          }

          &.secondary {
            background: white;
            color: #4a5568;

            &:hover:not(:disabled) {
              background: #f7fafc;
            }

            &:disabled {
              color: #a0aec0;
              cursor: not-allowed;
            }
          }

          .icon {
            font-size: 1rem;
          }
        }

        .close-button {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 2rem;
          height: 2rem;
          border: none;
          background: none;
          color: #718096;
          cursor: pointer;
          border-radius: 4px;
          transition: all 0.2s;

          &:hover {
            background: #edf2f7;
            color: #4a5568;
          }

          .icon {
            font-size: 1.25rem;
          }
        }
      }
    }

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;

      .loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;

        .loading-spinner {
          width: 3rem;
          height: 3rem;
          border: 3px solid #e2e8f0;
          border-top: 3px solid #3182ce;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        .loading-text {
          font-size: 1rem;
          color: #4a5568;
          margin: 0;
        }
      }
    }

    .modal-body {
      flex: 1;
      overflow: hidden;
      padding: 0;

      app-template-gallery {
        display: block;
        height: 100%;
      }
    }

    .modal-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.5rem 2rem;
      border-top: 1px solid #e2e8f0;
      background: #f8fafc;

      .selected-template-info {
        flex: 1;

        .selected-name {
          font-size: 1.125rem;
          font-weight: 600;
          color: #1a202c;
          margin: 0 0 0.25rem 0;
        }

        .selected-description {
          font-size: 0.875rem;
          color: #718096;
          margin: 0;
          line-height: 1.4;
        }
      }

      .footer-actions {
        display: flex;
        gap: 0.75rem;

        .action-button {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.75rem 1.5rem;
          border: 1px solid #e2e8f0;
          border-radius: 6px;
          font-size: 0.875rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
          min-width: 120px;
          justify-content: center;

          &.primary {
            background: #3182ce;
            color: white;
            border-color: #3182ce;

            &:hover:not(:disabled) {
              background: #2c5aa0;
            }

            &:disabled {
              background: #a0aec0;
              border-color: #a0aec0;
              cursor: not-allowed;
            }
          }

          &.secondary {
            background: white;
            color: #4a5568;

            &:hover:not(:disabled) {
              background: #f7fafc;
            }

            &:disabled {
              color: #a0aec0;
              cursor: not-allowed;
            }
          }
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive adjustments
@media (max-width: 768px) {
  .template-browser-overlay {
    padding: 1rem;

    .template-browser-modal {
      max-height: 95vh;

      .modal-header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;

        .header-actions {
          justify-content: space-between;
        }
      }

      .modal-footer {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;

        .selected-template-info {
          text-align: center;
        }

        .footer-actions {
          justify-content: center;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .template-browser-overlay {
    padding: 0.5rem;

    .template-browser-modal {
      .modal-header {
        .header-actions {
          flex-direction: column;
          gap: 0.5rem;

          .action-button {
            width: 100%;
            justify-content: center;
          }
        }
      }

      .modal-footer {
        .footer-actions {
          flex-direction: column;

          .action-button {
            width: 100%;
          }
        }
      }
    }
  }
}
