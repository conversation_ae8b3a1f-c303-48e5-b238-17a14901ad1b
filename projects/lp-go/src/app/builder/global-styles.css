/* Global styles that will override Angular's encapsulation */

/* Make all container components clearly visible */
app-container-component {
  display: block !important;
  min-height: 120px !important;
  border: 3px dashed #ff0000 !important;
  margin: 20px !important;
  padding: 20px !important;
  background-color: rgba(255, 255, 0, 0.2) !important;
  position: relative !important;
}

/* Add a label */
app-container-component::before {
  content: 'CONTAINER';
  position: absolute;
  top: 0;
  left: 0;
  background-color: red;
  color: white;
  padding: 3px 6px;
  font-size: 10px;
  z-index: 9999;
}

/* Hover state */
app-container-component.cdk-drop-list-dragging,
app-container-component:hover {
  border: 3px dashed #ff00ff !important;
  background-color: rgba(255, 0, 255, 0.2) !important;
  box-shadow: 0 0 15px 5px rgba(255, 0, 255, 0.5) !important;
}
