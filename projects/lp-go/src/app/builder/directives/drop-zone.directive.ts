import { Directive, ElementRef, Input, OnInit, OnChanges, SimpleChanges, Renderer2, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';

/**
 * Directive to mark an element as a drop zone for drag and drop operations
 * Also provides visual feedback for allowed component types
 */
@Directive({
  selector: '[appDropZone]',
  standalone: true
})
export class DropZoneDirective implements OnInit, OnChanges {
  @Input() componentType = 'container';
  @Input() acceptedTypes: string[] = []; // Types of components this zone accepts

  constructor(
    private el: ElementRef,
    private renderer: Renderer2
  ) {}

  ngOnInit(): void {
    this.updateAttributes();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Update attributes when inputs change
    if (changes['componentType'] || changes['acceptedTypes']) {
      this.updateAttributes();
    }
  }

  /**
   * Listen for dragenter events to provide visual feedback
   */
  @HostListener('dragenter', ['$event'])
  onDragEnter(event: DragEvent): void {
    // Add active class when dragging over
    this.renderer.addClass(this.el.nativeElement, 'drop-zone-active');
    console.log('Drag entered drop zone:', this.componentType);
  }
  
  /**
   * Listen for dragleave events to remove visual feedback
   */
  @HostListener('dragleave', ['$event'])
  onDragLeave(event: DragEvent): void {
    // Remove active class when dragging out
    this.renderer.removeClass(this.el.nativeElement, 'drop-zone-active');
    console.log('Drag left drop zone:', this.componentType);
  }
  
  private updateAttributes(): void {
    // Add data attributes for component type
    this.renderer.setAttribute(
      this.el.nativeElement, 
      'data-component-type', 
      this.componentType
    );
    
    // By default, containers should accept all types if none are specified
    if (!this.acceptedTypes || this.acceptedTypes.length === 0) {
      if (this.componentType === 'container') {
        // Default to accepting common component types if none specified
        this.acceptedTypes = ['text', 'button', 'image', 'container', 'input', 'heading'];
      }
    }

    // Set accepted types attribute for use in drag-drop logic
    this.renderer.setAttribute(
      this.el.nativeElement,
      'data-accepted-types',
      this.acceptedTypes.join(',')
    );
    
    // Add drop zone class
    this.renderer.addClass(this.el.nativeElement, 'drop-zone');
    
    // Make sure the element is properly set up for drag-drop
    this.renderer.setAttribute(this.el.nativeElement, 'cdkDropListEnterPredicate', 'true');
    
    console.log('Drop zone initialized:', {
      type: this.componentType,
      acceptedTypes: this.acceptedTypes
    });
  }
}
