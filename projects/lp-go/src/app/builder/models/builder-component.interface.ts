/**
 * Interface for application configuration
 */
export interface AppConfig {
  /** Unique identifier for the app configuration */
  id: string;
  
  /** Application name */
  name: string;
  
  /** Array of pages in the application */
  pages: PageConfig[];
  
  /** Application version */
  version: string;
  
  /** Creation timestamp */
  createdAt: string;
  
  /** Last modified timestamp */
  updatedAt: string;
  
  /** Application owner */
  owner?: string;
  
  /** Application settings */
  settings?: {
    theme?: string;
    [key: string]: any;
  };
}

/**
 * Interface defining a component within the builder
 */
export interface BuilderComponent {
  /** Unique identifier for the component */
  id: string;
  
  /** Component type (container, text, button, etc.) */
  type: string;
  
  /** Reference to parent component, null for root-level components */
  parentId: string | null;
  
  /** Component properties including Tailwind classes and other settings */
  properties: {
    /** Tailwind classes applied to the component */
    tailwindClasses: string;
    
    /** Component content (for text components) */
    content?: string;
    
    /** Component source (for image components) */
    src?: string;
    
    /** Additional component-specific properties */
    [key: string]: any;
  };
  
  /** Array of child component IDs for container components */
  children: string[];

  /** Whether this component can contain other components */
  isContainer: boolean;

  /** Optional restriction on child component types */
  allowedChildTypes?: string[];

  /** Component label for display in the component tree */
  label?: string;

  /** Component position for layout purposes */
  position?: ComponentPosition;

  /** Component size for layout purposes */
  size?: {
    width: string | number;
    height: string | number;
  };

  /** Component display styles (optional) */
  styles?: {
    backgroundColor?: string;
    [key: string]: string | undefined;
  };
}

/**
 * Interface for page configuration
 */
export interface PageConfig {
  /** Unique identifier for the page */
  id: string;
  
  /** Page name for display */
  name: string;
  
  /** Array of root-level components on the page */
  components: string[];
  
  /** Page type */
  type: 'builder' | 'config';
  
  /** Page layout */
  layout: 'fluid' | 'fixed';

  /** Whether the page requires authentication */
  secure?: boolean;

  /** Page properties including Tailwind classes */
  properties?: {
    /** Tailwind classes for the canvas */
    tailwindClasses?: string;

    /** Additional page-specific properties */
    [key: string]: any;
  };

  /** Page styles (legacy, prefer using tailwindClasses) */
  styles?: {
    backgroundColor?: string;
    backgroundImage?: string;
    [key: string]: any;
  };

  /** Original config data if converted from API */
  originalConfig?: any;

  /** Canvas state for this page */
  canvasState?: CanvasState;
}

// Canvas state management interfaces
export interface GridConfig {
  enabled: boolean;
  size: number; // pixels
  color: string;
  opacity: number;
  snapToGrid: boolean;
}

export interface CanvasState {
  zoom: number;
  gridEnabled: boolean;
  gridConfig: GridConfig;
  selectedComponents: string[];
  viewportOffset: { x: number; y: number };
  lastModified: Date;
}

export interface ZoomLevel {
  value: number;
  label: string;
}

// Selection state for enhanced component selection
export interface SelectionState {
  selectedIds: string[];
  hoveredId: string | null;
  multiSelectMode: boolean;
  selectionRect?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

/**
 * Interface for component preset
 */
export interface ComponentPreset {
  /** Unique identifier for the preset */
  id: string;
  
  /** Preset name for display */
  name: string;
  
  /** Component type this preset applies to */
  componentType: string;
  
  /** Predefined Tailwind classes */
  tailwindClasses: string;
  
  /** Category for grouping presets */
  category: string;
  
  /** Optional description */
  description?: string;
  
  /** Preview image or icon */
  preview?: string;
}

/**
 * Alignment types for group operations
 */
export enum AlignmentType {
  LEFT = 'left',
  CENTER_HORIZONTAL = 'centerHorizontal',
  RIGHT = 'right',
  TOP = 'top',
  CENTER_VERTICAL = 'centerVertical',
  BOTTOM = 'bottom'
}

/**
 * Distribution types for group operations
 */
export enum DistributionType {
  HORIZONTAL = 'horizontal',
  VERTICAL = 'vertical'
}

/**
 * Interface for clipboard data
 */
export interface ClipboardData {
  components: BuilderComponent[];
  timestamp: string;
  sourcePageId: string;
  relativePositions: ComponentPosition[];
}

/**
 * Component position for clipboard operations
 */
export interface ComponentPosition {
  id: string;
  x: number;
  y: number;
}

/**
 * Interface for bulk property values
 */
export interface BulkPropertyValue {
  value: any;
  isMixed: boolean;
  affectedComponents: string[];
}

/**
 * Interface for bulk property editing
 */
export interface BulkPropertyUpdate {
  property: string;
  value: any;
  componentIds: string[];
}

/**
 * Interface for drag item data
 */
export interface DragItem {
  /** Source type: palette or canvas */
  source: 'palette' | 'canvas';
  
  /** Component type being dragged */
  componentType?: string;
  
  /** Component ID if dragging from canvas */
  componentId?: string;
  
  /** Preset ID if dragging a preset */
  presetId?: string;
}
