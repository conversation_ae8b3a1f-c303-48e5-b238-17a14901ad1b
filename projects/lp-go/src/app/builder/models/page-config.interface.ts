import { BuilderComponent } from './builder-component.interface';

export interface PageConfig {
  id: string;
  name: string;
  type: 'builder' | 'config';
  layout: 'fluid' | 'fixed';
  components: BuilderComponent[];
  styles: {
    backgroundColor?: string;
    backgroundImage?: string;
    [key: string]: any;
  };
  originalConfig?: any; // Original config from API if this is a converted page
}

export interface AppConfig {
  version?: string;
  env?: string;
  client?: string;
  pages: Record<string, PageConfig | any>;
  [key: string]: any;
}

export interface ConfigParams {
  version: string;
  env: string;
  client: string;
}
