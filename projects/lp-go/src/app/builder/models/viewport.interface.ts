/**
 * Viewport and responsive design interfaces
 */

/**
 * Device types for responsive design
 */
export enum DeviceType {
  MOBILE = 'mobile',
  TABLET = 'tablet',
  DESKTOP = 'desktop'
}

/**
 * Device orientation
 */
export enum Orientation {
  PORTRAIT = 'portrait',
  LANDSCAPE = 'landscape'
}

/**
 * Breakpoint definition
 */
export interface Breakpoint {
  /** Unique identifier */
  id: string;
  
  /** Display name */
  name: string;
  
  /** Viewport width in pixels */
  width: number;
  
  /** Viewport height in pixels */
  height: number;
  
  /** Device type category */
  deviceType: DeviceType;
  
  /** Device orientation */
  orientation: Orientation;
  
  /** CSS media query */
  mediaQuery: string;
  
  /** Whether this is a built-in breakpoint */
  isBuiltIn: boolean;
  
  /** Icon for UI display */
  icon?: string;
}

/**
 * Device preset for common devices
 */
export interface DevicePreset {
  /** Unique identifier */
  id: string;
  
  /** Device name */
  name: string;
  
  /** Device brand */
  brand: string;
  
  /** Device type */
  deviceType: DeviceType;
  
  /** Portrait dimensions */
  portrait: {
    width: number;
    height: number;
  };
  
  /** Landscape dimensions */
  landscape: {
    width: number;
    height: number;
  };
  
  /** Device pixel ratio */
  pixelRatio: number;
  
  /** User agent string */
  userAgent?: string;
  
  /** Device frame image or CSS */
  frame?: {
    image?: string;
    css?: string;
  };
}

/**
 * Responsive property value
 */
export interface ResponsiveProperty<T = any> {
  /** Default value (fallback) */
  default: T;
  
  /** Mobile-specific value */
  mobile?: T;
  
  /** Tablet-specific value */
  tablet?: T;
  
  /** Desktop-specific value */
  desktop?: T;
  
  /** Custom breakpoint values */
  custom?: Record<string, T>;
}

/**
 * Responsive component properties
 */
export interface ResponsiveComponentProperties {
  [propertyKey: string]: ResponsiveProperty | any;
}

/**
 * Viewport state
 */
export interface ViewportState {
  /** Currently active breakpoint */
  currentBreakpoint: Breakpoint;
  
  /** Whether responsive mode is enabled */
  responsiveModeEnabled: boolean;
  
  /** Whether device frame is shown */
  deviceFrameEnabled: boolean;
  
  /** Selected device preset */
  selectedDevice: DevicePreset | null;
  
  /** Current orientation */
  orientation: Orientation;
  
  /** Zoom level for preview */
  zoomLevel: number;
}

/**
 * Responsive validation rule
 */
export interface ResponsiveValidationRule {
  /** Unique rule identifier */
  id: string;
  
  /** Rule name */
  name: string;
  
  /** Rule description */
  description: string;
  
  /** Validation severity */
  severity: 'error' | 'warning' | 'info';
  
  /** Target device types */
  targetDevices: DeviceType[];
  
  /** Validation function */
  validate: (component: any, viewport: Breakpoint) => ResponsiveValidationResult;
}

/**
 * Responsive validation result
 */
export interface ResponsiveValidationResult {
  /** Whether validation passed */
  isValid: boolean;
  
  /** Validation message */
  message: string;
  
  /** Suggested fix */
  suggestion?: string;
  
  /** Auto-fix function */
  autoFix?: () => void;
}

/**
 * Responsive builder state
 */
export interface ResponsiveBuilderState {
  /** Current viewport state */
  viewport: ViewportState;
  
  /** Available breakpoints */
  breakpoints: Breakpoint[];
  
  /** Available device presets */
  devicePresets: DevicePreset[];
  
  /** Active validation rules */
  validationRules: ResponsiveValidationRule[];
  
  /** Current validation results */
  validationResults: Record<string, ResponsiveValidationResult[]>;
}

/**
 * Responsive property change event
 */
export interface ResponsivePropertyChangeEvent {
  /** Component ID */
  componentId: string;
  
  /** Property key */
  propertyKey: string;
  
  /** Target breakpoint */
  breakpoint: string;
  
  /** Old value */
  oldValue: any;
  
  /** New value */
  newValue: any;
  
  /** Whether this affects inheritance */
  affectsInheritance: boolean;
}

/**
 * Device frame configuration
 */
export interface DeviceFrameConfig {
  /** Whether frame is enabled */
  enabled: boolean;
  
  /** Frame type */
  type: 'browser' | 'mobile' | 'tablet';
  
  /** Frame color */
  color: string;
  
  /** Frame scale */
  scale: number;
  
  /** Show device controls */
  showControls: boolean;
  
  /** Show device info */
  showInfo: boolean;
}
