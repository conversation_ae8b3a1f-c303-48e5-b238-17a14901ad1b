/**
 * Property Definition System for Dynamic Input Management
 * Defines metadata for component properties to enable dynamic property panels
 */

// Property data types
export type PropertyType = 
  | 'string' 
  | 'number' 
  | 'boolean' 
  | 'color' 
  | 'select' 
  | 'multiselect'
  | 'textarea' 
  | 'url' 
  | 'email' 
  | 'date' 
  | 'time' 
  | 'datetime'
  | 'range' 
  | 'file' 
  | 'image' 
  | 'icon' 
  | 'json' 
  | 'array'
  | 'object'
  | 'tailwind-classes';

// Property validation rules
export interface PropertyValidation {
  required?: boolean;
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  custom?: (value: any) => string | null; // Returns error message or null
}

// Property options for select/multiselect
export interface PropertyOption {
  label: string;
  value: any;
  description?: string;
  icon?: string;
  disabled?: boolean;
}

// Property group for organization
export interface PropertyGroup {
  id: string;
  label: string;
  description?: string;
  icon?: string;
  expanded?: boolean;
  order?: number;
}

// Main property definition interface
export interface PropertyDefinition {
  // Basic metadata
  key: string;
  label: string;
  description?: string;
  type: PropertyType;
  
  // Default value
  defaultValue?: any;
  
  // Validation
  validation?: PropertyValidation;
  
  // Options for select/multiselect types
  options?: PropertyOption[];
  
  // Grouping and organization
  group?: string; // Group ID
  order?: number;
  
  // UI hints
  placeholder?: string;
  helpText?: string;
  icon?: string;
  
  // Conditional display
  showWhen?: (properties: Record<string, any>) => boolean;
  
  // Advanced configuration
  config?: {
    // For range type
    step?: number;
    
    // For textarea
    rows?: number;
    
    // For file/image
    accept?: string;
    multiple?: boolean;
    
    // For color
    format?: 'hex' | 'rgb' | 'hsl';
    
    // For tailwind-classes
    categories?: string[];
    
    // Custom configuration
    [key: string]: any;
  };
}

// Component property schema
export interface ComponentPropertySchema {
  componentType: string;
  properties: PropertyDefinition[];
  groups: PropertyGroup[];
  metadata?: {
    version: string;
    author?: string;
    description?: string;
    tags?: string[];
  };
}

// Property value with metadata
export interface PropertyValue {
  key: string;
  value: any;
  type: PropertyType;
  isValid: boolean;
  errors: string[];
  isDirty: boolean;
  lastModified: Date;
}

// Property change event
export interface PropertyChangeEvent {
  key: string;
  oldValue: any;
  newValue: any;
  type: PropertyType;
  componentId: string;
  timestamp: Date;
}

// Property validation result
export interface PropertyValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Property registry entry
export interface PropertyRegistryEntry {
  componentType: string;
  schema: ComponentPropertySchema;
  lastUpdated: Date;
  source: 'manual' | 'auto-discovered' | 'imported';
}

// Common property groups
export const COMMON_PROPERTY_GROUPS: PropertyGroup[] = [
  {
    id: 'content',
    label: 'Content',
    description: 'Text, images, and other content properties',
    icon: 'edit',
    expanded: true,
    order: 1
  },
  {
    id: 'appearance',
    label: 'Appearance',
    description: 'Visual styling and appearance properties',
    icon: 'palette',
    expanded: true,
    order: 2
  },
  {
    id: 'layout',
    label: 'Layout',
    description: 'Positioning, sizing, and layout properties',
    icon: 'grid',
    expanded: false,
    order: 3
  },
  {
    id: 'behavior',
    label: 'Behavior',
    description: 'Interactive behavior and functionality',
    icon: 'settings',
    expanded: false,
    order: 4
  },
  {
    id: 'data',
    label: 'Data',
    description: 'Data binding and configuration properties',
    icon: 'database',
    expanded: false,
    order: 5
  },
  {
    id: 'advanced',
    label: 'Advanced',
    description: 'Advanced configuration options',
    icon: 'code',
    expanded: false,
    order: 6
  }
];

// Common property definitions that can be reused
export const COMMON_PROPERTY_DEFINITIONS: Record<string, PropertyDefinition> = {
  // Content properties
  text: {
    key: 'text',
    label: 'Text',
    description: 'The text content to display',
    type: 'string',
    group: 'content',
    order: 1,
    validation: { required: true }
  },
  
  content: {
    key: 'content',
    label: 'Content',
    description: 'The main content',
    type: 'textarea',
    group: 'content',
    order: 1,
    config: { rows: 3 }
  },
  
  title: {
    key: 'title',
    label: 'Title',
    description: 'The title or heading text',
    type: 'string',
    group: 'content',
    order: 1
  },
  
  // Appearance properties
  color: {
    key: 'color',
    label: 'Color',
    description: 'The color scheme',
    type: 'select',
    group: 'appearance',
    order: 1,
    options: [
      { label: 'Primary', value: 'primary' },
      { label: 'Secondary', value: 'secondary' },
      { label: 'Success', value: 'success' },
      { label: 'Warning', value: 'warning' },
      { label: 'Danger', value: 'danger' },
      { label: 'Info', value: 'info' }
    ]
  },
  
  backgroundColor: {
    key: 'backgroundColor',
    label: 'Background Color',
    description: 'The background color',
    type: 'color',
    group: 'appearance',
    order: 2,
    config: { format: 'hex' }
  },
  
  // Layout properties
  width: {
    key: 'width',
    label: 'Width',
    description: 'The width of the component',
    type: 'string',
    group: 'layout',
    order: 1,
    placeholder: 'auto, 100px, 50%, etc.'
  },
  
  height: {
    key: 'height',
    label: 'Height',
    description: 'The height of the component',
    type: 'string',
    group: 'layout',
    order: 2,
    placeholder: 'auto, 100px, 50%, etc.'
  },
  
  // Styling
  tailwindClasses: {
    key: 'tailwindClasses',
    label: 'Tailwind Classes',
    description: 'Custom Tailwind CSS classes',
    type: 'tailwind-classes',
    group: 'appearance',
    order: 10
  }
};
