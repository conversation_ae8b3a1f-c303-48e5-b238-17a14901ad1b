import { BuilderComponent, PageConfig } from './builder-component.interface';

/**
 * Template categories for organization
 */
export enum TemplateCategory {
  PAGE = 'page',
  COMPONENT = 'component',
  LAYOUT = 'layout',
  FORM = 'form',
  CUSTOM = 'custom'
}

/**
 * Template metadata for additional information
 */
export interface TemplateMetadata {
  /** Template author */
  author?: string;
  
  /** Template version */
  version?: string;
  
  /** Required components for this template */
  requiredComponents?: string[];
  
  /** Compatibility information */
  compatibility?: {
    minVersion?: string;
    maxVersion?: string;
  };
  
  /** Usage statistics */
  usage?: {
    timesUsed?: number;
    lastUsed?: string;
  };
  
  /** Template difficulty level */
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  
  /** Estimated setup time */
  estimatedTime?: string;
}

/**
 * Template preview data
 */
export interface TemplatePreview {
  /** Template ID */
  templateId: string;
  
  /** Preview thumbnail URL or base64 */
  thumbnail: string;
  
  /** Component count */
  componentCount: number;
  
  /** Preview description */
  description: string;
  
  /** Preview metadata */
  metadata: TemplateMetadata;
}

/**
 * Main template interface
 */
export interface Template {
  /** Unique template identifier */
  id: string;
  
  /** Template display name */
  name: string;
  
  /** Template description */
  description: string;
  
  /** Template category */
  category: TemplateCategory;
  
  /** Searchable tags */
  tags: string[];
  
  /** Template thumbnail URL or base64 */
  thumbnail: string;
  
  /** Components included in template */
  components: Record<string, BuilderComponent>;
  
  /** Page configuration for template */
  pageConfig: PageConfig;
  
  /** Additional template metadata */
  metadata: TemplateMetadata;
  
  /** Creation timestamp */
  createdAt: string;
  
  /** Last update timestamp */
  updatedAt: string;
  
  /** Whether template is built-in or user-created */
  isBuiltIn: boolean;
  
  /** Whether template is favorited by user */
  isFavorite?: boolean;
}

/**
 * Template application options
 */
export interface TemplateApplicationOptions {
  /** Target page ID */
  targetPageId: string;
  
  /** Whether to replace existing content */
  replaceContent?: boolean;
  
  /** Position offset for components */
  positionOffset?: {
    x: number;
    y: number;
  };
  
  /** Whether to generate new component IDs */
  generateNewIds?: boolean;
  
  /** Custom property overrides */
  propertyOverrides?: Record<string, any>;
}

/**
 * Template search and filter options
 */
export interface TemplateSearchOptions {
  /** Search query */
  query?: string;
  
  /** Filter by category */
  category?: TemplateCategory;
  
  /** Filter by tags */
  tags?: string[];
  
  /** Filter by author */
  author?: string;
  
  /** Sort options */
  sortBy?: 'name' | 'date' | 'popularity' | 'category';
  
  /** Sort direction */
  sortDirection?: 'asc' | 'desc';
  
  /** Show only favorites */
  favoritesOnly?: boolean;
  
  /** Show only built-in templates */
  builtInOnly?: boolean;
}

/**
 * Template creation data
 */
export interface TemplateCreationData {
  /** Template name */
  name: string;
  
  /** Template description */
  description: string;
  
  /** Template category */
  category: TemplateCategory;
  
  /** Template tags */
  tags: string[];
  
  /** Source page ID to create template from */
  sourcePageId: string;
  
  /** Additional metadata */
  metadata?: Partial<TemplateMetadata>;
}

/**
 * Template validation result
 */
export interface TemplateValidationResult {
  /** Whether template is valid */
  isValid: boolean;
  
  /** Validation errors */
  errors: string[];
  
  /** Validation warnings */
  warnings: string[];
  
  /** Missing components */
  missingComponents: string[];
  
  /** Compatibility issues */
  compatibilityIssues: string[];
}

/**
 * Template import/export data
 */
export interface TemplateExportData {
  /** Template data */
  template: Template;
  
  /** Export format version */
  formatVersion: string;
  
  /** Export timestamp */
  exportedAt: string;
  
  /** Additional export metadata */
  exportMetadata?: Record<string, any>;
}
