import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, map } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';
import { 
  BuilderComponent, 
  PageConfig, 
  CanvasState, 
  SelectionState, 
  GridConfig, 
  ZoomLevel,
  AlignmentType,
  DistributionType,
  BulkPropertyUpdate,
  ComponentPosition,
  ClipboardData
} from '../models/builder-component.interface';

@Injectable({
  providedIn: 'root'
})
export class ComponentStoreService {
  // Core state management
  private componentsMap = new Map<string, BuilderComponent>();
  public componentsSubject = new BehaviorSubject<Map<string, BuilderComponent>>(new Map());
  private selectedComponentId = new BehaviorSubject<string | null>(null);
  private activePage = new BehaviorSubject<PageConfig | null>(null);
  
  // Canvas state management
  private canvasState = new BehaviorSubject<CanvasState>(this.getDefaultCanvasState());
  
  // Selection state management
  private selectionState = new BehaviorSubject<SelectionState>({
    selectedIds: [],
    hoveredId: null,
    multiSelectMode: false,
    selectionRect: undefined
  });

  // Zoom levels configuration
  private zoomLevels: ZoomLevel[] = [
    { label: '25%', value: 0.25 },
    { label: '50%', value: 0.5 },
    { label: '75%', value: 0.75 },
    { label: '100%', value: 1 },
    { label: '125%', value: 1.25 },
    { label: '150%', value: 1.5 },
    { label: '200%', value: 2 },
    { label: '300%', value: 3 }
  ];

  // Subscribers for change notifications
  private subscribers = new Set<(action: string) => void>();

  constructor() {
    console.log('[ComponentStoreService] Initialized');
  }

  // Core Component Management Methods

  /**
   * Get all components as observable
   */
  getComponents(): Observable<Map<string, BuilderComponent>> {
    return this.componentsSubject.asObservable();
  }

  /**
   * Get all components as observable (legacy method name)
   */
  getAllComponents(): Observable<Map<string, BuilderComponent>> {
    return this.getComponents();
  }

  /**
   * Get component by ID synchronously
   */
  getComponentSync(id: string): BuilderComponent | undefined {
    return this.componentsMap.get(id);
  }

  /**
   * Get component by ID as observable
   */
  getComponent(id: string): Observable<BuilderComponent | undefined> {
    return this.componentsSubject.pipe(
      map(components => components.get(id))
    );
  }



  /**
   * Update component properties
   */
  updateComponentProperties(componentId: string, properties: Partial<BuilderComponent['properties']>): void {
    const component = this.componentsMap.get(componentId);
    if (component) {
      const updatedComponent = {
        ...component,
        properties: {
          ...component.properties,
          ...properties
        }
      };
      this.componentsMap.set(componentId, updatedComponent);
      this.componentsSubject.next(new Map(this.componentsMap));
      this.notifySubscribers('updateComponentProperties');
      console.log(`[ComponentStoreService] Updated properties for component: ${componentId}`);
    }
  }

  /**
   * Remove a component
   */
  removeComponent(componentId: string): void {
    const component = this.componentsMap.get(componentId);
    if (component) {
      // Remove from parent's children array if it has a parent
      if (component.parentId) {
        const parent = this.componentsMap.get(component.parentId);
        if (parent) {
          parent.children = parent.children.filter(childId => childId !== componentId);
          this.componentsMap.set(component.parentId, { ...parent });
        }
      }

      // Remove from components map
      this.componentsMap.delete(componentId);
      
      // Remove from selection if selected
      this.removeFromSelection(componentId);
      
      this.componentsSubject.next(new Map(this.componentsMap));
      this.notifySubscribers('removeComponent');
      console.log(`[ComponentStoreService] Removed component: ${componentId}`);
    }
  }

  /**
   * Get selected component as observable
   */
  getSelectedComponent(): Observable<BuilderComponent | undefined> {
    return this.selectedComponentId.pipe(
      map(id => id ? this.componentsMap.get(id) : undefined)
    );
  }

  /**
   * Set selected component
   */
  setSelectedComponent(componentId: string | null): void {
    this.selectedComponentId.next(componentId);
    
    // Update selection state for consistency
    if (componentId) {
      this.updateSelectionState({ selectedIds: [componentId] });
    } else {
      this.updateSelectionState({ selectedIds: [] });
    }
    
    this.notifySubscribers('setSelectedComponent');
    console.log(`[ComponentStoreService] Selected component: ${componentId}`);
  }

  /**
   * Get active page
   */
  getActivePage(): Observable<PageConfig | null> {
    return this.activePage.asObservable();
  }

  /**
   * Set active page
   */
  setActivePage(page: PageConfig | null): void {
    this.activePage.next(page);
    this.notifySubscribers('setActivePage');
    console.log(`[ComponentStoreService] Set active page: ${page?.id}`);
  }

  /**
   * Reset all state
   */
  resetState(): void {
    this.componentsMap.clear();
    this.componentsSubject.next(new Map());
    this.selectedComponentId.next(null);
    this.clearSelection();
    this.canvasState.next(this.getDefaultCanvasState());
    this.notifySubscribers('resetState');
    console.log('[ComponentStoreService] State reset');
  }

  /**
   * Clear all components (for template application)
   */
  clearAllComponents(): void {
    this.componentsMap.clear();
    this.componentsSubject.next(new Map());
    this.clearSelection();
    this.notifySubscribers('clearAllComponents');
    console.log('[ComponentStoreService] Cleared all components');
  }

  /**
   * Get default canvas state
   */
  private getDefaultCanvasState(): CanvasState {
    return {
      zoom: 1,
      gridEnabled: true,
      gridConfig: {
        enabled: true,
        size: 20,
        color: '#e5e7eb',
        opacity: 0.5,
        snapToGrid: false
      },
      selectedComponents: [],
      viewportOffset: { x: 0, y: 0 },
      lastModified: new Date()
    };
  }

  /**
   * Subscribe to store changes
   */
  subscribe(callback: (action: string) => void): () => void {
    this.subscribers.add(callback);
    return () => this.subscribers.delete(callback);
  }

  /**
   * Notify all subscribers of changes
   */
  private notifySubscribers(action: string): void {
    this.subscribers.forEach(callback => callback(action));
  }

  /**
   * Load components from a page config
   */
  loadFromPage(page: PageConfig, components?: Record<string, BuilderComponent>): void {
    console.log(`Loading components from page: ${page.id}`);

    // Reset current state
    this.resetState();

    // Set the active page
    this.activePage.next(page);

    // If components record provided, load it directly
    if (components && Object.keys(components).length > 0) {
      console.log(`Loading ${Object.keys(components).length} components from provided map`);
      Object.values(components).forEach(component => {
        this.componentsMap.set(component.id, {...component});
      });

      this.componentsSubject.next(new Map(this.componentsMap));
      return;
    }

    // Check if components is an array
    if (!Array.isArray(page.components)) {
      console.warn('Page components is not an array');
      return;
    }

    // Notify subscribers
    this.componentsSubject.next(new Map(this.componentsMap));
    console.log(`Loaded ${this.componentsMap.size} components from page`);
  }

  // Canvas State Management Methods

  /**
   * Get canvas state as observable
   */
  getCanvasState(): Observable<CanvasState> {
    return this.canvasState.asObservable();
  }

  /**
   * Get current canvas state value
   */
  getCurrentCanvasState(): CanvasState {
    return this.canvasState.value;
  }

  /**
   * Update canvas state
   */
  updateCanvasState(updates: Partial<CanvasState>): void {
    const currentState = this.canvasState.value;
    const newState = {
      ...currentState,
      ...updates,
      lastModified: new Date()
    };
    this.canvasState.next(newState);
    this.notifySubscribers('updateCanvasState');
  }

  /**
   * Set zoom level
   */
  setZoom(zoom: number): void {
    const clampedZoom = Math.max(0.25, Math.min(3, zoom));
    this.updateCanvasState({ zoom: clampedZoom });
  }

  /**
   * Zoom in
   */
  zoomIn(): void {
    const currentZoom = this.canvasState.value.zoom;
    const nextZoomIndex = this.zoomLevels.findIndex(level => level.value > currentZoom);
    if (nextZoomIndex !== -1) {
      this.setZoom(this.zoomLevels[nextZoomIndex].value);
    }
  }

  /**
   * Zoom out
   */
  zoomOut(): void {
    const currentZoom = this.canvasState.value.zoom;
    const prevZoomIndex = this.zoomLevels.slice().reverse().findIndex(level => level.value < currentZoom);
    if (prevZoomIndex !== -1) {
      const actualIndex = this.zoomLevels.length - 1 - prevZoomIndex;
      this.setZoom(this.zoomLevels[actualIndex].value);
    }
  }

  /**
   * Set zoom to actual size (100%)
   */
  actualSize(): void {
    this.setZoom(1);
  }

  /**
   * Get available zoom levels
   */
  getZoomLevels(): ZoomLevel[] {
    return [...this.zoomLevels];
  }

  /**
   * Toggle grid
   */
  toggleGrid(): void {
    const currentState = this.canvasState.value;
    this.updateCanvasState({
      gridEnabled: !currentState.gridEnabled,
      gridConfig: {
        ...currentState.gridConfig,
        enabled: !currentState.gridEnabled
      }
    });
  }

  /**
   * Update grid configuration
   */
  updateGridConfig(config: Partial<GridConfig>): void {
    const currentState = this.canvasState.value;
    this.updateCanvasState({
      gridConfig: {
        ...currentState.gridConfig,
        ...config
      }
    });
  }

  // Selection State Management Methods

  /**
   * Get selection state as observable
   */
  getSelectionState(): Observable<SelectionState> {
    return this.selectionState.asObservable();
  }

  /**
   * Update selection state
   */
  updateSelectionState(updates: Partial<SelectionState>): void {
    const currentState = this.selectionState.value;
    const newState = { ...currentState, ...updates };
    this.selectionState.next(newState);
    this.notifySubscribers('updateSelectionState');
  }

  /**
   * Set hovered component
   */
  setHoveredComponent(componentId: string | null): void {
    this.updateSelectionState({ hoveredId: componentId });
  }

  /**
   * Toggle multi-select mode
   */
  toggleMultiSelectMode(): void {
    const currentState = this.selectionState.value;
    this.updateSelectionState({ multiSelectMode: !currentState.multiSelectMode });
  }

  /**
   * Add component to selection
   */
  addToSelection(componentId: string): void {
    const currentState = this.selectionState.value;
    if (!currentState.selectedIds.includes(componentId)) {
      this.updateSelectionState({
        selectedIds: [...currentState.selectedIds, componentId]
      });
    }
  }

  /**
   * Remove component from selection
   */
  removeFromSelection(componentId: string): void {
    const currentState = this.selectionState.value;
    this.updateSelectionState({
      selectedIds: currentState.selectedIds.filter(id => id !== componentId)
    });
  }

  /**
   * Clear selection
   */
  clearSelection(): void {
    this.updateSelectionState({
      selectedIds: [],
      selectionRect: undefined
    });
  }

  /**
   * Check if component is selected
   */
  isComponentSelected(componentId: string): boolean {
    return this.selectionState.value.selectedIds.includes(componentId);
  }

  /**
   * Select multiple components
   */
  selectMultipleComponents(componentIds: string[]): void {
    this.updateSelectionState({
      selectedIds: componentIds,
      multiSelectMode: componentIds.length > 1
    });
    // Also update the legacy single selection for compatibility
    if (componentIds.length === 1) {
      this.selectedComponentId.next(componentIds[0]);
    } else {
      this.selectedComponentId.next(null);
    }
  }

  /**
   * Get currently selected components
   */
  getSelectedComponents(): Observable<BuilderComponent[]> {
    return this.selectionState.pipe(
      map(state =>
        state.selectedIds
          .map(id => this.componentsMap.get(id))
          .filter(Boolean) as BuilderComponent[]
      )
    );
  }

  /**
   * Get selected component IDs
   */
  getSelectedComponentIds(): string[] {
    return this.selectionState.value.selectedIds;
  }

  /**
   * Get selected component ID (legacy method for compatibility)
   */
  getSelectedComponentId(): Observable<string | null> {
    return this.selectedComponentId.asObservable();
  }

  /**
   * Select component (legacy method for compatibility)
   */
  selectComponent(componentId: string | null): void {
    this.setSelectedComponent(componentId);
  }

  /**
   * Toggle component selection with Ctrl+Click behavior
   */
  toggleComponentSelection(componentId: string, multiSelect: boolean = false): void {
    const currentState = this.selectionState.value;

    if (!multiSelect) {
      // Single selection mode - replace current selection
      this.selectMultipleComponents([componentId]);
      return;
    }

    // Multi-select mode
    const isSelected = currentState.selectedIds.includes(componentId);

    if (isSelected) {
      // Remove from selection
      const newSelection = currentState.selectedIds.filter(id => id !== componentId);
      this.selectMultipleComponents(newSelection);
    } else {
      // Add to selection
      const newSelection = [...currentState.selectedIds, componentId];
      this.selectMultipleComponents(newSelection);
    }
  }

  /**
   * Select all components
   */
  selectAllComponents(): void {
    const allIds = Array.from(this.componentsMap.keys());
    this.selectMultipleComponents(allIds);
  }

  /**
   * Delete selected components
   */
  deleteSelectedComponents(): void {
    const selectedIds = this.selectionState.value.selectedIds;

    selectedIds.forEach(id => {
      this.removeComponent(id);
    });

    this.clearSelection();
  }

  /**
   * Get root components (components without parents)
   */
  getRootComponents(): Observable<BuilderComponent[]> {
    return this.componentsSubject.pipe(
      map(components => {
        const rootComponents: BuilderComponent[] = [];
        const activePage = this.activePage.value;

        if (activePage && Array.isArray(activePage.components)) {
          // Get components in the order specified by the page
          activePage.components.forEach(componentId => {
            const component = components.get(componentId);
            if (component) {
              rootComponents.push(component);
            }
          });
        } else {
          // Fallback: get all components without parents
          components.forEach(component => {
            if (!component.parentId) {
              rootComponents.push(component);
            }
          });
        }

        return rootComponents;
      })
    );
  }

  /**
   * Add component with optional parent (Observable version)
   */
  addComponent(componentData: Partial<BuilderComponent>, parentId?: string): Observable<string> {
    return new Observable(observer => {
      try {
        const componentId = this.addComponentSync(componentData, parentId);
        observer.next(componentId);
        observer.complete();
      } catch (error) {
        observer.error(error);
      }
    });
  }

  /**
   * Add component with optional parent (synchronous version)
   */
  addComponentSync(componentData: Partial<BuilderComponent>, parentId?: string): string {
    // Create a complete BuilderComponent from partial data
    const component: BuilderComponent = {
      id: componentData.id || uuidv4(),
      type: componentData.type || 'text',
      parentId: parentId || null,
      properties: componentData.properties || { tailwindClasses: '' },
      children: componentData.children || [],
      isContainer: componentData.isContainer || false,
      label: componentData.label,
      position: componentData.position,
      size: componentData.size,
      styles: componentData.styles
    };

    // Set parent if provided
    if (parentId) {
      component.parentId = parentId;

      // Add to parent's children array
      const parent = this.componentsMap.get(parentId);
      if (parent) {
        if (!parent.children) {
          parent.children = [];
        }
        parent.children.push(component.id);
        this.componentsMap.set(parentId, { ...parent });
      }
    } else {
      // Add to active page's root components
      const activePage = this.activePage.value;
      if (activePage) {
        if (!activePage.components) {
          activePage.components = [];
        }
        activePage.components.push(component.id);
        this.activePage.next({ ...activePage });
      }
    }

    // Add component to map
    this.componentsMap.set(component.id, component);
    this.componentsSubject.next(new Map(this.componentsMap));
    this.notifySubscribers('addComponent');

    console.log(`[ComponentStoreService] Added component: ${component.id}`);
    return component.id;
  }

  /**
   * Move component between containers or to root
   */
  moveComponent(componentId: string, sourceContainerId: string | null, targetContainerId: string | null, targetIndex: number): void {
    const component = this.componentsMap.get(componentId);
    if (!component) return;

    // Remove from source
    if (sourceContainerId) {
      const sourceContainer = this.componentsMap.get(sourceContainerId);
      if (sourceContainer && sourceContainer.children) {
        sourceContainer.children = sourceContainer.children.filter(id => id !== componentId);
        this.componentsMap.set(sourceContainerId, { ...sourceContainer });
      }
    } else {
      // Remove from root components
      const activePage = this.activePage.value;
      if (activePage && activePage.components) {
        activePage.components = activePage.components.filter(id => id !== componentId);
        this.activePage.next({ ...activePage });
      }
    }

    // Add to target
    if (targetContainerId) {
      const targetContainer = this.componentsMap.get(targetContainerId);
      if (targetContainer) {
        if (!targetContainer.children) {
          targetContainer.children = [];
        }
        targetContainer.children.splice(targetIndex, 0, componentId);
        this.componentsMap.set(targetContainerId, { ...targetContainer });
      }
      component.parentId = targetContainerId;
    } else {
      // Add to root components
      const activePage = this.activePage.value;
      if (activePage) {
        if (!activePage.components) {
          activePage.components = [];
        }
        activePage.components.splice(targetIndex, 0, componentId);
        this.activePage.next({ ...activePage });
      }
      component.parentId = null;
    }

    this.componentsMap.set(componentId, { ...component });
    this.componentsSubject.next(new Map(this.componentsMap));
    this.notifySubscribers('moveComponent');
  }

  /**
   * Reorder children in a container
   */
  reorderChildren(containerId: string, newChildrenOrder: string[]): void {
    const container = this.componentsMap.get(containerId);
    if (container) {
      container.children = [...newChildrenOrder];
      this.componentsMap.set(containerId, { ...container });
      this.componentsSubject.next(new Map(this.componentsMap));
      this.notifySubscribers('reorderChildren');
    }
  }

  /**
   * Reorder root components
   */
  reorderRootComponents(previousIndex: number, currentIndex: number): void {
    const page = this.activePage.value;
    if (!page || !Array.isArray(page.components)) {
      console.warn('Cannot reorder: no active page or invalid components array');
      return;
    }

    // Create new components array with reordered items
    const newComponents = [...page.components];
    const [moved] = newComponents.splice(previousIndex, 1);
    newComponents.splice(currentIndex, 0, moved);

    // Update the page
    page.components = newComponents;
    this.activePage.next({ ...page });
    this.notifySubscribers('reorderRootComponents');
  }

  /**
   * Copy components to clipboard
   */
  copyComponents(componentIds: string[]): ClipboardData {
    const components = componentIds
      .map(id => this.componentsMap.get(id))
      .filter(Boolean) as BuilderComponent[];

    const relativePositions: ComponentPosition[] = components.map(component => ({
      id: component.id,
      x: component.position?.x || 0,
      y: component.position?.y || 0
    }));

    const currentPage = this.activePage.value;

    const clipboardData: ClipboardData = {
      components: components.map(c => ({ ...c })), // Deep copy
      timestamp: new Date().toISOString(),
      sourcePageId: currentPage?.id || '',
      relativePositions
    };

    // Store in clipboard service if available
    console.log('Copied components to clipboard:', clipboardData);
    return clipboardData;
  }

  /**
   * Paste components from clipboard
   */
  pasteComponents(clipboardData: ClipboardData, targetPageId?: string, offsetX: number = 20, offsetY: number = 20): Observable<string[]> {
    return new Observable(observer => {
      try {
        const newComponentIds: string[] = [];
        const currentPage = this.activePage.value;

        if (!currentPage) {
          observer.error(new Error('No active page'));
          return;
        }

        clipboardData.components.forEach((component, index) => {
          // Generate new ID for pasted component
          const newId = uuidv4();

          // Calculate new position with offset
          const originalPosition = clipboardData.relativePositions.find(pos => pos.id === component.id);
          const newPosition: ComponentPosition = {
            id: newId,
            x: (originalPosition?.x || 0) + offsetX,
            y: (originalPosition?.y || 0) + offsetY
          };

          // Create new component with updated properties
          const newComponent: BuilderComponent = {
            ...component,
            id: newId,
            parentId: null, // Reset parent for paste operation
            position: newPosition,
            label: `${component.label || component.type} (Copy)`
          };

          // Add to component store
          this.componentsMap.set(newId, newComponent);
          newComponentIds.push(newId);

          // Add to current page
          if (currentPage.components) {
            currentPage.components = [...currentPage.components, newId];
          } else {
            currentPage.components = [newId];
          }
        });

        // Update active page
        this.activePage.next({ ...currentPage });
        this.notifySubscribers('pasteComponents');

        observer.next(newComponentIds);
        observer.complete();
      } catch (error) {
        observer.error(error);
      }
    });
  }

  /**
   * Align components
   */
  alignComponents(componentIds: string[], alignment: AlignmentType): Observable<void> {
    return new Observable(observer => {
      try {
        if (componentIds.length < 2) {
          observer.error('At least 2 components required for alignment');
          return;
        }

        const components = componentIds
          .map(id => this.componentsMap.get(id))
          .filter(Boolean) as BuilderComponent[];

        let referenceValue: number;

        switch (alignment) {
          case AlignmentType.LEFT:
            referenceValue = Math.min(...components.map(c => c.position?.x || 0));
            components.forEach(component => {
              const currentPosition = component.position || { id: component.id, x: 0, y: 0 };
              component.position = { id: component.id, x: referenceValue, y: currentPosition.y };
              this.componentsMap.set(component.id, { ...component });
            });
            break;

          case AlignmentType.RIGHT:
            referenceValue = Math.max(...components.map(c => c.position?.x || 0));
            components.forEach(component => {
              const currentPosition = component.position || { id: component.id, x: 0, y: 0 };
              component.position = { id: component.id, x: referenceValue, y: currentPosition.y };
              this.componentsMap.set(component.id, { ...component });
            });
            break;

          case AlignmentType.CENTER_HORIZONTAL:
            const avgX = components.reduce((sum, c) => sum + (c.position?.x || 0), 0) / components.length;
            components.forEach(component => {
              const currentPosition = component.position || { id: component.id, x: 0, y: 0 };
              component.position = { id: component.id, x: avgX, y: currentPosition.y };
              this.componentsMap.set(component.id, { ...component });
            });
            break;

          case AlignmentType.TOP:
            referenceValue = Math.min(...components.map(c => c.position?.y || 0));
            components.forEach(component => {
              const currentPosition = component.position || { id: component.id, x: 0, y: 0 };
              component.position = { id: component.id, x: currentPosition.x, y: referenceValue };
              this.componentsMap.set(component.id, { ...component });
            });
            break;

          case AlignmentType.BOTTOM:
            referenceValue = Math.max(...components.map(c => c.position?.y || 0));
            components.forEach(component => {
              const currentPosition = component.position || { id: component.id, x: 0, y: 0 };
              component.position = { id: component.id, x: currentPosition.x, y: referenceValue };
              this.componentsMap.set(component.id, { ...component });
            });
            break;

          case AlignmentType.CENTER_VERTICAL:
            const avgY = components.reduce((sum, c) => sum + (c.position?.y || 0), 0) / components.length;
            components.forEach(component => {
              const currentPosition = component.position || { id: component.id, x: 0, y: 0 };
              component.position = { id: component.id, x: currentPosition.x, y: avgY };
              this.componentsMap.set(component.id, { ...component });
            });
            break;
        }

        this.notifySubscribers('alignComponents');
        observer.next();
        observer.complete();
      } catch (error) {
        observer.error(error);
      }
    });
  }

  /**
   * Distribute components evenly
   */
  distributeComponents(componentIds: string[], distribution: DistributionType): Observable<void> {
    return new Observable(observer => {
      try {
        if (componentIds.length < 3) {
          observer.complete();
          return;
        }

        const components = componentIds
          .map(id => this.componentsMap.get(id))
          .filter(Boolean) as BuilderComponent[];

        if (components.length < 3) {
          observer.complete();
          return;
        }

        // Sort components by position
        if (distribution === DistributionType.HORIZONTAL) {
          components.sort((a, b) => (a.position?.x || 0) - (b.position?.x || 0));
          const minX = components[0].position?.x || 0;
          const maxX = components[components.length - 1].position?.x || 0;
          const spacing = (maxX - minX) / (components.length - 1);

          components.forEach((component, index) => {
            const newX = minX + (spacing * index);
            const currentPosition = component.position || { id: component.id, x: 0, y: 0 };
            component.position = { id: component.id, x: newX, y: currentPosition.y };
            this.componentsMap.set(component.id, { ...component });
          });
        } else {
          components.sort((a, b) => (a.position?.y || 0) - (b.position?.y || 0));
          const minY = components[0].position?.y || 0;
          const maxY = components[components.length - 1].position?.y || 0;
          const spacing = (maxY - minY) / (components.length - 1);

          components.forEach((component, index) => {
            const newY = minY + (spacing * index);
            const currentPosition = component.position || { id: component.id, x: 0, y: 0 };
            component.position = { id: component.id, x: currentPosition.x, y: newY };
            this.componentsMap.set(component.id, { ...component });
          });
        }

        this.notifySubscribers('distributeComponents');
        observer.next();
        observer.complete();
      } catch (error) {
        observer.error(error);
      }
    });
  }

  /**
   * Bulk update properties for multiple components
   */
  bulkUpdateProperties(componentIds: string[], properties: Partial<BuilderComponent['properties']>): Observable<void> {
    return new Observable(observer => {
      try {
        const updatedComponents: BuilderComponent[] = [];

        componentIds.forEach(componentId => {
          const component = this.componentsMap.get(componentId);
          if (component) {
            const updatedComponent = {
              ...component,
              properties: {
                ...component.properties,
                ...properties
              }
            };
            this.componentsMap.set(componentId, updatedComponent);
            updatedComponents.push(updatedComponent);
          }
        });

        if (updatedComponents.length > 0) {
          this.componentsSubject.next(new Map(this.componentsMap));
          this.notifySubscribers('bulkUpdateProperties');
          console.log(`[ComponentStoreService] Bulk updated properties for ${updatedComponents.length} components`);
        }

        observer.next();
        observer.complete();
      } catch (error) {
        observer.error(error);
      }
    });
  }

  /**
   * Resize components to match dimensions
   */
  resizeComponents(componentIds: string[], resizeType: 'width' | 'height' | 'both'): Observable<void> {
    return new Observable(observer => {
      try {
        if (componentIds.length < 2) {
          observer.error('At least 2 components required for resize');
          return;
        }

        const components = componentIds
          .map(id => this.componentsMap.get(id))
          .filter(Boolean) as BuilderComponent[];

        if (components.length < 2) {
          observer.complete();
          return;
        }

        // Use the first component as reference for dimensions
        const referenceComponent = components[0];
        const referenceSize = referenceComponent.size || { width: 100, height: 50 };

        components.slice(1).forEach(component => {
          const currentSize = component.size || { width: 100, height: 50 };

          let newSize = { ...currentSize };

          if (resizeType === 'width' || resizeType === 'both') {
            newSize.width = referenceSize.width;
          }

          if (resizeType === 'height' || resizeType === 'both') {
            newSize.height = referenceSize.height;
          }

          component.size = newSize;
          this.componentsMap.set(component.id, { ...component });
        });

        this.componentsSubject.next(new Map(this.componentsMap));
        this.notifySubscribers('resizeComponents');
        observer.next();
        observer.complete();
      } catch (error) {
        observer.error(error);
      }
    });
  }

  /**
   * Export current state (legacy method)
   */
  exportState(): Record<string, BuilderComponent> {
    const state: Record<string, BuilderComponent> = {};
    this.componentsMap.forEach((component, id) => {
      state[id] = component;
    });
    return state;
  }

  /**
   * Get root component IDs (legacy method)
   */
  getRootComponentIds(): string[] {
    const activePage = this.activePage.value;
    return activePage?.components || [];
  }

  /**
   * Get components for a specific page (legacy method)
   */
  getComponentsForPage(pageId: string): Record<string, BuilderComponent> {
    return this.exportState();
  }

  /**
   * Get all container IDs synchronously (for drag and drop)
   */
  getAllContainerIdsSync(): string[] {
    const containerIds: string[] = [];
    this.componentsMap.forEach(component => {
      if (component.isContainer) {
        containerIds.push(`container-${component.id}`);
      }
    });
    return containerIds;
  }

  /**
   * Refresh container children (legacy method)
   */
  refreshContainerChildren(containerId: string): void {
    const container = this.componentsMap.get(containerId);
    if (!container) {
      console.warn('Attempted to refresh nonexistent container:', containerId);
      return;
    }

    // Create a new reference for the container to trigger change detection
    const updatedContainer = { ...container };
    this.componentsMap.set(containerId, updatedContainer);

    // Explicitly notify subscribers
    this.notifySubscribers('refreshContainerChildren');
    console.log(`Refreshed container ${containerId} with children:`, updatedContainer.children);
  }

}
