import { TestBed } from '@angular/core/testing';
import { ComponentStoreService } from './component-store.service';
import { NotificationService } from '../../shared/services/notification.service';
import { 
  MockAppConfigFactory, 
  MockNotificationService,
  TestDataLoader,
  PerformanceTestUtils
} from '../../../testing';
import { BuilderComponent } from '../models/builder-component.interface';

describe('ComponentStoreService', () => {
  let service: ComponentStoreService;
  let notificationService: jasmine.SpyObj<NotificationService>;

  beforeEach(async () => {
    const notificationSpyObj = jasmine.createSpyObj('NotificationService', ['show', 'error', 'success']);

    await TestBed.configureTestingModule({
      providers: [
        ComponentStoreService,
        { provide: NotificationService, useValue: notificationSpyObj }
      ]
    }).compileComponents();

    service = TestBed.inject(ComponentStoreService);
    notificationService = TestBed.inject(NotificationService) as jasmine.SpyObj<NotificationService>;
  });

  describe('Component Management', () => {
    it('should add a component', (done) => {
      const mockComponent = MockAppConfigFactory.createMockBuilderComponent();

      service.addComponent(mockComponent).subscribe(result => {
        expect(result).toEqual(mockComponent);
        done();
      });

      service.components$.subscribe(components => {
        expect(components[mockComponent.id]).toEqual(mockComponent);
      });
    });

    it('should update a component', (done) => {
      const mockComponent = MockAppConfigFactory.createMockBuilderComponent();
      const updates = { displayName: 'Updated Component' };

      // First add the component
      service.addComponent(mockComponent).subscribe(() => {
        // Then update it
        service.updateComponent(mockComponent.id, updates).subscribe(result => {
          expect(result.displayName).toBe('Updated Component');
          expect(result.id).toBe(mockComponent.id);
          done();
        });
      });
    });

    it('should remove a component', (done) => {
      const mockComponent = MockAppConfigFactory.createMockBuilderComponent();

      // Add component first
      service.addComponent(mockComponent).subscribe(() => {
        // Then remove it
        service.removeComponent(mockComponent.id).subscribe(result => {
          expect(result).toBe(true);
          
          // Verify it's removed from the store
          service.components$.subscribe(components => {
            expect(components[mockComponent.id]).toBeUndefined();
            done();
          });
        });
      });
    });

    it('should handle removing non-existent component', (done) => {
      service.removeComponent('non-existent-id').subscribe(result => {
        expect(result).toBe(false);
        expect(notificationService.error).toHaveBeenCalledWith(
          jasmine.stringContaining('Component not found')
        );
        done();
      });
    });

    it('should get component by ID', (done) => {
      const mockComponent = MockAppConfigFactory.createMockBuilderComponent();

      service.addComponent(mockComponent).subscribe(() => {
        service.getComponent(mockComponent.id).subscribe(result => {
          expect(result).toEqual(mockComponent);
          done();
        });
      });
    });

    it('should return null for non-existent component', (done) => {
      service.getComponent('non-existent-id').subscribe(result => {
        expect(result).toBeNull();
        done();
      });
    });
  });

  describe('Component Selection', () => {
    it('should select a component', (done) => {
      const mockComponent = MockAppConfigFactory.createMockBuilderComponent();

      service.addComponent(mockComponent).subscribe(() => {
        service.selectComponent(mockComponent.id).subscribe(result => {
          expect(result).toBe(mockComponent.id);
          done();
        });
      });

      service.selectedComponent$.subscribe(selectedId => {
        if (selectedId === mockComponent.id) {
          expect(selectedId).toBe(mockComponent.id);
        }
      });
    });

    it('should deselect component', (done) => {
      service.selectComponent(null).subscribe(result => {
        expect(result).toBe(null);
        done();
      });
    });

    it('should handle selecting non-existent component', (done) => {
      service.selectComponent('non-existent-id').subscribe(result => {
        expect(result).toBe(null);
        expect(notificationService.error).toHaveBeenCalledWith(
          jasmine.stringContaining('Component not found')
        );
        done();
      });
    });
  });

  describe('Component Hierarchy', () => {
    it('should build component hierarchy', (done) => {
      const config = TestDataLoader.createNestedConfiguration(3);
      
      // Load the hierarchical components
      Object.values(config.components).forEach(component => {
        service.addComponent(component).subscribe();
      });

      setTimeout(() => {
        service.getComponentHierarchy().subscribe(hierarchy => {
          expect(hierarchy.length).toBeGreaterThan(0);
          expect(hierarchy[0].children).toBeDefined();
          done();
        });
      }, 100);
    });

    it('should find component ancestors', (done) => {
      const config = TestDataLoader.createNestedConfiguration(4);
      const components = Object.values(config.components);
      
      // Load components
      components.forEach(component => {
        service.addComponent(component).subscribe();
      });

      setTimeout(() => {
        const leafComponent = components.find(c => c.children.length === 0);
        if (leafComponent) {
          service.getComponentAncestors(leafComponent.id).subscribe(ancestors => {
            expect(ancestors.length).toBeGreaterThan(0);
            expect(ancestors[0].id).toBe(leafComponent.parentId);
            done();
          });
        }
      }, 100);
    });

    it('should find component descendants', (done) => {
      const config = TestDataLoader.createNestedConfiguration(3);
      const components = Object.values(config.components);
      
      // Load components
      components.forEach(component => {
        service.addComponent(component).subscribe();
      });

      setTimeout(() => {
        const rootComponent = components.find(c => !c.parentId);
        if (rootComponent) {
          service.getComponentDescendants(rootComponent.id).subscribe(descendants => {
            expect(descendants.length).toBeGreaterThan(0);
            done();
          });
        }
      }, 100);
    });
  });

  describe('Component Operations', () => {
    it('should duplicate a component', (done) => {
      const mockComponent = MockAppConfigFactory.createMockBuilderComponent();

      service.addComponent(mockComponent).subscribe(() => {
        service.duplicateComponent(mockComponent.id).subscribe(duplicate => {
          expect(duplicate.id).not.toBe(mockComponent.id);
          expect(duplicate.displayName).toContain('(Copy)');
          expect(duplicate.type).toBe(mockComponent.type);
          done();
        });
      });
    });

    it('should move component to new parent', (done) => {
      const parent1 = MockAppConfigFactory.createMockBuilderComponent({ type: 'container' });
      const parent2 = MockAppConfigFactory.createMockBuilderComponent({ type: 'container' });
      const child = MockAppConfigFactory.createMockBuilderComponent({ parentId: parent1.id });

      // Add all components
      service.addComponent(parent1).subscribe(() => {
        service.addComponent(parent2).subscribe(() => {
          service.addComponent(child).subscribe(() => {
            
            // Move child from parent1 to parent2
            service.moveComponent(child.id, parent2.id).subscribe(result => {
              expect(result.parentId).toBe(parent2.id);
              
              // Verify parent1 no longer has the child
              service.getComponent(parent1.id).subscribe(p1 => {
                expect(p1?.children).not.toContain(child.id);
                
                // Verify parent2 now has the child
                service.getComponent(parent2.id).subscribe(p2 => {
                  expect(p2?.children).toContain(child.id);
                  done();
                });
              });
            });
          });
        });
      });
    });

    it('should validate component constraints', (done) => {
      const container = MockAppConfigFactory.createMockBuilderComponent({
        type: 'container',
        constraints: {
          allowedParents: ['*'],
          allowedChildren: ['text', 'button'],
          maxChildren: 2
        }
      });

      const textComponent = MockAppConfigFactory.createMockBuilderComponent({
        type: 'text',
        parentId: container.id
      });

      service.addComponent(container).subscribe(() => {
        service.addComponent(textComponent).subscribe(() => {
          service.validateComponentConstraints(textComponent.id).subscribe(validation => {
            expect(validation.isValid).toBe(true);
            expect(validation.errors).toEqual([]);
            done();
          });
        });
      });
    });

    it('should detect constraint violations', (done) => {
      const container = MockAppConfigFactory.createMockBuilderComponent({
        type: 'container',
        constraints: {
          allowedParents: ['*'],
          allowedChildren: ['text'],
          maxChildren: 1
        }
      });

      const invalidChild = MockAppConfigFactory.createMockBuilderComponent({
        type: 'image', // Not allowed in this container
        parentId: container.id
      });

      service.addComponent(container).subscribe(() => {
        service.addComponent(invalidChild).subscribe(() => {
          service.validateComponentConstraints(invalidChild.id).subscribe(validation => {
            expect(validation.isValid).toBe(false);
            expect(validation.errors.length).toBeGreaterThan(0);
            done();
          });
        });
      });
    });
  });

  describe('Multi-Component Operations', () => {
    it('should select multiple components', (done) => {
      const components = [
        MockAppConfigFactory.createMockBuilderComponent(),
        MockAppConfigFactory.createMockBuilderComponent(),
        MockAppConfigFactory.createMockBuilderComponent()
      ];

      // Add all components
      components.forEach(component => {
        service.addComponent(component).subscribe();
      });

      setTimeout(() => {
        const componentIds = components.map(c => c.id);
        service.selectMultipleComponents(componentIds).subscribe(selectedIds => {
          expect(selectedIds).toEqual(componentIds);
          done();
        });
      }, 100);
    });

    it('should perform bulk updates', (done) => {
      const components = [
        MockAppConfigFactory.createMockBuilderComponent(),
        MockAppConfigFactory.createMockBuilderComponent()
      ];

      // Add components
      components.forEach(component => {
        service.addComponent(component).subscribe();
      });

      setTimeout(() => {
        const updates = { properties: { color: '#ff0000' } };
        const componentIds = components.map(c => c.id);

        service.bulkUpdateComponents(componentIds, updates).subscribe(updatedComponents => {
          expect(updatedComponents.length).toBe(2);
          updatedComponents.forEach(component => {
            expect(component.properties.color).toBe('#ff0000');
          });
          done();
        });
      }, 100);
    });

    it('should delete multiple components', (done) => {
      const components = [
        MockAppConfigFactory.createMockBuilderComponent(),
        MockAppConfigFactory.createMockBuilderComponent()
      ];

      // Add components
      components.forEach(component => {
        service.addComponent(component).subscribe();
      });

      setTimeout(() => {
        const componentIds = components.map(c => c.id);
        service.deleteMultipleComponents(componentIds).subscribe(result => {
          expect(result).toBe(true);
          
          // Verify components are removed
          service.components$.subscribe(allComponents => {
            componentIds.forEach(id => {
              expect(allComponents[id]).toBeUndefined();
            });
            done();
          });
        });
      }, 100);
    });
  });

  describe('Performance Tests', () => {
    it('should handle adding many components efficiently', async () => {
      const componentCount = 1000;
      const { duration } = await PerformanceTestUtils.measureExecutionTime(async () => {
        const components = Array.from({ length: componentCount }, () => 
          MockAppConfigFactory.createMockBuilderComponent()
        );

        // Add all components
        for (const component of components) {
          await service.addComponent(component).toPromise();
        }
      }, 'Adding 1000 components');

      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should handle large hierarchy efficiently', async () => {
      const config = TestDataLoader.createNestedConfiguration(10);
      const components = Object.values(config.components);

      const { duration } = await PerformanceTestUtils.measureExecutionTime(async () => {
        // Add all components
        for (const component of components) {
          await service.addComponent(component).toPromise();
        }

        // Build hierarchy
        await service.getComponentHierarchy().toPromise();
      }, 'Building deep hierarchy');

      expect(duration).toBeLessThan(1000); // Should complete within 1 second
    });
  });
});