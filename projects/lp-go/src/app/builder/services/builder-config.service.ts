import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { map, catchError, tap, switchMap } from 'rxjs/operators';
import { AppConfig, PageConfig } from '../models/builder-component.interface';
import { BuilderComponent } from '../models/builder-component.interface';
import { BuilderApiService } from './api/builder-api.service';
import { ComponentStoreService } from './component-store.service';
import { ConfigurationPersistenceService, SaveResult } from './configuration-persistence.service';

/**
 * Service for managing builder configurations
 */
@Injectable({
  providedIn: 'root'
})
export class BuilderConfigService {
  // Current app configuration
  private currentAppConfig = new BehaviorSubject<AppConfig | null>(null);
  
  // Current page configuration
  private currentPage = new BehaviorSubject<PageConfig | null>(null);
  
  // Whether we're in a "dirty" state (unsaved changes)
  private isDirty = new BehaviorSubject<boolean>(false);
  
  // Loading state
  private isLoading = new BehaviorSubject<boolean>(false);
  
  // Config parameters
  private configParams = new BehaviorSubject<{client: string, env: string, version: string} | null>(null);

  constructor(
    private apiService: BuilderApiService,
    private componentStore: ComponentStoreService,
    private persistenceService: ConfigurationPersistenceService
  ) {}
  
  /**
   * Set configuration parameters
   */
  setConfigParams(params: {client: string, env: string, version: string}): void {
    this.configParams.next(params);
  }
  
  /**
   * Get configuration parameters
   */
  getConfigParams(): Observable<{client: string, env: string, version: string} | null> {
    return this.configParams.asObservable();
  }
  
  /**
   * Set stored app configuration directly
   */
  setStoredAppConfig(config: AppConfig): void {
    console.log('DEBUG: Setting stored app config:', config);
    
    try {
      this.currentAppConfig.next(config);
      
      // If the config has pages, set the first one as active
      if (config.pages) {
        console.log('DEBUG: Config pages type:', Array.isArray(config.pages) ? 'Array' : typeof config.pages);
        
        // Handle both array and object formats for pages
        if (Array.isArray(config.pages) && config.pages.length > 0) {
          console.log('DEBUG: Setting first page from array:', config.pages[0]);
          const firstPage = config.pages[0] as PageConfig; // Assert type
          console.log('DEBUG: Setting first page from array:', firstPage);
          this.currentPage.next(firstPage);
        } else if (typeof config.pages === 'object' && config.pages !== null) {
          // Double-check it's not an array to help TS type narrowing
          if (!Array.isArray(config.pages)) { 
            const firstPageKey = Object.keys(config.pages)[0];
            if (firstPageKey) {
              // Now TS should be confident config.pages is Record<string, PageConfig>
              const firstPage = config.pages[firstPageKey]; // Assertion might be removable now, check TS inference
              console.log('DEBUG: Setting first page from object:', firstPage);
              // Assuming firstPage is correctly inferred/asserted as PageConfig
              this.currentPage.next(firstPage as PageConfig); // Add assertion if needed
            } else {
               console.warn('DEBUG: No pages found in config object');
            }
          } else {
             // This case should logically not be hit due to the outer if/else structure
             console.error("DEBUG: Inconsistent state - config.pages is both an object and an array?");
          }
        } else {
          console.warn('DEBUG: Config has no pages property');
        }
      } else {
        console.warn('DEBUG: Config has no pages property');
      }
    } catch (error) {
      console.error('DEBUG: Error in setStoredAppConfig:', error);
    }
  }

  /**
   * Get current app configuration
   */
  getCurrentAppConfig(): Observable<AppConfig | null> {
    return this.currentAppConfig.asObservable();
  }

  /**
   * Get current page configuration - Enhanced Logging
   */
  getCurrentPage(): Observable<PageConfig | null> {
    console.log('DEBUG: getCurrentPage() called. Current value:', this.currentPage.getValue());
    return this.currentPage.asObservable();
  }

  /**
   * Get loading state
   */
  getIsLoading(): Observable<boolean> {
    return this.isLoading.asObservable();
  }

  /**
   * Get dirty state (unsaved changes)
   */
  getIsDirty(): Observable<boolean> {
    return this.isDirty.asObservable();
  }

  /**
   * Get available configurations (Enhanced with Real API)
   */
  getAvailableConfigs(): Observable<AppConfig[]> {
    this.isLoading.next(true);

    // Get config parameters to determine client
    const params = this.configParams.getValue();
    const client = params?.client || 'lp-client-test';

    console.log(`[BuilderConfig] Loading configurations for client: ${client}`);

    // Use enhanced API service with real environment integration
    return this.apiService.getAvailableConfigs(client).pipe(
      tap(configs => {
        console.log(`[BuilderConfig] Loaded ${configs.length} configurations`);
        this.isLoading.next(false);
      }),
      catchError(error => {
        console.warn('[BuilderConfig] Failed to load configurations, using fallback:', error);
        this.isLoading.next(false);

        // Fallback to mock data if real API fails
        return this.apiService.getMockConfigs();
      })
    );
  }

  /**
   * Load an app configuration - Enhanced Logging
   */
  loadAppConfig(configId: string): Observable<AppConfig> {
    this.isLoading.next(true);
    this.isDirty.next(false); // Reset dirty state

    console.log(`%cDEBUG: loadAppConfig started for configId: ${configId}`, 'color: blue; font-weight: bold;');

    const storageKey = `app_config_${configId}`;
    const storedConfig = localStorage.getItem(storageKey);

    console.log(`DEBUG: Checking localStorage for key: ${storageKey}. Found: ${!!storedConfig}`);

    if (storedConfig) {
      try {
        // Validate JSON parsing strictly
        let config: AppConfig = JSON.parse(storedConfig);

        console.log(`%cDEBUG: Successfully parsed config from localStorage (${storageKey}):`, 'color: green;', config);
        console.log(`%cDEBUG: Config structure - pages type: ${Array.isArray(config.pages) ? 'Array' : typeof config.pages}`, 'color: blue; font-weight: bold;');
        
        // Examine pages structure in detail
        if (Array.isArray(config.pages)) {
          console.log(`%cDEBUG: Pages array length: ${config.pages.length}`, 'color: blue;');
          if (config.pages.length > 0) {
            console.log(`%cDEBUG: First page structure:`, 'color: blue;', JSON.stringify(config.pages[0], null, 2));
            // Validate first page structure
            const firstPage = config.pages[0];
            console.log(`%cDEBUG: First page validation - id exists: ${!!firstPage.id}, components exists: ${!!firstPage.components}`, 'color: blue;');
          }
        }

        // Add adapter to convert config to expected format
        config = this.adaptConfigForBuilder(config, configId);

        // Basic validation of config structure (add more as needed)
        if (!config || typeof config !== 'object' || !config.id) {
           console.error(`DEBUG: Invalid config structure loaded from localStorage (${storageKey})`, config);
           throw new Error('Invalid config structure in localStorage');
        }

        console.log(`DEBUG: Setting currentAppConfig (BehaviorSubject) with parsed config.`);
        this.currentAppConfig.next(config);

        // Safely attempt to load the first page
        try {
          console.log(`DEBUG: Attempting to find first page to load.`);
          // Check if pages exists (can be array or object)
          if (config.pages) { 
            // Ensure pagesArray elements are treated as PageConfig
            const pagesArray: PageConfig[] = Array.isArray(config.pages) 
                ? config.pages 
                : typeof config.pages === 'object' ? Object.values(config.pages as Record<string, PageConfig>) : []; // Handle non-object/array case too

            if (pagesArray.length > 0 && pagesArray[0]?.id) { // Use optional chaining and check id
              const firstPageToLoad = pagesArray[0];

              console.log(`%cDEBUG: Found first page. Calling loadPageConfig for: ${firstPageToLoad.id}`, 'color: purple; font-weight: bold;');
              // Wrap the loadPageConfig call in a try/catch to catch synchronous errors
              try {
                // Call loadPageConfig with error handling
                this.loadPageConfig(config.id, firstPageToLoad.id)
                  .subscribe({
                    next: (pageConfig) => console.log(`%cDEBUG: Successfully loaded first page: ${firstPageToLoad.id}`, 'color: green;', pageConfig),
                    error: (err) => console.error(`%cDEBUG: Error in loadPageConfig subscription: ${firstPageToLoad.id}`, 'color: red;', err)
                  });
              } catch (directError) {
                console.error(`%cDEBUG: Direct error when calling loadPageConfig: ${firstPageToLoad.id}`, 'color: red;', directError);
              }
            } else {
               console.warn(`DEBUG: No valid first page found in config (${storageKey})`, config.pages);
            }
          } else {
             console.warn(`DEBUG: Config (${storageKey}) has no 'pages' property or it's null/undefined`, config);
          }
        } catch (pageLoadError) {
           console.error(`%cDEBUG: Error triggering loadPageConfig for first page (${storageKey}):`, 'color: red;', pageLoadError);
           console.error(`%cDEBUG: Error details:`, 'color: red;', pageLoadError instanceof Error ? pageLoadError.message : pageLoadError);
           console.error(`%cDEBUG: Error stack:`, 'color: red;', pageLoadError instanceof Error ? pageLoadError.stack : 'No stack available');
            // Decide how to handle this - maybe load a default state or show an error
        }

        console.log(`%cDEBUG: loadAppConfig successful from localStorage for ${configId}. Setting isLoading=false.`, 'color: green; font-weight: bold;');
        this.isLoading.next(false);
        return of(config);

      } catch (error: any) { // Catch specific error type if possible
        console.error(`%cDEBUG: Error processing stored config (${storageKey}):`, 'color: red; font-weight: bold;', error);

        // Log potential custom error codes
        if (error && typeof error === 'object' && 'code' in error) {
          console.error(`%cDEBUG: Detected error code: ${error.code}`, 'color: red; font-weight: bold;');
        }

        // Remove corrupted data from localStorage to prevent repeated errors
        localStorage.removeItem(storageKey);
        // Explicitly return an error observable or fall through consciously
        // Option 1: Return error
        // return throwError(() => new Error(`Failed to parse/process stored config: ${error.message}`));
        // Option 2: Fall through to fallback logic (as it was before, but now aware of the error)
        console.warn(`DEBUG: Falling back to mock/API due to error with stored config (${storageKey})`);
      }
    } else {
       console.log(`DEBUG: No config found in localStorage for key: ${storageKey}. Falling back.`);
    }

    // Fallback logic (API/mock)
    console.warn(`DEBUG: Falling back to mock/API for configId: ${configId}. Calling getAvailableConfigs...`);
    return this.getAvailableConfigs().pipe(
      map(configs => {
        console.log(`DEBUG: getAvailableConfigs returned ${configs?.length ?? 0} configs.`);
        const foundConfig = configs.find(c => c.id === configId);
        // Use found config or create a new mock one if not found
        const configToUse = foundConfig ? foundConfig : this.createMockConfig(configId);
        console.log(`DEBUG: Using ${foundConfig ? 'found' : 'new mock'} config from fallback logic:`, configToUse);
        return configToUse;
      }),
      tap(config => {
        console.log(`DEBUG: Setting currentAppConfig (BehaviorSubject) with fallback config.`);
        this.currentAppConfig.next(config);

        // Set the first page as active
        console.log(`DEBUG: Attempting to find and set first page from fallback config.`);
        if (config.pages && config.pages.length > 0) {
          console.log(`%cDEBUG: Found first page in fallback. Calling loadPageConfig for: ${config.pages[0]?.id}`, 'color: purple; font-weight: bold;');
          // Call loadPageConfig for the first page of the fallback config
          this.loadPageConfig(config.id, config.pages[0].id); 
        } else {
          console.warn(`DEBUG: No pages found in fallback config. Resetting component store.`);
          this.componentStore.resetState();
        }

        console.log(`%cDEBUG: loadAppConfig successful from fallback for ${configId}. Setting isLoading=false.`, 'color: green; font-weight: bold;');
        this.isLoading.next(false);
        this.isDirty.next(false);
      }),
      catchError(error => {
        console.error(`%cDEBUG: Error during fallback logic in loadAppConfig for ${configId}:`, 'color: red; font-weight: bold;', error);

        // Log potential custom error codes
        if (error && typeof error === 'object' && 'code' in error) {
          console.error(`%cDEBUG: Detected error code: ${error.code}`, 'color: red; font-weight: bold;');
        }

        this.isLoading.next(false);
        return throwError(() => error);
      })
    );
  }

  /**
   * Create a mock config when the real one is not found
   */
  private createMockConfig(configId: string, minimal: boolean = false): AppConfig {
    const configParts = configId.split('_');
    const client = configParts[0] || 'default';
    const env = configParts[1] || 'DEV';
    const version = configParts[2] || '1';
    
    console.log(`Creating mock config for ${configId}`);
    
    const now = new Date().toISOString();
    const mockConfig: AppConfig = {
      id: configId,
      name: `${client} Configuration`,
      version: version,
      createdAt: now,
      updatedAt: now,
      owner: 'default_user',
      pages: minimal ? [] : [
        {
          id: 'home_' + Date.now(),
          name: 'Home Page',
          components: [],
          type: 'builder',
          layout: 'fluid',
          styles: {
            backgroundColor: 'bg-white'
          }
        }
      ],
      settings: {
        theme: 'default'
      }
    };
    
    // Save to localStorage
    localStorage.setItem(`app_config_${configId}`, JSON.stringify(mockConfig));
    
    return mockConfig;
  }

  /**
   * Load a page configuration - Enhanced Logging
   */
  loadPageConfig(configId: string, pageId: string): Observable<PageConfig> {
    console.log(`%cDEBUG: loadPageConfig started for configId=${configId}, pageId=${pageId}`, 'color: blue; font-weight: bold;');
    this.isLoading.next(true);
    
    // Reset component store
    console.log(`DEBUG: Resetting component store before loading page: ${pageId}`);
    this.componentStore.resetState();
    
    // Find the page in the current config
    console.log(`DEBUG: Getting currentAppConfig value to find page: ${pageId}`);
    const config = this.currentAppConfig.getValue();
    if (!config) {
      console.error('%cDEBUG: No configuration loaded in currentAppConfig!', 'color: red; font-weight: bold;');
      this.isLoading.next(false);
      return throwError(() => new Error('No configuration loaded'));
    }
    
    console.log('DEBUG: Current App Config:', JSON.stringify(config, null, 2));
    console.log('DEBUG: Pages data type:', Array.isArray(config.pages) ? 'Array' : typeof config.pages);
    
    // Handle different page formats (array vs object)
    let page: PageConfig | undefined;
    
    try {
      if (Array.isArray(config.pages)) {
        console.log('DEBUG: Pages is an array, finding page by ID...');
        page = config.pages.find((p: PageConfig) => {
          console.log(`DEBUG: Comparing page id ${p.id} with target ${pageId}`);
          return p.id === pageId;
        });
      } else if (typeof config.pages === 'object' && config.pages !== null) {
        console.log('DEBUG: Pages is an object, getting page by key...');
        // Handle object format where pages are keyed by ID
        const pagesObj = config.pages as unknown as Record<string, PageConfig>;
        page = pagesObj[pageId];
      }
      
      if (!page) {
        console.error(`%cDEBUG: Page with ID ${pageId} not found in config ${configId}!`, 'color: red; font-weight: bold;');
        this.isLoading.next(false);
        return throwError(() => new Error(`Page with ID ${pageId} not found`));
      }
      
      // Validate page object has required properties
      if (!page.id || !page.components) {
        console.error('%cDEBUG: Invalid page structure - missing required properties!', 'color: red; font-weight: bold;', page);
        this.isLoading.next(false);
        return throwError(() => new Error('Invalid page structure - missing required properties'));
      }
      
      console.log('%cDEBUG: Found page config:', 'color: green;', JSON.stringify(page, null, 2));
      
      console.log(`Loading page ${page.id} with ${page.components?.length ?? 0} root components from config.`);
      try {
        this.componentStore.loadFromPage(page);
      } catch (loadError) {
        console.error('%cDEBUG: Error in componentStore.loadFromPage:', 'color: red; font-weight: bold;', loadError);
        this.isLoading.next(false);
        return throwError(() => new Error(`Error loading page components: ${loadError instanceof Error ? loadError.message : String(loadError)}`));
      }
      
      console.log(`DEBUG: Setting currentPage (BehaviorSubject) to page: ${page.id}`);
      this.currentPage.next(page);
      
      console.log(`%cDEBUG: loadPageConfig successful for page ${pageId}. Setting isLoading=false.`, 'color: green; font-weight: bold;');
      this.isLoading.next(false);
      return of(page);
    } catch (error) {
      console.error('%cDEBUG: Unexpected error in loadPageConfig:', 'color: red; font-weight: bold;', error);
      this.isLoading.next(false);
      return throwError(() => new Error(`Unexpected error loading page: ${error instanceof Error ? error.message : String(error)}`));
    }
  }

  /**
   * Create a new app configuration
   */
  createNewAppConfig(name: string): Observable<AppConfig> {
    this.isLoading.next(true);
    
    // In a real implementation, this would call the API service
    // return this.apiService.createAppConfig({ name }).pipe(...);
    
    // For now, use mock implementation
    return this.apiService.mockCreateConfig(name).pipe(
      tap(config => {
        this.currentAppConfig.next(config);
        
        // Set the first page as active
        if (config.pages && config.pages.length > 0) {
          this.currentPage.next(config.pages[0]);
          this.componentStore.resetState(); // Reset component store for new page
        }
        
        this.isLoading.next(false);
        this.isDirty.next(false);
      }),
      catchError(error => {
        this.isLoading.next(false);
        return throwError(() => error);
      })
    );
  }

  /**
   * Create a new page in the current app configuration
   */
  createNewPage(configId: string, name: string): Observable<PageConfig> {
    this.isLoading.next(true);
    
    // Get the current config parameters
    const params = this.configParams.getValue();
    if (!params) {
      return throwError(() => new Error('No configuration parameters loaded'));
    }
    
    // In a real implementation, this would call the API service
    // return this.apiService.createPage(configId, { name }).pipe(...);
    
    // For now, use mock implementation
    return this.apiService.mockCreatePage(name).pipe(
      tap(page => {
        // Add the new page to the current app config
        const currentConfig = this.currentAppConfig.getValue(); // Get value once
        if (currentConfig && currentConfig.id) { // Explicitly check config and id
          console.log(`DEBUG: Adding new page ${page.id} to currentConfig ${currentConfig.id}`);
          // Standardize to array format for simplicity
          let pagesArray: PageConfig[] = [];
          if (currentConfig.pages) {
            pagesArray = Array.isArray(currentConfig.pages) 
              ? currentConfig.pages 
              : typeof currentConfig.pages === 'object' ? Object.values(currentConfig.pages as Record<string, PageConfig>) : []; // Handle non-object/array case too
          } else {
            pagesArray = []; // Initialize if pages was null/undefined
          }
          pagesArray.push(page);
          // Create a new object for the update to ensure change detection
          const updatedConfig: AppConfig = { 
            ...currentConfig, // Spread existing properties (id is guaranteed here)
            pages: pagesArray // Assign the updated array
          }; 
            
          this.currentAppConfig.next(updatedConfig); // Trigger update with the new object
          this.currentPage.next(page); // Set the new page as active
          console.log(`DEBUG: Resetting component store for new page: ${page.id}`);
          this.componentStore.resetState(); // Reset components for new page
        }
        
        this.isLoading.next(false);
        this.isDirty.next(true); // Mark as dirty since we have a new page
      }),
      catchError(error => {
        this.isLoading.next(false);
        console.error('Error creating page:', error);
        // Log potential custom error codes
        if (error && typeof error === 'object' && 'code' in error) {
          console.error(`%cDEBUG: Detected error code: ${error.code}`, 'color: red; font-weight: bold;');
        }
        return throwError(() => error);
      })
    );
  }

  /**
   * Duplicate a page
   */
  duplicatePage(configId: string, pageId: string, newName: string): Observable<PageConfig> {
    this.isLoading.next(true);
    
    // Get the current state of components to duplicate
    const componentsState = this.componentStore.exportState();
    
    // In a real implementation, this would call the API service
    // return this.apiService.duplicatePage(configId, pageId, newName).pipe(...);
    
    // For now, use mock implementation
    return this.apiService.mockCreatePage(newName).pipe(
      tap(page => {
        // Add the new page to the current app config
        const currentConfig = this.currentAppConfig.getValue();
        if (currentConfig) {
          currentConfig.pages.push(page);
          this.currentAppConfig.next(currentConfig);
        }
        
        this.currentPage.next(page);
        
        // Duplicate the components from the current page
        if (componentsState['components']) {
          // Create new IDs for all components to avoid conflicts
          const oldToNewIdMap = new Map<string, string>();
          const duplicatedComponents: Record<string, BuilderComponent> = {};

          // First pass: duplicate all components with new IDs
          Object.values(componentsState['components']).forEach((component: any) => {
            const newId = `${component.id}_copy_${Date.now().toString(36)}`;
            oldToNewIdMap.set(component.id, newId);

            duplicatedComponents[newId] = {
              ...component,
              id: newId,
              parentId: null, // Will be updated in second pass
              children: [] // Will be updated in second pass
            };
          });

          // Second pass: update parent/child relationships with new IDs
          Object.values(duplicatedComponents).forEach(component => {
            const originalId = component.id.split('_copy_')[0];
            const originalComponent = (componentsState['components'] as any)[originalId];

            // Update parentId
            if (originalComponent.parentId) {
              const newParentId = oldToNewIdMap.get(originalComponent.parentId);
              if (newParentId) {
                component.parentId = newParentId;
              }
            }

            // Update children
            component.children = originalComponent.children
              .map((childId: string) => oldToNewIdMap.get(childId))
              .filter((id: string | undefined) => id !== undefined) as string[];
          });
          
          // Update page components (root level)
          page.components = Object.values(duplicatedComponents)
            .filter(c => !c.parentId)
            .map(c => c.id);
          
          // Load the duplicated components into the component store
          this.componentStore.loadFromPage(page, duplicatedComponents);
        }
        
        this.isLoading.next(false);
        this.isDirty.next(true); // Mark as dirty since we have a new page
      }),
      catchError(error => {
        this.isLoading.next(false);
        console.error('Error duplicating page:', error);
        // Log potential custom error codes
        if (error && typeof error === 'object' && 'code' in error) {
          console.error(`%cDEBUG: Detected error code: ${error.code}`, 'color: red; font-weight: bold;');
        }
        return throwError(() => error);
      })
    );
  }

  /**
   * Delete a page from the current app configuration
   */
  deletePage(configId: string, pageId: string): Observable<boolean> {
    this.isLoading.next(true);
    
    // In a real implementation, this would call the API service
    // return this.apiService.deletePage(configId, pageId).pipe(...);
    
    // For now, use mock implementation
    return of(true).pipe(
      tap(() => {
        // Remove the page from the current app config
        const currentConfig = this.currentAppConfig.getValue();
        if (currentConfig) {
          currentConfig.pages = currentConfig.pages.filter((p: PageConfig) => p.id !== pageId);
          this.currentAppConfig.next(currentConfig);
          
          // If we deleted the current page, select another one
          if (this.currentPage.getValue()?.id === pageId) {
            if (currentConfig.pages.length > 0) {
              this.currentPage.next(currentConfig.pages[0]);
              // We would need to load the components for the new page
              this.loadPageConfig(configId, currentConfig.pages[0].id);
            } else {
              this.currentPage.next(null);
              this.componentStore.resetState();
            }
          }
        }
        
        this.isLoading.next(false);
        this.isDirty.next(true); // Mark as dirty since we deleted a page
      }),
      catchError(error => {
        this.isLoading.next(false);
        console.error('Error deleting page:', error);
        // Log potential custom error codes
        if (error && typeof error === 'object' && 'code' in error) {
          console.error(`%cDEBUG: Detected error code: ${error.code}`, 'color: red; font-weight: bold;');
        }
        return throwError(() => error);
      })
    );
  }

  /**
   * Save the current state (Enhanced with Persistence Service)
   */
  saveCurrentState(): Observable<boolean> {
    console.log('[BuilderConfig] Starting enhanced save operation...');
    this.isLoading.next(true);

    const currentConfig = this.currentAppConfig.getValue();
    const currentPage = this.currentPage.getValue();

    if (!currentConfig || !currentPage) {
      this.isLoading.next(false);
      return throwError(() => new Error('No configuration or page loaded'));
    }

    // Get the current component state
    const componentState = this.componentStore.exportState();

    // Update the page in the app config with current component state
    const updatedPages = currentConfig.pages.map((p: PageConfig) =>
      p.id === currentPage.id
        ? {
            ...p,
            components: this.componentStore.getRootComponentIds(),
            updatedAt: new Date().toISOString()
          }
        : p
    );

    const updatedConfig: AppConfig = {
      ...currentConfig,
      pages: updatedPages,
      updatedAt: new Date().toISOString()
    };

    // Use the enhanced persistence service for robust saving
    return this.persistenceService.saveConfiguration(currentConfig.id, updatedConfig).pipe(
      map((saveResult: SaveResult) => {
        if (saveResult.success) {
          console.log(`[BuilderConfig] Configuration saved successfully. Changes: ${saveResult.changesCount}`);

          // Update local state with saved configuration
          this.currentAppConfig.next(updatedConfig);

          // Find and update the current page
          const updatedPage = updatedPages.find((p: PageConfig) => p.id === currentPage.id);
          if (updatedPage) {
            this.currentPage.next(updatedPage);
          }

          // Update localStorage as backup
          localStorage.setItem(`app_config_${currentConfig.id}`, JSON.stringify(updatedConfig));

          this.isDirty.next(false); // Reset dirty state after successful save

          if (saveResult.warnings.length > 0) {
            console.warn('[BuilderConfig] Save completed with warnings:', saveResult.warnings);
          }

          return true;
        } else {
          console.error('[BuilderConfig] Save failed:', saveResult.errors);
          throw new Error(`Save failed: ${saveResult.errors.join(', ')}`);
        }
      }),
      tap(() => {
        this.isLoading.next(false);
      }),
      catchError(error => {
        this.isLoading.next(false);
        console.error('[BuilderConfig] Error during save operation:', error);

        // Fallback to local storage save if API fails
        try {
          localStorage.setItem(`app_config_${currentConfig.id}`, JSON.stringify(updatedConfig));
          console.warn('[BuilderConfig] Saved to localStorage as fallback');

          // Update local state even if API save failed
          this.currentAppConfig.next(updatedConfig);
          const updatedPage = updatedPages.find((p: PageConfig) => p.id === currentPage.id);
          if (updatedPage) {
            this.currentPage.next(updatedPage);
          }
          this.isDirty.next(false);

          return of(true); // Return success for localStorage fallback
        } catch (localError) {
          console.error('[BuilderConfig] Even localStorage fallback failed:', localError);
          return throwError(() => error);
        }
      })
    );
  }

  /**
   * Mark the current state as dirty (has unsaved changes)
   */
  markAsDirty(): void {
    this.isDirty.next(true);

    // Also mark in persistence service for auto-save tracking
    const currentConfig = this.currentAppConfig.getValue();
    if (currentConfig) {
      this.persistenceService.markAsChanged(`config_${currentConfig.id}_${Date.now()}`);
    }
  }

  /**
   * Get persistence state for UI indicators
   */
  getPersistenceState() {
    return this.persistenceService.getPersistenceState();
  }

  /**
   * Enable/disable auto-save
   */
  setAutoSaveEnabled(enabled: boolean): void {
    this.persistenceService.setAutoSaveEnabled(enabled);
  }

  /**
   * Create backup before major operations
   */
  createBackup(): Observable<string> {
    const currentConfig = this.currentAppConfig.getValue();
    if (!currentConfig) {
      return throwError(() => new Error('No configuration loaded'));
    }

    return this.persistenceService.createBackup(currentConfig.id);
  }

  /**
   * Publish configuration to environment
   */
  publishConfiguration(targetEnvironment: string): Observable<boolean> {
    const currentConfig = this.currentAppConfig.getValue();
    if (!currentConfig) {
      return throwError(() => new Error('No configuration loaded'));
    }

    return this.persistenceService.publishConfiguration(currentConfig.id, targetEnvironment);
  }

  /**
   * Adapt a loaded configuration to match the expected AppConfig structure
   * @param originalConfig The original configuration from localStorage
   * @param configId The configuration ID
   * @returns Adapted AppConfig object
   */
  private adaptConfigForBuilder(originalConfig: any, configId: string): AppConfig {
    console.log(`%cDEBUG: Adapting config for builder format - configId: ${configId}`, 'color: blue; font-weight: bold;');
    
    // Create a new config object with the required properties
    const now = new Date().toISOString();
    const adaptedConfig: AppConfig = {
      id: configId, // Use configId as the id property
      name: originalConfig.appName || `${configId} Configuration`,
      version: originalConfig.appVersion || '1.0.0',
      createdAt: now,
      updatedAt: now,
      pages: [],
      owner: 'system'
    };
    
    // Copy useful properties from the original config
    // These will be available for use by the components even though they're not in the AppConfig interface
    Object.assign(adaptedConfig, originalConfig);
    
    // Handle the pages array - transform page objects to match PageConfig interface
    if (Array.isArray(originalConfig.pages)) {
      adaptedConfig.pages = originalConfig.pages.map((page: any, index: number) => {
        // Create a unique ID for each page based on its path
        const pageId = page.id || `${page.path || 'page'}_${index}`;
        
        // Transform the page object to match PageConfig interface
        return {
          id: pageId,
          name: page.title || `Page ${index + 1}`,
          components: Array.isArray(page.components) 
            ? this.extractComponentIds(page.components) 
            : [],
          type: 'builder', // Default type
          layout: 'fluid',  // Default layout
          properties: {
            tailwindClasses: page.class || '',
            path: page.path || '',
            secure: page.secure || false,
            ...(page.properties || {})
          },
          // Keep original properties to preserve configuration data
          ...(page)
        };
      });
    }
    
    console.log(`%cDEBUG: Adapted config with id: ${adaptedConfig.id}, pages count: ${adaptedConfig.pages.length}`, 'color: green;');
    return adaptedConfig;
  }
  
  /**
   * Extract component IDs from component objects if needed
   * This handles the case where components might be objects with more data
   */
  private extractComponentIds(components: any[]): string[] {
    if (!components || !Array.isArray(components)) return [];
    
    // If components are already strings, return them directly
    if (typeof components[0] === 'string') return components;
    
    // Otherwise, extract IDs or generate them based on component type
    return components.map((comp, index) => {
      // If component has an ID, use it
      if (comp.id) return comp.id;
      
      // Otherwise generate a unique ID based on its type and index
      return `${comp.type || 'component'}_${index}`;
    });
  }
}

/**
 * Add a small delay to simulate network latency
 */
function delay<T>(ms: number) {
  return (source: Observable<T>) => 
    new Observable<T>(subscriber => {
      const subscription = source.subscribe({
        next(value: T) {
          setTimeout(() => subscriber.next(value), ms);
        },
        error(error: any) {
          setTimeout(() => subscriber.error(error), ms);
        },
        complete() {
          setTimeout(() => subscriber.complete(), ms);
        }
      });
      
      return () => subscription.unsubscribe();
    });
}
