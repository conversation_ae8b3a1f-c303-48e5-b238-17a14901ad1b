import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { BuilderConfigService } from './builder-config.service';
import { StorageService } from '../../shared/services/storage.service';
import { NotificationService } from '../../shared/services/notification.service';
import { 
  MockAppConfigFactory, 
  MockStorageService, 
  MockNotificationService,
  TestDataLoader,
  setupComponentTest 
} from '../../../testing';
import { AppConfig, ConfigurationStatus } from '../models/app-config.interface';

describe('BuilderConfigService', () => {
  let service: BuilderConfigService;
  let httpMock: HttpTestingController;
  let storageService: jasmine.SpyObj<StorageService>;
  let notificationService: jasmine.SpyObj<NotificationService>;

  beforeEach(async () => {
    const storageSpyObj = jasmine.createSpyObj('StorageService', ['get', 'set', 'remove', 'clear']);
    const notificationSpyObj = jasmine.createSpyObj('NotificationService', ['show', 'error', 'success']);

    await TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        BuilderConfigService,
        { provide: StorageService, useValue: storageSpyObj },
        { provide: NotificationService, useValue: notificationSpyObj }
      ]
    }).compileComponents();

    service = TestBed.inject(BuilderConfigService);
    httpMock = TestBed.inject(HttpTestingController);
    storageService = TestBed.inject(StorageService) as jasmine.SpyObj<StorageService>;
    notificationService = TestBed.inject(NotificationService) as jasmine.SpyObj<NotificationService>;
  });

  afterEach(() => {
    httpMock.verify();
  });

  describe('Configuration Management', () => {
    it('should save configuration', (done) => {
      const mockConfig = MockAppConfigFactory.createMockAppConfig();
      const expectedResponse = { ...mockConfig, updatedAt: new Date().toISOString() };

      service.saveConfiguration(mockConfig).subscribe(result => {
        expect(result.id).toBe(mockConfig.id);
        expect(result.updatedAt).toBeDefined();
        expect(storageService.set).toHaveBeenCalledWith(`config_${mockConfig.id}`, mockConfig);
        done();
      });

      const req = httpMock.expectOne('/api/configurations');
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(mockConfig);
      req.flush(expectedResponse);
    });

    it('should load configuration by ID', (done) => {
      const mockConfig = MockAppConfigFactory.createMockAppConfig();
      
      service.loadConfiguration(mockConfig.id).subscribe(result => {
        expect(result).toEqual(mockConfig);
        expect(storageService.set).toHaveBeenCalledWith(`config_${mockConfig.id}`, mockConfig);
        done();
      });

      const req = httpMock.expectOne(`/api/configurations/${mockConfig.id}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockConfig);
    });

    it('should load all configurations', (done) => {
      const mockConfigs = [
        MockAppConfigFactory.createMockAppConfig({ name: 'Config 1' }),
        MockAppConfigFactory.createMockAppConfig({ name: 'Config 2' })
      ];

      service.loadConfigurations().subscribe(result => {
        expect(result).toEqual(mockConfigs);
        expect(result.length).toBe(2);
        done();
      });

      const req = httpMock.expectOne('/api/configurations');
      expect(req.request.method).toBe('GET');
      req.flush(mockConfigs);
    });

    it('should delete configuration', (done) => {
      const configId = 'test-config-123';

      service.deleteConfiguration(configId).subscribe(result => {
        expect(result).toBe(true);
        expect(storageService.remove).toHaveBeenCalledWith(`config_${configId}`);
        done();
      });

      const req = httpMock.expectOne(`/api/configurations/${configId}`);
      expect(req.request.method).toBe('DELETE');
      req.flush({ success: true });
    });
  });

  describe('Configuration Validation', () => {
    it('should validate valid configuration', (done) => {
      const mockConfig = TestDataLoader.getBasicConfiguration();

      service.validateConfiguration(mockConfig).subscribe(result => {
        expect(result.isValid).toBe(true);
        expect(result.errors).toEqual([]);
        done();
      });

      const req = httpMock.expectOne('/api/configurations/validate');
      expect(req.request.method).toBe('POST');
      req.flush({ isValid: true, errors: [] });
    });

    it('should handle validation errors', (done) => {
      const invalidConfig = MockAppConfigFactory.createMockAppConfig({
        name: '', // Invalid: empty name
        pages: [] // Invalid: no pages
      });

      const expectedErrors = [
        { field: 'name', message: 'Configuration name is required' },
        { field: 'pages', message: 'At least one page is required' }
      ];

      service.validateConfiguration(invalidConfig).subscribe(result => {
        expect(result.isValid).toBe(false);
        expect(result.errors).toEqual(expectedErrors);
        done();
      });

      const req = httpMock.expectOne('/api/configurations/validate');
      req.flush({ isValid: false, errors: expectedErrors });
    });
  });

  describe('Import/Export Operations', () => {
    it('should export configuration as JSON', (done) => {
      const mockConfig = TestDataLoader.getComplexConfiguration();

      service.exportConfiguration(mockConfig).subscribe(result => {
        const parsed = JSON.parse(result);
        expect(parsed.id).toBe(mockConfig.id);
        expect(parsed.name).toBe(mockConfig.name);
        done();
      });

      const req = httpMock.expectOne('/api/configurations/export');
      expect(req.request.method).toBe('POST');
      req.flush(JSON.stringify(mockConfig, null, 2));
    });

    it('should import configuration from JSON', (done) => {
      const mockConfig = TestDataLoader.getBasicConfiguration();
      const jsonData = JSON.stringify(mockConfig);

      service.importConfiguration(jsonData).subscribe(result => {
        expect(result.id).toBe(mockConfig.id);
        expect(result.name).toBe(mockConfig.name);
        done();
      });

      const req = httpMock.expectOne('/api/configurations/import');
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({ data: jsonData });
      req.flush(mockConfig);
    });

    it('should handle invalid JSON import', (done) => {
      const invalidJson = '{ invalid json }';

      service.importConfiguration(invalidJson).subscribe({
        next: () => fail('Should have failed'),
        error: (error) => {
          expect(error.message).toContain('Invalid JSON');
          done();
        }
      });

      const req = httpMock.expectOne('/api/configurations/import');
      req.flush({ error: 'Invalid JSON format' }, { status: 400, statusText: 'Bad Request' });
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', (done) => {
      const mockConfig = MockAppConfigFactory.createMockAppConfig();

      service.saveConfiguration(mockConfig).subscribe({
        next: () => fail('Should have failed'),
        error: (error) => {
          expect(notificationService.error).toHaveBeenCalledWith(
            jasmine.stringContaining('Failed to save configuration')
          );
          done();
        }
      });

      const req = httpMock.expectOne('/api/configurations');
      req.error(new ErrorEvent('Network error'), { status: 0 });
    });

    it('should handle server errors', (done) => {
      const mockConfig = MockAppConfigFactory.createMockAppConfig();

      service.saveConfiguration(mockConfig).subscribe({
        next: () => fail('Should have failed'),
        error: (error) => {
          expect(error.status).toBe(500);
          expect(notificationService.error).toHaveBeenCalled();
          done();
        }
      });

      const req = httpMock.expectOne('/api/configurations');
      req.flush({ error: 'Internal server error' }, { status: 500, statusText: 'Server Error' });
    });
  });

  describe('Caching and Storage', () => {
    it('should cache configurations in storage', (done) => {
      const mockConfig = MockAppConfigFactory.createMockAppConfig();
      storageService.get.and.returnValue(Promise.resolve(mockConfig));

      service.getConfigurationFromCache(mockConfig.id).subscribe(result => {
        expect(result).toEqual(mockConfig);
        expect(storageService.get).toHaveBeenCalledWith(`config_${mockConfig.id}`);
        done();
      });
    });

    it('should return null for non-cached configuration', (done) => {
      storageService.get.and.returnValue(Promise.resolve(null));

      service.getConfigurationFromCache('non-existent-id').subscribe(result => {
        expect(result).toBeNull();
        done();
      });
    });

    it('should clear configuration cache', (done) => {
      const configId = 'test-config-123';

      service.clearConfigurationCache(configId).subscribe(result => {
        expect(result).toBe(true);
        expect(storageService.remove).toHaveBeenCalledWith(`config_${configId}`);
        done();
      });
    });
  });

  describe('Configuration Status Management', () => {
    it('should update configuration status', (done) => {
      const configId = 'test-config-123';
      const newStatus = ConfigurationStatus.PUBLISHED;

      service.updateConfigurationStatus(configId, newStatus).subscribe(result => {
        expect(result.status).toBe(newStatus);
        done();
      });

      const req = httpMock.expectOne(`/api/configurations/${configId}/status`);
      expect(req.request.method).toBe('PATCH');
      expect(req.request.body).toEqual({ status: newStatus });
      req.flush({ id: configId, status: newStatus });
    });

    it('should duplicate configuration', (done) => {
      const originalConfig = MockAppConfigFactory.createMockAppConfig();
      const duplicatedConfig = {
        ...originalConfig,
        id: 'duplicated-config-id',
        name: `${originalConfig.name} (Copy)`,
        status: ConfigurationStatus.DRAFT
      };

      service.duplicateConfiguration(originalConfig.id).subscribe(result => {
        expect(result.name).toContain('(Copy)');
        expect(result.status).toBe(ConfigurationStatus.DRAFT);
        expect(result.id).not.toBe(originalConfig.id);
        done();
      });

      const req = httpMock.expectOne(`/api/configurations/${originalConfig.id}/duplicate`);
      expect(req.request.method).toBe('POST');
      req.flush(duplicatedConfig);
    });
  });

  describe('Performance Tests', () => {
    it('should handle large configuration efficiently', (done) => {
      const largeConfig = TestDataLoader.getLargeConfiguration();
      const startTime = performance.now();

      service.saveConfiguration(largeConfig).subscribe(result => {
        const duration = performance.now() - startTime;
        expect(duration).toBeLessThan(1000); // Should complete within 1 second
        expect(result.id).toBe(largeConfig.id);
        done();
      });

      const req = httpMock.expectOne('/api/configurations');
      // Simulate server processing time
      setTimeout(() => {
        req.flush({ ...largeConfig, updatedAt: new Date().toISOString() });
      }, 100);
    });

    it('should batch multiple configuration operations', (done) => {
      const configs = [
        MockAppConfigFactory.createMockAppConfig(),
        MockAppConfigFactory.createMockAppConfig(),
        MockAppConfigFactory.createMockAppConfig()
      ];

      service.batchSaveConfigurations(configs).subscribe(results => {
        expect(results.length).toBe(3);
        expect(results.every(r => r.id)).toBe(true);
        done();
      });

      const req = httpMock.expectOne('/api/configurations/batch');
      expect(req.request.method).toBe('POST');
      expect(req.request.body.configurations.length).toBe(3);
      req.flush({ results: configs.map(c => ({ ...c, updatedAt: new Date().toISOString() })) });
    });
  });
});