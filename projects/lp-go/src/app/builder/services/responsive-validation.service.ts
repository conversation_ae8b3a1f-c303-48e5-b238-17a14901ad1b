import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { 
  ResponsiveValidationRule, 
  ResponsiveValidationResult, 
  DeviceType, 
  Breakpoint 
} from '../models/viewport.interface';
import { BuilderComponent } from '../models/builder-component.interface';

/**
 * Responsive validation issue
 */
export interface ResponsiveValidationIssue {
  id: string;
  componentId: string;
  ruleId: string;
  severity: 'error' | 'warning' | 'info';
  message: string;
  suggestion?: string;
  breakpoint: Breakpoint;
  autoFixAvailable: boolean;
  autoFix?: () => void;
}

/**
 * Responsive Validation Service
 * Validates components for responsive design best practices
 */
@Injectable({
  providedIn: 'root'
})
export class ResponsiveValidationService {
  private validationRules: ResponsiveValidationRule[] = [];
  private validationIssuesSubject = new BehaviorSubject<ResponsiveValidationIssue[]>([]);
  private isValidationEnabled = true;

  constructor() {
    this.initializeDefaultRules();
  }

  /**
   * Get validation issues as observable
   */
  getValidationIssues(): Observable<ResponsiveValidationIssue[]> {
    return this.validationIssuesSubject.asObservable();
  }

  /**
   * Get current validation issues
   */
  getCurrentValidationIssues(): ResponsiveValidationIssue[] {
    return this.validationIssuesSubject.value;
  }

  /**
   * Validate components for responsive issues
   */
  validateComponents(components: BuilderComponent[], breakpoints: Breakpoint[]): void {
    if (!this.isValidationEnabled) return;

    const issues: ResponsiveValidationIssue[] = [];

    components.forEach(component => {
      breakpoints.forEach(breakpoint => {
        this.validationRules.forEach(rule => {
          if (rule.targetDevices.includes(breakpoint.deviceType)) {
            const result = rule.validate(component, breakpoint);
            
            if (!result.isValid) {
              issues.push({
                id: `${component.id}-${rule.id}-${breakpoint.id}`,
                componentId: component.id,
                ruleId: rule.id,
                severity: rule.severity,
                message: result.message,
                suggestion: result.suggestion,
                breakpoint,
                autoFixAvailable: !!result.autoFix,
                autoFix: result.autoFix
              });
            }
          }
        });
      });
    });

    this.validationIssuesSubject.next(issues);
  }

  /**
   * Add custom validation rule
   */
  addValidationRule(rule: ResponsiveValidationRule): void {
    this.validationRules.push(rule);
  }

  /**
   * Remove validation rule
   */
  removeValidationRule(ruleId: string): void {
    this.validationRules = this.validationRules.filter(rule => rule.id !== ruleId);
  }

  /**
   * Enable/disable validation
   */
  setValidationEnabled(enabled: boolean): void {
    this.isValidationEnabled = enabled;
    if (!enabled) {
      this.validationIssuesSubject.next([]);
    }
  }

  /**
   * Apply auto-fix for an issue
   */
  applyAutoFix(issueId: string): void {
    const issue = this.validationIssuesSubject.value.find(i => i.id === issueId);
    if (issue?.autoFix) {
      issue.autoFix();
      // Re-validate after fix
      // This would need to be triggered by the calling component
    }
  }

  /**
   * Dismiss an issue
   */
  dismissIssue(issueId: string): void {
    const currentIssues = this.validationIssuesSubject.value;
    const filteredIssues = currentIssues.filter(issue => issue.id !== issueId);
    this.validationIssuesSubject.next(filteredIssues);
  }

  /**
   * Get issues by severity
   */
  getIssuesBySeverity(severity: 'error' | 'warning' | 'info'): ResponsiveValidationIssue[] {
    return this.validationIssuesSubject.value.filter(issue => issue.severity === severity);
  }

  /**
   * Get issues by component
   */
  getIssuesByComponent(componentId: string): ResponsiveValidationIssue[] {
    return this.validationIssuesSubject.value.filter(issue => issue.componentId === componentId);
  }

  /**
   * Initialize default validation rules
   */
  private initializeDefaultRules(): void {
    // Text size validation
    this.validationRules.push({
      id: 'text-size-mobile',
      name: 'Text Size - Mobile',
      description: 'Text should be at least 16px on mobile devices for readability',
      severity: 'warning',
      targetDevices: [DeviceType.MOBILE],
      validate: (component: BuilderComponent, breakpoint: Breakpoint): ResponsiveValidationResult => {
        if (component.type !== 'text') {
          return { isValid: true, message: '' };
        }

        const fontSize = this.extractFontSize(component.properties?.tailwindClasses || '');
        if (fontSize && fontSize < 16) {
          return {
            isValid: false,
            message: `Text size ${fontSize}px is too small for mobile. Minimum recommended: 16px`,
            suggestion: 'Use text-base (16px) or larger for mobile devices',
            autoFix: () => {
              // Auto-fix implementation would go here
              console.log('Auto-fixing text size for mobile');
            }
          };
        }

        return { isValid: true, message: '' };
      }
    });

    // Touch target size validation
    this.validationRules.push({
      id: 'touch-target-size',
      name: 'Touch Target Size',
      description: 'Interactive elements should be at least 44px for touch accessibility',
      severity: 'error',
      targetDevices: [DeviceType.MOBILE, DeviceType.TABLET],
      validate: (component: BuilderComponent, breakpoint: Breakpoint): ResponsiveValidationResult => {
        if (!['button', 'input', 'select'].includes(component.type)) {
          return { isValid: true, message: '' };
        }

        const minSize = this.extractMinSize(component.properties?.tailwindClasses || '');
        if (minSize && minSize < 44) {
          return {
            isValid: false,
            message: `Touch target ${minSize}px is too small. Minimum recommended: 44px`,
            suggestion: 'Add padding or increase size to meet touch target requirements',
            autoFix: () => {
              console.log('Auto-fixing touch target size');
            }
          };
        }

        return { isValid: true, message: '' };
      }
    });

    // Content width validation
    this.validationRules.push({
      id: 'content-width-mobile',
      name: 'Content Width - Mobile',
      description: 'Content should not exceed viewport width on mobile',
      severity: 'warning',
      targetDevices: [DeviceType.MOBILE],
      validate: (component: BuilderComponent, breakpoint: Breakpoint): ResponsiveValidationResult => {
        const hasFixedWidth = this.hasFixedWidth(component.properties?.tailwindClasses || '');
        const width = this.extractWidth(component.properties?.tailwindClasses || '');
        
        if (hasFixedWidth && width && width > breakpoint.width) {
          return {
            isValid: false,
            message: `Fixed width ${width}px exceeds mobile viewport ${breakpoint.width}px`,
            suggestion: 'Use responsive width classes like w-full or max-w-*',
            autoFix: () => {
              console.log('Auto-fixing content width for mobile');
            }
          };
        }

        return { isValid: true, message: '' };
      }
    });

    // Image optimization validation
    this.validationRules.push({
      id: 'image-optimization',
      name: 'Image Optimization',
      description: 'Images should be optimized for mobile devices',
      severity: 'info',
      targetDevices: [DeviceType.MOBILE],
      validate: (component: BuilderComponent, breakpoint: Breakpoint): ResponsiveValidationResult => {
        if (component.type !== 'image') {
          return { isValid: true, message: '' };
        }

        // Check if image has responsive classes
        const hasResponsiveClasses = this.hasResponsiveImageClasses(component.properties?.tailwindClasses || '');
        
        if (!hasResponsiveClasses) {
          return {
            isValid: false,
            message: 'Image lacks responsive optimization classes',
            suggestion: 'Add classes like object-cover, w-full, or h-auto for better mobile display'
          };
        }

        return { isValid: true, message: '' };
      }
    });

    // Spacing validation
    this.validationRules.push({
      id: 'spacing-mobile',
      name: 'Spacing - Mobile',
      description: 'Adequate spacing between elements on mobile',
      severity: 'warning',
      targetDevices: [DeviceType.MOBILE],
      validate: (component: BuilderComponent, breakpoint: Breakpoint): ResponsiveValidationResult => {
        const hasAdequateSpacing = this.hasAdequateSpacing(component.properties?.tailwindClasses || '');
        
        if (!hasAdequateSpacing) {
          return {
            isValid: false,
            message: 'Insufficient spacing for mobile touch interaction',
            suggestion: 'Add margin or padding classes for better mobile usability'
          };
        }

        return { isValid: true, message: '' };
      }
    });
  }

  // Helper methods for validation

  private extractFontSize(classes: string): number | null {
    const sizeMap: { [key: string]: number } = {
      'text-xs': 12,
      'text-sm': 14,
      'text-base': 16,
      'text-lg': 18,
      'text-xl': 20,
      'text-2xl': 24,
      'text-3xl': 30,
      'text-4xl': 36,
      'text-5xl': 48,
      'text-6xl': 60
    };

    for (const [className, size] of Object.entries(sizeMap)) {
      if (classes.includes(className)) {
        return size;
      }
    }

    return null;
  }

  private extractMinSize(classes: string): number | null {
    // Extract minimum size from padding and height classes
    const paddingMatch = classes.match(/p-(\d+)|py-(\d+)|px-(\d+)/);
    const heightMatch = classes.match(/h-(\d+)/);
    
    if (paddingMatch || heightMatch) {
      // Simplified calculation - in real implementation would be more sophisticated
      return 32; // Default minimum for demonstration
    }

    return null;
  }

  private hasFixedWidth(classes: string): boolean {
    return /w-\d+/.test(classes) && !classes.includes('w-full') && !classes.includes('w-auto');
  }

  private extractWidth(classes: string): number | null {
    const widthMatch = classes.match(/w-(\d+)/);
    if (widthMatch) {
      // Convert Tailwind width to pixels (simplified)
      return parseInt(widthMatch[1]) * 4; // Tailwind uses 0.25rem units
    }
    return null;
  }

  private hasResponsiveImageClasses(classes: string): boolean {
    const responsiveClasses = ['object-cover', 'object-contain', 'w-full', 'h-auto', 'max-w-full'];
    return responsiveClasses.some(cls => classes.includes(cls));
  }

  private hasAdequateSpacing(classes: string): boolean {
    const spacingClasses = ['m-', 'p-', 'space-', 'gap-'];
    return spacingClasses.some(cls => classes.includes(cls));
  }
}
