import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { v4 as uuidv4 } from 'uuid';

import {
  Template,
  TemplateCategory,
  TemplatePreview,
  TemplateApplicationOptions,
  TemplateSearchOptions,
  TemplateCreationData,
  TemplateValidationResult,
  TemplateExportData,
  TemplateMetadata
} from '../models/template.interface';
import { BuilderComponent, PageConfig } from '../models/builder-component.interface';
import { ComponentStoreService } from './component-store.service';
import { BuilderConfigService } from './builder-config.service';

/**
 * Template Service
 * Manages template creation, storage, retrieval, and application
 */
@Injectable({
  providedIn: 'root'
})
export class TemplateService {
  private readonly STORAGE_KEY = 'lp-go-templates';
  private readonly BUILT_IN_TEMPLATES_KEY = 'lp-go-built-in-templates';
  
  // Template state management
  private templatesSubject = new BehaviorSubject<Template[]>([]);
  private builtInTemplates: Template[] = [];
  private userTemplates: Template[] = [];
  
  constructor(
    private componentStore: ComponentStoreService,
    private configService: BuilderConfigService
  ) {
    this.initializeService();
  }

  /**
   * Initialize the template service
   */
  private initializeService(): void {
    this.loadBuiltInTemplates();
    this.loadUserTemplates();
    this.updateTemplatesSubject();
  }

  /**
   * Get all templates with optional filtering
   */
  getTemplates(options?: TemplateSearchOptions): Observable<Template[]> {
    return this.templatesSubject.pipe(
      map(templates => this.filterTemplates(templates, options))
    );
  }

  /**
   * Get template by ID
   */
  getTemplate(templateId: string): Observable<Template | null> {
    return this.templatesSubject.pipe(
      map(templates => templates.find(t => t.id === templateId) || null)
    );
  }

  /**
   * Get template preview
   */
  getTemplatePreview(templateId: string): Observable<TemplatePreview> {
    return this.getTemplate(templateId).pipe(
      map(template => {
        if (!template) {
          throw new Error(`Template ${templateId} not found`);
        }
        
        return {
          templateId: template.id,
          thumbnail: template.thumbnail,
          componentCount: Object.keys(template.components).length,
          description: template.description,
          metadata: template.metadata
        };
      }),
      catchError(error => throwError(() => error))
    );
  }

  /**
   * Apply template to a page
   */
  applyTemplate(templateId: string, options: TemplateApplicationOptions): Observable<void> {
    return new Observable(observer => {
      this.getTemplate(templateId).subscribe({
        next: (template) => {
          if (!template) {
            observer.error(new Error(`Template ${templateId} not found`));
            return;
          }

          try {
            this.applyTemplateToPage(template, options);
            observer.next();
            observer.complete();
          } catch (error) {
            observer.error(error);
          }
        },
        error: (error) => observer.error(error)
      });
    });
  }

  /**
   * Save current page as template
   */
  saveAsTemplate(creationData: TemplateCreationData): Observable<Template> {
    return new Observable(observer => {
      try {
        // Get current page and components
        const currentPage = this.componentStore.getActivePage();
        const components = this.componentStore.exportState();
        
        if (!currentPage) {
          observer.error(new Error('No active page to save as template'));
          return;
        }

        // Create template
        const template: Template = {
          id: uuidv4(),
          name: creationData.name,
          description: creationData.description,
          category: creationData.category,
          tags: creationData.tags,
          thumbnail: '', // Will be generated separately
          components: { ...components },
          pageConfig: { ...currentPage.value! },
          metadata: {
            author: 'User',
            version: '1.0.0',
            requiredComponents: this.extractRequiredComponents(components),
            difficulty: 'beginner',
            estimatedTime: '5 minutes',
            ...creationData.metadata
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          isBuiltIn: false,
          isFavorite: false
        };

        // Save to user templates
        this.userTemplates.push(template);
        this.saveUserTemplates();
        this.updateTemplatesSubject();

        observer.next(template);
        observer.complete();
      } catch (error) {
        observer.error(error);
      }
    });
  }

  /**
   * Delete template
   */
  deleteTemplate(templateId: string): Observable<void> {
    return new Observable(observer => {
      try {
        const templateIndex = this.userTemplates.findIndex(t => t.id === templateId);
        
        if (templateIndex === -1) {
          observer.error(new Error(`Template ${templateId} not found or cannot be deleted`));
          return;
        }

        this.userTemplates.splice(templateIndex, 1);
        this.saveUserTemplates();
        this.updateTemplatesSubject();

        observer.next();
        observer.complete();
      } catch (error) {
        observer.error(error);
      }
    });
  }

  /**
   * Validate template
   */
  validateTemplate(template: Template): TemplateValidationResult {
    const result: TemplateValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      missingComponents: [],
      compatibilityIssues: []
    };

    // Basic validation
    if (!template.name || template.name.trim().length === 0) {
      result.errors.push('Template name is required');
      result.isValid = false;
    }

    if (!template.description || template.description.trim().length === 0) {
      result.warnings.push('Template description is recommended');
    }

    if (!template.components || Object.keys(template.components).length === 0) {
      result.errors.push('Template must contain at least one component');
      result.isValid = false;
    }

    // Component validation
    Object.values(template.components).forEach(component => {
      // Check if component type is supported
      // This would integrate with ComponentRegistryService
      if (!this.isComponentTypeSupported(component.type)) {
        result.missingComponents.push(component.type);
        result.isValid = false;
      }
    });

    return result;
  }

  /**
   * Export template
   */
  exportTemplate(templateId: string): Observable<TemplateExportData> {
    return this.getTemplate(templateId).pipe(
      map(template => {
        if (!template) {
          throw new Error(`Template ${templateId} not found`);
        }

        return {
          template,
          formatVersion: '1.0.0',
          exportedAt: new Date().toISOString(),
          exportMetadata: {
            exportedBy: 'LP-Go Template System'
          }
        };
      })
    );
  }

  /**
   * Import template
   */
  importTemplate(exportData: TemplateExportData): Observable<Template> {
    return new Observable(observer => {
      try {
        const validation = this.validateTemplate(exportData.template);
        
        if (!validation.isValid) {
          observer.error(new Error(`Invalid template: ${validation.errors.join(', ')}`));
          return;
        }

        // Generate new ID for imported template
        const importedTemplate: Template = {
          ...exportData.template,
          id: uuidv4(),
          isBuiltIn: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        this.userTemplates.push(importedTemplate);
        this.saveUserTemplates();
        this.updateTemplatesSubject();

        observer.next(importedTemplate);
        observer.complete();
      } catch (error) {
        observer.error(error);
      }
    });
  }

  /**
   * Toggle template favorite status
   */
  toggleFavorite(templateId: string): Observable<void> {
    return new Observable(observer => {
      const template = this.userTemplates.find(t => t.id === templateId);
      
      if (!template) {
        observer.error(new Error(`Template ${templateId} not found`));
        return;
      }

      template.isFavorite = !template.isFavorite;
      template.updatedAt = new Date().toISOString();
      
      this.saveUserTemplates();
      this.updateTemplatesSubject();

      observer.next();
      observer.complete();
    });
  }

  /**
   * Get template categories
   */
  getCategories(): TemplateCategory[] {
    return Object.values(TemplateCategory);
  }

  /**
   * Get popular tags
   */
  getPopularTags(): Observable<string[]> {
    return this.templatesSubject.pipe(
      map(templates => {
        const tagCounts = new Map<string, number>();
        
        templates.forEach(template => {
          template.tags.forEach(tag => {
            tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
          });
        });

        return Array.from(tagCounts.entries())
          .sort((a, b) => b[1] - a[1])
          .slice(0, 20)
          .map(([tag]) => tag);
      })
    );
  }

  // Private helper methods

  private filterTemplates(templates: Template[], options?: TemplateSearchOptions): Template[] {
    if (!options) return templates;

    let filtered = [...templates];

    if (options.query) {
      const query = options.query.toLowerCase();
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(query) ||
        template.description.toLowerCase().includes(query) ||
        template.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    if (options.category) {
      filtered = filtered.filter(template => template.category === options.category);
    }

    if (options.tags && options.tags.length > 0) {
      filtered = filtered.filter(template =>
        options.tags!.some(tag => template.tags.includes(tag))
      );
    }

    if (options.favoritesOnly) {
      filtered = filtered.filter(template => template.isFavorite);
    }

    if (options.builtInOnly) {
      filtered = filtered.filter(template => template.isBuiltIn);
    }

    // Sort templates
    if (options.sortBy) {
      filtered.sort((a, b) => {
        let comparison = 0;
        
        switch (options.sortBy) {
          case 'name':
            comparison = a.name.localeCompare(b.name);
            break;
          case 'date':
            comparison = new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime();
            break;
          case 'category':
            comparison = a.category.localeCompare(b.category);
            break;
          case 'popularity':
            comparison = (a.metadata.usage?.timesUsed || 0) - (b.metadata.usage?.timesUsed || 0);
            break;
        }

        return options.sortDirection === 'desc' ? -comparison : comparison;
      });
    }

    return filtered;
  }

  private applyTemplateToPage(template: Template, options: TemplateApplicationOptions): void {
    try {
      console.log('Applying template:', template.name, 'with options:', options);

      // Step 1: Clear existing components if replaceContent is true
      if (options.replaceContent) {
        this.componentStore.clearAllComponents();
      }

      // Step 2: Create component ID mapping for new IDs if needed
      const componentIdMap = new Map<string, string>();
      if (options.generateNewIds) {
        Object.keys(template.components).forEach(oldId => {
          componentIdMap.set(oldId, uuidv4());
        });
      }

      // Step 3: Create new components from template
      const newComponents: BuilderComponent[] = [];
      Object.values(template.components).forEach(templateComponent => {
        const newComponent = this.createComponentFromTemplate(
          templateComponent,
          componentIdMap,
          options
        );
        newComponents.push(newComponent);
      });

      // Step 4: Add components to store in proper order (parents first)
      const sortedComponents = this.sortComponentsByHierarchy(newComponents);
      sortedComponents.forEach(component => {
        this.componentStore.addComponent(component).subscribe({
          next: () => {
            console.log(`Added component: ${component.id} (${component.type})`);
          },
          error: (error: any) => {
            console.error('Error adding component:', error);
          }
        });
      });

      // Step 5: Update page configuration if replacing content
      if (options.replaceContent && template.pageConfig) {
        this.updatePageConfiguration(template.pageConfig, componentIdMap);
      }

      // Step 6: Update template usage statistics
      this.updateTemplateUsageStats(template.id);

      console.log(`Successfully applied template: ${template.name}`);
    } catch (error) {
      console.error('Error applying template:', error);
      throw error;
    }
  }

  /**
   * Create a component from template with ID mapping and options
   */
  private createComponentFromTemplate(
    templateComponent: BuilderComponent,
    componentIdMap: Map<string, string>,
    options: TemplateApplicationOptions
  ): BuilderComponent {
    const newId = componentIdMap.get(templateComponent.id) || templateComponent.id;
    const newParentId = templateComponent.parentId
      ? (componentIdMap.get(templateComponent.parentId) || templateComponent.parentId)
      : null;

    // Apply position offset if specified
    let newPosition = templateComponent.position;
    if (newPosition && options.positionOffset) {
      newPosition = {
        ...newPosition,
        id: newId,
        x: newPosition.x + options.positionOffset.x,
        y: newPosition.y + options.positionOffset.y
      };
    } else if (newPosition) {
      newPosition = { ...newPosition, id: newId };
    }

    // Map child IDs
    const newChildren = templateComponent.children.map(childId =>
      componentIdMap.get(childId) || childId
    );

    // Apply property overrides if specified
    let newProperties = { ...templateComponent.properties };
    if (options.propertyOverrides) {
      newProperties = { ...newProperties, ...options.propertyOverrides };
    }

    return {
      ...templateComponent,
      id: newId,
      parentId: newParentId,
      children: newChildren,
      position: newPosition,
      properties: newProperties
    };
  }

  /**
   * Sort components by hierarchy (parents before children)
   */
  private sortComponentsByHierarchy(components: BuilderComponent[]): BuilderComponent[] {
    const componentMap = new Map<string, BuilderComponent>();
    components.forEach(comp => componentMap.set(comp.id, comp));

    const sorted: BuilderComponent[] = [];
    const visited = new Set<string>();

    const addComponentAndParents = (component: BuilderComponent) => {
      if (visited.has(component.id)) return;

      // Add parent first if it exists and hasn't been added
      if (component.parentId) {
        const parent = componentMap.get(component.parentId);
        if (parent && !visited.has(parent.id)) {
          addComponentAndParents(parent);
        }
      }

      // Add this component
      if (!visited.has(component.id)) {
        sorted.push(component);
        visited.add(component.id);
      }
    };

    // Process all components
    components.forEach(component => {
      addComponentAndParents(component);
    });

    return sorted;
  }

  /**
   * Update page configuration with new component IDs
   */
  private updatePageConfiguration(
    pageConfig: PageConfig,
    componentIdMap: Map<string, string>
  ): void {
    const newRootComponents = pageConfig.components.map(componentId =>
      componentIdMap.get(componentId) || componentId
    );

    // Update the active page configuration
    // This would integrate with the page management system
    console.log('Updating page configuration with components:', newRootComponents);
  }

  /**
   * Update template usage statistics
   */
  private updateTemplateUsageStats(templateId: string): void {
    const template = this.userTemplates.find(t => t.id === templateId) ||
                    this.builtInTemplates.find(t => t.id === templateId);

    if (template) {
      if (!template.metadata.usage) {
        template.metadata.usage = { timesUsed: 0 };
      }

      template.metadata.usage.timesUsed = (template.metadata.usage.timesUsed || 0) + 1;
      template.metadata.usage.lastUsed = new Date().toISOString();
      template.updatedAt = new Date().toISOString();

      // Save if it's a user template
      if (!template.isBuiltIn) {
        this.saveUserTemplates();
        this.updateTemplatesSubject();
      }
    }
  }

  private loadBuiltInTemplates(): void {
    // Load built-in templates from assets or static data
    this.builtInTemplates = this.getDefaultTemplates();
  }

  private loadUserTemplates(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        this.userTemplates = JSON.parse(stored);
      }
    } catch (error) {
      console.warn('Failed to load user templates:', error);
      this.userTemplates = [];
    }
  }

  private saveUserTemplates(): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.userTemplates));
    } catch (error) {
      console.error('Failed to save user templates:', error);
    }
  }

  private updateTemplatesSubject(): void {
    const allTemplates = [...this.builtInTemplates, ...this.userTemplates];
    this.templatesSubject.next(allTemplates);
  }

  private extractRequiredComponents(components: Record<string, BuilderComponent>): string[] {
    const componentTypes = new Set<string>();
    Object.values(components).forEach(component => {
      componentTypes.add(component.type);
    });
    return Array.from(componentTypes);
  }

  private isComponentTypeSupported(type: string): boolean {
    // This would integrate with ComponentRegistryService
    // For now, assume all types are supported
    return true;
  }

  private getDefaultTemplates(): Template[] {
    return [
      this.createDashboardTemplate(),
      this.createLandingPageTemplate(),
      this.createFormPageTemplate(),
      this.createProfilePageTemplate(),
      // Component templates
      this.createNavigationBarTemplate(),
      this.createCardLayoutTemplate(),
      this.createButtonGroupTemplate(),
      this.createFormGroupTemplate()
    ];
  }

  private createDashboardTemplate(): Template {
    const components: Record<string, BuilderComponent> = {
      'header-1': {
        id: 'header-1',
        type: 'container',
        parentId: null,
        properties: {
          tailwindClasses: 'bg-white shadow-sm border-b border-gray-200 p-4'
        },
        children: ['title-1', 'nav-1'],
        isContainer: true,
        label: 'Header Container'
      },
      'title-1': {
        id: 'title-1',
        type: 'text',
        parentId: 'header-1',
        properties: {
          tailwindClasses: 'text-2xl font-bold text-gray-900',
          content: 'Dashboard'
        },
        children: [],
        isContainer: false,
        label: 'Dashboard Title'
      },
      'nav-1': {
        id: 'nav-1',
        type: 'container',
        parentId: 'header-1',
        properties: {
          tailwindClasses: 'flex space-x-4 mt-2'
        },
        children: ['nav-item-1', 'nav-item-2', 'nav-item-3'],
        isContainer: true,
        label: 'Navigation'
      },
      'nav-item-1': {
        id: 'nav-item-1',
        type: 'button',
        parentId: 'nav-1',
        properties: {
          tailwindClasses: 'px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900',
          content: 'Overview'
        },
        children: [],
        isContainer: false,
        label: 'Nav Item 1'
      },
      'nav-item-2': {
        id: 'nav-item-2',
        type: 'button',
        parentId: 'nav-1',
        properties: {
          tailwindClasses: 'px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900',
          content: 'Analytics'
        },
        children: [],
        isContainer: false,
        label: 'Nav Item 2'
      },
      'nav-item-3': {
        id: 'nav-item-3',
        type: 'button',
        parentId: 'nav-1',
        properties: {
          tailwindClasses: 'px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900',
          content: 'Reports'
        },
        children: [],
        isContainer: false,
        label: 'Nav Item 3'
      },
      'main-content': {
        id: 'main-content',
        type: 'container',
        parentId: null,
        properties: {
          tailwindClasses: 'flex-1 p-6 bg-gray-50'
        },
        children: ['stats-grid', 'chart-section'],
        isContainer: true,
        label: 'Main Content'
      },
      'stats-grid': {
        id: 'stats-grid',
        type: 'container',
        parentId: 'main-content',
        properties: {
          tailwindClasses: 'grid grid-cols-1 md:grid-cols-3 gap-6 mb-6'
        },
        children: ['stat-card-1', 'stat-card-2', 'stat-card-3'],
        isContainer: true,
        label: 'Stats Grid'
      },
      'stat-card-1': {
        id: 'stat-card-1',
        type: 'container',
        parentId: 'stats-grid',
        properties: {
          tailwindClasses: 'bg-white p-6 rounded-lg shadow'
        },
        children: ['stat-title-1', 'stat-value-1'],
        isContainer: true,
        label: 'Stat Card 1'
      },
      'stat-title-1': {
        id: 'stat-title-1',
        type: 'text',
        parentId: 'stat-card-1',
        properties: {
          tailwindClasses: 'text-sm font-medium text-gray-500',
          content: 'Total Users'
        },
        children: [],
        isContainer: false,
        label: 'Stat Title 1'
      },
      'stat-value-1': {
        id: 'stat-value-1',
        type: 'text',
        parentId: 'stat-card-1',
        properties: {
          tailwindClasses: 'text-3xl font-bold text-gray-900 mt-2',
          content: '12,345'
        },
        children: [],
        isContainer: false,
        label: 'Stat Value 1'
      },
      'stat-card-2': {
        id: 'stat-card-2',
        type: 'container',
        parentId: 'stats-grid',
        properties: {
          tailwindClasses: 'bg-white p-6 rounded-lg shadow'
        },
        children: ['stat-title-2', 'stat-value-2'],
        isContainer: true,
        label: 'Stat Card 2'
      },
      'stat-title-2': {
        id: 'stat-title-2',
        type: 'text',
        parentId: 'stat-card-2',
        properties: {
          tailwindClasses: 'text-sm font-medium text-gray-500',
          content: 'Revenue'
        },
        children: [],
        isContainer: false,
        label: 'Stat Title 2'
      },
      'stat-value-2': {
        id: 'stat-value-2',
        type: 'text',
        parentId: 'stat-card-2',
        properties: {
          tailwindClasses: 'text-3xl font-bold text-gray-900 mt-2',
          content: '$98,765'
        },
        children: [],
        isContainer: false,
        label: 'Stat Value 2'
      },
      'stat-card-3': {
        id: 'stat-card-3',
        type: 'container',
        parentId: 'stats-grid',
        properties: {
          tailwindClasses: 'bg-white p-6 rounded-lg shadow'
        },
        children: ['stat-title-3', 'stat-value-3'],
        isContainer: true,
        label: 'Stat Card 3'
      },
      'stat-title-3': {
        id: 'stat-title-3',
        type: 'text',
        parentId: 'stat-card-3',
        properties: {
          tailwindClasses: 'text-sm font-medium text-gray-500',
          content: 'Growth'
        },
        children: [],
        isContainer: false,
        label: 'Stat Title 3'
      },
      'stat-value-3': {
        id: 'stat-value-3',
        type: 'text',
        parentId: 'stat-card-3',
        properties: {
          tailwindClasses: 'text-3xl font-bold text-green-600 mt-2',
          content: '+23.5%'
        },
        children: [],
        isContainer: false,
        label: 'Stat Value 3'
      },
      'chart-section': {
        id: 'chart-section',
        type: 'container',
        parentId: 'main-content',
        properties: {
          tailwindClasses: 'bg-white p-6 rounded-lg shadow'
        },
        children: ['chart-title', 'chart-placeholder'],
        isContainer: true,
        label: 'Chart Section'
      },
      'chart-title': {
        id: 'chart-title',
        type: 'text',
        parentId: 'chart-section',
        properties: {
          tailwindClasses: 'text-lg font-semibold text-gray-900 mb-4',
          content: 'Analytics Overview'
        },
        children: [],
        isContainer: false,
        label: 'Chart Title'
      },
      'chart-placeholder': {
        id: 'chart-placeholder',
        type: 'container',
        parentId: 'chart-section',
        properties: {
          tailwindClasses: 'h-64 bg-gray-100 rounded flex items-center justify-center'
        },
        children: ['chart-text'],
        isContainer: true,
        label: 'Chart Placeholder'
      },
      'chart-text': {
        id: 'chart-text',
        type: 'text',
        parentId: 'chart-placeholder',
        properties: {
          tailwindClasses: 'text-gray-500',
          content: 'Chart Component Here'
        },
        children: [],
        isContainer: false,
        label: 'Chart Text'
      }
    };

    return {
      id: 'dashboard-template',
      name: 'Dashboard Layout',
      description: 'A complete dashboard layout with header, navigation, stats cards, and chart section',
      category: TemplateCategory.PAGE,
      tags: ['dashboard', 'admin', 'analytics', 'stats'],
      thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxyZWN0IHg9IjEwIiB5PSIxMCIgd2lkdGg9IjE4MCIgaGVpZ2h0PSIzMCIgZmlsbD0iI0ZGRkZGRiIvPgo8cmVjdCB4PSIxMCIgeT0iNTAiIHdpZHRoPSI1NSIgaGVpZ2h0PSI0MCIgZmlsbD0iI0ZGRkZGRiIvPgo8cmVjdCB4PSI3NSIgeT0iNTAiIHdpZHRoPSI1NSIgaGVpZ2h0PSI0MCIgZmlsbD0iI0ZGRkZGRiIvPgo8cmVjdCB4PSIxNDAiIHk9IjUwIiB3aWR0aD0iNTAiIGhlaWdodD0iNDAiIGZpbGw9IiNGRkZGRkYiLz4KPHJlY3QgeD0iMTAiIHk9IjEwMCIgd2lkdGg9IjE4MCIgaGVpZ2h0PSI0MCIgZmlsbD0iI0ZGRkZGRiIvPgo8L3N2Zz4K',
      components,
      pageConfig: {
        id: 'dashboard-page',
        name: 'Dashboard',
        components: ['header-1', 'main-content'],
        type: 'builder',
        layout: 'fluid',
        properties: {
          tailwindClasses: 'min-h-screen bg-gray-50 flex flex-col'
        }
      },
      metadata: {
        author: 'LP-Go Team',
        version: '1.0.0',
        requiredComponents: ['container', 'text', 'button'],
        difficulty: 'beginner',
        estimatedTime: '5 minutes'
      },
      createdAt: '2025-01-05T00:00:00.000Z',
      updatedAt: '2025-01-05T00:00:00.000Z',
      isBuiltIn: true,
      isFavorite: false
    };
  }

  private createLandingPageTemplate(): Template {
    const components: Record<string, BuilderComponent> = {
      'hero-section': {
        id: 'hero-section',
        type: 'container',
        parentId: null,
        properties: {
          tailwindClasses: 'bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20'
        },
        children: ['hero-content'],
        isContainer: true,
        label: 'Hero Section'
      },
      'hero-content': {
        id: 'hero-content',
        type: 'container',
        parentId: 'hero-section',
        properties: {
          tailwindClasses: 'max-w-4xl mx-auto text-center px-4'
        },
        children: ['hero-title', 'hero-subtitle', 'hero-cta'],
        isContainer: true,
        label: 'Hero Content'
      },
      'hero-title': {
        id: 'hero-title',
        type: 'text',
        parentId: 'hero-content',
        properties: {
          tailwindClasses: 'text-5xl font-bold mb-6',
          content: 'Build Amazing Apps Fast'
        },
        children: [],
        isContainer: false,
        label: 'Hero Title'
      },
      'hero-subtitle': {
        id: 'hero-subtitle',
        type: 'text',
        parentId: 'hero-content',
        properties: {
          tailwindClasses: 'text-xl mb-8 opacity-90',
          content: 'Create beautiful, responsive applications with our drag-and-drop builder'
        },
        children: [],
        isContainer: false,
        label: 'Hero Subtitle'
      },
      'hero-cta': {
        id: 'hero-cta',
        type: 'button',
        parentId: 'hero-content',
        properties: {
          tailwindClasses: 'bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors',
          content: 'Get Started Free'
        },
        children: [],
        isContainer: false,
        label: 'Hero CTA'
      },
      'features-section': {
        id: 'features-section',
        type: 'container',
        parentId: null,
        properties: {
          tailwindClasses: 'py-16 bg-white'
        },
        children: ['features-content'],
        isContainer: true,
        label: 'Features Section'
      },
      'features-content': {
        id: 'features-content',
        type: 'container',
        parentId: 'features-section',
        properties: {
          tailwindClasses: 'max-w-6xl mx-auto px-4'
        },
        children: ['features-title', 'features-grid'],
        isContainer: true,
        label: 'Features Content'
      },
      'features-title': {
        id: 'features-title',
        type: 'text',
        parentId: 'features-content',
        properties: {
          tailwindClasses: 'text-3xl font-bold text-center mb-12 text-gray-900',
          content: 'Why Choose Our Platform?'
        },
        children: [],
        isContainer: false,
        label: 'Features Title'
      },
      'features-grid': {
        id: 'features-grid',
        type: 'container',
        parentId: 'features-content',
        properties: {
          tailwindClasses: 'grid grid-cols-1 md:grid-cols-3 gap-8'
        },
        children: ['feature-1', 'feature-2', 'feature-3'],
        isContainer: true,
        label: 'Features Grid'
      },
      'feature-1': {
        id: 'feature-1',
        type: 'container',
        parentId: 'features-grid',
        properties: {
          tailwindClasses: 'text-center'
        },
        children: ['feature-1-title', 'feature-1-desc'],
        isContainer: true,
        label: 'Feature 1'
      },
      'feature-1-title': {
        id: 'feature-1-title',
        type: 'text',
        parentId: 'feature-1',
        properties: {
          tailwindClasses: 'text-xl font-semibold mb-4 text-gray-900',
          content: 'Easy to Use'
        },
        children: [],
        isContainer: false,
        label: 'Feature 1 Title'
      },
      'feature-1-desc': {
        id: 'feature-1-desc',
        type: 'text',
        parentId: 'feature-1',
        properties: {
          tailwindClasses: 'text-gray-600',
          content: 'Intuitive drag-and-drop interface makes building apps simple and fun.'
        },
        children: [],
        isContainer: false,
        label: 'Feature 1 Description'
      },
      'feature-2': {
        id: 'feature-2',
        type: 'container',
        parentId: 'features-grid',
        properties: {
          tailwindClasses: 'text-center'
        },
        children: ['feature-2-title', 'feature-2-desc'],
        isContainer: true,
        label: 'Feature 2'
      },
      'feature-2-title': {
        id: 'feature-2-title',
        type: 'text',
        parentId: 'feature-2',
        properties: {
          tailwindClasses: 'text-xl font-semibold mb-4 text-gray-900',
          content: 'Responsive Design'
        },
        children: [],
        isContainer: false,
        label: 'Feature 2 Title'
      },
      'feature-2-desc': {
        id: 'feature-2-desc',
        type: 'text',
        parentId: 'feature-2',
        properties: {
          tailwindClasses: 'text-gray-600',
          content: 'Your apps look great on all devices with automatic responsive layouts.'
        },
        children: [],
        isContainer: false,
        label: 'Feature 2 Description'
      },
      'feature-3': {
        id: 'feature-3',
        type: 'container',
        parentId: 'features-grid',
        properties: {
          tailwindClasses: 'text-center'
        },
        children: ['feature-3-title', 'feature-3-desc'],
        isContainer: true,
        label: 'Feature 3'
      },
      'feature-3-title': {
        id: 'feature-3-title',
        type: 'text',
        parentId: 'feature-3',
        properties: {
          tailwindClasses: 'text-xl font-semibold mb-4 text-gray-900',
          content: 'Fast Performance'
        },
        children: [],
        isContainer: false,
        label: 'Feature 3 Title'
      },
      'feature-3-desc': {
        id: 'feature-3-desc',
        type: 'text',
        parentId: 'feature-3',
        properties: {
          tailwindClasses: 'text-gray-600',
          content: 'Optimized code generation ensures your apps run smoothly and efficiently.'
        },
        children: [],
        isContainer: false,
        label: 'Feature 3 Description'
      }
    };

    return {
      id: 'landing-page-template',
      name: 'Landing Page',
      description: 'A modern landing page with hero section, features, and call-to-action',
      category: TemplateCategory.PAGE,
      tags: ['landing', 'marketing', 'hero', 'features'],
      thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iNjAiIGZpbGw9IiMzQjgyRjYiLz4KPHJlY3QgeD0iMTAiIHk9IjcwIiB3aWR0aD0iNTUiIGhlaWdodD0iNzAiIGZpbGw9IiNGRkZGRkYiLz4KPHJlY3QgeD0iNzUiIHk9IjcwIiB3aWR0aD0iNTUiIGhlaWdodD0iNzAiIGZpbGw9IiNGRkZGRkYiLz4KPHJlY3QgeD0iMTQwIiB5PSI3MCIgd2lkdGg9IjUwIiBoZWlnaHQ9IjcwIiBmaWxsPSIjRkZGRkZGIi8+Cjwvc3ZnPgo=',
      components,
      pageConfig: {
        id: 'landing-page',
        name: 'Landing Page',
        components: ['hero-section', 'features-section'],
        type: 'builder',
        layout: 'fluid',
        properties: {
          tailwindClasses: 'min-h-screen bg-gray-50'
        }
      },
      metadata: {
        author: 'LP-Go Team',
        version: '1.0.0',
        requiredComponents: ['container', 'text', 'button'],
        difficulty: 'beginner',
        estimatedTime: '10 minutes'
      },
      createdAt: '2025-01-05T00:00:00.000Z',
      updatedAt: '2025-01-05T00:00:00.000Z',
      isBuiltIn: true,
      isFavorite: false
    };
  }

  private createFormPageTemplate(): Template {
    const components: Record<string, BuilderComponent> = {
      'form-container': {
        id: 'form-container',
        type: 'container',
        parentId: null,
        properties: {
          tailwindClasses: 'min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4'
        },
        children: ['form-card'],
        isContainer: true,
        label: 'Form Container'
      },
      'form-card': {
        id: 'form-card',
        type: 'container',
        parentId: 'form-container',
        properties: {
          tailwindClasses: 'max-w-md w-full bg-white rounded-lg shadow-md p-8'
        },
        children: ['form-title', 'form-subtitle', 'form-fields', 'form-submit'],
        isContainer: true,
        label: 'Form Card'
      },
      'form-title': {
        id: 'form-title',
        type: 'text',
        parentId: 'form-card',
        properties: {
          tailwindClasses: 'text-2xl font-bold text-center text-gray-900 mb-2',
          content: 'Contact Us'
        },
        children: [],
        isContainer: false,
        label: 'Form Title'
      },
      'form-subtitle': {
        id: 'form-subtitle',
        type: 'text',
        parentId: 'form-card',
        properties: {
          tailwindClasses: 'text-center text-gray-600 mb-8',
          content: 'We\'d love to hear from you. Send us a message!'
        },
        children: [],
        isContainer: false,
        label: 'Form Subtitle'
      },
      'form-fields': {
        id: 'form-fields',
        type: 'container',
        parentId: 'form-card',
        properties: {
          tailwindClasses: 'space-y-6 mb-6'
        },
        children: ['name-field', 'email-field', 'message-field'],
        isContainer: true,
        label: 'Form Fields'
      },
      'name-field': {
        id: 'name-field',
        type: 'input',
        parentId: 'form-fields',
        properties: {
          tailwindClasses: 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
          placeholder: 'Your Name',
          type: 'text'
        },
        children: [],
        isContainer: false,
        label: 'Name Field'
      },
      'email-field': {
        id: 'email-field',
        type: 'input',
        parentId: 'form-fields',
        properties: {
          tailwindClasses: 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
          placeholder: 'Your Email',
          type: 'email'
        },
        children: [],
        isContainer: false,
        label: 'Email Field'
      },
      'message-field': {
        id: 'message-field',
        type: 'textarea',
        parentId: 'form-fields',
        properties: {
          tailwindClasses: 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 h-32 resize-none',
          placeholder: 'Your Message'
        },
        children: [],
        isContainer: false,
        label: 'Message Field'
      },
      'form-submit': {
        id: 'form-submit',
        type: 'button',
        parentId: 'form-card',
        properties: {
          tailwindClasses: 'w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors font-medium',
          content: 'Send Message'
        },
        children: [],
        isContainer: false,
        label: 'Submit Button'
      }
    };

    return {
      id: 'form-page-template',
      name: 'Contact Form',
      description: 'A clean contact form page with validation and responsive design',
      category: TemplateCategory.FORM,
      tags: ['form', 'contact', 'input', 'validation'],
      thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxyZWN0IHg9IjUwIiB5PSIzMCIgd2lkdGg9IjEwMCIgaGVpZ2h0PSI5MCIgZmlsbD0iI0ZGRkZGRiIgcng9IjQiLz4KPHJlY3QgeD0iNjAiIHk9IjUwIiB3aWR0aD0iODAiIGhlaWdodD0iMTAiIGZpbGw9IiNFNUU3RUIiIHJ4PSIyIi8+CjxyZWN0IHg9IjYwIiB5PSI3MCIgd2lkdGg9IjgwIiBoZWlnaHQ9IjEwIiBmaWxsPSIjRTVFN0VCIiByeD0iMiIvPgo8cmVjdCB4PSI2MCIgeT0iOTAiIHdpZHRoPSI4MCIgaGVpZ2h0PSIyMCIgZmlsbD0iIzM3OEJGNiIgcng9IjIiLz4KPC9zdmc+Cg==',
      components,
      pageConfig: {
        id: 'form-page',
        name: 'Contact Form',
        components: ['form-container'],
        type: 'builder',
        layout: 'fluid',
        properties: {
          tailwindClasses: 'min-h-screen bg-gray-50'
        }
      },
      metadata: {
        author: 'LP-Go Team',
        version: '1.0.0',
        requiredComponents: ['container', 'text', 'input', 'textarea', 'button'],
        difficulty: 'beginner',
        estimatedTime: '8 minutes'
      },
      createdAt: '2025-01-05T00:00:00.000Z',
      updatedAt: '2025-01-05T00:00:00.000Z',
      isBuiltIn: true,
      isFavorite: false
    };
  }

  private createProfilePageTemplate(): Template {
    const components: Record<string, BuilderComponent> = {
      'profile-container': {
        id: 'profile-container',
        type: 'container',
        parentId: null,
        properties: {
          tailwindClasses: 'min-h-screen bg-gray-50 py-8'
        },
        children: ['profile-content'],
        isContainer: true,
        label: 'Profile Container'
      },
      'profile-content': {
        id: 'profile-content',
        type: 'container',
        parentId: 'profile-container',
        properties: {
          tailwindClasses: 'max-w-4xl mx-auto px-4'
        },
        children: ['profile-header', 'profile-body'],
        isContainer: true,
        label: 'Profile Content'
      },
      'profile-header': {
        id: 'profile-header',
        type: 'container',
        parentId: 'profile-content',
        properties: {
          tailwindClasses: 'bg-white rounded-lg shadow p-6 mb-6'
        },
        children: ['profile-avatar', 'profile-info'],
        isContainer: true,
        label: 'Profile Header'
      },
      'profile-avatar': {
        id: 'profile-avatar',
        type: 'container',
        parentId: 'profile-header',
        properties: {
          tailwindClasses: 'w-24 h-24 bg-gray-300 rounded-full mx-auto mb-4 flex items-center justify-center'
        },
        children: ['avatar-text'],
        isContainer: true,
        label: 'Profile Avatar'
      },
      'avatar-text': {
        id: 'avatar-text',
        type: 'text',
        parentId: 'profile-avatar',
        properties: {
          tailwindClasses: 'text-2xl font-bold text-gray-600',
          content: 'JD'
        },
        children: [],
        isContainer: false,
        label: 'Avatar Text'
      },
      'profile-info': {
        id: 'profile-info',
        type: 'container',
        parentId: 'profile-header',
        properties: {
          tailwindClasses: 'text-center'
        },
        children: ['profile-name', 'profile-email', 'profile-role'],
        isContainer: true,
        label: 'Profile Info'
      },
      'profile-name': {
        id: 'profile-name',
        type: 'text',
        parentId: 'profile-info',
        properties: {
          tailwindClasses: 'text-2xl font-bold text-gray-900 mb-2',
          content: 'John Doe'
        },
        children: [],
        isContainer: false,
        label: 'Profile Name'
      },
      'profile-email': {
        id: 'profile-email',
        type: 'text',
        parentId: 'profile-info',
        properties: {
          tailwindClasses: 'text-gray-600 mb-2',
          content: '<EMAIL>'
        },
        children: [],
        isContainer: false,
        label: 'Profile Email'
      },
      'profile-role': {
        id: 'profile-role',
        type: 'text',
        parentId: 'profile-info',
        properties: {
          tailwindClasses: 'text-blue-600 font-medium',
          content: 'Senior Developer'
        },
        children: [],
        isContainer: false,
        label: 'Profile Role'
      },
      'profile-body': {
        id: 'profile-body',
        type: 'container',
        parentId: 'profile-content',
        properties: {
          tailwindClasses: 'grid grid-cols-1 md:grid-cols-2 gap-6'
        },
        children: ['personal-info', 'preferences'],
        isContainer: true,
        label: 'Profile Body'
      },
      'personal-info': {
        id: 'personal-info',
        type: 'container',
        parentId: 'profile-body',
        properties: {
          tailwindClasses: 'bg-white rounded-lg shadow p-6'
        },
        children: ['personal-title', 'personal-fields'],
        isContainer: true,
        label: 'Personal Info'
      },
      'personal-title': {
        id: 'personal-title',
        type: 'text',
        parentId: 'personal-info',
        properties: {
          tailwindClasses: 'text-lg font-semibold text-gray-900 mb-4',
          content: 'Personal Information'
        },
        children: [],
        isContainer: false,
        label: 'Personal Title'
      },
      'personal-fields': {
        id: 'personal-fields',
        type: 'container',
        parentId: 'personal-info',
        properties: {
          tailwindClasses: 'space-y-4'
        },
        children: ['phone-field', 'location-field', 'bio-field'],
        isContainer: true,
        label: 'Personal Fields'
      },
      'phone-field': {
        id: 'phone-field',
        type: 'text',
        parentId: 'personal-fields',
        properties: {
          tailwindClasses: 'text-sm text-gray-600',
          content: 'Phone: +****************'
        },
        children: [],
        isContainer: false,
        label: 'Phone Field'
      },
      'location-field': {
        id: 'location-field',
        type: 'text',
        parentId: 'personal-fields',
        properties: {
          tailwindClasses: 'text-sm text-gray-600',
          content: 'Location: San Francisco, CA'
        },
        children: [],
        isContainer: false,
        label: 'Location Field'
      },
      'bio-field': {
        id: 'bio-field',
        type: 'text',
        parentId: 'personal-fields',
        properties: {
          tailwindClasses: 'text-sm text-gray-600',
          content: 'Bio: Passionate developer with 5+ years of experience in web technologies.'
        },
        children: [],
        isContainer: false,
        label: 'Bio Field'
      },
      'preferences': {
        id: 'preferences',
        type: 'container',
        parentId: 'profile-body',
        properties: {
          tailwindClasses: 'bg-white rounded-lg shadow p-6'
        },
        children: ['preferences-title', 'preferences-list'],
        isContainer: true,
        label: 'Preferences'
      },
      'preferences-title': {
        id: 'preferences-title',
        type: 'text',
        parentId: 'preferences',
        properties: {
          tailwindClasses: 'text-lg font-semibold text-gray-900 mb-4',
          content: 'Preferences'
        },
        children: [],
        isContainer: false,
        label: 'Preferences Title'
      },
      'preferences-list': {
        id: 'preferences-list',
        type: 'container',
        parentId: 'preferences',
        properties: {
          tailwindClasses: 'space-y-3'
        },
        children: ['pref-1', 'pref-2', 'pref-3'],
        isContainer: true,
        label: 'Preferences List'
      },
      'pref-1': {
        id: 'pref-1',
        type: 'text',
        parentId: 'preferences-list',
        properties: {
          tailwindClasses: 'text-sm text-gray-600',
          content: '✓ Email notifications'
        },
        children: [],
        isContainer: false,
        label: 'Preference 1'
      },
      'pref-2': {
        id: 'pref-2',
        type: 'text',
        parentId: 'preferences-list',
        properties: {
          tailwindClasses: 'text-sm text-gray-600',
          content: '✓ Dark mode'
        },
        children: [],
        isContainer: false,
        label: 'Preference 2'
      },
      'pref-3': {
        id: 'pref-3',
        type: 'text',
        parentId: 'preferences-list',
        properties: {
          tailwindClasses: 'text-sm text-gray-600',
          content: '✗ Marketing emails'
        },
        children: [],
        isContainer: false,
        label: 'Preference 3'
      }
    };

    return {
      id: 'profile-page-template',
      name: 'User Profile',
      description: 'A comprehensive user profile page with personal information and preferences',
      category: TemplateCategory.PAGE,
      tags: ['profile', 'user', 'settings', 'personal'],
      thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxyZWN0IHg9IjEwIiB5PSIxMCIgd2lkdGg9IjE4MCIgaGVpZ2h0PSI1MCIgZmlsbD0iI0ZGRkZGRiIgcng9IjQiLz4KPGNpcmNsZSBjeD0iMTAwIiBjeT0iMzUiIHI9IjE1IiBmaWxsPSIjRTVFN0VCIi8+CjxyZWN0IHg9IjEwIiB5PSI3MCIgd2lkdGg9Ijg1IiBoZWlnaHQ9IjcwIiBmaWxsPSIjRkZGRkZGIiByeD0iNCIvPgo8cmVjdCB4PSIxMDUiIHk9IjcwIiB3aWR0aD0iODUiIGhlaWdodD0iNzAiIGZpbGw9IiNGRkZGRkYiIHJ4PSI0Ii8+Cjwvc3ZnPgo=',
      components,
      pageConfig: {
        id: 'profile-page',
        name: 'User Profile',
        components: ['profile-container'],
        type: 'builder',
        layout: 'fluid',
        properties: {
          tailwindClasses: 'min-h-screen bg-gray-50'
        }
      },
      metadata: {
        author: 'LP-Go Team',
        version: '1.0.0',
        requiredComponents: ['container', 'text'],
        difficulty: 'beginner',
        estimatedTime: '7 minutes'
      },
      createdAt: '2025-01-05T00:00:00.000Z',
      updatedAt: '2025-01-05T00:00:00.000Z',
      isBuiltIn: true,
      isFavorite: false
    };
  }

  private createNavigationBarTemplate(): Template {
    const components: Record<string, BuilderComponent> = {
      'nav-container': {
        id: 'nav-container',
        type: 'container',
        parentId: null,
        properties: {
          tailwindClasses: 'bg-white shadow-sm border-b border-gray-200 px-4 py-3'
        },
        children: ['nav-content'],
        isContainer: true,
        label: 'Navigation Container'
      },
      'nav-content': {
        id: 'nav-content',
        type: 'container',
        parentId: 'nav-container',
        properties: {
          tailwindClasses: 'max-w-7xl mx-auto flex justify-between items-center'
        },
        children: ['nav-brand', 'nav-menu'],
        isContainer: true,
        label: 'Navigation Content'
      },
      'nav-brand': {
        id: 'nav-brand',
        type: 'text',
        parentId: 'nav-content',
        properties: {
          tailwindClasses: 'text-xl font-bold text-gray-900',
          content: 'Brand Name'
        },
        children: [],
        isContainer: false,
        label: 'Brand'
      },
      'nav-menu': {
        id: 'nav-menu',
        type: 'container',
        parentId: 'nav-content',
        properties: {
          tailwindClasses: 'flex space-x-6'
        },
        children: ['nav-link-1', 'nav-link-2', 'nav-link-3'],
        isContainer: true,
        label: 'Navigation Menu'
      },
      'nav-link-1': {
        id: 'nav-link-1',
        type: 'button',
        parentId: 'nav-menu',
        properties: {
          tailwindClasses: 'text-gray-700 hover:text-gray-900 px-3 py-2 text-sm font-medium',
          content: 'Home'
        },
        children: [],
        isContainer: false,
        label: 'Nav Link 1'
      },
      'nav-link-2': {
        id: 'nav-link-2',
        type: 'button',
        parentId: 'nav-menu',
        properties: {
          tailwindClasses: 'text-gray-700 hover:text-gray-900 px-3 py-2 text-sm font-medium',
          content: 'About'
        },
        children: [],
        isContainer: false,
        label: 'Nav Link 2'
      },
      'nav-link-3': {
        id: 'nav-link-3',
        type: 'button',
        parentId: 'nav-menu',
        properties: {
          tailwindClasses: 'text-gray-700 hover:text-gray-900 px-3 py-2 text-sm font-medium',
          content: 'Contact'
        },
        children: [],
        isContainer: false,
        label: 'Nav Link 3'
      }
    };

    return {
      id: 'navigation-bar-template',
      name: 'Navigation Bar',
      description: 'A responsive navigation bar with brand and menu items',
      category: TemplateCategory.COMPONENT,
      tags: ['navigation', 'header', 'menu', 'responsive'],
      thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iNDAiIGZpbGw9IiNGRkZGRkYiLz4KPHJlY3QgeD0iMTAiIHk9IjEwIiB3aWR0aD0iNjAiIGhlaWdodD0iMjAiIGZpbGw9IiMzNzRGNjgiLz4KPHJlY3QgeD0iMTMwIiB5PSIxMCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiBmaWxsPSIjOUNBM0FGIi8+CjxyZWN0IHg9IjE2MCIgeT0iMTAiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K',
      components,
      pageConfig: {
        id: 'navigation-bar',
        name: 'Navigation Bar',
        components: ['nav-container'],
        type: 'builder',
        layout: 'fluid',
        properties: {
          tailwindClasses: 'w-full'
        }
      },
      metadata: {
        author: 'LP-Go Team',
        version: '1.0.0',
        requiredComponents: ['container', 'text', 'button'],
        difficulty: 'beginner',
        estimatedTime: '3 minutes'
      },
      createdAt: '2025-01-05T00:00:00.000Z',
      updatedAt: '2025-01-05T00:00:00.000Z',
      isBuiltIn: true,
      isFavorite: false
    };
  }

  private createCardLayoutTemplate(): Template {
    const components: Record<string, BuilderComponent> = {
      'card-container': {
        id: 'card-container',
        type: 'container',
        parentId: null,
        properties: {
          tailwindClasses: 'bg-white rounded-lg shadow-md overflow-hidden max-w-sm'
        },
        children: ['card-image', 'card-content'],
        isContainer: true,
        label: 'Card Container'
      },
      'card-image': {
        id: 'card-image',
        type: 'container',
        parentId: 'card-container',
        properties: {
          tailwindClasses: 'h-48 bg-gray-300 flex items-center justify-center'
        },
        children: ['image-placeholder'],
        isContainer: true,
        label: 'Card Image'
      },
      'image-placeholder': {
        id: 'image-placeholder',
        type: 'text',
        parentId: 'card-image',
        properties: {
          tailwindClasses: 'text-gray-500',
          content: 'Image'
        },
        children: [],
        isContainer: false,
        label: 'Image Placeholder'
      },
      'card-content': {
        id: 'card-content',
        type: 'container',
        parentId: 'card-container',
        properties: {
          tailwindClasses: 'p-6'
        },
        children: ['card-title', 'card-description', 'card-action'],
        isContainer: true,
        label: 'Card Content'
      },
      'card-title': {
        id: 'card-title',
        type: 'text',
        parentId: 'card-content',
        properties: {
          tailwindClasses: 'text-xl font-semibold text-gray-900 mb-2',
          content: 'Card Title'
        },
        children: [],
        isContainer: false,
        label: 'Card Title'
      },
      'card-description': {
        id: 'card-description',
        type: 'text',
        parentId: 'card-content',
        properties: {
          tailwindClasses: 'text-gray-600 mb-4',
          content: 'This is a description of the card content. It provides more details about the item.'
        },
        children: [],
        isContainer: false,
        label: 'Card Description'
      },
      'card-action': {
        id: 'card-action',
        type: 'button',
        parentId: 'card-content',
        properties: {
          tailwindClasses: 'bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors',
          content: 'Learn More'
        },
        children: [],
        isContainer: false,
        label: 'Card Action'
      }
    };

    return {
      id: 'card-layout-template',
      name: 'Card Layout',
      description: 'A versatile card component with image, title, description, and action button',
      category: TemplateCategory.COMPONENT,
      tags: ['card', 'layout', 'content', 'image'],
      thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxyZWN0IHg9IjUwIiB5PSIyMCIgd2lkdGg9IjEwMCIgaGVpZ2h0PSIxMTAiIGZpbGw9IiNGRkZGRkYiIHJ4PSI0Ii8+CjxyZWN0IHg9IjUwIiB5PSIyMCIgd2lkdGg9IjEwMCIgaGVpZ2h0PSI2MCIgZmlsbD0iI0U1RTdFQiIgcng9IjQiLz4KPHJlY3QgeD0iNjAiIHk9IjkwIiB3aWR0aD0iNjAiIGhlaWdodD0iMTAiIGZpbGw9IiMzNzRGNjgiLz4KPHJlY3QgeD0iNjAiIHk9IjEwNSIgd2lkdGg9IjgwIiBoZWlnaHQ9IjgiIGZpbGw9IiM5Q0EzQUYiLz4KPHJlY3QgeD0iNjAiIHk9IjEyMCIgd2lkdGg9IjMwIiBoZWlnaHQ9IjEwIiBmaWxsPSIjMzc4QkY2IiByeD0iMiIvPgo8L3N2Zz4K',
      components,
      pageConfig: {
        id: 'card-layout',
        name: 'Card Layout',
        components: ['card-container'],
        type: 'builder',
        layout: 'fluid',
        properties: {
          tailwindClasses: 'inline-block'
        }
      },
      metadata: {
        author: 'LP-Go Team',
        version: '1.0.0',
        requiredComponents: ['container', 'text', 'button'],
        difficulty: 'beginner',
        estimatedTime: '4 minutes'
      },
      createdAt: '2025-01-05T00:00:00.000Z',
      updatedAt: '2025-01-05T00:00:00.000Z',
      isBuiltIn: true,
      isFavorite: false
    };
  }

  private createButtonGroupTemplate(): Template {
    const components: Record<string, BuilderComponent> = {
      'button-group': {
        id: 'button-group',
        type: 'container',
        parentId: null,
        properties: {
          tailwindClasses: 'flex space-x-2'
        },
        children: ['btn-primary', 'btn-secondary', 'btn-outline'],
        isContainer: true,
        label: 'Button Group'
      },
      'btn-primary': {
        id: 'btn-primary',
        type: 'button',
        parentId: 'button-group',
        properties: {
          tailwindClasses: 'bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors',
          content: 'Primary'
        },
        children: [],
        isContainer: false,
        label: 'Primary Button'
      },
      'btn-secondary': {
        id: 'btn-secondary',
        type: 'button',
        parentId: 'button-group',
        properties: {
          tailwindClasses: 'bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors',
          content: 'Secondary'
        },
        children: [],
        isContainer: false,
        label: 'Secondary Button'
      },
      'btn-outline': {
        id: 'btn-outline',
        type: 'button',
        parentId: 'button-group',
        properties: {
          tailwindClasses: 'border border-blue-600 text-blue-600 px-4 py-2 rounded hover:bg-blue-50 transition-colors',
          content: 'Outline'
        },
        children: [],
        isContainer: false,
        label: 'Outline Button'
      }
    };

    return {
      id: 'button-group-template',
      name: 'Button Group',
      description: 'A set of styled buttons with different variants',
      category: TemplateCategory.COMPONENT,
      tags: ['buttons', 'actions', 'group', 'ui'],
      thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxyZWN0IHg9IjIwIiB5PSI2MCIgd2lkdGg9IjQ1IiBoZWlnaHQ9IjMwIiBmaWxsPSIjMzc4QkY2IiByeD0iNCIvPgo8cmVjdCB4PSI3NSIgeT0iNjAiIHdpZHRoPSI0NSIgaGVpZ2h0PSIzMCIgZmlsbD0iIzZCNzI4MCIgcng9IjQiLz4KPHJlY3QgeD0iMTMwIiB5PSI2MCIgd2lkdGg9IjQ1IiBoZWlnaHQ9IjMwIiBmaWxsPSJ0cmFuc3BhcmVudCIgc3Ryb2tlPSIjMzc4QkY2IiBzdHJva2Utd2lkdGg9IjIiIHJ4PSI0Ii8+Cjwvc3ZnPgo=',
      components,
      pageConfig: {
        id: 'button-group',
        name: 'Button Group',
        components: ['button-group'],
        type: 'builder',
        layout: 'fluid',
        properties: {
          tailwindClasses: 'inline-block'
        }
      },
      metadata: {
        author: 'LP-Go Team',
        version: '1.0.0',
        requiredComponents: ['container', 'button'],
        difficulty: 'beginner',
        estimatedTime: '2 minutes'
      },
      createdAt: '2025-01-05T00:00:00.000Z',
      updatedAt: '2025-01-05T00:00:00.000Z',
      isBuiltIn: true,
      isFavorite: false
    };
  }

  private createFormGroupTemplate(): Template {
    const components: Record<string, BuilderComponent> = {
      'form-group': {
        id: 'form-group',
        type: 'container',
        parentId: null,
        properties: {
          tailwindClasses: 'space-y-4'
        },
        children: ['name-group', 'email-group', 'message-group'],
        isContainer: true,
        label: 'Form Group'
      },
      'name-group': {
        id: 'name-group',
        type: 'container',
        parentId: 'form-group',
        properties: {
          tailwindClasses: 'flex flex-col'
        },
        children: ['name-label', 'name-input'],
        isContainer: true,
        label: 'Name Group'
      },
      'name-label': {
        id: 'name-label',
        type: 'text',
        parentId: 'name-group',
        properties: {
          tailwindClasses: 'text-sm font-medium text-gray-700 mb-1',
          content: 'Full Name'
        },
        children: [],
        isContainer: false,
        label: 'Name Label'
      },
      'name-input': {
        id: 'name-input',
        type: 'input',
        parentId: 'name-group',
        properties: {
          tailwindClasses: 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
          placeholder: 'Enter your full name',
          type: 'text'
        },
        children: [],
        isContainer: false,
        label: 'Name Input'
      },
      'email-group': {
        id: 'email-group',
        type: 'container',
        parentId: 'form-group',
        properties: {
          tailwindClasses: 'flex flex-col'
        },
        children: ['email-label', 'email-input'],
        isContainer: true,
        label: 'Email Group'
      },
      'email-label': {
        id: 'email-label',
        type: 'text',
        parentId: 'email-group',
        properties: {
          tailwindClasses: 'text-sm font-medium text-gray-700 mb-1',
          content: 'Email Address'
        },
        children: [],
        isContainer: false,
        label: 'Email Label'
      },
      'email-input': {
        id: 'email-input',
        type: 'input',
        parentId: 'email-group',
        properties: {
          tailwindClasses: 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500',
          placeholder: 'Enter your email address',
          type: 'email'
        },
        children: [],
        isContainer: false,
        label: 'Email Input'
      },
      'message-group': {
        id: 'message-group',
        type: 'container',
        parentId: 'form-group',
        properties: {
          tailwindClasses: 'flex flex-col'
        },
        children: ['message-label', 'message-input'],
        isContainer: true,
        label: 'Message Group'
      },
      'message-label': {
        id: 'message-label',
        type: 'text',
        parentId: 'message-group',
        properties: {
          tailwindClasses: 'text-sm font-medium text-gray-700 mb-1',
          content: 'Message'
        },
        children: [],
        isContainer: false,
        label: 'Message Label'
      },
      'message-input': {
        id: 'message-input',
        type: 'textarea',
        parentId: 'message-group',
        properties: {
          tailwindClasses: 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 h-24 resize-none',
          placeholder: 'Enter your message'
        },
        children: [],
        isContainer: false,
        label: 'Message Input'
      }
    };

    return {
      id: 'form-group-template',
      name: 'Form Group',
      description: 'A complete form group with labels and inputs for name, email, and message',
      category: TemplateCategory.COMPONENT,
      tags: ['form', 'inputs', 'labels', 'contact'],
      thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxyZWN0IHg9IjMwIiB5PSIyMCIgd2lkdGg9IjE0MCIgaGVpZ2h0PSIxMTAiIGZpbGw9IiNGRkZGRkYiIHJ4PSI0Ii8+CjxyZWN0IHg9IjQwIiB5PSIzNSIgd2lkdGg9IjEyMCIgaGVpZ2h0PSIxMCIgZmlsbD0iI0U1RTdFQiIgcng9IjIiLz4KPHJlY3QgeD0iNDAiIHk9IjU1IiB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEwIiBmaWxsPSIjRTVFN0VCIiByeD0iMiIvPgo8cmVjdCB4PSI0MCIgeT0iNzUiIHdpZHRoPSIxMjAiIGhlaWdodD0iMzAiIGZpbGw9IiNFNUU3RUIiIHJ4PSIyIi8+Cjwvc3ZnPgo=',
      components,
      pageConfig: {
        id: 'form-group',
        name: 'Form Group',
        components: ['form-group'],
        type: 'builder',
        layout: 'fluid',
        properties: {
          tailwindClasses: 'w-full max-w-md'
        }
      },
      metadata: {
        author: 'LP-Go Team',
        version: '1.0.0',
        requiredComponents: ['container', 'text', 'input', 'textarea'],
        difficulty: 'beginner',
        estimatedTime: '5 minutes'
      },
      createdAt: '2025-01-05T00:00:00.000Z',
      updatedAt: '2025-01-05T00:00:00.000Z',
      isBuiltIn: true,
      isFavorite: false
    };
  }
}
