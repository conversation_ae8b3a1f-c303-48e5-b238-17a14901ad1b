import { Injectable, OnDestroy } from '@angular/core';
import { BehaviorSubject, Observable, Subject, fromEvent, merge, timer, combineLatest } from 'rxjs';
import { takeUntil, filter, debounceTime, distinctUntilChanged, switchMap, catchError, retry, tap, take } from 'rxjs/operators';
import { ComponentStoreService } from './component-store.service';
import { BuilderConfigService } from './builder-config.service';
import { AppConfig, PageConfig } from '../models/builder-component.interface';

export interface BridgeStatus {
  connected: boolean;
  lastSync: Date | null;
  error: string | null;
  targetUrl: string | null;
  syncInProgress: boolean;
}

export interface LiveUpdateMessage {
  type: 'config_update' | 'page_update' | 'component_update' | 'reload_request';
  configId: string;
  pageId?: string;
  componentId?: string;
  data?: any;
  timestamp: number;
}

@Injectable({
  providedIn: 'root'
})
export class BuilderBridgeService implements OnDestroy {
  private destroy$ = new Subject<void>();
  private eventSource: EventSource | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private debug = true;

  // Bridge status
  private bridgeStatus = new BehaviorSubject<BridgeStatus>({
    connected: false,
    lastSync: null,
    error: null,
    targetUrl: null,
    syncInProgress: false
  });

  // Live update messages
  private liveUpdates = new Subject<LiveUpdateMessage>();

  // Target application URLs
  private targetUrls = {
    'lp-client-test': 'http://localhost:8101',
    'lp-client': 'http://localhost:8100'
  };

  constructor(
    private componentStore: ComponentStoreService,
    private configService: BuilderConfigService
  ) {
    this.initializeBridge();
    this.setupChangeListeners();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.disconnectEventSource();
  }

  /**
   * Get bridge status as observable
   */
  getBridgeStatus(): Observable<BridgeStatus> {
    return this.bridgeStatus.asObservable();
  }

  /**
   * Get live updates stream
   */
  getLiveUpdates(): Observable<LiveUpdateMessage> {
    return this.liveUpdates.asObservable();
  }

  /**
   * Connect to target application
   */
  connectToApplication(targetApp: 'lp-client-test' | 'lp-client'): Observable<boolean> {
    const targetUrl = this.targetUrls[targetApp];
    
    if (this.debug) {
      console.log(`[BuilderBridge] Connecting to ${targetApp} at ${targetUrl}`);
    }

    this.updateStatus({ 
      targetUrl,
      syncInProgress: true,
      error: null 
    });

    return this.establishConnection(targetUrl).pipe(
      tap(connected => {
        if (connected) {
          this.updateStatus({ 
            connected: true,
            syncInProgress: false,
            lastSync: new Date()
          });
          
          // Send initial sync
          this.sendFullSync();
        } else {
          this.updateStatus({ 
            connected: false,
            syncInProgress: false,
            error: 'Failed to establish connection'
          });
        }
      }),
      catchError(error => {
        this.updateStatus({ 
          connected: false,
          syncInProgress: false,
          error: error.message 
        });
        throw error;
      })
    );
  }

  /**
   * Disconnect from target application
   */
  disconnect(): void {
    if (this.debug) {
      console.log('[BuilderBridge] Disconnecting from target application');
    }

    this.disconnectEventSource();
    this.updateStatus({ 
      connected: false,
      targetUrl: null,
      error: null,
      syncInProgress: false
    });
  }

  /**
   * Send live reload request to target application
   */
  requestLiveReload(): Observable<boolean> {
    return this.configService.getCurrentAppConfig().pipe(
      take(1),
      switchMap(currentConfig => {
        if (!currentConfig) {
          throw new Error('No configuration loaded');
        }

        const message: LiveUpdateMessage = {
          type: 'reload_request',
          configId: currentConfig.id,
          timestamp: Date.now()
        };

        return this.sendMessage(message);
      })
    );
  }

  /**
   * Force full synchronization
   */
  forceSyncToApplication(): Observable<boolean> {
    if (this.debug) {
      console.log('[BuilderBridge] Forcing full sync to target application');
    }

    this.updateStatus({ syncInProgress: true });

    return this.sendFullSync().pipe(
      tap(() => {
        this.updateStatus({ 
          syncInProgress: false,
          lastSync: new Date()
        });
      }),
      catchError(error => {
        this.updateStatus({ 
          syncInProgress: false,
          error: error.message 
        });
        throw error;
      })
    );
  }

  /**
   * Initialize bridge service
   */
  private initializeBridge(): void {
    if (this.debug) {
      console.log('[BuilderBridge] Initializing builder bridge service');
    }

    // Auto-connect to lp-client-test if running locally
    if (window.location.hostname === 'localhost') {
      setTimeout(() => {
        this.connectToApplication('lp-client-test').subscribe({
          next: (connected) => {
            if (this.debug) {
              console.log(`[BuilderBridge] Auto-connection result: ${connected}`);
            }
          },
          error: (error) => {
            console.warn('[BuilderBridge] Auto-connection failed:', error);
          }
        });
      }, 2000); // Wait 2 seconds for builder to initialize
    }
  }

  /**
   * Setup change listeners for automatic sync
   */
  private setupChangeListeners(): void {
    // Listen for component changes
    this.componentStore.getAllComponents().pipe(
      takeUntil(this.destroy$),
      debounceTime(1000), // Wait 1 second after last change
      distinctUntilChanged(),
      filter(() => this.bridgeStatus.value.connected)
    ).subscribe(() => {
      if (this.debug) {
        console.log('[BuilderBridge] Components changed, sending update');
      }
      this.sendComponentUpdate();
    });

    // Listen for page changes
    this.configService.getCurrentPage().pipe(
      takeUntil(this.destroy$),
      filter(page => !!page),
      distinctUntilChanged((prev, curr) => prev?.id === curr?.id),
      filter(() => this.bridgeStatus.value.connected)
    ).subscribe((page) => {
      if (this.debug) {
        console.log('[BuilderBridge] Page changed, sending update:', page?.id);
      }
      this.sendPageUpdate(page!);
    });

    // Listen for config changes
    this.configService.getCurrentAppConfig().pipe(
      takeUntil(this.destroy$),
      filter(config => !!config),
      distinctUntilChanged((prev, curr) => prev?.id === curr?.id),
      filter(() => this.bridgeStatus.value.connected)
    ).subscribe((config) => {
      if (this.debug) {
        console.log('[BuilderBridge] Config changed, sending update:', config?.id);
      }
      this.sendConfigUpdate(config!);
    });
  }

  /**
   * Establish connection to target application
   */
  private establishConnection(targetUrl: string): Observable<boolean> {
    return new Observable(observer => {
      try {
        // First, check if target application is reachable
        fetch(`${targetUrl}/health`, { method: 'HEAD' })
          .then(() => {
            // Target is reachable, establish SSE connection
            this.connectEventSource(`${targetUrl}/api/builder/events`);
            observer.next(true);
            observer.complete();
          })
          .catch(() => {
            // Target not reachable, but we can still try to send updates
            console.warn('[BuilderBridge] Target application not reachable, operating in send-only mode');
            observer.next(true);
            observer.complete();
          });
      } catch (error) {
        observer.error(error);
      }
    });
  }

  /**
   * Connect to Server-Sent Events stream
   */
  private connectEventSource(url: string): void {
    try {
      this.eventSource = new EventSource(url);

      this.eventSource.onopen = () => {
        if (this.debug) {
          console.log('[BuilderBridge] SSE connection opened');
        }
        this.reconnectAttempts = 0;
        this.reconnectDelay = 1000;
      };

      this.eventSource.onmessage = (event) => {
        try {
          const message: LiveUpdateMessage = JSON.parse(event.data);
          this.liveUpdates.next(message);
        } catch (error) {
          console.error('[BuilderBridge] Failed to parse SSE message:', error);
        }
      };

      this.eventSource.onerror = () => {
        if (this.debug) {
          console.warn('[BuilderBridge] SSE connection error');
        }
        this.handleConnectionError();
      };
    } catch (error) {
      console.error('[BuilderBridge] Failed to create EventSource:', error);
    }
  }

  /**
   * Disconnect event source
   */
  private disconnectEventSource(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
  }

  /**
   * Handle connection errors with exponential backoff
   */
  private handleConnectionError(): void {
    this.disconnectEventSource();

    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      
      if (this.debug) {
        console.log(`[BuilderBridge] Reconnecting in ${this.reconnectDelay}ms (attempt ${this.reconnectAttempts})`);
      }

      setTimeout(() => {
        const status = this.bridgeStatus.value;
        if (status.targetUrl) {
          this.establishConnection(status.targetUrl).subscribe();
        }
      }, this.reconnectDelay);

      // Exponential backoff
      this.reconnectDelay = Math.min(this.reconnectDelay * 2, 30000);
    } else {
      this.updateStatus({ 
        connected: false,
        error: 'Max reconnection attempts reached'
      });
    }
  }

  /**
   * Send message to target application
   */
  private sendMessage(message: LiveUpdateMessage): Observable<boolean> {
    const status = this.bridgeStatus.value;
    if (!status.targetUrl) {
      throw new Error('No target application connected');
    }

    return new Observable(observer => {
      fetch(`${status.targetUrl}/api/builder/update`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(message)
      })
      .then(response => {
        if (response.ok) {
          observer.next(true);
        } else {
          observer.error(new Error(`HTTP ${response.status}: ${response.statusText}`));
        }
        observer.complete();
      })
      .catch(error => {
        // If direct API call fails, try postMessage for iframe communication
        this.sendPostMessage(message);
        observer.next(true);
        observer.complete();
      });
    });
  }

  /**
   * Send full sync to target application
   */
  private sendFullSync(): Observable<boolean> {
    return combineLatest([
      this.configService.getCurrentAppConfig(),
      this.configService.getCurrentPage()
    ]).pipe(
      take(1),
      switchMap(([currentConfig, currentPage]) => {
        if (!currentConfig) {
          throw new Error('No configuration loaded');
        }

        const message: LiveUpdateMessage = {
          type: 'config_update',
          configId: currentConfig.id,
          pageId: currentPage?.id,
          data: {
            config: currentConfig,
            page: currentPage,
            components: this.componentStore.exportState()
          },
          timestamp: Date.now()
        };

        return this.sendMessage(message);
      })
    );
  }

  /**
   * Send component update
   */
  private sendComponentUpdate(): void {
    combineLatest([
      this.configService.getCurrentAppConfig(),
      this.configService.getCurrentPage()
    ]).pipe(
      take(1)
    ).subscribe(([currentConfig, currentPage]) => {
      if (!currentConfig || !currentPage) return;

      const message: LiveUpdateMessage = {
        type: 'component_update',
        configId: currentConfig.id,
        pageId: currentPage.id,
        data: {
          components: this.componentStore.exportState()
        },
        timestamp: Date.now()
      };

      this.sendMessage(message).subscribe({
        error: (error) => {
          console.warn('[BuilderBridge] Failed to send component update:', error);
        }
      });
    });
  }

  /**
   * Send page update
   */
  private sendPageUpdate(page: PageConfig): void {
    this.configService.getCurrentAppConfig().pipe(
      take(1)
    ).subscribe(currentConfig => {
      if (!currentConfig) return;

      const message: LiveUpdateMessage = {
        type: 'page_update',
        configId: currentConfig.id,
        pageId: page.id,
        data: { page },
        timestamp: Date.now()
      };

      this.sendMessage(message).subscribe({
        error: (error) => {
          console.warn('[BuilderBridge] Failed to send page update:', error);
        }
      });
    });
  }

  /**
   * Send config update
   */
  private sendConfigUpdate(config: AppConfig): void {
    const message: LiveUpdateMessage = {
      type: 'config_update',
      configId: config.id,
      data: { config },
      timestamp: Date.now()
    };

    this.sendMessage(message).subscribe({
      error: (error) => {
        console.warn('[BuilderBridge] Failed to send config update:', error);
      }
    });
  }

  /**
   * Send message via postMessage for iframe communication
   */
  private sendPostMessage(message: LiveUpdateMessage): void {
    const targetOrigin = this.bridgeStatus.value.targetUrl || '*';
    
    // Send to all frames
    window.postMessage({
      source: 'lp-app-builder',
      ...message
    }, targetOrigin);

    // Also try to send to parent if we're in an iframe
    if (window.parent !== window) {
      window.parent.postMessage({
        source: 'lp-app-builder',
        ...message
      }, targetOrigin);
    }
  }

  /**
   * Update bridge status
   */
  private updateStatus(updates: Partial<BridgeStatus>): void {
    const currentStatus = this.bridgeStatus.value;
    this.bridgeStatus.next({
      ...currentStatus,
      ...updates
    });
  }
}
