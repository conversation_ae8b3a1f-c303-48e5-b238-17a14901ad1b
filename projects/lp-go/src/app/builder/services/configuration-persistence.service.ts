import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, of, throwError, forkJoin } from 'rxjs';
import { map, catchError, tap, switchMap, finalize } from 'rxjs/operators';

import { BuilderApiService } from './api/builder-api.service';
import { EnvironmentApiService } from './api/environment-api.service';
import { ComponentStoreService } from './component-store.service';
import { AppConfig, PageConfig, BuilderComponent } from '../models/builder-component.interface';

/**
 * Configuration persistence state
 */
export interface PersistenceState {
  isSaving: boolean;
  lastSaved: Date | null;
  hasUnsavedChanges: boolean;
  saveInProgress: boolean;
  lastError: string | null;
  autoSaveEnabled: boolean;
}

/**
 * Save operation result
 */
export interface SaveResult {
  success: boolean;
  savedAt: Date;
  configId: string;
  pageId?: string;
  errors: string[];
  warnings: string[];
  changesCount: number;
}

/**
 * Configuration Persistence Service
 * Handles robust saving and persistence of builder configurations to environment files
 */
@Injectable({
  providedIn: 'root'
})
export class ConfigurationPersistenceService {
  private persistenceState = new BehaviorSubject<PersistenceState>({
    isSaving: false,
    lastSaved: null,
    hasUnsavedChanges: false,
    saveInProgress: false,
    lastError: null,
    autoSaveEnabled: true
  });

  private autoSaveInterval: any;
  private pendingChanges = new Set<string>();
  private debug = true;

  constructor(
    private builderApi: BuilderApiService,
    private environmentApi: EnvironmentApiService,
    private componentStore: ComponentStoreService
  ) {
    this.initializeAutoSave();
  }

  /**
   * Get persistence state as observable
   */
  getPersistenceState(): Observable<PersistenceState> {
    return this.persistenceState.asObservable();
  }

  /**
   * Save complete configuration
   */
  saveConfiguration(configId: string, config: AppConfig): Observable<SaveResult> {
    if (this.debug) console.log(`[ConfigPersistence] Saving configuration: ${configId}`);

    this.updateState({ isSaving: true, saveInProgress: true, lastError: null });

    const startTime = new Date();
    let changesCount = 0;
    const errors: string[] = [];
    const warnings: string[] = [];

    return this.validateConfiguration(config).pipe(
      switchMap(validation => {
        if (!validation.isValid) {
          errors.push(...validation.errors);
          warnings.push(...validation.warnings);
        }

        // Save each page
        const saveOperations = config.pages.map(page => 
          this.savePage(configId, page).pipe(
            tap(() => changesCount++),
            catchError(error => {
              errors.push(`Failed to save page ${page.id}: ${error.message}`);
              return of(false);
            })
          )
        );

        return forkJoin(saveOperations);
      }),
      switchMap(() => {
        // Save configuration metadata
        return this.saveConfigurationMetadata(configId, config).pipe(
          catchError(error => {
            errors.push(`Failed to save configuration metadata: ${error.message}`);
            return of(false);
          })
        );
      }),
      map(() => {
        const result: SaveResult = {
          success: errors.length === 0,
          savedAt: new Date(),
          configId,
          errors,
          warnings,
          changesCount
        };

        if (result.success) {
          this.updateState({
            lastSaved: result.savedAt,
            hasUnsavedChanges: false,
            lastError: null
          });
          this.pendingChanges.clear();
        } else {
          this.updateState({
            lastError: errors.join('; ')
          });
        }

        return result;
      }),
      finalize(() => {
        this.updateState({ isSaving: false, saveInProgress: false });
      }),
      catchError(error => {
        this.updateState({
          isSaving: false,
          saveInProgress: false,
          lastError: error.message
        });
        
        return of({
          success: false,
          savedAt: new Date(),
          configId,
          errors: [error.message],
          warnings: [],
          changesCount: 0
        });
      })
    );
  }

  /**
   * Save a specific page
   */
  savePage(configId: string, page: PageConfig): Observable<boolean> {
    if (this.debug) console.log(`[ConfigPersistence] Saving page: ${configId}/${page.id}`);

    // Get current components for this page
    const components = this.componentStore.getComponentsForPage(page.id);
    
    return this.builderApi.updatePageConfig(configId, page.id, page).pipe(
      switchMap(() => {
        // Save components separately
        return this.builderApi.savePageComponents(configId, page.id, components);
      }),
      tap(() => {
        if (this.debug) console.log(`[ConfigPersistence] Successfully saved page: ${page.id}`);
      }),
      catchError(error => {
        console.error(`[ConfigPersistence] Failed to save page ${page.id}:`, error);
        throw error;
      })
    );
  }

  /**
   * Save configuration metadata
   */
  private saveConfigurationMetadata(configId: string, config: AppConfig): Observable<boolean> {
    const metadata = {
      name: config.name,
      version: config.version,
      updatedAt: new Date().toISOString(),
      settings: config.settings
    };

    return this.builderApi.updateAppConfig(configId, metadata).pipe(
      map(() => true),
      catchError(error => {
        console.error(`[ConfigPersistence] Failed to save configuration metadata:`, error);
        throw error;
      })
    );
  }

  /**
   * Auto-save functionality
   */
  private initializeAutoSave(): void {
    // Auto-save every 30 seconds if there are pending changes
    this.autoSaveInterval = setInterval(() => {
      const state = this.persistenceState.value;
      if (state.autoSaveEnabled && state.hasUnsavedChanges && !state.saveInProgress) {
        this.performAutoSave();
      }
    }, 30000);
  }

  /**
   * Perform auto-save
   */
  private performAutoSave(): void {
    if (this.debug) console.log('[ConfigPersistence] Performing auto-save...');
    
    // This would need to be connected to the current configuration
    // For now, just log that auto-save would happen
    console.log('[ConfigPersistence] Auto-save triggered (implementation needed)');
  }

  /**
   * Mark configuration as having unsaved changes
   */
  markAsChanged(changeId?: string): void {
    if (changeId) {
      this.pendingChanges.add(changeId);
    }
    
    this.updateState({ hasUnsavedChanges: true });
    
    if (this.debug) {
      console.log(`[ConfigPersistence] Configuration marked as changed. Pending changes: ${this.pendingChanges.size}`);
    }
  }

  /**
   * Validate configuration before saving
   */
  private validateConfiguration(config: AppConfig): Observable<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Basic validation
    if (!config.id) {
      errors.push('Configuration ID is required');
    }

    if (!config.name || config.name.trim().length === 0) {
      errors.push('Configuration name is required');
    }

    if (!config.pages || config.pages.length === 0) {
      warnings.push('Configuration has no pages');
    }

    // Validate each page
    config.pages?.forEach((page, index) => {
      if (!page.id) {
        errors.push(`Page ${index + 1} is missing an ID`);
      }

      if (!page.name || page.name.trim().length === 0) {
        errors.push(`Page ${index + 1} is missing a name`);
      }

      // Validate components
      if (page.components && page.components.length > 0) {
        page.components.forEach((componentId, compIndex) => {
          const component = this.componentStore.getComponent(componentId);
          if (!component) {
            warnings.push(`Page ${page.name} references missing component: ${componentId}`);
          }
        });
      }
    });

    return of({
      isValid: errors.length === 0,
      errors,
      warnings
    });
  }

  /**
   * Create backup before saving
   */
  createBackup(configId: string): Observable<string> {
    if (this.debug) console.log(`[ConfigPersistence] Creating backup for: ${configId}`);

    // Extract client and environment from configId
    const [client, environment] = configId.split('_');
    
    return this.environmentApi.backupEnvironment(client, environment).pipe(
      tap(backupId => {
        if (this.debug) console.log(`[ConfigPersistence] Backup created: ${backupId}`);
      }),
      catchError(error => {
        console.warn('[ConfigPersistence] Failed to create backup:', error);
        // Don't fail the save operation if backup fails
        return of('backup_failed');
      })
    );
  }

  /**
   * Publish configuration to environment
   */
  publishConfiguration(configId: string, targetEnvironment: string): Observable<boolean> {
    if (this.debug) console.log(`[ConfigPersistence] Publishing ${configId} to ${targetEnvironment}`);

    this.updateState({ isSaving: true, saveInProgress: true });

    return this.builderApi.publishConfig(configId, targetEnvironment).pipe(
      tap(() => {
        if (this.debug) console.log(`[ConfigPersistence] Successfully published to ${targetEnvironment}`);
      }),
      finalize(() => {
        this.updateState({ isSaving: false, saveInProgress: false });
      }),
      catchError(error => {
        this.updateState({
          isSaving: false,
          saveInProgress: false,
          lastError: `Failed to publish: ${error.message}`
        });
        throw error;
      })
    );
  }

  /**
   * Enable/disable auto-save
   */
  setAutoSaveEnabled(enabled: boolean): void {
    this.updateState({ autoSaveEnabled: enabled });
    
    if (this.debug) {
      console.log(`[ConfigPersistence] Auto-save ${enabled ? 'enabled' : 'disabled'}`);
    }
  }

  /**
   * Get pending changes count
   */
  getPendingChangesCount(): number {
    return this.pendingChanges.size;
  }

  /**
   * Clear all pending changes
   */
  clearPendingChanges(): void {
    this.pendingChanges.clear();
    this.updateState({ hasUnsavedChanges: false });
  }

  /**
   * Update persistence state
   */
  private updateState(updates: Partial<PersistenceState>): void {
    const currentState = this.persistenceState.value;
    this.persistenceState.next({ ...currentState, ...updates });
  }

  /**
   * Cleanup on destroy
   */
  ngOnDestroy(): void {
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
    }
  }
}
