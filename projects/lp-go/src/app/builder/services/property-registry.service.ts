import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject, timer } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap } from 'rxjs/operators';
import {
  PropertyDefinition,
  ComponentPropertySchema,
  PropertyRegistryEntry,
  PropertyGroup,
  PropertyValidationResult,
  COMMON_PROPERTY_GROUPS,
  COMMON_PROPERTY_DEFINITIONS,
  PropertyType
} from '../models/property-definition.interface';

/**
 * Property Registry Service
 * Manages property definitions for all components and provides dynamic property discovery
 */
@Injectable({
  providedIn: 'root'
})
export class PropertyRegistryService {
  private registry = new Map<string, PropertyRegistryEntry>();
  private registrySubject = new BehaviorSubject<Map<string, PropertyRegistryEntry>>(new Map());
  private debug = true;

  // Real-time validation subjects
  private validationSubject = new Subject<{componentType: string, propertyKey: string, value: any}>();
  private validationResults = new Map<string, PropertyValidationResult>();

  constructor() {
    this.initializeRegistry();
  }

  /**
   * Initialize the registry with common component property definitions
   */
  private initializeRegistry(): void {
    if (this.debug) console.log('[PropertyRegistry] Initializing property registry...');

    // Register common component types
    this.registerCommonComponents();
    
    // Auto-discover mobile-components properties
    this.autoDiscoverMobileComponentProperties();
    
    if (this.debug) console.log(`[PropertyRegistry] Registry initialized with ${this.registry.size} component schemas`);
  }

  /**
   * Register common component property schemas
   */
  private registerCommonComponents(): void {
    // Button component
    this.registerComponentSchema('ButtonComponent', {
      componentType: 'ButtonComponent',
      properties: [
        { ...COMMON_PROPERTY_DEFINITIONS['text'], key: 'text', label: 'Button Text' },
        {
          key: 'type',
          label: 'Button Type',
          description: 'The type of button',
          type: 'select',
          group: 'behavior',
          order: 1,
          defaultValue: 'button',
          options: [
            { label: 'Button', value: 'button' },
            { label: 'Submit', value: 'submit' },
            { label: 'Reset', value: 'reset' }
          ]
        },
        COMMON_PROPERTY_DEFINITIONS['color'],
        {
          key: 'disabled',
          label: 'Disabled',
          description: 'Whether the button is disabled',
          type: 'boolean',
          group: 'behavior',
          order: 2,
          defaultValue: false
        },
        COMMON_PROPERTY_DEFINITIONS['tailwindClasses']
      ],
      groups: COMMON_PROPERTY_GROUPS
    });

    // Text component
    this.registerComponentSchema('TextComponent', {
      componentType: 'TextComponent',
      properties: [
        COMMON_PROPERTY_DEFINITIONS['content'],
        {
          key: 'tag',
          label: 'HTML Tag',
          description: 'The HTML tag to use',
          type: 'select',
          group: 'behavior',
          order: 1,
          defaultValue: 'p',
          options: [
            { label: 'Paragraph (p)', value: 'p' },
            { label: 'Span', value: 'span' },
            { label: 'Div', value: 'div' }
          ]
        },
        COMMON_PROPERTY_DEFINITIONS['tailwindClasses']
      ],
      groups: COMMON_PROPERTY_GROUPS
    });

    // Heading component
    this.registerComponentSchema('HeadingComponent', {
      componentType: 'HeadingComponent',
      properties: [
        { ...COMMON_PROPERTY_DEFINITIONS['text'], key: 'text', label: 'Heading Text' },
        {
          key: 'level',
          label: 'Heading Level',
          description: 'The heading level (h1-h6)',
          type: 'select',
          group: 'content',
          order: 2,
          defaultValue: 2,
          options: [
            { label: 'H1', value: 1 },
            { label: 'H2', value: 2 },
            { label: 'H3', value: 3 },
            { label: 'H4', value: 4 },
            { label: 'H5', value: 5 },
            { label: 'H6', value: 6 }
          ]
        },
        COMMON_PROPERTY_DEFINITIONS['tailwindClasses']
      ],
      groups: COMMON_PROPERTY_GROUPS
    });

    // Image component
    this.registerComponentSchema('ImageComponent', {
      componentType: 'ImageComponent',
      properties: [
        {
          key: 'src',
          label: 'Image URL',
          description: 'The URL of the image',
          type: 'url',
          group: 'content',
          order: 1,
          validation: { required: true },
          placeholder: 'https://example.com/image.jpg'
        },
        {
          key: 'alt',
          label: 'Alt Text',
          description: 'Alternative text for accessibility',
          type: 'string',
          group: 'content',
          order: 2,
          validation: { required: true },
          placeholder: 'Describe the image'
        },
        COMMON_PROPERTY_DEFINITIONS['width'],
        COMMON_PROPERTY_DEFINITIONS['height'],
        COMMON_PROPERTY_DEFINITIONS['tailwindClasses']
      ],
      groups: COMMON_PROPERTY_GROUPS
    });
  }

  /**
   * Auto-discover properties for mobile-components
   */
  private autoDiscoverMobileComponentProperties(): void {
    // This would integrate with the component registry to discover properties
    // For now, we'll add some common mobile-component schemas
    
    const mobileComponentTypes = [
      'WelcomeComponent',
      'ProfileComponent',
      'DashboardHeaderComponent',
      'HomeHeaderComponent',
      'NavigationComponent',
      'CompanyOverviewComponent',
      'TransactionsComponent',
      'ButtonGroupComponent'
    ];

    mobileComponentTypes.forEach(componentType => {
      if (!this.registry.has(componentType)) {
        this.registerGenericComponentSchema(componentType);
      }
    });
  }

  /**
   * Register a generic component schema for unknown components
   */
  private registerGenericComponentSchema(componentType: string): void {
    const schema: ComponentPropertySchema = {
      componentType,
      properties: [
        {
          key: 'className',
          label: 'CSS Classes',
          description: 'Custom CSS classes',
          type: 'string',
          group: 'appearance',
          order: 1
        },
        {
          key: 'showWhen',
          label: 'Show When',
          description: 'When to show this component',
          type: 'select',
          group: 'behavior',
          order: 1,
          defaultValue: 'always',
          options: [
            { label: 'Always', value: 'always' },
            { label: 'Authenticated', value: 'authenticated' },
            { label: 'Anonymous', value: 'anonymous' }
          ]
        },
        COMMON_PROPERTY_DEFINITIONS['tailwindClasses']
      ],
      groups: COMMON_PROPERTY_GROUPS
    };

    this.registerComponentSchema(componentType, schema);
  }

  /**
   * Register a component property schema
   */
  registerComponentSchema(componentType: string, schema: ComponentPropertySchema): void {
    const entry: PropertyRegistryEntry = {
      componentType,
      schema,
      lastUpdated: new Date(),
      source: 'manual'
    };

    this.registry.set(componentType, entry);
    this.registrySubject.next(this.registry);

    if (this.debug) {
      console.log(`[PropertyRegistry] Registered schema for ${componentType}:`, schema);
    }
  }

  /**
   * Get property schema for a component type
   */
  getComponentSchema(componentType: string): ComponentPropertySchema | null {
    const entry = this.registry.get(componentType);
    return entry ? entry.schema : null;
  }

  /**
   * Get property definitions for a component type
   */
  getComponentProperties(componentType: string): PropertyDefinition[] {
    const schema = this.getComponentSchema(componentType);
    return schema ? schema.properties : [];
  }

  /**
   * Get property groups for a component type
   */
  getComponentGroups(componentType: string): PropertyGroup[] {
    const schema = this.getComponentSchema(componentType);
    return schema ? schema.groups : COMMON_PROPERTY_GROUPS;
  }

  /**
   * Get all registered component types
   */
  getRegisteredComponentTypes(): string[] {
    return Array.from(this.registry.keys());
  }

  /**
   * Get registry as observable
   */
  getRegistry(): Observable<Map<string, PropertyRegistryEntry>> {
    return this.registrySubject.asObservable();
  }

  /**
   * Check if a component type is registered
   */
  isComponentRegistered(componentType: string): boolean {
    return this.registry.has(componentType);
  }

  /**
   * Get property definition by key for a component type
   */
  getPropertyDefinition(componentType: string, propertyKey: string): PropertyDefinition | null {
    const properties = this.getComponentProperties(componentType);
    return properties.find(prop => prop.key === propertyKey) || null;
  }

  /**
   * Validate property value against its definition
   */
  validateProperty(componentType: string, propertyKey: string, value: any): PropertyValidationResult {
    const definition = this.getPropertyDefinition(componentType, propertyKey);
    if (!definition) {
      return {
        isValid: false,
        errors: [`Property ${propertyKey} not found for ${componentType}`],
        warnings: []
      };
    }

    const errors: string[] = [];
    const warnings: string[] = [];

    // Required validation
    if (definition.validation?.required && (value === null || value === undefined || value === '')) {
      errors.push(`${definition.label} is required`);
    }

    // Type-specific validation
    if (value !== null && value !== undefined && value !== '') {
      switch (definition.type) {
        case 'number':
          if (isNaN(Number(value))) {
            errors.push(`${definition.label} must be a number`);
          } else {
            const num = Number(value);
            if (definition.validation?.min !== undefined && num < definition.validation.min) {
              errors.push(`${definition.label} must be at least ${definition.validation.min}`);
            }
            if (definition.validation?.max !== undefined && num > definition.validation.max) {
              errors.push(`${definition.label} must be at most ${definition.validation.max}`);
            }
          }
          break;

        case 'string':
        case 'textarea':
          const str = String(value);
          if (definition.validation?.minLength !== undefined && str.length < definition.validation.minLength) {
            errors.push(`${definition.label} must be at least ${definition.validation.minLength} characters`);
          }
          if (definition.validation?.maxLength !== undefined && str.length > definition.validation.maxLength) {
            errors.push(`${definition.label} must be at most ${definition.validation.maxLength} characters`);
          }
          if (definition.validation?.pattern) {
            const regex = new RegExp(definition.validation.pattern);
            if (!regex.test(str)) {
              errors.push(`${definition.label} format is invalid`);
            }
          }
          break;

        case 'url':
          try {
            new URL(String(value));
          } catch {
            errors.push(`${definition.label} must be a valid URL`);
          }
          break;

        case 'email':
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(String(value))) {
            errors.push(`${definition.label} must be a valid email address`);
          }
          break;
      }
    }

    // Custom validation
    if (definition.validation?.custom) {
      const customError = definition.validation.custom(value);
      if (customError) {
        errors.push(customError);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Real-time validation with debouncing
   */
  validatePropertyRealTime(componentType: string, propertyKey: string, value: any): Observable<PropertyValidationResult> {
    const validationKey = `${componentType}.${propertyKey}`;

    // Trigger validation with debouncing
    this.validationSubject.next({ componentType, propertyKey, value });

    return this.validationSubject.pipe(
      debounceTime(300), // 300ms debounce
      distinctUntilChanged((prev, curr) =>
        prev.componentType === curr.componentType &&
        prev.propertyKey === curr.propertyKey &&
        prev.value === curr.value
      ),
      switchMap(({ componentType, propertyKey, value }) => {
        const result = this.validateProperty(componentType, propertyKey, value);
        this.validationResults.set(validationKey, result);
        return [result];
      })
    );
  }

  /**
   * Get cached validation result
   */
  getCachedValidationResult(componentType: string, propertyKey: string): PropertyValidationResult | null {
    const validationKey = `${componentType}.${propertyKey}`;
    return this.validationResults.get(validationKey) || null;
  }

  /**
   * Clear validation cache
   */
  clearValidationCache(): void {
    this.validationResults.clear();
  }

  /**
   * Get property group metadata for a component type
   */
  getPropertyGroupMetadata(componentType: string): { groups: PropertyGroup[], totalProperties: number } {
    const schema = this.getComponentSchema(componentType);
    const groups = schema ? schema.groups : COMMON_PROPERTY_GROUPS;
    const properties = schema ? schema.properties : [];

    return {
      groups,
      totalProperties: properties.length
    };
  }

  /**
   * Enhanced validation with user-friendly messages
   */
  validatePropertyWithEnhancedMessages(componentType: string, propertyKey: string, value: any): PropertyValidationResult {
    const definition = this.getPropertyDefinition(componentType, propertyKey);
    if (!definition) {
      return {
        isValid: false,
        errors: [`Property "${propertyKey}" is not recognized for ${componentType} components.`],
        warnings: []
      };
    }

    const errors: string[] = [];
    const warnings: string[] = [];

    // Enhanced required validation
    if (definition.validation?.required && (value === null || value === undefined || value === '')) {
      errors.push(`${definition.label} is required. Please provide a value.`);
      return { isValid: false, errors, warnings };
    }

    // Enhanced type-specific validation with better messages
    if (value !== null && value !== undefined && value !== '') {
      switch (definition.type) {
        case 'number':
          if (isNaN(Number(value))) {
            errors.push(`${definition.label} must be a valid number. Please enter a numeric value.`);
          } else {
            const num = Number(value);
            if (definition.validation?.min !== undefined && num < definition.validation.min) {
              errors.push(`${definition.label} must be at least ${definition.validation.min}. Current value: ${num}`);
            }
            if (definition.validation?.max !== undefined && num > definition.validation.max) {
              errors.push(`${definition.label} must be at most ${definition.validation.max}. Current value: ${num}`);
            }
          }
          break;

        case 'string':
        case 'textarea':
          const str = String(value);
          if (definition.validation?.minLength !== undefined && str.length < definition.validation.minLength) {
            errors.push(`${definition.label} must be at least ${definition.validation.minLength} characters long. Current length: ${str.length}`);
          }
          if (definition.validation?.maxLength !== undefined && str.length > definition.validation.maxLength) {
            errors.push(`${definition.label} must be at most ${definition.validation.maxLength} characters long. Current length: ${str.length}`);
          }
          if (definition.validation?.pattern) {
            const regex = new RegExp(definition.validation.pattern);
            if (!regex.test(str)) {
              errors.push(`${definition.label} format is invalid. Please check the format and try again.`);
            }
          }
          break;

        case 'url':
          try {
            new URL(String(value));
          } catch {
            errors.push(`${definition.label} must be a valid URL (e.g., https://example.com). Please check the format.`);
          }
          break;

        case 'email':
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(String(value))) {
            errors.push(`${definition.label} must be a valid email address (e.g., <EMAIL>). Please check the format.`);
          }
          break;

        case 'color':
          const colorValue = String(value);
          const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
          const rgbRegex = /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/;
          const rgbaRegex = /^rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)$/;

          if (!hexRegex.test(colorValue) && !rgbRegex.test(colorValue) && !rgbaRegex.test(colorValue)) {
            errors.push(`${definition.label} must be a valid color format (hex, rgb, or rgba). Examples: #FF0000, rgb(255,0,0), rgba(255,0,0,0.5)`);
          }
          break;
      }
    }

    // Custom validation with enhanced error handling
    if (definition.validation?.custom) {
      try {
        const customError = definition.validation.custom(value);
        if (customError) {
          errors.push(`${definition.label}: ${customError}`);
        }
      } catch (error) {
        errors.push(`${definition.label}: Validation error occurred. Please check the value.`);
        console.warn('Custom validation error:', error);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}
