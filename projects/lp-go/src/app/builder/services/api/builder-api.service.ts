import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError, of } from 'rxjs';
import { catchError, map, tap, delay } from 'rxjs/operators';
import { AppConfig, PageConfig } from '../../models/builder-component.interface';
import { BuilderComponent } from '../../models/builder-component.interface';

/**
 * API response format
 */
interface ApiResponse<T> {
  data?: T;
  error?: string;
  success: boolean;
}

/**
 * Configuration query parameters
 */
export interface ConfigQueryParams {
  client: string;
  environment: string;
  version?: string;
}

/**
 * Environment file structure for lp-client-test integration
 */
export interface EnvironmentConfig {
  production: boolean;
  env: string;
  client: string;
  lssConfig: {
    appName: string;
    appVersion: string;
    pages: PageEnvironmentConfig[];
    [key: string]: any;
  };
}

/**
 * Page configuration as stored in environment files
 */
export interface PageEnvironmentConfig {
  title: string;
  path: string;
  secure?: boolean;
  class?: string;
  components: ComponentEnvironmentConfig[];
}

/**
 * Component configuration as stored in environment files
 */
export interface ComponentEnvironmentConfig {
  type: string;
  showWhen?: 'authenticated' | 'anonymous' | 'always';
  inputs?: Record<string, any>;
  config?: Record<string, any>;
  class?: string;
  id?: string;
}

/**
 * Builder API service to handle all API calls related to the app builder
 */
@Injectable({
  providedIn: 'root'
})
export class BuilderApiService {
  // API endpoint URL
  private readonly apiBaseUrl = '/api/builder';

  // Environment file API endpoints
  private readonly environmentApiUrl = '/api/environment';

  // Enable debug logging
  private debug = true;

  constructor(private http: HttpClient) {}
  
  /**
   * Get available configurations for a client (Real API)
   */
  getAvailableConfigs(client: string): Observable<AppConfig[]> {
    if (this.debug) console.log(`[BuilderAPI] Loading configurations for client: ${client}`);

    // Try real API first, fallback to mock if needed
    return this.getEnvironmentConfigs(client).pipe(
      map(envConfigs => this.convertEnvironmentToAppConfigs(envConfigs)),
      catchError(error => {
        console.warn('[BuilderAPI] Real API failed, falling back to mock data:', error);
        return this.getMockConfigs();
      })
    );
  }

  /**
   * Get environment configurations from lp-client-test
   */
  getEnvironmentConfigs(client: string): Observable<EnvironmentConfig[]> {
    const url = `${this.environmentApiUrl}/${client}/configs`;

    return this.http.get<ApiResponse<EnvironmentConfig[]>>(url).pipe(
      map(response => {
        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to fetch environment configurations');
        }
        return response.data;
      }),
      catchError(this.handleError)
    );
  }

  /**
   * Convert environment configs to app configs
   */
  private convertEnvironmentToAppConfigs(envConfigs: EnvironmentConfig[]): AppConfig[] {
    return envConfigs.map(envConfig => {
      const appConfig: AppConfig = {
        id: `${envConfig.client}_${envConfig.env}`,
        name: `${envConfig.lssConfig.appName} (${envConfig.env})`,
        version: envConfig.lssConfig.appVersion || '1.0.0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        owner: 'environment',
        pages: envConfig.lssConfig.pages.map(page => this.convertEnvironmentPageToPageConfig(page)),
        settings: {
          theme: 'environment',
          client: envConfig.client,
          environment: envConfig.env
        }
      };

      if (this.debug) {
        console.log(`[BuilderAPI] Converted environment config:`, {
          id: appConfig.id,
          name: appConfig.name,
          pageCount: appConfig.pages.length
        });
      }

      return appConfig;
    });
  }

  /**
   * Convert environment page to page config
   */
  private convertEnvironmentPageToPageConfig(envPage: PageEnvironmentConfig): PageConfig {
    return {
      id: `page_${envPage.path}`,
      name: envPage.title,
      components: envPage.components.map(comp => comp.id || `comp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`),
      type: 'builder',
      layout: 'fluid',
      styles: {
        backgroundColor: envPage.class || 'bg-white'
      },
      secure: envPage.secure || false
    };
  }

  /**
   * Convert environment component to builder component
   */
  private convertEnvironmentComponentToBuilderComponent(envComp: ComponentEnvironmentConfig): BuilderComponent {
    return {
      id: envComp.id || `comp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: envComp.type,
      properties: {
        ...envComp.inputs,
        ...envComp.config,
        showWhen: envComp.showWhen,
        className: envComp.class,
        tailwindClasses: envComp.class || ''
      },
      children: [],
      parentId: null,
      isContainer: false, // Default to false, can be determined by component type
      position: { id: '', x: 0, y: 0 },
      size: { width: 'auto', height: 'auto' }
    };
  }
  
  /**
   * Get a specific app configuration
   */
  getAppConfig(configId: string): Observable<AppConfig> {
    const url = `${this.apiBaseUrl}/configs/${configId}`;
    
    return this.http.get<ApiResponse<AppConfig>>(url).pipe(
      map(response => {
        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to fetch configuration');
        }
        return response.data;
      }),
      catchError(this.handleError)
    );
  }
  
  /**
   * Get a specific page configuration with components (Real API)
   */
  getPageConfig(configId: string, pageId: string): Observable<{
    pageConfig: PageConfig;
    components: Record<string, BuilderComponent>;
  }> {
    if (this.debug) console.log(`[BuilderAPI] Loading page config: ${configId}/${pageId}`);

    // Try real API first, fallback to mock if needed
    return this.getEnvironmentPageConfig(configId, pageId).pipe(
      catchError(error => {
        console.warn('[BuilderAPI] Real page API failed, falling back to mock data:', error);
        return this.getMockPageConfig(configId, pageId);
      })
    );
  }

  /**
   * Get page configuration from environment files
   */
  getEnvironmentPageConfig(configId: string, pageId: string): Observable<{
    pageConfig: PageConfig;
    components: Record<string, BuilderComponent>;
  }> {
    const url = `${this.environmentApiUrl}/${configId}/pages/${pageId}`;

    return this.http.get<ApiResponse<PageEnvironmentConfig>>(url).pipe(
      map(response => {
        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to fetch environment page configuration');
        }

        const envPage = response.data;
        const pageConfig = this.convertEnvironmentPageToPageConfig(envPage);

        // Create components map from the environment page data
        const components: Record<string, BuilderComponent> = {};
        envPage.components.forEach(envComp => {
          const builderComp = this.convertEnvironmentComponentToBuilderComponent(envComp);
          components[builderComp.id] = builderComp;
        });

        if (this.debug) {
          console.log(`[BuilderAPI] Loaded environment page:`, {
            pageId: pageConfig.id,
            name: pageConfig.name,
            componentCount: pageConfig.components.length
          });
        }

        return { pageConfig, components };
      }),
      catchError(this.handleError)
    );
  }

  /**
   * Mock page config for fallback
   */
  private getMockPageConfig(configId: string, pageId: string): Observable<{
    pageConfig: PageConfig;
    components: Record<string, BuilderComponent>;
  }> {
    const mockPageConfig: PageConfig = {
      id: pageId,
      name: 'Mock Page',
      components: [],
      type: 'builder',
      layout: 'fluid',
      styles: {
        backgroundColor: 'bg-white'
      }
    };

    return of({
      pageConfig: mockPageConfig,
      components: {}
    }).pipe(delay(500));
  }
  
  /**
   * Create a new app configuration
   */
  createAppConfig(config: Partial<AppConfig>): Observable<AppConfig> {
    const url = `${this.apiBaseUrl}/configs`;

    return this.http.post<ApiResponse<AppConfig>>(url, config).pipe(
      map(response => {
        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to create configuration');
        }
        return response.data;
      }),
      catchError(this.handleError)
    );
  }

  /**
   * Update an existing app configuration
   */
  updateAppConfig(configId: string, updates: Partial<AppConfig>): Observable<AppConfig> {
    if (this.debug) console.log(`[BuilderAPI] Updating app config: ${configId}`);

    const url = `${this.apiBaseUrl}/configs/${configId}`;

    return this.http.put<ApiResponse<AppConfig>>(url, updates).pipe(
      map(response => {
        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to update configuration');
        }

        if (this.debug) {
          console.log(`[BuilderAPI] Successfully updated app config: ${configId}`);
        }

        return response.data;
      }),
      catchError(error => {
        console.warn('[BuilderAPI] Failed to update app config, using fallback:', error);

        // Fallback: return the updates as if they were successful
        return of({
          id: configId,
          ...updates
        } as AppConfig);
      })
    );
  }
  
  /**
   * Create a new page in an app configuration
   */
  createPage(configId: string, page: Partial<PageConfig>): Observable<PageConfig> {
    const url = `${this.apiBaseUrl}/configs/${configId}/pages`;
    
    return this.http.post<ApiResponse<PageConfig>>(url, page).pipe(
      map(response => {
        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to create page');
        }
        return response.data;
      }),
      catchError(this.handleError)
    );
  }
  

  
  /**
   * Update a page configuration (Real API)
   */
  updatePageConfig(configId: string, pageId: string, updates: Partial<PageConfig>): Observable<PageConfig> {
    if (this.debug) console.log(`[BuilderAPI] Updating page config: ${configId}/${pageId}`);

    // Try real API first, fallback to mock if needed
    return this.updateEnvironmentPage(configId, pageId, updates).pipe(
      catchError(error => {
        console.warn('[BuilderAPI] Real update API failed, falling back to mock:', error);
        return this.mockUpdatePage(configId, pageId, updates);
      })
    );
  }

  /**
   * Update page in environment files
   */
  private updateEnvironmentPage(configId: string, pageId: string, updates: Partial<PageConfig>): Observable<PageConfig> {
    const url = `${this.environmentApiUrl}/${configId}/pages/${pageId}`;

    // Convert updates to environment format
    const envUpdates = this.convertPageUpdatesToEnvironmentFormat(updates);

    return this.http.put<ApiResponse<PageEnvironmentConfig>>(url, envUpdates).pipe(
      map(response => {
        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to update environment page');
        }

        // Convert back to PageConfig format
        const updatedPage = this.convertEnvironmentPageToPageConfig(response.data);

        if (this.debug) {
          console.log(`[BuilderAPI] Successfully updated environment page: ${pageId}`);
        }

        return updatedPage;
      }),
      catchError(this.handleError)
    );
  }

  /**
   * Convert page updates to environment format
   */
  private convertPageUpdatesToEnvironmentFormat(updates: Partial<PageConfig>): Partial<PageEnvironmentConfig> {
    const envUpdates: Partial<PageEnvironmentConfig> = {};

    if (updates.name) envUpdates.title = updates.name;
    if (updates.styles?.backgroundColor) envUpdates.class = updates.styles.backgroundColor;
    // Note: components array contains component IDs, not full component objects
    // Component updates should be handled separately via savePageComponents

    return envUpdates;
  }

  /**
   * Convert builder component back to environment format
   */
  private convertBuilderComponentToEnvironmentComponent(builderComp: BuilderComponent): ComponentEnvironmentConfig {
    const envComp: ComponentEnvironmentConfig = {
      type: builderComp.type,
      inputs: { ...builderComp.properties }
    };

    // Extract special properties
    if (builderComp.properties['showWhen']) {
      envComp.showWhen = builderComp.properties['showWhen'];
      delete envComp.inputs!['showWhen'];
    }

    if (builderComp.properties['className']) {
      envComp.class = builderComp.properties['className'];
      delete envComp.inputs!['className'];
    }

    if (builderComp.id && !builderComp.id.startsWith('comp_')) {
      envComp.id = builderComp.id;
    }

    return envComp;
  }

  /**
   * Mock update for fallback
   */
  private mockUpdatePage(configId: string, pageId: string, updates: Partial<PageConfig>): Observable<PageConfig> {
    console.log('[BuilderAPI] Mock page update - changes will not persist');

    // Return a mock updated page
    const mockPage: PageConfig = {
      id: pageId,
      name: updates.name || 'Updated Page',
      components: updates.components || [],
      type: 'builder',
      layout: 'fluid',
      styles: updates.styles || { backgroundColor: 'bg-white' }
    };

    return of(mockPage).pipe(delay(500));
  }
  
  /**
   * Save page components (Real API)
   */
  savePageComponents(
    configId: string,
    pageId: string,
    components: Record<string, BuilderComponent>
  ): Observable<boolean> {
    if (this.debug) console.log(`[BuilderAPI] Saving components for: ${configId}/${pageId}`);

    // Try real API first, fallback to mock if needed
    return this.saveEnvironmentPageComponents(configId, pageId, components).pipe(
      catchError(error => {
        console.warn('[BuilderAPI] Real save API failed, falling back to mock:', error);
        return this.mockSaveComponents(configId, pageId, components);
      })
    );
  }

  /**
   * Save components to environment files
   */
  private saveEnvironmentPageComponents(
    configId: string,
    pageId: string,
    components: Record<string, BuilderComponent>
  ): Observable<boolean> {
    const url = `${this.environmentApiUrl}/${configId}/pages/${pageId}/components`;

    // Convert components to environment format
    const envComponents = Object.values(components).map(comp =>
      this.convertBuilderComponentToEnvironmentComponent(comp)
    );

    return this.http.put<ApiResponse<boolean>>(url, { components: envComponents }).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.error || 'Failed to save components to environment');
        }

        if (this.debug) {
          console.log(`[BuilderAPI] Successfully saved ${envComponents.length} components to environment`);
        }

        return true;
      }),
      catchError(this.handleError)
    );
  }

  /**
   * Mock save for fallback
   */
  private mockSaveComponents(
    configId: string,
    pageId: string,
    components: Record<string, BuilderComponent>
  ): Observable<boolean> {
    console.log('[BuilderAPI] Mock component save - changes will not persist');
    return of(true).pipe(delay(500));
  }
  
  /**
   * Delete a page from an app configuration
   */
  deletePage(configId: string, pageId: string): Observable<boolean> {
    const url = `${this.apiBaseUrl}/configs/${configId}/pages/${pageId}`;
    
    return this.http.delete<ApiResponse<boolean>>(url).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.error || 'Failed to delete page');
        }
        return true;
      }),
      catchError(this.handleError)
    );
  }
  
  /**
   * Delete an entire app configuration
   */
  deleteAppConfig(configId: string): Observable<boolean> {
    const url = `${this.apiBaseUrl}/configs/${configId}`;
    
    return this.http.delete<ApiResponse<boolean>>(url).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.error || 'Failed to delete configuration');
        }
        return true;
      }),
      catchError(this.handleError)
    );
  }
  
  /**
   * Duplicate a page
   */
  duplicatePage(configId: string, pageId: string, newName: string): Observable<PageConfig> {
    const url = `${this.apiBaseUrl}/configs/${configId}/pages/${pageId}/duplicate`;
    
    return this.http.post<ApiResponse<PageConfig>>(url, { name: newName }).pipe(
      map(response => {
        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to duplicate page');
        }
        return response.data;
      }),
      catchError(this.handleError)
    );
  }
  
  /**
   * Publish an app configuration
   */
  publishConfig(configId: string, environment: string): Observable<boolean> {
    const url = `${this.apiBaseUrl}/configs/${configId}/publish`;
    
    return this.http.post<ApiResponse<boolean>>(url, { environment }).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.error || 'Failed to publish configuration');
        }
        return true;
      }),
      catchError(this.handleError)
    );
  }
  
  /**
   * Error handler for API calls
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = `Error Code: ${error.status}, Message: ${error.message}`;
    }
    
    console.error('API Error:', errorMessage, error);
    return throwError(() => new Error(errorMessage));
  }
  
  /**
   * MOCK IMPLEMENTATION:
   * The following methods implement mock functionality when the actual API is not available.
   * These will simulate API responses for development and testing.
   */
  
  /**
   * Mock: Get available configurations
   */
  getMockConfigs(): Observable<AppConfig[]> {
    // Simulate API delay
    return of([
      {
        id: 'config1',
        name: 'Default Configuration',
        version: '1.0.0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        owner: 'default_user',
        pages: [
          {
            id: 'page1',
            name: 'Home Page',
            components: [],
            type: 'builder' as 'builder',
            layout: 'fluid' as 'fluid',
            styles: {
              backgroundColor: 'bg-white'
            }
          },
          {
            id: 'page2',
            name: 'Products Page',
            components: [],
            type: 'builder' as 'builder',
            layout: 'fluid' as 'fluid',
            styles: {
              backgroundColor: 'bg-gray-50'
            }
          }
        ],
        settings: {
          theme: 'default'
        }
      },
      {
        id: 'config2',
        name: 'Marketing Configuration',
        version: '1.0.0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        owner: 'default_user',
        pages: [
          {
            id: 'page3',
            name: 'Landing Page',
            components: [],
            type: 'builder' as 'builder',
            layout: 'fluid' as 'fluid',
            styles: {
              backgroundColor: 'bg-blue-50'
            }
          }
        ],
        settings: {
          theme: 'marketing'
        }
      }
    ]).pipe(delay(500)); // Add artificial delay to simulate network
  }
  
  /**
   * Mock: Create a new app config
   */
  mockCreateConfig(name: string): Observable<AppConfig> {
    const now = new Date().toISOString();
    const newConfig: AppConfig = {
      id: `config_${Date.now().toString(36)}`,
      name,
      version: '1.0.0',
      createdAt: now,
      updatedAt: now,
      owner: 'default_user',
      pages: [
        {
          id: `page_${Date.now().toString(36)}`,
          name: 'New Page',
          components: [],
          type: 'builder' as 'builder',
          layout: 'fluid' as 'fluid',
          styles: {
            backgroundColor: 'bg-white'
          }
        }
      ],
      settings: {
        theme: 'default'
      }
    };
    
    return of(newConfig).pipe(delay(500));
  }
  
  /**
   * Mock: Create a new page
   */
  mockCreatePage(name: string): Observable<PageConfig> {
    const newPage: PageConfig = {
      id: `page_${Date.now().toString(36)}`,
      name,
      components: [],
      type: 'builder',
      layout: 'fluid',
      styles: {
        backgroundColor: 'bg-white'
      }
    };
    
    return of(newPage).pipe(delay(500));
  }
}
