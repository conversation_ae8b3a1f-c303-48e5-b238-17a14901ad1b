import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

/**
 * Environment API Service for connecting to lp-client-test environment files
 * This service provides a bridge between the builder and the actual environment configurations
 */
@Injectable({
  providedIn: 'root'
})
export class EnvironmentApiService {
  private readonly baseUrl = '/api/environment';
  private debug = true;

  constructor(private http: HttpClient) {}

  /**
   * Get available environment configurations for a client
   */
  getClientEnvironments(client: string): Observable<string[]> {
    if (this.debug) console.log(`[EnvironmentAPI] Getting environments for client: ${client}`);
    
    const url = `${this.baseUrl}/${client}/environments`;
    
    return this.http.get<{ success: boolean; data: string[] }>(url).pipe(
      map(response => {
        if (!response.success) {
          throw new Error('Failed to fetch client environments');
        }
        return response.data;
      }),
      catchError(error => {
        console.warn('[EnvironmentAPI] Failed to fetch environments, using defaults:', error);
        return of(['dev', 'staging', 'prod']);
      })
    );
  }

  /**
   * Get environment configuration file content
   */
  getEnvironmentConfig(client: string, environment: string): Observable<any> {
    if (this.debug) console.log(`[EnvironmentAPI] Getting config for ${client}/${environment}`);
    
    const url = `${this.baseUrl}/${client}/${environment}/config`;
    
    return this.http.get<{ success: boolean; data: any }>(url).pipe(
      map(response => {
        if (!response.success) {
          throw new Error('Failed to fetch environment configuration');
        }
        return response.data;
      }),
      catchError(error => {
        console.warn('[EnvironmentAPI] Failed to fetch config:', error);
        throw error;
      })
    );
  }

  /**
   * Get specific page configuration from environment
   */
  getPageConfiguration(client: string, environment: string, pageId: string): Observable<any> {
    if (this.debug) console.log(`[EnvironmentAPI] Getting page ${pageId} for ${client}/${environment}`);
    
    const url = `${this.baseUrl}/${client}/${environment}/pages/${pageId}`;
    
    return this.http.get<{ success: boolean; data: any }>(url).pipe(
      map(response => {
        if (!response.success) {
          throw new Error('Failed to fetch page configuration');
        }
        return response.data;
      }),
      catchError(error => {
        console.warn('[EnvironmentAPI] Failed to fetch page config:', error);
        throw error;
      })
    );
  }

  /**
   * Update page configuration in environment file
   */
  updatePageConfiguration(client: string, environment: string, pageId: string, pageConfig: any): Observable<boolean> {
    if (this.debug) console.log(`[EnvironmentAPI] Updating page ${pageId} for ${client}/${environment}`);
    
    const url = `${this.baseUrl}/${client}/${environment}/pages/${pageId}`;
    
    return this.http.put<{ success: boolean; message?: string }>(url, pageConfig).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message || 'Failed to update page configuration');
        }
        return true;
      }),
      catchError(error => {
        console.error('[EnvironmentAPI] Failed to update page config:', error);
        throw error;
      })
    );
  }

  /**
   * Update components for a specific page
   */
  updatePageComponents(client: string, environment: string, pageId: string, components: any[]): Observable<boolean> {
    if (this.debug) console.log(`[EnvironmentAPI] Updating components for page ${pageId} in ${client}/${environment}`);
    
    const url = `${this.baseUrl}/${client}/${environment}/pages/${pageId}/components`;
    
    return this.http.put<{ success: boolean; message?: string }>(url, { components }).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message || 'Failed to update page components');
        }
        return true;
      }),
      catchError(error => {
        console.error('[EnvironmentAPI] Failed to update components:', error);
        throw error;
      })
    );
  }

  /**
   * Create a new page in environment configuration
   */
  createPage(client: string, environment: string, pageConfig: any): Observable<any> {
    if (this.debug) console.log(`[EnvironmentAPI] Creating new page for ${client}/${environment}`);
    
    const url = `${this.baseUrl}/${client}/${environment}/pages`;
    
    return this.http.post<{ success: boolean; data?: any; message?: string }>(url, pageConfig).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message || 'Failed to create page');
        }
        return response.data;
      }),
      catchError(error => {
        console.error('[EnvironmentAPI] Failed to create page:', error);
        throw error;
      })
    );
  }

  /**
   * Delete a page from environment configuration
   */
  deletePage(client: string, environment: string, pageId: string): Observable<boolean> {
    if (this.debug) console.log(`[EnvironmentAPI] Deleting page ${pageId} from ${client}/${environment}`);
    
    const url = `${this.baseUrl}/${client}/${environment}/pages/${pageId}`;
    
    return this.http.delete<{ success: boolean; message?: string }>(url).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message || 'Failed to delete page');
        }
        return true;
      }),
      catchError(error => {
        console.error('[EnvironmentAPI] Failed to delete page:', error);
        throw error;
      })
    );
  }

  /**
   * Backup current environment configuration
   */
  backupEnvironment(client: string, environment: string): Observable<string> {
    if (this.debug) console.log(`[EnvironmentAPI] Creating backup for ${client}/${environment}`);
    
    const url = `${this.baseUrl}/${client}/${environment}/backup`;
    
    return this.http.post<{ success: boolean; data?: string; message?: string }>(url, {}).pipe(
      map(response => {
        if (!response.success) {
          throw new Error(response.message || 'Failed to create backup');
        }
        return response.data || '';
      }),
      catchError(error => {
        console.error('[EnvironmentAPI] Failed to create backup:', error);
        throw error;
      })
    );
  }

  /**
   * Validate environment configuration
   */
  validateEnvironment(client: string, environment: string): Observable<{ valid: boolean; errors: string[] }> {
    if (this.debug) console.log(`[EnvironmentAPI] Validating ${client}/${environment}`);
    
    const url = `${this.baseUrl}/${client}/${environment}/validate`;
    
    return this.http.get<{ success: boolean; data?: { valid: boolean; errors: string[] } }>(url).pipe(
      map(response => {
        if (!response.success) {
          throw new Error('Failed to validate environment');
        }
        return response.data || { valid: false, errors: ['Validation failed'] };
      }),
      catchError(error => {
        console.error('[EnvironmentAPI] Failed to validate environment:', error);
        return of({ valid: false, errors: [error.message || 'Validation error'] });
      })
    );
  }
}
