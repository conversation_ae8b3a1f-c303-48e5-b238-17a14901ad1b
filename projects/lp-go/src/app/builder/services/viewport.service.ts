import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { 
  Breakpoint, 
  DevicePreset, 
  ViewportState, 
  ResponsiveBuilderState,
  DeviceType,
  Orientation,
  ResponsiveProperty,
  ResponsiveValidationRule,
  ResponsiveValidationResult,
  DeviceFrameConfig
} from '../models/viewport.interface';

/**
 * Viewport Service
 * Manages responsive design state, breakpoints, and device simulation
 */
@Injectable({
  providedIn: 'root'
})
export class ViewportService {
  private readonly STORAGE_KEY = 'lp-go-viewport-state';
  
  // Default breakpoints
  private readonly DEFAULT_BREAKPOINTS: Breakpoint[] = [
    {
      id: 'mobile',
      name: 'Mobile',
      width: 375,
      height: 667,
      deviceType: DeviceType.MOBILE,
      orientation: Orientation.PORTRAIT,
      mediaQuery: '(max-width: 767px)',
      isBuiltIn: true,
      icon: '📱'
    },
    {
      id: 'tablet',
      name: 'Tablet',
      width: 768,
      height: 1024,
      deviceType: DeviceType.TABLET,
      orientation: Orientation.PORTRAIT,
      mediaQuery: '(min-width: 768px) and (max-width: 1023px)',
      isBuiltIn: true,
      icon: '📱'
    },
    {
      id: 'desktop',
      name: 'Desktop',
      width: 1440,
      height: 900,
      deviceType: DeviceType.DESKTOP,
      orientation: Orientation.LANDSCAPE,
      mediaQuery: '(min-width: 1024px)',
      isBuiltIn: true,
      icon: '🖥️'
    }
  ];

  // Default device presets
  private readonly DEFAULT_DEVICE_PRESETS: DevicePreset[] = [
    {
      id: 'iphone-14',
      name: 'iPhone 14',
      brand: 'Apple',
      deviceType: DeviceType.MOBILE,
      portrait: { width: 390, height: 844 },
      landscape: { width: 844, height: 390 },
      pixelRatio: 3,
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15'
    },
    {
      id: 'ipad-air',
      name: 'iPad Air',
      brand: 'Apple',
      deviceType: DeviceType.TABLET,
      portrait: { width: 820, height: 1180 },
      landscape: { width: 1180, height: 820 },
      pixelRatio: 2,
      userAgent: 'Mozilla/5.0 (iPad; CPU OS 16_0 like Mac OS X) AppleWebKit/605.1.15'
    },
    {
      id: 'macbook-pro',
      name: 'MacBook Pro',
      brand: 'Apple',
      deviceType: DeviceType.DESKTOP,
      portrait: { width: 1440, height: 900 },
      landscape: { width: 1440, height: 900 },
      pixelRatio: 2,
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
    }
  ];

  // State management
  private viewportStateSubject = new BehaviorSubject<ViewportState>(this.getDefaultViewportState());
  private breakpointsSubject = new BehaviorSubject<Breakpoint[]>(this.DEFAULT_BREAKPOINTS);
  private devicePresetsSubject = new BehaviorSubject<DevicePreset[]>(this.DEFAULT_DEVICE_PRESETS);
  private deviceFrameConfigSubject = new BehaviorSubject<DeviceFrameConfig>(this.getDefaultFrameConfig());

  constructor() {
    this.loadState();
  }

  /**
   * Get viewport state as observable
   */
  getViewportState(): Observable<ViewportState> {
    return this.viewportStateSubject.asObservable();
  }

  /**
   * Get current viewport state value
   */
  getCurrentViewportState(): ViewportState {
    return this.viewportStateSubject.value;
  }

  /**
   * Get available breakpoints
   */
  getBreakpoints(): Observable<Breakpoint[]> {
    return this.breakpointsSubject.asObservable();
  }

  /**
   * Get available device presets
   */
  getDevicePresets(): Observable<DevicePreset[]> {
    return this.devicePresetsSubject.asObservable();
  }

  /**
   * Get device frame configuration
   */
  getDeviceFrameConfig(): Observable<DeviceFrameConfig> {
    return this.deviceFrameConfigSubject.asObservable();
  }

  /**
   * Set current breakpoint
   */
  setCurrentBreakpoint(breakpoint: Breakpoint): void {
    const currentState = this.viewportStateSubject.value;
    const newState: ViewportState = {
      ...currentState,
      currentBreakpoint: breakpoint
    };
    
    this.viewportStateSubject.next(newState);
    this.saveState();
    console.log('Viewport breakpoint changed to:', breakpoint.name);
  }

  /**
   * Set selected device preset
   */
  setSelectedDevice(device: DevicePreset | null): void {
    const currentState = this.viewportStateSubject.value;
    let newBreakpoint = currentState.currentBreakpoint;
    
    if (device) {
      // Update breakpoint based on device
      const dimensions = currentState.orientation === Orientation.PORTRAIT 
        ? device.portrait 
        : device.landscape;
        
      newBreakpoint = {
        ...currentState.currentBreakpoint,
        width: dimensions.width,
        height: dimensions.height,
        deviceType: device.deviceType
      };
    }
    
    const newState: ViewportState = {
      ...currentState,
      selectedDevice: device,
      currentBreakpoint: newBreakpoint
    };
    
    this.viewportStateSubject.next(newState);
    this.saveState();
    console.log('Selected device changed to:', device?.name || 'None');
  }

  /**
   * Toggle device orientation
   */
  toggleOrientation(): void {
    const currentState = this.viewportStateSubject.value;
    const newOrientation = currentState.orientation === Orientation.PORTRAIT 
      ? Orientation.LANDSCAPE 
      : Orientation.PORTRAIT;
    
    let newBreakpoint = currentState.currentBreakpoint;
    
    if (currentState.selectedDevice) {
      const dimensions = newOrientation === Orientation.PORTRAIT
        ? currentState.selectedDevice.portrait
        : currentState.selectedDevice.landscape;
        
      newBreakpoint = {
        ...currentState.currentBreakpoint,
        width: dimensions.width,
        height: dimensions.height,
        orientation: newOrientation
      };
    }
    
    const newState: ViewportState = {
      ...currentState,
      orientation: newOrientation,
      currentBreakpoint: newBreakpoint
    };
    
    this.viewportStateSubject.next(newState);
    this.saveState();
    console.log('Orientation changed to:', newOrientation);
  }

  /**
   * Toggle responsive mode
   */
  toggleResponsiveMode(): void {
    const currentState = this.viewportStateSubject.value;
    const newState: ViewportState = {
      ...currentState,
      responsiveModeEnabled: !currentState.responsiveModeEnabled
    };
    
    this.viewportStateSubject.next(newState);
    this.saveState();
    console.log('Responsive mode:', newState.responsiveModeEnabled ? 'enabled' : 'disabled');
  }

  /**
   * Toggle device frame
   */
  toggleDeviceFrame(): void {
    const currentState = this.viewportStateSubject.value;
    const newState: ViewportState = {
      ...currentState,
      deviceFrameEnabled: !currentState.deviceFrameEnabled
    };
    
    this.viewportStateSubject.next(newState);
    this.saveState();
    console.log('Device frame:', newState.deviceFrameEnabled ? 'enabled' : 'disabled');
  }

  /**
   * Set zoom level
   */
  setZoomLevel(zoomLevel: number): void {
    const currentState = this.viewportStateSubject.value;
    const clampedZoom = Math.max(0.25, Math.min(3, zoomLevel));
    
    const newState: ViewportState = {
      ...currentState,
      zoomLevel: clampedZoom
    };
    
    this.viewportStateSubject.next(newState);
    this.saveState();
    console.log('Zoom level changed to:', clampedZoom);
  }

  /**
   * Get responsive property value for current breakpoint
   */
  getResponsivePropertyValue<T>(property: ResponsiveProperty<T>): T {
    const currentBreakpoint = this.viewportStateSubject.value.currentBreakpoint;
    
    // Check for exact breakpoint match
    if (property[currentBreakpoint.deviceType as keyof ResponsiveProperty<T>] !== undefined) {
      return property[currentBreakpoint.deviceType as keyof ResponsiveProperty<T>] as T;
    }
    
    // Check for custom breakpoint
    if (property.custom && property.custom[currentBreakpoint.id] !== undefined) {
      return property.custom[currentBreakpoint.id];
    }
    
    // Fallback to inheritance chain
    switch (currentBreakpoint.deviceType) {
      case DeviceType.MOBILE:
        return property.mobile ?? property.tablet ?? property.desktop ?? property.default;
      case DeviceType.TABLET:
        return property.tablet ?? property.desktop ?? property.default;
      case DeviceType.DESKTOP:
        return property.desktop ?? property.default;
      default:
        return property.default;
    }
  }

  /**
   * Set responsive property value for current breakpoint
   */
  setResponsivePropertyValue<T>(
    property: ResponsiveProperty<T>, 
    value: T, 
    breakpoint?: Breakpoint
  ): ResponsiveProperty<T> {
    const targetBreakpoint = breakpoint || this.viewportStateSubject.value.currentBreakpoint;
    
    const updatedProperty = { ...property };
    
    switch (targetBreakpoint.deviceType) {
      case DeviceType.MOBILE:
        updatedProperty.mobile = value;
        break;
      case DeviceType.TABLET:
        updatedProperty.tablet = value;
        break;
      case DeviceType.DESKTOP:
        updatedProperty.desktop = value;
        break;
      default:
        if (!updatedProperty.custom) {
          updatedProperty.custom = {};
        }
        updatedProperty.custom[targetBreakpoint.id] = value;
    }
    
    return updatedProperty;
  }

  /**
   * Get breakpoint by ID
   */
  getBreakpointById(id: string): Breakpoint | null {
    return this.breakpointsSubject.value.find(bp => bp.id === id) || null;
  }

  /**
   * Get device preset by ID
   */
  getDevicePresetById(id: string): DevicePreset | null {
    return this.devicePresetsSubject.value.find(device => device.id === id) || null;
  }

  /**
   * Add custom breakpoint
   */
  addCustomBreakpoint(breakpoint: Omit<Breakpoint, 'isBuiltIn'>): void {
    const customBreakpoint: Breakpoint = {
      ...breakpoint,
      isBuiltIn: false
    };
    
    const currentBreakpoints = this.breakpointsSubject.value;
    this.breakpointsSubject.next([...currentBreakpoints, customBreakpoint]);
    this.saveState();
  }

  /**
   * Remove custom breakpoint
   */
  removeCustomBreakpoint(id: string): void {
    const currentBreakpoints = this.breakpointsSubject.value;
    const filteredBreakpoints = currentBreakpoints.filter(bp => bp.id !== id || bp.isBuiltIn);
    this.breakpointsSubject.next(filteredBreakpoints);
    this.saveState();
  }

  // Private helper methods

  private getDefaultViewportState(): ViewportState {
    return {
      currentBreakpoint: this.DEFAULT_BREAKPOINTS[2], // Desktop
      responsiveModeEnabled: false,
      deviceFrameEnabled: false,
      selectedDevice: null,
      orientation: Orientation.LANDSCAPE,
      zoomLevel: 1
    };
  }

  private getDefaultFrameConfig(): DeviceFrameConfig {
    return {
      enabled: false,
      type: 'browser',
      color: '#333333',
      scale: 1,
      showControls: true,
      showInfo: false
    };
  }

  private loadState(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const state = JSON.parse(stored);
        if (state.viewportState) {
          this.viewportStateSubject.next(state.viewportState);
        }
        if (state.deviceFrameConfig) {
          this.deviceFrameConfigSubject.next(state.deviceFrameConfig);
        }
      }
    } catch (error) {
      console.warn('Failed to load viewport state:', error);
    }
  }

  private saveState(): void {
    try {
      const state = {
        viewportState: this.viewportStateSubject.value,
        deviceFrameConfig: this.deviceFrameConfigSubject.value
      };
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(state));
    } catch (error) {
      console.error('Failed to save viewport state:', error);
    }
  }
}
