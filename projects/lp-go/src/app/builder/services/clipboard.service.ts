import { Injectable } from '@angular/core';
import { ClipboardData } from '../models/builder-component.interface';

/**
 * Service for managing clipboard operations
 * Handles copying and pasting of components across pages
 */
@Injectable({
  providedIn: 'root'
})
export class ClipboardService {
  private clipboardData: ClipboardData | null = null;
  private readonly STORAGE_KEY = 'lp-go-clipboard';

  constructor() {
    // Try to restore clipboard data from localStorage on initialization
    this.restoreFromStorage();
  }

  /**
   * Store clipboard data
   */
  setClipboardData(data: ClipboardData): void {
    this.clipboardData = data;
    this.saveToStorage();
  }

  /**
   * Get clipboard data
   */
  getClipboardData(): ClipboardData | null {
    return this.clipboardData;
  }

  /**
   * Check if clipboard has data
   */
  hasClipboardData(): boolean {
    return this.clipboardData !== null && this.clipboardData.components.length > 0;
  }

  /**
   * Clear clipboard
   */
  clearClipboard(): void {
    this.clipboardData = null;
    localStorage.removeItem(this.STORAGE_KEY);
  }

  /**
   * Check if clipboard data is from different page
   */
  isFromDifferentPage(currentPageId: string): boolean {
    return this.clipboardData?.sourcePageId !== currentPageId;
  }

  /**
   * Get clipboard data age in milliseconds
   */
  getClipboardAge(): number {
    if (!this.clipboardData?.timestamp) {
      return Infinity;
    }
    return Date.now() - new Date(this.clipboardData.timestamp).getTime();
  }

  /**
   * Check if clipboard data is stale (older than 1 hour)
   */
  isClipboardStale(): boolean {
    const ONE_HOUR = 60 * 60 * 1000;
    return this.getClipboardAge() > ONE_HOUR;
  }

  /**
   * Save clipboard data to localStorage for persistence across sessions
   */
  private saveToStorage(): void {
    if (this.clipboardData) {
      try {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.clipboardData));
      } catch (error) {
        console.warn('Failed to save clipboard data to localStorage:', error);
      }
    }
  }

  /**
   * Restore clipboard data from localStorage
   */
  private restoreFromStorage(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const data = JSON.parse(stored) as ClipboardData;
        // Only restore if not stale
        if (!this.isDataStale(data)) {
          this.clipboardData = data;
        } else {
          localStorage.removeItem(this.STORAGE_KEY);
        }
      }
    } catch (error) {
      console.warn('Failed to restore clipboard data from localStorage:', error);
      localStorage.removeItem(this.STORAGE_KEY);
    }
  }

  /**
   * Check if stored data is stale
   */
  private isDataStale(data: ClipboardData): boolean {
    const ONE_HOUR = 60 * 60 * 1000;
    const age = Date.now() - new Date(data.timestamp).getTime();
    return age > ONE_HOUR;
  }
}
