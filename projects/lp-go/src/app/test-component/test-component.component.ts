import { Component, OnInit, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'app-test-component',
  standalone: true,
  imports: [CommonModule, IonicModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  template: `
    <div class="p-4 bg-white rounded shadow">
      <h2 class="text-xl font-bold mb-4">Test Component</h2>
      <p class="mb-4">This is a test component to verify that components are working correctly.</p>

      <div class="mb-4">
        <h3 class="text-lg font-semibold mb-2">Ionic Button Test:</h3>
        <ion-button>Ionic Button</ion-button>
      </div>

      <div class="mb-4">
        <h3 class="text-lg font-semibold mb-2">Regular Button Test:</h3>
        <button class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded">Regular Button</button>
      </div>

      <div #buttonContainer class="mb-4">
        <h3 class="text-lg font-semibold mb-2">Dynamic Button Test:</h3>
        <!-- Button will be inserted here dynamically -->
      </div>

      <div class="mt-4 p-4 bg-gray-100 rounded">
        <p>Testing component rendering...</p>
      </div>
    </div>
  `,
})
export class TestComponentComponent implements AfterViewInit {
  @ViewChild('buttonContainer') buttonContainer!: ElementRef;

  ngAfterViewInit() {
    // Try to create a button element dynamically
    setTimeout(() => {
      try {
        // Create a regular button as a fallback
        const regularButton = document.createElement('button');
        regularButton.textContent = 'Fallback Button';
        regularButton.className = 'bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded';
        this.buttonContainer.nativeElement.appendChild(regularButton);

        console.log('Button added to container');
      } catch (error) {
        console.error('Error creating button:', error);
      }
    }, 100);
  }
}
