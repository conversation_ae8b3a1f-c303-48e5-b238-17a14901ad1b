import { AppConfig, PageConfig, BuilderComponent, ConfigurationStatus } from '../../app/builder/models/app-config.interface';

/**
 * Mock data factory for testing
 */
export class MockAppConfigFactory {
  private static idCounter = 1;

  static generateId(): string {
    return `test-id-${this.idCounter++}`;
  }

  static createMockAppConfig(overrides?: Partial<AppConfig>): AppConfig {
    return {
      id: this.generateId(),
      name: 'Test Configuration',
      description: 'Test configuration for unit testing',
      version: '1.0.0',
      clientId: 'test-client-123',
      pages: [this.createMockPageConfig()],
      components: {},
      globalSettings: {
        theme: 'default',
        primaryColor: '#007bff'
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: ConfigurationStatus.DRAFT,
      ...overrides
    };
  }

  static createMockPageConfig(overrides?: Partial<PageConfig>): PageConfig {
    return {
      id: this.generateId(),
      name: 'test-page',
      title: 'Test Page',
      description: 'Test page for unit testing',
      route: '/test-page',
      components: [],
      layout: {
        type: 'default',
        properties: {}
      },
      ...overrides
    };
  }

  static createMockBuilderComponent(overrides?: Partial<BuilderComponent>): BuilderComponent {
    return {
      id: this.generateId(),
      type: 'test-component',
      displayName: 'Test Component',
      properties: {
        text: 'Test text',
        color: '#000000'
      },
      children: [],
      position: {
        x: 100,
        y: 100,
        width: 200,
        height: 100,
        zIndex: 1
      },
      constraints: {
        allowedParents: ['*'],
        allowedChildren: ['*'],
        maxChildren: 10
      },
      ...overrides
    };
  }

  static createLargeConfiguration(): AppConfig {
    const config = this.createMockAppConfig({
      name: 'Large Test Configuration',
      description: 'Configuration with many components for performance testing'
    });

    // Add 100 components for performance testing
    for (let i = 0; i < 100; i++) {
      const component = this.createMockBuilderComponent({
        type: `component-type-${i % 10}`,
        displayName: `Component ${i}`,
        position: {
          x: (i % 10) * 250,
          y: Math.floor(i / 10) * 150,
          width: 200,
          height: 100,
          zIndex: i
        }
      });
      config.components[component.id] = component;
      config.pages[0].components.push(component.id);
    }

    return config;
  }

  static createConfigurationWithHierarchy(): AppConfig {
    const config = this.createMockAppConfig({
      name: 'Hierarchical Test Configuration'
    });

    // Create parent container
    const container = this.createMockBuilderComponent({
      type: 'container',
      displayName: 'Container Component',
      constraints: {
        allowedParents: ['*'],
        allowedChildren: ['text', 'button', 'image'],
        maxChildren: 5
      }
    });

    // Create child components
    const textComponent = this.createMockBuilderComponent({
      type: 'text',
      displayName: 'Text Component',
      parentId: container.id,
      position: { x: 10, y: 10, width: 180, height: 30, zIndex: 2 }
    });

    const buttonComponent = this.createMockBuilderComponent({
      type: 'button',
      displayName: 'Button Component',
      parentId: container.id,
      position: { x: 10, y: 50, width: 100, height: 40, zIndex: 3 }
    });

    // Update container children
    container.children = [textComponent.id, buttonComponent.id];

    // Add to configuration
    config.components[container.id] = container;
    config.components[textComponent.id] = textComponent;
    config.components[buttonComponent.id] = buttonComponent;
    config.pages[0].components = [container.id];

    return config;
  }
}