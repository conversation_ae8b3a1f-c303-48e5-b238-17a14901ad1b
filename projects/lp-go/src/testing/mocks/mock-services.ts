import { Injectable } from '@angular/core';
import { Observable, of, BehaviorSubject, throwError } from 'rxjs';
import { delay } from 'rxjs/operators';
import { AppConfig, BuilderComponent } from '../../app/builder/models/app-config.interface';
import { MockAppConfigFactory } from './mock-app-config';

/**
 * Mock BuilderConfigService for testing
 */
@Injectable()
export class MockBuilderConfigService {
  private configsSubject = new BehaviorSubject<AppConfig[]>([]);
  private currentConfigSubject = new BehaviorSubject<AppConfig | null>(null);

  configs$ = this.configsSubject.asObservable();
  currentConfig$ = this.currentConfigSubject.asObservable();

  saveConfiguration = jasmine.createSpy('saveConfiguration').and.callFake((config: AppConfig) => {
    return of({ ...config, updatedAt: new Date().toISOString() }).pipe(delay(100));
  });

  loadConfiguration = jasmine.createSpy('loadConfiguration').and.callFake((id: string) => {
    const config = MockAppConfigFactory.createMockAppConfig({ id });
    this.currentConfigSubject.next(config);
    return of(config).pipe(delay(100));
  });

  loadConfigurations = jasmine.createSpy('loadConfigurations').and.callFake(() => {
    const configs = [
      MockAppConfigFactory.createMockAppConfig({ name: 'Config 1' }),
      MockAppConfigFactory.createMockAppConfig({ name: 'Config 2' })
    ];
    this.configsSubject.next(configs);
    return of(configs).pipe(delay(100));
  });

  deleteConfiguration = jasmine.createSpy('deleteConfiguration').and.returnValue(of(true).pipe(delay(100)));

  exportConfiguration = jasmine.createSpy('exportConfiguration').and.callFake((config: AppConfig) => {
    return of(JSON.stringify(config, null, 2)).pipe(delay(100));
  });

  importConfiguration = jasmine.createSpy('importConfiguration').and.callFake((data: string) => {
    const config = JSON.parse(data) as AppConfig;
    return of(config).pipe(delay(100));
  });

  validateConfiguration = jasmine.createSpy('validateConfiguration').and.callFake((config: AppConfig) => {
    return of({ isValid: true, errors: [] }).pipe(delay(50));
  });
}

/**
 * Mock ComponentStoreService for testing
 */
@Injectable()
export class MockComponentStoreService {
  private componentsSubject = new BehaviorSubject<Record<string, BuilderComponent>>({});
  private selectedComponentSubject = new BehaviorSubject<string | null>(null);

  components$ = this.componentsSubject.asObservable();
  selectedComponent$ = this.selectedComponentSubject.asObservable();

  addComponent = jasmine.createSpy('addComponent').and.callFake((component: BuilderComponent) => {
    const current = this.componentsSubject.value;
    this.componentsSubject.next({ ...current, [component.id]: component });
    return of(component);
  });

  updateComponent = jasmine.createSpy('updateComponent').and.callFake((id: string, updates: Partial<BuilderComponent>) => {
    const current = this.componentsSubject.value;
    const updated = { ...current[id], ...updates };
    this.componentsSubject.next({ ...current, [id]: updated });
    return of(updated);
  });

  removeComponent = jasmine.createSpy('removeComponent').and.callFake((id: string) => {
    const current = this.componentsSubject.value;
    const { [id]: removed, ...remaining } = current;
    this.componentsSubject.next(remaining);
    return of(true);
  });

  selectComponent = jasmine.createSpy('selectComponent').and.callFake((id: string | null) => {
    this.selectedComponentSubject.next(id);
    return of(id);
  });

  getComponent = jasmine.createSpy('getComponent').and.callFake((id: string) => {
    return of(this.componentsSubject.value[id] || null);
  });

  getComponentHierarchy = jasmine.createSpy('getComponentHierarchy').and.returnValue(of([]));

  duplicateComponent = jasmine.createSpy('duplicateComponent').and.callFake((id: string) => {
    const original = this.componentsSubject.value[id];
    if (original) {
      const duplicate = MockAppConfigFactory.createMockBuilderComponent({
        ...original,
        displayName: `${original.displayName} (Copy)`
      });
      return this.addComponent(duplicate);
    }
    return throwError(() => new Error('Component not found'));
  });
}

/**
 * Mock BridgeService for testing
 */
@Injectable()
export class MockBridgeService {
  private connectionStatusSubject = new BehaviorSubject<boolean>(false);
  
  connectionStatus$ = this.connectionStatusSubject.asObservable();

  connect = jasmine.createSpy('connect').and.callFake(() => {
    this.connectionStatusSubject.next(true);
    return of({ connected: true, clientId: 'test-client' }).pipe(delay(100));
  });

  disconnect = jasmine.createSpy('disconnect').and.callFake(() => {
    this.connectionStatusSubject.next(false);
    return of({ disconnected: true }).pipe(delay(100));
  });

  syncConfiguration = jasmine.createSpy('syncConfiguration').and.callFake((config: AppConfig) => {
    return of({ success: true, syncId: 'sync-123' }).pipe(delay(200));
  });

  sendMessage = jasmine.createSpy('sendMessage').and.callFake((message: any) => {
    return of({ sent: true, messageId: 'msg-123' }).pipe(delay(100));
  });

  subscribeToMessages = jasmine.createSpy('subscribeToMessages').and.returnValue(of({}));
}

/**
 * Mock StorageService for testing
 */
@Injectable()
export class MockStorageService {
  private storage = new Map<string, any>();

  get = jasmine.createSpy('get').and.callFake((key: string) => {
    return of(this.storage.get(key) || null);
  });

  set = jasmine.createSpy('set').and.callFake((key: string, value: any) => {
    this.storage.set(key, value);
    return of(true);
  });

  remove = jasmine.createSpy('remove').and.callFake((key: string) => {
    const existed = this.storage.has(key);
    this.storage.delete(key);
    return of(existed);
  });

  clear = jasmine.createSpy('clear').and.callFake(() => {
    this.storage.clear();
    return of(true);
  });

  has = jasmine.createSpy('has').and.callFake((key: string) => {
    return of(this.storage.has(key));
  });

  keys = jasmine.createSpy('keys').and.callFake(() => {
    return of(Array.from(this.storage.keys()));
  });
}

/**
 * Mock NotificationService for testing
 */
@Injectable()
export class MockNotificationService {
  show = jasmine.createSpy('show');
  hide = jasmine.createSpy('hide');
  clear = jasmine.createSpy('clear');
  success = jasmine.createSpy('success');
  error = jasmine.createSpy('error');
  warning = jasmine.createSpy('warning');
  info = jasmine.createSpy('info');
}

/**
 * Mock ValidationService for testing
 */
@Injectable()
export class MockValidationService {
  validateComponent = jasmine.createSpy('validateComponent').and.callFake((component: BuilderComponent) => {
    return of({ isValid: true, errors: [] }).pipe(delay(50));
  });

  validateConfiguration = jasmine.createSpy('validateConfiguration').and.callFake((config: AppConfig) => {
    return of({ isValid: true, errors: [], warnings: [] }).pipe(delay(100));
  });

  validateProperty = jasmine.createSpy('validateProperty').and.callFake((value: any, schema: any) => {
    return of({ isValid: true, error: null }).pipe(delay(25));
  });
}