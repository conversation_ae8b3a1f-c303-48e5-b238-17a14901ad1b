import { TestBed } from '@angular/core/testing';
import { fakeAsync, tick } from '@angular/core/testing';
import { BuilderConfigService } from '../../app/builder/services/builder-config.service';
import { ComponentStoreService } from '../../app/builder/services/component-store.service';
import { BridgeService } from '../../app/builder/services/bridge.service';
import { StorageService } from '../../app/shared/services/storage.service';
import { 
  MockAppConfigFactory, 
  TestDataLoader, 
  BuilderTestingModule,
  PerformanceTestUtils
} from '../index';
import { AppConfig } from '../../app/builder/models/app-config.interface';

describe('Builder Workflow Integration', () => {
  let configService: BuilderConfigService;
  let componentStore: ComponentStoreService;
  let bridgeService: BridgeService;
  let storageService: StorageService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BuilderTestingModule],
      providers: [
        BuilderConfigService,
        ComponentStoreService,
        BridgeService,
        StorageService
      ]
    }).compileComponents();

    configService = TestBed.inject(BuilderConfigService);
    componentStore = TestBed.inject(ComponentStoreService);
    bridgeService = TestBed.inject(BridgeService);
    storageService = TestBed.inject(StorageService);
  });

  describe('Configuration Management Integration', () => {
    it('should create and save a complete configuration', fakeAsync(() => {
      let savedConfig: AppConfig | null = null;

      // Create a new configuration
      const newConfig = MockAppConfigFactory.createMockAppConfig({
        name: 'Integration Test Config'
      });

      // Save configuration
      configService.saveConfiguration(newConfig).subscribe(result => {
        savedConfig = result;
      });

      tick();

      expect(savedConfig).toBeTruthy();
      expect(savedConfig?.name).toBe('Integration Test Config');
    }));

    it('should load configuration and populate component store', fakeAsync(() => {
      const testConfig = TestDataLoader.getComplexConfiguration();
      let loadedComponents: Record<string, any> = {};

      // First save the configuration
      configService.saveConfiguration(testConfig).subscribe();
      tick();

      // Load configuration
      configService.loadConfiguration(testConfig.id).subscribe(config => {
        // Populate component store
        Object.values(config.components).forEach(component => {
          componentStore.addComponent(component).subscribe();
        });
      });

      tick();

      // Verify components are loaded in store
      componentStore.components$.subscribe(components => {
        loadedComponents = components;
      });

      tick();

      expect(Object.keys(loadedComponents).length).toBeGreaterThan(0);
      expect(loadedComponents[Object.keys(testConfig.components)[0]]).toBeTruthy();
    }));

    it('should handle configuration changes and auto-save', fakeAsync(() => {
      const testConfig = TestDataLoader.getBasicConfiguration();
      let autoSaveTriggered = false;

      // Load initial configuration
      configService.loadConfiguration(testConfig.id).subscribe();
      tick();

      // Setup auto-save spy
      spyOn(configService, 'saveConfiguration').and.callFake(() => {
        autoSaveTriggered = true;
        return configService.saveConfiguration(testConfig);
      });

      // Make a change to a component
      const firstComponentId = Object.keys(testConfig.components)[0];
      componentStore.updateComponent(firstComponentId, {
        properties: { text: 'Updated text' }
      }).subscribe();

      tick(5000); // Wait for auto-save delay

      expect(autoSaveTriggered).toBe(true);
    }));
  });

  describe('Component Operations Integration', () => {
    it('should add component and sync with bridge', fakeAsync(() => {
      let bridgeSyncCalled = false;
      const newComponent = MockAppConfigFactory.createMockBuilderComponent();

      // Setup bridge sync spy
      spyOn(bridgeService, 'syncConfiguration').and.callFake(() => {
        bridgeSyncCalled = true;
        return bridgeService.syncConfiguration({} as AppConfig);
      });

      // Add component
      componentStore.addComponent(newComponent).subscribe();
      tick(500); // Wait for sync throttle

      expect(bridgeSyncCalled).toBe(true);
    }));

    it('should handle component hierarchy operations', fakeAsync(() => {
      const hierarchyConfig = TestDataLoader.createNestedConfiguration(3);
      const components = Object.values(hierarchyConfig.components);
      let hierarchyBuilt = false;

      // Add all components
      components.forEach(component => {
        componentStore.addComponent(component).subscribe();
      });

      tick();

      // Build and verify hierarchy
      componentStore.getComponentHierarchy().subscribe(hierarchy => {
        hierarchyBuilt = true;
        expect(hierarchy.length).toBeGreaterThan(0);
        expect(hierarchy[0].children).toBeDefined();
      });

      tick();

      expect(hierarchyBuilt).toBe(true);
    }));

    it('should handle component constraints validation across services', fakeAsync(() => {
      const container = MockAppConfigFactory.createMockBuilderComponent({
        type: 'container',
        constraints: {
          allowedParents: ['*'],
          allowedChildren: ['text', 'button'],
          maxChildren: 2
        }
      });

      const validChild = MockAppConfigFactory.createMockBuilderComponent({
        type: 'text',
        parentId: container.id
      });

      const invalidChild = MockAppConfigFactory.createMockBuilderComponent({
        type: 'image', // Not allowed
        parentId: container.id
      });

      let validationResults: any[] = [];

      // Add container
      componentStore.addComponent(container).subscribe();
      tick();

      // Add valid child
      componentStore.addComponent(validChild).subscribe(() => {
        componentStore.validateComponentConstraints(validChild.id).subscribe(result => {
          validationResults.push({ component: 'valid', result });
        });
      });

      tick();

      // Try to add invalid child
      componentStore.addComponent(invalidChild).subscribe(() => {
        componentStore.validateComponentConstraints(invalidChild.id).subscribe(result => {
          validationResults.push({ component: 'invalid', result });
        });
      });

      tick();

      expect(validationResults.length).toBe(2);
      expect(validationResults[0].result.isValid).toBe(true);
      expect(validationResults[1].result.isValid).toBe(false);
    }));
  });

  describe('Storage and Caching Integration', () => {
    it('should cache configurations in storage service', fakeAsync(() => {
      const testConfig = TestDataLoader.getBasicConfiguration();
      let cachedData: any = null;

      // Save configuration (should cache it)
      configService.saveConfiguration(testConfig).subscribe();
      tick();

      // Retrieve from cache
      storageService.get(`config_${testConfig.id}`).subscribe(data => {
        cachedData = data;
      });

      tick();

      expect(cachedData).toBeTruthy();
      expect(cachedData.id).toBe(testConfig.id);
    }));

    it('should handle offline mode with cached data', fakeAsync(() => {
      const testConfig = TestDataLoader.getBasicConfiguration();
      let offlineData: AppConfig | null = null;

      // First, cache the configuration
      storageService.set(`config_${testConfig.id}`, testConfig).subscribe();
      tick();

      // Simulate offline mode - try to load from cache
      configService.getConfigurationFromCache(testConfig.id).subscribe(config => {
        offlineData = config;
      });

      tick();

      expect(offlineData).toEqual(testConfig);
    }));

    it('should sync cached changes when coming back online', fakeAsync(() => {
      const testConfig = TestDataLoader.getBasicConfiguration();
      let syncCompleted = false;

      // Simulate offline changes
      const modifiedConfig = { ...testConfig, name: 'Modified Offline' };
      storageService.set(`config_${testConfig.id}`, modifiedConfig).subscribe();
      tick();

      // Simulate coming back online and syncing
      configService.syncOfflineChanges().subscribe(result => {
        syncCompleted = true;
      });

      tick();

      expect(syncCompleted).toBe(true);
    }));
  });

  describe('Multi-Component Operations Integration', () => {
    it('should handle bulk operations across multiple components', fakeAsync(() => {
      const components = [
        MockAppConfigFactory.createMockBuilderComponent(),
        MockAppConfigFactory.createMockBuilderComponent(),
        MockAppConfigFactory.createMockBuilderComponent()
      ];

      let bulkUpdateCompleted = false;

      // Add all components
      components.forEach(component => {
        componentStore.addComponent(component).subscribe();
      });

      tick();

      // Perform bulk update
      const componentIds = components.map(c => c.id);
      const updates = { properties: { backgroundColor: '#ff0000' } };

      componentStore.bulkUpdateComponents(componentIds, updates).subscribe(results => {
        bulkUpdateCompleted = true;
        expect(results.length).toBe(3);
        results.forEach(component => {
          expect(component.properties.backgroundColor).toBe('#ff0000');
        });
      });

      tick();

      expect(bulkUpdateCompleted).toBe(true);
    }));

    it('should handle copy/paste operations with component relationships', fakeAsync(() => {
      const hierarchyConfig = TestDataLoader.createNestedConfiguration(2);
      const components = Object.values(hierarchyConfig.components);
      let pasteCompleted = false;

      // Add original hierarchy
      components.forEach(component => {
        componentStore.addComponent(component).subscribe();
      });

      tick();

      // Copy the root component (should include children)
      const rootComponent = components.find(c => !c.parentId);
      if (rootComponent) {
        componentStore.copyComponentWithChildren(rootComponent.id).subscribe(copiedData => {
          // Paste the copied hierarchy
          componentStore.pasteComponents(copiedData).subscribe(pastedComponents => {
            pasteCompleted = true;
            expect(pastedComponents.length).toBeGreaterThan(1); // Should include children
          });
        });
      }

      tick();

      expect(pasteCompleted).toBe(true);
    }));
  });

  describe('Error Handling Integration', () => {
    it('should handle service errors gracefully', fakeAsync(() => {
      let errorHandled = false;

      // Force an error in the config service
      spyOn(configService, 'loadConfiguration').and.throwError('Network error');

      // Attempt to load configuration
      configService.loadConfiguration('invalid-id').subscribe({
        next: () => fail('Should have failed'),
        error: (error) => {
          errorHandled = true;
          expect(error.message).toContain('Network error');
        }
      });

      tick();

      expect(errorHandled).toBe(true);
    }));

    it('should recover from bridge connection failures', fakeAsync(() => {
      let reconnectionAttempted = false;

      // Simulate bridge connection failure
      spyOn(bridgeService, 'connect').and.throwError('Connection failed');
      spyOn(bridgeService, 'reconnect').and.callFake(() => {
        reconnectionAttempted = true;
        return bridgeService.reconnect();
      });

      // Attempt connection
      bridgeService.connect().subscribe({
        error: () => {
          // Should trigger reconnection
          bridgeService.reconnect().subscribe();
        }
      });

      tick();

      expect(reconnectionAttempted).toBe(true);
    }));
  });

  describe('Performance Integration Tests', () => {
    it('should handle large configuration loading efficiently', async () => {
      const largeConfig = TestDataLoader.getLargeConfiguration();
      
      const { duration } = await PerformanceTestUtils.measureExecutionTime(async () => {
        // Load large configuration
        await configService.saveConfiguration(largeConfig).toPromise();
        await configService.loadConfiguration(largeConfig.id).toPromise();
        
        // Populate component store
        const components = Object.values(largeConfig.components);
        for (const component of components) {
          await componentStore.addComponent(component).toPromise();
        }
      }, 'Large configuration workflow');

      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should handle concurrent operations efficiently', async () => {
      const components = Array.from({ length: 50 }, () => 
        MockAppConfigFactory.createMockBuilderComponent()
      );

      const { duration } = await PerformanceTestUtils.measureExecutionTime(async () => {
        // Perform concurrent component operations
        const operations = components.map(component => 
          componentStore.addComponent(component).toPromise()
        );

        await Promise.all(operations);
      }, 'Concurrent component operations');

      expect(duration).toBeLessThan(2000); // Should complete within 2 seconds
    });
  });

  describe('Data Consistency Integration', () => {
    it('should maintain data consistency across services', fakeAsync(() => {
      const testConfig = TestDataLoader.getBasicConfiguration();
      let configConsistent = false;

      // Load configuration
      configService.loadConfiguration(testConfig.id).subscribe(config => {
        // Populate component store
        Object.values(config.components).forEach(component => {
          componentStore.addComponent(component).subscribe();
        });

        tick();

        // Verify consistency between config and store
        componentStore.components$.subscribe(storeComponents => {
          const configComponentIds = Object.keys(config.components);
          const storeComponentIds = Object.keys(storeComponents);
          
          configConsistent = configComponentIds.every(id => 
            storeComponentIds.includes(id)
          );
        });
      });

      tick();

      expect(configConsistent).toBe(true);
    }));

    it('should handle concurrent modifications safely', fakeAsync(() => {
      const component = MockAppConfigFactory.createMockBuilderComponent();
      let modificationsCompleted = 0;

      // Add initial component
      componentStore.addComponent(component).subscribe();
      tick();

      // Simulate concurrent modifications
      const modification1 = { properties: { text: 'Modification 1' } };
      const modification2 = { properties: { color: '#ff0000' } };

      componentStore.updateComponent(component.id, modification1).subscribe(() => {
        modificationsCompleted++;
      });

      componentStore.updateComponent(component.id, modification2).subscribe(() => {
        modificationsCompleted++;
      });

      tick();

      expect(modificationsCompleted).toBe(2);

      // Verify final state includes both modifications
      componentStore.getComponent(component.id).subscribe(finalComponent => {
        expect(finalComponent?.properties.text).toBe('Modification 1');
        expect(finalComponent?.properties.color).toBe('#ff0000');
      });

      tick();
    }));
  });
});