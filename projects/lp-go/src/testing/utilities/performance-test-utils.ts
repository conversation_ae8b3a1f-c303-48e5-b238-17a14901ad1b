/**
 * Performance testing utilities for measuring and validating performance metrics
 */
export class PerformanceTestUtils {
  private static readonly DEFAULT_TIMEOUT = 5000;
  private static readonly DEFAULT_PERFORMANCE_BUDGET = {
    renderTime: 100,
    operationTime: 50,
    memoryUsage: 50 * 1024 * 1024, // 50MB
    bundleSize: 2 * 1024 * 1024 // 2MB
  };

  /**
   * Measures the execution time of a function
   */
  static async measureExecutionTime<T>(
    operation: () => Promise<T> | T,
    operationName: string = 'operation'
  ): Promise<{ result: T; duration: number }> {
    const startTime = performance.now();
    const result = await operation();
    const duration = performance.now() - startTime;

    console.log(`${operationName} took ${duration.toFixed(2)}ms`);
    
    return { result, duration };
  }

  /**
   * Measures memory usage before and after an operation
   */
  static async measureMemoryUsage<T>(
    operation: () => Promise<T> | T,
    operationName: string = 'operation'
  ): Promise<{ result: T; memoryDelta: number }> {
    // Force garbage collection if available (Chrome DevTools)
    if ('gc' in window && typeof (window as any).gc === 'function') {
      (window as any).gc();
    }

    const beforeMemory = this.getMemoryUsage();
    const result = await operation();
    
    // Force garbage collection again
    if ('gc' in window && typeof (window as any).gc === 'function') {
      (window as any).gc();
    }
    
    const afterMemory = this.getMemoryUsage();
    const memoryDelta = afterMemory - beforeMemory;

    console.log(`${operationName} memory delta: ${(memoryDelta / 1024 / 1024).toFixed(2)}MB`);
    
    return { result, memoryDelta };
  }

  /**
   * Gets current memory usage (if available)
   */
  private static getMemoryUsage(): number {
    if ('memory' in performance && (performance as any).memory) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }

  /**
   * Validates that an operation meets performance budgets
   */
  static validatePerformanceBudget(
    metrics: { duration?: number; memoryDelta?: number },
    budgets: Partial<typeof PerformanceTestUtils.DEFAULT_PERFORMANCE_BUDGET> = {}
  ): { passed: boolean; violations: string[] } {
    const budget = { ...this.DEFAULT_PERFORMANCE_BUDGET, ...budgets };
    const violations: string[] = [];

    if (metrics.duration !== undefined && metrics.duration > budget.operationTime) {
      violations.push(`Operation time ${metrics.duration.toFixed(2)}ms exceeds budget ${budget.operationTime}ms`);
    }

    if (metrics.memoryDelta !== undefined && metrics.memoryDelta > budget.memoryUsage) {
      violations.push(`Memory usage ${(metrics.memoryDelta / 1024 / 1024).toFixed(2)}MB exceeds budget ${(budget.memoryUsage / 1024 / 1024).toFixed(2)}MB`);
    }

    return {
      passed: violations.length === 0,
      violations
    };
  }

  /**
   * Simulates heavy load by creating many components
   */
  static async simulateHeavyLoad(
    componentCount: number,
    componentFactory: () => any,
    batchSize: number = 10
  ): Promise<any[]> {
    const components: any[] = [];
    
    for (let i = 0; i < componentCount; i += batchSize) {
      const batch = Math.min(batchSize, componentCount - i);
      
      for (let j = 0; j < batch; j++) {
        components.push(componentFactory());
      }
      
      // Allow browser to process between batches
      await new Promise(resolve => setTimeout(resolve, 0));
    }
    
    return components;
  }

  /**
   * Measures frame rate during an operation
   */
  static async measureFrameRate(
    operation: () => Promise<void> | void,
    duration: number = 1000
  ): Promise<{ averageFrameRate: number; frameRates: number[] }> {
    const frameRates: number[] = [];
    let lastFrameTime = performance.now();
    let isRunning = true;

    const frameCallback = (currentTime: number) => {
      if (!isRunning) return;

      const frameDuration = currentTime - lastFrameTime;
      const frameRate = 1000 / frameDuration;
      frameRates.push(frameRate);
      lastFrameTime = currentTime;

      requestAnimationFrame(frameCallback);
    };

    requestAnimationFrame(frameCallback);

    // Start the operation
    const operationPromise = Promise.resolve(operation());

    // Wait for specified duration
    await new Promise(resolve => setTimeout(resolve, duration));
    
    // Stop measuring
    isRunning = false;
    
    // Wait for operation to complete
    await operationPromise;

    const averageFrameRate = frameRates.reduce((sum, rate) => sum + rate, 0) / frameRates.length;

    return { averageFrameRate, frameRates };
  }

  /**
   * Monitors long tasks (tasks that block the main thread for > 50ms)
   */
  static monitorLongTasks(
    operation: () => Promise<void> | void,
    threshold: number = 50
  ): Promise<{ longTasks: PerformanceEntry[]; totalBlockingTime: number }> {
    return new Promise(async (resolve) => {
      const longTasks: PerformanceEntry[] = [];
      let totalBlockingTime = 0;

      // Create a PerformanceObserver to monitor long tasks
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.duration > threshold) {
              longTasks.push(entry);
              totalBlockingTime += entry.duration - threshold;
            }
          }
        });

        try {
          observer.observe({ entryTypes: ['longtask'] });
        } catch (e) {
          // Long task API not supported
        }

        // Execute operation
        await Promise.resolve(operation());

        // Stop observing
        observer.disconnect();
      } else {
        // Fallback: just execute the operation
        await Promise.resolve(operation());
      }

      resolve({ longTasks, totalBlockingTime });
    });
  }

  /**
   * Creates a performance test suite
   */
  static createPerformanceTestSuite(name: string) {
    return {
      name,
      tests: [] as Array<{
        name: string;
        operation: () => Promise<any> | any;
        budget?: Partial<typeof PerformanceTestUtils.DEFAULT_PERFORMANCE_BUDGET>;
      }>,

      addTest(
        testName: string,
        operation: () => Promise<any> | any,
        budget?: Partial<typeof PerformanceTestUtils.DEFAULT_PERFORMANCE_BUDGET>
      ) {
        this.tests.push({ name: testName, operation, budget });
      },

      async run() {
        const results: Array<{
          name: string;
          duration: number;
          memoryDelta: number;
          passed: boolean;
          violations: string[];
        }> = [];

        for (const test of this.tests) {
          console.log(`Running performance test: ${test.name}`);

          const { duration } = await PerformanceTestUtils.measureExecutionTime(
            test.operation,
            test.name
          );

          const { memoryDelta } = await PerformanceTestUtils.measureMemoryUsage(
            () => Promise.resolve(),
            `${test.name} cleanup`
          );

          const validation = PerformanceTestUtils.validatePerformanceBudget(
            { duration, memoryDelta },
            test.budget
          );

          results.push({
            name: test.name,
            duration,
            memoryDelta,
            passed: validation.passed,
            violations: validation.violations
          });

          if (!validation.passed) {
            console.warn(`Performance test "${test.name}" failed:`, validation.violations);
          }
        }

        return results;
      }
    };
  }

  /**
   * Generates a performance report
   */
  static generatePerformanceReport(results: Array<{
    name: string;
    duration: number;
    memoryDelta: number;
    passed: boolean;
    violations: string[];
  }>): string {
    const totalTests = results.length;
    const passedTests = results.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;

    let report = `\n=== Performance Test Report ===\n`;
    report += `Total Tests: ${totalTests}\n`;
    report += `Passed: ${passedTests}\n`;
    report += `Failed: ${failedTests}\n`;
    report += `Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%\n\n`;

    results.forEach(result => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      report += `${status} ${result.name}\n`;
      report += `  Duration: ${result.duration.toFixed(2)}ms\n`;
      report += `  Memory: ${(result.memoryDelta / 1024 / 1024).toFixed(2)}MB\n`;
      
      if (result.violations.length > 0) {
        report += `  Violations:\n`;
        result.violations.forEach(violation => {
          report += `    - ${violation}\n`;
        });
      }
      report += '\n';
    });

    return report;
  }
}