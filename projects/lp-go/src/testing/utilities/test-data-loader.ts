import { AppConfig } from '../../app/builder/models/app-config.interface';
import testConfigurations from '../fixtures/test-configurations.json';

/**
 * Utility for loading test data and fixtures
 */
export class TestDataLoader {
  private static configurations = testConfigurations as Record<string, AppConfig>;

  /**
   * Gets a test configuration by name
   */
  static getConfiguration(name: keyof typeof testConfigurations): AppConfig {
    const config = this.configurations[name];
    if (!config) {
      throw new Error(`Test configuration "${name}" not found`);
    }
    return JSON.parse(JSON.stringify(config)); // Deep clone to avoid mutations
  }

  /**
   * Gets all available test configuration names
   */
  static getAvailableConfigurations(): string[] {
    return Object.keys(this.configurations);
  }

  /**
   * Gets a basic configuration for simple tests
   */
  static getBasicConfiguration(): AppConfig {
    return this.getConfiguration('basicConfiguration');
  }

  /**
   * Gets a complex configuration with nested components
   */
  static getComplexConfiguration(): AppConfig {
    return this.getConfiguration('complexConfiguration');
  }

  /**
   * Gets a large configuration for performance testing
   */
  static getLargeConfiguration(): AppConfig {
    const config = this.getConfiguration('largeConfiguration');
    
    // Generate 100 components for performance testing
    for (let i = 0; i < 100; i++) {
      const componentId = `perf-component-${i.toString().padStart(3, '0')}`;
      config.components[componentId] = {
        id: componentId,
        type: `type-${i % 5}`, // 5 different component types
        displayName: `Performance Component ${i}`,
        properties: {
          text: `Component ${i}`,
          backgroundColor: `hsl(${(i * 37) % 360}, 70%, 90%)`,
          index: i
        },
        children: [],
        position: {
          x: (i % 10) * 120,
          y: Math.floor(i / 10) * 80,
          width: 100,
          height: 60,
          zIndex: i + 1
        },
        constraints: {
          allowedParents: ['*'],
          allowedChildren: [],
          maxChildren: 0
        }
      };
      config.pages[0].components.push(componentId);
    }
    
    return config;
  }

  /**
   * Gets an empty configuration for edge case testing
   */
  static getEmptyConfiguration(): AppConfig {
    return this.getConfiguration('emptyConfiguration');
  }

  /**
   * Creates a configuration with a specific number of components
   */
  static createConfigurationWithComponents(componentCount: number): AppConfig {
    const config = this.getBasicConfiguration();
    config.name = `Generated Configuration (${componentCount} components)`;
    config.id = `generated-config-${componentCount}`;
    
    // Clear existing components
    config.components = {};
    config.pages[0].components = [];
    
    // Generate the requested number of components
    for (let i = 0; i < componentCount; i++) {
      const componentId = `generated-component-${i.toString().padStart(4, '0')}`;
      config.components[componentId] = {
        id: componentId,
        type: this.getRandomComponentType(),
        displayName: `Generated Component ${i}`,
        properties: this.getRandomProperties(),
        children: [],
        position: {
          x: (i % 20) * 60,
          y: Math.floor(i / 20) * 60,
          width: 50,
          height: 50,
          zIndex: i + 1
        },
        constraints: {
          allowedParents: ['*'],
          allowedChildren: [],
          maxChildren: 0
        }
      };
      config.pages[0].components.push(componentId);
    }
    
    return config;
  }

  /**
   * Creates a deeply nested configuration for hierarchy testing
   */
  static createNestedConfiguration(depth: number = 5): AppConfig {
    const config = this.getBasicConfiguration();
    config.name = `Nested Configuration (depth: ${depth})`;
    config.id = `nested-config-${depth}`;
    
    // Clear existing components
    config.components = {};
    config.pages[0].components = [];
    
    let currentParentId: string | undefined;
    
    // Create nested structure
    for (let i = 0; i < depth; i++) {
      const componentId = `nested-container-${i}`;
      const component = {
        id: componentId,
        type: 'container',
        displayName: `Container Level ${i}`,
        properties: {
          backgroundColor: `hsl(${i * 60}, 50%, 95%)`,
          padding: 10,
          border: '1px solid #ddd'
        },
        children: [] as string[],
        parentId: currentParentId,
        position: {
          x: i * 20,
          y: i * 20,
          width: 300 - (i * 20),
          height: 200 - (i * 15),
          zIndex: i + 1
        },
        constraints: {
          allowedParents: i === 0 ? ['*'] : ['container'],
          allowedChildren: ['container', 'text', 'button'],
          maxChildren: 5
        }
      };
      
      config.components[componentId] = component;
      
      if (currentParentId) {
        config.components[currentParentId].children.push(componentId);
      } else {
        config.pages[0].components.push(componentId);
      }
      
      currentParentId = componentId;
    }
    
    // Add a leaf component at the deepest level
    if (currentParentId) {
      const leafId = 'leaf-component';
      config.components[leafId] = {
        id: leafId,
        type: 'text',
        displayName: 'Leaf Component',
        properties: {
          text: `Depth: ${depth}`,
          fontSize: 12
        },
        children: [],
        parentId: currentParentId,
        position: {
          x: 10,
          y: 10,
          width: 100,
          height: 30,
          zIndex: depth + 1
        },
        constraints: {
          allowedParents: ['container'],
          allowedChildren: [],
          maxChildren: 0
        }
      };
      config.components[currentParentId].children.push(leafId);
    }
    
    return config;
  }

  /**
   * Gets a random component type for generation
   */
  private static getRandomComponentType(): string {
    const types = ['text', 'button', 'image', 'container', 'input', 'select'];
    return types[Math.floor(Math.random() * types.length)];
  }

  /**
   * Gets random properties for generated components
   */
  private static getRandomProperties(): Record<string, any> {
    return {
      backgroundColor: `hsl(${Math.floor(Math.random() * 360)}, 70%, 90%)`,
      color: `hsl(${Math.floor(Math.random() * 360)}, 80%, 20%)`,
      fontSize: Math.floor(Math.random() * 8) + 12,
      padding: Math.floor(Math.random() * 10) + 5,
      text: `Random text ${Math.floor(Math.random() * 1000)}`
    };
  }

  /**
   * Validates that a configuration matches expected structure
   */
  static validateConfiguration(config: AppConfig): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!config.id) errors.push('Configuration ID is required');
    if (!config.name) errors.push('Configuration name is required');
    if (!config.version) errors.push('Configuration version is required');
    if (!config.clientId) errors.push('Configuration clientId is required');
    if (!config.pages || config.pages.length === 0) {
      errors.push('Configuration must have at least one page');
    }

    // Validate component references
    for (const page of config.pages || []) {
      for (const componentId of page.components || []) {
        if (!config.components[componentId]) {
          errors.push(`Page "${page.name}" references non-existent component "${componentId}"`);
        }
      }
    }

    // Validate component hierarchy
    for (const [componentId, component] of Object.entries(config.components || {})) {
      if (component.parentId && !config.components[component.parentId]) {
        errors.push(`Component "${componentId}" references non-existent parent "${component.parentId}"`);
      }
      
      for (const childId of component.children || []) {
        if (!config.components[childId]) {
          errors.push(`Component "${componentId}" references non-existent child "${childId}"`);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}