import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Type, Provider, DebugElement } from '@angular/core';
import { By } from '@angular/platform-browser';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

/**
 * Utility class for creating and managing component tests
 */
export class ComponentTestHarness {
  /**
   * Creates a component fixture with common testing setup
   */
  static async createComponent<T>(
    component: Type<T>,
    options: {
      providers?: Provider[];
      imports?: any[];
      declarations?: any[];
      schemas?: any[];
    } = {}
  ): Promise<ComponentFixture<T>> {
    const {
      providers = [],
      imports = [],
      declarations = [],
      schemas = []
    } = options;

    await TestBed.configureTestingModule({
      declarations: [component, ...declarations],
      imports: [
        CommonModule,
        NoopAnimationsModule,
        HttpClientTestingModule,
        RouterTestingModule,
        FormsModule,
        ReactiveFormsModule,
        ...imports
      ],
      providers: [
        ...this.getDefaultProviders(),
        ...providers
      ],
      schemas
    }).compileComponents();

    return TestBed.createComponent(component);
  }

  /**
   * Gets default providers for testing
   */
  private static getDefaultProviders(): Provider[] {
    return [
      // Add common testing providers here
    ];
  }

  /**
   * Triggers change detection and waits for async operations
   */
  static async detectChanges<T>(fixture: ComponentFixture<T>): Promise<void> {
    fixture.detectChanges();
    await fixture.whenStable();
  }

  /**
   * Gets an element by data-cy attribute (Cypress testing attribute)
   */
  static getByTestId<T>(fixture: ComponentFixture<T>, testId: string): DebugElement | null {
    return fixture.debugElement.query(By.css(`[data-cy="${testId}"]`));
  }

  /**
   * Gets all elements by data-cy attribute
   */
  static getAllByTestId<T>(fixture: ComponentFixture<T>, testId: string): DebugElement[] {
    return fixture.debugElement.queryAll(By.css(`[data-cy="${testId}"]`));
  }

  /**
   * Clicks an element and triggers change detection
   */
  static async clickElement<T>(fixture: ComponentFixture<T>, element: DebugElement): Promise<void> {
    element.nativeElement.click();
    await this.detectChanges(fixture);
  }

  /**
   * Clicks an element by test ID
   */
  static async clickByTestId<T>(fixture: ComponentFixture<T>, testId: string): Promise<void> {
    const element = this.getByTestId(fixture, testId);
    if (element) {
      await this.clickElement(fixture, element);
    } else {
      throw new Error(`Element with test ID "${testId}" not found`);
    }
  }

  /**
   * Sets input value and triggers change detection
   */
  static async setInputValue<T>(
    fixture: ComponentFixture<T>,
    element: DebugElement,
    value: string
  ): Promise<void> {
    const input = element.nativeElement as HTMLInputElement;
    input.value = value;
    input.dispatchEvent(new Event('input'));
    await this.detectChanges(fixture);
  }

  /**
   * Sets input value by test ID
   */
  static async setInputValueByTestId<T>(
    fixture: ComponentFixture<T>,
    testId: string,
    value: string
  ): Promise<void> {
    const element = this.getByTestId(fixture, testId);
    if (element) {
      await this.setInputValue(fixture, element, value);
    } else {
      throw new Error(`Input element with test ID "${testId}" not found`);
    }
  }

  /**
   * Waits for an element to appear
   */
  static async waitForElement<T>(
    fixture: ComponentFixture<T>,
    testId: string,
    timeout = 5000
  ): Promise<DebugElement> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      await this.detectChanges(fixture);
      const element = this.getByTestId(fixture, testId);
      if (element) {
        return element;
      }
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    throw new Error(`Element with test ID "${testId}" did not appear within ${timeout}ms`);
  }

  /**
   * Waits for an element to disappear
   */
  static async waitForElementToDisappear<T>(
    fixture: ComponentFixture<T>,
    testId: string,
    timeout = 5000
  ): Promise<void> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      await this.detectChanges(fixture);
      const element = this.getByTestId(fixture, testId);
      if (!element) {
        return;
      }
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    throw new Error(`Element with test ID "${testId}" did not disappear within ${timeout}ms`);
  }

  /**
   * Dispatches a keyboard event
   */
  static dispatchKeyboardEvent<T>(
    fixture: ComponentFixture<T>,
    element: DebugElement,
    eventType: string,
    keyCode: number,
    key?: string
  ): void {
    const event = new KeyboardEvent(eventType, {
      key: key || '',
      keyCode,
      bubbles: true
    });
    element.nativeElement.dispatchEvent(event);
  }

  /**
   * Dispatches a drag and drop event
   */
  static dispatchDragEvent<T>(
    fixture: ComponentFixture<T>,
    source: DebugElement,
    target: DebugElement,
    eventType: string
  ): void {
    const dataTransfer = new DataTransfer();
    const event = new DragEvent(eventType, {
      dataTransfer,
      bubbles: true
    });
    
    if (eventType === 'dragstart') {
      source.nativeElement.dispatchEvent(event);
    } else {
      target.nativeElement.dispatchEvent(event);
    }
  }

  /**
   * Simulates a complete drag and drop operation
   */
  static async simulateDragAndDrop<T>(
    fixture: ComponentFixture<T>,
    sourceTestId: string,
    targetTestId: string
  ): Promise<void> {
    const source = this.getByTestId(fixture, sourceTestId);
    const target = this.getByTestId(fixture, targetTestId);

    if (!source || !target) {
      throw new Error('Source or target element not found for drag and drop');
    }

    // Simulate drag start
    this.dispatchDragEvent(fixture, source, target, 'dragstart');
    await this.detectChanges(fixture);

    // Simulate drag over
    this.dispatchDragEvent(fixture, source, target, 'dragover');
    await this.detectChanges(fixture);

    // Simulate drop
    this.dispatchDragEvent(fixture, source, target, 'drop');
    await this.detectChanges(fixture);

    // Simulate drag end
    this.dispatchDragEvent(fixture, source, target, 'dragend');
    await this.detectChanges(fixture);
  }

  /**
   * Checks if element has CSS class
   */
  static hasClass<T>(fixture: ComponentFixture<T>, testId: string, className: string): boolean {
    const element = this.getByTestId(fixture, testId);
    return element ? element.nativeElement.classList.contains(className) : false;
  }

  /**
   * Gets computed style of element
   */
  static getComputedStyle<T>(fixture: ComponentFixture<T>, testId: string): CSSStyleDeclaration {
    const element = this.getByTestId(fixture, testId);
    if (!element) {
      throw new Error(`Element with test ID "${testId}" not found`);
    }
    return window.getComputedStyle(element.nativeElement);
  }
}