{"basicConfiguration": {"id": "basic-config-001", "name": "Basic Test Configuration", "description": "Simple configuration for basic testing scenarios", "version": "1.0.0", "clientId": "test-client-basic", "pages": [{"id": "page-001", "name": "home", "title": "Home Page", "description": "Main landing page", "route": "/home", "components": ["component-001", "component-002"], "layout": {"type": "default", "properties": {"backgroundColor": "#ffffff", "padding": 20}}}], "components": {"component-001": {"id": "component-001", "type": "text", "displayName": "Welcome Text", "properties": {"text": "Welcome to our application", "fontSize": 24, "color": "#333333", "fontWeight": "bold"}, "children": [], "position": {"x": 50, "y": 50, "width": 300, "height": 60, "zIndex": 1}, "constraints": {"allowedParents": ["*"], "allowedChildren": [], "maxChildren": 0}}, "component-002": {"id": "component-002", "type": "button", "displayName": "Get Started But<PERSON>", "properties": {"text": "Get Started", "backgroundColor": "#007bff", "color": "#ffffff", "borderRadius": 4}, "children": [], "position": {"x": 50, "y": 130, "width": 120, "height": 40, "zIndex": 2}, "constraints": {"allowedParents": ["*"], "allowedChildren": [], "maxChildren": 0}}}, "globalSettings": {"theme": "default", "primaryColor": "#007bff", "secondaryColor": "#6c757d"}, "createdAt": "2024-01-15T10:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z", "status": "DRAFT"}, "complexConfiguration": {"id": "complex-config-001", "name": "Complex Test Configuration", "description": "Complex configuration with nested components and multiple pages", "version": "2.1.0", "clientId": "test-client-complex", "pages": [{"id": "page-001", "name": "home", "title": "Home Page", "route": "/home", "components": ["container-001"], "layout": {"type": "grid", "properties": {"columns": 12, "gap": 16}}}, {"id": "page-002", "name": "dashboard", "title": "Dashboard", "route": "/dashboard", "components": ["container-002"], "layout": {"type": "flex", "properties": {"direction": "column", "gap": 24}}}], "components": {"container-001": {"id": "container-001", "type": "container", "displayName": "Main Container", "properties": {"backgroundColor": "#f8f9fa", "padding": 24, "borderRadius": 8}, "children": ["text-001", "form-001"], "position": {"x": 0, "y": 0, "width": 800, "height": 600, "zIndex": 1}, "constraints": {"allowedParents": ["*"], "allowedChildren": ["text", "button", "form", "image"], "maxChildren": 10}}, "text-001": {"id": "text-001", "type": "text", "displayName": "Header Text", "parentId": "container-001", "properties": {"text": "Welcome to Dashboard", "fontSize": 32, "fontWeight": "bold", "textAlign": "center"}, "children": [], "position": {"x": 24, "y": 24, "width": 752, "height": 50, "zIndex": 2}, "constraints": {"allowedParents": ["container"], "allowedChildren": [], "maxChildren": 0}}, "form-001": {"id": "form-001", "type": "form", "displayName": "Contact Form", "parentId": "container-001", "properties": {"title": "Contact Us", "submitUrl": "/api/contact"}, "children": ["input-001", "input-002", "button-001"], "position": {"x": 24, "y": 100, "width": 400, "height": 300, "zIndex": 3}, "constraints": {"allowedParents": ["container"], "allowedChildren": ["input", "textarea", "button", "select"], "maxChildren": 20}}, "input-001": {"id": "input-001", "type": "input", "displayName": "Name Input", "parentId": "form-001", "properties": {"placeholder": "Enter your name", "required": true, "type": "text"}, "children": [], "position": {"x": 20, "y": 50, "width": 360, "height": 40, "zIndex": 4}, "constraints": {"allowedParents": ["form"], "allowedChildren": [], "maxChildren": 0}}, "input-002": {"id": "input-002", "type": "input", "displayName": "Email Input", "parentId": "form-001", "properties": {"placeholder": "Enter your email", "required": true, "type": "email"}, "children": [], "position": {"x": 20, "y": 110, "width": 360, "height": 40, "zIndex": 5}, "constraints": {"allowedParents": ["form"], "allowedChildren": [], "maxChildren": 0}}, "button-001": {"id": "button-001", "type": "button", "displayName": "Submit <PERSON>", "parentId": "form-001", "properties": {"text": "Submit", "type": "submit", "backgroundColor": "#28a745", "color": "#ffffff"}, "children": [], "position": {"x": 20, "y": 170, "width": 100, "height": 40, "zIndex": 6}, "constraints": {"allowedParents": ["form", "container"], "allowedChildren": [], "maxChildren": 0}}, "container-002": {"id": "container-002", "type": "container", "displayName": "Dashboard Container", "properties": {"backgroundColor": "#ffffff", "boxShadow": "0 2px 4px rgba(0,0,0,0.1)"}, "children": ["chart-001", "table-001"], "position": {"x": 0, "y": 0, "width": 1200, "height": 800, "zIndex": 1}, "constraints": {"allowedParents": ["*"], "allowedChildren": ["chart", "table", "text", "button"], "maxChildren": 15}}, "chart-001": {"id": "chart-001", "type": "chart", "displayName": "Sales Chart", "parentId": "container-002", "properties": {"chartType": "line", "dataSource": "/api/sales-data", "title": "Monthly Sales"}, "children": [], "position": {"x": 24, "y": 24, "width": 500, "height": 300, "zIndex": 2}, "constraints": {"allowedParents": ["container"], "allowedChildren": [], "maxChildren": 0}}, "table-001": {"id": "table-001", "type": "table", "displayName": "Data Table", "parentId": "container-002", "properties": {"dataSource": "/api/table-data", "columns": ["name", "email", "status"], "sortable": true, "filterable": true}, "children": [], "position": {"x": 550, "y": 24, "width": 600, "height": 400, "zIndex": 3}, "constraints": {"allowedParents": ["container"], "allowedChildren": [], "maxChildren": 0}}}, "globalSettings": {"theme": "modern", "primaryColor": "#007bff", "secondaryColor": "#6c757d", "spacing": {"small": 8, "medium": 16, "large": 24}, "typography": {"fontFamily": "Inter, sans-serif", "baseFontSize": 16}}, "createdAt": "2024-01-10T14:30:00.000Z", "updatedAt": "2024-01-15T16:45:00.000Z", "status": "PUBLISHED"}, "largeConfiguration": {"id": "large-config-001", "name": "Large Test Configuration", "description": "Configuration with many components for performance testing", "version": "1.0.0", "clientId": "test-client-large", "pages": [{"id": "page-001", "name": "performance-test", "title": "Performance Test Page", "route": "/performance", "components": [], "layout": {"type": "grid", "properties": {"columns": 10, "rows": 10, "gap": 8}}}], "components": {}, "globalSettings": {"theme": "performance", "primaryColor": "#ff6b6b"}, "createdAt": "2024-01-15T09:00:00.000Z", "updatedAt": "2024-01-15T09:00:00.000Z", "status": "DRAFT"}, "emptyConfiguration": {"id": "empty-config-001", "name": "Empty Test Configuration", "description": "Minimal configuration for testing edge cases", "version": "1.0.0", "clientId": "test-client-empty", "pages": [{"id": "page-001", "name": "empty", "title": "Empty Page", "route": "/empty", "components": [], "layout": {"type": "default", "properties": {}}}], "components": {}, "globalSettings": {}, "createdAt": "2024-01-15T12:00:00.000Z", "updatedAt": "2024-01-15T12:00:00.000Z", "status": "DRAFT"}}