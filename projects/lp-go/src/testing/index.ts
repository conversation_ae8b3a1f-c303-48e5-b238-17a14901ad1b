/**
 * Testing utilities and infrastructure for LP-Go Builder
 */

// Mock factories and data
export { MockAppConfigFactory } from './mocks/mock-app-config';
export {
  MockBuilderConfigService,
  MockComponentStoreService,
  MockBridgeService,
  MockStorageService,
  MockNotificationService,
  MockValidationService
} from './mocks/mock-services';

// Test utilities
export { ComponentTestHarness } from './utilities/component-test-harness';
export { PerformanceTestUtils } from './utilities/performance-test-utils';
export { TestDataLoader } from './utilities/test-data-loader';

// Test fixtures
export * from './fixtures/test-configurations.json';

/**
 * Common testing module for builder components
 */
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

@NgModule({
  imports: [
    CommonModule,
    NoopAnimationsModule,
    HttpClientTestingModule,
    RouterTestingModule,
    FormsModule,
    ReactiveFormsModule
  ],
  exports: [
    CommonModule,
    NoopAnimationsModule,
    HttpClientTestingModule,
    RouterTestingModule,
    FormsModule,
    ReactiveFormsModule
  ]
})
export class BuilderTestingModule { }

/**
 * Default test providers for builder testing
 */
import { Provider } from '@angular/core';

export const DEFAULT_TEST_PROVIDERS: Provider[] = [
  // Add common test providers here
];

/**
 * Utility function to set up component tests
 */
export function setupComponentTest<T>(
  component: any,
  options: {
    providers?: Provider[];
    imports?: any[];
    declarations?: any[];
  } = {}
) {
  return ComponentTestHarness.createComponent(component, {
    ...options,
    imports: [BuilderTestingModule, ...(options.imports || [])],
    providers: [...DEFAULT_TEST_PROVIDERS, ...(options.providers || [])]
  });
}

/**
 * Utility function to create performance test suites
 */
export function createPerformanceTestSuite(name: string) {
  return PerformanceTestUtils.createPerformanceTestSuite(name);
}

/**
 * Common test configuration for all builder tests
 */
export const TEST_CONFIG = {
  performance: {
    budgets: {
      renderTime: 100,
      operationTime: 50,
      memoryUsage: 50 * 1024 * 1024, // 50MB
      bundleSize: 2 * 1024 * 1024 // 2MB
    }
  },
  timeouts: {
    default: 5000,
    integration: 10000,
    e2e: 30000
  },
  retries: {
    flaky: 3,
    integration: 2,
    e2e: 1
  }
};