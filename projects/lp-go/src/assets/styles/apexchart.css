/** Apexchart **/
.apexcharts-tooltip {

  /** Dark mode */
  &.apexcharts-theme-dark {
    box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.2);
    border-radius: 0.475rem;
    border: 0 !important;
    background: var(--card) !important;
    color: var(--foreground);

    .apexcharts-tooltip-title {
      background: var(--card) !important;
      font-weight: 500;
      color: var(--foreground);
      border-bottom: 1px solid var(--border) !important;
    }
  }

  .apexcharts-tooltip-title {
    padding: 0.5rem 1rem;
  }

  /** Light mode */
  &.apexcharts-theme-light {
    box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.2);
    border-radius: 0.475rem;
    border: 0 !important;
    background: var(--background) !important;
    color: var(--foreground);

    .apexcharts-tooltip-title {
      background: var(--background) !important;
      font-weight: 500;
      color: var(--foreground);
      border-bottom: 1px solid var(--border) !important;
    }
  }

  .apexcharts-tooltip-title {
    padding: 0.5rem 1rem;
  }
}

.apexcharts-xaxistooltip {

  /** Dark mode */
  &.apexcharts-theme-dark {
    border-radius: 0.475rem;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.3);
    border: 0 !important;
    background: var(--card) !important;
    color: var(--foreground);

    &:before {
      border-bottom: 0 !important;
    }

    &:after {
      border-bottom-color: var(--card) !important;
    }
  }

  /** Light mode */
  &.apexcharts-theme-light {
    border-radius: 0.475rem;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.3);
    border: 0 !important;
    background: var(--background) !important;
    color: var(--foreground);

    &:before {
      border-bottom: 0 !important;
    }

    &:after {
      border-bottom-color: var(--background) !important;
    }
  }
}
