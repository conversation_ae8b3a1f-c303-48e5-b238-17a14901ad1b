<!DOCTYPE html>
<html lang="en" data-theme="base" dir="ltr">
  <head>
    <meta charset="utf-8" />
    <title>Angular Tailwind</title>
    <base href="/" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/x-icon" href="favicon.ico" />
    
    <!-- Direct styles for Angular CDK - v3 -->
    <style>
      /* Global CDK styles with high specificity to ensure they apply */
      [cdkDropList] {
        min-height: 50px !important;
        transition: all 0.2s ease !important;
      }
      
      /* Special styling when dragging over a droplist */
      [cdkDropList].cdk-drop-list-dragging {
        background-color: rgba(66, 153, 225, 0.15) !important;
        border: 3px dashed #4299e1 !important;
        box-shadow: 0 0 0 1px rgba(66, 153, 225, 0.3) !important;
      }
      
      /* Style for the placeholder during drag */
      .cdk-drag-placeholder {
        opacity: 0.4;
        background-color: #e2e8f0 !important;
        border: 2px dashed #a0aec0 !important;
        min-height: 50px !important;
      }
      
      /* Style for the preview while dragging */
      .cdk-drag-preview {
        z-index: 9999 !important;
        opacity: 0.8 !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
      }
      
      /* Container specific styling */
      .container-box {
        cursor: move !important;
        z-index: 10 !important;
      }
      
      .container-content {
        cursor: default !important;
        min-height: 100px !important;
        z-index: 20 !important; /* Higher z-index for drop zone */
        transition: all 0.3s ease !important;
        position: relative !important;
      }
      
      /* Extremely visible active state for container drop zones */
      .container-content:hover {
        background-color: rgba(66, 153, 225, 0.08) !important;
        border: 2px dashed #4299e1 !important;
      }
      
      /* Active overlay */
      .container-active-overlay {
        pointer-events: none !important;
        z-index: 50 !important;
        animation: pulseOverlay 1.5s infinite alternate !important;
      }
      
      @keyframes pulseOverlay {
        from { opacity: 0.7; }
        to { opacity: 1; }
      }
      
      /* Special state for drag hover on containers */
      .container-content.cdk-drop-list-dragging {
        background-color: rgba(56, 178, 172, 0.15) !important;
        border: 3px dashed #38b2ac !important;
      }
      
      /* Special container drop bubble */
      .container-drop-bubble {
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        background-color: #38b2ac !important;
        color: white !important;
        padding: 10px 20px !important;
        border-radius: 30px !important;
        font-weight: bold !important;
        font-size: 16px !important;
        box-shadow: 0 0 20px rgba(56, 178, 172, 0.5) !important;
        z-index: 9999 !important;
        animation: pulseBubble 0.8s infinite alternate !important;
      }
      
      @keyframes pulseBubble {
        from { transform: translate(-50%, -50%) scale(1); }
        to { transform: translate(-50%, -50%) scale(1.1); }
      }
      
      /* Debug indicator */
      body::after {
        content: "Styles v4 loaded";
        position: fixed;
        bottom: 5px;
        right: 5px;
        background: darkgreen;
        color: white;
        padding: 3px 6px;
        font-size: 10px;
        border-radius: 3px;
        z-index: 9999;
        opacity: 0.7;
      }
    </style>
  </head>

  <body class="bg-background font-poppins selection:bg-primary selection:text-primary-foreground">
    <app-root>
      <div class="flex h-screen">
        <div class="m-auto">
          <button
            type="button"
            class="bg-primary text-primary-foreground inline-flex cursor-not-allowed items-center rounded-md px-4 py-2 text-sm font-semibold leading-6 shadow-sm transition duration-150 ease-in-out"
            disabled="">
            <svg
              class="-ml-1 mr-3 h-5 w-5 animate-spin"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading...
          </button>
        </div>
      </div>
    </app-root>
  </body>
</html>
