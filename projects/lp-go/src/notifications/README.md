# Push Notification Backend

This module provides a backend service for handling push notifications in the LP Angular application. It integrates with Firebase Cloud Messaging (FCM) to send notifications to iOS, Android, and web clients.

## Features

- Device registration and management
- Send notifications to specific devices, topics, or conditions
- Support for notification groups/categories
- Analytics tracking for notification events
- Cross-platform support (iOS, Android, Web)

## Setup

### Prerequisites

- Node.js 14+
- Firebase project with FCM enabled
- Service account credentials for Firebase Admin SDK

### Installation

1. Install dependencies:

```bash
cd projects/lp-go
npm install
```

2. Configure environment variables:

Copy the `.env.example` file to `.env` and update the values:

```bash
cp .env.example .env
```

3. Set up Firebase credentials:

Either:
- Set the `FIREBASE_SERVICE_ACCOUNT` environment variable with your service account JSON
- Set the `GOOGLE_APPLICATION_CREDENTIALS` environment variable to point to your service account file
- Place your service account JSON file at the root of the project as `service-account.json`

### Running the Service

Start the notification service:

```bash
npm run start:notifications
```

For development with auto-reload:

```bash
npm run dev:notifications
```

## API Endpoints

### Register Device

Register a device for push notifications.

- **URL**: `/api/notifications/register-device`
- **Method**: `POST`
- **Body**:
  ```json
  {
    "token": "fcm-device-token",
    "userId": "user-id",
    "platform": "ios|android|web",
    "appVersion": "1.0.0",
    "groups": ["general", "promotions"]
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "deviceId": "device-id"
  }
  ```

### Send Notification

Send a notification to specific devices, users, or groups.

- **URL**: `/api/notifications/send`
- **Method**: `POST`
- **Body**:
  ```json
  {
    "notification": {
      "title": "Notification Title",
      "body": "Notification Body",
      "imageUrl": "https://example.com/image.jpg",
      "actionUrl": "app://open/screen",
      "group": "promotions",
      "data": {
        "key": "value"
      }
    },
    "tokens": ["device-token-1", "device-token-2"],
    "userIds": ["user-id-1", "user-id-2"],
    "groups": ["general", "promotions"],
    "topic": "topic-name",
    "condition": "'general' in topics || 'promotions' in topics"
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "successCount": 2,
    "failureCount": 0,
    "successfulTokens": ["device-token-1", "device-token-2"],
    "failedTokens": [],
    "notificationId": "notification-id"
  }
  ```

### Update Device Groups

Update notification groups for a device.

- **URL**: `/api/notifications/groups`
- **Method**: `PUT`
- **Body**:
  ```json
  {
    "token": "fcm-device-token",
    "groups": ["general", "promotions", "transactions"]
  }
  ```
- **Response**:
  ```json
  {
    "success": true
  }
  ```

### Get Analytics

Get notification analytics data.

- **URL**: `/api/notifications/analytics`
- **Method**: `GET`
- **Query Parameters**:
  - `startDate`: ISO date string (optional)
  - `endDate`: ISO date string (optional)
- **Response**:
  ```json
  {
    "counts": {
      "sent": 100,
      "delivered": 95,
      "opened": 50,
      "dismissed": 10,
      "actionClicked": 20
    },
    "rates": {
      "delivery": 0.95,
      "open": 0.53,
      "dismiss": 0.11,
      "action": 0.4
    }
  }
  ```

## Integration with Frontend

The backend service integrates with the existing frontend implementation:

- `PushNotificationService` in `projects/lp-client/src/app/services/push-notification.service.ts`
- `NotificationPermissionPromptComponent` in `projects/mobile-components/src/lib/notifications/notification-permission-prompt/notification-permission-prompt.component.ts`
- `NotificationAnalyticsService` in `projects/mobile-components/src/lib/notifications/services/notification-analytics.service.ts`

## Security Considerations

- Ensure your Firebase Admin SDK credentials are kept secure
- Implement proper authentication for the API endpoints
- Validate all input data before processing
- Use HTTPS for all API requests
- Implement rate limiting to prevent abuse

## Testing

To test the notification service:

1. Start the service
2. Use a tool like Postman to send requests to the API endpoints
3. Verify that notifications are received on test devices
