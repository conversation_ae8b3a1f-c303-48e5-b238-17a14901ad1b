// Mock implementation of the notification service
console.log('Starting mock notification service...');

// Mock device storage
const devices = [];

// Mock notification storage
const notifications = [];

// Mock device registration
function registerDevice(token, platform, userId) {
  console.log(`Registering device: ${token}, platform: ${platform}, userId: ${userId || 'anonymous'}`);
  
  const existingDeviceIndex = devices.findIndex(d => d.token === token);
  
  if (existingDeviceIndex >= 0) {
    // Update existing device
    devices[existingDeviceIndex] = {
      ...devices[existingDeviceIndex],
      platform,
      userId,
      updatedAt: new Date()
    };
    
    console.log(`Device updated: ${token}`);
    return devices[existingDeviceIndex];
  } else {
    // Create new device
    const device = {
      token,
      platform,
      userId,
      id: `device-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      status: 'active'
    };
    
    devices.push(device);
    
    console.log(`Device registered: ${token}`);
    return device;
  }
}

// Mock send notification
function sendNotification(title, body, tokens) {
  console.log(`Sending notification: ${title} - ${body}`);
  console.log(`To tokens: ${tokens.join(', ')}`);
  
  const notification = {
    id: `notification-${Date.now()}`,
    title,
    body,
    sentAt: new Date(),
    tokens,
    successCount: tokens.length,
    failureCount: 0
  };
  
  notifications.push(notification);
  
  console.log(`Notification sent: ${notification.id}`);
  return notification;
}

// Register some test devices
const device1 = registerDevice('test-token-1', 'ios', 'user-1');
const device2 = registerDevice('test-token-2', 'android', 'user-2');
const device3 = registerDevice('test-token-3', 'web', 'user-3');

// Send a test notification
const notification = sendNotification(
  'Test Notification',
  'This is a test notification from the mock service',
  [device1.token, device2.token, device3.token]
);

// Print summary
console.log('\nMock Service Summary:');
console.log(`Registered devices: ${devices.length}`);
console.log(`Sent notifications: ${notifications.length}`);
console.log('\nMock service is ready for testing!');
