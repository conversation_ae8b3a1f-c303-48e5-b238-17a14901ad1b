/**
 * Firebase Admin SDK configuration
 *
 * This file contains the configuration for the Firebase Admin SDK.
 * It initializes the SDK with the service account credentials and exports
 * the admin instance for use in other parts of the application.
 */

import * as admin from 'firebase-admin';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Check if the app has already been initialized to prevent multiple initializations
let firebaseAdmin: admin.app.App;

/**
 * Initialize Firebase Admin SDK
 *
 * @returns Firebase Admin app instance
 */
export function initializeFirebaseAdmin(): admin.app.App {
  if (!firebaseAdmin) {
    // Check if we have service account credentials in environment variables
    if (process.env['FIREBASE_SERVICE_ACCOUNT']) {
      try {
        // Parse the service account JSON from environment variable
        const serviceAccount = JSON.parse(process.env['FIREBASE_SERVICE_ACCOUNT']);

        firebaseAdmin = admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
          databaseURL: process.env['FIREBASE_DATABASE_URL']
        });

        console.log('Firebase Admin SDK initialized successfully with service account');
      } catch (error) {
        console.error('Error initializing Firebase Admin SDK with service account:', error);
        throw error;
      }
    } else if (process.env['GOOGLE_APPLICATION_CREDENTIALS']) {
      // Use the GOOGLE_APPLICATION_CREDENTIALS environment variable
      try {
        firebaseAdmin = admin.initializeApp({
          credential: admin.credential.applicationDefault(),
          databaseURL: process.env['FIREBASE_DATABASE_URL']
        });

        console.log('Firebase Admin SDK initialized successfully with application default credentials');
      } catch (error) {
        console.error('Error initializing Firebase Admin SDK with application default credentials:', error);
        throw error;
      }
    } else {
      // For development or testing, use a mock implementation
      console.log('Using mock Firebase Admin SDK for development');

      // Create a mock Firebase Admin app
      firebaseAdmin = {
        messaging: () => ({
          send: async () => `mock-message-id-${Date.now()}`
        })
      } as unknown as admin.app.App;
    }
  }

  return firebaseAdmin;
}

// Export the Firebase Admin instance
export const getFirebaseAdmin = (): admin.app.App => {
  if (!firebaseAdmin) {
    return initializeFirebaseAdmin();
  }
  return firebaseAdmin;
};

// Export the Firebase Messaging instance
export const getMessaging = (): admin.messaging.Messaging => {
  return getFirebaseAdmin().messaging();
};
