/**
 * Notification Controller
 *
 * This controller handles HTTP requests related to push notifications.
 * It provides endpoints for registering devices, sending notifications,
 * and managing notification preferences.
 */

import { Request, Response } from 'express';
import { NotificationService } from '../services/notification-service';
import { AnalyticsService } from '../services/analytics-service';
import {
  NotificationRequest,
  NotificationResponse
} from '../models/notification.model';
import {
  Device,
  DeviceRegistrationRequest,
  DeviceRegistrationResponse,
  DeviceGroupUpdateRequest,
  DeviceGroupUpdateResponse,
  DeviceStatus
} from '../models/device.model';

// In-memory storage for devices (in a real implementation, this would be a database)
const devices: Device[] = [];

export class NotificationController {
  private notificationService: NotificationService;
  private analyticsService: AnalyticsService;

  constructor() {
    this.notificationService = new NotificationService();
    this.analyticsService = new AnalyticsService();
  }

  /**
   * Register a device for push notifications
   *
   * @param req Express request
   * @param res Express response
   */
  async registerDevice(req: Request, res: Response): Promise<void> {
    try {
      const registrationRequest: DeviceRegistrationRequest = req.body;

      // Validate request
      if (!registrationRequest.token || !registrationRequest.platform) {
        res.status(400).json({
          success: false,
          error: 'Token and platform are required'
        });
        return;
      }

      // Check if device already exists
      const existingDeviceIndex = devices.findIndex(d => d.token === registrationRequest.token);

      let device: Device;
      let response: DeviceRegistrationResponse;

      if (existingDeviceIndex >= 0) {
        // Update existing device
        device = {
          ...devices[existingDeviceIndex],
          ...registrationRequest,
          updatedAt: new Date()
        };

        devices[existingDeviceIndex] = device;

        response = {
          success: true,
          deviceId: device.id
        };

        console.log(`Device updated: ${device.token}`);
      } else {
        // Create new device
        device = {
          ...registrationRequest,
          id: `device-${Date.now()}`,
          createdAt: new Date(),
          updatedAt: new Date(),
          status: DeviceStatus.ACTIVE
        };

        devices.push(device);

        response = {
          success: true,
          deviceId: device.id
        };

        console.log(`Device registered: ${device.token}`);
      }

      res.status(200).json(response);
    } catch (error: any) {
      console.error('Error registering device:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Failed to register device'
      });
    }
  }

  /**
   * Send a notification to specific devices
   *
   * @param req Express request
   * @param res Express response
   */
  async sendNotification(req: Request, res: Response): Promise<void> {
    try {
      const notificationRequest: NotificationRequest = req.body;

      // Validate request
      if (!notificationRequest.notification) {
        res.status(400).json({
          success: false,
          error: 'Notification is required'
        });
        return;
      }

      // Generate notification ID if not provided
      if (!notificationRequest.notification.id) {
        notificationRequest.notification.id = `notification-${Date.now()}`;
      }

      let response: NotificationResponse;

      // Determine how to send the notification
      if (notificationRequest.tokens && notificationRequest.tokens.length > 0) {
        // Send to specific devices
        response = await this.notificationService.sendToDevices(notificationRequest);
      } else if (notificationRequest.topic) {
        // Send to a topic
        response = await this.notificationService.sendToTopic(notificationRequest);
      } else if (notificationRequest.condition) {
        // Send based on a condition
        response = await this.notificationService.sendToCondition(notificationRequest);
      } else if (notificationRequest.userIds && notificationRequest.userIds.length > 0) {
        // Find devices for the specified users
        const userDevices = devices.filter(d =>
          notificationRequest.userIds!.includes(d.userId || '')
        );

        if (userDevices.length === 0) {
          res.status(404).json({
            success: false,
            error: 'No devices found for the specified users'
          });
          return;
        }

        // Extract tokens
        const tokens = userDevices.map(d => d.token);

        // Send to user devices
        response = await this.notificationService.sendToDevices({
          ...notificationRequest,
          tokens
        });
      } else if (notificationRequest.groups && notificationRequest.groups.length > 0) {
        // Find devices subscribed to the specified groups
        const groupDevices = devices.filter(d =>
          d.groups && d.groups.some(g => notificationRequest.groups!.includes(g))
        );

        if (groupDevices.length === 0) {
          res.status(404).json({
            success: false,
            error: 'No devices found for the specified groups'
          });
          return;
        }

        // Extract tokens
        const tokens = groupDevices.map(d => d.token);

        // Send to group devices
        response = await this.notificationService.sendToDevices({
          ...notificationRequest,
          tokens
        });
      } else {
        res.status(400).json({
          success: false,
          error: 'No recipients specified (tokens, topic, condition, userIds, or groups)'
        });
        return;
      }

      res.status(200).json(response);
    } catch (error: any) {
      console.error('Error sending notification:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Failed to send notification'
      });
    }
  }

  /**
   * Update notification groups for a device
   *
   * @param req Express request
   * @param res Express response
   */
  async updateDeviceGroups(req: Request, res: Response): Promise<void> {
    try {
      const groupUpdateRequest: DeviceGroupUpdateRequest = req.body;

      // Validate request
      if (!groupUpdateRequest.token || !groupUpdateRequest.groups) {
        res.status(400).json({
          success: false,
          error: 'Token and groups are required'
        });
        return;
      }

      // Find the device
      const deviceIndex = devices.findIndex(d => d.token === groupUpdateRequest.token);

      if (deviceIndex === -1) {
        res.status(404).json({
          success: false,
          error: 'Device not found'
        });
        return;
      }

      // Update the device groups
      devices[deviceIndex].groups = groupUpdateRequest.groups;
      devices[deviceIndex].updatedAt = new Date();

      const response: DeviceGroupUpdateResponse = {
        success: true
      };

      console.log(`Device groups updated: ${groupUpdateRequest.token}`);

      res.status(200).json(response);
    } catch (error: any) {
      console.error('Error updating device groups:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Failed to update device groups'
      });
    }
  }

  /**
   * Get notification analytics
   *
   * @param req Express request
   * @param res Express response
   */
  async getAnalytics(req: Request, res: Response): Promise<void> {
    try {
      // Parse date range from query parameters
      let startDate: Date | undefined;
      let endDate: Date | undefined;

      if (req.query['startDate']) {
        startDate = new Date(req.query['startDate'] as string);
      }

      if (req.query['endDate']) {
        endDate = new Date(req.query['endDate'] as string);
      }

      // Get analytics data
      const analytics = await this.analyticsService.getAnalytics(startDate, endDate);

      res.status(200).json(analytics);
    } catch (error: any) {
      console.error('Error getting notification analytics:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Failed to get notification analytics'
      });
    }
  }
}
