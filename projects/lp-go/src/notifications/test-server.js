const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

// Create Express app
const app = express();

// Middleware
app.use(cors());
app.use(bodyParser.json());

// Mock device storage
const devices = [];

// Register device endpoint
app.post('/api/notifications/register-device', (req, res) => {
  const { token, platform } = req.body;
  
  if (!token || !platform) {
    return res.status(400).json({
      success: false,
      error: 'Token and platform are required'
    });
  }
  
  const existingDeviceIndex = devices.findIndex(d => d.token === token);
  
  if (existingDeviceIndex >= 0) {
    // Update existing device
    devices[existingDeviceIndex] = {
      ...devices[existingDeviceIndex],
      ...req.body,
      updatedAt: new Date()
    };
    
    console.log(`Device updated: ${token}`);
    
    return res.status(200).json({
      success: true,
      deviceId: devices[existingDeviceIndex].id
    });
  } else {
    // Create new device
    const device = {
      ...req.body,
      id: `device-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      status: 'active'
    };
    
    devices.push(device);
    
    console.log(`Device registered: ${token}`);
    
    return res.status(200).json({
      success: true,
      deviceId: device.id
    });
  }
});

// Send notification endpoint
app.post('/api/notifications/send', (req, res) => {
  const { notification, tokens } = req.body;
  
  if (!notification) {
    return res.status(400).json({
      success: false,
      error: 'Notification is required'
    });
  }
  
  console.log(`Sending notification: ${JSON.stringify(notification)}`);
  
  return res.status(200).json({
    success: true,
    successCount: tokens ? tokens.length : 0,
    failureCount: 0,
    notificationId: `notification-${Date.now()}`
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', service: 'push-notification-service' });
});

// Start the server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Test server running on port ${PORT}`);
});
