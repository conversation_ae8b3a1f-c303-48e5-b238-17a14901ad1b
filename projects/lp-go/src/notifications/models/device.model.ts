/**
 * Device models
 * 
 * This file contains the interfaces for device-related data structures.
 */

/**
 * Device platform types
 */
export enum DevicePlatform {
  IOS = 'ios',
  ANDROID = 'android',
  WEB = 'web'
}

/**
 * Device registration status
 */
export enum DeviceStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  UNINSTALLED = 'uninstalled'
}

/**
 * Device token information
 * Represents a device registered for push notifications
 */
export interface Device {
  /** Unique identifier for the device record */
  id?: string;
  
  /** FCM token for the device */
  token: string;
  
  /** User ID associated with the device */
  userId?: string;
  
  /** Device platform (ios, android, web) */
  platform: DevicePlatform;
  
  /** App version installed on the device */
  appVersion?: string;
  
  /** Device model information */
  deviceModel?: string;
  
  /** Operating system version */
  osVersion?: string;
  
  /** Language preference */
  language?: string;
  
  /** Timezone information */
  timezone?: string;
  
  /** Array of notification group IDs the device is subscribed to */
  groups?: string[];
  
  /** Current status of the device */
  status?: DeviceStatus;
  
  /** Last time the device was active */
  lastActiveAt?: Date | string;
  
  /** When the device was registered */
  createdAt?: Date | string;
  
  /** When the device record was last updated */
  updatedAt?: Date | string;
}

/**
 * Device registration request
 * Used when registering a new device
 */
export interface DeviceRegistrationRequest {
  /** FCM token for the device */
  token: string;
  
  /** User ID associated with the device */
  userId?: string;
  
  /** Device platform (ios, android, web) */
  platform: DevicePlatform;
  
  /** App version installed on the device */
  appVersion?: string;
  
  /** Device model information */
  deviceModel?: string;
  
  /** Operating system version */
  osVersion?: string;
  
  /** Language preference */
  language?: string;
  
  /** Timezone information */
  timezone?: string;
  
  /** Array of notification group IDs to subscribe to */
  groups?: string[];
}

/**
 * Device registration response
 * Returned after registering a device
 */
export interface DeviceRegistrationResponse {
  /** Success status */
  success: boolean;
  
  /** Device ID */
  deviceId?: string;
  
  /** Error message if registration failed */
  error?: string;
}

/**
 * Device group update request
 * Used when updating notification groups for a device
 */
export interface DeviceGroupUpdateRequest {
  /** FCM token for the device */
  token: string;
  
  /** Array of notification group IDs to subscribe to */
  groups: string[];
}

/**
 * Device group update response
 * Returned after updating device groups
 */
export interface DeviceGroupUpdateResponse {
  /** Success status */
  success: boolean;
  
  /** Error message if update failed */
  error?: string;
}
