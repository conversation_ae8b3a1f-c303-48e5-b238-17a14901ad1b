/**
 * Notification models
 * 
 * This file contains the interfaces for notification-related data structures.
 */

/**
 * Notification data structure
 * Represents a notification to be sent to devices
 */
export interface Notification {
  /** Unique identifier for the notification */
  id?: string;
  
  /** Notification title */
  title: string;
  
  /** Notification body text */
  body: string;
  
  /** Optional image URL to display in the notification */
  imageUrl?: string;
  
  /** Optional deep link or action URL */
  actionUrl?: string;
  
  /** Notification group/category identifier */
  group?: string;
  
  /** Optional data payload to include with the notification */
  data?: Record<string, any>;
  
  /** Optional time to live in seconds */
  ttl?: number;
  
  /** Optional priority level */
  priority?: 'normal' | 'high';
  
  /** Optional collapse key for grouping notifications */
  collapseKey?: string;
  
  /** Optional flag to indicate if notification is silent */
  silent?: boolean;
  
  /** Optional timestamp for scheduled delivery */
  scheduledTime?: Date | string;
  
  /** Optional expiration time */
  expirationTime?: Date | string;
}

/**
 * Notification request structure
 * Used when sending a notification to specific devices
 */
export interface NotificationRequest {
  /** The notification to send */
  notification: Notification;
  
  /** Array of device tokens to send the notification to */
  tokens?: string[];
  
  /** Array of user IDs to send the notification to */
  userIds?: string[];
  
  /** Array of notification groups to target */
  groups?: string[];
  
  /** Optional condition for FCM topic expressions */
  condition?: string;
  
  /** Optional topic to send to */
  topic?: string;
}

/**
 * Notification response structure
 * Returned after sending a notification
 */
export interface NotificationResponse {
  /** Success status */
  success: boolean;
  
  /** Number of messages sent successfully */
  successCount?: number;
  
  /** Number of messages that failed to send */
  failureCount?: number;
  
  /** Array of successful message IDs */
  successfulTokens?: string[];
  
  /** Array of failed tokens with error information */
  failedTokens?: { token: string; error: string }[];
  
  /** Optional error message if the operation failed */
  error?: string;
  
  /** Optional notification ID */
  notificationId?: string;
}

/**
 * Notification analytics event types
 */
export enum NotificationEventType {
  /** Notification was sent */
  SENT = 'notification_sent',
  
  /** Notification was delivered to the device */
  DELIVERED = 'notification_delivered',
  
  /** Notification was opened by the user */
  OPENED = 'notification_opened',
  
  /** Notification was dismissed by the user */
  DISMISSED = 'notification_dismissed',
  
  /** User clicked on an action button in the notification */
  ACTION_CLICKED = 'notification_action_clicked'
}

/**
 * Notification analytics event
 */
export interface NotificationEvent {
  /** Type of event */
  eventType: NotificationEventType;
  
  /** Notification ID */
  notificationId: string;
  
  /** User ID (if available) */
  userId?: string;
  
  /** Device token */
  deviceToken?: string;
  
  /** Platform (ios, android, web) */
  platform?: string;
  
  /** Timestamp when the event occurred */
  timestamp: Date | string;
  
  /** Additional data related to the event */
  data?: Record<string, any>;
}
