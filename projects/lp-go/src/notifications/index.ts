/**
 * Push Notification Backend
 *
 * This is the main entry point for the push notification backend.
 * It initializes the Firebase Admin SDK and sets up the Express routes.
 */

import express from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import { initializeFirebaseAdmin } from './config/firebase-config';
import notificationRoutes from './routes/notification-routes';

// Initialize Firebase Admin SDK
initializeFirebaseAdmin();

// Create Express app
const app = express();

// Middleware
app.use(cors());
app.use(bodyParser.json());

// Routes
app.use('/api/notifications', notificationRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', service: 'push-notification-service' });
});

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
});

// Export the Express app
export default app;

// If this file is run directly, start the server
if (require.main === module) {
  const PORT = process.env['PORT'] || 3000;
  app.listen(PORT, () => {
    console.log(`Push notification service running on port ${PORT}`);
  });
}
