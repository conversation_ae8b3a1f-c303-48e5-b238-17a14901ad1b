/**
 * Notification Service
 *
 * This service handles sending push notifications using Firebase Cloud Messaging.
 * It provides methods for sending notifications to specific devices, topics, or conditions.
 */

import * as admin from 'firebase-admin';
import { getMessaging } from '../config/firebase-config';
import {
  NotificationRequest,
  NotificationResponse,
  NotificationEventType
} from '../models/notification.model';
import { AnalyticsService } from './analytics-service';

export class NotificationService {
  private messaging: admin.messaging.Messaging;
  private analyticsService: AnalyticsService;

  constructor() {
    this.messaging = getMessaging();
    this.analyticsService = new AnalyticsService();
  }

  /**
   * Send a notification to specific devices
   *
   * @param request Notification request with tokens
   * @returns Promise with notification response
   */
  async sendToDevices(request: NotificationRequest): Promise<NotificationResponse> {
    try {
      if (!request.tokens || request.tokens.length === 0) {
        return {
          success: false,
          error: 'No device tokens provided'
        };
      }

      // Create the FCM message
      const message: admin.messaging.MulticastMessage = {
        tokens: request.tokens,
        notification: {
          title: request.notification.title,
          body: request.notification.body,
          imageUrl: request.notification.imageUrl
        },
        data: {
          ...request.notification.data,
          id: request.notification.id || `notification-${Date.now()}`,
          actionUrl: request.notification.actionUrl || '',
          group: request.notification.group || 'general'
        },
        android: {
          priority: request.notification.priority === 'high' ? 'high' : 'normal',
          ttl: request.notification.ttl ? request.notification.ttl * 1000 : undefined,
          collapseKey: request.notification.collapseKey
        },
        apns: {
          payload: {
            aps: {
              badge: 1,
              sound: 'default',
              'content-available': 1
            }
          }
        },
        webpush: {
          notification: {
            icon: '/assets/icons/icon-72x72.png'
          }
        }
      };

      // Send the message
      // Since sendMulticast is not available in the current version of firebase-admin,
      // we'll use send method for each token
      const results = await Promise.all(
        request.tokens.map(token =>
          this.messaging.send({
            ...message,
            token,
            topic: undefined,
            condition: undefined
          })
        )
      );

      // Calculate success and failure counts
      const successCount = results.filter(Boolean).length;
      const failureCount = request.tokens.length - successCount;

      // Track the notification send event
      await this.analyticsService.trackEvent({
        eventType: NotificationEventType.SENT,
        notificationId: request.notification.id || `notification-${Date.now()}`,
        timestamp: new Date(),
        data: {
          title: request.notification.title,
          body: request.notification.body,
          successCount,
          failureCount
        }
      });

      // Process the response
      const failedTokens: { token: string; error: string }[] = [];
      const successfulTokens: string[] = [];

      request.tokens.forEach((token, index) => {
        if (results[index]) {
          successfulTokens.push(token);
        } else {
          failedTokens.push({
            token,
            error: 'Failed to send notification'
          });
        }
      });

      return {
        success: true,
        successCount,
        failureCount,
        successfulTokens,
        failedTokens,
        notificationId: request.notification.id || `notification-${Date.now()}`
      };
    } catch (error: any) {
      console.error('Error sending notification to devices:', error);
      return {
        success: false,
        error: error.message || 'Failed to send notification'
      };
    }
  }

  /**
   * Send a notification to a topic
   *
   * @param request Notification request with topic
   * @returns Promise with notification response
   */
  async sendToTopic(request: NotificationRequest): Promise<NotificationResponse> {
    try {
      if (!request.topic) {
        return {
          success: false,
          error: 'No topic provided'
        };
      }

      // Create the FCM message
      const message: admin.messaging.Message = {
        topic: request.topic,
        notification: {
          title: request.notification.title,
          body: request.notification.body,
          imageUrl: request.notification.imageUrl
        },
        data: {
          ...request.notification.data,
          id: request.notification.id || `notification-${Date.now()}`,
          actionUrl: request.notification.actionUrl || '',
          group: request.notification.group || 'general'
        },
        android: {
          priority: request.notification.priority === 'high' ? 'high' : 'normal',
          ttl: request.notification.ttl ? request.notification.ttl * 1000 : undefined,
          collapseKey: request.notification.collapseKey
        },
        apns: {
          payload: {
            aps: {
              badge: 1,
              sound: 'default',
              'content-available': 1
            }
          }
        }
      };

      // Send the message
      const response = await this.messaging.send(message);

      // Track the notification send event
      await this.analyticsService.trackEvent({
        eventType: NotificationEventType.SENT,
        notificationId: request.notification.id || `notification-${Date.now()}`,
        timestamp: new Date(),
        data: {
          title: request.notification.title,
          body: request.notification.body,
          topic: request.topic
        }
      });

      return {
        success: true,
        notificationId: response
      };
    } catch (error: any) {
      console.error('Error sending notification to topic:', error);
      return {
        success: false,
        error: error.message || 'Failed to send notification to topic'
      };
    }
  }

  /**
   * Send a notification based on a condition
   *
   * @param request Notification request with condition
   * @returns Promise with notification response
   */
  async sendToCondition(request: NotificationRequest): Promise<NotificationResponse> {
    try {
      if (!request.condition) {
        return {
          success: false,
          error: 'No condition provided'
        };
      }

      // Create the FCM message
      const message: admin.messaging.Message = {
        condition: request.condition,
        notification: {
          title: request.notification.title,
          body: request.notification.body,
          imageUrl: request.notification.imageUrl
        },
        data: {
          ...request.notification.data,
          id: request.notification.id || `notification-${Date.now()}`,
          actionUrl: request.notification.actionUrl || '',
          group: request.notification.group || 'general'
        },
        android: {
          priority: request.notification.priority === 'high' ? 'high' : 'normal',
          ttl: request.notification.ttl ? request.notification.ttl * 1000 : undefined,
          collapseKey: request.notification.collapseKey
        },
        apns: {
          payload: {
            aps: {
              badge: 1,
              sound: 'default',
              'content-available': 1
            }
          }
        }
      };

      // Send the message
      const response = await this.messaging.send(message);

      // Track the notification send event
      await this.analyticsService.trackEvent({
        eventType: NotificationEventType.SENT,
        notificationId: request.notification.id || `notification-${Date.now()}`,
        timestamp: new Date(),
        data: {
          title: request.notification.title,
          body: request.notification.body,
          condition: request.condition
        }
      });

      return {
        success: true,
        notificationId: response
      };
    } catch (error: any) {
      console.error('Error sending notification with condition:', error);
      return {
        success: false,
        error: error.message || 'Failed to send notification with condition'
      };
    }
  }
}
