import { defineConfig } from 'vite';
// import angular from '@analogjs/vite-plugin-angular';
import fs from 'fs';
import path from 'path';

// Read .viteignore file if it exists
const ignorePatterns = (() => {
  try {
    const ignoreFile = path.resolve(__dirname, '.viteignore');
    if (fs.existsSync(ignoreFile)) {
      return fs.readFileSync(ignoreFile, 'utf8')
        .split('\n')
        .filter(line => line.trim() !== '' && !line.startsWith('#'))
        .map(pattern => new RegExp(pattern.replace(/\*/g, '.*')));
    }
  } catch (e) {
    console.warn('Failed to read .viteignore file:', e);
  }
  return [];
})();

export default defineConfig(({ command, mode }) => ({
  // Disable warnings for dynamic imports that cannot be analyzed
  build: {
    rollupOptions: {
      onwarn(warning, warn) {
        // Ignore specific warnings
        if (warning.code === 'DYNAMIC_IMPORT_VARIABLES') {
          return;
        }
        // Use default for everything else
        warn(warning);
      },
      output: {
        manualChunks: (id) => {
          if (id.includes('node_modules')) {
            return 'vendor';
          }
          return undefined;
        }
      }
    },
    commonjsOptions: {
      transformMixedEsModules: true
    }
  },
  plugins: [
    // angular({
    //   tsconfig: 'tsconfig.app.json',
    // }),
    // Custom plugin to suppress specific warnings
    {
      name: 'suppress-dynamic-import-warnings',
      transform(code, id) {
        // Specifically target the problematic chunk file
        if (id.includes('chunk-KOIBO45S.js')) {
          // Add @vite-ignore comment to the specific dynamic import pattern
          return code.replace(
            /import\(["']\.\/["']\.concat\(([^)]+), ["']\.entry\.js["']\)\.concat\(["']{2}\)\)/g,
            'import(/* @vite-ignore */ "./".concat($1, ".entry.js").concat(""))'
          );
        }

        // For other files that match ignore patterns
        if (ignorePatterns.some(pattern => pattern.test(id))) {
          // Add @vite-ignore comment to dynamic imports
          return code.replace(
            /import\(\s*["']\.\/["']\s*\.concat\s*\(/g,
            'import(/* @vite-ignore */ "./".concat('
          );
        }

        return null;
      }
    },
    // Additional plugin to handle dynamic imports during build
    {
      name: 'handle-dynamic-imports',
      configureServer(server) {
        // Override Vite's warning handler for the dev server
        const originalOnLog = server.config.logger.warn;
        server.config.logger.warn = (msg, ...args) => {
          // Skip dynamic import warnings
          if (typeof msg === 'string' &&
              (msg.includes('dynamic import cannot be analyzed') ||
               msg.includes('DYNAMIC_IMPORT_VARIABLES'))) {
            return;
          }
          originalOnLog(msg, ...args);
        };
      }
    }
  ],

  optimizeDeps: {
    include: ['@angular/common', '@angular/core'],
    exclude: ['@analogjs/vite-plugin-angular'],
    // Disable dynamic import warnings
    esbuildOptions: {
      logOverride: {
        'dynamic-import-in-optimized-deps': 'silent'
      }
    }
  },
  resolve: {
    preserveSymlinks: true
  },
  esbuild: {
    supported: {
      'dynamic-import': true
    }
  },
  // Silence specific warnings if VITE_LOGGER_SILENT is set
  logLevel: process.env['VITE_LOGGER_SILENT'] ? 'error' : 'info'
}));