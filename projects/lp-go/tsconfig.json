{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "forceConsistentCasingInFileNames": true, "strict": true, "esModuleInterop": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "sourceMap": true, "declaration": false, "experimentalDecorators": true, "moduleResolution": "node", "importHelpers": true, "target": "ES2022", "module": "es2020", "resolveJsonModule": true, "paths": {"@projects/mobile-components/*": ["../../mobile-components/src/*"], "mobile-components": ["../../mobile-components/src/public-api.ts"], "mobile-components/*": ["../../mobile-components/src/*"], "keycloak-lp-ionic": ["../../projects/keycloak"], "keycloak-lp-ionic/*": ["../../projects/keycloak/*"], "node_modules/keycloak-lp-ionic": ["../../projects/keycloak"], "node_modules/keycloak-lp-ionic/*": ["../../projects/keycloak/*"], "lp-client-api": ["../../dist/lp-client-api"], "lp-client-api/*": ["../../dist/lp-client-api/*"]}, "lib": ["es2020", "dom"], "useDefineForClassFields": false, "skipLibCheck": true}, "angularCompilerOptions": {"enableI18nLegacyMessageIdFormat": false, "strictInjectionParameters": true, "strictInputAccessModifiers": true, "strictTemplates": true}}