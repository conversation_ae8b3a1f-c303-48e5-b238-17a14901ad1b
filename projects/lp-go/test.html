<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mobile Components Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .test-section {
      margin-bottom: 20px;
      padding: 15px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
    }
    h1 {
      color: #333;
    }
    h2 {
      color: #555;
      margin-top: 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Mobile Components Test Page</h1>
    
    <div class="test-section">
      <h2>Button Component Test</h2>
      <base-button text="Test Button" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded"></base-button>
    </div>
    
    <div class="test-section">
      <h2>Paragraph Component Test</h2>
      <base-paragraph>This is a test paragraph from mobile-components.</base-paragraph>
    </div>
    
    <div class="test-section">
      <h2>Heading Component Test</h2>
      <base-heading>This is a test heading from mobile-components.</base-heading>
    </div>
  </div>

  <script>
    // This script will check if the components are properly defined
    setTimeout(() => {
      const components = ['base-button', 'base-paragraph', 'base-heading'];
      components.forEach(tag => {
        const element = document.querySelector(tag);
        console.log(`${tag} exists:`, !!element);
        if (element) {
          console.log(`${tag} innerHTML:`, element.innerHTML);
        }
      });
    }, 1000);
  </script>
</body>
</html>
