import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'lib-dashboard-header',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <div class="dashboard-header" [ngClass]="config?.class">
      <!-- Background -->
      <div class="header-background" [ngClass]="config?.backgroundClass">
        <div class="animated-bg"></div>
      </div>

      <!-- Header Content -->
      <div class="header-content">
        <!-- Logo and Balance Section -->
        <div class="header-top">
          <div class="logo-section" *ngIf="config?.showLogo">
            <img 
              [src]="config?.logoUrl || 'assets/images/logo.png'" 
              [alt]="config?.logoAlt || 'Logo'"
              class="header-logo"
            />
          </div>

          <div class="balance-section" *ngIf="profile && config?.showBalance">
            <div class="balance-container">
              <div class="balance-label">{{ config?.balanceLabel || 'Current Balance' }}</div>
              <div class="balance-value">{{ profile?.currentBalance || 0 }}</div>
              <div class="balance-unit">{{ config?.balanceUnit || 'points' }}</div>
            </div>
          </div>
        </div>

        <!-- User Info Section -->
        <div class="user-info" *ngIf="profile && config?.showUserInfo">
          <div class="user-details">
            <div class="user-name">
              {{ profile?.givenNames || 'User' }} {{ profile?.surname || '' }}
            </div>
            <div class="membership-info" *ngIf="profile?.newMembershipNumber">
              <span class="membership-label">Member ID:</span>
              <span class="membership-number">{{ profile.newMembershipNumber }}</span>
            </div>
          </div>

          <div class="user-stats" *ngIf="config?.showStats">
            <div class="stat-item" *ngIf="profile?.availRands !== undefined">
              <div class="stat-label">Rand Value</div>
              <div class="stat-value">R {{ profile.availRands?.toFixed(2) || '0.00' }}</div>
            </div>
            <div class="stat-item" *ngIf="profile?.availUnits !== undefined">
              <div class="stat-label">Available Units</div>
              <div class="stat-value">{{ profile.availUnits || 0 }}</div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="header-actions" *ngIf="config?.actions && config.actions.length > 0">
          <button 
            *ngFor="let action of config.actions"
            class="header-action-btn"
            [ngClass]="action.class"
            (click)="onAction(action.action || action.navigation)"
          >
            <ion-icon [name]="action.icon" *ngIf="action.icon"></ion-icon>
            <span>{{ action.label }}</span>
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .dashboard-header {
      position: relative;
      padding: 1rem;
      margin-bottom: 1rem;
      overflow: hidden;
    }

    .header-background {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-secondary));
      z-index: 0;
    }

    .animated-bg {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(-45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05), rgba(255,255,255,0.1), rgba(255,255,255,0.05));
      background-size: 400% 400%;
      animation: gradientShift 15s ease infinite;
    }

    @keyframes gradientShift {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    .header-content {
      position: relative;
      z-index: 1;
      color: white;
    }

    .header-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .header-logo {
      height: 40px;
      width: auto;
      max-width: 120px;
    }

    .balance-section {
      text-align: right;
    }

    .balance-container {
      background: rgba(255, 255, 255, 0.15);
      padding: 0.75rem 1rem;
      border-radius: 0.75rem;
      backdrop-filter: blur(10px);
    }

    .balance-label {
      font-size: 0.8rem;
      opacity: 0.9;
      margin-bottom: 0.25rem;
    }

    .balance-value {
      font-size: 1.5rem;
      font-weight: bold;
      line-height: 1;
    }

    .balance-unit {
      font-size: 0.8rem;
      opacity: 0.8;
    }

    .user-info {
      background: rgba(255, 255, 255, 0.1);
      padding: 1rem;
      border-radius: 0.75rem;
      backdrop-filter: blur(10px);
      margin-bottom: 1rem;
    }

    .user-details {
      margin-bottom: 0.75rem;
    }

    .user-name {
      font-size: 1.2rem;
      font-weight: bold;
      margin-bottom: 0.25rem;
    }

    .membership-info {
      font-size: 0.9rem;
      opacity: 0.9;
    }

    .membership-label {
      margin-right: 0.5rem;
    }

    .membership-number {
      font-weight: 600;
    }

    .user-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 1rem;
    }

    .stat-item {
      text-align: center;
      padding: 0.5rem;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 0.5rem;
    }

    .stat-label {
      font-size: 0.8rem;
      opacity: 0.8;
      margin-bottom: 0.25rem;
    }

    .stat-value {
      font-size: 1rem;
      font-weight: bold;
    }

    .header-actions {
      display: flex;
      gap: 0.5rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    .header-action-btn {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.5rem 1rem;
      background: rgba(255, 255, 255, 0.2);
      border: none;
      border-radius: 1rem;
      color: white;
      font-size: 0.9rem;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .header-action-btn:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-1px);
    }

    .header-action-btn ion-icon {
      font-size: 1rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .dashboard-header {
        padding: 0.75rem;
      }

      .header-top {
        flex-direction: column;
        gap: 0.75rem;
        text-align: center;
      }

      .balance-section {
        text-align: center;
      }

      .user-stats {
        grid-template-columns: 1fr 1fr;
      }

      .user-name {
        font-size: 1.1rem;
      }

      .balance-value {
        font-size: 1.3rem;
      }
    }

    @media (max-width: 480px) {
      .user-stats {
        grid-template-columns: 1fr;
      }

      .header-action-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
      }
    }
  `]
})
export class DashboardHeaderComponent implements OnInit {
  @Input() config: any = {};
  @Input() profile: any;
  @Input() data: any;

  @Output() actionEvent = new EventEmitter<string>();
  @Output() navigationEvent = new EventEmitter<string>();

  constructor() {}

  ngOnInit() {
    // Set default configuration if not provided
    if (!this.config) {
      this.config = {
        showLogo: true,
        showBalance: true,
        showUserInfo: true,
        showStats: true,
        balanceLabel: 'Current Balance',
        balanceUnit: 'points'
      };
    }
  }

  onAction(action: string) {
    if (action?.startsWith('/')) {
      this.navigationEvent.emit(action);
    } else {
      this.actionEvent.emit(action);
    }
  }
}
