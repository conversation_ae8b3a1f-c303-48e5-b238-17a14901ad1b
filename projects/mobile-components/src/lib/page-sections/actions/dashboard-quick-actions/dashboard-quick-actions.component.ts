import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'lib-dashboard-quick-actions',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <div class="dashboard-quick-actions" [ngClass]="config?.class">
      <!-- Section Title -->
      <h2 class="section-title" *ngIf="config?.title">{{ config.title }}</h2>
      
      <!-- Actions Grid -->
      <div class="actions-grid" [ngClass]="getGridClass()">
        <div 
          *ngFor="let action of getFilteredActions()"
          class="action-item"
          [ngClass]="action.class"
          (click)="onAction(action)"
        >
          <div class="action-icon">
            <ion-icon [name]="action.icon"></ion-icon>
          </div>
          <div class="action-content">
            <h3 class="action-label">{{ action.label }}</h3>
            <p class="action-description" *ngIf="action.description">{{ action.description }}</p>
          </div>
          <div class="action-arrow" *ngIf="config?.showArrows">
            <ion-icon name="chevron-forward-outline"></ion-icon>
          </div>
        </div>
      </div>

      <!-- Additional Info -->
      <div class="additional-info" *ngIf="config?.additionalInfo">
        <p class="info-text">{{ config.additionalInfo }}</p>
      </div>
    </div>
  `,
  styles: [`
    .dashboard-quick-actions {
      padding: 1rem;
      margin-bottom: 1rem;
    }

    .section-title {
      font-size: 1.3rem;
      font-weight: bold;
      margin-bottom: 1rem;
      color: var(--ion-color-dark);
      text-align: left;
    }

    .actions-grid {
      display: grid;
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .actions-grid.grid-2 {
      grid-template-columns: repeat(2, 1fr);
    }

    .actions-grid.grid-3 {
      grid-template-columns: repeat(3, 1fr);
    }

    .actions-grid.grid-4 {
      grid-template-columns: repeat(2, 1fr);
    }

    .actions-grid.grid-1 {
      grid-template-columns: 1fr;
    }

    .action-item {
      display: flex;
      align-items: center;
      padding: 1.25rem;
      background: white;
      border-radius: 1rem;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid var(--ion-color-light);
      position: relative;
      overflow: hidden;
    }

    .action-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, var(--ion-color-primary), var(--ion-color-secondary));
      transform: scaleX(0);
      transition: transform 0.3s ease;
    }

    .action-item:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    }

    .action-item:hover::before {
      transform: scaleX(1);
    }

    .action-icon {
      flex-shrink: 0;
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-secondary));
      border-radius: 50%;
      margin-right: 1rem;
      position: relative;
    }

    .action-icon::after {
      content: '';
      position: absolute;
      inset: 2px;
      background: white;
      border-radius: 50%;
      z-index: 0;
    }

    .action-icon ion-icon {
      font-size: 1.5rem;
      color: var(--ion-color-primary);
      position: relative;
      z-index: 1;
    }

    .action-content {
      flex: 1;
    }

    .action-label {
      font-size: 1.1rem;
      font-weight: 600;
      margin: 0 0 0.25rem 0;
      color: var(--ion-color-dark);
    }

    .action-description {
      font-size: 0.85rem;
      margin: 0;
      color: var(--ion-color-medium);
      line-height: 1.4;
    }

    .action-arrow {
      flex-shrink: 0;
      margin-left: 0.75rem;
      opacity: 0.6;
      transition: all 0.3s ease;
    }

    .action-item:hover .action-arrow {
      opacity: 1;
      transform: translateX(3px);
    }

    .action-arrow ion-icon {
      font-size: 1.2rem;
      color: var(--ion-color-medium);
    }

    .additional-info {
      text-align: center;
      margin-top: 1rem;
    }

    .info-text {
      font-size: 0.9rem;
      color: var(--ion-color-medium);
      margin: 0;
      font-style: italic;
    }

    /* Special action item styles */
    .action-item.primary {
      background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-secondary));
      color: white;
    }

    .action-item.primary .action-label,
    .action-item.primary .action-description {
      color: white;
    }

    .action-item.primary .action-icon {
      background: rgba(255, 255, 255, 0.2);
    }

    .action-item.primary .action-icon::after {
      background: rgba(255, 255, 255, 0.9);
    }

    .action-item.secondary {
      background: var(--ion-color-light);
      border-color: var(--ion-color-medium);
    }

    .action-item.warning {
      border-left: 4px solid var(--ion-color-warning);
    }

    .action-item.danger {
      border-left: 4px solid var(--ion-color-danger);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .dashboard-quick-actions {
        padding: 0.75rem;
      }

      .actions-grid.grid-4,
      .actions-grid.grid-3 {
        grid-template-columns: repeat(2, 1fr);
      }

      .action-item {
        padding: 1rem;
      }

      .action-icon {
        width: 45px;
        height: 45px;
        margin-right: 0.75rem;
      }

      .action-icon ion-icon {
        font-size: 1.3rem;
      }

      .action-label {
        font-size: 1rem;
      }

      .action-description {
        font-size: 0.8rem;
      }
    }

    @media (max-width: 480px) {
      .actions-grid {
        grid-template-columns: 1fr;
      }

      .action-item {
        padding: 1rem;
      }

      .section-title {
        font-size: 1.2rem;
      }
    }
  `]
})
export class DashboardQuickActionsComponent implements OnInit {
  @Input() config: any = {};
  @Input() profile: any;
  @Input() data: any;

  @Output() actionEvent = new EventEmitter<string>();
  @Output() navigationEvent = new EventEmitter<string>();

  private defaultActions = [
    {
      label: 'Profile',
      description: 'View and edit your profile information',
      icon: 'person-outline',
      path: '/secure/profile',
      showWhen: 'authenticated'
    },
    {
      label: 'Security',
      description: 'Manage your account security settings',
      icon: 'shield-checkmark-outline',
      path: '/secure/security',
      showWhen: 'authenticated'
    },
    {
      label: 'Transactions',
      description: 'View your transaction history',
      icon: 'card-outline',
      path: '/secure/transactions',
      showWhen: 'authenticated'
    },
    {
      label: 'Virtual Card',
      description: 'Access your digital loyalty card',
      icon: 'wallet-outline',
      path: '/secure/virtualcard',
      showWhen: 'authenticated'
    }
  ];

  constructor() {}

  ngOnInit() {
    // Set default configuration if not provided
    if (!this.config) {
      this.config = {
        title: 'Quick Actions',
        showArrows: true,
        gridColumns: 2
      };
    }
  }

  getFilteredActions() {
    const actions = this.config?.actions || this.defaultActions;
    const isAuthenticated = !!this.profile;

    return actions.filter((action: any) => {
      if (action.showWhen === 'authenticated') return isAuthenticated;
      if (action.showWhen === 'anonymous') return !isAuthenticated;
      return true; // showWhen === 'always' or undefined
    });
  }

  getGridClass() {
    const columns = this.config?.gridColumns || 2;
    return `grid-${columns}`;
  }

  onAction(action: any) {
    if (action.path) {
      this.navigationEvent.emit(action.path);
    } else if (action.action) {
      this.actionEvent.emit(action.action);
    }
  }
}
