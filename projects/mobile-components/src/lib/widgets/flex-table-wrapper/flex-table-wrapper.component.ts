import { Component, Input, ContentChild, TemplateRef } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'lib-flex-table-wrapper',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './flex-table-wrapper.component.html',
  styleUrl: './flex-table-wrapper.component.css'
})
export class FlexTableWrapperComponent {
  // Standard inputs for customization
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() maxHeight: string = '';
  @Input() scrollable: boolean = true;
  @Input() bordered: boolean = true;
  @Input() striped: boolean = false;
  @Input() hoverable: boolean = true;
  @Input() responsive: boolean = true;
  @Input() minWidth: string = '100%';
  @Input() overflow: 'auto' | 'scroll' | 'hidden' | 'visible' = 'auto';

  // Content projection
  @ContentChild('tableContent') tableContent?: TemplateRef<any>;

  // Computed getter for wrapper CSS classes
  get computedClasses(): string {
    const baseClasses = 'flex-table-wrapper-widget';
    
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };

    const variantClasses = {
      default: 'bg-white border-gray-200',
      primary: 'bg-blue-50 border-blue-200',
      secondary: 'bg-gray-50 border-gray-300',
      success: 'bg-green-50 border-green-200',
      warning: 'bg-yellow-50 border-yellow-200',
      danger: 'bg-red-50 border-red-200'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const borderClass = this.bordered ? 'border' : '';
    const responsiveClass = this.responsive ? 'overflow-x-auto' : '';
    
    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      borderClass,
      responsiveClass,
      this.className
    ].filter(Boolean).join(' ');
  }

  // Computed getter for table container classes
  get tableContainerClasses(): string {
    const scrollableClass = this.scrollable ? `overflow-${this.overflow}` : '';
    const hoverableClass = this.hoverable ? 'hover-enabled' : '';
    const stripedClass = this.striped ? 'striped-rows' : '';
    
    const classes = [
      'table-container',
      scrollableClass,
      hoverableClass,
      stripedClass
    ].filter(Boolean).join(' ');
    
    return classes;
  }

  // Computed getter for inline styles
  get containerStyles(): any {
    const styles: any = {};
    
    if (this.maxHeight) {
      styles.maxHeight = this.maxHeight;
    }
    
    if (this.minWidth) {
      styles.minWidth = this.minWidth;
    }
    
    return styles;
  }
}
