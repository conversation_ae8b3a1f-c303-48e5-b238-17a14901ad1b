/* Flex Table Wrapper Widget Styles */
.flex-table-wrapper-widget {
  position: relative;
  background: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* Table Container */
.table-container {
  position: relative;
  width: 100%;
}

/* Scrollable styles */
.table-container.overflow-auto {
  overflow: auto;
}

.table-container.overflow-scroll {
  overflow: scroll;
}

.table-container.overflow-hidden {
  overflow: hidden;
}

/* Custom scrollbar styling */
.table-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Striped rows */
.table-container.striped-rows :nth-child(even) {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Hoverable rows */
.table-container.hover-enabled :hover {
  background-color: rgba(0, 0, 0, 0.04);
  transition: background-color 0.2s ease;
}

/* Responsive table improvements */
@media (max-width: 768px) {
  .flex-table-wrapper-widget.overflow-x-auto {
    overflow-x: auto;
  }
  
  .table-container {
    min-width: 600px;
  }
}

/* Focus states for accessibility */
.flex-table-wrapper-widget:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Loading state animation */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.table-container.loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Scroll indicator styling */
.flex-table-wrapper-widget .absolute.bottom-0 {
  pointer-events: none;
  z-index: 10;
}