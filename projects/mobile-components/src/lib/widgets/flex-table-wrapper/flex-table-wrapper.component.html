<!-- Flex Table Wrapper Widget -->
<div [class]="computedClasses" [ngStyle]="containerStyles">
  <!-- Table Container -->
  <div [class]="tableContainerClasses">
    <!-- Content projection for table content -->
    <ng-content select="[slot=table]"></ng-content>
    
    <!-- Default content when no table is provided -->
    <div *ngIf="!tableContent" class="p-8 text-center bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg">
      <div class="flex flex-col items-center">
        <i class="fas fa-table text-3xl text-gray-400 mb-3" aria-hidden="true"></i>
        <h4 class="text-lg font-medium text-gray-900 mb-2">Table Wrapper Ready</h4>
        <p class="text-sm text-gray-600 mb-4 max-w-md">
          This wrapper provides container functionality for tables with scrolling, 
          responsive design, and styling options.
        </p>
        <div class="grid grid-cols-2 gap-2 text-xs text-gray-500">
          <div class="flex items-center space-x-1">
            <i class="fas fa-arrows-alt-h text-blue-500" aria-hidden="true"></i>
            <span>Horizontal Scroll</span>
          </div>
          <div class="flex items-center space-x-1">
            <i class="fas fa-arrows-alt-v text-green-500" aria-hidden="true"></i>
            <span>Vertical Scroll</span>
          </div>
          <div class="flex items-center space-x-1">
            <i class="fas fa-mobile-alt text-purple-500" aria-hidden="true"></i>
            <span>Responsive</span>
          </div>
          <div class="flex items-center space-x-1">
            <i class="fas fa-palette text-orange-500" aria-hidden="true"></i>
            <span>Customizable</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Template content if provided -->
    <ng-container *ngIf="tableContent">
      <ng-container *ngTemplateOutlet="tableContent"></ng-container>
    </ng-container>
  </div>
  
  <!-- Scroll indicators for better UX -->
  <div *ngIf="scrollable && maxHeight" class="absolute bottom-0 left-0 right-0 h-1 bg-gray-100">
    <div class="h-full bg-blue-500 transition-all duration-300" style="width: 30%"></div>
  </div>
</div>
