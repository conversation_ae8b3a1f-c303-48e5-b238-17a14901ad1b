<div 
  (click)="onRowClick($event)" 
  (mouseenter)="onRowHover()"
  [attr.draggable]="draggable && !disabled"
  (dragstart)="onDragStart($event)"
  (dragover)="onDragOver($event)"
  (drop)="onDrop($event)"
  [attr.role]="clickable ? 'button' : null"
  [attr.tabindex]="clickable && !disabled ? 0 : null"
>
  <div [class]="cellContainerClasses">
    <!-- Selection Checkbox -->
    <div *ngIf="selectable" class="flex items-center px-3">
      <input
        type="checkbox"
        [checked]="selected"
        (change)="onRowSelect()"
        (click)="$event.stopPropagation()"
        [class]="selectCheckboxClasses"
        [disabled]="disabled"
      >
    </div>

    <!-- Expand Button -->
    <div *ngIf="expandable" class="flex items-center px-2">
      <button
        [class]="expandButtonClasses"
        (click)="onRowExpand(); $event.stopPropagation()"
        [disabled]="disabled"
        [attr.aria-expanded]="expanded"
        aria-label="Expand row"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </button>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading" class="flex items-center px-3">
      <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
    </div>

    <!-- Data Cells -->
    <div 
      *ngFor="let key of getObjectKeys(data); trackBy: trackByKey"
      [class]="getCellClasses(key, getCellValue(key))"
      (click)="onCellClick(key, getCellValue(key), $event)"
    >
      <!-- Cell Content -->
      <div class="flex items-center space-x-2 min-w-0">
        <!-- Status Badge -->
        <span 
          *ngIf="key.toLowerCase().includes('status')" 
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
          [class.bg-green-100]="getCellValue(key) && stringValue(getCellValue(key)).toLowerCase().includes('active')"
          [class.text-green-800]="getCellValue(key) && stringValue(getCellValue(key)).toLowerCase().includes('active')"
          [class.bg-yellow-100]="getCellValue(key) && stringValue(getCellValue(key)).toLowerCase().includes('pending')"
          [class.text-yellow-800]="getCellValue(key) && stringValue(getCellValue(key)).toLowerCase().includes('pending')"
          [class.bg-red-100]="getCellValue(key) && stringValue(getCellValue(key)).toLowerCase().includes('inactive')"
          [class.text-red-800]="getCellValue(key) && stringValue(getCellValue(key)).toLowerCase().includes('inactive')"
          [class.bg-gray-100]="!(stringValue(getCellValue(key)).toLowerCase().includes('active') || stringValue(getCellValue(key)).toLowerCase().includes('pending') || stringValue(getCellValue(key)).toLowerCase().includes('inactive'))"
          [class.text-gray-800]="!(stringValue(getCellValue(key)).toLowerCase().includes('active') || stringValue(getCellValue(key)).toLowerCase().includes('pending') || stringValue(getCellValue(key)).toLowerCase().includes('inactive'))"
        >
          {{ formatCellValue(getCellValue(key)) }}
        </span>

        <!-- Email Link -->
        <a 
          *ngIf="key.toLowerCase().includes('email') && getCellValue(key)"
          [href]="'mailto:' + getCellValue(key)"
          class="text-blue-600 hover:text-blue-800 hover:underline"
          (click)="$event.stopPropagation()"
        >
          {{ formatCellValue(getCellValue(key)) }}
        </a>

        <!-- Regular Text -->
        <span 
          *ngIf="!key.toLowerCase().includes('status') && !key.toLowerCase().includes('email')"
          class="truncate"
          [title]="formatCellValue(getCellValue(key))"
        >
          {{ formatCellValue(getCellValue(key)) }}
        </span>
      </div>
    </div>

    <!-- Actions Column -->
    <div class="flex items-center justify-end px-3 space-x-2">
      <!-- Edit Action -->
      <button
        class="text-gray-400 hover:text-blue-600 focus:outline-none focus:text-blue-600"
        (click)="onActionClick('edit', $event)"
        [disabled]="disabled"
        title="Edit"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
        </svg>
      </button>

      <!-- Delete Action -->
      <button
        class="text-gray-400 hover:text-red-600 focus:outline-none focus:text-red-600"
        (click)="onActionClick('delete', $event)"
        [disabled]="disabled"
        title="Delete"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
        </svg>
      </button>

      <!-- More Actions Menu -->
      <button
        class="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
        (click)="onActionClick('menu', $event)"
        [disabled]="disabled"
        title="More actions"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
        </svg>
      </button>
    </div>
  </div>

  <!-- Expanded Content -->
  <div 
    *ngIf="expandable && expanded" 
    class="w-full px-6 py-4 bg-gray-50 border-t border-gray-200"
  >
    <div class="text-sm text-gray-600">
      <!-- Custom expanded content slot -->
      <ng-content select="[slot=expanded]"></ng-content>
      
      <!-- Default expanded content -->
      <div *ngIf="!hasExpandedContent()" class="space-y-2">
        <h4 class="font-medium text-gray-900">Additional Details</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div *ngFor="let key of getObjectKeys(data)" class="flex justify-between">
            <span class="font-medium">{{ key }}:</span>
            <span>{{ formatCellValue(getCellValue(key)) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Selection Indicator -->
  <div 
    *ngIf="selected" 
    class="absolute inset-y-0 left-0 w-1 bg-blue-500"
  ></div>

  <!-- Drag Handle -->
  <div 
    *ngIf="draggable" 
    class="absolute inset-y-0 left-0 w-1 bg-gray-300 hover:bg-gray-400 cursor-move opacity-0 hover:opacity-100 transition-opacity"
  ></div>
</div>
