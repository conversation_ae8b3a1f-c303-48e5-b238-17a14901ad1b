import { Component, Input, Output, EventEmitter, HostBinding } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'lib-flex-table-row',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './flex-table-row.component.html',
  styleUrl: './flex-table-row.component.css'
})
export class FlexTableRowComponent {
  @Input() data: Record<string, any> = {
    name: '<PERSON>',
    email: '<EMAIL>',
    status: 'Active',
    department: 'Engineering',
    role: 'Senior Developer'
  };
  @Input() index: number = 0;
  @Input() selected: boolean = false;
  @Input() highlighted: boolean = false;
  @Input() disabled: boolean = false;
  @Input() clickable: boolean = true;
  @Input() hoverable: boolean = true;
  @Input() striped: boolean = false;
  @Input() bordered: boolean = false;
  @Input() selectable: boolean = false;
  @Input() expandable: boolean = false;
  @Input() expanded: boolean = false;
  @Input() loading: boolean = false;
  
  // Row behavior
  @Input() sticky: boolean = false;
  @Input() draggable: boolean = false;
  @Input() droppable: boolean = false;
  
  // Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'subtle' | 'bordered' | 'card' | 'highlighted' | 'warning' | 'error' | 'success' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'xl' = 'none';
  @Input() spacing: 'none' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  
  @Output() rowClick = new EventEmitter<{data: Record<string, any>, index: number}>();
  @Output() rowSelect = new EventEmitter<{selected: boolean, data: Record<string, any>, index: number}>();
  @Output() rowExpand = new EventEmitter<{expanded: boolean, data: Record<string, any>, index: number}>();
  @Output() rowHover = new EventEmitter<{data: Record<string, any>, index: number}>();
  @Output() cellClick = new EventEmitter<{key: string, value: any, data: Record<string, any>, index: number}>();
  @Output() actionClick = new EventEmitter<{action: string, data: Record<string, any>, index: number}>();

  @HostBinding('class') get hostClasses(): string {
    return this.rowClasses;
  }

  get rowClasses(): string {
    const baseClasses = [
      'flex-table-row',
      'flex',
      'w-full',
      'transition-colors',
      'duration-200'
    ];

    // Size classes (affects padding and text)
    const sizeClasses = {
      xs: ['text-xs', 'min-h-8'],
      sm: ['text-sm', 'min-h-10'],
      md: ['text-sm', 'min-h-12'],
      lg: ['text-base', 'min-h-14'],
      xl: ['text-lg', 'min-h-16']
    };
    baseClasses.push(...sizeClasses[this.size]);

    // Variant styling
    const variantClasses = {
      default: ['bg-white', 'border-gray-200'],
      subtle: ['bg-gray-50', 'border-gray-200'],
      bordered: ['bg-white', 'border', 'border-gray-200'],
      card: ['bg-white', 'border', 'border-gray-200', 'shadow-sm'],
      highlighted: ['bg-blue-50', 'border-blue-200'],
      warning: ['bg-yellow-50', 'border-yellow-200'],
      error: ['bg-red-50', 'border-red-200'],
      success: ['bg-green-50', 'border-green-200']
    };
    baseClasses.push(...variantClasses[this.variant]);

    // State classes
    if (this.selected) {
      baseClasses.push('bg-blue-100', 'border-blue-300', 'shadow-sm');
    }

    if (this.highlighted) {
      baseClasses.push('ring-2', 'ring-blue-500', 'ring-opacity-50');
    }

    if (this.striped && this.index % 2 === 1) {
      baseClasses.push('bg-gray-50');
    }

    // Interactive states
    if (this.clickable && !this.disabled) {
      baseClasses.push('cursor-pointer');
      
      if (this.hoverable) {
        baseClasses.push('hover:bg-gray-50', 'hover:shadow-sm');
      }
    }

    if (this.disabled) {
      baseClasses.push('opacity-50', 'cursor-not-allowed');
    }

    if (this.loading) {
      baseClasses.push('animate-pulse');
    }

    // Sticky row
    if (this.sticky) {
      baseClasses.push('sticky', 'top-0', 'z-10');
    }

    // Draggable
    if (this.draggable) {
      baseClasses.push('cursor-move');
    }

    // Rounded corners
    const roundedClasses = {
      none: [],
      sm: ['rounded-sm'],
      md: ['rounded-md'],
      lg: ['rounded-lg'],
      xl: ['rounded-xl']
    };
    baseClasses.push(...roundedClasses[this.rounded]);

    // Spacing
    const spacingClasses = {
      none: [],
      sm: ['gap-2'],
      md: ['gap-4'],
      lg: ['gap-6'],
      xl: ['gap-8']
    };
    baseClasses.push(...spacingClasses[this.spacing]);

    if (this.className) {
      baseClasses.push(this.className);
    }

    return baseClasses.join(' ');
  }

  get cellContainerClasses(): string {
    const baseClasses = [
      'flex',
      'items-center',
      'w-full'
    ];

    // Spacing between cells
    baseClasses.push(...this.getSpacingClasses());

    return baseClasses.join(' ');
  }

  get expandButtonClasses(): string {
    const baseClasses = [
      'flex',
      'items-center',
      'justify-center',
      'w-6',
      'h-6',
      'rounded',
      'text-gray-400',
      'hover:text-gray-600',
      'hover:bg-gray-100',
      'focus:outline-none',
      'focus:ring-2',
      'focus:ring-blue-500',
      'transition-transform',
      'duration-200'
    ];

    if (this.expanded) {
      baseClasses.push('transform', 'rotate-90');
    }

    return baseClasses.join(' ');
  }

  get selectCheckboxClasses(): string {
    return [
      'h-4',
      'w-4',
      'text-blue-600',
      'focus:ring-blue-500',
      'border-gray-300',
      'rounded'
    ].join(' ');
  }

  private getSpacingClasses(): string[] {
    const spacingClasses = {
      none: [],
      sm: ['space-x-2'],
      md: ['space-x-4'],
      lg: ['space-x-6'],
      xl: ['space-x-8']
    };
    return spacingClasses[this.spacing];
  }

  onRowClick(event: Event): void {
    if (this.disabled) return;
    
    // Don't trigger row click if clicking on interactive elements
    const target = event.target as HTMLElement;
    if (target.tagName === 'INPUT' || target.tagName === 'BUTTON' || target.closest('button')) {
      return;
    }
    
    this.rowClick.emit({ data: this.data, index: this.index });
  }

  onRowSelect(): void {
    if (this.disabled) return;
    
    this.selected = !this.selected;
    this.rowSelect.emit({ selected: this.selected, data: this.data, index: this.index });
  }

  onRowExpand(): void {
    if (this.disabled || !this.expandable) return;
    
    this.expanded = !this.expanded;
    this.rowExpand.emit({ expanded: this.expanded, data: this.data, index: this.index });
  }

  onRowHover(): void {
    if (this.disabled || !this.hoverable) return;
    
    this.rowHover.emit({ data: this.data, index: this.index });
  }

  onCellClick(key: string, value: any, event: Event): void {
    event.stopPropagation();
    this.cellClick.emit({ key, value, data: this.data, index: this.index });
  }

  onActionClick(action: string, event: Event): void {
    event.stopPropagation();
    this.actionClick.emit({ action, data: this.data, index: this.index });
  }

  trackByKey(index: number, item: any): any {
    return item.key || index;
  }

  getObjectKeys(obj: Record<string, any>): string[] {
    return Object.keys(obj);
  }

  getCellValue(key: string): any {
    return this.data[key];
  }

  formatCellValue(value: any): string {
    if (value === null || value === undefined) {
      return '';
    }
    
    if (typeof value === 'boolean') {
      return value ? 'Yes' : 'No';
    }
    
    if (typeof value === 'object' && value instanceof Date) {
      return value.toLocaleDateString();
    }
    
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    
    return String(value);
  }

  getCellClasses(key: string, value: any): string {
    const baseClasses = [
      'flex-1',
      'min-w-0',
      'px-3',
      'py-2'
    ];

    // Special styling for certain field types
    if (key.toLowerCase().includes('email')) {
      baseClasses.push('text-blue-600', 'hover:text-blue-800', 'cursor-pointer');
    } else if (key.toLowerCase().includes('status')) {
      const status = String(value).toLowerCase();
      if (status.includes('active') || status.includes('success')) {
        baseClasses.push('text-green-700');
      } else if (status.includes('pending') || status.includes('warning')) {
        baseClasses.push('text-yellow-700');
      } else if (status.includes('inactive') || status.includes('error')) {
        baseClasses.push('text-red-700');
      }
    }

    return baseClasses.join(' ');
  }

  onDragStart(event: DragEvent): void {
    if (!this.draggable || this.disabled) {
      event.preventDefault();
      return;
    }
    
    if (event.dataTransfer) {
      event.dataTransfer.setData('text/plain', JSON.stringify({
        index: this.index,
        data: this.data
      }));
      event.dataTransfer.effectAllowed = 'move';
    }
  }

  onDragOver(event: DragEvent): void {
    if (!this.droppable || this.disabled) return;
    
    event.preventDefault();
    event.dataTransfer!.dropEffect = 'move';
  }

  onDrop(event: DragEvent): void {
    if (!this.droppable || this.disabled) return;
    
    event.preventDefault();
    // Handle drop logic would be implemented by parent component
  }

  hasExpandedContent(): boolean {
    // This would detect if there's projected content in the expanded slot
    // In a real implementation, you might use ViewChild or ContentChild
    return false;
  }

  stringValue(value: any): string {
    return String(value);
  }
}
