<div
  [class]="itemClasses"
  (click)="onItemClick()"
  [attr.aria-selected]="item.selected"
  [attr.aria-disabled]="item.disabled"
  [attr.role]="selectable ? 'option' : 'listitem'"
  [attr.tabindex]="item.disabled ? -1 : 0"
>
  <!-- Selection Indicator -->
  <div *ngIf="showSelection && selectable" class="flex-shrink-0 mr-3">
    <div class="w-4 h-4 border-2 rounded transition-colors"
         [class.border-blue-500]="item.selected"
         [class.bg-blue-500]="item.selected"
         [class.border-gray-300]="!item.selected">
      <svg *ngIf="item.selected" class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
      </svg>
    </div>
  </div>

  <!-- Icon or Avatar -->
  <div *ngIf="showIcon || showAvatar" class="flex-shrink-0 mr-3">
    <!-- Avatar -->
    <div *ngIf="showAvatar && item.avatar" class="w-8 h-8 rounded-full overflow-hidden">
      <img [src]="item.avatar" [alt]="item.label" class="w-full h-full object-cover" />
    </div>

    <!-- Avatar Initials -->
    <div *ngIf="showAvatar && !item.avatar"
         class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-sm font-medium text-gray-700">
      {{ getAvatarInitials(item.label) }}
    </div>

    <!-- Icon -->
    <div *ngIf="showIcon && !showAvatar"
         class="w-6 h-6 text-gray-400 cursor-pointer hover:text-gray-600"
         (click)="onIconClick($event)">
      <svg class="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <!-- Document icon -->
        <path *ngIf="item.icon === 'document'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        <!-- User icon -->
        <path *ngIf="item.icon === 'user'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
        <!-- Folder icon -->
        <path *ngIf="item.icon === 'folder'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z"></path>
        <!-- Default icon -->
        <path *ngIf="!item.icon || item.icon === 'default'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
      </svg>
    </div>
  </div>

  <!-- Content -->
  <div class="flex-1 min-w-0">
    <div class="flex items-start justify-between">
      <div class="flex-1 min-w-0">
        <!-- Label -->
        <p class="text-sm font-medium text-gray-900 truncate">{{ item.label }}</p>

        <!-- Subtitle -->
        <p *ngIf="showMetadata && item.metadata?.subtitle"
           class="text-xs text-gray-500 truncate mt-0.5">
          {{ item.metadata?.subtitle }}
        </p>

        <!-- Description -->
        <p *ngIf="showDescription && item.description"
           class="text-sm text-gray-600 mt-1 line-clamp-2">
          {{ item.description }}
        </p>
      </div>

      <!-- Badge -->
      <div *ngIf="showBadge && item.badge"
           class="flex-shrink-0 ml-2 cursor-pointer"
           (click)="onBadgeClick($event)">
        <span [class]="getBadgeClasses()">{{ item.badge.text }}</span>
      </div>
    </div>

    <!-- Metadata -->
    <div *ngIf="showMetadata && item.metadata" class="mt-2 space-y-1">
      <!-- Status and Timestamp -->
      <div class="flex items-center justify-between text-xs text-gray-500">
        <span *ngIf="item.metadata.status"
              class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"
              [class.bg-green-100]="item.metadata.status === 'Active'"
              [class.text-green-800]="item.metadata.status === 'Active'"
              [class.bg-gray-100]="item.metadata.status !== 'Active'"
              [class.text-gray-800]="item.metadata.status !== 'Active'">
          {{ item.metadata.status }}
        </span>

        <span *ngIf="item.metadata.timestamp">
          {{ formatTimestamp(item.metadata.timestamp) }}
        </span>
      </div>

      <!-- Tags -->
      <div *ngIf="item.metadata.tags && item.metadata.tags.length > 0"
           class="flex flex-wrap gap-1">
        <span *ngFor="let tag of item.metadata.tags"
              class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
          {{ tag }}
        </span>
      </div>
    </div>
  </div>

  <!-- Action Arrow -->
  <div *ngIf="clickable && !item.disabled" class="flex-shrink-0 ml-3">
    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
    </svg>
  </div>
</div>
