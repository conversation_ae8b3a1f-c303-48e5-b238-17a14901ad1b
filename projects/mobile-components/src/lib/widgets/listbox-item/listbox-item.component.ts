import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface ListboxItemData {
  id: string | number;
  label: string;
  value?: any;
  description?: string;
  icon?: string;
  avatar?: string;
  badge?: {
    text: string;
    color?: string;
  };
  metadata?: {
    subtitle?: string;
    timestamp?: Date | string;
    status?: string;
    tags?: string[];
  };
  selected?: boolean;
  disabled?: boolean;
  group?: string;
}

@Component({
  selector: 'base-listbox-item',
  templateUrl: './listbox-item.component.html',
  styleUrls: ['./listbox-item.component.css'],
  standalone: true,
  imports: [CommonModule]
})
export class ListboxItemComponent {
  // Standard Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() item: ListboxItemData = {
    id: 1,
    label: 'Sample List Item',
    description: 'This is a sample listbox item with description and metadata',
    icon: 'document',
    badge: { text: 'New', color: 'blue' },
    metadata: {
      subtitle: 'Sample subtitle',
      timestamp: new Date(),
      status: 'Active',
      tags: ['Important', 'Featured']
    },
    selected: false,
    disabled: false
  };

  @Input() showIcon: boolean = true;
  @Input() showAvatar: boolean = false;
  @Input() showDescription: boolean = true;
  @Input() showBadge: boolean = true;
  @Input() showMetadata: boolean = true;
  @Input() showSelection: boolean = true;
  @Input() selectable: boolean = true;
  @Input() clickable: boolean = true;
  @Input() multiSelect: boolean = false;

  // Event outputs
  @Output() itemClick = new EventEmitter<ListboxItemData>();
  @Output() itemSelect = new EventEmitter<{item: ListboxItemData, selected: boolean}>();
  @Output() iconClick = new EventEmitter<ListboxItemData>();
  @Output() badgeClick = new EventEmitter<ListboxItemData>();

  onItemClick() {
    if (!this.item.disabled && this.clickable) {
      if (this.selectable) {
        this.item.selected = this.multiSelect ? !this.item.selected : true;
        this.itemSelect.emit({ item: this.item, selected: this.item.selected });
      }
      this.itemClick.emit(this.item);
    }
  }

  onIconClick(event: Event) {
    event.stopPropagation();
    if (!this.item.disabled) {
      this.iconClick.emit(this.item);
    }
  }

  onBadgeClick(event: Event) {
    event.stopPropagation();
    if (!this.item.disabled) {
      this.badgeClick.emit(this.item);
    }
  }

  formatTimestamp(timestamp: Date | string): string {
    const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  }

  getAvatarInitials(name: string): string {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  }

  get itemClasses(): string {
    const baseClasses = 'flex items-center p-3 transition-colors border-b border-gray-100 last:border-b-0';

    const sizeClasses = {
      xs: 'p-2 text-xs',
      sm: 'p-2.5 text-sm',
      md: 'p-3 text-base',
      lg: 'p-4 text-lg',
      xl: 'p-5 text-xl'
    };

    const variantClasses = {
      default: 'hover:bg-gray-50',
      primary: 'hover:bg-blue-50',
      secondary: 'hover:bg-purple-50',
      success: 'hover:bg-green-50',
      warning: 'hover:bg-yellow-50',
      danger: 'hover:bg-red-50'
    };

    const selectedClasses = this.item.selected
      ? 'bg-blue-50 border-blue-200'
      : '';

    const disabledClasses = this.item.disabled
      ? 'opacity-50 cursor-not-allowed'
      : 'cursor-pointer';

    const clickableClasses = this.clickable && !this.item.disabled
      ? variantClasses[this.variant]
      : '';

    return [
      baseClasses,
      sizeClasses[this.size],
      selectedClasses,
      disabledClasses,
      clickableClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  getBadgeClasses(): string {
    const baseClasses = 'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium';
    const color = this.item.badge?.color || 'gray';
    const colorClasses = {
      gray: 'bg-gray-100 text-gray-800',
      blue: 'bg-blue-100 text-blue-800',
      green: 'bg-green-100 text-green-800',
      yellow: 'bg-yellow-100 text-yellow-800',
      red: 'bg-red-100 text-red-800',
      purple: 'bg-purple-100 text-purple-800'
    };

    return [baseClasses, colorClasses[color as keyof typeof colorClasses] || colorClasses.gray].join(' ');
  }
}
