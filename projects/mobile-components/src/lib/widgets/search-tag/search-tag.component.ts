import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

export interface SearchTag {
  id: string;
  label: string;
  value: string;
  color?: string;
  category?: string;
}

@Component({
  selector: 'lib-search-tag',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './search-tag.component.html',
  styleUrl: './search-tag.component.css'
})
export class SearchTagComponent implements OnInit {
  // Standard inputs for customization
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() placeholder: string = 'Search and add tags...';
  @Input() availableTags: SearchTag[] = [
    { id: '1', label: 'Frontend', value: 'frontend', color: 'blue' },
    { id: '2', label: 'Backend', value: 'backend', color: 'green' },
    { id: '3', label: 'Database', value: 'database', color: 'purple' },
    { id: '4', label: 'API', value: 'api', color: 'orange' },
    { id: '5', label: 'Testing', value: 'testing', color: 'red' },
    { id: '6', label: 'Security', value: 'security', color: 'gray' }
  ];

  @Input() selectedTags: SearchTag[] = [];
  @Input() allowCustomTags: boolean = true;
  @Input() maxTags: number = 10;
  @Input() showSuggestions: boolean = true;
  @Input() caseSensitive: boolean = false;
  @Input() disabled: boolean = false;
  @Input() readonly: boolean = false;

  // Component state
  searchTerm: string = '';
  showDropdown: boolean = false;
  filteredTags: SearchTag[] = [];
  focusedIndex: number = -1;

  // Event outputs
  @Output() tagsChange = new EventEmitter<SearchTag[]>();
  @Output() tagAdd = new EventEmitter<SearchTag>();
  @Output() tagRemove = new EventEmitter<SearchTag>();
  @Output() searchChange = new EventEmitter<string>();

  ngOnInit(): void {
    this.filteredTags = this.availableTags;
  }

  // Computed getter for CSS classes
  get computedClasses(): string {
    const baseClasses = 'search-tag-widget';
    
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };

    const variantClasses = {
      default: 'border-gray-300 focus:border-blue-500 focus:ring-blue-500',
      primary: 'border-blue-300 focus:border-blue-500 focus:ring-blue-500',
      secondary: 'border-gray-400 focus:border-gray-500 focus:ring-gray-500',
      success: 'border-green-300 focus:border-green-500 focus:ring-green-500',
      warning: 'border-yellow-300 focus:border-yellow-500 focus:ring-yellow-500',
      danger: 'border-red-300 focus:border-red-500 focus:ring-red-500'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const disabledClass = this.disabled ? 'opacity-50 cursor-not-allowed' : '';
    
    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      disabledClass,
      'relative',
      this.className
    ].filter(Boolean).join(' ');
  }

  addTag(tag: SearchTag): void {
    if (this.selectedTags.length >= this.maxTags) return;
    if (this.selectedTags.find(t => t.id === tag.id)) return;

    const newSelectedTags = [...this.selectedTags, tag];
    this.selectedTags = newSelectedTags;
    this.tagsChange.emit(newSelectedTags);
    this.tagAdd.emit(tag);
    this.searchTerm = '';
    this.showDropdown = false;
    this.focusedIndex = -1;
  }

  removeTag(tag: SearchTag): void {
    if (this.disabled || this.readonly) return;

    const newSelectedTags = this.selectedTags.filter(t => t.id !== tag.id);
    this.selectedTags = newSelectedTags;
    this.tagsChange.emit(newSelectedTags);
    this.tagRemove.emit(tag);
  }

  getTagColorClasses(color?: string): string {
    const colorMap = {
      blue: 'bg-blue-100 text-blue-800 border-blue-200',
      green: 'bg-green-100 text-green-800 border-green-200',
      purple: 'bg-purple-100 text-purple-800 border-purple-200',
      orange: 'bg-orange-100 text-orange-800 border-orange-200',
      red: 'bg-red-100 text-red-800 border-red-200',
      gray: 'bg-gray-100 text-gray-800 border-gray-200'
    };

    return colorMap[color as keyof typeof colorMap] || colorMap.gray;
  }
}
