import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface TimelineEvent {
  id: string | number;
  title: string;
  description?: string;
  timestamp: Date | string;
  type?: 'default' | 'success' | 'warning' | 'danger' | 'info';
  icon?: string;
  user?: {
    name: string;
    avatar?: string;
  };
  metadata?: {
    location?: string;
    duration?: string;
    status?: string;
    tags?: string[];
  };
  clickable?: boolean;
  expandable?: boolean;
  expanded?: boolean;
}

@Component({
  selector: 'lib-timeline-compact',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './timeline-compact.component.html',
  styleUrl: './timeline-compact.component.css'
})
export class TimelineCompactComponent {
  // Standard Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() events: TimelineEvent[] = [
    {
      id: 1,
      title: 'Project Started',
      description: 'Initial project setup and team onboarding completed',
      timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      type: 'success',
      user: { name: 'John Doe', avatar: '' },
      metadata: { status: 'Completed', tags: ['Setup', 'Team'] },
      clickable: true,
      expandable: true,
      expanded: false
    },
    {
      id: 2,
      title: 'Design Phase',
      description: 'UI/UX design mockups and wireframes created',
      timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      type: 'info',
      user: { name: 'Jane Smith', avatar: '' },
      metadata: { status: 'In Progress', duration: '2 weeks', tags: ['Design', 'UI'] },
      clickable: true,
      expandable: true,
      expanded: false
    },
    {
      id: 3,
      title: 'Development Sprint 1',
      description: 'Core functionality implementation',
      timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      type: 'warning',
      user: { name: 'Mike Johnson', avatar: '' },
      metadata: { status: 'Active', duration: '3 weeks', tags: ['Development', 'Sprint'] },
      clickable: true,
      expandable: true,
      expanded: false
    },
    {
      id: 4,
      title: 'Testing Phase',
      description: 'Quality assurance and bug fixes',
      timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      type: 'default',
      user: { name: 'Sarah Wilson', avatar: '' },
      metadata: { status: 'Pending', tags: ['Testing', 'QA'] },
      clickable: true,
      expandable: true,
      expanded: false
    }
  ];

  @Input() showIcons: boolean = true;
  @Input() showUsers: boolean = true;
  @Input() showTimestamps: boolean = true;
  @Input() showDescriptions: boolean = true;
  @Input() showMetadata: boolean = true;
  @Input() allowExpand: boolean = true;
  @Input() allowClick: boolean = true;
  @Input() orientation: 'vertical' | 'horizontal' = 'vertical';
  @Input() lineStyle: 'solid' | 'dashed' | 'dotted' = 'solid';

  // Event outputs
  @Output() eventClick = new EventEmitter<TimelineEvent>();
  @Output() eventExpand = new EventEmitter<TimelineEvent>();
  @Output() userClick = new EventEmitter<{event: TimelineEvent, user: any}>();

  onEventClick(event: TimelineEvent) {
    if (event.clickable && this.allowClick) {
      this.eventClick.emit(event);
    }
  }

  onEventExpand(event: TimelineEvent) {
    if (event.expandable && this.allowExpand) {
      event.expanded = !event.expanded;
      this.eventExpand.emit(event);
    }
  }

  onUserClick(event: TimelineEvent) {
    if (event.user && this.allowClick) {
      this.userClick.emit({ event, user: event.user });
    }
  }

  formatTimestamp(timestamp: Date | string): string {
    const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMins = Math.floor(diffMs / (1000 * 60));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  }

  getAvatarInitials(name: string): string {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  }

  getEventIconClasses(event: TimelineEvent): string {
    const baseClasses = 'w-8 h-8 rounded-full flex items-center justify-center text-white';
    const typeClasses = {
      default: 'bg-gray-500',
      success: 'bg-green-500',
      warning: 'bg-yellow-500',
      danger: 'bg-red-500',
      info: 'bg-blue-500'
    };

    return [baseClasses, typeClasses[event.type || 'default']].join(' ');
  }

  get containerClasses(): string {
    const baseClasses = 'relative';
    const orientationClasses = this.orientation === 'horizontal'
      ? 'flex space-x-8 overflow-x-auto'
      : 'space-y-6';

    return [baseClasses, orientationClasses, this.className].filter(Boolean).join(' ');
  }

  get timelineLineClasses(): string {
    const baseClasses = 'absolute bg-gray-300';
    const orientationClasses = this.orientation === 'horizontal'
      ? 'top-4 left-4 right-4 h-0.5'
      : 'left-4 top-8 bottom-0 w-0.5';

    const styleClasses = {
      solid: '',
      dashed: 'border-dashed',
      dotted: 'border-dotted'
    };

    return [baseClasses, orientationClasses, styleClasses[this.lineStyle]].filter(Boolean).join(' ');
  }

  trackByEvent(index: number, event: TimelineEvent): any {
    return event.id;
  }
}
