<div [class]="containerClasses">
  <!-- Timeline Line -->
  <div [class]="timelineLineClasses"></div>

  <!-- Timeline Events -->
  <div
    *ngFor="let event of events; trackBy: trackByEvent; let isLast = last"
    class="relative flex items-start"
    [class.pb-6]="!isLast && orientation === 'vertical'"
    [class.flex-shrink-0]="orientation === 'horizontal'"
  >
    <!-- Event Icon -->
    <div class="relative z-10 flex-shrink-0">
      <div [class]="getEventIconClasses(event)">
        <!-- Custom Icon or Default -->
        <svg *ngIf="showIcons" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <!-- Default event icon -->
          <path
            *ngIf="!event.icon || event.icon === 'default'"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
          ></path>
          <!-- Success icon -->
          <path
            *ngIf="event.icon === 'success' || event.type === 'success'"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M5 13l4 4L19 7"
          ></path>
          <!-- Warning icon -->
          <path
            *ngIf="event.icon === 'warning' || event.type === 'warning'"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
          ></path>
          <!-- Info icon -->
          <path
            *ngIf="event.icon === 'info' || event.type === 'info'"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          ></path>
        </svg>
      </div>
    </div>

    <!-- Event Content -->
    <div
      class="ml-4 flex-1 min-w-0"
      [class.cursor-pointer]="event.clickable && allowClick"
      (click)="onEventClick(event)"
    >
      <!-- Event Header -->
      <div class="flex items-start justify-between">
        <div class="flex-1 min-w-0">
          <h3 class="text-sm font-medium text-gray-900 truncate">{{ event.title }}</h3>

          <!-- Timestamp -->
          <p *ngIf="showTimestamps" class="text-xs text-gray-500 mt-1">
            {{ formatTimestamp(event.timestamp) }}
          </p>
        </div>

        <!-- Expand Button -->
        <button
          *ngIf="event.expandable && allowExpand"
          type="button"
          class="ml-2 p-1 text-gray-400 hover:text-gray-600 transition-colors"
          (click)="onEventExpand(event); $event.stopPropagation()"
          [attr.aria-label]="(event.expanded ? 'Collapse' : 'Expand') + ' event details'"
        >
          <svg class="w-4 h-4 transition-transform" [class.rotate-180]="event.expanded" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
      </div>

      <!-- Event Description -->
      <p *ngIf="showDescriptions && event.description"
         class="text-sm text-gray-600 mt-2"
         [class.line-clamp-2]="!event.expanded">
        {{ event.description }}
      </p>

      <!-- User Info -->
      <div *ngIf="showUsers && event.user"
           class="flex items-center mt-2 cursor-pointer"
           (click)="onUserClick(event); $event.stopPropagation()">
        <div class="flex-shrink-0 mr-2">
          <div
            *ngIf="!event.user.avatar"
            class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center text-xs font-medium text-gray-700"
          >
            {{ getAvatarInitials(event.user.name) }}
          </div>
          <img
            *ngIf="event.user.avatar"
            [src]="event.user.avatar"
            [alt]="event.user.name"
            class="w-6 h-6 rounded-full object-cover"
          />
        </div>
        <span class="text-xs text-gray-600">{{ event.user.name }}</span>
      </div>

      <!-- Expanded Metadata -->
      <div *ngIf="showMetadata && event.metadata && (event.expanded || !event.expandable)" class="mt-3 space-y-2">
        <!-- Status -->
        <div *ngIf="event.metadata.status" class="flex items-center">
          <span class="text-xs font-medium text-gray-500 mr-2">Status:</span>
          <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"
                [class.bg-green-100]="event.metadata.status === 'Completed'"
                [class.text-green-800]="event.metadata.status === 'Completed'"
                [class.bg-yellow-100]="event.metadata.status === 'In Progress' || event.metadata.status === 'Active'"
                [class.text-yellow-800]="event.metadata.status === 'In Progress' || event.metadata.status === 'Active'"
                [class.bg-gray-100]="event.metadata.status === 'Pending'"
                [class.text-gray-800]="event.metadata.status === 'Pending'">
            {{ event.metadata.status }}
          </span>
        </div>

        <!-- Duration -->
        <div *ngIf="event.metadata.duration" class="flex items-center">
          <span class="text-xs font-medium text-gray-500 mr-2">Duration:</span>
          <span class="text-xs text-gray-700">{{ event.metadata.duration }}</span>
        </div>

        <!-- Location -->
        <div *ngIf="event.metadata.location" class="flex items-center">
          <span class="text-xs font-medium text-gray-500 mr-2">Location:</span>
          <span class="text-xs text-gray-700">{{ event.metadata.location }}</span>
        </div>

        <!-- Tags -->
        <div *ngIf="event.metadata.tags && event.metadata.tags.length > 0" class="flex flex-wrap gap-1">
          <span
            *ngFor="let tag of event.metadata.tags"
            class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"
          >
            {{ tag }}
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="events.length === 0" class="text-center py-8">
    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
    </svg>
    <h3 class="text-sm font-medium text-gray-900 mb-1">No timeline events</h3>
    <p class="text-sm text-gray-500">No events to display in the timeline</p>
  </div>
</div>
