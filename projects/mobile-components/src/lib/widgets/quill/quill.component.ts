import { Component, Input, Output, EventEmitter, ViewChild, ElementRef, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'lib-quill',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './quill.component.html',
  styleUrl: './quill.component.css'
})
export class QuillComponent implements OnInit, OnDestroy {
  @ViewChild('editor', { static: true }) editorRef!: ElementRef;

  // Standard inputs for customization
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() content: string = '';
  @Input() placeholder: string = 'Start typing...';
  @Input() readonly: boolean = false;
  @Input() disabled: boolean = false;
  @Input() showToolbar: boolean = true;
  @Input() height: string = '200px';

  // Component state
  currentContent: string = '';
  isInitialized: boolean = false;
  wordCount: number = 0;
  characterCount: number = 0;

  // Event outputs
  @Output() contentChange = new EventEmitter<string>();
  @Output() textChange = new EventEmitter<{html: string, text: string}>();
  @Output() focus = new EventEmitter<void>();
  @Output() blur = new EventEmitter<void>();

  ngOnInit(): void {
    this.currentContent = this.content;
    this.isInitialized = true;
  }

  ngOnDestroy(): void {
    // Cleanup if needed
  }

  // Computed getter for CSS classes
  get computedClasses(): string {
    const baseClasses = 'quill-widget';
    
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };

    const variantClasses = {
      default: 'border-gray-300 focus-within:border-blue-500',
      primary: 'border-blue-300 focus-within:border-blue-500',
      secondary: 'border-gray-400 focus-within:border-gray-500',
      success: 'border-green-300 focus-within:border-green-500',
      warning: 'border-yellow-300 focus-within:border-yellow-500',
      danger: 'border-red-300 focus-within:border-red-500'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-xl'
    };

    const disabledClass = this.disabled ? 'opacity-50 cursor-not-allowed' : '';
    
    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      disabledClass,
      'border bg-white',
      this.className
    ].filter(Boolean).join(' ');
  }

  onContentChange(): void {
    this.contentChange.emit(this.currentContent);
    this.textChange.emit({
      html: this.currentContent,
      text: this.currentContent.replace(/<[^>]*>/g, '')
    });
  }

  onFocus(): void {
    this.focus.emit();
  }

  onBlur(): void {
    this.blur.emit();
  }

  formatText(format: string): void {
    if (this.readonly || this.disabled) return;
    document.execCommand(format, false);
  }
}
