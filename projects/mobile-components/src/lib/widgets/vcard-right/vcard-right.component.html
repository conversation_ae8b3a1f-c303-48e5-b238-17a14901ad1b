<div [class]="containerClasses" 
     role="region" 
     [attr.aria-label]="'Contact card for ' + vcard.name"
     (click)="onCardClick()">
  
  <!-- Avatar Section -->
  <div *ngIf="showAvatar && (avatarPosition === 'right' || layout === 'vertical')" 
       class="relative flex-shrink-0">
    <!-- Avatar Image or Initials -->
    <div class="relative">
      <img *ngIf="vcard.avatar" 
           [src]="vcard.avatar" 
           [alt]="vcard.name"
           [class]="avatarClasses">
      
      <!-- Initials Fallback -->
      <div *ngIf="!vcard.avatar" 
           [class]="avatarClasses"
           class="bg-gray-300 flex items-center justify-center text-gray-600 font-semibold">
        {{ vcard.initials || vcard.name.charAt(0).toUpperCase() }}
      </div>

      <!-- Status Indicator -->
      <div *ngIf="showStatus && vcard.status" 
           class="absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white"
           [class]="statusColor"
           [title]="vcard.status">
      </div>

      <!-- Verified Badge -->
      <div *ngIf="vcard.verified" 
           class="absolute -top-1 -right-1 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center"
           title="Verified account">
        <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
        </svg>
      </div>
    </div>
  </div>

  <!-- Content Section -->
  <div class="flex-1 min-w-0">
    <!-- Header Info -->
    <div class="mb-3">
      <div class="flex items-start justify-between">
        <div class="min-w-0 flex-1">
          <h3 class="text-lg font-semibold text-gray-900 truncate">
            {{ vcard.name }}
            <span *ngIf="vcard.verified" class="inline-flex items-center ml-1">
              <svg class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
              </svg>
            </span>
          </h3>
          
          <p *ngIf="vcard.title" class="text-sm font-medium text-gray-600 truncate">
            {{ vcard.title }}
          </p>
          
          <p *ngIf="vcard.company" class="text-sm text-gray-500 truncate">
            {{ vcard.company }}
            <span *ngIf="vcard.department"> · {{ vcard.department }}</span>
          </p>
          
          <p *ngIf="vcard.location" class="text-xs text-gray-400 mt-1 flex items-center">
            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
            </svg>
            {{ vcard.location }}
          </p>
        </div>

        <!-- Status Badge -->
        <div *ngIf="showStatus && vcard.status" 
             class="ml-2 px-2 py-1 text-xs font-medium rounded-full"
             [ngClass]="{
               'bg-green-100 text-green-800': vcard.status === 'online',
               'bg-gray-100 text-gray-800': vcard.status === 'offline',
               'bg-red-100 text-red-800': vcard.status === 'busy',
               'bg-yellow-100 text-yellow-800': vcard.status === 'away'
             }">
          {{ vcard.status | titlecase }}
        </div>
      </div>
    </div>

    <!-- Bio -->
    <div *ngIf="showBio && vcard.bio" class="mb-4">
      <p class="text-sm text-gray-600 leading-relaxed">{{ vcard.bio }}</p>
    </div>

    <!-- Contact Information -->
    <div *ngIf="showContacts && vcard.contacts.length > 0" class="mb-4">
      <h4 class="text-xs font-semibold text-gray-400 uppercase tracking-wide mb-2">Contact</h4>
      <div class="space-y-2">
        <div *ngFor="let contact of vcard.contacts" 
             class="flex items-center text-sm">
          <svg class="w-4 h-4 mr-2 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path *ngIf="contact.type === 'email'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
            <path *ngIf="contact.type === 'phone'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
            <path *ngIf="contact.type === 'website'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"/>
            <path *ngIf="contact.type === 'address'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
          </svg>
          
          <a *ngIf="contact.href" 
             [href]="contact.href"
             class="text-blue-600 hover:text-blue-800 hover:underline"
             (click)="onContactClick(contact); $event.stopPropagation()">
            {{ contact.value }}
          </a>
          
          <span *ngIf="!contact.href" class="text-gray-600">
            {{ contact.value }}
          </span>
        </div>
      </div>
    </div>

    <!-- Social Links -->
    <div *ngIf="showSocial && vcard.socialLinks && vcard.socialLinks.length > 0" class="mb-4">
      <h4 class="text-xs font-semibold text-gray-400 uppercase tracking-wide mb-2">Social</h4>
      <div class="flex flex-wrap gap-2">
        <a *ngFor="let social of vcard.socialLinks"
           [href]="social.url"
           target="_blank"
           rel="noopener noreferrer"
           class="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
           [title]="social.platform | titlecase"
           (click)="onSocialClick(social); $event.stopPropagation()">
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path [attr.d]="getSocialIcon(social.platform)"/>
          </svg>
        </a>
      </div>
    </div>

    <!-- Tags -->
    <div *ngIf="showTags && vcard.tags && vcard.tags.length > 0" class="mb-4">
      <h4 class="text-xs font-semibold text-gray-400 uppercase tracking-wide mb-2">Skills</h4>
      <div class="flex flex-wrap gap-1">
        <button *ngFor="let tag of vcard.tags"
                class="px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200 transition-colors duration-200"
                (click)="onTagClick(tag); $event.stopPropagation()">
          {{ tag }}
        </button>
      </div>
    </div>
  </div>

  <!-- Left Avatar (when position is left) -->
  <div *ngIf="showAvatar && avatarPosition === 'left' && layout === 'horizontal'" 
       class="relative flex-shrink-0 order-first">
    <!-- Avatar Image or Initials -->
    <div class="relative">
      <img *ngIf="vcard.avatar" 
           [src]="vcard.avatar" 
           [alt]="vcard.name"
           [class]="avatarClasses">
      
      <!-- Initials Fallback -->
      <div *ngIf="!vcard.avatar" 
           [class]="avatarClasses"
           class="bg-gray-300 flex items-center justify-center text-gray-600 font-semibold">
        {{ vcard.initials || vcard.name.charAt(0).toUpperCase() }}
      </div>

      <!-- Status Indicator -->
      <div *ngIf="showStatus && vcard.status" 
           class="absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white"
           [class]="statusColor"
           [title]="vcard.status">
      </div>

      <!-- Verified Badge -->
      <div *ngIf="vcard.verified" 
           class="absolute -top-1 -right-1 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center"
           title="Verified account">
        <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
        </svg>
      </div>
    </div>
  </div>
</div>
