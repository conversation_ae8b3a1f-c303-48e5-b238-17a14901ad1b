import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  ViewChild,
  ElementRef,
  signal,
  computed
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

export interface UploadFile {
  id: string;
  name: string;
  size: number;
  type: string;
  lastModified: number;
  file: File;
  status: 'pending' | 'uploading' | 'success' | 'error' | 'paused';
  progress: number;
  error?: string;
  url?: string;
  thumbnail?: string;
  uploadId?: string;
}

export interface UploadEvent {
  files: UploadFile[];
  action: 'add' | 'remove' | 'upload' | 'pause' | 'resume' | 'retry' | 'clear';
}

export interface UploadConfig {
  maxFiles: number;
  maxSize: number;
  maxTotalSize: number;
  acceptedTypes: string[];
  allowMultiple: boolean;
  autoUpload: boolean;
  chunkSize: number;
  retryAttempts: number;
  showPreview: boolean;
  showProgress: boolean;
  validateBeforeUpload: boolean;
}

@Component({
  selector: 'upload',
  templateUrl: './upload.component.html',
  styleUrls: ['./upload.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule
  ]
})
export class UploadComponent implements OnInit {
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  
  // Component-specific properties
  @Input() uploadId: string = 'upload-' + Math.random().toString(36).substring(7);
  @Input() label: string = 'Upload Files';
  @Input() description: string = 'Choose files to upload';
  @Input() acceptedTypes: string[] = ['image/*', 'application/pdf', '.doc', '.docx'];
  @Input() maxFiles: number = 10;
  @Input() maxSize: number = 10 * 1024 * 1024; // 10MB
  @Input() maxTotalSize: number = 100 * 1024 * 1024; // 100MB
  @Input() allowMultiple: boolean = true;
  @Input() autoUpload: boolean = false;
  @Input() showPreview: boolean = true;
  @Input() showProgress: boolean = true;
  @Input() showFileList: boolean = true;
  @Input() enableDragDrop: boolean = true;
  @Input() enablePasteUpload: boolean = true;
  @Input() validateBeforeUpload: boolean = true;
  @Input() retryAttempts: number = 3;
  @Input() chunkSize: number = 1024 * 1024; // 1MB chunks
  @Input() uploadUrl: string = '';
  @Input() headers: { [key: string]: string } = {};

  // UI Text
  @Input() dropText: string = 'Drop files here to upload';
  @Input() browseText: string = 'Browse Files';
  @Input() pasteText: string = 'or paste images';
  @Input() uploadButtonText: string = 'Upload All';
  @Input() pauseButtonText: string = 'Pause';
  @Input() resumeButtonText: string = 'Resume';
  @Input() retryButtonText: string = 'Retry';
  @Input() removeButtonText: string = 'Remove';
  @Input() clearAllButtonText: string = 'Clear All';
  @Input() cancelButtonText: string = 'Cancel';

  // Events
  @Output() fileAdd = new EventEmitter<UploadEvent>();
  @Output() fileRemove = new EventEmitter<UploadEvent>();
  @Output() uploadStart = new EventEmitter<UploadEvent>();
  @Output() uploadProgress = new EventEmitter<{ file: UploadFile; progress: number }>();
  @Output() uploadComplete = new EventEmitter<UploadFile>();
  @Output() uploadError = new EventEmitter<{ file: UploadFile; error: string }>();
  @Output() uploadCancel = new EventEmitter<UploadFile>();
  @Output() allUploadsComplete = new EventEmitter<UploadFile[]>();
  @Output() validationError = new EventEmitter<{ file: File; errors: string[] }>();

  @ViewChild('fileInput', { static: false }) fileInput!: ElementRef<HTMLInputElement>;
  @ViewChild('dropzone', { static: false }) dropzone!: ElementRef<HTMLDivElement>;

  // State
  files = signal<UploadFile[]>([]);
  isDragOver = signal(false);
  isUploading = signal(false);
  isPaused = signal(false);
  dragCounter = 0;

  get config(): UploadConfig {
    return {
      maxFiles: this.maxFiles,
      maxSize: this.maxSize,
      maxTotalSize: this.maxTotalSize,
      acceptedTypes: this.acceptedTypes,
      allowMultiple: this.allowMultiple,
      autoUpload: this.autoUpload,
      chunkSize: this.chunkSize,
      retryAttempts: this.retryAttempts,
      showPreview: this.showPreview,
      showProgress: this.showProgress,
      validateBeforeUpload: this.validateBeforeUpload
    };
  }

  get containerClasses(): string {
    const sizeClasses = {
      xs: 'p-2 text-xs',
      sm: 'p-3 text-sm',
      md: 'p-4 text-base',
      lg: 'p-5 text-lg',
      xl: 'p-6 text-xl'
    };

    const variantClasses = {
      default: 'bg-white border border-gray-300',
      primary: 'bg-blue-50 border border-blue-300',
      secondary: 'bg-gray-50 border border-gray-300',
      success: 'bg-green-50 border border-green-300',
      warning: 'bg-yellow-50 border border-yellow-300',
      danger: 'bg-red-50 border border-red-300'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-xl'
    };

    return `
      ${sizeClasses[this.size]}
      ${variantClasses[this.variant]}
      ${roundedClasses[this.rounded]}
      ${this.className}
    `.trim().replace(/\s+/g, ' ');
  }

  get dropzoneClasses(): string {
    const baseClasses = 'border-2 border-dashed transition-all duration-200 cursor-pointer';
    const dragOverClasses = this.isDragOver() 
      ? 'border-blue-500 bg-blue-50' 
      : 'border-gray-300 hover:border-gray-400';

    return `${baseClasses} ${dragOverClasses}`;
  }

  get totalFiles(): number {
    return this.files().length;
  }

  get totalSize(): string {
    const bytes = this.files().reduce((sum, file) => sum + file.size, 0);
    return this.formatFileSize(bytes);
  }

  get pendingFiles(): UploadFile[] {
    return this.files().filter(file => file.status === 'pending');
  }

  get uploadingFiles(): UploadFile[] {
    return this.files().filter(file => file.status === 'uploading');
  }

  get successFiles(): UploadFile[] {
    return this.files().filter(file => file.status === 'success');
  }

  get errorFiles(): UploadFile[] {
    return this.files().filter(file => file.status === 'error');
  }

  get overallProgress(): number {
    const files = this.files();
    if (files.length === 0) return 0;
    const totalProgress = files.reduce((sum, file) => sum + file.progress, 0);
    return Math.round(totalProgress / files.length);
  }

  get canUpload(): boolean {
    return this.pendingFiles.length > 0 && !this.isUploading();
  }

  get canPause(): boolean {
    return this.isUploading() && !this.isPaused();
  }

  get canResume(): boolean {
    return this.isPaused() && this.uploadingFiles.length > 0;
  }

  ngOnInit(): void {
    this.files.set([]);
    
    if (this.enablePasteUpload) {
      this.setupPasteUpload();
    }
  }

  setupPasteUpload(): void {
    document.addEventListener('paste', (event) => {
      const items = event.clipboardData?.items;
      if (!items) return;

      const files: File[] = [];
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        if (item.type.indexOf('image') !== -1) {
          const file = item.getAsFile();
          if (file) {
            files.push(file);
          }
        }
      }

      if (files.length > 0) {
        this.addFiles(files);
      }
    });
  }

  onDragOver(event: DragEvent): void {
    if (!this.enableDragDrop) return;
    event.preventDefault();
    event.stopPropagation();
  }

  onDragEnter(event: DragEvent): void {
    if (!this.enableDragDrop) return;
    event.preventDefault();
    event.stopPropagation();
    this.dragCounter++;
    this.isDragOver.set(true);
  }

  onDragLeave(event: DragEvent): void {
    if (!this.enableDragDrop) return;
    event.preventDefault();
    event.stopPropagation();
    this.dragCounter--;
    if (this.dragCounter === 0) {
      this.isDragOver.set(false);
    }
  }

  onDrop(event: DragEvent): void {
    if (!this.enableDragDrop) return;
    event.preventDefault();
    event.stopPropagation();
    this.dragCounter = 0;
    this.isDragOver.set(false);

    const droppedFiles = Array.from(event.dataTransfer?.files || []);
    this.addFiles(droppedFiles);
  }

  onFileSelect(event: Event): void {
    const input = event.target as HTMLInputElement;
    const selectedFiles = Array.from(input.files || []);
    this.addFiles(selectedFiles);
    
    // Reset input
    input.value = '';
  }

  addFiles(fileList: File[]): void {
    const currentFiles = this.files();
    const newFiles: UploadFile[] = [];
    const errors: { file: File; errors: string[] }[] = [];

    for (const file of fileList) {
      // Validate file
      const validationErrors = this.validateFile(file, currentFiles.length + newFiles.length);
      
      if (validationErrors.length > 0) {
        errors.push({ file, errors: validationErrors });
        continue;
      }

      const uploadFile: UploadFile = {
        id: this.generateFileId(),
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified,
        file: file,
        status: 'pending',
        progress: 0
      };

      // Generate thumbnail for images
      if (this.showPreview && file.type.startsWith('image/')) {
        this.generateThumbnail(uploadFile);
      }

      newFiles.push(uploadFile);
    }

    // Emit validation errors
    errors.forEach(error => {
      this.validationError.emit(error);
    });

    if (newFiles.length > 0) {
      const updatedFiles = this.allowMultiple ? [...currentFiles, ...newFiles] : newFiles;
      this.files.set(updatedFiles);

      this.fileAdd.emit({
        files: newFiles,
        action: 'add'
      });

      if (this.autoUpload) {
        this.uploadFiles();
      }
    }
  }

  validateFile(file: File, currentFileCount: number): string[] {
    const errors: string[] = [];

    // Check file count
    if (currentFileCount >= this.maxFiles) {
      errors.push(`Maximum ${this.maxFiles} files allowed`);
    }

    // Check file size
    if (file.size > this.maxSize) {
      errors.push(`File size exceeds ${this.formatFileSize(this.maxSize)} limit`);
    }

    // Check total size
    const currentTotalSize = this.files().reduce((sum, f) => sum + f.size, 0);
    if (currentTotalSize + file.size > this.maxTotalSize) {
      errors.push(`Total size exceeds ${this.formatFileSize(this.maxTotalSize)} limit`);
    }

    // Check file type
    if (this.acceptedTypes.length > 0 && !this.isFileTypeAccepted(file)) {
      errors.push(`File type not supported. Accepted: ${this.acceptedTypes.join(', ')}`);
    }

    return errors;
  }

  removeFile(fileToRemove: UploadFile): void {
    const currentFiles = this.files();
    const updatedFiles = currentFiles.filter(file => file.id !== fileToRemove.id);
    this.files.set(updatedFiles);

    this.fileRemove.emit({
      files: [fileToRemove],
      action: 'remove'
    });
  }

  clearFiles(): void {
    this.files.set([]);
    this.fileRemove.emit({
      files: [],
      action: 'clear'
    });
  }

  uploadFiles(): void {
    const pendingFiles = this.pendingFiles;
    if (pendingFiles.length === 0) return;

    this.isUploading.set(true);
    this.isPaused.set(false);

    this.uploadStart.emit({
      files: pendingFiles,
      action: 'upload'
    });

    pendingFiles.forEach(file => {
      this.uploadFile(file);
    });
  }

  pauseUpload(): void {
    this.isPaused.set(true);
    // In a real implementation, you would pause ongoing uploads
  }

  resumeUpload(): void {
    this.isPaused.set(false);
    // In a real implementation, you would resume paused uploads
  }

  retryUpload(file: UploadFile): void {
    file.status = 'pending';
    file.progress = 0;
    file.error = undefined;
    this.files.set([...this.files()]);
    this.uploadFile(file);
  }

  openFileBrowser(): void {
    this.fileInput?.nativeElement?.click();
  }

  private uploadFile(file: UploadFile): void {
    file.status = 'uploading';
    file.progress = 0;

    // Simulate upload progress
    let progress = 0;
    const interval = setInterval(() => {
      if (this.isPaused()) {
        file.status = 'paused';
        clearInterval(interval);
        return;
      }

      progress += Math.random() * 15;
      if (progress >= 100) {
        progress = 100;
        file.status = 'success';
        file.progress = 100;
        clearInterval(interval);
        
        this.uploadComplete.emit(file);
        
        // Check if all uploads are complete
        const stillUploading = this.files().some(f => 
          f.status === 'uploading' || f.status === 'pending'
        );
        
        if (!stillUploading) {
          this.isUploading.set(false);
          this.allUploadsComplete.emit(this.successFiles);
        }
      } else {
        file.progress = Math.round(progress);
        this.uploadProgress.emit({ file, progress: file.progress });
      }

      // Trigger change detection
      this.files.set([...this.files()]);
    }, 300);
  }

  private isFileTypeAccepted(file: File): boolean {
    return this.acceptedTypes.some(type => {
      if (type.startsWith('.')) {
        return file.name.toLowerCase().endsWith(type.toLowerCase());
      }
      if (type.includes('*')) {
        const baseType = type.split('/')[0];
        return file.type.startsWith(baseType);
      }
      return file.type === type;
    });
  }

  private generateFileId(): string {
    return 'file-' + Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  private generateThumbnail(file: UploadFile): void {
    const reader = new FileReader();
    reader.onload = (e) => {
      file.thumbnail = e.target?.result as string;
      this.files.set([...this.files()]);
    };
    reader.readAsDataURL(file.file);
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getFileIcon(file: UploadFile): string {
    if (file.type.startsWith('image/')) return '🖼️';
    if (file.type.includes('pdf')) return '📄';
    if (file.type.includes('word') || file.name.endsWith('.doc') || file.name.endsWith('.docx')) return '📝';
    if (file.type.includes('excel') || file.name.endsWith('.xls') || file.name.endsWith('.xlsx')) return '📊';
    if (file.type.includes('powerpoint') || file.name.endsWith('.ppt') || file.name.endsWith('.pptx')) return '📽️';
    if (file.type.startsWith('video/')) return '🎥';
    if (file.type.startsWith('audio/')) return '🎵';
    return '📎';
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'pending': return '⏳';
      case 'uploading': return '📤';
      case 'success': return '✅';
      case 'error': return '❌';
      case 'paused': return '⏸️';
      default: return '📎';
    }
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'pending': return 'text-yellow-600';
      case 'uploading': return 'text-blue-600';
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'paused': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  }

  trackByFileId(index: number, file: UploadFile): string {
    return file.id;
  }
}
