<!-- Upload component container -->
<div [ngClass]="containerClasses">
  
  <!-- Header -->
  <div class="mb-4">
    <h3 class="text-lg font-semibold text-gray-900">{{ label }}</h3>
    <p class="text-sm text-gray-600 mt-1">{{ description }}</p>
  </div>

  <!-- Upload area -->
  <div 
    #dropzone
    [ngClass]="dropzoneClasses"
    class="relative p-6 text-center"
    (dragover)="onDragOver($event)"
    (dragenter)="onDragEnter($event)"
    (dragleave)="onDragLeave($event)"
    (drop)="onDrop($event)"
    (click)="openFileBrowser()">
    
    <!-- Upload icon -->
    <div class="mx-auto w-12 h-12 text-gray-400 mb-4">
      <svg class="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
      </svg>
    </div>
    
    <!-- Upload text -->
    <div class="space-y-2">
      <p class="text-lg text-gray-700 font-medium">{{ dropText }}</p>
      <p class="text-sm text-gray-500">
        <span class="font-medium text-blue-600 cursor-pointer hover:text-blue-500">{{ browseText }}</span>
        <span *ngIf="enablePasteUpload"> {{ pasteText }}</span>
      </p>
    </div>
    
    <!-- File type info -->
    <div class="mt-4 text-xs text-gray-500 space-y-1">
      <p>Maximum {{ maxFiles }} files, {{ formatFileSize(maxSize) }} each</p>
      <p>Total limit: {{ formatFileSize(maxTotalSize) }}</p>
      <div *ngIf="acceptedTypes.length > 0" class="mt-2">
        <span class="font-medium">Accepted:</span> {{ acceptedTypes.join(', ') }}
      </div>
    </div>
  </div>

  <!-- File list -->
  <div *ngIf="showFileList && totalFiles > 0" class="mt-6">
    
    <!-- Summary bar -->
    <div class="flex items-center justify-between bg-gray-50 rounded-lg p-4 mb-4">
      <div class="flex items-center space-x-4">
        <div class="text-sm">
          <span class="font-medium text-gray-900">{{ totalFiles }}</span>
          <span class="text-gray-600">{{ totalFiles === 1 ? 'file' : 'files' }}</span>
        </div>
        <div class="text-sm text-gray-600">
          Total: {{ totalSize }}
        </div>
        <div *ngIf="successFiles.length > 0" class="text-sm text-green-600">
          {{ successFiles.length }} uploaded
        </div>
        <div *ngIf="errorFiles.length > 0" class="text-sm text-red-600">
          {{ errorFiles.length }} failed
        </div>
      </div>
      
      <div class="flex items-center space-x-2">
        <!-- Control buttons -->
        <button 
          *ngIf="canUpload"
          (click)="uploadFiles()"
          class="px-3 py-1 text-sm font-medium text-white bg-blue-600 rounded hover:bg-blue-700 transition-colors">
          {{ uploadButtonText }}
        </button>
        
        <button 
          *ngIf="canPause"
          (click)="pauseUpload()"
          class="px-3 py-1 text-sm font-medium text-gray-700 bg-gray-200 rounded hover:bg-gray-300 transition-colors">
          {{ pauseButtonText }}
        </button>
        
        <button 
          *ngIf="canResume"
          (click)="resumeUpload()"
          class="px-3 py-1 text-sm font-medium text-white bg-green-600 rounded hover:bg-green-700 transition-colors">
          {{ resumeButtonText }}
        </button>
        
        <button 
          (click)="clearFiles()"
          class="px-3 py-1 text-sm font-medium text-gray-700 bg-gray-200 rounded hover:bg-gray-300 transition-colors">
          {{ clearAllButtonText }}
        </button>
      </div>
    </div>

    <!-- Overall progress -->
    <div *ngIf="isUploading() && showProgress" class="mb-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div class="flex items-center justify-between mb-2">
        <span class="text-sm font-medium text-blue-900">Overall Progress</span>
        <span class="text-sm text-blue-700">{{ overallProgress }}%</span>
      </div>
      <div class="w-full bg-blue-200 rounded-full h-2">
        <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
             [style.width.%]="overallProgress"></div>
      </div>
    </div>

    <!-- Files grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <div *ngFor="let file of files(); trackBy: trackByFileId"
           class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
        
        <!-- File header -->
        <div class="flex items-start justify-between mb-3">
          <div class="flex items-center space-x-3 flex-1 min-w-0">
            <!-- File preview/icon -->
            <div class="flex-shrink-0">
              <div *ngIf="file.thumbnail && showPreview" class="w-12 h-12 rounded-lg overflow-hidden bg-gray-100">
                <img [src]="file.thumbnail" [alt]="file.name" class="w-full h-full object-cover">
              </div>
              <div *ngIf="!file.thumbnail || !showPreview" 
                   class="w-12 h-12 flex items-center justify-center bg-gray-100 rounded-lg text-2xl">
                {{ getFileIcon(file) }}
              </div>
            </div>
            
            <!-- File info -->
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate" [title]="file.name">
                {{ file.name }}
              </p>
              <p class="text-xs text-gray-500">
                {{ formatFileSize(file.size) }}
              </p>
              <p class="text-xs mt-1" [ngClass]="getStatusColor(file.status)">
                {{ file.status | titlecase }}
              </p>
            </div>
          </div>
          
          <!-- Status and actions -->
          <div class="flex items-center space-x-2 ml-2">
            <!-- Status icon -->
            <span class="text-lg" [title]="file.status">
              {{ getStatusIcon(file.status) }}
            </span>
            
            <!-- Action buttons -->
            <div class="flex items-center space-x-1">
              <!-- Retry button -->
              <button 
                *ngIf="file.status === 'error'"
                (click)="retryUpload(file)"
                class="p-1 text-blue-500 hover:text-blue-700 rounded transition-colors"
                [title]="retryButtonText">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
              </button>
              
              <!-- Remove button -->
              <button 
                (click)="removeFile(file)"
                class="p-1 text-gray-400 hover:text-red-500 rounded transition-colors"
                [title]="removeButtonText">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
        
        <!-- Progress bar -->
        <div *ngIf="(file.status === 'uploading' || file.status === 'paused') && showProgress" class="mb-2">
          <div class="flex justify-between text-xs text-gray-600 mb-1">
            <span>{{ file.status === 'paused' ? 'Paused' : 'Uploading' }}...</span>
            <span>{{ file.progress }}%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-1.5">
            <div class="bg-blue-600 h-1.5 rounded-full transition-all duration-300" 
                 [style.width.%]="file.progress"
                 [class.bg-gray-400]="file.status === 'paused'"></div>
          </div>
        </div>
        
        <!-- Success indicator -->
        <div *ngIf="file.status === 'success'" class="flex items-center text-green-600 text-xs">
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          Upload complete
        </div>
        
        <!-- Error message -->
        <div *ngIf="file.status === 'error' && file.error" 
             class="text-xs text-red-600 bg-red-50 p-2 rounded mt-2">
          {{ file.error }}
        </div>
      </div>
    </div>
  </div>

  <!-- Hidden file input -->
  <input 
    #fileInput
    type="file"
    class="hidden"
    [multiple]="allowMultiple"
    [accept]="acceptedTypes.join(',')"
    (change)="onFileSelect($event)">
</div>
