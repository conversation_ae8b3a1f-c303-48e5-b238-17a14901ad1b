<div [class]="computedClasses">
  <!-- Account Balance Header -->
  <div class="flex items-start justify-between mb-3">
    <div class="flex-1">
      <p [class]="labelClasses">{{ label }}</p>
      <h3 [class]="balanceClasses">{{ balance }}</h3>
      <span *ngIf="showCurrency && currency" class="text-xs text-gray-500 uppercase">{{ currency }}</span>
    </div>
    
    <!-- Avatar Section -->
    <div *ngIf="showAvatar" class="ml-4">
      <div class="w-12 h-12 rounded-full bg-gray-200 overflow-hidden">
        <img 
          *ngIf="avatarSrc" 
          [src]="avatarSrc" 
          [alt]="accountName"
          class="w-full h-full object-cover"
        >
        <div 
          *ngIf="!avatarSrc" 
          class="w-full h-full flex items-center justify-center text-gray-400 text-lg font-semibold"
        >
          {{ accountName.charAt(0).toUpperCase() }}
        </div>
      </div>
    </div>
  </div>

  <!-- Account Details -->
  <div *ngIf="type === 'summary' || type === 'detailed'" class="space-y-2">
    <div class="flex justify-between items-center">
      <span class="text-sm text-gray-600">Account Holder</span>
      <span class="text-sm font-medium">{{ accountName }}</span>
    </div>
    
    <div *ngIf="showMembership" class="flex justify-between items-center">
      <span class="text-sm text-gray-600">Membership</span>
      <span class="text-sm font-medium">{{ membership }}</span>
    </div>
    
    <div *ngIf="showPhone && type === 'detailed'" class="flex justify-between items-center">
      <span class="text-sm text-gray-600">Phone</span>
      <span class="text-sm font-medium">{{ phone }}</span>
    </div>
  </div>
</div>