import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'lib-account-balance',
  templateUrl: './account-balance.component.html',
  styleUrls: ['./account-balance.component.css'],
  standalone: true,
  imports: [CommonModule]
})
export class AccountBalanceComponent {
  @Input() balance: string = '$1,234.56';
  @Input() label: string = 'Account Balance';
  @Input() currency: string = 'USD';
  @Input() showCurrency: boolean = true;
  @Input() accountName: string = '<PERSON>';
  @Input() membership: string = 'Premium';
  @Input() phone: string = '+****************';
  @Input() avatarSrc: string = '';
  @Input() showAvatar: boolean = true;
  @Input() showMembership: boolean = true;
  @Input() showPhone: boolean = false;
  @Input() type: 'balance' | 'summary' | 'detailed' = 'balance';
  
  // Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() bgColor: string = '';

  get computedClasses(): string {
    const baseClasses = [
      'account-balance-widget',
      'p-4',
      'border',
      'bg-white',
      'shadow-sm'
    ];

    // Size classes
    const sizeClasses = {
      xs: ['text-xs', 'p-2'],
      sm: ['text-sm', 'p-3'],
      md: ['text-base', 'p-4'],
      lg: ['text-lg', 'p-6'],
      xl: ['text-xl', 'p-8']
    };

    // Variant classes
    const variantClasses = {
      default: ['border-gray-200', 'text-gray-900'],
      primary: ['border-blue-200', 'bg-blue-50', 'text-blue-900'],
      secondary: ['border-gray-300', 'bg-gray-50', 'text-gray-700'],
      success: ['border-green-200', 'bg-green-50', 'text-green-900'],
      warning: ['border-yellow-200', 'bg-yellow-50', 'text-yellow-900'],
      danger: ['border-red-200', 'bg-red-50', 'text-red-900']
    };

    // Rounded classes
    const roundedClasses = {
      none: [],
      sm: ['rounded-sm'],
      md: ['rounded-md'],
      lg: ['rounded-lg'],
      full: ['rounded-full']
    };

    const classes = [
      ...baseClasses,
      ...sizeClasses[this.size],
      ...variantClasses[this.variant],
      ...roundedClasses[this.rounded]
    ];

    if (this.bgColor) {
      classes.push(`bg-${this.bgColor}`);
    }

    if (this.className) {
      classes.push(this.className);
    }

    return classes.join(' ');
  }

  get balanceClasses(): string {
    const sizeClasses = {
      xs: 'text-lg font-semibold',
      sm: 'text-xl font-semibold',
      md: 'text-2xl font-bold',
      lg: 'text-3xl font-bold',
      xl: 'text-4xl font-bold'
    };
    return sizeClasses[this.size];
  }

  get labelClasses(): string {
    const sizeClasses = {
      xs: 'text-xs text-gray-600',
      sm: 'text-sm text-gray-600',
      md: 'text-sm text-gray-700',
      lg: 'text-base text-gray-700',
      xl: 'text-lg text-gray-700'
    };
    return sizeClasses[this.size];
  }
}