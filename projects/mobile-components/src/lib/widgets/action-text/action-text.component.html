<div [class]="containerClasses">
  <!-- Button Action Type -->
  <button 
    *ngIf="actionType === 'button'"
    [class]="computedClasses"
    [disabled]="disabled"
    (click)="onClick($event)"
    (keydown)="onKeyDown($event)"
    type="button"
  >
    <span *ngIf="icon && iconPosition === 'left'" [class]="icon + ' mr-2'"></span>
    <span>{{ text }}</span>
    <span *ngIf="icon && iconPosition === 'right'" [class]="icon + ' ml-2'"></span>
  </button>

  <!-- Link Action Type -->
  <a 
    *ngIf="actionType === 'link' && href"
    [class]="computedClasses"
    [href]="disabled ? null : href"
    [target]="target"
    [attr.aria-disabled]="disabled"
    (click)="onClick($event)"
    (keydown)="onKeyDown($event)"
  >
    <span *ngIf="icon && iconPosition === 'left'" [class]="icon + ' mr-2'"></span>
    <span>{{ text }}</span>
    <span *ngIf="icon && iconPosition === 'right'" [class]="icon + ' ml-2'"></span>
  </a>

  <!-- Text Action Type (span with click handler) -->
  <span 
    *ngIf="actionType === 'text' || (actionType === 'link' && !href)"
    [class]="computedClasses"
    [attr.tabindex]="disabled ? -1 : 0"
    [attr.aria-disabled]="disabled"
    role="button"
    (click)="onClick($event)"
    (keydown)="onKeyDown($event)"
  >
    <span *ngIf="icon && iconPosition === 'left'" [class]="icon + ' mr-2'"></span>
    <span>{{ text }}</span>
    <span *ngIf="icon && iconPosition === 'right'" [class]="icon + ' ml-2'"></span>
  </span>

  <!-- Subtext -->
  <div *ngIf="showSubtext && subtext" [class]="subtextClasses">
    {{ subtext }}
  </div>
</div>