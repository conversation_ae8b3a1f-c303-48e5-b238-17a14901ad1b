import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'lib-action-text',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './action-text.component.html',
  styleUrl: './action-text.component.css'
})
export class ActionTextComponent {
  @Input() text: string = 'Take Action Now';
  @Input() subtext: string = 'Click here to continue';
  @Input() actionType: 'button' | 'link' | 'text' = 'button';
  @Input() href: string = '';
  @Input() target: '_self' | '_blank' | '_parent' | '_top' = '_self';
  @Input() disabled: boolean = false;
  @Input() showSubtext: boolean = true;
  @Input() icon: string = '';
  @Input() iconPosition: 'left' | 'right' = 'left';
  
  // Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'primary';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() textAlign: 'left' | 'center' | 'right' = 'center';

  @Output() actionClick = new EventEmitter<Event>();

  get computedClasses(): string {
    const baseClasses = ['action-text-widget', 'inline-flex', 'items-center', 'transition-all', 'duration-200'];

    // Size classes
    const sizeClasses = {
      xs: ['text-xs', 'py-1', 'px-2'],
      sm: ['text-sm', 'py-2', 'px-3'],
      md: ['text-base', 'py-2', 'px-4'],
      lg: ['text-lg', 'py-3', 'px-6'],
      xl: ['text-xl', 'py-4', 'px-8']
    };

    // Variant classes for different action types
    const variantClasses = {
      default: {
        button: ['bg-gray-100', 'text-gray-900', 'border', 'border-gray-300', 'hover:bg-gray-200'],
        link: ['text-gray-700', 'hover:text-gray-900', 'underline'],
        text: ['text-gray-700', 'hover:text-gray-900']
      },
      primary: {
        button: ['bg-blue-600', 'text-white', 'hover:bg-blue-700', 'focus:ring-blue-500'],
        link: ['text-blue-600', 'hover:text-blue-800', 'underline'],
        text: ['text-blue-600', 'hover:text-blue-800']
      },
      secondary: {
        button: ['bg-gray-600', 'text-white', 'hover:bg-gray-700', 'focus:ring-gray-500'],
        link: ['text-gray-600', 'hover:text-gray-800', 'underline'],
        text: ['text-gray-600', 'hover:text-gray-800']
      },
      success: {
        button: ['bg-green-600', 'text-white', 'hover:bg-green-700', 'focus:ring-green-500'],
        link: ['text-green-600', 'hover:text-green-800', 'underline'],
        text: ['text-green-600', 'hover:text-green-800']
      },
      warning: {
        button: ['bg-yellow-600', 'text-white', 'hover:bg-yellow-700', 'focus:ring-yellow-500'],
        link: ['text-yellow-600', 'hover:text-yellow-800', 'underline'],
        text: ['text-yellow-600', 'hover:text-yellow-800']
      },
      danger: {
        button: ['bg-red-600', 'text-white', 'hover:bg-red-700', 'focus:ring-red-500'],
        link: ['text-red-600', 'hover:text-red-800', 'underline'],
        text: ['text-red-600', 'hover:text-red-800']
      }
    };

    // Rounded classes
    const roundedClasses = {
      none: [],
      sm: ['rounded-sm'],
      md: ['rounded-md'],
      lg: ['rounded-lg'],
      full: ['rounded-full']
    };

    // Text alignment
    const alignClasses = {
      left: ['text-left'],
      center: ['text-center'],
      right: ['text-right']
    };

    const classes = [
      ...baseClasses,
      ...sizeClasses[this.size],
      ...variantClasses[this.variant][this.actionType],
      ...alignClasses[this.textAlign]
    ];

    // Add rounded classes only for button type
    if (this.actionType === 'button') {
      classes.push(...roundedClasses[this.rounded]);
      classes.push('focus:outline-none', 'focus:ring-2', 'focus:ring-offset-2');
    }

    // Disabled state
    if (this.disabled) {
      classes.push('opacity-50', 'cursor-not-allowed', 'pointer-events-none');
    } else {
      classes.push('cursor-pointer');
    }

    if (this.className) {
      classes.push(this.className);
    }

    return classes.join(' ');
  }

  get containerClasses(): string {
    const alignClasses = {
      left: 'text-left',
      center: 'text-center',
      right: 'text-right'
    };
    return `action-text-container ${alignClasses[this.textAlign]}`;
  }

  get subtextClasses(): string {
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-xs',
      md: 'text-sm',
      lg: 'text-base',
      xl: 'text-lg'
    };
    return `${sizeClasses[this.size]} text-gray-600 mt-1`;
  }

  onClick(event: Event): void {
    if (!this.disabled) {
      this.actionClick.emit(event);
    }
  }

  onKeyDown(event: KeyboardEvent): void {
    if ((event.key === 'Enter' || event.key === ' ') && !this.disabled) {
      event.preventDefault();
      this.actionClick.emit(event);
    }
  }
}