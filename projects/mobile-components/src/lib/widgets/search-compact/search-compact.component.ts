import { Component, Input, Output, EventEmitter, OnInit, Inject, Optional, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'lib-search-compact',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './search-compact.component.html',
  styleUrl: './search-compact.component.css',
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class SearchCompactComponent implements OnInit {
  /**
   * Search input value.
   */
  @Input() value: string = '';

  /**
   * Placeholder text for the search input.
   */
  @Input() placeholder: string = 'Search...';

  /**
   * Whether the search input is disabled.
   */
  @Input() disabled: boolean = false;

  /**
   * Whether the search input is readonly.
   */
  @Input() readonly: boolean = false;

  /**
   * Custom CSS classes for the container.
   */
  @Input() className: string = 'relative flex items-center bg-white border border-gray-300 rounded-lg shadow-sm focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500';

  /**
   * Custom CSS classes for additional styling.
   */
  @Input() class?: string;

  /**
   * Custom inline styles.
   */
  @Input() style?: string;

  /**
   * Size of the search input.
   */
  @Input() size: 'sm' | 'md' | 'lg' = 'md';

  /**
   * Whether to show the search icon.
   */
  @Input() showIcon: boolean = true;

  /**
   * Search icon class.
   */
  @Input() iconClass: string = 'w-4 h-4 text-gray-400';

  /**
   * Search icon position.
   */
  @Input() iconPosition: 'left' | 'right' = 'left';

  /**
   * Whether to show the clear button when there's text.
   */
  @Input() clearable: boolean = true;

  /**
   * Clear button icon class.
   */
  @Input() clearIconClass: string = 'w-4 h-4 text-gray-400 hover:text-gray-600 cursor-pointer';

  /**
   * Input field classes.
   */
  @Input() inputClass: string = 'w-full border-0 bg-transparent focus:ring-0 focus:outline-none';

  /**
   * Maximum length of the search input.
   */
  @Input() maxLength?: number;

  /**
   * Minimum length before triggering search.
   */
  @Input() minLength: number = 0;

  /**
   * Debounce delay in milliseconds.
   */
  @Input() debounceTime: number = 300;

  /**
   * Whether to trim whitespace from search value.
   */
  @Input() trim: boolean = true;

  /**
   * Whether to convert search to lowercase.
   */
  @Input() lowercase: boolean = false;

  /**
   * Loading state indicator.
   */
  @Input() loading: boolean = false;

  /**
   * Loading spinner classes.
   */
  @Input() loadingClass: string = 'w-4 h-4 text-blue-500 animate-spin';

  /**
   * Event emitted when search value changes.
   */
  @Output() valueChange = new EventEmitter<string>();

  /**
   * Event emitted when search is performed.
   */
  @Output() search = new EventEmitter<string>();

  /**
   * Event emitted when input is focused.
   */
  @Output() focus = new EventEmitter<Event>();

  /**
   * Event emitted when input loses focus.
   */
  @Output() blur = new EventEmitter<Event>();

  /**
   * Event emitted when clear button is clicked.
   */
  @Output() clear = new EventEmitter<void>();

  /**
   * Event emitted when Enter key is pressed.
   */
  @Output() enter = new EventEmitter<string>();

  private debounceTimer?: ReturnType<typeof setTimeout>;

  constructor(@Optional() @Inject('componentProperties') public properties: any) {}

  ngOnInit(): void {
    if (this.properties) {
      // Map properties from injected properties
      this.value = this.properties.value || this.value;
      this.placeholder = this.properties.placeholder || this.placeholder;
      this.disabled = this.properties.disabled !== undefined ? this.properties.disabled : this.disabled;
      this.readonly = this.properties.readonly !== undefined ? this.properties.readonly : this.readonly;
      this.className = this.properties.className || this.className;
      this.class = this.properties.class || this.class;
      this.style = this.properties.style || this.style;
      this.size = this.properties.size || this.size;
      this.showIcon = this.properties.showIcon !== undefined ? this.properties.showIcon : this.showIcon;
      this.iconClass = this.properties.iconClass || this.iconClass;
      this.iconPosition = this.properties.iconPosition || this.iconPosition;
      this.clearable = this.properties.clearable !== undefined ? this.properties.clearable : this.clearable;
      this.clearIconClass = this.properties.clearIconClass || this.clearIconClass;
      this.inputClass = this.properties.inputClass || this.inputClass;
      this.maxLength = this.properties.maxLength || this.maxLength;
      this.minLength = this.properties.minLength !== undefined ? this.properties.minLength : this.minLength;
      this.debounceTime = this.properties.debounceTime !== undefined ? this.properties.debounceTime : this.debounceTime;
      this.trim = this.properties.trim !== undefined ? this.properties.trim : this.trim;
      this.lowercase = this.properties.lowercase !== undefined ? this.properties.lowercase : this.lowercase;
      this.loading = this.properties.loading !== undefined ? this.properties.loading : this.loading;
      this.loadingClass = this.properties.loadingClass || this.loadingClass;
    }
  }

  get finalClasses(): string {
    let classes = this.className;
    
    // Size-specific classes
    if (this.size === 'sm') {
      classes += ' px-2 py-1 text-sm';
    } else if (this.size === 'lg') {
      classes += ' px-4 py-3 text-lg';
    } else {
      classes += ' px-3 py-2';
    }
    
    if (this.disabled) {
      classes += ' opacity-50 cursor-not-allowed bg-gray-50';
    }
    
    if (this.class) {
      classes += ' ' + this.class;
    }
    
    return classes;
  }

  get finalInputClasses(): string {
    let classes = this.inputClass;
    
    // Add padding based on icon position
    if (this.showIcon && this.iconPosition === 'left') {
      classes += ' pl-8';
    } else if (this.showIcon && this.iconPosition === 'right') {
      classes += ' pr-8';
    }
    
    // Add padding for clear button
    if (this.clearable && this.value) {
      if (this.iconPosition === 'left' || !this.showIcon) {
        classes += ' pr-8';
      } else {
        classes += ' pr-12';
      }
    }
    
    return classes;
  }

  onInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    let newValue = target.value;
    
    if (this.trim) {
      newValue = newValue.trim();
    }
    
    if (this.lowercase) {
      newValue = newValue.toLowerCase();
    }
    
    this.value = newValue;
    this.valueChange.emit(this.value);
    
    // Clear existing timer
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
    
    // Set new timer if value meets minimum length
    if (this.value.length >= this.minLength) {
      this.debounceTimer = setTimeout(() => {
        this.search.emit(this.value);
      }, this.debounceTime);
    }
  }

  onKeydown(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
      this.enter.emit(this.value);
      this.search.emit(this.value);
    }
  }

  onFocus(event: Event): void {
    this.focus.emit(event);
  }

  onBlur(event: Event): void {
    this.blur.emit(event);
  }

  onClear(): void {
    this.value = '';
    this.valueChange.emit(this.value);
    this.clear.emit();
    this.search.emit(this.value);
  }

  get showClearButton(): boolean {
    return this.clearable && this.value.length > 0 && !this.disabled && !this.readonly;
  }
}
