<div 
  [class]="finalClasses"
  [style]="style"
>
  <!-- Search Icon (Left) -->
  <div 
    *ngIf="showIcon && iconPosition === 'left'" 
    class="absolute left-3 flex items-center pointer-events-none"
  >
    <svg 
      *ngIf="!loading"
      [class]="iconClass" 
      fill="none" 
      stroke="currentColor" 
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <path 
        stroke-linecap="round" 
        stroke-linejoin="round" 
        stroke-width="2" 
        d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607z" 
      />
    </svg>
    <svg 
      *ngIf="loading"
      [class]="loadingClass"
      fill="none" 
      stroke="currentColor" 
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <path 
        stroke-linecap="round" 
        stroke-linejoin="round" 
        stroke-width="2" 
        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" 
      />
    </svg>
  </div>

  <!-- Search Input -->
  <input
    type="text"
    [value]="value"
    [placeholder]="placeholder"
    [disabled]="disabled"
    [readonly]="readonly"
    [maxlength]="maxLength"
    [class]="finalInputClasses"
    (input)="onInput($event)"
    (keydown)="onKeydown($event)"
    (focus)="onFocus($event)"
    (blur)="onBlur($event)"
    autocomplete="off"
    spellcheck="false"
  />

  <!-- Search Icon (Right) -->
  <div 
    *ngIf="showIcon && iconPosition === 'right'" 
    class="absolute right-3 flex items-center pointer-events-none"
    [class.right-10]="showClearButton"
  >
    <svg 
      *ngIf="!loading"
      [class]="iconClass" 
      fill="none" 
      stroke="currentColor" 
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <path 
        stroke-linecap="round" 
        stroke-linejoin="round" 
        stroke-width="2" 
        d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607z" 
      />
    </svg>
    <svg 
      *ngIf="loading"
      [class]="loadingClass"
      fill="none" 
      stroke="currentColor" 
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <path 
        stroke-linecap="round" 
        stroke-linejoin="round" 
        stroke-width="2" 
        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" 
      />
    </svg>
  </div>

  <!-- Clear Button -->
  <button
    *ngIf="showClearButton"
    type="button"
    class="absolute right-3 flex items-center"
    [class.right-10]="showIcon && iconPosition === 'right'"
    (click)="onClear()"
    aria-label="Clear search"
  >
    <svg 
      [class]="clearIconClass"
      fill="none" 
      stroke="currentColor" 
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <path 
        stroke-linecap="round" 
        stroke-linejoin="round" 
        stroke-width="2" 
        d="M6 18L18 6M6 6l12 12" 
      />
    </svg>
  </button>
</div>
