import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface TagItem {
  id: string | number;
  label: string;
  color?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info';
  count?: number;
  icon?: string;
  removable?: boolean;
  clickable?: boolean;
  disabled?: boolean;
  href?: string;
  target?: '_blank' | '_self' | '_parent' | '_top';
}

@Component({
  selector: 'lib-tag-list-compact',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './tag-list-compact.component.html',
  styleUrl: './tag-list-compact.component.css'
})
export class TagListCompactComponent {
  // Standard Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() tags: TagItem[] = [
    { id: 1, label: 'JavaScript', color: 'primary', count: 45, removable: true, clickable: true },
    { id: 2, label: 'React', color: 'info', count: 32, removable: true, clickable: true },
    { id: 3, label: 'TypeScript', color: 'secondary', count: 28, removable: true, clickable: true },
    { id: 4, label: 'Angular', color: 'danger', count: 15, removable: false, clickable: true },
    { id: 5, label: 'Vue.js', color: 'success', count: 12, removable: true, clickable: true },
    { id: 6, label: 'Node.js', color: 'warning', count: 8, removable: true, clickable: true }
  ];

  @Input() layout: 'horizontal' | 'vertical' | 'grid' = 'horizontal';
  @Input() spacing: 'tight' | 'normal' | 'loose' = 'normal';
  @Input() showCounts: boolean = true;
  @Input() showIcons: boolean = false;
  @Input() allowRemove: boolean = true;
  @Input() allowClick: boolean = true;
  @Input() maxDisplay: number = 0; // 0 means show all
  @Input() truncateLength: number = 20;

  // Event outputs
  @Output() tagClick = new EventEmitter<TagItem>();
  @Output() tagRemove = new EventEmitter<TagItem>();
  @Output() tagHover = new EventEmitter<TagItem>();

  get displayedTags(): TagItem[] {
    if (this.maxDisplay > 0 && this.tags.length > this.maxDisplay) {
      return this.tags.slice(0, this.maxDisplay);
    }
    return this.tags;
  }

  get hiddenCount(): number {
    if (this.maxDisplay > 0 && this.tags.length > this.maxDisplay) {
      return this.tags.length - this.maxDisplay;
    }
    return 0;
  }

  onTagClick(tag: TagItem) {
    if (!tag.disabled && this.allowClick && tag.clickable) {
      this.tagClick.emit(tag);
    }
  }

  onTagRemove(tag: TagItem, event: Event) {
    event.stopPropagation();
    if (!tag.disabled && (this.allowRemove || tag.removable)) {
      this.tagRemove.emit(tag);
    }
  }

  onTagHover(tag: TagItem) {
    if (!tag.disabled) {
      this.tagHover.emit(tag);
    }
  }

  truncateText(text: string): string {
    if (this.truncateLength > 0 && text.length > this.truncateLength) {
      return text.substring(0, this.truncateLength) + '...';
    }
    return text;
  }

  getTagClasses(tag: TagItem): string {
    const baseClasses = 'inline-flex items-center font-medium transition-colors';

    const sizeClasses = {
      xs: 'px-2 py-0.5 text-xs',
      sm: 'px-2.5 py-0.5 text-sm',
      md: 'px-3 py-1 text-sm',
      lg: 'px-3 py-1.5 text-base',
      xl: 'px-4 py-2 text-lg'
    };

    const colorClasses = {
      default: 'bg-gray-100 text-gray-800 hover:bg-gray-200',
      primary: 'bg-blue-100 text-blue-800 hover:bg-blue-200',
      secondary: 'bg-purple-100 text-purple-800 hover:bg-purple-200',
      success: 'bg-green-100 text-green-800 hover:bg-green-200',
      warning: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',
      danger: 'bg-red-100 text-red-800 hover:bg-red-200',
      info: 'bg-cyan-100 text-cyan-800 hover:bg-cyan-200'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const interactiveClasses = (tag.clickable && this.allowClick && !tag.disabled)
      ? 'cursor-pointer hover:shadow-sm'
      : '';

    const disabledClasses = tag.disabled ? 'opacity-50 cursor-not-allowed' : '';
    const tagColor = tag.color || this.variant;

    return [
      baseClasses,
      sizeClasses[this.size],
      colorClasses[tagColor],
      roundedClasses[this.rounded],
      interactiveClasses,
      disabledClasses
    ].filter(Boolean).join(' ');
  }

  get containerClasses(): string {
    const baseClasses = 'flex';

    const layoutClasses = {
      horizontal: 'flex-row flex-wrap',
      vertical: 'flex-col',
      grid: 'grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4'
    };

    const spacingClasses = {
      tight: 'gap-1',
      normal: 'gap-2',
      loose: 'gap-3'
    };

    return [
      baseClasses,
      layoutClasses[this.layout],
      spacingClasses[this.spacing],
      this.className
    ].filter(Boolean).join(' ');
  }

  trackByTag(index: number, tag: TagItem): any {
    return tag.id;
  }
}
