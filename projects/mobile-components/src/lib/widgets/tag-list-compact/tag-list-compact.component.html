<div [class]="containerClasses">
  <!-- Display tags -->
  <div
    *ngFor="let tag of displayedTags; trackBy: trackByTag"
    [class]="getTagClasses(tag)"
    (click)="onTagClick(tag)"
    (mouseenter)="onTagHover(tag)"
    [attr.aria-label]="tag.label + (tag.count ? ' (' + tag.count + ')' : '')"
    [attr.role]="tag.clickable && allowClick ? 'button' : 'status'"
    [attr.tabindex]="tag.clickable && allowClick && !tag.disabled ? '0' : null"
  >
    <!-- Icon -->
    <svg
      *ngIf="showIcons && tag.icon"
      class="w-4 h-4 mr-1.5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      [attr.aria-hidden]="true"
    >
      <!-- Default tag icon -->
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
      ></path>
    </svg>

    <!-- Tag Label -->
    <span class="font-medium">{{ truncateText(tag.label) }}</span>

    <!-- Tag Count -->
    <span *ngIf="showCounts && tag.count !== undefined" class="ml-1 opacity-75">
      ({{ tag.count }})
    </span>

    <!-- Remove Button -->
    <button
      *ngIf="(allowRemove || tag.removable) && !tag.disabled"
      type="button"
      class="ml-1.5 inline-flex items-center justify-center w-4 h-4 text-current hover:bg-black hover:bg-opacity-10 rounded-full transition-colors"
      (click)="onTagRemove(tag, $event)"
      [attr.aria-label]="'Remove ' + tag.label"
    >
      <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
    </button>
  </div>

  <!-- Show count of hidden tags -->
  <div
    *ngIf="hiddenCount > 0"
    class="inline-flex items-center px-3 py-1 text-sm font-medium text-gray-500 bg-gray-100 rounded-md"
  >
    +{{ hiddenCount }} more
  </div>

  <!-- Empty state -->
  <div
    *ngIf="tags.length === 0"
    class="inline-flex items-center px-3 py-1 text-sm text-gray-500 bg-gray-50 rounded-md"
  >
    No tags to display
  </div>
</div>
