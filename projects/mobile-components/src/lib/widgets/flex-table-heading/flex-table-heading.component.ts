import { Component, Input, Output, EventEmitter, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'base-flex-table-heading',
  templateUrl: './flex-table-heading.component.html',
  styleUrls: ['./flex-table-heading.component.css'],
  standalone: true,
  imports: [CommonModule, FormsModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class FlexTableHeadingComponent {
  @Input() type: 'grow' | 'shrink' | 'stable' | 'auto' = 'stable';
  @Input() title: string = 'Column Header';
  @Input() sortable: boolean = false;
  @Input() sortDirection: 'asc' | 'desc' | null = null;
  @Input() resizable: boolean = false;
  @Input() filterable: boolean = false;
  @Input() align: 'left' | 'center' | 'right' = 'left';
  @Input() width: string = '';
  @Input() minWidth: string = '';
  @Input() maxWidth: string = '';
  @Input() sticky: boolean = false;
  @Input() required: boolean = false;
  @Input() disabled: boolean = false;
  
  // Content and styling
  @Input() icon: string = '';
  @Input() tooltip: string = '';
  @Input() badge: string | number = '';
  @Input() uppercase: boolean = true;
  
  // Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'minimal' | 'bordered' | 'highlighted' | 'primary' | 'secondary' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'xl' = 'none';
  @Input() padding: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  
  @Output() sort = new EventEmitter<'asc' | 'desc'>();
  @Output() filter = new EventEmitter<any>();
  @Output() resize = new EventEmitter<{width: number, column: string}>();
  @Output() headerClick = new EventEmitter<MouseEvent>();

  isResizing: boolean = false;
  showFilterMenu: boolean = false;
  filterValue: string = '';

  get headerClasses(): string {
    const baseClasses = [
      'flex-table-heading',
      'relative',
      'flex',
      'items-center',
      'font-medium',
      'text-gray-900',
      'tracking-wider',
      'select-none',
      'transition-colors',
      'duration-200'
    ];

    // Flex behavior classes
    const flexClasses = {
      grow: ['flex-1', 'min-w-0'],
      shrink: ['flex-shrink'],
      stable: ['sm:w-[90px]', 'md:w-[110px]', 'md:shrink-0'],
      auto: ['flex-auto']
    };
    baseClasses.push(...flexClasses[this.type]);

    // Size classes (padding and text size)
    const sizeClasses = {
      xs: ['text-xs', 'py-1', 'px-2'],
      sm: ['text-xs', 'py-2', 'px-3'],
      md: ['text-xs', 'py-3', 'px-4'],
      lg: ['text-sm', 'py-3', 'px-6'],
      xl: ['text-sm', 'py-4', 'px-8']
    };
    baseClasses.push(...sizeClasses[this.size]);

    // Alignment
    const alignClasses = {
      left: ['justify-start', 'text-left'],
      center: ['justify-center', 'text-center'],
      right: ['justify-end', 'text-right']
    };
    baseClasses.push(...alignClasses[this.align]);

    // Variant styling
    const variantClasses = {
      default: ['bg-gray-50', 'text-gray-900', 'border-b', 'border-gray-200'],
      minimal: ['bg-transparent', 'text-gray-700'],
      bordered: ['bg-white', 'text-gray-900', 'border', 'border-gray-200'],
      highlighted: ['bg-blue-50', 'text-blue-900', 'border-b', 'border-blue-200'],
      primary: ['bg-blue-600', 'text-white'],
      secondary: ['bg-gray-600', 'text-white']
    };
    baseClasses.push(...variantClasses[this.variant]);

    // Interactive states
    if (this.sortable && !this.disabled) {
      baseClasses.push('cursor-pointer', 'hover:bg-gray-100');
      
      if (this.variant === 'primary') {
        baseClasses.push('hover:bg-blue-700');
      } else if (this.variant === 'secondary') {
        baseClasses.push('hover:bg-gray-700');
      }
    }

    if (this.disabled) {
      baseClasses.push('opacity-50', 'cursor-not-allowed');
    }

    // Text transform
    if (this.uppercase) {
      baseClasses.push('uppercase');
    }

    // Sticky header
    if (this.sticky) {
      baseClasses.push('sticky', 'top-0', 'z-10');
    }

    // Rounded corners
    const roundedClasses = {
      none: [],
      sm: ['rounded-sm'],
      md: ['rounded-md'],
      lg: ['rounded-lg'],
      xl: ['rounded-xl']
    };
    baseClasses.push(...roundedClasses[this.rounded]);

    if (this.className) {
      baseClasses.push(this.className);
    }

    return baseClasses.join(' ');
  }

  get contentClasses(): string {
    const baseClasses = [
      'flex',
      'items-center',
      'space-x-2',
      'min-w-0'
    ];

    if (this.type === 'grow') {
      baseClasses.push('w-full');
    }

    return baseClasses.join(' ');
  }

  get computedStyle(): {[key: string]: string} {
    const styles: {[key: string]: string} = {};
    
    if (this.width) {
      styles['width'] = this.width;
    }
    
    if (this.minWidth) {
      styles['min-width'] = this.minWidth;
    }
    
    if (this.maxWidth) {
      styles['max-width'] = this.maxWidth;
    }
    
    return styles;
  }

  onHeaderClick(event: MouseEvent): void {
    if (this.disabled) return;
    
    this.headerClick.emit(event);
    
    if (this.sortable) {
      this.onSort();
    }
  }

  onSort(): void {
    if (this.disabled || !this.sortable) return;
    
    let newDirection: 'asc' | 'desc';
    
    if (this.sortDirection === null || this.sortDirection === 'desc') {
      newDirection = 'asc';
    } else {
      newDirection = 'desc';
    }
    
    this.sortDirection = newDirection;
    this.sort.emit(newDirection);
  }

  onFilterToggle(): void {
    if (this.disabled || !this.filterable) return;
    
    this.showFilterMenu = !this.showFilterMenu;
  }

  onFilterApply(): void {
    this.filter.emit(this.filterValue);
    this.showFilterMenu = false;
  }

  onFilterClear(): void {
    this.filterValue = '';
    this.filter.emit('');
    this.showFilterMenu = false;
  }

  onResizeStart(event: MouseEvent): void {
    if (this.disabled || !this.resizable) return;
    
    this.isResizing = true;
    event.preventDefault();
    
    const startX = event.clientX;
    const startWidth = ((event.target as HTMLElement).closest('.flex-table-heading') as HTMLElement)?.offsetWidth || 0;
    
    const onMouseMove = (e: MouseEvent) => {
      const newWidth = startWidth + (e.clientX - startX);
      this.resize.emit({ width: newWidth, column: this.title });
    };
    
    const onMouseUp = () => {
      this.isResizing = false;
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };
    
    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  }

  getSortIcon(): string {
    if (!this.sortable || this.sortDirection === null) {
      return 'M7 10l5 5 5-5z'; // Default unsorted icon
    }
    
    return this.sortDirection === 'asc'
      ? 'M5 15l7-7 7 7z' // Sort ascending
      : 'M19 9l-7 7-7-7z'; // Sort descending
  }

  getSortIconClasses(): string {
    const baseClasses = ['w-4', 'h-4', 'transition-transform', 'duration-200'];
    
    if (this.sortDirection === null) {
      baseClasses.push('text-gray-400');
    } else {
      baseClasses.push('text-blue-600');
    }
    
    return baseClasses.join(' ');
  }

  getBadgeClasses(): string {
    const badgeClasses = [
      'inline-flex',
      'items-center',
      'justify-center',
      'px-2',
      'py-1',
      'text-xs',
      'font-bold',
      'leading-none',
      'rounded-full'
    ];

    // Badge color based on variant
    const badgeVariantClasses = {
      default: ['text-white', 'bg-gray-500'],
      minimal: ['text-white', 'bg-gray-500'],
      bordered: ['text-white', 'bg-gray-500'],
      highlighted: ['text-white', 'bg-blue-500'],
      primary: ['text-blue-600', 'bg-blue-100'],
      secondary: ['text-gray-600', 'bg-gray-100']
    };
    badgeClasses.push(...badgeVariantClasses[this.variant]);

    return badgeClasses.join(' ');
  }

  onKeyDown(event: KeyboardEvent): void {
    if (this.disabled) return;
    
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      if (this.sortable) {
        this.onSort();
      }
    }
  }
}
