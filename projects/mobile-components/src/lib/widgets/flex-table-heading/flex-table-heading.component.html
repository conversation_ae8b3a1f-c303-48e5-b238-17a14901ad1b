<div
  [class]="headerClasses"
  [ngStyle]="computedStyle"
  (click)="onHeaderClick($event)"
  (keydown)="onKeyDown($event)"
  [attr.role]="sortable ? 'button' : null"
  [attr.tabindex]="sortable && !disabled ? 0 : null"
  [attr.aria-sort]="sortDirection"
  [title]="tooltip"
>
  <div [class]="contentClasses">
    <!-- Icon -->
    <span *ngIf="icon" class="flex-shrink-0" [innerHTML]="icon"></span>
    
    <!-- Title Content -->
    <span class="min-w-0 flex-1" [class.truncate]="type !== 'grow'">
      {{ title }}
      <ng-content></ng-content>
    </span>
    
    <!-- Required Indicator -->
    <span *ngIf="required" class="text-red-500 ml-1" aria-label="Required field">*</span>
    
    <!-- Badge -->
    <span *ngIf="badge" [class]="getBadgeClasses()">
      {{ badge }}
    </span>
    
    <!-- Sort Icon -->
    <svg 
      *ngIf="sortable" 
      [class]="getSortIconClasses()"
      fill="none" 
      stroke="currentColor" 
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="getSortIcon()"></path>
    </svg>
    
    <!-- Filter Button -->
    <button
      *ngIf="filterable"
      (click)="onFilterToggle(); $event.stopPropagation()"
      [disabled]="disabled"
      class="ml-1 p-1 rounded hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
      [class.text-blue-600]="showFilterMenu"
      title="Filter column"
    >
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"></path>
      </svg>
    </button>
  </div>

  <!-- Filter Menu -->
  <div 
    *ngIf="filterable && showFilterMenu" 
    class="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-20 p-3 min-w-48"
  >
    <div class="space-y-2">
      <input
        type="text"
        [(ngModel)]="filterValue"
        placeholder="Filter..."
        class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
      >
      <div class="flex space-x-2">
        <button
          (click)="onFilterApply()"
          class="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Apply
        </button>
        <button
          (click)="onFilterClear()"
          class="px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          Clear
        </button>
      </div>
    </div>
  </div>
  
  <!-- Resize Handle -->
  <div 
    *ngIf="resizable"
    (mousedown)="onResizeStart($event)"
    class="absolute right-0 top-0 bottom-0 w-1 cursor-col-resize hover:bg-blue-500 opacity-0 hover:opacity-100 transition-opacity"
    [class.bg-blue-500]="isResizing"
    [class.opacity-100]="isResizing"
  ></div>
</div>
