<div [class]="computedClasses" role="group" [attr.aria-label]="'Button group'">
  <!-- Dynamic Buttons -->
  <button
    *ngFor="let button of buttons; trackBy: trackByFn; let i = index"
    type="button"
    [class]="getButtonClasses(button, i)"
    [disabled]="button.disabled || disabled"
    [attr.aria-pressed]="button.selected"
    (click)="onButtonClick(button)"
  >
    <!-- Icon -->
    <i *ngIf="button.icon" [class]="button.icon + ' mr-2'" aria-hidden="true"></i>
    
    <!-- Label -->
    <span>{{ button.label }}</span>
  </button>

  <!-- Fallback to ng-content for custom button content -->
  <ng-content *ngIf="buttons.length === 0"></ng-content>
</div>