import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface ButtonItem {
  id: string | number;
  label: string;
  value?: any;
  icon?: string;
  disabled?: boolean;
  selected?: boolean;
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
}

@Component({
  selector: 'lib-button-group',
  templateUrl: './button-group.component.html',
  styleUrls: ['./button-group.component.css'],
  standalone: true,
  imports: [CommonModule]
})
export class ButtonGroupComponent {
  @Input() buttons: ButtonItem[] = [
    { id: 1, label: 'Button 1', selected: true },
    { id: 2, label: 'Button 2' },
    { id: 3, label: 'Button 3' }
  ];
  @Input() multiple: boolean = false;
  @Input() disabled: boolean = false;
  @Input() vertical: boolean = false;
  @Input() allowDeselect: boolean = false;
  @Input() equalWidth: boolean = false;
  
  // Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  @Output() selectionChange = new EventEmitter<ButtonItem[]>();
  @Output() buttonClick = new EventEmitter<ButtonItem>();

  get computedClasses(): string {
    const baseClasses = [
      'button-group-widget',
      'inline-flex'
    ];

    if (this.vertical) {
      baseClasses.push('flex-col');
    } else {
      baseClasses.push('flex-row');
    }

    if (this.equalWidth) {
      baseClasses.push('w-full');
    }

    if (this.className) {
      baseClasses.push(this.className);
    }

    return baseClasses.join(' ');
  }

  getButtonClasses(button: ButtonItem, index: number): string {
    const baseClasses = [
      'relative',
      'inline-flex',
      'items-center',
      'justify-center',
      'border',
      'font-medium',
      'transition-all',
      'duration-200',
      'focus:outline-none',
      'focus:ring-2',
      'focus:ring-offset-2'
    ];

    if (this.equalWidth) {
      baseClasses.push('flex-1');
    }

    // Size classes
    const sizeClasses = {
      xs: ['text-xs', 'px-2', 'py-1'],
      sm: ['text-sm', 'px-3', 'py-1.5'],
      md: ['text-sm', 'px-4', 'py-2'],
      lg: ['text-base', 'px-4', 'py-2'],
      xl: ['text-base', 'px-6', 'py-3']
    };

    // Get effective variant (button-specific or global)
    const effectiveVariant = button.variant || this.variant;

    // Variant classes for selected/unselected states
    const variantClasses = {
      default: {
        selected: ['bg-gray-200', 'text-gray-900', 'border-gray-300'],
        unselected: ['bg-white', 'text-gray-700', 'border-gray-300', 'hover:bg-gray-50']
      },
      primary: {
        selected: ['bg-blue-600', 'text-white', 'border-blue-600'],
        unselected: ['bg-white', 'text-blue-600', 'border-blue-600', 'hover:bg-blue-50']
      },
      secondary: {
        selected: ['bg-gray-600', 'text-white', 'border-gray-600'],
        unselected: ['bg-white', 'text-gray-600', 'border-gray-600', 'hover:bg-gray-50']
      },
      success: {
        selected: ['bg-green-600', 'text-white', 'border-green-600'],
        unselected: ['bg-white', 'text-green-600', 'border-green-600', 'hover:bg-green-50']
      },
      warning: {
        selected: ['bg-yellow-600', 'text-white', 'border-yellow-600'],
        unselected: ['bg-white', 'text-yellow-600', 'border-yellow-600', 'hover:bg-yellow-50']
      },
      danger: {
        selected: ['bg-red-600', 'text-white', 'border-red-600'],
        unselected: ['bg-white', 'text-red-600', 'border-red-600', 'hover:bg-red-50']
      }
    };

    // Position-based border radius classes
    const isFirst = index === 0;
    const isLast = index === this.buttons.length - 1;
    const isSingle = this.buttons.length === 1;

    let borderClasses: string[] = [];
    
    if (this.rounded !== 'none') {
      const roundedValues = {
        sm: 'rounded-sm',
        md: 'rounded-md', 
        lg: 'rounded-lg',
        full: 'rounded-full'
      };
      
      if (isSingle) {
        borderClasses.push(roundedValues[this.rounded]);
      } else if (this.vertical) {
        if (isFirst) {
          borderClasses.push(`${roundedValues[this.rounded].replace('rounded', 'rounded-t')}`);
        } else if (isLast) {
          borderClasses.push(`${roundedValues[this.rounded].replace('rounded', 'rounded-b')}`);
        }
      } else {
        if (isFirst) {
          borderClasses.push(`${roundedValues[this.rounded].replace('rounded', 'rounded-l')}`);
        } else if (isLast) {
          borderClasses.push(`${roundedValues[this.rounded].replace('rounded', 'rounded-r')}`);
        }
      }
    }

    // Handle border overlaps
    if (!isFirst) {
      if (this.vertical) {
        borderClasses.push('-mt-px');
      } else {
        borderClasses.push('-ml-px');
      }
    }

    // Focus ring color
    const focusRingClasses = {
      default: 'focus:ring-gray-500',
      primary: 'focus:ring-blue-500',
      secondary: 'focus:ring-gray-500',
      success: 'focus:ring-green-500',
      warning: 'focus:ring-yellow-500',
      danger: 'focus:ring-red-500'
    };

    const classes = [
      ...baseClasses,
      ...sizeClasses[this.size],
      ...variantClasses[effectiveVariant][button.selected ? 'selected' : 'unselected'],
      ...borderClasses,
      focusRingClasses[effectiveVariant]
    ];

    // Disabled state
    if (button.disabled || this.disabled) {
      classes.push('opacity-50', 'cursor-not-allowed', 'pointer-events-none');
    } else {
      classes.push('cursor-pointer');
    }

    return classes.join(' ');
  }

  onButtonClick(button: ButtonItem): void {
    if (button.disabled || this.disabled) {
      return;
    }

    // Handle selection logic
    if (this.multiple) {
      // Multiple selection mode
      button.selected = !button.selected;
    } else {
      // Single selection mode
      if (button.selected && this.allowDeselect) {
        button.selected = false;
      } else {
        // Deselect all others and select this one
        this.buttons.forEach(b => b.selected = false);
        button.selected = true;
      }
    }

    this.buttonClick.emit(button);
    this.selectionChange.emit(this.getSelectedButtons());
  }

  getSelectedButtons(): ButtonItem[] {
    return this.buttons.filter(button => button.selected);
  }

  // Legacy support for existing NUI classes (backward compatibility)
  get classes(): string[] {
    return this.computedClasses.split(' ');
  }

  trackByFn(index: number, button: ButtonItem): any {
    return button.id || index;
  }
}