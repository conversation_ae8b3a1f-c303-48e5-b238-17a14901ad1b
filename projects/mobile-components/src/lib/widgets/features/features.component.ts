import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface Feature {
  id: string;
  title: string;
  description: string;
  icon?: string;
  image?: string;
  badge?: string;
  isNew?: boolean;
  isPopular?: boolean;
  isPremium?: boolean;
  link?: string;
  category?: string;
  tags?: string[];
  status?: 'available' | 'coming-soon' | 'beta';
}

export interface FeatureEvent {
  feature: Feature;
  action: 'click' | 'learn-more' | 'try-now' | 'upgrade';
}

@Component({
  selector: 'lib-features',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './features.component.html',
  styleUrl: './features.component.css'
})
export class FeaturesComponent {
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  
  // Component-specific properties
  @Input() features: Feature[] = [
    {
      id: '1',
      title: 'Advanced Analytics',
      description: 'Get deep insights into your business performance with comprehensive analytics and reporting tools.',
      icon: '📊',
      badge: 'Most Popular',
      isPopular: true,
      status: 'available',
      category: 'Analytics',
      tags: ['data', 'reports', 'insights']
    },
    {
      id: '2',
      title: 'AI-Powered Automation',
      description: 'Automate repetitive tasks with intelligent AI algorithms that learn from your workflow patterns.',
      icon: '🤖',
      badge: 'New',
      isNew: true,
      status: 'available',
      category: 'Automation',
      tags: ['ai', 'automation', 'efficiency']
    },
    {
      id: '3',
      title: 'Real-time Collaboration',
      description: 'Work together seamlessly with team members using real-time editing and communication tools.',
      icon: '👥',
      status: 'available',
      category: 'Collaboration',
      tags: ['teamwork', 'real-time', 'communication']
    },
    {
      id: '4',
      title: 'Enterprise Security',
      description: 'Bank-level security with end-to-end encryption, two-factor authentication, and compliance standards.',
      icon: '🔒',
      badge: 'Premium',
      isPremium: true,
      status: 'available',
      category: 'Security',
      tags: ['security', 'encryption', 'compliance']
    },
    {
      id: '5',
      title: 'Mobile App',
      description: 'Access all features on the go with our native mobile applications for iOS and Android.',
      icon: '📱',
      status: 'available',
      category: 'Mobile',
      tags: ['mobile', 'ios', 'android']
    },
    {
      id: '6',
      title: 'Advanced Integrations',
      description: 'Connect with over 1000+ third-party applications and services through our robust API platform.',
      icon: '🔗',
      status: 'coming-soon',
      category: 'Integrations',
      tags: ['api', 'integrations', 'third-party']
    }
  ];
  @Input() layout: 'grid' | 'list' | 'card' | 'compact' = 'grid';
  @Input() columns: 1 | 2 | 3 | 4 | 6 = 3;
  @Input() showBadges: boolean = true;
  @Input() showCategories: boolean = true;
  @Input() showStatus: boolean = true;
  @Input() showActions: boolean = true;
  @Input() allowFiltering: boolean = true;
  @Input() filterCategories: string[] = [];
  @Input() maxFeatures: number = 0; // 0 means no limit

  // Events
  @Output() featureClick = new EventEmitter<FeatureEvent>();
  @Output() categoryFilter = new EventEmitter<string>();
  @Output() learnMore = new EventEmitter<Feature>();
  @Output() tryNow = new EventEmitter<Feature>();
  @Output() upgrade = new EventEmitter<Feature>();

  selectedCategory: string = 'all';

  get containerClasses(): string {
    const sizeClasses = {
      xs: 'p-2 text-xs',
      sm: 'p-4 text-sm',
      md: 'p-6 text-base',
      lg: 'p-8 text-lg',
      xl: 'p-10 text-xl'
    };

    const variantClasses = {
      default: 'bg-white',
      primary: 'bg-blue-50',
      secondary: 'bg-gray-50',
      success: 'bg-green-50',
      warning: 'bg-yellow-50',
      danger: 'bg-red-50'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-xl'
    };

    return `
      ${sizeClasses[this.size]}
      ${variantClasses[this.variant]}
      ${roundedClasses[this.rounded]}
      ${this.className}
    `.trim().replace(/\s+/g, ' ');
  }

  get gridClasses(): string {
    const columnClasses = {
      1: 'grid-cols-1',
      2: 'grid-cols-1 md:grid-cols-2',
      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
      6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6'
    };

    return this.layout === 'grid' ? `grid gap-6 ${columnClasses[this.columns]}` : '';
  }

  get categories(): string[] {
    const allCategories = [...new Set(this.features.map(f => f.category).filter(Boolean))] as string[];
    return this.filterCategories.length > 0 ? this.filterCategories : allCategories;
  }

  get filteredFeatures(): Feature[] {
    let filtered = this.features;
    
    if (this.selectedCategory !== 'all') {
      filtered = filtered.filter(f => f.category === this.selectedCategory);
    }
    
    if (this.maxFeatures > 0) {
      filtered = filtered.slice(0, this.maxFeatures);
    }
    
    return filtered;
  }

  onFeatureClick(feature: Feature): void {
    this.featureClick.emit({ feature, action: 'click' });
  }

  onCategorySelect(category: string): void {
    this.selectedCategory = category;
    this.categoryFilter.emit(category);
  }

  onLearnMore(feature: Feature, event: Event): void {
    event.stopPropagation();
    this.learnMore.emit(feature);
    this.featureClick.emit({ feature, action: 'learn-more' });
  }

  onTryNow(feature: Feature, event: Event): void {
    event.stopPropagation();
    this.tryNow.emit(feature);
    this.featureClick.emit({ feature, action: 'try-now' });
  }

  onUpgrade(feature: Feature, event: Event): void {
    event.stopPropagation();
    this.upgrade.emit(feature);
    this.featureClick.emit({ feature, action: 'upgrade' });
  }

  getStatusColor(status?: string): string {
    const colors = {
      'available': 'text-green-600 bg-green-100',
      'coming-soon': 'text-orange-600 bg-orange-100',
      'beta': 'text-blue-600 bg-blue-100'
    };
    return colors[status as keyof typeof colors] || 'text-gray-600 bg-gray-100';
  }

  getBadgeColor(feature: Feature): string {
    if (feature.isNew) return 'bg-green-500 text-white';
    if (feature.isPopular) return 'bg-blue-500 text-white';
    if (feature.isPremium) return 'bg-purple-500 text-white';
    return 'bg-gray-500 text-white';
  }

  trackByFeatureId(index: number, feature: Feature): string {
    return feature.id;
  }

  trackByCategory(index: number, category: string): string {
    return category;
  }
}
