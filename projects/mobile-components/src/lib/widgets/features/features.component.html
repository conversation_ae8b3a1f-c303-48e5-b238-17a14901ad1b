<div [class]="containerClasses" 
     role="region" 
     aria-label="Feature showcase">
  
  <!-- Header Section -->
  <div class="mb-8">
    <h2 class="text-3xl font-bold text-gray-900 mb-4">Features</h2>
    <p class="text-lg text-gray-600 max-w-3xl">
      Discover powerful tools and capabilities designed to enhance your productivity and streamline your workflow.
    </p>
  </div>

  <!-- Category Filter -->
  <div *ngIf="allowFiltering && categories.length > 0" class="mb-8">
    <div class="flex flex-wrap gap-2">
      <button 
        (click)="onCategorySelect('all')"
        [class]="selectedCategory === 'all' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
        class="px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200">
        All Features
      </button>
      <button 
        *ngFor="let category of categories; trackBy: trackByCategory"
        (click)="onCategorySelect(category)"
        [class]="selectedCategory === category ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
        class="px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200">
        {{ category }}
      </button>
    </div>
  </div>

  <!-- Features Grid Layout -->
  <div *ngIf="layout === 'grid'" [class]="gridClasses">
    <div *ngFor="let feature of filteredFeatures; trackBy: trackByFeatureId"
         class="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden cursor-pointer group"
         (click)="onFeatureClick(feature)">
      
      <!-- Feature Image -->
      <div *ngIf="feature.image" class="h-48 bg-gray-100 overflow-hidden">
        <img [src]="feature.image" 
             [alt]="feature.title"
             class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200">
      </div>

      <!-- Feature Content -->
      <div class="p-6">
        <!-- Header with Icon and Badges -->
        <div class="flex items-start justify-between mb-4">
          <div class="flex items-center space-x-3">
            <div *ngIf="feature.icon" class="text-2xl">{{ feature.icon }}</div>
            <div>
              <h3 class="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200">
                {{ feature.title }}
              </h3>
              <p *ngIf="showCategories && feature.category" class="text-sm text-gray-500 mt-1">
                {{ feature.category }}
              </p>
            </div>
          </div>
          
          <!-- Badges -->
          <div *ngIf="showBadges" class="flex flex-col space-y-1">
            <span *ngIf="feature.badge" 
                  [class]="getBadgeColor(feature)"
                  class="px-2 py-1 text-xs font-medium rounded-full">
              {{ feature.badge }}
            </span>
            <span *ngIf="showStatus && feature.status" 
                  [class]="getStatusColor(feature.status)"
                  class="px-2 py-1 text-xs font-medium rounded-full capitalize">
              {{ feature.status.replace('-', ' ') }}
            </span>
          </div>
        </div>

        <!-- Description -->
        <p class="text-gray-600 mb-4 line-clamp-3">{{ feature.description }}</p>

        <!-- Tags -->
        <div *ngIf="feature.tags && feature.tags.length > 0" class="mb-4">
          <div class="flex flex-wrap gap-1">
            <span *ngFor="let tag of feature.tags" 
                  class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-md">
              {{ tag }}
            </span>
          </div>
        </div>

        <!-- Actions -->
        <div *ngIf="showActions" class="flex space-x-2">
          <button 
            *ngIf="feature.status === 'available'"
            (click)="onTryNow(feature, $event)"
            class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors duration-200">
            Try Now
          </button>
          <button 
            *ngIf="feature.isPremium"
            (click)="onUpgrade(feature, $event)"
            class="flex-1 bg-purple-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-purple-700 transition-colors duration-200">
            Upgrade
          </button>
          <button 
            (click)="onLearnMore(feature, $event)"
            class="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors duration-200">
            Learn More
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Features List Layout -->
  <div *ngIf="layout === 'list'" class="space-y-4">
    <div *ngFor="let feature of filteredFeatures; trackBy: trackByFeatureId"
         class="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden cursor-pointer group"
         (click)="onFeatureClick(feature)">
      
      <div class="flex items-start p-6">
        <!-- Icon -->
        <div *ngIf="feature.icon" class="flex-shrink-0 text-3xl mr-4">{{ feature.icon }}</div>
        
        <!-- Content -->
        <div class="flex-1">
          <div class="flex items-start justify-between mb-2">
            <div>
              <h3 class="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200">
                {{ feature.title }}
              </h3>
              <p *ngIf="showCategories && feature.category" class="text-sm text-gray-500">
                {{ feature.category }}
              </p>
            </div>
            
            <!-- Badges -->
            <div *ngIf="showBadges" class="flex space-x-2">
              <span *ngIf="feature.badge" 
                    [class]="getBadgeColor(feature)"
                    class="px-2 py-1 text-xs font-medium rounded-full">
                {{ feature.badge }}
              </span>
              <span *ngIf="showStatus && feature.status" 
                    [class]="getStatusColor(feature.status)"
                    class="px-2 py-1 text-xs font-medium rounded-full capitalize">
                {{ feature.status.replace('-', ' ') }}
              </span>
            </div>
          </div>
          
          <p class="text-gray-600 mb-4">{{ feature.description }}</p>
          
          <!-- Tags -->
          <div *ngIf="feature.tags && feature.tags.length > 0" class="mb-4">
            <div class="flex flex-wrap gap-1">
              <span *ngFor="let tag of feature.tags" 
                    class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-md">
                {{ tag }}
              </span>
            </div>
          </div>
          
          <!-- Actions -->
          <div *ngIf="showActions" class="flex space-x-2">
            <button 
              *ngIf="feature.status === 'available'"
              (click)="onTryNow(feature, $event)"
              class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors duration-200">
              Try Now
            </button>
            <button 
              *ngIf="feature.isPremium"
              (click)="onUpgrade(feature, $event)"
              class="bg-purple-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-purple-700 transition-colors duration-200">
              Upgrade
            </button>
            <button 
              (click)="onLearnMore(feature, $event)"
              class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors duration-200">
              Learn More
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Features Card Layout -->
  <div *ngIf="layout === 'card'" class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div *ngFor="let feature of filteredFeatures; trackBy: trackByFeatureId"
         class="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-lg transition-shadow duration-200 overflow-hidden cursor-pointer group"
         (click)="onFeatureClick(feature)">
      
      <div class="p-8 text-center">
        <!-- Icon -->
        <div *ngIf="feature.icon" class="text-5xl mb-4">{{ feature.icon }}</div>
        
        <!-- Badges -->
        <div *ngIf="showBadges" class="flex justify-center space-x-2 mb-4">
          <span *ngIf="feature.badge" 
                [class]="getBadgeColor(feature)"
                class="px-3 py-1 text-xs font-medium rounded-full">
            {{ feature.badge }}
          </span>
          <span *ngIf="showStatus && feature.status" 
                [class]="getStatusColor(feature.status)"
                class="px-3 py-1 text-xs font-medium rounded-full capitalize">
            {{ feature.status.replace('-', ' ') }}
          </span>
        </div>
        
        <!-- Title -->
        <h3 class="text-2xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-200">
          {{ feature.title }}
        </h3>
        
        <!-- Category -->
        <p *ngIf="showCategories && feature.category" class="text-sm text-gray-500 mb-4">
          {{ feature.category }}
        </p>
        
        <!-- Description -->
        <p class="text-gray-600 mb-6">{{ feature.description }}</p>
        
        <!-- Tags -->
        <div *ngIf="feature.tags && feature.tags.length > 0" class="mb-6">
          <div class="flex flex-wrap justify-center gap-2">
            <span *ngFor="let tag of feature.tags" 
                  class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-md">
              {{ tag }}
            </span>
          </div>
        </div>
        
        <!-- Actions -->
        <div *ngIf="showActions" class="space-y-2">
          <button 
            *ngIf="feature.status === 'available'"
            (click)="onTryNow(feature, $event)"
            class="w-full bg-blue-600 text-white px-6 py-3 rounded-md font-medium hover:bg-blue-700 transition-colors duration-200">
            Try Now
          </button>
          <button 
            *ngIf="feature.isPremium"
            (click)="onUpgrade(feature, $event)"
            class="w-full bg-purple-600 text-white px-6 py-3 rounded-md font-medium hover:bg-purple-700 transition-colors duration-200">
            Upgrade to Access
          </button>
          <button 
            (click)="onLearnMore(feature, $event)"
            class="w-full bg-gray-100 text-gray-700 px-6 py-3 rounded-md font-medium hover:bg-gray-200 transition-colors duration-200">
            Learn More
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Features Compact Layout -->
  <div *ngIf="layout === 'compact'" class="space-y-3">
    <div *ngFor="let feature of filteredFeatures; trackBy: trackByFeatureId"
         class="bg-white rounded-md border border-gray-200 hover:border-blue-300 transition-colors duration-200 cursor-pointer group"
         (click)="onFeatureClick(feature)">
      
      <div class="flex items-center p-4">
        <!-- Icon -->
        <div *ngIf="feature.icon" class="flex-shrink-0 text-xl mr-3">{{ feature.icon }}</div>
        
        <!-- Content -->
        <div class="flex-1">
          <div class="flex items-center justify-between">
            <h3 class="font-medium text-gray-900 group-hover:text-blue-600 transition-colors duration-200">
              {{ feature.title }}
            </h3>
            
            <!-- Badges -->
            <div *ngIf="showBadges" class="flex space-x-1">
              <span *ngIf="feature.badge" 
                    [class]="getBadgeColor(feature)"
                    class="px-2 py-1 text-xs font-medium rounded-full">
                {{ feature.badge }}
              </span>
              <span *ngIf="showStatus && feature.status" 
                    [class]="getStatusColor(feature.status)"
                    class="px-2 py-1 text-xs font-medium rounded-full capitalize">
                {{ feature.status.replace('-', ' ') }}
              </span>
            </div>
          </div>
          
          <p class="text-sm text-gray-600 mt-1 line-clamp-2">{{ feature.description }}</p>
          
          <!-- Category -->
          <p *ngIf="showCategories && feature.category" class="text-xs text-gray-500 mt-1">
            {{ feature.category }}
          </p>
        </div>
        
        <!-- Arrow -->
        <div class="flex-shrink-0 ml-3">
          <svg class="w-5 h-5 text-gray-400 group-hover:text-blue-600 transition-colors duration-200" 
               fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
          </svg>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="filteredFeatures.length === 0" class="text-center py-12">
    <div class="text-gray-400 text-5xl mb-4">🔍</div>
    <h3 class="text-lg font-medium text-gray-900 mb-2">No features found</h3>
    <p class="text-gray-600">Try adjusting your filters or browse all available features.</p>
    <button 
      *ngIf="selectedCategory !== 'all'"
      (click)="onCategorySelect('all')"
      class="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors duration-200">
      Show All Features
    </button>
  </div>
</div>
