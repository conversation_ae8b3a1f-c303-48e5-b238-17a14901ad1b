<div class="tree-select-container" [ngClass]="containerClasses">
  <!-- Trigger button -->
  <div 
    class="tree-select-trigger flex items-center justify-between w-full border border-gray-300 rounded-md bg-white"
    [class.border-blue-500]="isDropdownOpen"
    [class.ring-1]="isDropdownOpen"
    [class.ring-blue-500]="isDropdownOpen"
    [class.cursor-not-allowed]="disabled || readonly"
    [class.opacity-50]="disabled"
    (click)="toggleDropdown()"
  >
    <div class="flex-1 min-w-0">
      <span 
        class="block truncate"
        [class.text-gray-500]="selectedItems.length === 0"
        [class.text-gray-900]="selectedItems.length > 0"
      >
        {{ displayText }}
      </span>
    </div>
    
    <!-- Selected count badge -->
    <div 
      *ngIf="showSelectedCount && selectedItems.length > 1"
      class="ml-2 px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full"
    >
      {{ selectedCount }}
    </div>
    
    <!-- Clear button -->
    <button 
      *ngIf="clearable && selectedItems.length > 0 && !disabled && !readonly"
      type="button"
      class="ml-2 p-1 text-gray-400 hover:text-gray-600 rounded-sm"
      (click)="clearAll(); $event.stopPropagation()"
    >
      <i class="fa fa-times text-sm"></i>
    </button>
    
    <!-- Dropdown arrow -->
    <div class="ml-2 flex items-center">
      <i 
        class="fa fa-chevron-down text-gray-400 transition-transform duration-200"
        [class.rotate-180]="isDropdownOpen"
      ></i>
    </div>
  </div>

  <!-- Dropdown content -->
  <div 
    *ngIf="isDropdownOpen"
    class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg"
    [style.max-height]="maxHeight"
    style="overflow-y: auto;"
  >
    <!-- Search input -->
    <div 
      *ngIf="searchable"
      class="p-3 border-b border-gray-200"
    >
      <div class="relative">
        <input
          type="text"
          [(ngModel)]="searchTerm"
          [placeholder]="searchPlaceholder"
          [disabled]="disabled"
          class="w-full pl-8 pr-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          (input)="onSearchInput($event)"
        />
        <i class="fa fa-search absolute left-2.5 top-2.5 text-gray-400 text-sm"></i>
        
        <!-- Clear search button -->
        <button
          *ngIf="searchTerm"
          type="button"
          class="absolute right-2 top-2 p-1 text-gray-400 hover:text-gray-600"
          (click)="clearSearch()"
        >
          <i class="fa fa-times text-sm"></i>
        </button>
      </div>
    </div>

    <!-- Action buttons -->
    <div 
      *ngIf="showSelectAllButton && multiSelect"
      class="p-2 border-b border-gray-200 flex gap-2"
    >
      <button
        type="button"
        class="px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded border border-blue-200"
        [disabled]="disabled || readonly"
        (click)="selectAll()"
      >
        {{ selectAllText }}
      </button>
      
      <button
        type="button"
        class="px-3 py-1 text-xs font-medium text-gray-600 bg-gray-50 hover:bg-gray-100 rounded border border-gray-200"
        [disabled]="disabled || readonly || selectedItems.length === 0"
        (click)="clearAll()"
      >
        {{ clearAllText }}
      </button>

      <button
        type="button"
        class="px-3 py-1 text-xs font-medium text-gray-600 bg-gray-50 hover:bg-gray-100 rounded border border-gray-200"
        (click)="expandAllItems()"
      >
        Expand All
      </button>

      <button
        type="button"
        class="px-3 py-1 text-xs font-medium text-gray-600 bg-gray-50 hover:bg-gray-100 rounded border border-gray-200"
        (click)="collapseAllItems()"
      >
        Collapse All
      </button>
    </div>

    <!-- Loading indicator -->
    <div 
      *ngIf="loading"
      class="p-4 text-center text-gray-500"
    >
      <i class="fa fa-spinner fa-spin mr-2"></i>
      {{ loadingText }}
    </div>

    <!-- Tree items -->
    <div 
      *ngIf="!loading"
      class="py-1"
    >
      <!-- No data message -->
      <div 
        *ngIf="filteredTreeData.length === 0 && !searchTerm"
        class="p-4 text-center text-gray-500"
      >
        {{ noDataText }}
      </div>

      <!-- No search results message -->
      <div 
        *ngIf="filteredTreeData.length === 0 && searchTerm"
        class="p-4 text-center text-gray-500"
      >
        {{ noResultsText }}
      </div>

      <!-- Tree items list -->
      <ng-container *ngFor="let item of filteredTreeData; trackBy: trackByItemId">
        <lib-tree-select-item
          [item]="item"
          [showCheckbox]="showCheckboxes"
          [multiSelect]="multiSelect"
          [highlightSearch]="searchTerm"
          [showDescription]="showItemDescription"
          [indentSize]="indentSize"
          [draggable]="draggable"
          [droppable]="droppable"
          [disabled]="disabled"
          [readonly]="readonly"
          (itemToggle)="onItemSelectionChange($event)"
          (itemExpand)="onItemExpand($event)"
          (itemCollapse)="onItemCollapse($event)"
          (itemClick)="onItemClick($event)"
          (itemDoubleClick)="onItemDoubleClick($event)"
          (itemDragStart)="dragStart.emit($event)"
          (itemDragOver)="dragOver.emit($event)"
          (itemDrop)="drop.emit($event)"
        ></lib-tree-select-item>
      </ng-container>
    </div>
  </div>

  <!-- Backdrop to close dropdown -->
  <div 
    *ngIf="isDropdownOpen"
    class="fixed inset-0 z-40"
    (click)="closeDropdown()"
  ></div>
</div>
