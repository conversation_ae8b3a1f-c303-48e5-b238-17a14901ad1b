/* Tree Select Component Styles */

.tree-select-container {
  position: relative;
  display: inline-block;
  width: 100%;
}

.tree-select-trigger {
  transition: all 0.2s ease-in-out;
  min-height: 2.5rem;
}

.tree-select-trigger:hover:not(.cursor-not-allowed) {
  border-color: #6b7280;
}

.tree-select-trigger:focus-within {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

/* Dropdown animations */
.tree-select-dropdown {
  animation: slideDown 0.2s ease-out;
  transform-origin: top;
}

@keyframes slideDown {
  0% {
    opacity: 0;
    transform: scaleY(0.95) translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: scaleY(1) translateY(0);
  }
}

/* Custom scrollbar for dropdown */
.tree-select-dropdown::-webkit-scrollbar {
  width: 6px;
}

.tree-select-dropdown::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.tree-select-dropdown::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.tree-select-dropdown::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Selected count badge animation */
.selected-count-badge {
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Search input focus styles */
.tree-select-search input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

/* Action buttons hover effects */
.tree-select-actions button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tree-select-actions button:active:not(:disabled) {
  transform: translateY(0);
}

/* Loading spinner animation */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Chevron rotation animation */
.chevron-icon {
  transition: transform 0.2s ease-in-out;
}

/* Clear button hover effect */
.clear-button:hover {
  background-color: #f3f4f6;
  border-radius: 4px;
}

/* Size variants */
.tree-select-xs {
  font-size: 0.75rem;
  min-height: 1.75rem;
}

.tree-select-sm {
  font-size: 0.875rem;
  min-height: 2rem;
}

.tree-select-md {
  font-size: 1rem;
  min-height: 2.5rem;
}

.tree-select-lg {
  font-size: 1.125rem;
  min-height: 3rem;
}

.tree-select-xl {
  font-size: 1.25rem;
  min-height: 3.5rem;
}

/* Disabled state */
.tree-select-disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.tree-select-disabled .tree-select-trigger {
  background-color: #f9fafb;
  color: #6b7280;
}

/* Readonly state */
.tree-select-readonly {
  cursor: default;
}

.tree-select-readonly .tree-select-trigger {
  background-color: #f9fafb;
}

/* Empty state styling */
.tree-select-empty {
  color: #6b7280;
  font-style: italic;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .tree-select-trigger {
    border-width: 2px;
  }
  
  .tree-select-trigger:focus-within {
    border-width: 3px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .tree-select-trigger,
  .chevron-icon,
  .tree-select-dropdown,
  .selected-count-badge,
  .tree-select-actions button {
    animation: none;
    transition: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .tree-select-trigger {
    background-color: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }
  
  .tree-select-dropdown {
    background-color: #374151;
    border-color: #4b5563;
  }
  
  .tree-select-disabled .tree-select-trigger {
    background-color: #1f2937;
    color: #9ca3af;
  }
  
  .tree-select-readonly .tree-select-trigger {
    background-color: #1f2937;
  }
}

/* Print styles */
@media print {
  .tree-select-dropdown {
    display: none;
  }
  
  .tree-select-trigger::after {
    content: " [" attr(data-selected-text) "]";
    color: #000;
    font-weight: normal;
  }
}