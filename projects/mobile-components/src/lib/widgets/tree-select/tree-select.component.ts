import { Component, Input, Output, EventEmitter, forwardRef, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { TreeSelectItemComponent, TreeSelectItemData } from '../tree-select-item/tree-select-item.component';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'lib-tree-select',
  templateUrl: './tree-select.component.html',
  styleUrls: ['./tree-select.component.css'],
  standalone: true,
  imports: [CommonModule, FormsModule, TreeSelectItemComponent],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => TreeSelectComponent),
      multi: true
    }
  ]
})
export class TreeSelectComponent implements ControlValueAccessor, OnInit, OnDestroy {
  @Input() treeData: TreeSelectItemData[] = [
    {
      id: 'folder1',
      label: 'Documents',
      icon: 'fa fa-folder',
      level: 0,
      hasChildren: true,
      expanded: false,
      children: [
        {
          id: 'file1',
          label: 'Report.pdf',
          icon: 'fa fa-file-pdf',
          level: 1,
          hasChildren: false,
          parentId: 'folder1'
        },
        {
          id: 'file2',
          label: 'Presentation.pptx',
          icon: 'fa fa-file-powerpoint',
          level: 1,
          hasChildren: false,
          parentId: 'folder1'
        }
      ]
    },
    {
      id: 'folder2',
      label: 'Images',
      icon: 'fa fa-folder',
      level: 0,
      hasChildren: true,
      expanded: false,
      children: [
        {
          id: 'subfolder1',
          label: 'Photos',
          icon: 'fa fa-folder',
          level: 1,
          hasChildren: true,
          expanded: false,
          parentId: 'folder2',
          children: [
            {
              id: 'image1',
              label: 'vacation.jpg',
              icon: 'fa fa-file-image',
              level: 2,
              hasChildren: false,
              parentId: 'subfolder1'
            }
          ]
        }
      ]
    }
  ];
  @Input() placeholder: string = 'Select items from tree...';
  @Input() searchable: boolean = true;
  @Input() searchPlaceholder: string = 'Search tree items...';
  @Input() multiSelect: boolean = true;
  @Input() showCheckboxes: boolean = true;
  @Input() expandOnSelect: boolean = false;
  @Input() selectParentsAutomatically: boolean = false;
  @Input() selectChildrenAutomatically: boolean = false;
  @Input() disabled: boolean = false;
  @Input() readonly: boolean = false;
  @Input() lazyLoading: boolean = false;
  @Input() maxHeight: string = '300px';
  @Input() showSelectedCount: boolean = true;
  @Input() selectedCountText: string = 'selected';
  @Input() clearable: boolean = true;
  @Input() expandAll: boolean = false;
  @Input() collapseAll: boolean = false;
  @Input() showSelectAllButton: boolean = true;
  @Input() selectAllText: string = 'Select All';
  @Input() clearAllText: string = 'Clear All';
  @Input() noDataText: string = 'No data available';
  @Input() noResultsText: string = 'No results found';
  @Input() loadingText: string = 'Loading...';
  @Input() indentSize: number = 20;
  @Input() debounceTime: number = 300;
  @Input() caseSensitiveSearch: boolean = false;
  @Input() searchInDescription: boolean = true;
  @Input() highlightSearch: boolean = true;
  @Input() autoExpandSearchResults: boolean = true;
  @Input() showItemDescription: boolean = false;
  @Input() draggable: boolean = false;
  @Input() droppable: boolean = false;
  
  // Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  @Output() selectionChange = new EventEmitter<TreeSelectItemData[]>();
  @Output() itemSelect = new EventEmitter<TreeSelectItemData>();
  @Output() itemDeselect = new EventEmitter<TreeSelectItemData>();
  @Output() itemExpand = new EventEmitter<TreeSelectItemData>();
  @Output() itemCollapse = new EventEmitter<TreeSelectItemData>();
  @Output() itemClick = new EventEmitter<TreeSelectItemData>();
  @Output() itemDoubleClick = new EventEmitter<TreeSelectItemData>();
  @Output() searchChange = new EventEmitter<string>();
  @Output() lazyLoad = new EventEmitter<TreeSelectItemData>();
  @Output() dragStart = new EventEmitter<{ item: TreeSelectItemData, event: DragEvent }>();
  @Output() dragOver = new EventEmitter<{ item: TreeSelectItemData, event: DragEvent }>();
  @Output() drop = new EventEmitter<{ item: TreeSelectItemData, event: DragEvent }>();

  searchTerm: string = '';
  isDropdownOpen: boolean = false;
  filteredTreeData: TreeSelectItemData[] = [];
  flatTreeData: TreeSelectItemData[] = [];
  selectedItems: TreeSelectItemData[] = [];
  loading: boolean = false;

  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();
  private onChange = (value: any[]) => {};
  private onTouched = () => {};

  constructor() {}

  ngOnInit(): void {
    this.initializeTree();
    this.setupSearch();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // ControlValueAccessor implementation
  writeValue(value: any[]): void {
    if (value && value.length > 0) {
      this.selectedItems = this.getItemsByValues(value);
      this.updateSelectionInTree();
    } else {
      this.selectedItems = [];
      this.clearAllSelections();
    }
  }

  registerOnChange(fn: (value: any[]) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  // Tree management methods
  initializeTree(): void {
    this.flatTreeData = this.flattenTree(this.treeData);
    this.filteredTreeData = [...this.treeData];
    
    if (this.expandAll) {
      this.expandAllItems();
    }
  }

  setupSearch(): void {
    this.searchSubject.pipe(
      debounceTime(this.debounceTime),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(searchTerm => {
      this.performSearch(searchTerm);
      this.searchChange.emit(searchTerm);
    });
  }

  flattenTree(items: TreeSelectItemData[], level: number = 0): TreeSelectItemData[] {
    let result: TreeSelectItemData[] = [];
    
    for (const item of items) {
      const flatItem = { ...item, level };
      result.push(flatItem);
      
      if (item.children && item.children.length > 0) {
        result = result.concat(this.flattenTree(item.children, level + 1));
      }
    }
    
    return result;
  }

  getItemsByValues(values: any[]): TreeSelectItemData[] {
    return this.flatTreeData.filter(item => 
      values.some(value => 
        (typeof value === 'string' && value === item.id) ||
        (typeof value === 'object' && value.id === item.id)
      )
    );
  }

  updateSelectionInTree(): void {
    this.flatTreeData.forEach(item => {
      item.selected = this.selectedItems.some(selected => selected.id === item.id);
    });
  }

  clearAllSelections(): void {
    this.flatTreeData.forEach(item => {
      item.selected = false;
    });
  }

  performSearch(searchTerm: string): void {
    if (!searchTerm.trim()) {
      this.filteredTreeData = [...this.treeData];
      return;
    }

    const term = this.caseSensitiveSearch ? searchTerm : searchTerm.toLowerCase();
    const matchingItems = this.flatTreeData.filter(item => {
      const label = this.caseSensitiveSearch ? item.label : item.label.toLowerCase();
      const description = this.caseSensitiveSearch ? (item.description || '') : (item.description || '').toLowerCase();
      
      return label.includes(term) || (this.searchInDescription && description.includes(term));
    });

    if (this.autoExpandSearchResults) {
      this.expandParentsOfItems(matchingItems);
    }

    this.filteredTreeData = this.buildFilteredTree(matchingItems);
  }

  expandParentsOfItems(items: TreeSelectItemData[]): void {
    items.forEach(item => {
      let currentItem = item;
      while (currentItem.parentId) {
        const parent = this.flatTreeData.find(p => p.id === currentItem.parentId);
        if (parent) {
          parent.expanded = true;
          currentItem = parent;
        } else {
          break;
        }
      }
    });
  }

  buildFilteredTree(matchingItems: TreeSelectItemData[]): TreeSelectItemData[] {
    const rootItems: TreeSelectItemData[] = [];
    const itemMap = new Map<string, TreeSelectItemData>();

    // Create a map of all items that should be included (matching items + their ancestors)
    const includeSet = new Set<string>();
    
    matchingItems.forEach(item => {
      includeSet.add(String(item.id));
      // Add all ancestors
      let currentItem = item;
      while (currentItem.parentId) {
        includeSet.add(String(currentItem.parentId));
        currentItem = this.flatTreeData.find(p => p.id === currentItem.parentId) || currentItem;
        if (!currentItem.parentId) break;
      }
    });

    // Build the filtered tree
    this.treeData.forEach(item => {
      const filteredItem = this.buildFilteredSubtree(item, includeSet);
      if (filteredItem) {
        rootItems.push(filteredItem);
      }
    });

    return rootItems;
  }

  buildFilteredSubtree(item: TreeSelectItemData, includeSet: Set<string>): TreeSelectItemData | null {
    if (!includeSet.has(String(item.id))) {
      return null;
    }

    const filteredItem = { ...item };
    
    if (item.children) {
      filteredItem.children = [];
      item.children.forEach(child => {
        const filteredChild = this.buildFilteredSubtree(child, includeSet);
        if (filteredChild) {
          filteredItem.children!.push(filteredChild);
        }
      });
    }

    return filteredItem;
  }

  // Event handlers
  toggleDropdown(): void {
    if (this.disabled || this.readonly) return;
    
    this.isDropdownOpen = !this.isDropdownOpen;
    this.onTouched();
  }

  closeDropdown(): void {
    this.isDropdownOpen = false;
  }

  onSearchInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchTerm = target.value;
    this.searchSubject.next(this.searchTerm);
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.onSearchInput({ target: { value: '' } } as any);
  }

  onItemSelectionChange(item: TreeSelectItemData): void {
    if (this.disabled || this.readonly) return;

    const isCurrentlySelected = this.selectedItems.some(selected => selected.id === item.id);
    
    if (isCurrentlySelected) {
      this.deselectItem(item);
    } else {
      this.selectItem(item);
    }
  }

  selectItem(item: TreeSelectItemData): void {
    if (!this.multiSelect) {
      this.selectedItems = [item];
    } else {
      if (!this.selectedItems.some(selected => selected.id === item.id)) {
        this.selectedItems.push(item);
      }
    }

    item.selected = true;

    if (this.selectChildrenAutomatically && item.children) {
      this.selectChildren(item);
    }

    if (this.selectParentsAutomatically && item.parentId) {
      this.selectParentsIfAllChildrenSelected(item);
    }

    if (this.expandOnSelect && item.hasChildren) {
      item.expanded = true;
    }

    this.emitSelectionChange();
    this.itemSelect.emit(item);
  }

  deselectItem(item: TreeSelectItemData): void {
    this.selectedItems = this.selectedItems.filter(selected => selected.id !== item.id);
    item.selected = false;

    if (this.selectChildrenAutomatically && item.children) {
      this.deselectChildren(item);
    }

    if (this.selectParentsAutomatically && item.parentId) {
      this.deselectParents(item);
    }

    this.emitSelectionChange();
    this.itemDeselect.emit(item);
  }

  selectChildren(parent: TreeSelectItemData): void {
    if (parent.children) {
      parent.children.forEach(child => {
        if (!child.selected) {
          child.selected = true;
          if (!this.selectedItems.some(selected => selected.id === child.id)) {
            this.selectedItems.push(child);
          }
        }
        if (child.children) {
          this.selectChildren(child);
        }
      });
    }
  }

  deselectChildren(parent: TreeSelectItemData): void {
    if (parent.children) {
      parent.children.forEach(child => {
        if (child.selected) {
          child.selected = false;
          this.selectedItems = this.selectedItems.filter(selected => selected.id !== child.id);
        }
        if (child.children) {
          this.deselectChildren(child);
        }
      });
    }
  }

  selectParentsIfAllChildrenSelected(item: TreeSelectItemData): void {
    if (!item.parentId) return;

    const parent = this.flatTreeData.find(p => p.id === item.parentId);
    if (!parent || !parent.children) return;

    const allChildrenSelected = parent.children.every(child => child.selected);
    
    if (allChildrenSelected && !parent.selected) {
      parent.selected = true;
      if (!this.selectedItems.some(selected => selected.id === parent.id)) {
        this.selectedItems.push(parent);
      }
      this.selectParentsIfAllChildrenSelected(parent);
    }
  }

  deselectParents(item: TreeSelectItemData): void {
    if (!item.parentId) return;

    const parent = this.flatTreeData.find(p => p.id === item.parentId);
    if (!parent) return;

    if (parent.selected) {
      parent.selected = false;
      this.selectedItems = this.selectedItems.filter(selected => selected.id !== parent.id);
      this.deselectParents(parent);
    }
  }

  onItemExpand(item: TreeSelectItemData): void {
    item.expanded = true;
    
    if (this.lazyLoading && item.children?.length === 0) {
      this.loading = true;
      this.lazyLoad.emit(item);
    }
    
    this.itemExpand.emit(item);
  }

  onItemCollapse(item: TreeSelectItemData): void {
    item.expanded = false;
    this.itemCollapse.emit(item);
  }

  onItemClick(item: TreeSelectItemData): void {
    this.itemClick.emit(item);
  }

  onItemDoubleClick(item: TreeSelectItemData): void {
    this.itemDoubleClick.emit(item);
  }

  // Utility methods
  selectAll(): void {
    if (this.disabled || this.readonly) return;

    this.selectedItems = [...this.flatTreeData];
    this.flatTreeData.forEach(item => {
      item.selected = true;
    });
    
    this.emitSelectionChange();
  }

  clearAll(): void {
    if (this.disabled || this.readonly) return;

    this.selectedItems = [];
    this.flatTreeData.forEach(item => {
      item.selected = false;
    });
    
    this.emitSelectionChange();
  }

  expandAllItems(): void {
    this.flatTreeData.forEach(item => {
      if (item.hasChildren) {
        item.expanded = true;
      }
    });
  }

  collapseAllItems(): void {
    this.flatTreeData.forEach(item => {
      item.expanded = false;
    });
  }

  private emitSelectionChange(): void {
    const selectedValues = this.selectedItems.map(item => item.id);
    this.onChange(selectedValues);
    this.selectionChange.emit([...this.selectedItems]);
  }

  // Getters for template
  get selectedCount(): number {
    return this.selectedItems.length;
  }

  get displayText(): string {
    if (this.selectedItems.length === 0) {
      return this.placeholder;
    }
    
    if (this.selectedItems.length === 1) {
      return this.selectedItems[0].label;
    }
    
    return `${this.selectedItems.length} ${this.selectedCountText}`;
  }

  get sizeClasses(): string {
    const sizes = {
      xs: 'text-xs px-2 py-1',
      sm: 'text-sm px-3 py-1.5',
      md: 'text-base px-4 py-2',
      lg: 'text-lg px-5 py-2.5',
      xl: 'text-xl px-6 py-3'
    };
    return sizes[this.size];
  }

  get variantClasses(): string {
    const variants = {
      default: 'border-gray-300 bg-white text-gray-900 focus:border-blue-500 focus:ring-blue-500',
      primary: 'border-blue-300 bg-blue-50 text-blue-900 focus:border-blue-500 focus:ring-blue-500',
      secondary: 'border-gray-300 bg-gray-50 text-gray-900 focus:border-gray-500 focus:ring-gray-500',
      success: 'border-green-300 bg-green-50 text-green-900 focus:border-green-500 focus:ring-green-500',
      warning: 'border-yellow-300 bg-yellow-50 text-yellow-900 focus:border-yellow-500 focus:ring-yellow-500',
      danger: 'border-red-300 bg-red-50 text-red-900 focus:border-red-500 focus:ring-red-500'
    };
    return variants[this.variant];
  }

  get roundedClasses(): string {
    const rounded = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };
    return rounded[this.rounded];
  }

  get containerClasses(): string {
    return [
      'relative w-full',
      this.sizeClasses,
      this.variantClasses,
      this.roundedClasses,
      this.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer',
      this.className
    ].filter(Boolean).join(' ');
  }

  // Track by function for ngFor
  trackByItemId(index: number, item: TreeSelectItemData): string | number {
    return item.id;
  }
}
