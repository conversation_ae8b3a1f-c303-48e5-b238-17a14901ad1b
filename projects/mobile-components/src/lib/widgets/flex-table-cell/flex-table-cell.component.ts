import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'lib-flex-table-cell',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './flex-table-cell.component.html',
  styleUrl: './flex-table-cell.component.css'
})
export class FlexTableCellComponent {
  @Input() type: 'grow' | 'shrink' | 'stable' | 'auto' = 'auto';
  @Input() value: any = 'Sample Data';
  @Input() editable: boolean = false;
  @Input() sortable: boolean = false;
  @Input() align: 'left' | 'center' | 'right' = 'left';
  @Input() width: string = '';
  @Input() minWidth: string = '';
  @Input() maxWidth: string = '';
  @Input() truncate: boolean = false;
  @Input() wrap: boolean = true;
  @Input() clickable: boolean = false;
  @Input() selected: boolean = false;
  @Input() highlighted: boolean = false;
  @Input() disabled: boolean = false;
  
  // Content type specific inputs
  @Input() dataType: 'text' | 'number' | 'date' | 'boolean' | 'image' | 'badge' | 'action' | 'custom' = 'text';
  @Input() formatter: ((value: any) => string) | null = null;
  @Input() prefix: string = '';
  @Input() suffix: string = '';
  @Input() placeholder: string = 'Enter value...';
  
  // Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'subtle' | 'bordered' | 'highlighted' | 'warning' | 'error' | 'success' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'none';
  @Input() padding: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  
  @Output() cellClick = new EventEmitter<any>();
  @Output() valueChange = new EventEmitter<any>();
  @Output() editStart = new EventEmitter<void>();
  @Output() editEnd = new EventEmitter<any>();
  @Output() actionClick = new EventEmitter<string>();

  isEditing: boolean = false;
  editValue: any;

  get cellClasses(): string {
    const baseClasses = [
      'flex-table-cell',
      'relative',
      'transition-colors',
      'duration-200'
    ];

    // Flex behavior classes
    const flexClasses = {
      grow: ['flex-1', 'min-w-0'],
      shrink: ['flex-shrink'],
      stable: ['flex-shrink-0'],
      auto: ['flex-auto']
    };
    baseClasses.push(...flexClasses[this.type]);

    // Size classes (padding)
    const paddingClasses = {
      none: [],
      xs: ['p-1'],
      sm: ['p-2'],
      md: ['p-3'],
      lg: ['p-4'],
      xl: ['p-6']
    };
    baseClasses.push(...paddingClasses[this.padding]);

    // Text size
    const textSizeClasses = {
      xs: ['text-xs'],
      sm: ['text-sm'],
      md: ['text-sm'],
      lg: ['text-base'],
      xl: ['text-lg']
    };
    baseClasses.push(...textSizeClasses[this.size]);

    // Alignment
    const alignClasses = {
      left: ['text-left'],
      center: ['text-center'],
      right: ['text-right']
    };
    baseClasses.push(...alignClasses[this.align]);

    // Variant styling
    const variantClasses = {
      default: ['bg-white', 'text-gray-900'],
      subtle: ['bg-gray-50', 'text-gray-700'],
      bordered: ['bg-white', 'text-gray-900', 'border', 'border-gray-200'],
      highlighted: ['bg-blue-50', 'text-blue-900'],
      warning: ['bg-yellow-50', 'text-yellow-800'],
      error: ['bg-red-50', 'text-red-800'],
      success: ['bg-green-50', 'text-green-800']
    };
    baseClasses.push(...variantClasses[this.variant]);

    // State classes
    if (this.selected) {
      baseClasses.push('bg-blue-100', 'border-blue-300');
    }

    if (this.highlighted) {
      baseClasses.push('ring-2', 'ring-blue-500', 'ring-opacity-50');
    }

    if (this.clickable && !this.disabled) {
      baseClasses.push('cursor-pointer', 'hover:bg-gray-50');
    }

    if (this.disabled) {
      baseClasses.push('opacity-50', 'cursor-not-allowed');
    }

    // Text handling
    if (this.truncate) {
      baseClasses.push('truncate');
    } else if (this.wrap) {
      baseClasses.push('break-words');
    } else {
      baseClasses.push('whitespace-nowrap');
    }

    // Rounded corners
    const roundedClasses = {
      none: [],
      sm: ['rounded-sm'],
      md: ['rounded-md'],
      lg: ['rounded-lg'],
      full: ['rounded-full']
    };
    baseClasses.push(...roundedClasses[this.rounded]);

    if (this.className) {
      baseClasses.push(this.className);
    }

    return baseClasses.join(' ');
  }

  get contentClasses(): string {
    const baseClasses = [
      'flex',
      'items-center',
      'min-h-0'
    ];

    // Alignment for flex content
    const justifyClasses = {
      left: ['justify-start'],
      center: ['justify-center'],
      right: ['justify-end']
    };
    baseClasses.push(...justifyClasses[this.align]);

    if (this.type === 'grow') {
      baseClasses.push('w-full');
    }

    return baseClasses.join(' ');
  }

  get computedStyle(): {[key: string]: string} {
    const styles: {[key: string]: string} = {};
    
    if (this.width) {
      styles['width'] = this.width;
    }
    
    if (this.minWidth) {
      styles['min-width'] = this.minWidth;
    }
    
    if (this.maxWidth) {
      styles['max-width'] = this.maxWidth;
    }
    
    return styles;
  }

  onCellClick(): void {
    if (this.disabled) return;
    
    if (this.clickable) {
      this.cellClick.emit(this.value);
    }
    
    if (this.editable && !this.isEditing) {
      this.startEdit();
    }
  }

  startEdit(): void {
    if (this.disabled || !this.editable) return;
    
    this.isEditing = true;
    this.editValue = this.value;
    this.editStart.emit();
  }

  cancelEdit(): void {
    this.isEditing = false;
    this.editValue = this.value;
  }

  saveEdit(): void {
    this.isEditing = false;
    this.value = this.editValue;
    this.valueChange.emit(this.editValue);
    this.editEnd.emit(this.editValue);
  }

  onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      this.saveEdit();
    } else if (event.key === 'Escape') {
      this.cancelEdit();
    }
  }

  onActionClick(action: string, event?: Event): void {
    if (event) {
      event.stopPropagation();
    }
    this.actionClick.emit(action);
  }

  getFormattedValue(): string {
    if (this.formatter) {
      return this.formatter(this.value);
    }

    if (this.value === null || this.value === undefined) {
      return '';
    }

    switch (this.dataType) {
      case 'number':
        return typeof this.value === 'number' ? this.value.toLocaleString() : String(this.value);
      case 'date':
        try {
          const date = new Date(this.value);
          return date.toLocaleDateString();
        } catch {
          return String(this.value);
        }
      case 'boolean':
        return this.value ? 'Yes' : 'No';
      default:
        return String(this.value);
    }
  }

  getBadgeClasses(): string {
    const badgeClasses = [
      'inline-flex',
      'items-center',
      'px-2.5',
      'py-0.5',
      'rounded-full',
      'text-xs',
      'font-medium'
    ];

    // Badge color based on value
    const value = String(this.value).toLowerCase();
    if (value.includes('active') || value.includes('success') || value.includes('completed')) {
      badgeClasses.push('bg-green-100', 'text-green-800');
    } else if (value.includes('warning') || value.includes('pending') || value.includes('draft')) {
      badgeClasses.push('bg-yellow-100', 'text-yellow-800');
    } else if (value.includes('error') || value.includes('failed') || value.includes('inactive')) {
      badgeClasses.push('bg-red-100', 'text-red-800');
    } else {
      badgeClasses.push('bg-gray-100', 'text-gray-800');
    }

    return badgeClasses.join(' ');
  }

  trackByFn(index: number, item: any): any {
    return item.id || index;
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    img.style.display = 'none';
  }
}
