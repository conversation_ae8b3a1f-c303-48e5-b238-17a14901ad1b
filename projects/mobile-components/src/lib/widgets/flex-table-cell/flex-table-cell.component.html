<div 
  [class]="cellClasses"
  [ngStyle]="computedStyle"
  (click)="onCellClick()"
  [attr.role]="clickable ? 'button' : null"
  [attr.tabindex]="clickable && !disabled ? 0 : null"
>
  <div [class]="contentClasses">
    <!-- Prefix -->
    <span *ngIf="prefix" class="text-gray-500 mr-1">{{ prefix }}</span>
    
    <!-- Editable Input -->
    <div *ngIf="isEditing && editable" class="flex-1 min-w-0">
      <input
        type="text"
        [(ngModel)]="editValue"
        [placeholder]="placeholder"
        (keydown)="onKeyDown($event)"
        (blur)="saveEdit()"
        class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
        #editInput
      >
    </div>
    
    <!-- Content Display -->
    <div *ngIf="!isEditing" class="flex items-center space-x-2 min-w-0 flex-1">
      <!-- Text Content -->
      <span *ngIf="dataType === 'text'" class="min-w-0">
        {{ getFormattedValue() }}
      </span>
      
      <!-- Number Content -->
      <span *ngIf="dataType === 'number'" class="font-mono">
        {{ getFormattedValue() }}
      </span>
      
      <!-- Date Content -->
      <span *ngIf="dataType === 'date'" class="text-gray-700">
        {{ getFormattedValue() }}
      </span>
      
      <!-- Boolean Content -->
      <div *ngIf="dataType === 'boolean'" class="flex items-center">
        <div class="flex items-center">
          <div 
            class="w-4 h-4 rounded-full border-2"
            [class.bg-green-500]="value"
            [class.border-green-500]="value"
            [class.bg-gray-300]="!value"
            [class.border-gray-300]="!value"
          >
            <svg *ngIf="value" class="w-3 h-3 text-white ml-0.5 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <span class="ml-2 text-sm">{{ getFormattedValue() }}</span>
        </div>
      </div>
      
      <!-- Image Content -->
      <div *ngIf="dataType === 'image'" class="flex items-center">
        <img 
          *ngIf="value" 
          [src]="value" 
          [alt]="'Image'"
          class="h-8 w-8 rounded-full object-cover"
(error)="onImageError($event)"
        >
        <div 
          *ngIf="!value"
          class="h-8 w-8 bg-gray-200 rounded-full flex items-center justify-center"
        >
          <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
        </div>
      </div>
      
      <!-- Badge Content -->
      <span *ngIf="dataType === 'badge'" [class]="getBadgeClasses()">
        {{ getFormattedValue() }}
      </span>
      
      <!-- Action Content -->
      <div *ngIf="dataType === 'action'" class="flex items-center space-x-1">
        <button
          *ngFor="let action of value; trackBy: trackByFn"
          (click)="onActionClick(action.id || action.name, $event)"
          [disabled]="action.disabled || disabled"
          class="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          [class.text-red-700]="action.variant === 'danger'"
          [class.border-red-300]="action.variant === 'danger'"
          [class.hover:bg-red-50]="action.variant === 'danger'"
        >
          <svg *ngIf="action.icon" class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="action.icon"></path>
          </svg>
          {{ action.label || action.name }}
        </button>
      </div>
      
      <!-- Custom Content -->
      <div *ngIf="dataType === 'custom'" class="w-full">
        <ng-content></ng-content>
      </div>
    </div>
    
    <!-- Suffix -->
    <span *ngIf="suffix" class="text-gray-500 ml-1">{{ suffix }}</span>
    
    <!-- Edit Button -->
    <button
      *ngIf="editable && !isEditing && !disabled"
      (click)="startEdit(); $event.stopPropagation()"
      class="ml-2 opacity-0 group-hover:opacity-100 text-gray-400 hover:text-gray-600 focus:opacity-100 focus:outline-none"
      aria-label="Edit cell"
    >
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
      </svg>
    </button>
    
    <!-- Sort Indicator -->
    <div *ngIf="sortable" class="ml-1 flex flex-col">
      <svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 10l5 5 5-5z"></path>
      </svg>
    </div>
  </div>
  
  <!-- Selected Indicator -->
  <div 
    *ngIf="selected" 
    class="absolute inset-y-0 left-0 w-1 bg-blue-500"
  ></div>
</div>
