import { Component, Input, Output, EventEmitter, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'lib-modal-small-tier',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './modal-small-tier.component.html',
  styleUrl: './modal-small-tier.component.css'
})
export class ModalSmallTierComponent {
  // Standard inputs for customization
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'sm';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() isOpen: boolean = false;
  @Input() title: string = 'Confirm Action';
  @Input() message: string = 'Are you sure you want to proceed?';
  @Input() showHeader: boolean = true;
  @Input() showFooter: boolean = true;
  @Input() showCloseButton: boolean = true;
  @Input() closableOnBackdrop: boolean = true;
  @Input() closableOnEscape: boolean = true;
  @Input() primaryButtonLabel: string = 'Confirm';
  @Input() secondaryButtonLabel: string = 'Cancel';
  @Input() showPrimaryButton: boolean = true;
  @Input() showSecondaryButton: boolean = true;
  @Input() primaryButtonDisabled: boolean = false;
  @Input() loading: boolean = false;
  @Input() icon: string = 'fas fa-question-circle';
  @Input() iconColor: string = 'text-blue-600';

  // Event outputs
  @Output() close = new EventEmitter<void>();
  @Output() primaryAction = new EventEmitter<void>();
  @Output() secondaryAction = new EventEmitter<void>();
  @Output() backdropClick = new EventEmitter<void>();

  @HostListener('document:keydown.escape', ['$event'])
  onEscapeKey(event: KeyboardEvent): void {
    if (this.isOpen && this.closableOnEscape) {
      this.onClose();
    }
  }

  // Computed getter for modal CSS classes
  get modalClasses(): string {
    const baseClasses = 'modal-small-tier-widget';
    
    const sizeClasses = {
      xs: 'max-w-xs',
      sm: 'max-w-sm',
      md: 'max-w-md',
      lg: 'max-w-lg',
      xl: 'max-w-xl'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-xl'
    };
    
    return [
      baseClasses,
      sizeClasses[this.size],
      roundedClasses[this.rounded],
      'bg-white shadow-xl',
      this.className
    ].filter(Boolean).join(' ');
  }

  get primaryButtonClasses(): string {
    const variantClasses = {
      default: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',
      primary: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',
      secondary: 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500',
      success: 'bg-green-600 hover:bg-green-700 focus:ring-green-500',
      warning: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500',
      danger: 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
    };

    const disabledClass = this.primaryButtonDisabled ? 'opacity-50 cursor-not-allowed' : '';
    
    return `inline-flex justify-center px-4 py-2 text-sm font-medium text-white border border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors ${variantClasses[this.variant]} ${disabledClass}`;
  }

  onClose(): void {
    this.close.emit();
  }

  onPrimaryAction(): void {
    if (!this.primaryButtonDisabled && !this.loading) {
      this.primaryAction.emit();
    }
  }

  onSecondaryAction(): void {
    if (!this.loading) {
      this.secondaryAction.emit();
    }
  }

  onBackdropClick(): void {
    this.backdropClick.emit();
    if (this.closableOnBackdrop) {
      this.onClose();
    }
  }

  onModalClick(event: Event): void {
    event.stopPropagation();
  }
}
