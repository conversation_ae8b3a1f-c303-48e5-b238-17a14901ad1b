<!-- Modal Small Tier Widget -->
<div *ngIf="isOpen" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
  <!-- Backdrop -->
  <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
    <div 
      class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" 
      aria-hidden="true"
      (click)="onBackdropClick()"></div>

    <!-- Center modal vertically -->
    <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

    <!-- Modal panel -->
    <div 
      [class]="modalClasses"
      class="inline-block align-bottom text-left overflow-hidden transform transition-all sm:my-8 sm:align-middle w-full"
      (click)="onModalClick($event)">
      
      <!-- Loading overlay -->
      <div *ngIf="loading" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
        <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
      </div>

      <!-- Content -->
      <div class="px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
        <div class="sm:flex sm:items-start">
          <!-- Icon -->
          <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-gray-100 sm:mx-0 sm:h-10 sm:w-10">
            <i [class]="icon + ' ' + iconColor + ' text-xl'" aria-hidden="true"></i>
          </div>
          
          <!-- Content -->
          <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
            <h3 *ngIf="showHeader" class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
              {{ title }}
            </h3>
            <div class="mt-2">
              <p class="text-sm text-gray-500">
                {{ message }}
              </p>
              
              <!-- Content projection -->
              <ng-content select="[slot=content]"></ng-content>
            </div>
          </div>

          <!-- Close button -->
          <button 
            *ngIf="showCloseButton"
            type="button"
            (click)="onClose()"
            class="absolute top-3 right-3 bg-white rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            aria-label="Close modal">
            <span class="sr-only">Close</span>
            <i class="fas fa-times" aria-hidden="true"></i>
          </button>
        </div>
      </div>

      <!-- Footer -->
      <div *ngIf="showFooter" class="px-4 py-3 bg-gray-50 border-t border-gray-200 sm:px-6 sm:flex sm:flex-row-reverse">
        <!-- Primary button -->
        <button 
          *ngIf="showPrimaryButton"
          type="button"
          (click)="onPrimaryAction()"
          [disabled]="primaryButtonDisabled || loading"
          [class]="primaryButtonClasses"
          class="w-full sm:ml-3 sm:w-auto">
          <span *ngIf="loading" class="mr-2">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white inline-block"></div>
          </span>
          {{ primaryButtonLabel }}
        </button>
        
        <!-- Secondary button -->
        <button 
          *ngIf="showSecondaryButton"
          type="button"
          (click)="onSecondaryAction()"
          [disabled]="loading"
          class="mt-3 w-full inline-flex justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors sm:mt-0 sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed">
          {{ secondaryButtonLabel }}
        </button>
      </div>
    </div>
  </div>
</div>
