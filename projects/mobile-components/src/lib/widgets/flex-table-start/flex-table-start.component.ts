import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'lib-flex-table-start',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './flex-table-start.component.html',
  styleUrl: './flex-table-start.component.css'
})
export class FlexTableStartComponent {
  // Standard inputs for customization
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() title: string = 'Data Table';
  @Input() description: string = 'Manage and view data in an organized table format';
  @Input() showHeader: boolean = true;
  @Input() showDescription: boolean = true;
  @Input() showActions: boolean = true;
  @Input() actionLabel: string = 'Add New';
  @Input() headerBg: string = '';
  @Input() bordered: boolean = true;

  // Computed getter for CSS classes
  get computedClasses(): string {
    const baseClasses = 'flex-table-start-widget';
    
    const sizeClasses = {
      xs: 'text-xs p-2',
      sm: 'text-sm p-3',
      md: 'text-base p-4',
      lg: 'text-lg p-5',
      xl: 'text-xl p-6'
    };

    const variantClasses = {
      default: 'bg-white border-gray-200',
      primary: 'bg-blue-50 border-blue-200',
      secondary: 'bg-gray-50 border-gray-300',
      success: 'bg-green-50 border-green-200',
      warning: 'bg-yellow-50 border-yellow-200',
      danger: 'bg-red-50 border-red-200'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const borderClass = this.bordered ? 'border' : '';
    
    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      borderClass,
      this.headerBg || '',
      this.className
    ].filter(Boolean).join(' ');
  }

  // Computed getter for header classes
  get headerClasses(): string {
    const variantHeaderClasses = {
      default: 'bg-gray-50 border-gray-200',
      primary: 'bg-blue-100 border-blue-300',
      secondary: 'bg-gray-100 border-gray-300',
      success: 'bg-green-100 border-green-300',
      warning: 'bg-yellow-100 border-yellow-300',
      danger: 'bg-red-100 border-red-300'
    };

    return `flex justify-between items-center p-4 border-b ${variantHeaderClasses[this.variant]}`;
  }

  // Computed getter for action button classes
  get actionButtonClasses(): string {
    const variantButtonClasses = {
      default: 'bg-blue-600 hover:bg-blue-700 text-white',
      primary: 'bg-blue-600 hover:bg-blue-700 text-white',
      secondary: 'bg-gray-600 hover:bg-gray-700 text-white',
      success: 'bg-green-600 hover:bg-green-700 text-white',
      warning: 'bg-yellow-600 hover:bg-yellow-700 text-white',
      danger: 'bg-red-600 hover:bg-red-700 text-white'
    };

    return `px-4 py-2 rounded-md font-medium transition-colors ${variantButtonClasses[this.variant]}`;
  }
}
