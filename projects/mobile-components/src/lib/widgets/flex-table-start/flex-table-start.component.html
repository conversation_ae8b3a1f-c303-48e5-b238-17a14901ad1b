<!-- Flex Table Start Widget -->
<div [class]="computedClasses">
  <!-- Header Section -->
  <div *ngIf="showHeader" [class]="headerClasses">
    <div class="flex-1">
      <h3 class="text-lg font-semibold text-gray-900">{{ title }}</h3>
      <p *ngIf="showDescription && description" class="mt-1 text-sm text-gray-600">
        {{ description }}
      </p>
    </div>
    
    <!-- Action Button -->
    <div *ngIf="showActions" class="flex-shrink-0 ml-4">
      <button 
        type="button"
        [class]="actionButtonClasses"
        aria-label="Add new item">
        <i class="fas fa-plus mr-2" aria-hidden="true"></i>
        {{ actionLabel }}
      </button>
    </div>
  </div>

  <!-- Table Container -->
  <div class="overflow-hidden">
    <!-- Table placeholder area -->
    <div class="p-6 text-center bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg">
      <div class="flex flex-col items-center">
        <i class="fas fa-table text-4xl text-gray-400 mb-4" aria-hidden="true"></i>
        <h4 class="text-lg font-medium text-gray-900 mb-2">Table Content Area</h4>
        <p class="text-sm text-gray-600 mb-4">
          This is where your table rows and data will be displayed. 
          Connect this component with table rows and cells.
        </p>
        <div class="flex space-x-2 text-xs text-gray-500">
          <span class="px-2 py-1 bg-gray-200 rounded">Rows</span>
          <span class="px-2 py-1 bg-gray-200 rounded">Columns</span>
          <span class="px-2 py-1 bg-gray-200 rounded">Data</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Section (optional) -->
  <div class="px-4 py-3 bg-gray-50 border-t border-gray-200 flex justify-between items-center text-sm text-gray-700">
    <span>Ready for table content</span>
    <span class="text-xs text-gray-500">Flex Table Start Component</span>
  </div>
</div>
