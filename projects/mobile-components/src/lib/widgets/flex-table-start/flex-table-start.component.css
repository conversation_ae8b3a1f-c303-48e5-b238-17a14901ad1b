/* Flex Table Start Widget Styles */
.flex-table-start-widget {
  position: relative;
  background: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* Enhanced hover effects */
.flex-table-start-widget:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease-in-out;
}

/* Action button hover animation */
.flex-table-start-widget button {
  transition: all 0.2s ease-in-out;
}

.flex-table-start-widget button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Focus states for accessibility */
.flex-table-start-widget button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Table placeholder animation */
.flex-table-start-widget .border-dashed {
  animation: pulse-border 2s infinite;
}

@keyframes pulse-border {
  0%, 100% {
    border-color: #d1d5db;
  }
  50% {
    border-color: #9ca3af;
  }
}

/* Icon animation on hover */
.flex-table-start-widget .fa-table {
  transition: transform 0.3s ease-in-out;
}

.flex-table-start-widget:hover .fa-table {
  transform: scale(1.1);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .flex-table-start-widget .flex.justify-between {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .flex-table-start-widget .flex-shrink-0 {
    width: 100%;
  }
  
  .flex-table-start-widget button {
    width: 100%;
    justify-content: center;
  }
}