import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import ALL widget components
// Standalone (43)
import { CompanyOverviewComponent } from './company-overview/company-overview.component';
import { ButtonGroupComponent } from './button-group/button-group.component';
import { FlexTableStartComponent } from './flex-table-start/flex-table-start.component';
import { ImageGalleryComponent } from './image-gallery/image-gallery.component';
import { FilterComponent } from './filter/filter.component';
import { TabbedContentComponent } from './tabbed-content/tabbed-content.component';
import { IconTextComponent } from './icon-text/icon-text.component';
import { FlexTableCellComponent } from './flex-table-cell/flex-table-cell.component';
import { IconBoxComponent } from './icon-box/icon-box.component';
import { SearchCompactComponent } from './search-compact/search-compact.component';
import { ModalMediumTierComponent } from './modal-medium-tier/modal-medium-tier.component';
import { AuthorsListCompactComponent } from './authors-list-compact/authors-list-compact.component';
import { AvatarGroupComponent } from './avatar-group/avatar-group.component';
import { ImageLinksComponent } from './image-links/image-links.component';
import { MenuIconListComponent } from './menu-icon-list/menu-icon-list.component';
import { ListItemComponent } from './list-item/list-item.component';
import { MapMarkerComponent } from './map-marker/map-marker.component';
import { CardFiltersComponent } from './card-filters/card-filters.component';
import { ActionTextComponent } from './action-text/action-text.component';
import { TimelineCompactComponent } from './timeline-compact/timeline-compact.component';
import { ModalFooterComponent } from './modal-footer/modal-footer.component';
import { WelcomeComponent } from './welcome/welcome.component';
import { InfoImageComponent } from './info-image/info-image.component';
import { ModalLargeTierComponent } from './modal-large-tier/modal-large-tier.component';
import { PlaceholderMinimalComponent } from './placeholder-minimal/placeholder-minimal.component';
import { InfoBadgesComponent } from './info-badges/info-badges.component';
import { FeaturesComponent } from './features/features.component';
import { DatepickerComponent } from './datepicker/datepicker.component';
import { VcardRightComponent } from './vcard-right/vcard-right.component';
import { ModalSmallTierComponent } from './modal-small-tier/modal-small-tier.component';
import { FlexTableHeadingComponent } from './flex-table-heading/flex-table-heading.component';
import { FlexTableRowComponent } from './flex-table-row/flex-table-row.component';
import { FlexTableWrapperComponent } from './flex-table-wrapper/flex-table-wrapper.component';
import { FollowersCompactComponent } from './followers-compact/followers-compact.component';
import { InboxMessageComponent } from './inbox-message/inbox-message.component';
import { VideoCompactComponent } from './video-compact/video-compact.component';
import { TagListCompactComponent } from './tag-list-compact/tag-list-compact.component';
import { FileListTabbedComponent } from './file-list-tabbed/file-list-tabbed.component';
import { AvatarGroupIdComponent } from './avatar-group-id/avatar-group-id.component';
import { CommentListCompactComponent } from './comment-list-compact/comment-list-compact.component';
import { FullscreenDropfileComponent } from './fullscreen-dropfile/fullscreen-dropfile.component';
import { PlaceholderCompactComponent } from './placeholder-compact/placeholder-compact.component';
import { ProgressCircleComponent } from './progress-circle/progress-circle.component';
import { AccountBalanceComponent } from './account-balance/account-balance.component';
import { DynamicListComponent } from './dynamic-list/dynamic-list.component';
import { FlexTableComponent } from './flex-table/flex-table.component';
import { FocusLoopComponent } from './focus-loop/focus-loop.component';
import { ListboxItemComponent } from './listbox-item/listbox-item.component';
import { QuillComponent } from './quill/quill.component';
import { SearchComponent } from './search/search.component';
import { SearchTagComponent } from './search-tag/search-tag.component';
import { SelectMultiComponent } from './select-multi/select-multi.component';
import { TabSliderComponent } from './tab-slider/tab-slider.component';
import { TagsComponent } from './tags/tags.component';
import { UploadComponent } from './upload/upload.component';
import { TreeSelectComponent } from './tree-select/tree-select.component';
import { TreeSelectItemComponent } from './tree-select-item/tree-select-item.component';
import { UploadAvatarComponent } from './upload-avatar/upload-avatar.component';
import { UploadInputComponent } from './upload-input/upload-input.component';

@NgModule({
  declarations: [ 
  ],
  imports: [
    CommonModule,
    IonicModule,
    AccountBalanceComponent,
    DynamicListComponent,
    FlexTableComponent,
    FocusLoopComponent,
    ListboxItemComponent,
    QuillComponent,
    SearchComponent,
    SearchTagComponent,
    SelectMultiComponent,
    TabSliderComponent,
    TagsComponent,
    CompanyOverviewComponent,
    ButtonGroupComponent,
    FlexTableStartComponent,
    ImageGalleryComponent,
    FilterComponent,
    TabbedContentComponent,
    IconTextComponent,
    FlexTableCellComponent,
    IconBoxComponent,
    SearchCompactComponent,
    ModalMediumTierComponent,
    AuthorsListCompactComponent,
    AvatarGroupComponent,
    ImageLinksComponent,
    MenuIconListComponent,
    ListItemComponent,
    MapMarkerComponent,
    CardFiltersComponent,
    ActionTextComponent,
    TimelineCompactComponent,
    ModalFooterComponent,
    WelcomeComponent,
    InfoImageComponent,
    ModalLargeTierComponent,
    PlaceholderMinimalComponent,
    InfoBadgesComponent,
    FeaturesComponent,
    DatepickerComponent,
    VcardRightComponent,
    ModalSmallTierComponent,
    FlexTableHeadingComponent,
    FlexTableRowComponent,
    FlexTableWrapperComponent,
    FollowersCompactComponent,
    InboxMessageComponent,
    VideoCompactComponent,
    TagListCompactComponent,
    FileListTabbedComponent,
    AvatarGroupIdComponent,
    CommentListCompactComponent,
    FullscreenDropfileComponent,
    PlaceholderCompactComponent,
    ProgressCircleComponent,
    UploadComponent,
    TreeSelectComponent,
    TreeSelectItemComponent,
    UploadAvatarComponent,
    UploadInputComponent
  ],
  exports: [
    CompanyOverviewComponent,
    ButtonGroupComponent,
    FlexTableStartComponent,
    ImageGalleryComponent,
    FilterComponent,
    TabbedContentComponent,
    IconTextComponent,
    FlexTableCellComponent,
    IconBoxComponent,
    SearchCompactComponent,
    ModalMediumTierComponent,
    AuthorsListCompactComponent,
    AvatarGroupComponent,
    ImageLinksComponent,
    MenuIconListComponent,
    ListItemComponent,
    MapMarkerComponent,
    CardFiltersComponent,
    ActionTextComponent,
    TimelineCompactComponent,
    ModalFooterComponent,
    WelcomeComponent,
    InfoImageComponent,
    ModalLargeTierComponent,
    PlaceholderMinimalComponent,
    InfoBadgesComponent,
    FeaturesComponent,
    DatepickerComponent,
    VcardRightComponent,
    ModalSmallTierComponent,
    FlexTableHeadingComponent,
    FlexTableRowComponent,
    FlexTableWrapperComponent,
    FollowersCompactComponent,
    InboxMessageComponent,
    VideoCompactComponent,
    TagListCompactComponent,
    FileListTabbedComponent,
    AvatarGroupIdComponent,
    CommentListCompactComponent,
    FullscreenDropfileComponent,
    PlaceholderCompactComponent,
    ProgressCircleComponent,
    UploadComponent,
    TreeSelectComponent,
    TreeSelectItemComponent,
    UploadAvatarComponent,
    UploadInputComponent
  ]
})
export class WidgetsModule { }
