import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { CommonModule } from '@angular/common';

import { UploadInputComponent, UploadedFile, UploadEvent } from './upload-input.component';

describe('UploadInputComponent', () => {
  let component: UploadInputComponent;
  let fixture: ComponentFixture<UploadInputComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [UploadInputComponent, CommonModule]
    }).compileComponents();
    
    fixture = TestBed.createComponent(UploadInputComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have default input values', () => {
    expect(component.className).toBe('');
    expect(component.size).toBe('md');
    expect(component.variant).toBe('default');
    expect(component.rounded).toBe('md');
    expect(component.multiple).toBe(true);
    expect(component.maxFiles).toBe(10);
    expect(component.maxFileSize).toBe(10 * 1024 * 1024);
    expect(component.showFileList).toBe(true);
    expect(component.showProgress).toBe(true);
    expect(component.autoUpload).toBe(false);
    expect(component.dragDrop).toBe(true);
  });

  it('should generate correct container classes', () => {
    component.className = 'custom-class';
    const classes = component.containerClasses;
    expect(classes).toContain('w-full');
    expect(classes).toContain('custom-class');
  });

  it('should generate correct upload area classes', () => {
    component.size = 'lg';
    component.variant = 'primary';
    component.rounded = 'lg';
    
    const classes = component.uploadAreaClasses;
    expect(classes).toContain('border-2');
    expect(classes).toContain('min-h-[140px]');
    expect(classes).toContain('border-blue-300');
    expect(classes).toContain('rounded-lg');
  });

  it('should handle file selection', () => {
    spyOn(component.filesSelected, 'emit');
    
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });
    const event = { target: { files: [file], value: 'test.txt' } } as any;
    
    component.onFileSelected(event);
    
    expect(component.files.length).toBe(1);
    expect(component.files[0].name).toBe('test.txt');
    expect(component.filesSelected.emit).toHaveBeenCalled();
    expect(event.target.value).toBe(''); // Should reset input
  });

  it('should validate file size', () => {
    component.maxFileSize = 1024; // 1KB
    
    const largeFile = new File(['x'.repeat(2048)], 'large.txt', { type: 'text/plain' });
    component['handleFiles']([largeFile]);
    
    expect(component.files.length).toBe(0);
    expect(component.errors.length).toBeGreaterThan(0);
    expect(component.errors[0]).toContain('File too large');
  });

  it('should validate file type when accept is specified', () => {
    component.accept = 'image/*';
    
    const textFile = new File(['test'], 'test.txt', { type: 'text/plain' });
    component['handleFiles']([textFile]);
    
    expect(component.files.length).toBe(0);
    expect(component.errors.length).toBeGreaterThan(0);
    expect(component.errors[0]).toContain('Invalid file type');
  });

  it('should respect max files limit', () => {
    component.maxFiles = 2;
    
    const files = [
      new File(['1'], 'file1.txt', { type: 'text/plain' }),
      new File(['2'], 'file2.txt', { type: 'text/plain' }),
      new File(['3'], 'file3.txt', { type: 'text/plain' })
    ];
    
    component['handleFiles'](files);
    
    expect(component.files.length).toBe(2);
    expect(component.errors.length).toBeGreaterThan(0);
    expect(component.errors[0]).toContain('Maximum files reached');
  });

  it('should handle drag and drop events', () => {
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });
    const dataTransfer = { files: [file] } as any;
    const event = { preventDefault: jasmine.createSpy(), dataTransfer } as any;
    
    spyOn(component, 'handleFiles' as any);
    
    component.onDrop(event);
    
    expect(event.preventDefault).toHaveBeenCalled();
    expect(component.isDragOver).toBe(false);
    expect(component['handleFiles']).toHaveBeenCalledWith([file]);
  });

  it('should handle drag over and leave events', () => {
    const event = { preventDefault: jasmine.createSpy() } as any;
    
    component.onDragOver(event);
    expect(component.isDragOver).toBe(true);
    expect(event.preventDefault).toHaveBeenCalled();
    
    component.onDragLeave(event);
    expect(component.isDragOver).toBe(false);
  });

  it('should remove files', () => {
    spyOn(component.fileRemoved, 'emit');
    
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });
    component['handleFiles']([file]);
    
    const uploadedFile = component.files[0];
    component.removeFile(uploadedFile);
    
    expect(component.files.length).toBe(0);
    expect(component.fileRemoved.emit).toHaveBeenCalledWith(uploadedFile);
  });

  it('should clear all files', () => {
    const files = [
      new File(['1'], 'file1.txt', { type: 'text/plain' }),
      new File(['2'], 'file2.txt', { type: 'text/plain' })
    ];
    
    component['handleFiles'](files);
    expect(component.files.length).toBe(2);
    
    component.clearAll();
    expect(component.files.length).toBe(0);
    expect(component.errors.length).toBe(0);
  });

  it('should format file sizes correctly', () => {
    expect(component.formatFileSize(0)).toBe('0 Bytes');
    expect(component.formatFileSize(1024)).toBe('1 KB');
    expect(component.formatFileSize(1024 * 1024)).toBe('1 MB');
    expect(component.formatFileSize(1024 * 1024 * 1024)).toBe('1 GB');
  });

  it('should get correct file icons', () => {
    const imageFile: UploadedFile = { type: 'image/jpeg' } as UploadedFile;
    const pdfFile: UploadedFile = { type: 'application/pdf' } as UploadedFile;
    const docFile: UploadedFile = { type: 'application/msword' } as UploadedFile;
    const genericFile: UploadedFile = { type: 'text/plain' } as UploadedFile;
    
    expect(component.getFileIcon(imageFile)).toBe('🖼️');
    expect(component.getFileIcon(pdfFile)).toBe('📄');
    expect(component.getFileIcon(docFile)).toBe('📝');
    expect(component.getFileIcon(genericFile)).toBe('📎');
  });

  it('should track files by id', () => {
    const file: UploadedFile = { id: 'test-id' } as UploadedFile;
    expect(component.trackByFileId(0, file)).toBe('test-id');
  });

  it('should calculate total file size', () => {
    const files = [
      new File(['1'.repeat(100)], 'file1.txt', { type: 'text/plain' }),
      new File(['2'.repeat(200)], 'file2.txt', { type: 'text/plain' })
    ];
    
    component['handleFiles'](files);
    
    expect(component.totalFileSize).toBe(300);
  });

  it('should identify when at max files', () => {
    component.maxFiles = 1;
    expect(component.isAtMaxFiles).toBe(false);
    
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });
    component['handleFiles']([file]);
    
    expect(component.isAtMaxFiles).toBe(true);
  });

  it('should handle upload simulation', fakeAsync(() => {
    spyOn(component.uploadComplete, 'emit');
    
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });
    component['handleFiles']([file]);
    
    const uploadedFile = component.files[0];
    component['simulateUpload'](uploadedFile);
    
    expect(uploadedFile.status).toBe('uploading');
    expect(uploadedFile.progress).toBe(0);
    
    tick(2000); // Fast-forward time
    
    expect(uploadedFile.status).toBe('completed');
    expect(uploadedFile.progress).toBe(100);
    expect(component.uploadComplete.emit).toHaveBeenCalled();
  }));

  it('should handle disabled state', () => {
    component.disabled = true;
    
    const event = { target: { files: [] } } as any;
    component.onFileSelected(event);
    
    // Should not process files when disabled
    expect(component.files.length).toBe(0);
  });
});
