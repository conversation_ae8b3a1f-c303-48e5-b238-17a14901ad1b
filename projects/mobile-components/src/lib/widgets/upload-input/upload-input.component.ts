import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

// Interface for uploaded file with metadata
export interface UploadedFile {
  file: File;
  id: string;
  name: string;
  size: number;
  type: string;
  preview?: string;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
}

// Interface for upload events
export interface UploadInputEvent {
  files: UploadedFile[];
  totalSize: number;
}

@Component({
  selector: 'base-upload-input',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './upload-input.component.html',
  styleUrls: ['./upload-input.component.css'],
})
export class UploadInputComponent implements OnInit {
  // Standard Tailwind inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() multiple: boolean = true;
  @Input() accept: string = '';
  @Input() maxFiles: number = 10;
  @Input() maxFileSize: number = 10 * 1024 * 1024; // 10MB default
  @Input() maxTotalSize: number = 100 * 1024 * 1024; // 100MB default
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() showFileList: boolean = true;
  @Input() showProgress: boolean = true;
  @Input() showPreview: boolean = true;
  @Input() autoUpload: boolean = false;
  @Input() dragDrop: boolean = true;
  @Input() placeholder: string = 'Choose files or drag them here';
  @Input() buttonText: string = 'Choose Files';
  @Input() dragActiveText: string = 'Drop files here';
  @Input() maxFilesText: string = 'Maximum files reached';
  @Input() maxSizeText: string = 'File too large';
  @Input() invalidTypeText: string = 'Invalid file type';
  @Input() uploadText: string = 'Upload';
  @Input() removeText: string = 'Remove';
  @Input() retryText: string = 'Retry';

  // Events
  @Output() filesSelected = new EventEmitter<UploadInputEvent>();
  @Output() fileRemoved = new EventEmitter<UploadedFile>();
  @Output() uploadStart = new EventEmitter<UploadedFile[]>();
  @Output() uploadProgress = new EventEmitter<UploadedFile>();
  @Output() uploadComplete = new EventEmitter<UploadedFile>();
  @Output() uploadError = new EventEmitter<{ file: UploadedFile; error: string }>();
  @Output() allUploadsComplete = new EventEmitter<UploadedFile[]>();

  // Component state
  files: UploadedFile[] = [];
  isDragOver = false;
  isUploading = false;
  totalProgress = 0;
  errors: string[] = [];

  ngOnInit() {
    // Initialize with default accept types if none provided
    if (!this.accept) {
      this.accept = 'image/*,.pdf,.doc,.docx,.txt';
    }
  }

  // Computed classes for the container
  get containerClasses(): string {
    const baseClasses = 'w-full';
    return `${baseClasses} ${this.className}`.trim();
  }

  // Computed classes for the upload area
  get uploadAreaClasses(): string {
    const baseClasses = 'border-2 border-dashed transition-all duration-200 flex flex-col items-center justify-center text-center';
    const sizeClasses = this.getSizeClasses();
    const variantClasses = this.getVariantClasses();
    const roundedClasses = this.getRoundedClasses();
    const stateClasses = this.getStateClasses();
    
    return `${baseClasses} ${sizeClasses} ${variantClasses} ${roundedClasses} ${stateClasses}`.trim();
  }

  // Computed classes for file items
  get fileItemClasses(): string {
    const baseClasses = 'flex items-center justify-between p-3 border rounded-md transition-all duration-200';
    const variantClasses = 'border-gray-200 bg-gray-50 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:hover:bg-gray-600';
    const roundedClasses = this.getRoundedClasses();
    
    return `${baseClasses} ${variantClasses} ${roundedClasses}`.trim();
  }

  private getSizeClasses(): string {
    const sizeMap = {
      xs: 'min-h-[80px] p-4',
      sm: 'min-h-[100px] p-4',
      md: 'min-h-[120px] p-6',
      lg: 'min-h-[140px] p-8',
      xl: 'min-h-[160px] p-10'
    };
    return sizeMap[this.size] || sizeMap.md;
  }

  private getVariantClasses(): string {
    const variantMap = {
      default: 'border-gray-300 bg-gray-50 dark:border-gray-600 dark:bg-gray-800',
      primary: 'border-blue-300 bg-blue-50 dark:border-blue-600 dark:bg-blue-900/20',
      secondary: 'border-gray-400 bg-gray-100 dark:border-gray-500 dark:bg-gray-700',
      success: 'border-green-300 bg-green-50 dark:border-green-600 dark:bg-green-900/20',
      warning: 'border-yellow-300 bg-yellow-50 dark:border-yellow-600 dark:bg-yellow-900/20',
      danger: 'border-red-300 bg-red-50 dark:border-red-600 dark:bg-red-900/20'
    };
    return variantMap[this.variant] || variantMap.default;
  }

  private getRoundedClasses(): string {
    const roundedMap = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };
    return roundedMap[this.rounded] || roundedMap.md;
  }

  private getStateClasses(): string {
    if (this.disabled) return 'opacity-60 cursor-not-allowed border-gray-200';
    if (this.isDragOver) return 'border-blue-400 bg-blue-100 dark:bg-blue-900/30';
    if (this.isAtMaxFiles) return 'opacity-75 cursor-not-allowed';
    return 'hover:border-gray-400 dark:hover:border-gray-500 cursor-pointer';
  }

  // File handling methods
  onFileSelected(event: Event): void {
    if (this.disabled || this.isAtMaxFiles) return;
    
    const input = event.target as HTMLInputElement;
    if (input.files) {
      this.handleFiles(Array.from(input.files));
      input.value = ''; // Reset input to allow same file selection
    }
  }

  onDrop(event: DragEvent): void {
    if (this.disabled || this.isAtMaxFiles) return;
    
    event.preventDefault();
    this.isDragOver = false;
    
    if (event.dataTransfer?.files) {
      this.handleFiles(Array.from(event.dataTransfer.files));
    }
  }

  onDragOver(event: DragEvent): void {
    if (this.disabled || this.isAtMaxFiles) return;
    
    event.preventDefault();
    this.isDragOver = true;
  }

  onDragLeave(event: DragEvent): void {
    if (this.disabled || this.isAtMaxFiles) return;
    
    event.preventDefault();
    this.isDragOver = false;
  }

  private handleFiles(files: File[]): void {
    this.errors = [];
    const newFiles: UploadedFile[] = [];
    
    for (const file of files) {
      // Check max files limit
      if (this.files.length + newFiles.length >= this.maxFiles) {
        this.errors.push(this.maxFilesText);
        break;
      }
      
      // Validate file
      const validation = this.validateFile(file);
      if (!validation.valid) {
        this.errors.push(validation.error!);
        continue;
      }
      
      // Create uploaded file object
      const uploadedFile: UploadedFile = {
        file,
        id: this.generateId(),
        name: file.name,
        size: file.size,
        type: file.type,
        progress: 0,
        status: 'pending'
      };
      
      // Generate preview for images
      if (this.showPreview && file.type.startsWith('image/')) {
        this.generatePreview(uploadedFile);
      }
      
      newFiles.push(uploadedFile);
    }
    
    // Add valid files
    this.files.push(...newFiles);
    
    // Emit events
    if (newFiles.length > 0) {
      this.filesSelected.emit({
        files: [...this.files],
        totalSize: this.totalFileSize
      });
      
      // Auto upload if enabled
      if (this.autoUpload) {
        this.uploadFiles(newFiles);
      }
    }
  }

  private validateFile(file: File): { valid: boolean; error?: string } {
    // Check file size
    if (file.size > this.maxFileSize) {
      return {
        valid: false,
        error: `${file.name}: ${this.maxSizeText} (${this.formatFileSize(this.maxFileSize)})`
      };
    }
    
    // Check file type if accept is specified
    if (this.accept) {
      const acceptedTypes = this.accept.split(',').map(type => type.trim());
      const isAccepted = acceptedTypes.some(acceptedType => {
        if (acceptedType.startsWith('.')) {
          return file.name.toLowerCase().endsWith(acceptedType.toLowerCase());
        } else {
          return file.type.match(acceptedType.replace('*', '.*'));
        }
      });
      
      if (!isAccepted) {
        return {
          valid: false,
          error: `${file.name}: ${this.invalidTypeText}`
        };
      }
    }
    
    // Check total size
    if (this.totalFileSize + file.size > this.maxTotalSize) {
      return {
        valid: false,
        error: `Total size limit exceeded (${this.formatFileSize(this.maxTotalSize)})`
      };
    }
    
    return { valid: true };
  }

  private generatePreview(uploadedFile: UploadedFile): void {
    const reader = new FileReader();
    reader.onload = (e) => {
      uploadedFile.preview = e.target?.result as string;
    };
    reader.readAsDataURL(uploadedFile.file);
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  // Upload methods
  uploadFiles(files?: UploadedFile[]): void {
    const filesToUpload = files || this.files.filter(f => f.status === 'pending' || f.status === 'error');
    
    if (filesToUpload.length === 0) return;
    
    this.isUploading = true;
    this.uploadStart.emit(filesToUpload);
    
    // Simulate upload for each file
    filesToUpload.forEach(file => this.simulateUpload(file));
  }

  private simulateUpload(file: UploadedFile): void {
    file.status = 'uploading';
    file.progress = 0;
    file.error = undefined;
    
    const interval = setInterval(() => {
      file.progress += Math.random() * 15;
      
      if (file.progress >= 100) {
        file.progress = 100;
        file.status = 'completed';
        clearInterval(interval);
        
        this.uploadProgress.emit(file);
        this.uploadComplete.emit(file);
        
        // Check if all uploads are complete
        if (this.files.every(f => f.status === 'completed' || f.status === 'error')) {
          this.isUploading = false;
          this.allUploadsComplete.emit(this.files.filter(f => f.status === 'completed'));
        }
      } else {
        this.uploadProgress.emit(file);
      }
    }, 200);
  }

  removeFile(file: UploadedFile): void {
    if (this.disabled) return;
    
    const index = this.files.findIndex(f => f.id === file.id);
    if (index > -1) {
      this.files.splice(index, 1);
      this.fileRemoved.emit(file);
    }
  }

  retryUpload(file: UploadedFile): void {
    if (this.disabled) return;
    
    this.uploadFiles([file]);
  }

  clearAll(): void {
    if (this.disabled) return;
    
    this.files = [];
    this.errors = [];
    this.isUploading = false;
  }

  // Utility methods and getters
  get totalFileSize(): number {
    return this.files.reduce((total, file) => total + file.size, 0);
  }

  get isAtMaxFiles(): boolean {
    return this.files.length >= this.maxFiles;
  }

  get hasFiles(): boolean {
    return this.files.length > 0;
  }

  get completedFiles(): UploadedFile[] {
    return this.files.filter(f => f.status === 'completed');
  }

  get pendingFiles(): UploadedFile[] {
    return this.files.filter(f => f.status === 'pending' || f.status === 'error');
  }

  get canUpload(): boolean {
    return this.pendingFiles.length > 0 && !this.isUploading;
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getFileIcon(file: UploadedFile): string {
    if (file.type.startsWith('image/')) return '🖼️';
    if (file.type.includes('pdf')) return '📄';
    if (file.type.includes('word') || file.type.includes('doc')) return '📝';
    if (file.type.includes('excel') || file.type.includes('sheet')) return '📊';
    if (file.type.includes('video')) return '🎥';
    if (file.type.includes('audio')) return '🎵';
    return '📎';
  }

  trackByFileId(index: number, file: UploadedFile): string {
    return file.id;
  }
}
