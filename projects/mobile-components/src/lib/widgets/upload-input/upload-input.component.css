/* Upload Input Component Styles */

:host {
  display: block;
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Upload area focus styles */
.upload-area:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Drag over animation */
.drag-over {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  transform: scale(1.02);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .9;
  }
}

/* File list scroll styling */
.file-list {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.file-list::-webkit-scrollbar {
  width: 6px;
}

.file-list::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 3px;
}

.file-list::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.file-list::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Progress bar animation */
.progress-bar {
  transition: width 0.3s ease-in-out;
}

/* File item hover effects */
.file-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Button hover effects */
button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

button:active:not(:disabled) {
  transform: translateY(0);
}

/* Icon button hover effects */
.icon-button:hover {
  background-color: rgba(59, 130, 246, 0.1);
  border-radius: 50%;
}

/* File preview image styling */
.file-preview {
  object-fit: cover;
  border-radius: 4px;
}

/* Status badge animations */
.status-badge {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Error message styling */
.error-message {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading states */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .upload-area {
    padding: 1rem;
    min-height: 100px;
  }
  
  .file-actions {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .file-info {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .file-list::-webkit-scrollbar-track {
    background: #374151;
  }
  
  .file-list::-webkit-scrollbar-thumb {
    background: #6b7280;
  }
  
  .file-list::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .upload-area {
    border-width: 3px;
  }
  
  .file-item {
    border-width: 2px;
  }
  
  button {
    border: 2px solid currentColor;
  }
}