<!-- Upload Input Component -->
<div [class]="containerClasses">
  <!-- Upload Area -->
  <div 
    [class]="uploadAreaClasses"
    (click)="!disabled && !isAtMaxFiles ? fileInput.click() : null"
    (drop)="dragDrop ? onDrop($event) : null"
    (dragover)="dragDrop ? onDragOver($event) : null"
    (dragleave)="dragDrop ? onDragLeave($event) : null"
    [attr.aria-label]="isDragOver ? dragActiveText : placeholder"
    [attr.tabindex]="disabled || isAtMaxFiles ? -1 : 0"
    role="button"
  >
    <!-- Upload Icon and Content -->
    <div class="flex flex-col items-center justify-center space-y-2">
      <!-- Upload Icon -->
      <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
      </svg>
      
      <!-- Main Text -->
      <div class="text-center">
        <p class="text-lg font-medium text-gray-700 dark:text-gray-200">
          {{ isDragOver ? dragActiveText : placeholder }}
        </p>
        
        <!-- File Info -->
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
          <span *ngIf="accept">{{ accept }}</span>
          <span *ngIf="accept && maxFileSize"> • </span>
          <span>Max {{ formatFileSize(maxFileSize) }} per file</span>
          <span *ngIf="multiple"> • Up to {{ maxFiles }} files</span>
        </p>
      </div>
      
      <!-- Browse Button -->
      <button
        type="button"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        [disabled]="disabled || isAtMaxFiles"
        (click)="$event.stopPropagation(); fileInput.click()"
        [attr.aria-label]="buttonText"
      >
        {{ buttonText }}
      </button>
    </div>
    
    <!-- Max Files Warning -->
    <div *ngIf="isAtMaxFiles" class="mt-2 text-center">
      <span class="text-sm text-orange-600 dark:text-orange-400">
        ⚠️ {{ maxFilesText }}
      </span>
    </div>
  </div>

  <!-- Error Messages -->
  <div *ngIf="errors.length > 0" class="mt-3 space-y-1">
    <div *ngFor="let error of errors" 
         class="text-sm text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 p-2 rounded-md border border-red-200 dark:border-red-800"
         role="alert">
      <svg class="w-4 h-4 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
      </svg>
      {{ error }}
    </div>
  </div>

  <!-- File List -->
  <div *ngIf="showFileList && hasFiles" class="mt-4 space-y-2">
    <!-- Header -->
    <div class="flex items-center justify-between pb-2 border-b border-gray-200 dark:border-gray-600">
      <h4 class="text-sm font-medium text-gray-700 dark:text-gray-200">
        Files ({{ files.length }}/{{ maxFiles }})
      </h4>
      
      <!-- Actions -->
      <div class="flex items-center space-x-2">
        <!-- Upload All Button -->
        <button
          *ngIf="canUpload && !autoUpload"
          type="button"
          class="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors duration-200"
          (click)="uploadFiles()"
          [disabled]="disabled || isUploading"
        >
          {{ uploadText }} All ({{ pendingFiles.length }})
        </button>
        
        <!-- Clear All Button -->
        <button
          type="button"
          class="px-3 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors duration-200"
          (click)="clearAll()"
          [disabled]="disabled || isUploading"
        >
          Clear All
        </button>
      </div>
    </div>

    <!-- File Items -->
    <div class="space-y-2 max-h-64 overflow-y-auto">
      <div *ngFor="let file of files; trackBy: trackByFileId" [class]="fileItemClasses">
        <!-- File Info -->
        <div class="flex items-center space-x-3 flex-1 min-w-0">
          <!-- Preview or Icon -->
          <div class="flex-shrink-0">
            <img 
              *ngIf="file.preview; else fileIcon" 
              [src]="file.preview" 
              [alt]="file.name"
              class="w-8 h-8 object-cover rounded"
            />
            <ng-template #fileIcon>
              <span class="text-lg">{{ getFileIcon(file) }}</span>
            </ng-template>
          </div>
          
          <!-- File Details -->
          <div class="flex-1 min-w-0">
            <div class="flex items-center justify-between">
              <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                {{ file.name }}
              </p>
              
              <!-- Status Badge -->
              <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full"
                    [ngClass]="{
                      'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-200': file.status === 'pending',
                      'bg-blue-100 text-blue-800 dark:bg-blue-600 dark:text-blue-200': file.status === 'uploading',
                      'bg-green-100 text-green-800 dark:bg-green-600 dark:text-green-200': file.status === 'completed',
                      'bg-red-100 text-red-800 dark:bg-red-600 dark:text-red-200': file.status === 'error'
                    }">
                {{ file.status === 'pending' ? 'Pending' : 
                   file.status === 'uploading' ? 'Uploading...' :
                   file.status === 'completed' ? 'Complete' : 'Error' }}
              </span>
            </div>
            
            <div class="flex items-center justify-between mt-1">
              <p class="text-xs text-gray-500 dark:text-gray-400">
                {{ formatFileSize(file.size) }}
              </p>
              
              <!-- Progress -->
              <span *ngIf="file.status === 'uploading' || file.status === 'completed'" 
                    class="text-xs text-gray-500 dark:text-gray-400">
                {{ file.progress.toFixed(0) }}%
              </span>
            </div>
            
            <!-- Progress Bar -->
            <div *ngIf="showProgress && (file.status === 'uploading' || (file.status === 'completed' && file.progress === 100))" 
                 class="w-full bg-gray-200 rounded-full h-1.5 mt-1 dark:bg-gray-700">
              <div 
                class="h-1.5 rounded-full transition-all duration-300"
                [ngClass]="{
                  'bg-blue-600': file.status === 'uploading',
                  'bg-green-600': file.status === 'completed'
                }"
                [style.width.%]="file.progress">
              </div>
            </div>
            
            <!-- Error Message -->
            <p *ngIf="file.error" class="text-xs text-red-600 dark:text-red-400 mt-1">
              {{ file.error }}
            </p>
          </div>
        </div>
        
        <!-- Actions -->
        <div class="flex items-center space-x-2 flex-shrink-0">
          <!-- Retry Button -->
          <button
            *ngIf="file.status === 'error'"
            type="button"
            class="p-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
            (click)="retryUpload(file)"
            [disabled]="disabled"
            [attr.aria-label]="retryText + ' ' + file.name"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
          </button>
          
          <!-- Upload Single File Button -->
          <button
            *ngIf="file.status === 'pending' && !autoUpload"
            type="button"
            class="p-1 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300"
            (click)="uploadFiles([file])"
            [disabled]="disabled || isUploading"
            [attr.aria-label]="uploadText + ' ' + file.name"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
            </svg>
          </button>
          
          <!-- Remove Button -->
          <button
            type="button"
            class="p-1 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
            (click)="removeFile(file)"
            [disabled]="disabled || (file.status === 'uploading')"
            [attr.aria-label]="removeText + ' ' + file.name"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
    
    <!-- Summary -->
    <div class="mt-3 pt-2 border-t border-gray-200 dark:border-gray-600">
      <div class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-300">
        <span>Total size: {{ formatFileSize(totalFileSize) }}</span>
        <span *ngIf="completedFiles.length > 0">
          {{ completedFiles.length }}/{{ files.length }} uploaded
        </span>
      </div>
    </div>
  </div>

  <!-- Required Indicator -->
  <div *ngIf="required && !hasFiles" 
       class="absolute top-2 right-2 text-red-500 text-lg"
       aria-label="Required field">
    *
  </div>

  <!-- Hidden File Input -->
  <input
    #fileInput
    type="file"
    class="hidden"
    [accept]="accept"
    [multiple]="multiple"
    [disabled]="disabled || isAtMaxFiles"
    (change)="onFileSelected($event)"
    [attr.aria-label]="placeholder"
  />
</div>
