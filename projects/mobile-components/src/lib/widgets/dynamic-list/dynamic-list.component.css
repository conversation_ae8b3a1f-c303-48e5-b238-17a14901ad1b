
/* Component Container */
.dynamic-list {
  @apply w-full bg-white dark:bg-gray-900;
}

/* Header Styles */
.dynamic-list-header {
  @apply mb-4 space-y-4;
}

.header-content {
  @apply space-y-2;
}

.list-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.list-description {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.header-controls {
  @apply flex flex-col sm:flex-row gap-3 sm:items-center sm:justify-between;
}

/* Search Styles */
.search-container {
  @apply flex-1 max-w-md;
}

.search-input-wrapper {
  @apply relative;
}

.search-input-wrapper input {
  @apply w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 
         rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white
         placeholder-gray-500 dark:placeholder-gray-400
         focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400;
}

.search-icon {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none;
}

.search-clear {
  @apply absolute right-2 top-1/2 transform -translate-y-1/2 
         p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300
         rounded-full hover:bg-gray-100 dark:hover:bg-gray-700
         transition-colors duration-200;
}

/* Filter and Sort Styles */
.filter-container,
.sort-container {
  @apply flex gap-2 flex-wrap;
}

.filter-container select,
.sort-container select {
  @apply px-3 py-2 border border-gray-300 dark:border-gray-600
         rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white
         text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400;
}

/* Header Actions */
.header-actions {
  @apply flex gap-2 flex-wrap;
}

.header-action {
  @apply inline-flex items-center gap-1 px-3 py-2 text-sm font-medium
         rounded-md border transition-colors duration-200;
}

.header-action.primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white border-blue-600;
}

.header-action.secondary {
  @apply bg-white hover:bg-gray-50 text-gray-700 border-gray-300
         dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-gray-300 dark:border-gray-600;
}

.header-action:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* Loading Skeleton */
.loading-container {
  @apply space-y-3;
}

.skeleton-item {
  @apply flex items-center gap-3 p-3 bg-gray-100 dark:bg-gray-800 rounded-md animate-pulse;
}

.skeleton-avatar {
  @apply w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex-shrink-0;
}

.skeleton-content {
  @apply flex-1 space-y-2;
}

.skeleton-title {
  @apply h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4;
}

.skeleton-subtitle {
  @apply h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2;
}

/* Empty State */
.empty-container {
  @apply py-12 text-center;
}

.empty-content {
  @apply space-y-4;
}

.empty-icon {
  @apply text-4xl text-gray-400 dark:text-gray-500;
}

.empty-title {
  @apply text-lg font-medium text-gray-900 dark:text-white;
}

.empty-message {
  @apply text-gray-600 dark:text-gray-400 max-w-md mx-auto;
}

.empty-action {
  @apply inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700
         text-white rounded-md font-medium transition-colors duration-200;
}

/* List Container */
.list-container {
  @apply space-y-1;
}

.virtual-scroll-container {
  @apply overflow-auto border border-gray-200 dark:border-gray-700 rounded-md;
}

.virtual-scroll-content {
  @apply relative;
}

.regular-list-container {
  @apply space-y-1;
}

/* List Items */
.list-item {
  @apply flex items-center gap-3 p-3 bg-white dark:bg-gray-900
         border border-gray-200 dark:border-gray-700 rounded-md
         transition-all duration-200 cursor-pointer
         hover:bg-gray-50 dark:hover:bg-gray-800
         focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900;
}

.list-item:hover {
  @apply border-gray-300 dark:border-gray-600 shadow-sm;
}

.list-item.selected {
  @apply bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700;
}

.list-item.disabled {
  @apply opacity-50 cursor-not-allowed hover:bg-white dark:hover:bg-gray-900;
}

.list-item[aria-selected="true"] {
  @apply ring-2 ring-blue-500 ring-offset-2 dark:ring-offset-gray-900;
}

/* Item Components */
.item-checkbox {
  @apply flex-shrink-0;
}

.item-checkbox input[type="checkbox"] {
  @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600
         rounded dark:bg-gray-800;
}

.item-drag-handle {
  @apply flex-shrink-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300
         cursor-grab active:cursor-grabbing;
}

.item-visual {
  @apply flex-shrink-0;
}

.item-avatar {
  @apply w-10 h-10 rounded-full object-cover;
}

.item-icon {
  @apply w-10 h-10 flex items-center justify-center bg-gray-100 dark:bg-gray-800
         rounded-full text-gray-600 dark:text-gray-400;
}

.item-content {
  @apply flex items-center gap-3 flex-1 min-w-0;
}

.item-text {
  @apply flex-1 min-w-0;
}

.item-title-row {
  @apply flex items-center gap-2 mb-1;
}

.item-title {
  @apply font-medium text-gray-900 dark:text-white truncate;
}

.item-subtitle {
  @apply text-sm text-gray-600 dark:text-gray-400 truncate;
}

.item-description {
  @apply text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2;
}

/* Badges */
.item-badges {
  @apply flex gap-1;
}

.item-badge {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium rounded-full;
}

.item-badge.solid {
  @apply text-white;
}

.item-badge.outline {
  @apply border;
}

.item-badge.soft {
  @apply;
}

/* Badge Colors */
.item-badge.default.solid { @apply bg-gray-600; }
.item-badge.default.outline { @apply text-gray-600 border-gray-300; }
.item-badge.default.soft { @apply text-gray-600 bg-gray-100; }

.item-badge.primary.solid { @apply bg-blue-600; }
.item-badge.primary.outline { @apply text-blue-600 border-blue-300; }
.item-badge.primary.soft { @apply text-blue-600 bg-blue-100; }

.item-badge.secondary.solid { @apply bg-purple-600; }
.item-badge.secondary.outline { @apply text-purple-600 border-purple-300; }
.item-badge.secondary.soft { @apply text-purple-600 bg-purple-100; }

.item-badge.success.solid { @apply bg-green-600; }
.item-badge.success.outline { @apply text-green-600 border-green-300; }
.item-badge.success.soft { @apply text-green-600 bg-green-100; }

.item-badge.warning.solid { @apply bg-yellow-600; }
.item-badge.warning.outline { @apply text-yellow-600 border-yellow-300; }
.item-badge.warning.soft { @apply text-yellow-600 bg-yellow-100; }

.item-badge.danger.solid { @apply bg-red-600; }
.item-badge.danger.outline { @apply text-red-600 border-red-300; }
.item-badge.danger.soft { @apply text-red-600 bg-red-100; }

/* Dark mode badge adjustments */
.dark .item-badge.default.soft { @apply text-gray-400 bg-gray-800; }
.dark .item-badge.primary.soft { @apply text-blue-400 bg-blue-900/20; }
.dark .item-badge.secondary.soft { @apply text-purple-400 bg-purple-900/20; }
.dark .item-badge.success.soft { @apply text-green-400 bg-green-900/20; }
.dark .item-badge.warning.soft { @apply text-yellow-400 bg-yellow-900/20; }
.dark .item-badge.danger.soft { @apply text-red-400 bg-red-900/20; }

/* Tags */
.item-tags {
  @apply flex flex-wrap gap-1 mt-2;
}

.item-tag {
  @apply inline-block px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800
         text-gray-700 dark:text-gray-300 rounded;
}

/* Metadata */
.item-metadata {
  @apply flex items-center gap-3 mt-2 text-xs text-gray-500 dark:text-gray-400;
}

.item-status[data-status="active"] { @apply text-green-600 dark:text-green-400; }
.item-status[data-status="inactive"] { @apply text-gray-500; }
.item-status[data-status="pending"] { @apply text-yellow-600 dark:text-yellow-400; }
.item-status[data-status="disabled"] { @apply text-red-600 dark:text-red-400; }

.item-priority[data-priority="low"] { @apply text-gray-500; }
.item-priority[data-priority="medium"] { @apply text-blue-600 dark:text-blue-400; }
.item-priority[data-priority="high"] { @apply text-orange-600 dark:text-orange-400; }
.item-priority[data-priority="urgent"] { @apply text-red-600 dark:text-red-400; }

/* Actions */
.item-actions {
  @apply flex items-center gap-1 flex-shrink-0;
}

.item-action {
  @apply inline-flex items-center gap-1 p-2 text-gray-400 hover:text-gray-600
         dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800
         rounded transition-colors duration-200;
}

.item-action:hover {
  @apply bg-gray-100 dark:bg-gray-800;
}

.item-action.primary {
  @apply text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/20;
}

.item-action.danger {
  @apply text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20;
}

.item-link-indicator {
  @apply flex-shrink-0 text-gray-400;
}

/* Pagination */
.pagination-container {
  @apply flex items-center justify-between mt-6 pt-4 border-t border-gray-200 dark:border-gray-700;
}

.pagination-info {
  @apply text-sm text-gray-700 dark:text-gray-300;
}

.pagination-controls {
  @apply flex items-center gap-1;
}

.pagination-button {
  @apply inline-flex items-center justify-center w-8 h-8 text-sm
         border border-gray-300 dark:border-gray-600 rounded
         bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300
         hover:bg-gray-50 dark:hover:bg-gray-700
         disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white dark:disabled:hover:bg-gray-800
         transition-colors duration-200;
}

.pagination-numbers {
  @apply flex items-center gap-1 mx-2;
}

.pagination-number {
  @apply inline-flex items-center justify-center w-8 h-8 text-sm
         border border-gray-300 dark:border-gray-600 rounded
         bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300
         hover:bg-gray-50 dark:hover:bg-gray-700
         transition-colors duration-200;
}

.pagination-number.active {
  @apply bg-blue-600 border-blue-600 text-white hover:bg-blue-700;
}

.pagination-ellipsis {
  @apply inline-flex items-center justify-center w-8 h-8 text-sm
         text-gray-400 cursor-default;
}

/* Layout Variants */
.layout-compact .list-item {
  @apply p-2;
}

.layout-compact .item-avatar {
  @apply w-8 h-8;
}

.layout-compact .item-icon {
  @apply w-8 h-8;
}

.layout-minimal .list-item {
  @apply p-2;
}

.layout-detailed .list-item {
  @apply p-4;
}

.layout-detailed .item-avatar {
  @apply w-12 h-12;
}

.layout-detailed .item-icon {
  @apply w-12 h-12;
}

/* Size Variants */
.size-sm .list-item {
  @apply p-2 text-sm;
}

.size-sm .item-avatar {
  @apply w-6 h-6;
}

.size-sm .item-icon {
  @apply w-6 h-6 text-xs;
}

.size-lg .list-item {
  @apply p-4;
}

.size-lg .item-avatar {
  @apply w-12 h-12;
}

.size-lg .item-icon {
  @apply w-12 h-12 text-lg;
}

.size-xl .list-item {
  @apply p-6;
}

.size-xl .item-avatar {
  @apply w-16 h-16;
}

.size-xl .item-icon {
  @apply w-16 h-16 text-xl;
}

/* Responsive Design */
@media (max-width: 640px) {
  .header-controls {
    @apply flex-col;
  }
  
  .search-container {
    @apply max-w-none;
  }
  
  .filter-container,
  .sort-container,
  .header-actions {
    @apply w-full justify-start;
  }
  
  .pagination-container {
    @apply flex-col gap-3;
  }
  
  .pagination-info {
    @apply order-2;
  }
  
  .item-actions .action-label {
    @apply hidden;
  }
  
  .item-description {
    @apply line-clamp-1;
  }
}

/* Animation Classes */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.list-item {
  animation: fadeIn 0.2s ease-out;
}

/* Focus Styles */
.list-item:focus-visible {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2 dark:ring-offset-gray-900;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .list-item {
    @apply border-2;
  }
  
  .list-item:hover {
    @apply border-blue-600;
  }
  
  .list-item.selected {
    @apply border-blue-700;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .list-item,
  .pagination-button,
  .item-action,
  .search-clear {
    @apply transition-none;
  }
  
  .skeleton-item {
    @apply animate-none;
  }
  
  .list-item {
    animation: none;
  }
}

/* Print Styles */
@media print {
  .header-controls,
  .pagination-container,
  .item-actions {
    @apply hidden;
  }
  
  .list-item {
    @apply border-gray-400 bg-white;
  }
}