<!-- Modal Large Tier Widget -->
<div *ngIf="isOpen" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
  <!-- Backdrop -->
  <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
    <div 
      class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" 
      aria-hidden="true"
      (click)="onBackdropClick()"></div>

    <!-- Center modal vertically -->
    <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

    <!-- Modal panel -->
    <div 
      [class]="modalClasses"
      class="inline-block align-bottom text-left overflow-hidden transform transition-all sm:my-8 sm:align-middle w-full"
      (click)="onModalClick($event)">
      
      <!-- Loading overlay -->
      <div *ngIf="loading" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
        <div class="flex items-center space-x-2">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span class="text-sm text-gray-600">Loading...</span>
        </div>
      </div>

      <!-- Header -->
      <div *ngIf="showHeader" [class]="headerClasses">
        <div class="flex items-center justify-between">
          <div class="flex-1">
            <h3 class="text-lg font-medium text-gray-900" id="modal-title">
              {{ title }}
            </h3>
            <p *ngIf="subtitle" class="mt-1 text-sm text-gray-500">
              {{ subtitle }}
            </p>
          </div>
          
          <!-- Close button -->
          <button 
            *ngIf="showCloseButton"
            type="button"
            (click)="onClose()"
            class="ml-4 bg-white rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            aria-label="Close modal">
            <span class="sr-only">Close</span>
            <i class="fas fa-times h-6 w-6" aria-hidden="true"></i>
          </button>
        </div>
      </div>

      <!-- Content -->
      <div class="px-6 py-6 max-h-96 overflow-y-auto">
        <!-- Default content slot -->
        <ng-content select="[slot=content]"></ng-content>
        
        <!-- Default content when no projection -->
        <div *ngIf="!showHeader" class="text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4">
            <i class="fas fa-info-circle text-blue-600 text-xl" aria-hidden="true"></i>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">{{ title }}</h3>
          <p class="text-sm text-gray-500 mb-6">{{ subtitle }}</p>
        </div>
        
        <!-- Sample content for preview -->
        <div class="space-y-4">
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-2">Large Modal Content Area</h4>
            <p class="text-sm text-gray-600 mb-3">
              This large tier modal is designed for complex content, forms, or detailed information that requires more space.
              It provides ample room for comprehensive user interactions.
            </p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">Sample Input</label>
                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Enter text here...">
              </div>
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">Sample Select</label>
                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option>Option 1</option>
                  <option>Option 2</option>
                  <option>Option 3</option>
                </select>
              </div>
            </div>
          </div>
          
          <div class="bg-blue-50 p-4 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-2">Additional Features</h4>
            <ul class="text-sm text-gray-600 space-y-1">
              <li>✅ Responsive design for all screen sizes</li>
              <li>✅ Keyboard navigation support</li>
              <li>✅ Focus management</li>
              <li>✅ Backdrop click handling</li>
              <li>✅ Loading states</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div *ngIf="showFooter" class="px-6 py-3 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
        <!-- Secondary button -->
        <button 
          *ngIf="showSecondaryButton"
          type="button"
          (click)="onSecondaryAction()"
          [disabled]="loading"
          class="inline-flex justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
          {{ secondaryButtonLabel }}
        </button>
        
        <!-- Primary button -->
        <button 
          *ngIf="showPrimaryButton"
          type="button"
          (click)="onPrimaryAction()"
          [disabled]="primaryButtonDisabled || loading"
          [class]="primaryButtonClasses">
          <span *ngIf="loading" class="mr-2">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          </span>
          {{ primaryButtonLabel }}
        </button>
      </div>
    </div>
  </div>
</div>
