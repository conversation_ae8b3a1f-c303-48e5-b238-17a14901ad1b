import { Component, Input, Output, EventEmitter, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'lib-modal-large-tier',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './modal-large-tier.component.html',
  styleUrl: './modal-large-tier.component.css'
})
export class ModalLargeTierComponent {
  // Standard inputs for customization
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'lg';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'lg';

  // Component-specific inputs
  @Input() isOpen: boolean = false;
  @Input() title: string = 'Large Modal Dialog';
  @Input() subtitle: string = 'Detailed content and actions';
  @Input() showHeader: boolean = true;
  @Input() showFooter: boolean = true;
  @Input() showCloseButton: boolean = true;
  @Input() closableOnBackdrop: boolean = true;
  @Input() closableOnEscape: boolean = true;
  @Input() primaryButtonLabel: string = 'Confirm';
  @Input() secondaryButtonLabel: string = 'Cancel';
  @Input() showPrimaryButton: boolean = true;
  @Input() showSecondaryButton: boolean = true;
  @Input() primaryButtonDisabled: boolean = false;
  @Input() loading: boolean = false;
  @Input() fullScreen: boolean = false;

  // Event outputs
  @Output() close = new EventEmitter<void>();
  @Output() primaryAction = new EventEmitter<void>();
  @Output() secondaryAction = new EventEmitter<void>();
  @Output() backdropClick = new EventEmitter<void>();

  @HostListener('document:keydown.escape', ['$event'])
  onEscapeKey(event: KeyboardEvent): void {
    if (this.isOpen && this.closableOnEscape) {
      this.onClose();
    }
  }

  // Computed getter for modal CSS classes
  get modalClasses(): string {
    const baseClasses = 'modal-large-tier-widget';
    
    const sizeClasses = {
      xs: 'max-w-sm',
      sm: 'max-w-md',
      md: 'max-w-lg',
      lg: 'max-w-4xl',
      xl: 'max-w-6xl'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-xl'
    };

    const fullScreenClass = this.fullScreen ? 'fullscreen-modal' : '';
    
    return [
      baseClasses,
      sizeClasses[this.size],
      roundedClasses[this.rounded],
      fullScreenClass,
      'bg-white shadow-xl',
      this.className
    ].filter(Boolean).join(' ');
  }

  get headerClasses(): string {
    const variantClasses = {
      default: 'bg-gray-50 border-gray-200',
      primary: 'bg-blue-50 border-blue-200',
      secondary: 'bg-gray-100 border-gray-300',
      success: 'bg-green-50 border-green-200',
      warning: 'bg-yellow-50 border-yellow-200',
      danger: 'bg-red-50 border-red-200'
    };

    return `px-6 py-4 border-b ${variantClasses[this.variant]}`;
  }

  get primaryButtonClasses(): string {
    const variantClasses = {
      default: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',
      primary: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',
      secondary: 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500',
      success: 'bg-green-600 hover:bg-green-700 focus:ring-green-500',
      warning: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500',
      danger: 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
    };

    const disabledClass = this.primaryButtonDisabled ? 'opacity-50 cursor-not-allowed' : '';
    
    return `inline-flex justify-center px-4 py-2 text-sm font-medium text-white border border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors ${variantClasses[this.variant]} ${disabledClass}`;
  }

  onClose(): void {
    this.close.emit();
  }

  onPrimaryAction(): void {
    if (!this.primaryButtonDisabled && !this.loading) {
      this.primaryAction.emit();
    }
  }

  onSecondaryAction(): void {
    if (!this.loading) {
      this.secondaryAction.emit();
    }
  }

  onBackdropClick(): void {
    this.backdropClick.emit();
    if (this.closableOnBackdrop) {
      this.onClose();
    }
  }

  onModalClick(event: Event): void {
    event.stopPropagation();
  }
}
