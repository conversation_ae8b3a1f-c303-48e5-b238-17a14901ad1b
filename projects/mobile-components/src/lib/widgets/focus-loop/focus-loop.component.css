
:host {
  @apply block w-full;
}

.focus-loop-container {
  @apply relative w-full;
}

.focus-loop-container:focus-within {
  @apply ring-2 ring-offset-2 rounded-md;
}

/* Screen Reader Only Content */
.sr-only {
  @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
  clip: rect(0, 0, 0, 0);
}

/* Focus Loop Content */
.focus-loop-content {
  @apply w-full;
}

/* Demo Content Styling */
.focus-loop-demo-content {
  @apply p-4 bg-gray-50 border border-gray-200 rounded-lg;
}

.demo-section h3 {
  @apply text-gray-800 font-semibold;
}

.demo-section p {
  @apply text-gray-600;
}

.demo-buttons {
  @apply flex flex-wrap items-center;
}

.demo-inputs {
  @apply space-y-2;
}

.demo-links {
  @apply flex flex-wrap items-center gap-2;
}

.demo-status {
  @apply text-gray-500 text-xs;
}

/* Focus Indicators */
.focus-loop-container *:focus {
  @apply outline-none ring-2 ring-offset-2 rounded;
}

/* Variant-based focus ring colors */
.focus-loop-container.focus-within\:ring-gray-500 *:focus {
  @apply ring-gray-500;
}

.focus-loop-container.focus-within\:ring-blue-500 *:focus {
  @apply ring-blue-500;
}

.focus-loop-container.focus-within\:ring-green-500 *:focus {
  @apply ring-green-500;
}

.focus-loop-container.focus-within\:ring-yellow-500 *:focus {
  @apply ring-yellow-500;
}

.focus-loop-container.focus-within\:ring-red-500 *:focus {
  @apply ring-red-500;
}

/* Enhanced Focus Indicators */
.focus-loop-container *:focus-visible {
  @apply ring-opacity-75;
  animation: focusPulse 0.3s ease-out;
}

/* Disabled State */
.focus-loop-container.opacity-50 {
  @apply cursor-not-allowed;
}

.focus-loop-container.pointer-events-none * {
  @apply pointer-events-none;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .focus-loop-container *:focus {
    @apply outline outline-2 outline-offset-2 outline-current;
  }
  
  .demo-buttons button {
    @apply border-2 border-current;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .focus-loop-container *:focus-visible {
    animation: none;
  }
  
  .demo-buttons button,
  .demo-inputs input,
  .demo-inputs select,
  .demo-inputs textarea {
    @apply transition-none;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .focus-loop-demo-content {
    @apply bg-gray-800 border-gray-700;
  }
  
  .demo-section h3 {
    @apply text-gray-100;
  }
  
  .demo-section p {
    @apply text-gray-300;
  }
  
  .demo-status {
    @apply text-gray-400 border-gray-700;
  }
  
  .demo-inputs input,
  .demo-inputs select,
  .demo-inputs textarea {
    @apply bg-gray-700 border-gray-600 text-gray-100;
  }
  
  .demo-inputs input::placeholder,
  .demo-inputs textarea::placeholder {
    @apply text-gray-400;
  }
}

/* Animation Keyframes */
@keyframes focusPulse {
  0% {
    ring-opacity: 0.5;
    transform: scale(1);
  }
  50% {
    ring-opacity: 1;
    transform: scale(1.02);
  }
  100% {
    ring-opacity: 0.75;
    transform: scale(1);
  }
}

/* Custom Focus Styles for Demo Elements */
.demo-buttons button:focus {
  @apply transform scale-105 shadow-lg;
}

.demo-inputs input:focus,
.demo-inputs select:focus,
.demo-inputs textarea:focus {
  @apply shadow-md border-blue-500;
}

.demo-links a:focus {
  @apply bg-blue-50 px-2 py-1 rounded;
}

/* Layout Responsive Adjustments */
@screen sm {
  .demo-buttons {
    @apply gap-3;
  }
}

@screen md {
  .focus-loop-demo-content {
    @apply p-6;
  }
}

/* Print Styles */
@media print {
  .focus-loop-container *:focus {
    @apply outline outline-1 outline-black;
  }
  
  .sr-only {
    @apply static w-auto h-auto p-2 m-0 overflow-visible whitespace-normal border border-gray-300;
    clip: auto;
  }
}