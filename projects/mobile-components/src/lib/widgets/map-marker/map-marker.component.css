/* Map Marker Component Styles */

/* Base marker container */
.map-marker-container {
  /* Smooth transitions for all interactions */
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Enhanced focus styles */
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  
  /* Border styling */
  border: 2px solid currentColor;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Hover effects */
.map-marker-container:hover:not(.disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Active/pressed state */
.map-marker-container:active:not(.disabled) {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Disabled state */
.map-marker-container.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Loading state */
.map-marker-container.loading {
  cursor: wait;
}

/* Icon styling */
.map-marker-icon svg {
  width: 1em;
  height: 1em;
  flex-shrink: 0;
}

/* Text content styling */
.map-marker-text {
  min-width: 0; /* Allow text truncation */
}

.map-marker-title {
  line-height: 1.2;
}

.map-marker-description {
  line-height: 1.3;
}

.map-marker-position {
  font-family: monospace;
  line-height: 1.2;
}

/* Layout-specific styles */
.map-marker-container.compact .map-marker-text {
  display: none;
}

.map-marker-container.detailed {
  min-width: 120px;
}

/* Orientation-specific styles */
.map-marker-content.vertical {
  text-align: center;
}

.map-marker-content.horizontal .map-marker-text {
  flex: 1;
  min-width: 0;
}

/* Tooltip styling */
.map-marker-container [role="tooltip"] {
  /* Enhanced tooltip appearance */
  backdrop-filter: blur(4px);
  white-space: nowrap;
  max-width: 200px;
  word-wrap: break-word;
  
  /* Animation */
  animation: tooltipFadeIn 0.2s ease-out;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Pulse animation for active markers */
.map-marker-container .animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* Badge styling */
.map-marker-container [class*="badge"] {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Size-specific enhancements */
.map-marker-container.w-6 {
  min-height: 1.5rem;
}

.map-marker-container.w-8 {
  min-height: 2rem;
}

.map-marker-container.w-10 {
  min-height: 2.5rem;
}

.map-marker-container.w-12 {
  min-height: 3rem;
}

.map-marker-container.w-14 {
  min-height: 3.5rem;
}

/* Responsive design */
@media (max-width: 640px) {
  .map-marker-container.detailed {
    min-width: 100px;
  }
  
  .map-marker-container [role="tooltip"] {
    max-width: 150px;
    font-size: 0.75rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .map-marker-container {
    border-width: 3px;
    box-shadow: 0 0 0 1px currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .map-marker-container,
  .map-marker-container *,
  .map-marker-container::before,
  .map-marker-container::after {
    transition: none !important;
    animation: none !important;
  }
}

/* Print styles */
@media print {
  .map-marker-container {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .map-marker-container [role="tooltip"] {
    display: none;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .map-marker-container {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  .map-marker-container:hover:not(.disabled) {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
  }
  
  .map-marker-container [role="tooltip"] {
    background-color: rgba(17, 24, 39, 0.95);
    backdrop-filter: blur(8px);
  }
}

/* Accessibility enhancements */
.map-marker-container:focus-visible {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}