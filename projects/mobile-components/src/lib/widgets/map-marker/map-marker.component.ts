import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

export interface MapMarkerData {
  id?: string;
  title: string;
  position: {
    lat: number;
    lng: number;
  };
  icon?: string;
  color?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  description?: string;
  metadata?: Record<string, any>;
  clickable?: boolean;
  draggable?: boolean;
  visible?: boolean;
}

@Component({
  selector: 'lib-map-marker',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './map-marker.component.html',
  styleUrl: './map-marker.component.css',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: MapMarkerComponent,
      multi: true
    }
  ]
})
export class MapMarkerComponent implements ControlValueAccessor {
  // Standard Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() marker: MapMarkerData = {
    title: 'Sample Location',
    position: { lat: -26.2041, lng: 28.0473 },
    description: 'This is a sample map marker',
    icon: 'location-outline',
    color: 'primary',
    clickable: true,
    draggable: false,
    visible: true
  };

  @Input() showTooltip: boolean = true;
  @Input() showDescription: boolean = true;
  @Input() animateOnClick: boolean = true;
  @Input() disabled: boolean = false;
  @Input() loading: boolean = false;

  // Layout and display options
  @Input() layout: 'compact' | 'standard' | 'detailed' = 'standard';
  @Input() orientation: 'horizontal' | 'vertical' = 'horizontal';
  @Input() alignment: 'left' | 'center' | 'right' = 'center';

  // Event outputs
  @Output() markerClick = new EventEmitter<MapMarkerData>();
  @Output() markerDoubleClick = new EventEmitter<MapMarkerData>();
  @Output() markerDragStart = new EventEmitter<MapMarkerData>();
  @Output() markerDragEnd = new EventEmitter<{ marker: MapMarkerData; newPosition: { lat: number; lng: number } }>();
  @Output() markerHover = new EventEmitter<MapMarkerData>();
  @Output() markerLeave = new EventEmitter<MapMarkerData>();

  // ControlValueAccessor properties
  private _value: MapMarkerData | null = null;
  private onChange = (value: MapMarkerData | null) => {};
  private onTouched = () => {};

  // Component state
  isHovered = false;
  isPressed = false;
  tooltipVisible = false;

  // Computed CSS classes
  get computedClasses(): string {
    const baseClasses = [
      // Base container styles
      'map-marker-container',
      'relative',
      'inline-flex',
      'items-center',
      'justify-center',
      'transition-all',
      'duration-200',
      'cursor-pointer',
      'select-none'
    ];

    // Size classes
    const sizeClasses = this.getSizeClasses();
    baseClasses.push(...sizeClasses);

    // Variant classes
    const variantClasses = this.getVariantClasses();
    baseClasses.push(...variantClasses);

    // Rounded classes
    const roundedClasses = this.getRoundedClasses();
    baseClasses.push(...roundedClasses);

    // Layout classes
    const layoutClasses = this.getLayoutClasses();
    baseClasses.push(...layoutClasses);

    // State classes
    if (this.disabled) {
      baseClasses.push('opacity-50', 'cursor-not-allowed');
    }

    if (this.loading) {
      baseClasses.push('animate-pulse');
    }

    if (this.isHovered && !this.disabled) {
      baseClasses.push('scale-105', 'shadow-lg');
    }

    if (this.isPressed && !this.disabled) {
      baseClasses.push('scale-95');
    }

    // Custom classes
    if (this.className) {
      baseClasses.push(this.className);
    }

    return baseClasses.join(' ');
  }

  get iconClasses(): string {
    const baseClasses = ['transition-all', 'duration-200'];
    
    // Size-based icon classes
    switch (this.size) {
      case 'xs':
        baseClasses.push('text-xs');
        break;
      case 'sm':
        baseClasses.push('text-sm');
        break;
      case 'md':
        baseClasses.push('text-base');
        break;
      case 'lg':
        baseClasses.push('text-lg');
        break;
      case 'xl':
        baseClasses.push('text-xl');
        break;
    }

    return baseClasses.join(' ');
  }

  get contentClasses(): string {
    const baseClasses = ['map-marker-content'];

    if (this.orientation === 'vertical') {
      baseClasses.push('flex', 'flex-col', 'items-center', 'space-y-1');
    } else {
      baseClasses.push('flex', 'items-center', 'space-x-2');
    }

    switch (this.alignment) {
      case 'left':
        baseClasses.push('justify-start');
        break;
      case 'center':
        baseClasses.push('justify-center');
        break;
      case 'right':
        baseClasses.push('justify-end');
        break;
    }

    return baseClasses.join(' ');
  }

  private getSizeClasses(): string[] {
    switch (this.size) {
      case 'xs':
        return ['w-6', 'h-6', 'text-xs'];
      case 'sm':
        return ['w-8', 'h-8', 'text-sm'];
      case 'md':
        return ['w-10', 'h-10', 'text-base'];
      case 'lg':
        return ['w-12', 'h-12', 'text-lg'];
      case 'xl':
        return ['w-14', 'h-14', 'text-xl'];
      default:
        return ['w-10', 'h-10', 'text-base'];
    }
  }

  private getVariantClasses(): string[] {
    switch (this.variant) {
      case 'primary':
        return ['bg-blue-500', 'text-white', 'hover:bg-blue-600', 'border-blue-600'];
      case 'secondary':
        return ['bg-gray-500', 'text-white', 'hover:bg-gray-600', 'border-gray-600'];
      case 'success':
        return ['bg-green-500', 'text-white', 'hover:bg-green-600', 'border-green-600'];
      case 'warning':
        return ['bg-yellow-500', 'text-white', 'hover:bg-yellow-600', 'border-yellow-600'];
      case 'danger':
        return ['bg-red-500', 'text-white', 'hover:bg-red-600', 'border-red-600'];
      default:
        return ['bg-gray-100', 'text-gray-700', 'hover:bg-gray-200', 'border-gray-300'];
    }
  }

  private getRoundedClasses(): string[] {
    switch (this.rounded) {
      case 'none':
        return ['rounded-none'];
      case 'sm':
        return ['rounded-sm'];
      case 'md':
        return ['rounded-md'];
      case 'lg':
        return ['rounded-lg'];
      case 'full':
        return ['rounded-full'];
      default:
        return ['rounded-md'];
    }
  }

  private getLayoutClasses(): string[] {
    const classes = [];

    switch (this.layout) {
      case 'compact':
        classes.push('p-1');
        break;
      case 'detailed':
        classes.push('p-3');
        break;
      default: // standard
        classes.push('p-2');
        break;
    }

    return classes;
  }

  // Event handlers
  onMarkerClick(): void {
    if (this.disabled || this.loading) return;

    this.onTouched();
    this.isPressed = true;
    
    setTimeout(() => {
      this.isPressed = false;
    }, 150);

    this.markerClick.emit(this.marker);
    
    if (this._value !== this.marker) {
      this._value = this.marker;
      this.onChange(this._value);
    }
  }

  onMarkerDoubleClick(): void {
    if (this.disabled || this.loading) return;
    this.markerDoubleClick.emit(this.marker);
  }

  onMarkerHover(): void {
    if (this.disabled) return;
    this.isHovered = true;
    this.tooltipVisible = this.showTooltip;
    this.markerHover.emit(this.marker);
  }

  onMarkerLeave(): void {
    this.isHovered = false;
    this.tooltipVisible = false;
    this.markerLeave.emit(this.marker);
  }

  onDragStart(): void {
    if (this.disabled || !this.marker.draggable) return;
    this.markerDragStart.emit(this.marker);
  }

  onDragEnd(newPosition: { lat: number; lng: number }): void {
    if (this.disabled || !this.marker.draggable) return;
    this.markerDragEnd.emit({ marker: this.marker, newPosition });
  }

  // ControlValueAccessor implementation
  writeValue(value: MapMarkerData | null): void {
    this._value = value;
    if (value) {
      this.marker = { ...this.marker, ...value };
    }
  }

  registerOnChange(fn: (value: MapMarkerData | null) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  // Utility methods
  getIconName(): string {
    return this.marker.icon || 'location-outline';
  }

  hasDescription(): boolean {
    return this.showDescription && !!this.marker.description;
  }

  isVisible(): boolean {
    return this.marker.visible !== false;
  }

  getTooltipText(): string {
    if (this.marker.description) {
      return this.marker.description;
    }
    return this.marker.title;
  }
}
