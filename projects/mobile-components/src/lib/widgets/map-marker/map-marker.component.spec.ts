import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';
import { FormsModule } from '@angular/forms';

import { MapMarkerComponent, MapMarkerData } from './map-marker.component';

describe('MapMarkerComponent', () => {
  let component: MapMarkerComponent;
  let fixture: ComponentFixture<MapMarkerComponent>;
  let debugElement: DebugElement;

  const mockMarkerData: MapMarkerData = {
    id: 'test-marker',
    title: 'Test Location',
    position: { lat: -26.2041, lng: 28.0473 },
    description: 'Test marker description',
    icon: 'location-outline',
    color: 'primary',
    clickable: true,
    draggable: false,
    visible: true
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MapMarkerComponent, FormsModule]
    }).compileComponents();

    fixture = TestBed.createComponent(MapMarkerComponent);
    component = fixture.componentInstance;
    debugElement = fixture.debugElement;
    fixture.detectChanges();
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should have default values', () => {
      expect(component.className).toBe('');
      expect(component.size).toBe('md');
      expect(component.variant).toBe('default');
      expect(component.rounded).toBe('md');
      expect(component.showTooltip).toBe(true);
      expect(component.showDescription).toBe(true);
      expect(component.animateOnClick).toBe(true);
      expect(component.disabled).toBe(false);
      expect(component.loading).toBe(false);
      expect(component.layout).toBe('standard');
      expect(component.orientation).toBe('horizontal');
      expect(component.alignment).toBe('center');
    });

    it('should have default marker data', () => {
      expect(component.marker).toBeDefined();
      expect(component.marker.title).toBe('Sample Location');
      expect(component.marker.position).toEqual({ lat: -26.2041, lng: 28.0473 });
      expect(component.marker.clickable).toBe(true);
      expect(component.marker.visible).toBe(true);
    });
  });

  describe('Input Properties', () => {
    it('should accept custom marker data', () => {
      component.marker = mockMarkerData;
      fixture.detectChanges();

      expect(component.marker.id).toBe('test-marker');
      expect(component.marker.title).toBe('Test Location');
      expect(component.marker.description).toBe('Test marker description');
    });

    it('should accept size variants', () => {
      ['xs', 'sm', 'md', 'lg', 'xl'].forEach(size => {
        component.size = size as any;
        fixture.detectChanges();
        expect(component.size).toBe(size);
      });
    });

    it('should accept variant types', () => {
      ['default', 'primary', 'secondary', 'success', 'warning', 'danger'].forEach(variant => {
        component.variant = variant as any;
        fixture.detectChanges();
        expect(component.variant).toBe(variant);
      });
    });

    it('should accept layout options', () => {
      ['compact', 'standard', 'detailed'].forEach(layout => {
        component.layout = layout as any;
        fixture.detectChanges();
        expect(component.layout).toBe(layout);
      });
    });

    it('should handle disabled state', () => {
      component.disabled = true;
      fixture.detectChanges();

      const container = debugElement.query(By.css('.map-marker-container'));
      expect(container.nativeElement.classList).toContain('opacity-50');
      expect(container.nativeElement.classList).toContain('cursor-not-allowed');
    });

    it('should handle loading state', () => {
      component.loading = true;
      fixture.detectChanges();

      const loadingSpinner = debugElement.query(By.css('.animate-spin'));
      expect(loadingSpinner).toBeTruthy();
    });
  });

  describe('Computed Classes', () => {
    it('should generate correct base classes', () => {
      const classes = component.computedClasses;
      expect(classes).toContain('map-marker-container');
      expect(classes).toContain('relative');
      expect(classes).toContain('inline-flex');
      expect(classes).toContain('items-center');
      expect(classes).toContain('justify-center');
      expect(classes).toContain('transition-all');
      expect(classes).toContain('duration-200');
      expect(classes).toContain('cursor-pointer');
      expect(classes).toContain('select-none');
    });

    it('should include size classes', () => {
      component.size = 'lg';
      const classes = component.computedClasses;
      expect(classes).toContain('w-12');
      expect(classes).toContain('h-12');
      expect(classes).toContain('text-lg');
    });

    it('should include variant classes', () => {
      component.variant = 'primary';
      const classes = component.computedClasses;
      expect(classes).toContain('bg-blue-500');
      expect(classes).toContain('text-white');
      expect(classes).toContain('hover:bg-blue-600');
    });

    it('should include custom className', () => {
      component.className = 'custom-class';
      const classes = component.computedClasses;
      expect(classes).toContain('custom-class');
    });

    it('should include disabled classes when disabled', () => {
      component.disabled = true;
      const classes = component.computedClasses;
      expect(classes).toContain('opacity-50');
      expect(classes).toContain('cursor-not-allowed');
    });

    it('should include loading classes when loading', () => {
      component.loading = true;
      const classes = component.computedClasses;
      expect(classes).toContain('animate-pulse');
    });
  });

  describe('User Interactions', () => {
    it('should emit markerClick event on click', () => {
      spyOn(component.markerClick, 'emit');
      component.marker = mockMarkerData;

      component.onMarkerClick();

      expect(component.markerClick.emit).toHaveBeenCalledWith(mockMarkerData);
    });

    it('should emit markerDoubleClick event on double click', () => {
      spyOn(component.markerDoubleClick, 'emit');
      component.marker = mockMarkerData;

      component.onMarkerDoubleClick();

      expect(component.markerDoubleClick.emit).toHaveBeenCalledWith(mockMarkerData);
    });

    it('should emit markerHover event on hover', () => {
      spyOn(component.markerHover, 'emit');
      component.marker = mockMarkerData;

      component.onMarkerHover();

      expect(component.markerHover.emit).toHaveBeenCalledWith(mockMarkerData);
      expect(component.isHovered).toBe(true);
      expect(component.tooltipVisible).toBe(true);
    });

    it('should emit markerLeave event on mouse leave', () => {
      spyOn(component.markerLeave, 'emit');
      component.marker = mockMarkerData;

      component.onMarkerLeave();

      expect(component.markerLeave.emit).toHaveBeenCalledWith(mockMarkerData);
      expect(component.isHovered).toBe(false);
      expect(component.tooltipVisible).toBe(false);
    });

    it('should not emit events when disabled', () => {
      spyOn(component.markerClick, 'emit');
      component.disabled = true;

      component.onMarkerClick();

      expect(component.markerClick.emit).not.toHaveBeenCalled();
    });

    it('should not emit events when loading', () => {
      spyOn(component.markerClick, 'emit');
      component.loading = true;

      component.onMarkerClick();

      expect(component.markerClick.emit).not.toHaveBeenCalled();
    });

    it('should handle drag events when draggable', () => {
      spyOn(component.markerDragStart, 'emit');
      spyOn(component.markerDragEnd, 'emit');
      
      component.marker = { ...mockMarkerData, draggable: true };
      const newPosition = { lat: -25, lng: 29 };

      component.onDragStart();
      component.onDragEnd(newPosition);

      expect(component.markerDragStart.emit).toHaveBeenCalledWith(component.marker);
      expect(component.markerDragEnd.emit).toHaveBeenCalledWith({
        marker: component.marker,
        newPosition
      });
    });

    it('should not handle drag events when not draggable', () => {
      spyOn(component.markerDragStart, 'emit');
      component.marker = { ...mockMarkerData, draggable: false };

      component.onDragStart();

      expect(component.markerDragStart.emit).not.toHaveBeenCalled();
    });
  });

  describe('Template Rendering', () => {
    it('should render marker container', () => {
      const container = debugElement.query(By.css('.map-marker-container'));
      expect(container).toBeTruthy();
    });

    it('should render marker icon', () => {
      const icon = debugElement.query(By.css('.map-marker-icon svg'));
      expect(icon).toBeTruthy();
    });

    it('should render marker title', () => {
      component.layout = 'standard';
      fixture.detectChanges();

      const title = debugElement.query(By.css('.map-marker-title'));
      expect(title).toBeTruthy();
      expect(title.nativeElement.textContent.trim()).toBe(component.marker.title);
    });

    it('should render marker description when enabled', () => {
      component.marker = mockMarkerData;
      component.showDescription = true;
      component.layout = 'standard';
      fixture.detectChanges();

      const description = debugElement.query(By.css('.map-marker-description'));
      expect(description).toBeTruthy();
      expect(description.nativeElement.textContent.trim()).toBe(mockMarkerData.description);
    });

    it('should not render description when showDescription is false', () => {
      component.marker = mockMarkerData;
      component.showDescription = false;
      fixture.detectChanges();

      const description = debugElement.query(By.css('.map-marker-description'));
      expect(description).toBeFalsy();
    });

    it('should render position info in detailed layout', () => {
      component.marker = mockMarkerData;
      component.layout = 'detailed';
      fixture.detectChanges();

      const position = debugElement.query(By.css('.map-marker-position'));
      expect(position).toBeTruthy();
    });

    it('should render loading spinner when loading', () => {
      component.loading = true;
      fixture.detectChanges();

      const spinner = debugElement.query(By.css('.animate-spin'));
      expect(spinner).toBeTruthy();

      const content = debugElement.query(By.css('.map-marker-content'));
      expect(content).toBeFalsy();
    });

    it('should render tooltip when visible', () => {
      component.tooltipVisible = true;
      fixture.detectChanges();

      const tooltip = debugElement.query(By.css('[role="tooltip"]'));
      expect(tooltip).toBeTruthy();
    });

    it('should render badge when metadata contains badge', () => {
      component.marker = {
        ...mockMarkerData,
        metadata: { badge: '5' }
      };
      component.layout = 'standard';
      fixture.detectChanges();

      const badge = debugElement.query(By.css('.absolute.-top-1.-right-1'));
      expect(badge).toBeTruthy();
      expect(badge.nativeElement.textContent.trim()).toBe('5');
    });

    it('should not render marker when not visible', () => {
      component.marker = { ...mockMarkerData, visible: false };
      fixture.detectChanges();

      const container = debugElement.query(By.css('.map-marker-container'));
      expect(container).toBeFalsy();
    });

    it('should render screen reader content', () => {
      const srContent = debugElement.query(By.css('.sr-only'));
      expect(srContent).toBeTruthy();
    });
  });

  describe('Utility Methods', () => {
    it('should get correct icon name', () => {
      component.marker = mockMarkerData;
      expect(component.getIconName()).toBe('location-outline');

      component.marker = { ...mockMarkerData, icon: undefined };
      expect(component.getIconName()).toBe('location-outline');
    });

    it('should check if has description', () => {
      component.marker = mockMarkerData;
      component.showDescription = true;
      expect(component.hasDescription()).toBe(true);

      component.showDescription = false;
      expect(component.hasDescription()).toBe(false);

      component.marker = { ...mockMarkerData, description: undefined };
      component.showDescription = true;
      expect(component.hasDescription()).toBe(false);
    });

    it('should check if is visible', () => {
      component.marker = mockMarkerData;
      expect(component.isVisible()).toBe(true);

      component.marker = { ...mockMarkerData, visible: false };
      expect(component.isVisible()).toBe(false);
    });

    it('should get correct tooltip text', () => {
      component.marker = mockMarkerData;
      expect(component.getTooltipText()).toBe(mockMarkerData.description);

      component.marker = { ...mockMarkerData, description: undefined };
      expect(component.getTooltipText()).toBe(mockMarkerData.title);
    });
  });

  describe('Accessibility', () => {
    it('should have correct ARIA attributes', () => {
      component.marker = mockMarkerData;
      fixture.detectChanges();

      const container = debugElement.query(By.css('.map-marker-container'));
      expect(container.nativeElement.getAttribute('aria-label')).toBe(mockMarkerData.title);
      expect(container.nativeElement.getAttribute('role')).toBe('button');
      expect(container.nativeElement.getAttribute('tabindex')).toBe('0');
    });

    it('should have aria-describedby when description exists', () => {
      component.marker = mockMarkerData;
      component.showDescription = true;
      fixture.detectChanges();

      const container = debugElement.query(By.css('.map-marker-container'));
      expect(container.nativeElement.getAttribute('aria-describedby')).toBe(`marker-description-${mockMarkerData.id}`);
    });

    it('should handle keyboard events', () => {
      spyOn(component, 'onMarkerClick');
      const container = debugElement.query(By.css('.map-marker-container'));

      // Test Enter key
      container.triggerEventHandler('keydown.enter', {});
      expect(component.onMarkerClick).toHaveBeenCalled();

      // Test Space key
      component.onMarkerClick.calls.reset();
      container.triggerEventHandler('keydown.space', {});
      expect(component.onMarkerClick).toHaveBeenCalled();
    });
  });

  describe('ControlValueAccessor', () => {
    it('should implement ControlValueAccessor interface', () => {
      expect(component.writeValue).toBeDefined();
      expect(component.registerOnChange).toBeDefined();
      expect(component.registerOnTouched).toBeDefined();
      expect(component.setDisabledState).toBeDefined();
    });

    it('should write value correctly', () => {
      component.writeValue(mockMarkerData);
      expect(component.marker.id).toBe(mockMarkerData.id);
      expect(component.marker.title).toBe(mockMarkerData.title);
    });

    it('should register change callback', () => {
      const mockFn = jasmine.createSpy('onChange');
      component.registerOnChange(mockFn);
      
      component.onMarkerClick();
      expect(mockFn).toHaveBeenCalledWith(component.marker);
    });

    it('should register touched callback', () => {
      const mockFn = jasmine.createSpy('onTouched');
      component.registerOnTouched(mockFn);
      
      component.onMarkerClick();
      expect(mockFn).toHaveBeenCalled();
    });

    it('should set disabled state', () => {
      component.setDisabledState(true);
      expect(component.disabled).toBe(true);

      component.setDisabledState(false);
      expect(component.disabled).toBe(false);
    });
  });
});
