<div
  [class]="computedClasses"
  [attr.aria-label]="marker.title"
  [attr.aria-describedby]="hasDescription() ? 'marker-description-' + marker.id : null"
  [attr.data-testid]="'map-marker-' + (marker.id || 'default')"
  role="button"
  tabindex="0"
  (click)="onMarkerClick()"
  (dblclick)="onMarkerDoubleClick()"
  (mouseenter)="onMarkerHover()"
  (mouseleave)="onMarkerLeave()"
  (keydown.enter)="onMarkerClick()"
  (keydown.space)="onMarkerClick()"
  *ngIf="isVisible()"
>
  <!-- Loading state -->
  <div *ngIf="loading" class="flex items-center justify-center">
    <div class="animate-spin rounded-full border-2 border-current border-t-transparent opacity-50"
         [ngClass]="{
           'w-3 h-3': size === 'xs',
           'w-4 h-4': size === 'sm',
           'w-5 h-5': size === 'md',
           'w-6 h-6': size === 'lg',
           'w-7 h-7': size === 'xl'
         }">
    </div>
  </div>

  <!-- Main marker content -->
  <div *ngIf="!loading" [class]="contentClasses">
    <!-- Marker icon -->
    <div class="map-marker-icon flex items-center justify-center">
      <!-- Using a simple location icon since we don't have ion-icon in standalone -->
      <svg [class]="iconClasses" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
      </svg>
    </div>

    <!-- Marker title and description -->
    <div *ngIf="layout !== 'compact'" class="map-marker-text">
      <!-- Title -->
      <div class="map-marker-title font-medium text-current truncate">
        {{ marker.title }}
      </div>

      <!-- Description -->
      <div 
        *ngIf="hasDescription()"
        [id]="'marker-description-' + marker.id"
        class="map-marker-description text-xs text-current opacity-75 truncate">
        {{ marker.description }}
      </div>

      <!-- Position info for detailed layout -->
      <div *ngIf="layout === 'detailed'" class="map-marker-position text-xs text-current opacity-60 mt-1">
        {{ marker.position.lat.toFixed(4) }}, {{ marker.position.lng.toFixed(4) }}
      </div>
    </div>
  </div>

  <!-- Tooltip -->
  <div 
    *ngIf="tooltipVisible && !loading"
    class="absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg pointer-events-none"
    [ngClass]="{
      'bottom-full mb-2': orientation === 'vertical',
      'left-full ml-2': orientation === 'horizontal'
    }"
    role="tooltip"
  >
    {{ getTooltipText() }}
    <!-- Tooltip arrow -->
    <div 
      class="absolute w-2 h-2 bg-gray-900 transform rotate-45"
      [ngClass]="{
        'top-full left-1/2 -translate-x-1/2 -mt-1': orientation === 'vertical',
        'right-full top-1/2 -translate-y-1/2 -mr-1': orientation === 'horizontal'
      }">
    </div>
  </div>

  <!-- Pulse animation for active markers -->
  <div 
    *ngIf="marker.clickable && !disabled && isHovered"
    class="absolute inset-0 rounded-full bg-current opacity-20 animate-ping">
  </div>

  <!-- Badge for marker metadata -->
  <div 
    *ngIf="marker.metadata && marker.metadata['badge'] && layout !== 'compact'"
    class="absolute -top-1 -right-1 flex items-center justify-center w-4 h-4 text-xs font-bold text-white bg-red-500 rounded-full">
    {{ marker.metadata['badge'] }}
  </div>
</div>

<!-- Screen reader content -->
<div class="sr-only">
  Map marker for {{ marker.title }}
  <span *ngIf="marker.description">. {{ marker.description }}</span>
  <span>. Located at coordinates {{ marker.position.lat }}, {{ marker.position.lng }}</span>
  <span *ngIf="marker.clickable">. Press Enter or Space to select this marker</span>
</div>
