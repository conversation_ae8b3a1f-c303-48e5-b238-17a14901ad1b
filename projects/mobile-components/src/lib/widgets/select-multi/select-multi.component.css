/* Select Multi Component Styles */
.select-multi-widget {
  /* Component-specific styles if needed */
}

/* Ensure proper z-index stacking */
.select-multi-widget .fixed {
  z-index: 40;
}

.select-multi-widget .absolute {
  z-index: 50;
}

/* Custom scrollbar for dropdown */
.select-multi-widget .overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.select-multi-widget .overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.select-multi-widget .overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.select-multi-widget .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Focus states for better accessibility */
.select-multi-widget [role="combobox"]:focus {
  outline: none;
}

.select-multi-widget [role="option"]:focus {
  background-color: #f3f4f6;
  outline: none;
}

/* Animation for dropdown */
.select-multi-widget .absolute {
  animation: fadeIn 0.15s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}