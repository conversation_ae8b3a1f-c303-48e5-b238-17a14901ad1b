<div [class]="computedClasses">
  <!-- Main Select Container -->
  <div [class]="containerClasses" 
       (click)="toggleDropdown()"
       (keydown)="onKeyDown($event)"
       tabindex="0"
       role="combobox"
       [attr.aria-expanded]="isDropdownOpen"
       [attr.aria-disabled]="disabled"
       [attr.aria-readonly]="readonly">
    
    <!-- Selected Values Display -->
    <div class="flex-1 flex flex-wrap gap-1 p-2 min-h-0">
      <!-- Selected Tags -->
      <div *ngFor="let value of selectedValues; trackBy: trackByValue" 
           [class]="selectedTagClasses">
        <span *ngIf="showIcons && getOptionByValue(value)?.icon" 
              [class]="getOptionByValue(value)?.icon + ' mr-1'"></span>
        <span>{{ getOptionByValue(value)?.label || value }}</span>
        <button *ngIf="!disabled && !readonly"
                type="button"
                (click)="removeSelectedValue(value); $event.stopPropagation()"
                class="ml-1 text-current opacity-70 hover:opacity-100 transition-opacity"
                [attr.aria-label]="'Remove ' + (getOptionByValue(value)?.label || value)">
          <i class="fa fa-times text-xs"></i>
        </button>
      </div>
      
      <!-- Placeholder or count display -->
      <div *ngIf="selectedValues.length === 0" 
           class="text-gray-500 pointer-events-none select-none py-1">
        {{ placeholder }}
      </div>
    </div>

    <!-- Control Icons -->
    <div class="flex items-center px-2 gap-1">
      <!-- Clear All Button -->
      <button *ngIf="clearable && selectedValues.length > 0 && !disabled && !readonly"
              type="button"
              (click)="onClearAll(); $event.stopPropagation()"
              class="text-gray-400 hover:text-gray-600 transition-colors"
              aria-label="Clear all selections">
        <i class="fa fa-times text-sm"></i>
      </button>
      
      <!-- Dropdown Arrow -->
      <div class="text-gray-400 pointer-events-none">
        <i class="fa transition-transform duration-200"
           [class.fa-chevron-up]="isDropdownOpen"
           [class.fa-chevron-down]="!isDropdownOpen"></i>
      </div>
    </div>
  </div>

  <!-- Dropdown Menu -->
  <div *ngIf="isDropdownOpen" 
       [class]="dropdownClasses"
       [style.max-height]="maxHeight">
    
    <!-- Search Input -->
    <div *ngIf="searchable" class="p-2 border-b border-gray-200">
      <input type="text"
             [value]="searchTerm"
             (input)="onSearchChange($event)"
             [placeholder]="searchPlaceholder"
             class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
             (click)="$event.stopPropagation()">
    </div>

    <!-- Actions Bar -->
    <div *ngIf="showSelectAll && filteredOptions.length > 0" 
         class="flex items-center justify-between p-2 bg-gray-50 border-b border-gray-200">
      <button type="button"
              (click)="onSelectAll(); $event.stopPropagation()"
              class="text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors">
        {{ selectAllText }}
      </button>
      <button *ngIf="selectedValues.length > 0"
              type="button"
              (click)="onClearAll(); $event.stopPropagation()"
              class="text-sm text-gray-600 hover:text-gray-800 transition-colors">
        {{ clearAllText }}
      </button>
    </div>

    <!-- Options List -->
    <div class="max-h-48 overflow-y-auto">
      <!-- No options message -->
      <div *ngIf="filteredOptions.length === 0" 
           class="p-3 text-sm text-gray-500 text-center">
        {{ searchTerm ? noResultsText : noOptionsText }}
      </div>

      <!-- Grouped Options -->
      <div *ngIf="groupOptions && filteredOptions.length > 0">
        <div *ngFor="let groupKey of getGroupKeys()" class="group">
          <!-- Group Header -->
          <div *ngIf="groupKey !== 'default'" 
               class="px-3 py-2 text-xs font-semibold text-gray-600 bg-gray-50 border-b border-gray-100">
            {{ groupKey }}
          </div>
          
          <!-- Group Options -->
          <div *ngFor="let option of getGroupedOptions()[groupKey]; trackBy: trackByOption"
               (click)="toggleOption(option); $event.stopPropagation()"
               class="flex items-center px-3 py-2 cursor-pointer hover:bg-gray-50 transition-colors"
               [class.bg-blue-50]="isSelected(option)"
               [class.text-blue-700]="isSelected(option)"
               [class.opacity-50]="option.disabled"
               [class.cursor-not-allowed]="option.disabled"
               role="option"
               [attr.aria-selected]="isSelected(option)">
            
            <!-- Checkbox -->
            <div class="flex items-center mr-3">
              <input type="checkbox"
                     [checked]="isSelected(option)"
                     [disabled]="option.disabled"
                     class="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                     tabindex="-1">
            </div>
            
            <!-- Icon -->
            <span *ngIf="showIcons && option.icon" 
                  [class]="option.icon + ' mr-2 text-gray-400'"></span>
            
            <!-- Content -->
            <div class="flex-1 min-w-0">
              <div class="text-sm font-medium">{{ option.label }}</div>
              <div *ngIf="showDescriptions && option.description" 
                   class="text-xs text-gray-500 mt-1">
                {{ option.description }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Ungrouped Options -->
      <div *ngIf="!groupOptions && filteredOptions.length > 0">
        <div *ngFor="let option of filteredOptions; trackBy: trackByOption"
             (click)="toggleOption(option); $event.stopPropagation()"
             class="flex items-center px-3 py-2 cursor-pointer hover:bg-gray-50 transition-colors"
             [class.bg-blue-50]="isSelected(option)"
             [class.text-blue-700]="isSelected(option)"
             [class.opacity-50]="option.disabled"
             [class.cursor-not-allowed]="option.disabled"
             role="option"
             [attr.aria-selected]="isSelected(option)">
          
          <!-- Checkbox -->
          <div class="flex items-center mr-3">
            <input type="checkbox"
                   [checked]="isSelected(option)"
                   [disabled]="option.disabled"
                   class="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                   tabindex="-1">
          </div>
          
          <!-- Icon -->
          <span *ngIf="showIcons && option.icon" 
                [class]="option.icon + ' mr-2 text-gray-400'"></span>
          
          <!-- Content -->
          <div class="flex-1 min-w-0">
            <div class="text-sm font-medium">{{ option.label }}</div>
            <div *ngIf="showDescriptions && option.description" 
                 class="text-xs text-gray-500 mt-1">
              {{ option.description }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Max selections warning -->
    <div *ngIf="maxSelections > 0 && selectedValues.length >= maxSelections"
         class="p-2 bg-yellow-50 border-t border-yellow-200 text-xs text-yellow-800">
      <i class="fa fa-exclamation-triangle mr-1"></i>
      Maximum {{ maxSelections }} selections reached
    </div>
  </div>

  <!-- Backdrop to close dropdown -->
  <div *ngIf="isDropdownOpen"
       class="fixed inset-0 z-40"
       (click)="closeDropdown()"></div>
</div>
