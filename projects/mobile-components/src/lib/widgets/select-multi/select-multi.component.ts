import { Component, Input, Output, EventEmitter, forwardRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

export interface SelectOption {
  value: any;
  label: string;
  disabled?: boolean;
  icon?: string;
  description?: string;
  group?: string;
}

@Component({
  selector: 'lib-select-multi',
  templateUrl: './select-multi.component.html',
  styleUrls: ['./select-multi.component.css'],
  standalone: true,
  imports: [CommonModule],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SelectMultiComponent),
      multi: true
    }
  ]
})
export class SelectMultiComponent implements ControlValueAccessor {
  @Input() options: SelectOption[] = [
    { value: 'option1', label: 'Option 1', icon: 'fa fa-star' },
    { value: 'option2', label: 'Option 2', icon: 'fa fa-heart' },
    { value: 'option3', label: 'Option 3', icon: 'fa fa-bookmark' },
    { value: 'option4', label: 'Option 4', disabled: true }
  ];
  @Input() placeholder: string = 'Select multiple options...';
  @Input() searchable: boolean = true;
  @Input() searchPlaceholder: string = 'Search options...';
  @Input() disabled: boolean = false;
  @Input() readonly: boolean = false;
  @Input() clearable: boolean = true;
  @Input() showSelectAll: boolean = true;
  @Input() selectAllText: string = 'Select All';
  @Input() clearAllText: string = 'Clear All';
  @Input() maxSelections: number = 0; // 0 = unlimited
  @Input() showSelectedCount: boolean = true;
  @Input() selectedCountText: string = 'selected';
  @Input() noOptionsText: string = 'No options available';
  @Input() noResultsText: string = 'No results found';
  @Input() maxHeight: string = '200px';
  @Input() showIcons: boolean = true;
  @Input() showDescriptions: boolean = false;
  @Input() groupOptions: boolean = false;
  @Input() allowCustomTags: boolean = false;
  @Input() tagSeparators: string[] = [',', ';'];
  
  // Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  @Output() selectionChange = new EventEmitter<any[]>();
  @Output() optionSelect = new EventEmitter<SelectOption>();
  @Output() optionDeselect = new EventEmitter<SelectOption>();
  @Output() selectAll = new EventEmitter<SelectOption[]>();
  @Output() clearAll = new EventEmitter<void>();
  @Output() searchChange = new EventEmitter<string>();
  @Output() maxSelectionsReached = new EventEmitter<void>();
  @Output() customTagAdd = new EventEmitter<string>();

  selectedValues: any[] = [];
  searchTerm: string = '';
  isDropdownOpen: boolean = false;
  filteredOptions: SelectOption[] = [];

  private onChange = (value: any[]) => {};
  private onTouched = () => {};

  constructor() {
    this.updateFilteredOptions();
  }

  // ControlValueAccessor implementation
  writeValue(value: any[]): void {
    this.selectedValues = value || [];
  }

  registerOnChange(fn: (value: any[]) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  get computedClasses(): string {
    const baseClasses = [
      'select-multi-widget',
      'relative',
      'w-full'
    ];

    if (this.className) {
      baseClasses.push(this.className);
    }

    return baseClasses.join(' ');
  }

  get containerClasses(): string {
    const baseClasses = [
      'relative',
      'border',
      'bg-white',
      'transition-all',
      'duration-200',
      'focus-within:outline-none',
      'focus-within:ring-2',
      'focus-within:ring-offset-1'
    ];

    // Size classes
    const sizeClasses = {
      xs: ['text-xs', 'min-h-6'],
      sm: ['text-sm', 'min-h-8'],
      md: ['text-base', 'min-h-10'],
      lg: ['text-lg', 'min-h-12'],
      xl: ['text-xl', 'min-h-14']
    };

    // Variant classes
    const variantClasses = {
      default: ['border-gray-300', 'focus-within:border-gray-500', 'focus-within:ring-gray-200'],
      primary: ['border-blue-300', 'focus-within:border-blue-500', 'focus-within:ring-blue-200'],
      secondary: ['border-gray-400', 'focus-within:border-gray-600', 'focus-within:ring-gray-200'],
      success: ['border-green-300', 'focus-within:border-green-500', 'focus-within:ring-green-200'],
      warning: ['border-yellow-300', 'focus-within:border-yellow-500', 'focus-within:ring-yellow-200'],
      danger: ['border-red-300', 'focus-within:border-red-500', 'focus-within:ring-red-200']
    };

    // Rounded classes
    const roundedClasses = {
      none: [],
      sm: ['rounded-sm'],
      md: ['rounded-md'],
      lg: ['rounded-lg'],
      full: ['rounded-full']
    };

    // Disabled state
    if (this.disabled) {
      baseClasses.push('opacity-50', 'cursor-not-allowed', 'bg-gray-50');
    } else {
      baseClasses.push('cursor-pointer', 'hover:border-gray-400');
    }

    return [
      ...baseClasses,
      ...sizeClasses[this.size],
      ...variantClasses[this.variant],
      ...roundedClasses[this.rounded]
    ].join(' ');
  }

  get dropdownClasses(): string {
    const baseClasses = [
      'absolute',
      'top-full',
      'left-0',
      'right-0',
      'z-50',
      'mt-1',
      'bg-white',
      'border',
      'border-gray-300',
      'shadow-lg',
      'max-h-60',
      'overflow-y-auto'
    ];

    const roundedClasses = {
      none: [],
      sm: ['rounded-sm'],
      md: ['rounded-md'],
      lg: ['rounded-lg'],
      full: ['rounded-lg'] // full would be too rounded for dropdown
    };

    return [
      ...baseClasses,
      ...roundedClasses[this.rounded]
    ].join(' ');
  }

  get selectedTagClasses(): string {
    const baseClasses = [
      'inline-flex',
      'items-center',
      'gap-1',
      'px-2',
      'py-1',
      'text-sm',
      'font-medium',
      'rounded-md',
      'transition-colors'
    ];

    const variantClasses = {
      default: ['bg-gray-100', 'text-gray-800', 'hover:bg-gray-200'],
      primary: ['bg-blue-100', 'text-blue-800', 'hover:bg-blue-200'],
      secondary: ['bg-gray-200', 'text-gray-800', 'hover:bg-gray-300'],
      success: ['bg-green-100', 'text-green-800', 'hover:bg-green-200'],
      warning: ['bg-yellow-100', 'text-yellow-800', 'hover:bg-yellow-200'],
      danger: ['bg-red-100', 'text-red-800', 'hover:bg-red-200']
    };

    return [
      ...baseClasses,
      ...variantClasses[this.variant]
    ].join(' ');
  }

  updateFilteredOptions(): void {
    if (!this.searchable || !this.searchTerm.trim()) {
      this.filteredOptions = [...this.options];
    } else {
      const term = this.searchTerm.toLowerCase();
      this.filteredOptions = this.options.filter(option =>
        option.label.toLowerCase().includes(term) ||
        (option.description && option.description.toLowerCase().includes(term))
      );
    }
  }

  toggleDropdown(): void {
    if (this.disabled || this.readonly) return;
    
    this.isDropdownOpen = !this.isDropdownOpen;
    if (this.isDropdownOpen) {
      this.onTouched();
    }
  }

  closeDropdown(): void {
    this.isDropdownOpen = false;
  }

  onSearchChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchTerm = target.value;
    this.updateFilteredOptions();
    this.searchChange.emit(this.searchTerm);
  }

  isSelected(option: SelectOption): boolean {
    return this.selectedValues.includes(option.value);
  }

  toggleOption(option: SelectOption): void {
    if (option.disabled || this.disabled || this.readonly) return;

    const isCurrentlySelected = this.isSelected(option);
    
    if (isCurrentlySelected) {
      this.deselectOption(option);
    } else {
      this.selectOption(option);
    }
  }

  selectOption(option: SelectOption): void {
    if (this.maxSelections > 0 && this.selectedValues.length >= this.maxSelections) {
      this.maxSelectionsReached.emit();
      return;
    }

    if (!this.isSelected(option)) {
      this.selectedValues = [...this.selectedValues, option.value];
      this.onChange(this.selectedValues);
      this.selectionChange.emit(this.selectedValues);
      this.optionSelect.emit(option);
    }
  }

  deselectOption(option: SelectOption): void {
    this.selectedValues = this.selectedValues.filter(value => value !== option.value);
    this.onChange(this.selectedValues);
    this.selectionChange.emit(this.selectedValues);
    this.optionDeselect.emit(option);
  }

  removeSelectedValue(value: any): void {
    const option = this.options.find(opt => opt.value === value);
    if (option) {
      this.deselectOption(option);
    }
  }

  onSelectAll(): void {
    const selectableOptions = this.filteredOptions.filter(opt => !opt.disabled);
    const newValues = selectableOptions.map(opt => opt.value);
    
    if (this.maxSelections > 0) {
      this.selectedValues = newValues.slice(0, this.maxSelections);
    } else {
      this.selectedValues = newValues;
    }
    
    this.onChange(this.selectedValues);
    this.selectionChange.emit(this.selectedValues);
    this.selectAll.emit(selectableOptions);
  }

  onClearAll(): void {
    this.selectedValues = [];
    this.onChange(this.selectedValues);
    this.selectionChange.emit(this.selectedValues);
    this.clearAll.emit();
  }

  getSelectedOptions(): SelectOption[] {
    return this.options.filter(option => this.isSelected(option));
  }

  getSelectedCount(): number {
    return this.selectedValues.length;
  }

  getDisplayText(): string {
    const count = this.getSelectedCount();
    if (count === 0) {
      return this.placeholder;
    }
    
    if (this.showSelectedCount && count > 1) {
      return `${count} ${this.selectedCountText}`;
    }
    
    const selectedOptions = this.getSelectedOptions();
    if (selectedOptions.length === 1) {
      return selectedOptions[0].label;
    }
    
    return `${count} ${this.selectedCountText}`;
  }

  onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.toggleDropdown();
    } else if (event.key === 'Escape') {
      this.closeDropdown();
    }
  }

  // Group options by group property
  getGroupedOptions(): { [key: string]: SelectOption[] } {
    if (!this.groupOptions) {
      return { 'default': this.filteredOptions };
    }

    return this.filteredOptions.reduce((groups, option) => {
      const group = option.group || 'Other';
      if (!groups[group]) {
        groups[group] = [];
      }
      groups[group].push(option);
      return groups;
    }, {} as { [key: string]: SelectOption[] });
  }

  getGroupKeys(): string[] {
    return Object.keys(this.getGroupedOptions());
  }

  trackByValue(index: number, value: any): any {
    return value;
  }

  trackByOption(index: number, option: SelectOption): any {
    return option.value;
  }

  getOptionByValue(value: any): SelectOption | undefined {
    return this.options.find(option => option.value === value);
  }
}
