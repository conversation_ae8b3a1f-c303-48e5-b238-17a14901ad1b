import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface ImageOverlay {
  type: 'text' | 'badge' | 'button' | 'icon';
  content: string;
  position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
  color?: string;
  clickable?: boolean;
}

@Component({
  selector: 'lib-info-image',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './info-image.component.html',
  styleUrl: './info-image.component.css'
})
export class InfoImageComponent {
  // Standard Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() src: string = 'https://via.placeholder.com/400x300/e5e7eb/6b7280?text=Info+Image';
  @Input() alt: string = 'Information image';
  @Input() title: string = 'Sample Info Image';
  @Input() description: string = 'This is a sample information image with overlay capabilities and zoom functionality.';
  @Input() caption: string = '';
  @Input() overlays: ImageOverlay[] = [
    { type: 'badge', content: 'Featured', position: 'top-right', color: 'blue' },
    { type: 'text', content: 'Sample Caption', position: 'bottom-left' }
  ];

  @Input() showTitle: boolean = true;
  @Input() showDescription: boolean = true;
  @Input() showCaption: boolean = true;
  @Input() showOverlays: boolean = true;
  @Input() allowZoom: boolean = true;
  @Input() allowFullscreen: boolean = false;
  @Input() aspectRatio: 'auto' | 'square' | '16:9' | '4:3' | '3:2' = 'auto';
  @Input() objectFit: 'cover' | 'contain' | 'fill' | 'scale-down' | 'none' = 'cover';
  @Input() loading: 'lazy' | 'eager' = 'lazy';
  @Input() placeholder: string = '';

  // Event outputs
  @Output() imageClick = new EventEmitter<Event>();
  @Output() imageLoad = new EventEmitter<Event>();
  @Output() imageError = new EventEmitter<Event>();
  @Output() overlayClick = new EventEmitter<ImageOverlay>();
  @Output() zoomToggle = new EventEmitter<boolean>();

  // Internal state
  isZoomed: boolean = false;
  isLoading: boolean = true;
  hasError: boolean = false;

  onImageClick(event: Event) {
    if (this.allowZoom) {
      this.isZoomed = !this.isZoomed;
      this.zoomToggle.emit(this.isZoomed);
    }
    this.imageClick.emit(event);
  }

  onImageLoad(event: Event) {
    this.isLoading = false;
    this.hasError = false;
    this.imageLoad.emit(event);
  }

  onImageError(event: Event) {
    this.isLoading = false;
    this.hasError = true;
    this.imageError.emit(event);
  }

  onOverlayClick(overlay: ImageOverlay, event: Event) {
    event.stopPropagation();
    if (overlay.clickable) {
      this.overlayClick.emit(overlay);
    }
  }

  get containerClasses(): string {
    const baseClasses = 'relative overflow-hidden';
    const sizeClasses = {
      xs: 'w-32 h-24',
      sm: 'w-48 h-36',
      md: 'w-64 h-48',
      lg: 'w-80 h-60',
      xl: 'w-96 h-72'
    };
    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };
    const aspectClasses = {
      auto: '',
      square: 'aspect-square',
      '16:9': 'aspect-video',
      '4:3': 'aspect-[4/3]',
      '3:2': 'aspect-[3/2]'
    };

    return [
      baseClasses,
      this.aspectRatio === 'auto' ? sizeClasses[this.size] : aspectClasses[this.aspectRatio],
      roundedClasses[this.rounded],
      this.className
    ].filter(Boolean).join(' ');
  }

  get imageClasses(): string {
    const baseClasses = 'w-full h-full transition-transform duration-300';
    const objectFitClasses = {
      cover: 'object-cover',
      contain: 'object-contain',
      fill: 'object-fill',
      'scale-down': 'object-scale-down',
      none: 'object-none'
    };
    const zoomClasses = this.isZoomed ? 'scale-110' : 'scale-100';
    const cursorClasses = this.allowZoom ? 'cursor-zoom-in' : 'cursor-default';

    return [
      baseClasses,
      objectFitClasses[this.objectFit],
      zoomClasses,
      cursorClasses
    ].filter(Boolean).join(' ');
  }

  getOverlayClasses(overlay: ImageOverlay): string {
    const baseClasses = 'absolute z-10 pointer-events-auto';
    const positionClasses = {
      'top-left': 'top-2 left-2',
      'top-right': 'top-2 right-2',
      'bottom-left': 'bottom-2 left-2',
      'bottom-right': 'bottom-2 right-2',
      'center': 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2'
    };
    const typeClasses = {
      text: 'bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm',
      badge: 'px-2 py-1 rounded-full text-xs font-medium',
      button: 'bg-white bg-opacity-90 hover:bg-opacity-100 px-3 py-1 rounded shadow text-sm font-medium transition-colors',
      icon: 'w-8 h-8 bg-white bg-opacity-90 rounded-full flex items-center justify-center shadow'
    };
    const colorClasses = overlay.color ? `bg-${overlay.color}-500 text-white` : '';
    const cursorClasses = overlay.clickable ? 'cursor-pointer hover:opacity-80' : '';

    return [
      baseClasses,
      positionClasses[overlay.position],
      typeClasses[overlay.type],
      colorClasses,
      cursorClasses
    ].filter(Boolean).join(' ');
  }

  trackByOverlay(index: number, overlay: ImageOverlay): any {
    return `${overlay.type}-${overlay.position}-${overlay.content}`;
  }
}
