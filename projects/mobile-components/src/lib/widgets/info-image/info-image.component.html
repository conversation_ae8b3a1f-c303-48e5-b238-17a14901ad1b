<div class="space-y-3">
  <!-- Image Container -->
  <div [class]="containerClasses">
    <!-- Loading Placeholder -->
    <div *ngIf="isLoading" class="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
      <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
      </svg>
    </div>

    <!-- Error State -->
    <div *ngIf="hasError" class="absolute inset-0 bg-gray-100 flex items-center justify-center">
      <div class="text-center">
        <svg class="w-12 h-12 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
        </svg>
        <p class="text-sm text-gray-500">Failed to load image</p>
      </div>
    </div>

    <!-- Main Image -->
    <img
      [src]="src"
      [alt]="alt"
      [class]="imageClasses"
      [loading]="loading"
      (click)="onImageClick($event)"
      (load)="onImageLoad($event)"
      (error)="onImageError($event)"
      [attr.aria-label]="alt"
    />

    <!-- Overlays -->
    <div *ngIf="showOverlays" class="absolute inset-0 pointer-events-none">
      <div
        *ngFor="let overlay of overlays; trackBy: trackByOverlay"
        [class]="getOverlayClasses(overlay)"
        (click)="onOverlayClick(overlay, $event)"
        [attr.aria-label]="overlay.content"
      >
        <!-- Text Overlay -->
        <span *ngIf="overlay.type === 'text'">{{ overlay.content }}</span>

        <!-- Badge Overlay -->
        <span *ngIf="overlay.type === 'badge'">{{ overlay.content }}</span>

        <!-- Button Overlay -->
        <button *ngIf="overlay.type === 'button'" type="button">{{ overlay.content }}</button>

        <!-- Icon Overlay -->
        <svg *ngIf="overlay.type === 'icon'" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </div>
    </div>

    <!-- Zoom Indicator -->
    <div *ngIf="allowZoom" class="absolute top-2 left-2 bg-black bg-opacity-50 text-white p-1 rounded text-xs opacity-0 hover:opacity-100 transition-opacity">
      {{ isZoomed ? 'Click to zoom out' : 'Click to zoom in' }}
    </div>
  </div>

  <!-- Image Info -->
  <div class="space-y-2">
    <!-- Title -->
    <h3 *ngIf="showTitle && title" class="text-lg font-semibold text-gray-900">{{ title }}</h3>

    <!-- Description -->
    <p *ngIf="showDescription && description" class="text-sm text-gray-600">{{ description }}</p>

    <!-- Caption -->
    <p *ngIf="showCaption && caption" class="text-xs text-gray-500 italic">{{ caption }}</p>
  </div>
</div>
