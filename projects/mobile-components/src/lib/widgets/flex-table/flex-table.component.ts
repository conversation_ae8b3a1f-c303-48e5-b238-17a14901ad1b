import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

export interface FlexTableColumn {
  key: string;
  title: string;
  type?: 'text' | 'number' | 'date' | 'boolean' | 'action';
  width?: string;
  sortable?: boolean;
  searchable?: boolean;
  align?: 'left' | 'center' | 'right';
  formatter?: (value: any) => string;
}

export interface FlexTableRow {
  id: string | number;
  data: Record<string, any>;
  selected?: boolean;
  disabled?: boolean;
  actions?: any[];
}

export interface FlexTableSort {
  column: string;
  direction: 'asc' | 'desc';
}

@Component({
  selector: 'base-flex-table',
  templateUrl: './flex-table.component.html',
  styleUrls: ['./flex-table.component.css'],
  standalone: true,
  imports: [CommonModule, FormsModule]
})
export class FlexTableComponent {
  @Input() columns: FlexTableColumn[] = [
    { key: 'name', title: 'Name', sortable: true, searchable: true },
    { key: 'email', title: 'Email', sortable: true, searchable: true },
    { key: 'status', title: 'Status', align: 'center' },
    { key: 'actions', title: 'Actions', type: 'action', align: 'right' }
  ];
  
  @Input() data: FlexTableRow[] = [
    {
      id: 1,
      data: {
        name: 'John Doe',
        email: '<EMAIL>',
        status: 'Active',
        actions: [{ label: 'Edit', action: 'edit' }, { label: 'Delete', action: 'delete' }]
      }
    },
    {
      id: 2,
      data: {
        name: 'Jane Smith',
        email: '<EMAIL>',
        status: 'Inactive',
        actions: [{ label: 'Edit', action: 'edit' }, { label: 'Delete', action: 'delete' }]
      }
    },
    {
      id: 3,
      data: {
        name: 'Bob Johnson',
        email: '<EMAIL>',
        status: 'Pending',
        actions: [{ label: 'Edit', action: 'edit' }, { label: 'Delete', action: 'delete' }]
      }
    }
  ];
  
  @Input() loading: boolean = false;
  @Input() selectable: boolean = false;
  @Input() multiSelect: boolean = false;
  @Input() sortable: boolean = true;
  @Input() searchable: boolean = false;
  @Input() paginated: boolean = false;
  @Input() pageSize: number = 10;
  @Input() showHeader: boolean = true;
  @Input() showFooter: boolean = false;
  @Input() striped: boolean = true;
  @Input() bordered: boolean = false;
  @Input() hoverable: boolean = true;
  @Input() condensed: boolean = false;
  @Input() responsive: boolean = true;
  @Input() sticky: boolean = false;
  @Input() virtualScroll: boolean = false;
  
  // Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'minimal' | 'bordered' | 'striped' | 'card' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() shadow: 'none' | 'sm' | 'md' | 'lg' | 'xl' = 'sm';
  
  @Output() rowClick = new EventEmitter<FlexTableRow>();
  @Output() rowSelect = new EventEmitter<FlexTableRow[]>();
  @Output() columnSort = new EventEmitter<FlexTableSort>();
  @Output() actionClick = new EventEmitter<{row: FlexTableRow, action: any}>();
  @Output() search = new EventEmitter<string>();
  @Output() pageChange = new EventEmitter<number>();

  currentSort: FlexTableSort | null = null;
  searchTerm: string = '';
  currentPage: number = 1;
  selectedRows: FlexTableRow[] = [];

  get containerClasses(): string {
    const baseClasses = [
      'flex-table-container',
      'w-full'
    ];

    if (this.responsive) {
      baseClasses.push('overflow-x-auto');
    }

    if (this.sticky) {
      baseClasses.push('sticky', 'top-0', 'z-10');
    }

    // Shadow classes
    const shadowClasses = {
      none: [],
      sm: ['shadow-sm'],
      md: ['shadow-md'],
      lg: ['shadow-lg'],
      xl: ['shadow-xl']
    };
    baseClasses.push(...shadowClasses[this.shadow]);

    // Rounded classes
    const roundedClasses = {
      none: [],
      sm: ['rounded-sm'],
      md: ['rounded-md'],
      lg: ['rounded-lg'],
      xl: ['rounded-xl']
    };
    baseClasses.push(...roundedClasses[this.rounded]);

    if (this.className) {
      baseClasses.push(this.className);
    }

    return baseClasses.join(' ');
  }

  get tableClasses(): string {
    const baseClasses = [
      'flex-table',
      'min-w-full',
      'divide-y',
      'divide-gray-200'
    ];

    // Variant classes
    const variantClasses = {
      default: ['bg-white'],
      minimal: ['bg-transparent'],
      bordered: ['bg-white', 'border', 'border-gray-200'],
      striped: ['bg-white'],
      card: ['bg-white', 'rounded-lg', 'shadow']
    };
    baseClasses.push(...variantClasses[this.variant]);

    return baseClasses.join(' ');
  }

  get headerClasses(): string {
    const baseClasses = [
      'table-header',
      'bg-gray-50'
    ];

    if (this.sticky) {
      baseClasses.push('sticky', 'top-0', 'z-10');
    }

    return baseClasses.join(' ');
  }

  get bodyClasses(): string {
    const baseClasses = [
      'table-body',
      'bg-white',
      'divide-y',
      'divide-gray-200'
    ];

    if (this.striped) {
      baseClasses.push('divide-gray-200');
    }

    return baseClasses.join(' ');
  }

  getRowClasses(row: FlexTableRow, index: number): string {
    const baseClasses = [
      'table-row'
    ];

    if (this.hoverable && !row.disabled) {
      baseClasses.push('hover:bg-gray-50', 'cursor-pointer');
    }

    if (this.striped && index % 2 === 1) {
      baseClasses.push('bg-gray-50');
    }

    if (row.selected) {
      baseClasses.push('bg-blue-50', 'border-blue-200');
    }

    if (row.disabled) {
      baseClasses.push('opacity-50', 'cursor-not-allowed');
    }

    // Size classes
    const sizeClasses = {
      xs: ['text-xs'],
      sm: ['text-sm'],
      md: ['text-sm'],
      lg: ['text-base'],
      xl: ['text-lg']
    };
    baseClasses.push(...sizeClasses[this.size]);

    return baseClasses.join(' ');
  }

  getCellClasses(column: FlexTableColumn): string {
    const baseClasses = [
      'table-cell',
      'whitespace-nowrap'
    ];

    // Size-based padding
    const paddingClasses = {
      xs: ['px-2', 'py-1'],
      sm: ['px-3', 'py-2'],
      md: ['px-4', 'py-3'],
      lg: ['px-6', 'py-4'],
      xl: ['px-8', 'py-5']
    };
    baseClasses.push(...paddingClasses[this.size]);

    // Alignment
    const alignClasses = {
      left: ['text-left'],
      center: ['text-center'],
      right: ['text-right']
    };
    if (column.align) {
      baseClasses.push(...alignClasses[column.align]);
    }

    return baseClasses.join(' ');
  }

  getHeaderCellClasses(column: FlexTableColumn): string {
    const baseClasses = [
      'header-cell',
      'font-medium',
      'text-gray-900',
      'tracking-wider',
      'uppercase'
    ];

    // Size-based styling
    const sizeClasses = {
      xs: ['text-xs', 'px-2', 'py-1'],
      sm: ['text-xs', 'px-3', 'py-2'],
      md: ['text-xs', 'px-4', 'py-3'],
      lg: ['text-sm', 'px-6', 'py-4'],
      xl: ['text-sm', 'px-8', 'py-5']
    };
    baseClasses.push(...sizeClasses[this.size]);

    // Alignment
    const alignClasses = {
      left: ['text-left'],
      center: ['text-center'],
      right: ['text-right']
    };
    if (column.align) {
      baseClasses.push(...alignClasses[column.align]);
    }

    if (column.sortable && this.sortable) {
      baseClasses.push('cursor-pointer', 'hover:bg-gray-100', 'select-none');
    }

    return baseClasses.join(' ');
  }

  onRowClick(row: FlexTableRow): void {
    if (row.disabled) return;
    
    this.rowClick.emit(row);
    
    if (this.selectable) {
      this.toggleRowSelection(row);
    }
  }

  toggleRowSelection(row: FlexTableRow): void {
    if (!this.multiSelect) {
      // Single select - clear others
      this.selectedRows.forEach(r => r.selected = false);
      this.selectedRows = [];
    }

    row.selected = !row.selected;
    
    if (row.selected) {
      this.selectedRows.push(row);
    } else {
      this.selectedRows = this.selectedRows.filter(r => r.id !== row.id);
    }

    this.rowSelect.emit([...this.selectedRows]);
  }

  onColumnSort(column: FlexTableColumn): void {
    if (!column.sortable || !this.sortable) return;

    const direction = this.currentSort?.column === column.key && this.currentSort?.direction === 'asc' ? 'desc' : 'asc';
    this.currentSort = { column: column.key, direction };
    this.columnSort.emit(this.currentSort);
  }

  onActionClick(row: FlexTableRow, action: any): void {
    this.actionClick.emit({ row, action });
  }

  onSearch(term: string): void {
    this.searchTerm = term;
    this.search.emit(term);
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.pageChange.emit(page);
  }

  formatCellValue(value: any, column: FlexTableColumn): string {
    if (column.formatter) {
      return column.formatter(value);
    }
    
    if (value === null || value === undefined) {
      return '';
    }
    
    return String(value);
  }

  getSortIcon(column: FlexTableColumn): string {
    if (!column.sortable || !this.currentSort || this.currentSort.column !== column.key) {
      return 'M7 10l5 5 5-5z'; // Default sort icon
    }
    
    return this.currentSort.direction === 'asc' 
      ? 'M5 15l7-7 7 7z' // Sort up
      : 'M19 9l-7 7-7-7z'; // Sort down
  }

  trackByFn(index: number, row: FlexTableRow): any {
    return row.id || index;
  }

  trackByColumnFn(index: number, column: FlexTableColumn): any {
    return column.key || index;
  }

  getMinValue(a: number, b: number): number {
    return Math.min(a, b);
  }

  get visibleData(): FlexTableRow[] {
    let filtered = [...this.data];
    
    // Apply search filter
    if (this.searchTerm && this.searchable) {
      const searchColumns = this.columns.filter(col => col.searchable);
      filtered = filtered.filter(row => 
        searchColumns.some(col => 
          String(row.data[col.key]).toLowerCase().includes(this.searchTerm.toLowerCase())
        )
      );
    }
    
    // Apply pagination
    if (this.paginated) {
      const startIndex = (this.currentPage - 1) * this.pageSize;
      filtered = filtered.slice(startIndex, startIndex + this.pageSize);
    }
    
    return filtered;
  }

  get totalPages(): number {
    return Math.ceil(this.data.length / this.pageSize);
  }

  selectAll(): void {
    if (!this.multiSelect) return;
    
    const allSelected = this.visibleData.every(row => row.selected);
    
    this.visibleData.forEach(row => {
      row.selected = !allSelected;
    });
    
    this.selectedRows = allSelected ? [] : [...this.visibleData.filter(row => row.selected)];
    this.rowSelect.emit([...this.selectedRows]);
  }

  get isAllSelected(): boolean {
    return this.visibleData.length > 0 && this.visibleData.every(row => row.selected);
  }

  get isIndeterminate(): boolean {
    const selectedCount = this.visibleData.filter(row => row.selected).length;
    return selectedCount > 0 && selectedCount < this.visibleData.length;
  }
}
