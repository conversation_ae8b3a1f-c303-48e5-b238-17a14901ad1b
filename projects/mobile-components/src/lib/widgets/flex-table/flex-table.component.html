<div [class]="containerClasses">
  <!-- Table Header Actions -->
  <div *ngIf="searchable || showHeader" class="flex justify-between items-center p-4 border-b border-gray-200">
    <!-- Search Input -->
    <div *ngIf="searchable" class="flex-1 max-w-sm">
      <div class="relative">
        <input
          type="text"
          placeholder="Search..."
          [(ngModel)]="searchTerm"
          (input)="onSearch(searchTerm)"
          class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        >
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </div>
      </div>
    </div>
    
    <!-- Header Actions Slot -->
    <div class="flex items-center space-x-2">
      <ng-content select="[slot=header-actions]"></ng-content>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex justify-center items-center p-8">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    <span class="ml-2 text-gray-600">Loading...</span>
  </div>

  <!-- Table Content -->
  <div *ngIf="!loading" class="overflow-hidden">
    <table [class]="tableClasses">
      <!-- Table Header -->
      <thead *ngIf="showHeader" [class]="headerClasses">
        <tr>
          <!-- Select All Checkbox -->
          <th *ngIf="selectable && multiSelect" [class]="getHeaderCellClasses({key: 'select', title: '', align: 'center'})">
            <input
              type="checkbox"
              [checked]="isAllSelected"
              [indeterminate]="isIndeterminate"
              (change)="selectAll()"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            >
          </th>
          
          <!-- Single Select Column -->
          <th *ngIf="selectable && !multiSelect" [class]="getHeaderCellClasses({key: 'select', title: '', align: 'center'})">
            <span class="sr-only">Select</span>
          </th>
          
          <!-- Data Columns -->
          <th 
            *ngFor="let column of columns; trackBy: trackByColumnFn"
            [class]="getHeaderCellClasses(column)"
            [style.width]="column.width"
            (click)="onColumnSort(column)"
          >
            <div class="flex items-center" [class.justify-center]="column.align === 'center'" [class.justify-end]="column.align === 'right'">
              <span>{{ column.title }}</span>
              <svg 
                *ngIf="column.sortable && sortable"
                class="ml-1 h-4 w-4 text-gray-400"
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="getSortIcon(column)"></path>
              </svg>
            </div>
          </th>
        </tr>
      </thead>

      <!-- Table Body -->
      <tbody [class]="bodyClasses">
        <tr 
          *ngFor="let row of visibleData; trackBy: trackByFn; let i = index"
          [class]="getRowClasses(row, i)"
          (click)="onRowClick(row)"
        >
          <!-- Select Checkbox -->
          <td *ngIf="selectable" [class]="getCellClasses({key: 'select', title: '', align: 'center'})">
            <input
              type="checkbox"
              [checked]="row.selected"
              (change)="toggleRowSelection(row)"
              (click)="$event.stopPropagation()"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            >
          </td>
          
          <!-- Data Cells -->
          <td 
            *ngFor="let column of columns; trackBy: trackByColumnFn"
            [class]="getCellClasses(column)"
          >
            <!-- Regular Content -->
            <div *ngIf="column.type !== 'action'" class="flex items-center">
              <span [innerHTML]="formatCellValue(row.data[column.key], column)"></span>
            </div>
            
            <!-- Action Buttons -->
            <div *ngIf="column.type === 'action'" class="flex items-center space-x-2">
              <button
                *ngFor="let action of row.data[column.key]"
                (click)="onActionClick(row, action); $event.stopPropagation()"
                class="inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                {{ action.label }}
              </button>
            </div>
          </td>
        </tr>
        
        <!-- Empty State -->
        <tr *ngIf="visibleData.length === 0">
          <td [attr.colspan]="columns.length + (selectable ? 1 : 0)" class="px-6 py-8 text-center text-gray-500">
            <div class="flex flex-col items-center">
              <svg class="h-12 w-12 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              <p class="text-sm">No data available</p>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Table Footer / Pagination -->
  <div *ngIf="showFooter || paginated" class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
    <div class="flex items-center justify-between">
      <!-- Results Info -->
      <div class="flex-1 flex justify-between sm:hidden">
        <p class="text-sm text-gray-700">
          Showing {{ ((currentPage - 1) * pageSize) + 1 }} to {{ getMinValue(currentPage * pageSize, data.length) }} of {{ data.length }} results
        </p>
      </div>
      
      <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
          <p class="text-sm text-gray-700">
            Showing
            <span class="font-medium">{{ ((currentPage - 1) * pageSize) + 1 }}</span>
            to
            <span class="font-medium">{{ getMinValue(currentPage * pageSize, data.length) }}</span>
            of
            <span class="font-medium">{{ data.length }}</span>
            results
          </p>
        </div>
        
        <!-- Pagination -->
        <div *ngIf="paginated && totalPages > 1">
          <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
            <!-- Previous Page -->
            <button
              [disabled]="currentPage === 1"
              (click)="onPageChange(currentPage - 1)"
              class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span class="sr-only">Previous</span>
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </button>
            
            <!-- Page Numbers -->
            <button
              *ngFor="let page of [].constructor(totalPages); let i = index"
              [class.bg-blue-50]="currentPage === i + 1"
              [class.border-blue-500]="currentPage === i + 1"
              [class.text-blue-600]="currentPage === i + 1"
              (click)="onPageChange(i + 1)"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              {{ i + 1 }}
            </button>
            
            <!-- Next Page -->
            <button
              [disabled]="currentPage === totalPages"
              (click)="onPageChange(currentPage + 1)"
              class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span class="sr-only">Next</span>
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </button>
          </nav>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Content Slot -->
  <div *ngIf="showFooter">
    <ng-content select="[slot=footer]"></ng-content>
  </div>
</div>
