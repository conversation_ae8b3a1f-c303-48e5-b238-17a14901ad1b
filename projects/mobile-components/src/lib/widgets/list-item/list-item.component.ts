import { Component, Input, ContentChild, ElementRef, OnInit, Inject, Optional, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { HeadingComponent } from '../../base/heading/heading.component';
import { ParagraphComponent } from '../../base/paragraph/paragraph.component';

@Component({
  selector: 'base-list-item',
  templateUrl: './list-item.component.html',
  styleUrls: ['./list-item.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    HeadingComponent,
    ParagraphComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class ListItemComponent implements OnInit {
  /**
   * The title of the list item.
   */
  @Input() title: string = '';

  /**
   * The subtitle of the list item.
   */
  @Input() subtitle: string = '';

  /**
   * Unique identifier for the list item.
   */
  @Input() item_id: string = '';

  /**
   * Custom CSS classes for the list item container.
   */
  @Input() className: string = 'flex items-center gap-3 p-3 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200';

  /**
   * Custom CSS classes for additional styling.
   */
  @Input() class?: string;

  /**
   * Custom inline styles.
   */
  @Input() style?: string;

  /**
   * Whether the list item is clickable/interactive.
   */
  @Input() clickable: boolean = false;

  /**
   * Whether the list item is disabled.
   */
  @Input() disabled: boolean = false;

  /**
   * Whether the list item is selected/active.
   */
  @Input() selected: boolean = false;

  /**
   * Router link for navigation when clicked.
   */
  @Input() routerLink?: string;

  /**
   * Icon to display before the content.
   */
  @Input() icon?: string;

  /**
   * Icon classes for styling the icon.
   */
  @Input() iconClass: string = 'w-5 h-5 text-gray-400';

  /**
   * Avatar image URL to display instead of icon.
   */
  @Input() avatar?: string;

  /**
   * Avatar classes for styling the avatar.
   */
  @Input() avatarClass: string = 'w-8 h-8 rounded-full object-cover';

  /**
   * Badge text to display.
   */
  @Input() badge?: string;

  /**
   * Badge classes for styling the badge.
   */
  @Input() badgeClass: string = 'px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full';

  /**
   * Title heading size.
   */
  @Input() titleSize: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';

  /**
   * Title heading weight.
   */
  @Input() titleWeight: 'light' | 'normal' | 'medium' | 'semibold' | 'bold' = 'medium';

  /**
   * Subtitle text size.
   */
  @Input() subtitleSize: 'xs' | 'sm' | 'md' | 'lg' = 'sm';

  /**
   * Custom title classes.
   */
  @Input() titleClass: string = 'text-gray-900 dark:text-gray-100';

  /**
   * Custom subtitle classes.
   */
  @Input() subtitleClass: string = 'text-gray-500 dark:text-gray-400';

  /**
   * Whether to show a divider after this item.
   */
  @Input() showDivider: boolean = false;

  /**
   * Divider classes.
   */
  @Input() dividerClass: string = 'border-b border-gray-200 dark:border-gray-700';

  @ContentChild('title') titleSlot?: ElementRef;
  @ContentChild('subtitle') subtitleSlot?: ElementRef;

  constructor(@Optional() @Inject('componentProperties') public properties: any) {}

  ngOnInit(): void {
    if (this.properties) {
      // Map properties from injected properties
      this.title = this.properties.title || this.title;
      this.subtitle = this.properties.subtitle || this.subtitle;
      this.item_id = this.properties.item_id || this.item_id;
      this.className = this.properties.className || this.className;
      this.class = this.properties.class || this.class;
      this.style = this.properties.style || this.style;
      this.clickable = this.properties.clickable !== undefined ? this.properties.clickable : this.clickable;
      this.disabled = this.properties.disabled !== undefined ? this.properties.disabled : this.disabled;
      this.selected = this.properties.selected !== undefined ? this.properties.selected : this.selected;
      this.routerLink = this.properties.routerLink || this.routerLink;
      this.icon = this.properties.icon || this.icon;
      this.iconClass = this.properties.iconClass || this.iconClass;
      this.avatar = this.properties.avatar || this.avatar;
      this.avatarClass = this.properties.avatarClass || this.avatarClass;
      this.badge = this.properties.badge || this.badge;
      this.badgeClass = this.properties.badgeClass || this.badgeClass;
      this.titleSize = this.properties.titleSize || this.titleSize;
      this.titleWeight = this.properties.titleWeight || this.titleWeight;
      this.subtitleSize = this.properties.subtitleSize || this.subtitleSize;
      this.titleClass = this.properties.titleClass || this.titleClass;
      this.subtitleClass = this.properties.subtitleClass || this.subtitleClass;
      this.showDivider = this.properties.showDivider !== undefined ? this.properties.showDivider : this.showDivider;
      this.dividerClass = this.properties.dividerClass || this.dividerClass;
    }
  }

  get hasTitle(): boolean {
    return Boolean(this.title || this.titleSlot);
  }

  get hasSubtitle(): boolean {
    return Boolean(this.subtitle || this.subtitleSlot);
  }

  get finalClasses(): string {
    let classes = this.className;
    
    if (this.clickable) {
      classes += ' cursor-pointer';
    }
    
    if (this.disabled) {
      classes += ' opacity-50 cursor-not-allowed';
    }
    
    if (this.selected) {
      classes += ' bg-blue-50 dark:bg-blue-900/20 border-l-4 border-l-blue-500';
    }
    
    if (this.class) {
      classes += ' ' + this.class;
    }
    
    return classes;
  }
}
