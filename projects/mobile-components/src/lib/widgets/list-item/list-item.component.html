<li 
  [class]="finalClasses"
  [style]="style"
  [attr.id]="item_id"
  [routerLink]="routerLink"
  [attr.aria-disabled]="disabled"
  [attr.aria-selected]="selected"
>
  <!-- Icon or Avatar -->
  <div *ngIf="icon || avatar" class="flex-shrink-0">
    <img 
      *ngIf="avatar" 
      [src]="avatar" 
      [class]="avatarClass"
      alt="Avatar"
    />
    <i 
      *ngIf="icon && !avatar" 
      [class]="icon + ' ' + iconClass"
      aria-hidden="true"
    ></i>
  </div>

  <!-- Content Area -->
  <div class="flex-1 min-w-0">
    <ng-content></ng-content>

    <div *ngIf="hasTitle || hasSubtitle" class="space-y-1">
      <base-heading
        *ngIf="hasTitle"
        as="h6"
        [weight]="titleWeight"
        [size]="titleSize"
        lead="tight"
        [class]="titleClass"
      >
        <ng-content select="[slot='title']"></ng-content>
        {{ title }}
      </base-heading>
      <base-paragraph
        *ngIf="hasSubtitle"
        [size]="subtitleSize"
        [class]="subtitleClass"
      >
        <ng-content select="[slot='subtitle']"></ng-content>
        {{ subtitle }}
      </base-paragraph>
    </div>
  </div>

  <!-- End Content -->
  <div class="flex-shrink-0 flex items-center gap-2">
    <!-- Badge -->
    <span 
      *ngIf="badge" 
      [class]="badgeClass"
    >
      {{ badge }}
    </span>

    <!-- End Slot -->
    <ng-content select="[slot='end']"></ng-content>
  </div>

  <!-- Divider -->
  <div 
    *ngIf="showDivider" 
    [class]="dividerClass"
  ></div>
</li>
