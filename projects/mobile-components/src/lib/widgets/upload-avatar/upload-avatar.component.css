/* Upload Avatar Component Styles */

:host {
  display: block;
}

/* Ensure smooth transitions for all interactive elements */
* {
  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Custom focus styles for accessibility */
.upload-area:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Smooth progress bar animation */
.progress-bar {
  transition: width 0.3s ease-in-out;
}

/* Custom drag over styles */
.drag-over {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .8;
  }
}

/* Loading spinner animation */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Hover effects for buttons */
button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

button:active:not(:disabled) {
  transform: translateY(0);
}

/* Custom styles for file input accessibility */
input[type="file"]:focus + label {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .upload-area {
    border-color: rgba(75, 85, 99, 0.3);
  }
  
  .upload-area:hover {
    border-color: rgba(75, 85, 99, 0.5);
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .action-buttons {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .action-buttons button {
    width: 100%;
  }
}