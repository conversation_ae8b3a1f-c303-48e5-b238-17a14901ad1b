import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

// Interface for avatar upload events
export interface AvatarUploadEvent {
  file: File;
  preview: string;
}

// Interface for avatar crop data
export interface AvatarCropData {
  x: number;
  y: number;
  width: number;
  height: number;
}

@Component({
  selector: 'base-upload-avatar',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './upload-avatar.component.html',
  styleUrls: ['./upload-avatar.component.css'],
})
export class UploadAvatarComponent {
  // Standard Tailwind inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'full';

  // Component-specific inputs
  @Input() currentAvatar: string = '';
  @Input() placeholder: string = 'Upload Avatar';
  @Input() maxFileSize: number = 5 * 1024 * 1024; // 5MB default
  @Input() acceptedTypes: string[] = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  @Input() showPreview: boolean = true;
  @Input() allowCrop: boolean = true;
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() showProgressBar: boolean = true;
  @Input() uploadText: string = 'Choose file or drag here';
  @Input() changeText: string = 'Change avatar';
  @Input() removeText: string = 'Remove avatar';
  @Input() dragActiveText: string = 'Drop file to upload';
  @Input() errorText: string = '';

  // Events
  @Output() fileSelected = new EventEmitter<AvatarUploadEvent>();
  @Output() cropDataChanged = new EventEmitter<AvatarCropData>();
  @Output() uploadComplete = new EventEmitter<string>();
  @Output() uploadError = new EventEmitter<string>();
  @Output() removed = new EventEmitter<void>();

  // Component state
  isDragOver = false;
  isUploading = false;
  uploadProgress = 0;
  previewUrl = '';
  currentError = '';
  cropData: AvatarCropData | null = null;

  ngOnInit() {
    this.previewUrl = this.currentAvatar;
    this.currentError = this.errorText;
  }

  // Computed classes for the container
  get containerClasses(): string {
    const baseClasses = 'relative flex flex-col items-center justify-center transition-all duration-200';
    const sizeClasses = this.getSizeClasses();
    const variantClasses = this.getVariantClasses();
    const roundedClasses = this.getRoundedClasses();
    const stateClasses = this.getStateClasses();
    
    return `${baseClasses} ${sizeClasses} ${variantClasses} ${roundedClasses} ${stateClasses} ${this.className}`.trim();
  }

  // Computed classes for the upload area
  get uploadAreaClasses(): string {
    const baseClasses = 'w-full h-full border-2 border-dashed flex flex-col items-center justify-center cursor-pointer transition-all duration-200';
    const stateClasses = this.isDragOver 
      ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20' 
      : this.disabled 
        ? 'border-gray-300 bg-gray-100 dark:border-gray-600 dark:bg-gray-800 cursor-not-allowed'
        : 'border-gray-300 hover:border-gray-400 dark:border-gray-600 dark:hover:border-gray-500';
    
    return `${baseClasses} ${stateClasses}`.trim();
  }

  // Computed classes for the preview image
  get previewClasses(): string {
    const baseClasses = 'object-cover';
    const sizeClasses = this.getSizeClasses();
    const roundedClasses = this.getRoundedClasses();
    
    return `${baseClasses} ${sizeClasses} ${roundedClasses}`.trim();
  }

  private getSizeClasses(): string {
    const sizeMap = {
      xs: 'w-16 h-16',
      sm: 'w-20 h-20',
      md: 'w-24 h-24',
      lg: 'w-32 h-32',
      xl: 'w-40 h-40'
    };
    return sizeMap[this.size] || sizeMap.md;
  }

  private getVariantClasses(): string {
    const variantMap = {
      default: 'bg-gray-50 dark:bg-gray-800',
      primary: 'bg-blue-50 dark:bg-blue-900/20',
      secondary: 'bg-gray-100 dark:bg-gray-700',
      success: 'bg-green-50 dark:bg-green-900/20',
      warning: 'bg-yellow-50 dark:bg-yellow-900/20',
      danger: 'bg-red-50 dark:bg-red-900/20'
    };
    return variantMap[this.variant] || variantMap.default;
  }

  private getRoundedClasses(): string {
    const roundedMap = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };
    return roundedMap[this.rounded] || roundedMap.full;
  }

  private getStateClasses(): string {
    if (this.disabled) return 'opacity-60 cursor-not-allowed';
    if (this.isUploading) return 'opacity-75';
    return '';
  }

  // File handling methods
  onFileSelected(event: Event): void {
    if (this.disabled || this.isUploading) return;
    
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.handleFile(input.files[0]);
    }
  }

  onDrop(event: DragEvent): void {
    if (this.disabled || this.isUploading) return;
    
    event.preventDefault();
    this.isDragOver = false;
    
    if (event.dataTransfer?.files && event.dataTransfer.files.length > 0) {
      this.handleFile(event.dataTransfer.files[0]);
    }
  }

  onDragOver(event: DragEvent): void {
    if (this.disabled || this.isUploading) return;
    
    event.preventDefault();
    this.isDragOver = true;
  }

  onDragLeave(event: DragEvent): void {
    if (this.disabled || this.isUploading) return;
    
    event.preventDefault();
    this.isDragOver = false;
  }

  private handleFile(file: File): void {
    this.currentError = '';
    
    // Validate file type
    if (!this.acceptedTypes.includes(file.type)) {
      this.currentError = `Invalid file type. Accepted types: ${this.acceptedTypes.join(', ')}`;
      this.uploadError.emit(this.currentError);
      return;
    }
    
    // Validate file size
    if (file.size > this.maxFileSize) {
      this.currentError = `File too large. Maximum size: ${(this.maxFileSize / (1024 * 1024)).toFixed(1)}MB`;
      this.uploadError.emit(this.currentError);
      return;
    }
    
    // Create preview URL
    const reader = new FileReader();
    reader.onload = (e) => {
      this.previewUrl = e.target?.result as string;
      
      // Emit file selected event
      this.fileSelected.emit({
        file: file,
        preview: this.previewUrl
      });
      
      // Simulate upload process if needed
      if (this.showProgressBar) {
        this.simulateUpload();
      }
    };
    reader.readAsDataURL(file);
  }

  private simulateUpload(): void {
    this.isUploading = true;
    this.uploadProgress = 0;
    
    const interval = setInterval(() => {
      this.uploadProgress += 10;
      
      if (this.uploadProgress >= 100) {
        clearInterval(interval);
        this.isUploading = false;
        this.uploadProgress = 0;
        this.uploadComplete.emit(this.previewUrl);
      }
    }, 100);
  }

  removeAvatar(): void {
    if (this.disabled) return;
    
    this.previewUrl = '';
    this.currentError = '';
    this.removed.emit();
  }

  // Utility methods
  get hasAvatar(): boolean {
    return !!this.previewUrl;
  }

  get showUploadArea(): boolean {
    return !this.hasAvatar || this.isUploading;
  }

  get progressPercentage(): string {
    return `${this.uploadProgress}%`;
  }

  get acceptedTypesDisplay(): string {
    return this.acceptedTypes.join(', ').replace(/image\//g, '').toUpperCase();
  }

  get maxFileSizeDisplay(): string {
    return (this.maxFileSize / (1024 * 1024)).toFixed(1);
  }
}
