<!-- Upload Avatar Component -->
<div [class]="containerClasses" role="button" [attr.aria-label]="placeholder" [attr.tabindex]="disabled ? -1 : 0">
  <!-- Current Avatar Preview -->
  <div *ngIf="hasAvatar && !isUploading" class="relative group">
    <img 
      [src]="previewUrl" 
      [alt]="placeholder"
      [class]="previewClasses"
      role="img"
    />
    
    <!-- Avatar Actions Overlay -->
    <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center rounded-full">
      <div class="flex gap-2">
        <!-- Change Avatar Button -->
        <button
          type="button"
          class="px-3 py-1 bg-white text-gray-800 text-sm rounded-md hover:bg-gray-100 transition-colors duration-200"
          (click)="$event.stopPropagation(); fileInput.click()"
          [disabled]="disabled"
          [attr.aria-label]="changeText"
        >
          <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"/>
          </svg>
          {{ changeText }}
        </button>
        
        <!-- Remove Avatar Button -->
        <button
          type="button"
          class="px-3 py-1 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors duration-200"
          (click)="$event.stopPropagation(); removeAvatar()"
          [disabled]="disabled"
          [attr.aria-label]="removeText"
        >
          <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
          </svg>
          {{ removeText }}
        </button>
      </div>
    </div>
  </div>

  <!-- Upload Area -->
  <div 
    *ngIf="showUploadArea"
    [class]="uploadAreaClasses"
    (click)="!disabled && !isUploading ? fileInput.click() : null"
    (drop)="onDrop($event)"
    (dragover)="onDragOver($event)"
    (dragleave)="onDragLeave($event)"
    [attr.aria-label]="isDragOver ? dragActiveText : uploadText"
  >
    <!-- Upload Icon and Text -->
    <div *ngIf="!isUploading" class="flex flex-col items-center justify-center text-center p-4">
      <!-- Upload Icon -->
      <svg class="w-8 h-8 mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
      </svg>
      
      <!-- Upload Text -->
      <p class="text-sm text-gray-600 dark:text-gray-300 mb-1">
        {{ isDragOver ? dragActiveText : uploadText }}
      </p>
      
      <!-- File Type Info -->
      <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
        {{ acceptedTypesDisplay }} 
        (Max {{ maxFileSizeDisplay }}MB)
      </p>
    </div>

    <!-- Upload Progress -->
    <div *ngIf="isUploading" class="flex flex-col items-center justify-center p-4 w-full">
      <!-- Loading Spinner -->
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-2"></div>
      
      <!-- Progress Text -->
      <p class="text-sm text-gray-600 dark:text-gray-300 mb-2">Uploading...</p>
      
      <!-- Progress Bar -->
      <div *ngIf="showProgressBar" class="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
        <div 
          class="bg-blue-600 h-2 rounded-full transition-all duration-300"
          [style.width]="progressPercentage"
          role="progressbar"
          [attr.aria-valuenow]="uploadProgress"
          [attr.aria-valuemin]="0"
          [attr.aria-valuemax]="100"
        ></div>
      </div>
      
      <!-- Progress Percentage -->
      <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ progressPercentage }}</p>
    </div>
  </div>

  <!-- Error Message -->
  <div *ngIf="currentError" 
       class="mt-2 text-sm text-red-600 dark:text-red-400 text-center"
       role="alert"
       [attr.aria-live]="'polite'">
    <svg class="w-4 h-4 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
      <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
    </svg>
    {{ currentError }}
  </div>

  <!-- Required Indicator -->
  <div *ngIf="required && !hasAvatar" 
       class="absolute -top-1 -right-1 text-red-500 text-lg"
       aria-label="Required field">
    *
  </div>

  <!-- Hidden File Input -->
  <input
    #fileInput
    type="file"
    class="hidden"
    [accept]="acceptedTypes.join(',')"
    [disabled]="disabled || isUploading"
    (change)="onFileSelected($event)"
    [attr.aria-label]="placeholder"
  />
</div>
