import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CommonModule } from '@angular/common';

import { UploadAvatarComponent, AvatarUploadEvent, AvatarCropData } from './upload-avatar.component';

describe('UploadAvatarComponent', () => {
  let component: UploadAvatarComponent;
  let fixture: ComponentFixture<UploadAvatarComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [UploadAvatarComponent, CommonModule]
    }).compileComponents();
    
    fixture = TestBed.createComponent(UploadAvatarComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have default input values', () => {
    expect(component.className).toBe('');
    expect(component.size).toBe('md');
    expect(component.variant).toBe('default');
    expect(component.rounded).toBe('full');
    expect(component.placeholder).toBe('Upload Avatar');
    expect(component.maxFileSize).toBe(5 * 1024 * 1024);
    expect(component.showPreview).toBe(true);
    expect(component.allowCrop).toBe(true);
    expect(component.disabled).toBe(false);
  });

  it('should generate correct container classes', () => {
    component.size = 'lg';
    component.variant = 'primary';
    component.rounded = 'md';
    component.className = 'custom-class';
    
    const classes = component.containerClasses;
    expect(classes).toContain('w-32 h-32');
    expect(classes).toContain('bg-blue-50');
    expect(classes).toContain('rounded-md');
    expect(classes).toContain('custom-class');
  });

  it('should handle file selection', () => {
    spyOn(component.fileSelected, 'emit');
    
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const event = { target: { files: [file] } } as any;
    
    component.onFileSelected(event);
    
    expect(component.fileSelected.emit).toHaveBeenCalled();
  });

  it('should validate file types', () => {
    spyOn(component.uploadError, 'emit');
    
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });
    component['handleFile'](file);
    
    expect(component.uploadError.emit).toHaveBeenCalled();
    expect(component.currentError).toContain('Invalid file type');
  });

  it('should validate file size', () => {
    spyOn(component.uploadError, 'emit');
    
    const file = new File(['x'.repeat(6 * 1024 * 1024)], 'large.jpg', { type: 'image/jpeg' });
    component['handleFile'](file);
    
    expect(component.uploadError.emit).toHaveBeenCalled();
    expect(component.currentError).toContain('File too large');
  });

  it('should handle drag and drop events', () => {
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const dataTransfer = { files: [file] } as any;
    const event = { preventDefault: jasmine.createSpy(), dataTransfer } as any;
    
    spyOn(component, 'handleFile' as any);
    
    component.onDrop(event);
    
    expect(event.preventDefault).toHaveBeenCalled();
    expect(component.isDragOver).toBe(false);
    expect(component['handleFile']).toHaveBeenCalledWith(file);
  });

  it('should remove avatar', () => {
    spyOn(component.removed, 'emit');
    
    component.previewUrl = 'test-url';
    component.removeAvatar();
    
    expect(component.previewUrl).toBe('');
    expect(component.currentError).toBe('');
    expect(component.removed.emit).toHaveBeenCalled();
  });

  it('should show upload area when no avatar', () => {
    component.previewUrl = '';
    expect(component.showUploadArea).toBe(true);
  });

  it('should show avatar when preview URL exists', () => {
    component.previewUrl = 'test-url';
    expect(component.hasAvatar).toBe(true);
  });

  it('should handle disabled state', () => {
    component.disabled = true;
    
    const event = { target: { files: [] } } as any;
    component.onFileSelected(event);
    
    // Should not process files when disabled
    expect(component.previewUrl).toBe('');
  });

  it('should emit upload complete event', (done) => {
    component.uploadComplete.subscribe((url: string) => {
      expect(url).toBeTruthy();
      done();
    });
    
    component['simulateUpload']();
  });
});
