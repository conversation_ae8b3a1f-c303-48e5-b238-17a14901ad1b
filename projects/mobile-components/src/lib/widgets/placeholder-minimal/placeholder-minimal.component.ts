import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'lib-placeholder-minimal',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './placeholder-minimal.component.html',
  styleUrl: './placeholder-minimal.component.css'
})
export class PlaceholderMinimalComponent {
  // Standard inputs for customization
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() lines: number = 3;
  @Input() showAvatar: boolean = false;
  @Input() showButton: boolean = false;
  @Input() animated: boolean = true;
  @Input() width: 'full' | 'auto' | string = 'full';
  @Input() height: 'auto' | string = 'auto';

  // Computed getter for CSS classes
  get computedClasses(): string {
    const baseClasses = 'placeholder-minimal-widget';
    
    const sizeClasses = {
      xs: 'space-y-1',
      sm: 'space-y-2',
      md: 'space-y-3',
      lg: 'space-y-4',
      xl: 'space-y-5'
    };

    const variantClasses = {
      default: 'bg-gray-100',
      primary: 'bg-blue-100',
      secondary: 'bg-gray-200',
      success: 'bg-green-100',
      warning: 'bg-yellow-100',
      danger: 'bg-red-100'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const animatedClass = this.animated ? 'animate-pulse' : '';
    
    return [
      baseClasses,
      sizeClasses[this.size],
      animatedClass,
      this.className
    ].filter(Boolean).join(' ');
  }

  get lineClasses(): string {
    const variantClasses = {
      default: 'bg-gray-200',
      primary: 'bg-blue-200',
      secondary: 'bg-gray-300',
      success: 'bg-green-200',
      warning: 'bg-yellow-200',
      danger: 'bg-red-200'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const sizeClasses = {
      xs: 'h-2',
      sm: 'h-3',
      md: 'h-4',
      lg: 'h-5',
      xl: 'h-6'
    };

    return [
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      sizeClasses[this.size]
    ].join(' ');
  }

  get avatarClasses(): string {
    const sizeClasses = {
      xs: 'w-6 h-6',
      sm: 'w-8 h-8',
      md: 'w-10 h-10',
      lg: 'w-12 h-12',
      xl: 'w-16 h-16'
    };

    const variantClasses = {
      default: 'bg-gray-200',
      primary: 'bg-blue-200',
      secondary: 'bg-gray-300',
      success: 'bg-green-200',
      warning: 'bg-yellow-200',
      danger: 'bg-red-200'
    };

    return [
      sizeClasses[this.size],
      variantClasses[this.variant],
      'rounded-full'
    ].join(' ');
  }

  get buttonClasses(): string {
    const sizeClasses = {
      xs: 'h-6 w-16',
      sm: 'h-8 w-20',
      md: 'h-10 w-24',
      lg: 'h-12 w-32',
      xl: 'h-14 w-40'
    };

    const variantClasses = {
      default: 'bg-gray-200',
      primary: 'bg-blue-200',
      secondary: 'bg-gray-300',
      success: 'bg-green-200',
      warning: 'bg-yellow-200',
      danger: 'bg-red-200'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    return [
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded]
    ].join(' ');
  }

  getLineWidth(index: number): string {
    // Vary line widths for more realistic placeholder
    const widths = ['w-full', 'w-3/4', 'w-5/6', 'w-2/3', 'w-4/5'];
    return widths[index % widths.length];
  }
}
