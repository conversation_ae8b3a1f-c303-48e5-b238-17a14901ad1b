import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface InboxMessage {
  id: string;
  sender: string;
  senderAvatar?: string;
  subject: string;
  content: string;
  timestamp: Date;
  isRead: boolean;
  isStarred: boolean;
  hasAttachments: boolean;
  priority: 'low' | 'normal' | 'high';
  tags?: string[];
}

@Component({
  selector: 'lib-inbox-message',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './inbox-message.component.html',
  styleUrl: './inbox-message.component.css'
})
export class InboxMessageComponent {
  // Standard inputs for customization
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() message: InboxMessage = {
    id: '1',
    sender: '<PERSON>',
    senderAvatar: '',
    subject: 'Important Project Update',
    content: 'Please review the latest project deliverables and provide your feedback by end of week.',
    timestamp: new Date(),
    isRead: false,
    isStarred: false,
    hasAttachments: true,
    priority: 'normal',
    tags: ['project', 'urgent']
  };

  @Input() showAvatar: boolean = true;
  @Input() showTimestamp: boolean = true;
  @Input() showPriority: boolean = true;
  @Input() showTags: boolean = true;
  @Input() showActions: boolean = true;
  @Input() compactView: boolean = false;
  @Input() selectable: boolean = false;
  @Input() selected: boolean = false;

  // Event outputs
  @Output() messageClick = new EventEmitter<InboxMessage>();
  @Output() toggleRead = new EventEmitter<InboxMessage>();
  @Output() toggleStar = new EventEmitter<InboxMessage>();
  @Output() deleteMessage = new EventEmitter<InboxMessage>();
  @Output() selectionChange = new EventEmitter<{message: InboxMessage, selected: boolean}>();

  // Computed getter for CSS classes
  get computedClasses(): string {
    const baseClasses = 'inbox-message-widget';
    
    const sizeClasses = {
      xs: 'p-2 text-xs',
      sm: 'p-3 text-sm',
      md: 'p-4 text-base',
      lg: 'p-5 text-lg',
      xl: 'p-6 text-xl'
    };

    const variantClasses = {
      default: 'bg-white border-gray-200',
      primary: 'bg-blue-50 border-blue-200',
      secondary: 'bg-gray-50 border-gray-300',
      success: 'bg-green-50 border-green-200',
      warning: 'bg-yellow-50 border-yellow-200',
      danger: 'bg-red-50 border-red-200'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const readClass = this.message.isRead ? 'message-read' : 'message-unread';
    const selectedClass = this.selected ? 'message-selected' : '';
    const compactClass = this.compactView ? 'compact-view' : '';
    
    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      readClass,
      selectedClass,
      compactClass,
      'border hover:shadow-md transition-all duration-200 cursor-pointer',
      this.className
    ].filter(Boolean).join(' ');
  }

  get priorityClasses(): string {
    const priorityMap = {
      low: 'text-gray-500',
      normal: 'text-blue-500',
      high: 'text-red-500'
    };
    return priorityMap[this.message.priority];
  }

  get priorityIcon(): string {
    const iconMap = {
      low: 'fas fa-arrow-down',
      normal: 'fas fa-minus',
      high: 'fas fa-exclamation-triangle'
    };
    return iconMap[this.message.priority];
  }

  get avatarDisplayName(): string {
    return this.message.sender.split(' ').map(n => n[0]).join('').toUpperCase();
  }

  get timeAgo(): string {
    const diff = Date.now() - this.message.timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  }

  onMessageClick(): void {
    this.messageClick.emit(this.message);
  }

  onToggleRead(event: Event): void {
    event.stopPropagation();
    this.message.isRead = !this.message.isRead;
    this.toggleRead.emit(this.message);
  }

  onToggleStar(event: Event): void {
    event.stopPropagation();
    this.message.isStarred = !this.message.isStarred;
    this.toggleStar.emit(this.message);
  }

  onDelete(event: Event): void {
    event.stopPropagation();
    this.deleteMessage.emit(this.message);
  }

  onSelectionChange(event: Event): void {
    event.stopPropagation();
    this.selected = !this.selected;
    this.selectionChange.emit({message: this.message, selected: this.selected});
  }
}
