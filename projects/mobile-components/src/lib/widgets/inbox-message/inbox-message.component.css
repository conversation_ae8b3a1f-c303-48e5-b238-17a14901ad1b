/* Inbox Message Widget Styles */
.inbox-message-widget {
  position: relative;
  background: white;
  transition: all 0.2s ease-in-out;
}

/* Read/Unread states */
.inbox-message-widget.message-unread {
  background-color: #fafbfc;
  border-left: 3px solid #3b82f6;
}

.inbox-message-widget.message-read {
  background-color: white;
}

/* Selected state */
.inbox-message-widget.message-selected {
  background-color: #eff6ff;
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

/* Hover effects */
.inbox-message-widget:hover {
  background-color: #f9fafb;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.inbox-message-widget.message-selected:hover {
  background-color: #dbeafe;
}

/* Compact view adjustments */
.inbox-message-widget.compact-view {
  padding: 0.5rem 1rem;
}

.inbox-message-widget.compact-view .flex.items-start {
  align-items: center;
}

/* Line clamp utility for content preview */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Action buttons styling */
.inbox-message-widget button {
  transition: all 0.2s ease-in-out;
  border-radius: 0.25rem;
}

.inbox-message-widget button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(1.1);
}

.inbox-message-widget button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Avatar hover effect */
.inbox-message-widget img,
.inbox-message-widget .w-10.h-10.bg-gray-300 {
  transition: transform 0.2s ease-in-out;
}

.inbox-message-widget:hover img,
.inbox-message-widget:hover .w-10.h-10.bg-gray-300 {
  transform: scale(1.05);
}

/* Tag styling enhancements */
.inbox-message-widget .bg-blue-100 {
  transition: all 0.2s ease-in-out;
}

.inbox-message-widget .bg-blue-100:hover {
  background-color: #dbeafe;
  transform: scale(1.05);
}

/* Unread indicator animation */
.inbox-message-widget .absolute.left-0.w-1 {
  animation: pulse-blue 2s infinite;
}

@keyframes pulse-blue {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Focus within for keyboard navigation */
.inbox-message-widget:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .inbox-message-widget .flex.items-center.space-x-2 {
    flex-direction: column;
    align-items: flex-start;
    space-x: 0;
    gap: 0.25rem;
  }
  
  .inbox-message-widget .flex.items-center.justify-between {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .inbox-message-widget .flex.flex-wrap.gap-1 {
    margin-top: 0.5rem;
  }
}

/* Print styles */
@media print {
  .inbox-message-widget button {
    display: none;
  }
  
  .inbox-message-widget {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}