<!-- Inbox Message Widget -->
<div [class]="computedClasses" (click)="onMessageClick()">
  <!-- Selection checkbox -->
  <div *ngIf="selectable" class="absolute top-4 left-4 z-10">
    <input 
      type="checkbox"
      [checked]="selected"
      (change)="onSelectionChange($event)"
      class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
      aria-label="Select message">
  </div>

  <div class="flex items-start space-x-3" [class.pl-8]="selectable">
    <!-- Avatar -->
    <div *ngIf="showAvatar" class="flex-shrink-0">
      <div *ngIf="message.senderAvatar; else defaultAvatar">
        <img 
          [src]="message.senderAvatar" 
          [alt]="message.sender"
          class="w-10 h-10 rounded-full object-cover">
      </div>
      <ng-template #defaultAvatar>
        <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 font-medium text-sm">
          {{ avatarDisplayName }}
        </div>
      </ng-template>
    </div>

    <!-- Message Content -->
    <div class="flex-1 min-w-0">
      <!-- Header -->
      <div class="flex items-center justify-between mb-1">
        <div class="flex items-center space-x-2">
          <h4 class="text-sm font-medium text-gray-900 truncate">{{ message.sender }}</h4>
          
          <!-- Priority indicator -->
          <div *ngIf="showPriority && message.priority !== 'normal'" 
               class="flex items-center">
            <i [class]="priorityIcon + ' ' + priorityClasses + ' text-xs'" 
               [attr.aria-label]="message.priority + ' priority'"></i>
          </div>

          <!-- Attachments indicator -->
          <div *ngIf="message.hasAttachments" class="flex items-center">
            <i class="fas fa-paperclip text-gray-500 text-xs" aria-label="Has attachments"></i>
          </div>
        </div>

        <!-- Timestamp -->
        <div *ngIf="showTimestamp" class="flex items-center space-x-2">
          <span class="text-xs text-gray-500">{{ timeAgo }}</span>
        </div>
      </div>

      <!-- Subject -->
      <div class="mb-1">
        <h5 class="text-sm font-medium text-gray-900 truncate" 
            [class.font-semibold]="!message.isRead">
          {{ message.subject }}
        </h5>
      </div>

      <!-- Content Preview -->
      <div *ngIf="!compactView" class="mb-2">
        <p class="text-sm text-gray-600 line-clamp-2">{{ message.content }}</p>
      </div>

      <!-- Tags -->
      <div *ngIf="showTags && message.tags && message.tags.length > 0" class="flex flex-wrap gap-1 mb-2">
        <span *ngFor="let tag of message.tags" 
              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {{ tag }}
        </span>
      </div>
    </div>

    <!-- Actions -->
    <div *ngIf="showActions" class="flex-shrink-0 flex items-center space-x-2">
      <!-- Star button -->
      <button 
        type="button"
        (click)="onToggleStar($event)"
        class="p-1 text-gray-400 hover:text-yellow-500 transition-colors"
        [class.text-yellow-500]="message.isStarred"
        [attr.aria-label]="message.isStarred ? 'Remove star' : 'Add star'">
        <i [class]="message.isStarred ? 'fas fa-star' : 'far fa-star'"></i>
      </button>

      <!-- Read/Unread button -->
      <button 
        type="button"
        (click)="onToggleRead($event)"
        class="p-1 text-gray-400 hover:text-blue-500 transition-colors"
        [attr.aria-label]="message.isRead ? 'Mark as unread' : 'Mark as read'">
        <i [class]="message.isRead ? 'fas fa-envelope-open' : 'fas fa-envelope'"></i>
      </button>

      <!-- Delete button -->
      <button 
        type="button"
        (click)="onDelete($event)"
        class="p-1 text-gray-400 hover:text-red-500 transition-colors"
        aria-label="Delete message">
        <i class="fas fa-trash"></i>
      </button>
    </div>
  </div>

  <!-- Unread indicator -->
  <div *ngIf="!message.isRead" 
       class="absolute left-0 top-0 bottom-0 w-1 bg-blue-500 rounded-l" 
       aria-hidden="true"></div>
</div>
