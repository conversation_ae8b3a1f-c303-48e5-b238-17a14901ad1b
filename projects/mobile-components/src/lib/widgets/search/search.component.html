<div [class]="computedClasses">
  <!-- Search Input Container -->
  <div [class]="inputContainerClasses">
    <!-- Search Icon -->
    <i *ngIf="searchIcon" [class]="searchIcon + ' ' + iconClasses" aria-hidden="true"></i>
    
    <!-- Input Field -->
    <input
      type="text"
      [class]="inputClasses"
      [placeholder]="placeholder"
      [value]="value"
      [disabled]="disabled"
      [readonly]="readonly"
      [attr.maxlength]="maxLength"
      [autocomplete]="autocomplete"
      (input)="onInput($event)"
      (focus)="onFocus($event)"
      (blur)="onBlur($event)"
      (keydown)="onKeyDown($event)"
    />
    
    <!-- Loading Spinner -->
    <div *ngIf="loading" class="mx-2">
      <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
    </div>
    
    <!-- Clear Button -->
    <button
      *ngIf="clearable && value && !loading"
      type="button"
      [class]="iconClasses"
      (click)="onClear()"
      [disabled]="disabled"
      aria-label="Clear search"
    >
      <i [class]="clearIcon" aria-hidden="true"></i>
    </button>
    
    <!-- Search Button -->
    <button
      *ngIf="showSearchButton"
      type="button"
      [class]="buttonClasses"
      (click)="onSearchClick()"
      [disabled]="disabled"
    >
      {{ searchButtonText }}
    </button>
  </div>

  <!-- Search Results Dropdown -->
  <div *ngIf="showResults && results.length > 0" [class]="resultsClasses">
    <div
      *ngFor="let result of results; trackBy: trackByFn"
      class="px-4 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
      (click)="onResultClick(result)"
    >
      <div class="text-sm">
        {{ result[resultDisplayProperty] || result }}
      </div>
    </div>
  </div>

  <!-- No Results -->
  <div *ngIf="showResults && results.length === 0 && !loading" [class]="resultsClasses">
    <div class="px-4 py-3 text-sm text-gray-500 text-center">
      {{ noResultsText }}
    </div>
  </div>
</div>