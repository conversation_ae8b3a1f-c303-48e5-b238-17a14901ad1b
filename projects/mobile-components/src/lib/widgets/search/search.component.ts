import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'lib-search',
  templateUrl: './search.component.html',
  styleUrls: ['./search.component.css'],
  standalone: true,
  imports: [CommonModule, FormsModule]
})
export class SearchComponent {
  @Input() placeholder: string = 'Search...';
  @Input() value: string = '';
  @Input() disabled: boolean = false;
  @Input() readonly: boolean = false;
  @Input() clearable: boolean = true;
  @Input() searchIcon: string = 'fa fa-search';
  @Input() clearIcon: string = 'fa fa-times';
  @Input() showSearchButton: boolean = false;
  @Input() searchButtonText: string = 'Search';
  @Input() debounceTime: number = 300;
  @Input() minLength: number = 0;
  @Input() maxLength: number = 255;
  @Input() autocomplete: string = 'off';
  @Input() showResults: boolean = false;
  @Input() results: any[] = [];
  @Input() resultDisplayProperty: string = 'name';
  @Input() noResultsText: string = 'No results found';
  @Input() loading: boolean = false;
  
  // Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  @Output() valueChange = new EventEmitter<string>();
  @Output() search = new EventEmitter<string>();
  @Output() clear = new EventEmitter<void>();
  @Output() focus = new EventEmitter<FocusEvent>();
  @Output() blur = new EventEmitter<FocusEvent>();
  @Output() resultSelect = new EventEmitter<any>();

  private debounceTimer: any;

  get computedClasses(): string {
    const baseClasses = [
      'search-widget',
      'relative',
      'w-full'
    ];

    if (this.className) {
      baseClasses.push(this.className);
    }

    return baseClasses.join(' ');
  }

  get inputContainerClasses(): string {
    const baseClasses = [
      'relative',
      'flex',
      'items-center',
      'border',
      'transition-all',
      'duration-200'
    ];

    // Size classes
    const sizeClasses = {
      xs: ['text-xs'],
      sm: ['text-sm'],
      md: ['text-base'],
      lg: ['text-lg'],
      xl: ['text-xl']
    };

    // Variant classes
    const variantClasses = {
      default: ['border-gray-300', 'bg-white', 'focus-within:border-gray-500'],
      primary: ['border-blue-300', 'bg-white', 'focus-within:border-blue-500'],
      secondary: ['border-gray-400', 'bg-white', 'focus-within:border-gray-600'],
      success: ['border-green-300', 'bg-white', 'focus-within:border-green-500'],
      warning: ['border-yellow-300', 'bg-white', 'focus-within:border-yellow-500'],
      danger: ['border-red-300', 'bg-white', 'focus-within:border-red-500']
    };

    // Rounded classes
    const roundedClasses = {
      none: [],
      sm: ['rounded-sm'],
      md: ['rounded-md'],
      lg: ['rounded-lg'],
      full: ['rounded-full']
    };

    const classes = [
      ...baseClasses,
      ...sizeClasses[this.size],
      ...variantClasses[this.variant],
      ...roundedClasses[this.rounded]
    ];

    if (this.disabled) {
      classes.push('bg-gray-100', 'cursor-not-allowed');
    }

    return classes.join(' ');
  }

  get inputClasses(): string {
    const baseClasses = [
      'flex-1',
      'border-0',
      'outline-none',
      'bg-transparent',
      'placeholder-gray-500'
    ];

    // Size classes for padding
    const sizeClasses = {
      xs: ['py-1', 'px-2'],
      sm: ['py-2', 'px-3'],
      md: ['py-2', 'px-3'],
      lg: ['py-3', 'px-4'],
      xl: ['py-4', 'px-5']
    };

    const classes = [
      ...baseClasses,
      ...sizeClasses[this.size]
    ];

    if (this.disabled) {
      classes.push('cursor-not-allowed');
    }

    return classes.join(' ');
  }

  get iconClasses(): string {
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };

    return `${sizeClasses[this.size]} text-gray-400 cursor-pointer mx-2`;
  }

  get buttonClasses(): string {
    const baseClasses = [
      'px-4',
      'py-2',
      'border-l',
      'bg-gray-50',
      'text-gray-700',
      'hover:bg-gray-100',
      'transition-colors',
      'duration-200'
    ];

    // Size classes
    const sizeClasses = {
      xs: ['text-xs', 'px-2', 'py-1'],
      sm: ['text-sm', 'px-3', 'py-1'],
      md: ['text-sm', 'px-4', 'py-2'],
      lg: ['text-base', 'px-5', 'py-2'],
      xl: ['text-lg', 'px-6', 'py-3']
    };

    const classes = [
      ...baseClasses,
      ...sizeClasses[this.size]
    ];

    if (this.disabled) {
      classes.push('opacity-50', 'cursor-not-allowed');
    }

    return classes.join(' ');
  }

  get resultsClasses(): string {
    return `
      absolute top-full left-0 right-0 z-50 mt-1 
      bg-white border border-gray-200 rounded-md shadow-lg 
      max-h-60 overflow-auto
    `;
  }

  onInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    const newValue = target.value;
    
    this.value = newValue;
    this.valueChange.emit(newValue);

    // Clear previous timer
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    // Set new timer for debounced search
    if (newValue.length >= this.minLength) {
      this.debounceTimer = setTimeout(() => {
        this.search.emit(newValue);
      }, this.debounceTime);
    }
  }

  onSearchClick(): void {
    if (!this.disabled && this.value.length >= this.minLength) {
      this.search.emit(this.value);
    }
  }

  onClear(): void {
    this.value = '';
    this.valueChange.emit('');
    this.clear.emit();
    this.showResults = false;
  }

  onFocus(event: FocusEvent): void {
    this.focus.emit(event);
  }

  onBlur(event: FocusEvent): void {
    // Delay hiding results to allow for result selection
    setTimeout(() => {
      this.showResults = false;
      this.blur.emit(event);
    }, 200);
  }

  onResultClick(result: any): void {
    this.resultSelect.emit(result);
    this.value = result[this.resultDisplayProperty] || result;
    this.valueChange.emit(this.value);
    this.showResults = false;
  }

  onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
      this.onSearchClick();
    } else if (event.key === 'Escape') {
      this.showResults = false;
    }
  }

  trackByFn(index: number, item: any): any {
    return item.id || item[this.resultDisplayProperty] || index;
  }
}