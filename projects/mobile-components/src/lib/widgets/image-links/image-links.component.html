<div [class]="containerClasses" role="region" aria-label="Image links gallery">
  <div *ngFor="let image of images; trackBy: trackByImageId" 
       class="group relative overflow-hidden"
       [class.break-inside-avoid]="layout === 'masonry'">
    
    <!-- Image Container -->
    <div class="relative overflow-hidden" [class.rounded-md]="rounded !== 'none'">
      <!-- Link Wrapper -->
      <a *ngIf="image.href; else imageOnly"
         [href]="image.href"
         [target]="getImageTarget(image)"
         [rel]="getImageTarget(image) === '_blank' ? 'noopener noreferrer' : ''"
         [title]="image.title || image.alt"
         class="block focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
         (click)="onImageClick(image, $event)">
        <img
          [src]="image.src"
          [alt]="image.alt"
          [class]="imageClasses"
          [loading]="loadingStrategy"
          (load)="onImageLoad(image, $event)"
          (error)="onImageError(image, $event)">
      </a>

      <!-- Image without link -->
      <ng-template #imageOnly>
        <img
          [src]="image.src"
          [alt]="image.alt"
          [class]="imageClasses"
          [loading]="loadingStrategy"
          (load)="onImageLoad(image, $event)"
          (error)="onImageError(image, $event)"
          (click)="onImageClick(image, $event)"
          [class.cursor-pointer]="!image.href"
          tabindex="0"
          role="button"
          [attr.aria-label]="image.title || image.alt">
      </ng-template>

      <!-- Badge Overlay -->
      <div *ngIf="showBadges && image.badge" 
           class="absolute top-2 left-2 px-2 py-1 text-xs font-medium text-white bg-black bg-opacity-70 rounded-md">
        {{ image.badge }}
      </div>

      <!-- Hover Overlay -->
      <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
        <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div *ngIf="showCaptions || showAuthors" class="p-3 bg-white">
      <!-- Title -->
      <h3 *ngIf="image.title" class="font-semibold text-gray-900 mb-1 line-clamp-2">
        {{ image.title }}
      </h3>

      <!-- Caption -->
      <p *ngIf="showCaptions && image.caption" 
         class="text-sm text-gray-600 mb-2 line-clamp-3">
        {{ image.caption }}
      </p>

      <!-- Description -->
      <p *ngIf="image.description" 
         class="text-xs text-gray-500 mb-2 line-clamp-2">
        {{ image.description }}
      </p>

      <!-- Author -->
      <div *ngIf="showAuthors && image.author" 
           class="flex items-center text-xs text-gray-500">
        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
        </svg>
        <span>{{ image.author }}</span>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="images.length === 0" 
       class="col-span-full flex flex-col items-center justify-center py-12 text-gray-500">
    <svg class="w-12 h-12 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
    </svg>
    <p class="text-lg font-medium mb-1">No images available</p>
    <p class="text-sm">Add some images to display in the gallery</p>
  </div>
</div>
