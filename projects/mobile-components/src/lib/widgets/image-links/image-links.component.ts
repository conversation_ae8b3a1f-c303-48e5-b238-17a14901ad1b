import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface ImageLink {
  id?: string;
  src: string;
  alt: string;
  title?: string;
  href?: string;
  target?: '_blank' | '_self' | '_parent' | '_top';
  caption?: string;
  author?: string;
  badge?: string;
  description?: string;
}

@Component({
  selector: 'lib-image-links',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './image-links.component.html',
  styleUrl: './image-links.component.css'
})
export class ImageLinksComponent {
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  
  // Component-specific properties
  @Input() images: ImageLink[] = [
    {
      id: '1',
      src: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop',
      alt: 'Mountain landscape',
      title: 'Beautiful Mountain View',
      href: '#mountain-view',
      caption: 'Stunning mountain landscape captured at sunrise',
      author: 'Nature Photographer',
      badge: 'Featured'
    },
    {
      id: '2',
      src: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',
      alt: 'Forest trail',
      title: 'Forest Adventure',
      href: '#forest-trail',
      caption: 'Peaceful forest trail perfect for hiking',
      author: 'Adventure Seeker',
      badge: 'Popular'
    },
    {
      id: '3',
      src: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop',
      alt: 'Ocean view',
      title: 'Ocean Sunset',
      href: '#ocean-sunset',
      caption: 'Magnificent sunset over the ocean waves',
      author: 'Travel Blogger'
    }
  ];
  @Input() layout: 'grid' | 'masonry' | 'list' = 'grid';
  @Input() columns: number = 3;
  @Input() showCaptions: boolean = true;
  @Input() showAuthors: <AUTHORS>
  @Input() showBadges: boolean = true;
  @Input() hoverEffect: 'none' | 'zoom' | 'fade' | 'lift' = 'zoom';
  @Input() aspectRatio: 'square' | 'landscape' | 'portrait' | 'auto' = 'landscape';
  @Input() loadingStrategy: 'eager' | 'lazy' = 'lazy';
  @Input() openInNewTab: boolean = false;

  // Events
  @Output() imageClick = new EventEmitter<ImageLink>();
  @Output() imageLoad = new EventEmitter<{ image: ImageLink; event: Event }>();
  @Output() imageError = new EventEmitter<{ image: ImageLink; event: Event }>();

  get containerClasses(): string {
    const sizeClasses = {
      xs: 'gap-1',
      sm: 'gap-2',
      md: 'gap-4',
      lg: 'gap-6',
      xl: 'gap-8'
    };

    const variantClasses = {
      default: 'bg-white',
      primary: 'bg-blue-50',
      secondary: 'bg-gray-50',
      success: 'bg-green-50',
      warning: 'bg-yellow-50',
      danger: 'bg-red-50'
    };

    const roundedClasses = {
      none: '',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const layoutClasses = {
      grid: `grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-${this.columns}`,
      masonry: 'columns-1 sm:columns-2 lg:columns-3 break-inside-avoid',
      list: 'flex flex-col'
    };

    return `
      ${layoutClasses[this.layout]}
      ${sizeClasses[this.size]}
      ${variantClasses[this.variant]}
      ${roundedClasses[this.rounded]}
      ${this.className}
    `.trim().replace(/\s+/g, ' ');
  }

  get imageClasses(): string {
    const hoverClasses = {
      none: '',
      zoom: 'hover:scale-105',
      fade: 'hover:opacity-75',
      lift: 'hover:shadow-lg hover:-translate-y-1'
    };

    const aspectClasses = {
      square: 'aspect-square',
      landscape: 'aspect-video',
      portrait: 'aspect-[3/4]',
      auto: ''
    };

    const roundedClasses = {
      none: '',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    return `
      w-full object-cover transition-all duration-300
      ${aspectClasses[this.aspectRatio]}
      ${hoverClasses[this.hoverEffect]}
      ${roundedClasses[this.rounded]}
    `.trim().replace(/\s+/g, ' ');
  }

  onImageClick(image: ImageLink, event: Event): void {
    if (!image.href) {
      event.preventDefault();
    }
    this.imageClick.emit(image);
  }

  onImageLoad(image: ImageLink, event: Event): void {
    this.imageLoad.emit({ image, event });
  }

  onImageError(image: ImageLink, event: Event): void {
    this.imageError.emit({ image, event });
  }

  getImageTarget(image: ImageLink): string {
    return image.target || (this.openInNewTab ? '_blank' : '_self');
  }

  trackByImageId(index: number, image: ImageLink): string {
    return image.id || index.toString();
  }
}
