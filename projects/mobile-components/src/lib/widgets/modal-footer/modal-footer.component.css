/* Modal Footer Component Styles */
.modal-footer-widget {
  /* Base styles for the modal footer */
  min-height: 60px;
}

.btn {
  /* Ensure buttons have consistent styling */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  white-space: nowrap;
  user-select: none;
}

.btn:disabled {
  pointer-events: none;
}

/* Animation for loading spinner */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Accessibility improvements */
.modal-footer-widget:focus-within {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
}