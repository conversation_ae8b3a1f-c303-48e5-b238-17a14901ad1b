import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

// Interface for action buttons
export interface ModalAction {
  id: string;
  label: string;
  type: 'primary' | 'secondary' | 'danger' | 'success' | 'warning';
  disabled?: boolean;
  loading?: boolean;
  icon?: string;
}

@Component({
  selector: 'lib-modal-footer',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './modal-footer.component.html',
  styleUrl: './modal-footer.component.css'
})
export class ModalFooterComponent {
  // Content inputs
  @Input() actions: ModalAction[] = [
    { id: 'cancel', label: 'Cancel', type: 'secondary' },
    { id: 'confirm', label: 'Confirm', type: 'primary' }
  ];
  @Input() alignment: 'left' | 'center' | 'right' | 'space-between' = 'right';
  @Input() showDivider: boolean = true;
  @Input() sticky: boolean = false;
  
  // Standard Tailwind inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'none';
  
  // Events
  @Output() actionClick = new EventEmitter<{ action: ModalAction; event: Event }>();
  @Output() cancelClick = new EventEmitter<Event>();
  @Output() confirmClick = new EventEmitter<Event>();

  get computedClasses(): string {
    const baseClasses = ['modal-footer-widget', 'flex', 'items-center', 'w-full'];
    
    // Size classes for padding and spacing
    const sizeClasses = {
      xs: ['p-2', 'gap-2'],
      sm: ['p-3', 'gap-2'],
      md: ['p-4', 'gap-3'],
      lg: ['p-5', 'gap-4'],
      xl: ['p-6', 'gap-4']
    };

    // Variant background colors
    const variantClasses = {
      default: ['bg-white', 'text-gray-900'],
      primary: ['bg-blue-50', 'text-blue-900'],
      secondary: ['bg-gray-50', 'text-gray-900'],
      success: ['bg-green-50', 'text-green-900'],
      warning: ['bg-yellow-50', 'text-yellow-900'],
      danger: ['bg-red-50', 'text-red-900']
    };

    // Alignment classes
    const alignmentClasses = {
      left: ['justify-start'],
      center: ['justify-center'],
      right: ['justify-end'],
      'space-between': ['justify-between']
    };

    // Rounded classes
    const roundedClasses = {
      none: [],
      sm: ['rounded-sm'],
      md: ['rounded-md'],
      lg: ['rounded-lg'],
      full: ['rounded-full']
    };

    const classes = [
      ...baseClasses,
      ...sizeClasses[this.size],
      ...variantClasses[this.variant],
      ...alignmentClasses[this.alignment],
      ...roundedClasses[this.rounded]
    ];

    // Add divider
    if (this.showDivider) {
      classes.push('border-t', 'border-gray-200');
    }

    // Add sticky positioning
    if (this.sticky) {
      classes.push('sticky', 'bottom-0', 'z-10');
    }

    // Add custom classes
    if (this.className) {
      classes.push(this.className);
    }

    return classes.join(' ');
  }

  getButtonClasses(action: ModalAction): string {
    const baseClasses = ['btn', 'transition-all', 'duration-200', 'font-medium', 'focus:outline-none', 'focus:ring-2', 'focus:ring-offset-2'];
    
    // Size-based classes
    const sizeClasses = {
      xs: ['px-2', 'py-1', 'text-xs', 'rounded'],
      sm: ['px-3', 'py-1.5', 'text-sm', 'rounded'],
      md: ['px-4', 'py-2', 'text-sm', 'rounded-md'],
      lg: ['px-6', 'py-2.5', 'text-base', 'rounded-md'],
      xl: ['px-8', 'py-3', 'text-lg', 'rounded-lg']
    };

    // Action type classes
    const typeClasses = {
      primary: ['bg-blue-600', 'text-white', 'hover:bg-blue-700', 'focus:ring-blue-500'],
      secondary: ['bg-gray-200', 'text-gray-900', 'hover:bg-gray-300', 'focus:ring-gray-500'],
      danger: ['bg-red-600', 'text-white', 'hover:bg-red-700', 'focus:ring-red-500'],
      success: ['bg-green-600', 'text-white', 'hover:bg-green-700', 'focus:ring-green-500'],
      warning: ['bg-yellow-600', 'text-white', 'hover:bg-yellow-700', 'focus:ring-yellow-500']
    };

    const classes = [
      ...baseClasses,
      ...sizeClasses[this.size],
      ...typeClasses[action.type]
    ];

    // Disabled state
    if (action.disabled || action.loading) {
      classes.push('opacity-50', 'cursor-not-allowed');
    }

    return classes.join(' ');
  }

  onActionClick(action: ModalAction, event: Event): void {
    if (action.disabled || action.loading) {
      event.preventDefault();
      return;
    }

    this.actionClick.emit({ action, event });

    // Emit specific events for common actions
    if (action.id === 'cancel') {
      this.cancelClick.emit(event);
    } else if (action.id === 'confirm') {
      this.confirmClick.emit(event);
    }
  }

  trackByActionId(index: number, action: ModalAction): string {
    return action.id;
  }
}
