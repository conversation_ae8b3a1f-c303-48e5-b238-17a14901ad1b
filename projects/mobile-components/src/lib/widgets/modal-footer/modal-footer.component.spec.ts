import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ModalFooterComponent, ModalAction } from './modal-footer.component';

describe('ModalFooterComponent', () => {
  let component: ModalFooterComponent;
  let fixture: ComponentFixture<ModalFooterComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ModalFooterComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ModalFooterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should render default actions', () => {
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.textContent).toContain('Cancel');
    expect(compiled.textContent).toContain('Confirm');
  });

  it('should apply custom actions', () => {
    const customActions: ModalAction[] = [
      { id: 'save', label: 'Save', type: 'primary' },
      { id: 'delete', label: 'Delete', type: 'danger' }
    ];
    component.actions = customActions;
    fixture.detectChanges();

    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.textContent).toContain('Save');
    expect(compiled.textContent).toContain('Delete');
  });

  it('should emit action clicks', () => {
    spyOn(component.actionClick, 'emit');
    const button = fixture.nativeElement.querySelector('button') as HTMLButtonElement;
    button.click();

    expect(component.actionClick.emit).toHaveBeenCalled();
  });

  it('should apply size classes correctly', () => {
    component.size = 'lg';
    fixture.detectChanges();

    expect(component.computedClasses).toContain('p-5');
    expect(component.computedClasses).toContain('gap-4');
  });

  it('should apply variant classes correctly', () => {
    component.variant = 'primary';
    fixture.detectChanges();

    expect(component.computedClasses).toContain('bg-blue-50');
    expect(component.computedClasses).toContain('text-blue-900');
  });

  it('should handle disabled actions', () => {
    const disabledAction: ModalAction = { id: 'disabled', label: 'Disabled', type: 'secondary', disabled: true };
    component.actions = [disabledAction];
    fixture.detectChanges();

    const button = fixture.nativeElement.querySelector('button') as HTMLButtonElement;
    expect(button.disabled).toBe(true);
    expect(button.classList.contains('opacity-50')).toBe(true);
  });
});
