<div [class]="containerClasses" 
     role="region" 
     [attr.aria-label]="'Company overview for ' + company.name"
     (click)="onCompanyClick()">
  
  <!-- Company Header -->
  <div class="flex items-start space-x-4 mb-6">
    <!-- Company Logo -->
    <div class="flex-shrink-0">
      <img *ngIf="company.logo" 
           [src]="company.logo" 
           [alt]="company.name + ' logo'"
           class="w-16 h-16 rounded-lg object-cover border border-gray-200">
      <div *ngIf="!company.logo" 
           class="w-16 h-16 rounded-lg bg-gray-300 flex items-center justify-center">
        <span class="text-gray-600 font-bold text-lg">
          {{ company.name.charAt(0).toUpperCase() }}
        </span>
      </div>
    </div>

    <!-- Company Info -->
    <div class="flex-1 min-w-0">
      <div class="flex items-center space-x-2 mb-2">
        <h1 class="text-2xl font-bold text-gray-900 truncate">{{ company.name }}</h1>
        <span *ngIf="company.verified" 
              class="flex-shrink-0 w-5 h-5 text-blue-500" 
              title="Verified company">
          <svg fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
          </svg>
        </span>
      </div>

      <div class="flex flex-wrap items-center gap-2 text-sm text-gray-600 mb-2">
        <span class="flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
          </svg>
          {{ company.industry }}
        </span>
        <span class="text-gray-400">•</span>
        <span>Founded {{ company.founded }}</span>
        <span class="text-gray-400">•</span>
        <span>{{ formatEmployeeCount(company.employeeCount) }} employees</span>
      </div>

      <div class="flex items-center space-x-4">
        <button *ngIf="company.website" 
                (click)="onWebsiteClick(); $event.stopPropagation()"
                class="text-sm text-blue-600 hover:text-blue-800 hover:underline transition-colors duration-200">
          {{ company.website.replace('https://', '').replace('http://', '') }}
        </button>
        
        <!-- Stock Info -->
        <div *ngIf="showStock && company.publiclyTraded && company.stockSymbol" 
             class="flex items-center space-x-2 text-sm">
          <span class="font-medium text-gray-700">{{ company.stockSymbol }}</span>
          <span class="text-green-600 font-medium">${{ company.stockPrice }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Company Description -->
  <div *ngIf="company.description" class="mb-6">
    <p class="text-gray-700 leading-relaxed">{{ company.description }}</p>
  </div>

  <!-- Key Metrics -->
  <div *ngIf="showMetrics && company.metrics.length > 0" class="mb-6">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Key Metrics</h3>
    <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
      <div *ngFor="let metric of company.metrics; trackBy: trackByMetricLabel"
           class="bg-gray-50 rounded-lg p-4 text-center">
        <div class="text-2xl font-bold" [class]="getMetricColor(metric)">
          {{ metric.value }}
        </div>
        <div class="text-sm text-gray-600 mt-1">{{ metric.label }}</div>
        <div *ngIf="metric.change !== undefined" 
             class="text-xs mt-2 flex items-center justify-center"
             [class]="getChangeColor(metric.changeType)">
          <svg class="w-3 h-3 mr-1" 
               [class.rotate-180]="metric.changeType === 'decrease'"
               fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 17l10-10M17 7l10 10"/>
          </svg>
          {{ metric.change }}%
        </div>
      </div>
    </div>
  </div>

  <!-- Leadership Team -->
  <div *ngIf="showExecutives && company.executives.length > 0" class="mb-6">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Leadership Team</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <div *ngFor="let executive of visibleExecutives; trackBy: trackByExecutiveId"
           class="flex items-start space-x-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200 cursor-pointer"
           (click)="onExecutiveClick(executive); $event.stopPropagation()">
        
        <!-- Executive Photo -->
        <div class="flex-shrink-0">
          <img *ngIf="executive.photo" 
               [src]="executive.photo" 
               [alt]="executive.name"
               class="w-12 h-12 rounded-full object-cover">
          <div *ngIf="!executive.photo" 
               class="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center">
            <span class="text-gray-600 font-medium">
              {{ getExecutiveInitials(executive.name) }}
            </span>
          </div>
        </div>

        <!-- Executive Info -->
        <div class="flex-1 min-w-0">
          <h4 class="font-medium text-gray-900 truncate">{{ executive.name }}</h4>
          <p class="text-sm text-gray-600 truncate">{{ executive.title }}</p>
          <p *ngIf="executive.bio" class="text-xs text-gray-500 mt-1 line-clamp-2">
            {{ executive.bio }}
          </p>
        </div>
      </div>
    </div>
    
    <div *ngIf="company.executives.length > maxExecutives" 
         class="text-center mt-4">
      <span class="text-sm text-gray-500">
        +{{ company.executives.length - maxExecutives }} more executives
      </span>
    </div>
  </div>

  <!-- Global Locations -->
  <div *ngIf="showLocations && company.locations.length > 0" class="mb-6">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Global Presence</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div *ngFor="let location of company.locations; trackBy: trackByLocationId"
           class="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200 cursor-pointer"
           (click)="onLocationClick(location); $event.stopPropagation()">
        
        <div class="flex-shrink-0 pt-1">
          <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
          </svg>
        </div>
        
        <div class="flex-1">
          <div class="flex items-center space-x-2">
            <h4 class="font-medium text-gray-900">{{ location.city }}, {{ location.country }}</h4>
            <span *ngIf="location.isHeadquarters" 
                  class="px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-full">
              HQ
            </span>
          </div>
          <p *ngIf="location.address" class="text-sm text-gray-600 mt-1">{{ location.address }}</p>
          <p *ngIf="location.employeeCount" class="text-sm text-gray-500 mt-1">
            {{ formatEmployeeCount(location.employeeCount) }} employees
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Tags -->
  <div *ngIf="showTags && company.tags && company.tags.length > 0" class="mb-6">
    <h3 class="text-lg font-semibold text-gray-900 mb-3">Specializations</h3>
    <div class="flex flex-wrap gap-2">
      <button *ngFor="let tag of company.tags"
              class="px-3 py-1 text-sm font-medium text-blue-700 bg-blue-100 rounded-full hover:bg-blue-200 transition-colors duration-200"
              (click)="onTagClick(tag); $event.stopPropagation()">
        {{ tag }}
      </button>
    </div>
  </div>

  <!-- Social Links -->
  <div *ngIf="showSocial && company.socialLinks && company.socialLinks.length > 0" class="mb-4">
    <h3 class="text-lg font-semibold text-gray-900 mb-3">Connect</h3>
    <div class="flex space-x-4">
      <a *ngFor="let social of company.socialLinks"
         [href]="social.url"
         target="_blank"
         rel="noopener noreferrer"
         class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
         [title]="social.platform"
         (click)="onSocialClick(social); $event.stopPropagation()">
        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
          <!-- LinkedIn -->
          <path *ngIf="social.platform === 'LinkedIn'" 
                d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
          <!-- Twitter -->
          <path *ngIf="social.platform === 'Twitter'" 
                d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
          <!-- GitHub -->
          <path *ngIf="social.platform === 'GitHub'" 
                d="M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12"/>
        </svg>
      </a>
    </div>
  </div>

  <!-- Quick Stats Footer (Compact Mode) -->
  <div *ngIf="layout === 'compact'" 
       class="pt-4 border-t border-gray-200 flex justify-between items-center text-sm text-gray-600">
    <span>{{ company.headquarters }}</span>
    <span *ngIf="company.revenue">{{ company.revenue }} revenue</span>
    <span>{{ formatEmployeeCount(company.employeeCount) }} employees</span>
  </div>
</div>
