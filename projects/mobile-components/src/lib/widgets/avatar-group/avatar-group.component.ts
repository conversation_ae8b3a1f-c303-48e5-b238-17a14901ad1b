import {
  Component,
  Input,
  Output,
  EventEmitter,
  computed,
  ContentChild,
  ElementRef,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { AvatarComponent } from '../../base/avatar/avatar.component';

// Interface for avatar data structure
export interface AvatarData {
  id: string;
  src?: string;
  srcDark?: string;
  text?: string;
  name?: string;
  initials?: string;
  alt?: string;
  color?: 'white' | 'muted' | 'primary' | 'success' | 'info' | 'warning' | 'danger' | 'pink' | 'yellow' | 'indigo' | 'violet';
  ring?: boolean | 'primary' | 'success' | 'info' | 'warning' | 'danger' | 'pink' | 'yellow';
  dot?: boolean | string;
  badge?: {
    src?: string;
    text?: string;
    color?: string;
  };
  tooltip?: string;
  clickable?: boolean;
}

@Component({
  selector: 'base-avatar-group',
  templateUrl: './avatar-group.component.html',
  styleUrls: ['./avatar-group.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    AvatarComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class AvatarGroupComponent {
  // Standard Tailwind inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'full';

  // Component-specific inputs
  @Input() avatars: AvatarData[] = [
    {
      id: '1',
      name: 'John Doe',
      initials: 'JD',
      color: 'primary',
      tooltip: 'John Doe',
      clickable: true
    },
    {
      id: '2',
      name: 'Jane Smith',
      initials: 'JS',
      color: 'success',
      tooltip: 'Jane Smith',
      clickable: true
    },
    {
      id: '3',
      name: 'Bob Wilson',
      initials: 'BW',
      color: 'warning',
      tooltip: 'Bob Wilson',
      clickable: true
    }
  ];
  @Input() limit?: number = 3;
  @Input() maxDisplay: number = 5;
  @Input() spacing: 'tight' | 'normal' | 'loose' = 'normal';
  @Input() direction: 'left' | 'right' = 'right';
  @Input() showTooltips: boolean = true;
  @Input() showCount: boolean = true;
  @Input() stackDirection: 'ltr' | 'rtl' = 'ltr';
  @Input() clickableAvatars: boolean = true;
  @Input() hoverEffect: boolean = true;
  @Input() borderWidth: 'none' | 'thin' | 'thick' = 'thin';
  @Input() loading: boolean = false;
  @Input() loadingCount: number = 3;
  @Input() emptyText: string = 'No avatars';
  @Input() showEmptyState: boolean = true;

  // Legacy support - deprecated but maintained for backward compatibility
  @Input() item_id: string = Math.random().toString(36).substring(7);
  @Input() classes?: {
    wrapper?: string | string[];
    outer?: string | string[];
    count?: string | string[];
  } = {};

  // Event outputs
  @Output() avatarClick = new EventEmitter<AvatarData>();
  @Output() avatarHover = new EventEmitter<AvatarData>();
  @Output() countClick = new EventEmitter<{ hiddenCount: number; hiddenAvatars: AvatarData[] }>();
  
  // Legacy output for backward compatibility
  @Output() avatarSelected = new EventEmitter<string | null>();

  @ContentChild('before') beforeContent!: ElementRef;
  @ContentChild('after') afterContent!: ElementRef;

  // Computed properties for dynamic styling
  containerClasses = computed(() => {
    const classes = [
      'avatar-group-container',
      `avatar-group-size-${this.size}`,
      `avatar-group-variant-${this.variant}`,
      `avatar-group-spacing-${this.spacing}`,
      `avatar-group-direction-${this.direction}`,
      `avatar-group-stack-${this.stackDirection}`,
      `avatar-group-border-${this.borderWidth}`
    ];

    if (this.hoverEffect) classes.push('hover-effect');
    if (this.loading) classes.push('loading');
    if (this.className) classes.push(this.className);

    return classes.join(' ');
  });

  avatarClasses = computed(() => {
    const classes = [
      'avatar-wrapper',
      `rounded-${this.rounded}`,
      `size-${this.size}`
    ];

    if (this.clickableAvatars) classes.push('clickable');
    if (this.hoverEffect) classes.push('hover-effect');

    return classes.join(' ');
  });

  countClasses = computed(() => {
    const classes = [
      'avatar-count',
      `size-${this.size}`,
      `variant-${this.variant}`,
      `rounded-${this.rounded}`
    ];

    if (this.clickableAvatars) classes.push('clickable');

    return classes.join(' ');
  });

  // Display logic for avatars
  displayAvatars = computed(() => {
    if (this.loading) {
      return Array(this.loadingCount).fill(null).map((_, index) => ({
        id: `loading-${index}`,
        loading: true,
        initials: '',
        clickable: false
      }));
    }

    if (!this.avatars || this.avatars.length === 0) {
      return [];
    }

    const effectiveLimit = this.limit || this.maxDisplay;
    
    if (this.avatars.length <= effectiveLimit) {
      return this.avatars;
    }

    return this.avatars.slice(0, effectiveLimit - 1);
  });

  hiddenAvatars = computed(() => {
    if (this.loading || !this.avatars || this.avatars.length === 0) {
      return [];
    }

    const effectiveLimit = this.limit || this.maxDisplay;
    
    if (this.avatars.length <= effectiveLimit) {
      return [];
    }

    return this.avatars.slice(effectiveLimit - 1);
  });

  hiddenCount = computed(() => {
    return this.hiddenAvatars().length;
  });

  shouldShowCount = computed(() => {
    return this.showCount && this.hiddenCount() > 0;
  });

  shouldShowEmptyState = computed(() => {
    return this.showEmptyState && !this.loading && 
           (!this.avatars || this.avatars.length === 0);
  });

  // Avatar size mapping for the base avatar component
  getAvatarSize(): 'xs' | 'sm' | 'md' | 'lg' | 'xl' {
    const sizeMap: Record<string, 'xs' | 'sm' | 'md' | 'lg' | 'xl'> = {
      'xs': 'xs',
      'sm': 'sm', 
      'md': 'md',
      'lg': 'lg',
      'xl': 'xl'
    };
    return sizeMap[this.size] || 'md';
  }

  // Avatar color mapping based on variant
  getAvatarColor(avatar: AvatarData): 'white' | 'muted' | 'primary' | 'success' | 'info' | 'warning' | 'danger' | 'pink' | 'yellow' | 'indigo' | 'violet' {
    if (avatar.color) return avatar.color;
    
    const variantColorMap: Record<string, 'white' | 'muted' | 'primary' | 'success' | 'info' | 'warning' | 'danger' | 'pink' | 'yellow' | 'indigo' | 'violet'> = {
      'default': 'muted',
      'primary': 'primary',
      'secondary': 'muted',
      'success': 'success',
      'warning': 'warning',
      'danger': 'danger'
    };
    
    return variantColorMap[this.variant] || 'muted';
  }

  // Generate initials from name
  getInitials(avatar: AvatarData): string {
    if (avatar.initials) return avatar.initials;
    if (avatar.text) return avatar.text;
    if (avatar.name) {
      return avatar.name
        .split(' ')
        .map(word => word.charAt(0).toUpperCase())
        .slice(0, 2)
        .join('');
    }
    return '?';
  }

  // Event handlers
  onAvatarClick(avatar: AvatarData): void {
    if (!avatar.clickable && !this.clickableAvatars) return;
    
    this.avatarClick.emit(avatar);
    // Legacy support
    this.avatarSelected.emit(avatar.id);
  }

  onAvatarHover(avatar: AvatarData): void {
    this.avatarHover.emit(avatar);
  }

  onCountClick(): void {
    this.countClick.emit({
      hiddenCount: this.hiddenCount(),
      hiddenAvatars: this.hiddenAvatars()
    });
  }

  // TrackBy functions for performance optimization
  trackByAvatarId(index: number, avatar: AvatarData): string {
    return avatar.id || `avatar-${index}`;
  }

  trackByIndex(index: number): number {
    return index;
  }

  // Legacy support methods
  isString(value: any): boolean {
    return typeof value === 'string';
  }

  hasBeforeContent(): boolean {
    return !!this.beforeContent;
  }

  hasAfterContent(): boolean {
    return !!this.afterContent;
  }

  // Legacy sizes mapping for backward compatibility
  sizes: Record<string, string> = {
    xxs: 'nui-avatar-group-xxs',
    xs: 'nui-avatar-group-xs', 
    sm: 'nui-avatar-group-sm',
    md: 'nui-avatar-group-md',
    lg: 'nui-avatar-group-lg',
    xl: 'nui-avatar-group-lg',
    '2xl': 'nui-avatar-group-lg',
    '3xl': 'nui-avatar-group-lg',
    '4xl': 'nui-avatar-group-lg',
  };

  // Legacy computed for backward compatibility
  avatarDisplay = computed(() => {
    if (
      this.avatars &&
      this.limit !== undefined &&
      this.avatars.length > this.limit
    ) {
      return this.avatars.slice(0, this.limit - 1);
    }
    return this.avatars;
  });
}
