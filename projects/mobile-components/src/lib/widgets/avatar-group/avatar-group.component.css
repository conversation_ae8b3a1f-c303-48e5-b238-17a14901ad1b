/* Avatar Group Component Styles */

/* Container Styles */
.avatar-group-container {
  @apply relative flex items-center;
  @apply transition-all duration-200 ease-in-out;
}

/* Direction variants */
.avatar-group-direction-left .avatar-group-list {
  @apply flex-row-reverse;
}

.avatar-group-direction-right .avatar-group-list {
  @apply flex-row;
}

/* Stack direction variants */
.avatar-group-stack-rtl .avatar-group-list {
  @apply flex-row-reverse;
}

.avatar-group-stack-ltr .avatar-group-list {
  @apply flex-row;
}

/* Avatar Group List */
.avatar-group-list {
  @apply flex items-center relative;
}

/* Spacing variants */
.avatar-group-spacing-tight .avatar-wrapper {
  @apply -ml-3;
}

.avatar-group-spacing-tight .avatar-wrapper:first-child {
  @apply ml-0;
}

.avatar-group-spacing-normal .avatar-wrapper {
  @apply -ml-2;
}

.avatar-group-spacing-normal .avatar-wrapper:first-child {
  @apply ml-0;
}

.avatar-group-spacing-loose .avatar-wrapper {
  @apply -ml-1;
}

.avatar-group-spacing-loose .avatar-wrapper:first-child {
  @apply ml-0;
}

/* Avatar Wrapper */
.avatar-wrapper {
  @apply relative flex items-center justify-center;
  @apply transition-all duration-200 ease-in-out;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
}

/* Border width variants */
.avatar-group-border-none .avatar-wrapper {
  @apply ring-0;
}

.avatar-group-border-thin .avatar-wrapper {
  @apply ring-2 ring-white dark:ring-gray-800;
}

.avatar-group-border-thick .avatar-wrapper {
  @apply ring-4 ring-white dark:ring-gray-800;
}

/* Size variants */
.avatar-wrapper.size-xs {
  @apply w-6 h-6;
}

.avatar-wrapper.size-sm {
  @apply w-8 h-8;
}

.avatar-wrapper.size-md {
  @apply w-10 h-10;
}

.avatar-wrapper.size-lg {
  @apply w-12 h-12;
}

.avatar-wrapper.size-xl {
  @apply w-16 h-16;
}

/* Clickable avatars */
.avatar-wrapper.clickable {
  @apply cursor-pointer;
}

.avatar-wrapper.clickable:hover {
  @apply transform scale-105;
  @apply shadow-lg;
  @apply z-10;
}

.avatar-wrapper.clickable:focus {
  @apply ring-blue-500 dark:ring-blue-400;
  @apply z-10;
}

/* Hover effects */
.avatar-wrapper.hover-effect:hover {
  @apply transform scale-105;
  @apply transition-transform duration-150 ease-in-out;
}

/* Avatar item */
.avatar-item {
  @apply w-full h-full;
}

/* Count indicator */
.avatar-count {
  @apply relative flex items-center justify-center;
  @apply bg-gray-100 dark:bg-gray-700;
  @apply border-2 border-white dark:border-gray-800;
  @apply text-gray-600 dark:text-gray-300;
  @apply font-medium text-sm;
  @apply transition-all duration-200 ease-in-out;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
}

/* Count size variants */
.avatar-count.size-xs {
  @apply w-6 h-6 text-xs;
}

.avatar-count.size-sm {
  @apply w-8 h-8 text-xs;
}

.avatar-count.size-md {
  @apply w-10 h-10 text-sm;
}

.avatar-count.size-lg {
  @apply w-12 h-12 text-base;
}

.avatar-count.size-xl {
  @apply w-16 h-16 text-lg;
}

/* Count variant colors */
.avatar-count.variant-default {
  @apply bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300;
}

.avatar-count.variant-primary {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300;
}

.avatar-count.variant-secondary {
  @apply bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300;
}

.avatar-count.variant-success {
  @apply bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300;
}

.avatar-count.variant-warning {
  @apply bg-yellow-100 dark:bg-yellow-900 text-yellow-600 dark:text-yellow-300;
}

.avatar-count.variant-danger {
  @apply bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-300;
}

/* Count clickable */
.avatar-count.clickable {
  @apply cursor-pointer;
}

.avatar-count.clickable:hover {
  @apply transform scale-105;
  @apply shadow-lg;
  @apply bg-opacity-80;
}

.avatar-count.clickable:focus {
  @apply ring-blue-500 dark:ring-blue-400;
}

/* Count content */
.count-content {
  @apply flex items-center justify-center w-full h-full;
}

.count-text {
  @apply font-medium select-none;
}

/* Loading state */
.avatar-group-loading {
  @apply flex items-center;
}

.avatar-skeleton {
  @apply relative overflow-hidden;
  @apply bg-gray-200 dark:bg-gray-700;
  @apply rounded-full;
  @apply animate-pulse;
}

.skeleton-content {
  @apply w-full h-full bg-gray-300 dark:bg-gray-600 rounded-full;
}

/* Empty state */
.avatar-group-empty {
  @apply flex flex-col items-center justify-center;
  @apply text-gray-500 dark:text-gray-400;
  @apply py-4 px-6;
}

.empty-icon {
  @apply w-8 h-8 mb-2;
  @apply text-gray-400 dark:text-gray-500;
}

.empty-text {
  @apply text-sm font-medium;
}

/* Responsive design */
@media (max-width: 640px) {
  .avatar-group-spacing-tight .avatar-wrapper {
    @apply -ml-2;
  }
  
  .avatar-group-spacing-normal .avatar-wrapper {
    @apply -ml-1;
  }
  
  .avatar-group-spacing-loose .avatar-wrapper {
    @apply ml-0;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .avatar-wrapper {
    @apply ring-4 ring-black dark:ring-white;
  }
  
  .avatar-count {
    @apply ring-4 ring-black dark:ring-white;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .avatar-wrapper,
  .avatar-count,
  .skeleton-content {
    @apply transition-none;
  }
  
  .avatar-wrapper.hover-effect:hover {
    @apply transform-none;
  }
}

/* Print styles */
@media print {
  .avatar-group-container {
    @apply break-inside-avoid;
  }
  
  .avatar-wrapper.clickable,
  .avatar-count.clickable {
    @apply cursor-default;
  }
  
  .skeleton-content {
    @apply animate-none;
  }
}

/* Legacy wrapper (hidden by default) */
.legacy-wrapper {
  @apply hidden;
}

/* Focus visible support */
.avatar-wrapper:focus-visible,
.avatar-count:focus-visible {
  @apply ring-2 ring-blue-500 ring-offset-2;
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .avatar-group-container {
    @apply text-gray-200;
  }
  
  .empty-icon,
  .empty-text {
    @apply text-gray-400;
  }
}

/* Animation classes */
@keyframes avatarPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.avatar-skeleton .skeleton-content {
  animation: avatarPulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Focus ring customization */
.avatar-wrapper:focus {
  @apply ring-2 ring-blue-500 ring-offset-2;
}

.avatar-count:focus {
  @apply ring-2 ring-blue-500 ring-offset-2;
}

/* Stacking context management */
.avatar-group-list {
  position: relative;
  z-index: 1;
}

.avatar-wrapper {
  position: relative;
}

.avatar-wrapper:hover {
  z-index: 10;
}