import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AvatarGroupComponent, AvatarData } from './avatar-group.component';
import { AvatarComponent } from '../../base/avatar/avatar.component';
import { CommonModule } from '@angular/common';
import { DebugElement } from '@angular/core';
import { By } from '@angular/platform-browser';

describe('AvatarGroupComponent', () => {
  let component: AvatarGroupComponent;
  let fixture: ComponentFixture<AvatarGroupComponent>;

  const mockAvatars: AvatarData[] = [
    {
      id: '1',
      name: '<PERSON>',
      initials: 'J<PERSON>',
      color: 'primary',
      tooltip: '<PERSON>',
      clickable: true
    },
    {
      id: '2',
      name: '<PERSON>',
      src: 'https://example.com/jane.jpg',
      color: 'success',
      tooltip: '<PERSON>',
      clickable: true
    },
    {
      id: '3',
      name: '<PERSON>',
      initials: 'B<PERSON>',
      color: 'warning',
      clickable: true
    },
    {
      id: '4',
      name: '<PERSON>',
      initials: '<PERSON>',
      color: 'danger',
      clickable: true
    },
    {
      id: '5',
      name: '<PERSON>',
      initials: '<PERSON>',
      color: 'info',
      clickable: true
    }
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        AvatarGroupComponent,
        AvatarComponent
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AvatarGroupComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  describe('Component Creation', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should have default values', () => {
      expect(component.className).toBe('');
      expect(component.size).toBe('md');
      expect(component.variant).toBe('default');
      expect(component.rounded).toBe('full');
      expect(component.maxDisplay).toBe(5);
      expect(component.spacing).toBe('normal');
      expect(component.direction).toBe('right');
      expect(component.showTooltips).toBe(true);
      expect(component.showCount).toBe(true);
      expect(component.clickableAvatars).toBe(true);
      expect(component.hoverEffect).toBe(true);
    });

    it('should render with default avatars', () => {
      expect(component.avatars.length).toBe(3);
      expect(component.avatars[0].name).toBe('John Doe');
      expect(component.avatars[1].name).toBe('Jane Smith');
      expect(component.avatars[2].name).toBe('Bob Wilson');
    });
  });

  describe('Input Properties', () => {
    it('should accept custom className', () => {
      component.className = 'custom-class';
      fixture.detectChanges();
      
      const container = fixture.debugElement.query(By.css('.avatar-group-container'));
      expect(container.nativeElement.className).toContain('custom-class');
    });

    it('should handle different sizes', () => {
      const sizes: Array<'xs' | 'sm' | 'md' | 'lg' | 'xl'> = ['xs', 'sm', 'md', 'lg', 'xl'];
      
      sizes.forEach(size => {
        component.size = size;
        fixture.detectChanges();
        
        const container = fixture.debugElement.query(By.css('.avatar-group-container'));
        expect(container.nativeElement.className).toContain(`avatar-group-size-${size}`);
      });
    });

    it('should handle different variants', () => {
      const variants: Array<'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'> = 
        ['default', 'primary', 'secondary', 'success', 'warning', 'danger'];
      
      variants.forEach(variant => {
        component.variant = variant;
        fixture.detectChanges();
        
        const container = fixture.debugElement.query(By.css('.avatar-group-container'));
        expect(container.nativeElement.className).toContain(`avatar-group-variant-${variant}`);
      });
    });

    it('should handle different spacing options', () => {
      const spacings: Array<'tight' | 'normal' | 'loose'> = ['tight', 'normal', 'loose'];
      
      spacings.forEach(spacing => {
        component.spacing = spacing;
        fixture.detectChanges();
        
        const container = fixture.debugElement.query(By.css('.avatar-group-container'));
        expect(container.nativeElement.className).toContain(`avatar-group-spacing-${spacing}`);
      });
    });

    it('should handle different directions', () => {
      component.direction = 'left';
      fixture.detectChanges();
      
      const container = fixture.debugElement.query(By.css('.avatar-group-container'));
      expect(container.nativeElement.className).toContain('avatar-group-direction-left');
    });

    it('should handle custom avatar data', () => {
      component.avatars = mockAvatars;
      fixture.detectChanges();
      
      expect(component.avatars.length).toBe(5);
      expect(component.avatars[0].name).toBe('John Doe');
      expect(component.avatars[1].src).toBe('https://example.com/jane.jpg');
    });
  });

  describe('Avatar Display Logic', () => {
    beforeEach(() => {
      component.avatars = mockAvatars;
      component.limit = 3;
      fixture.detectChanges();
    });

    it('should limit displayed avatars', () => {
      const displayedAvatars = component.displayAvatars();
      expect(displayedAvatars.length).toBe(2); // limit - 1 for count display
    });

    it('should calculate hidden avatars correctly', () => {
      const hiddenAvatars = component.hiddenAvatars();
      expect(hiddenAvatars.length).toBe(3); // 5 total - 2 displayed
    });

    it('should show count when avatars exceed limit', () => {
      expect(component.shouldShowCount()).toBe(true);
      expect(component.hiddenCount()).toBe(3);
    });

    it('should not show count when avatars within limit', () => {
      component.avatars = mockAvatars.slice(0, 2);
      fixture.detectChanges();
      
      expect(component.shouldShowCount()).toBe(false);
    });

    it('should handle maxDisplay properly', () => {
      component.limit = undefined;
      component.maxDisplay = 4;
      fixture.detectChanges();
      
      const displayedAvatars = component.displayAvatars();
      expect(displayedAvatars.length).toBe(3); // maxDisplay - 1 for count
    });
  });

  describe('Loading State', () => {
    it('should show loading skeletons when loading', () => {
      component.loading = true;
      component.loadingCount = 4;
      fixture.detectChanges();
      
      const loadingContainer = fixture.debugElement.query(By.css('.avatar-group-loading'));
      expect(loadingContainer).toBeTruthy();
      
      const skeletons = fixture.debugElement.queryAll(By.css('.avatar-skeleton'));
      expect(skeletons.length).toBe(4);
    });

    it('should hide main content when loading', () => {
      component.loading = true;
      fixture.detectChanges();
      
      const avatarList = fixture.debugElement.query(By.css('.avatar-group-list'));
      expect(avatarList).toBeFalsy();
    });

    it('should generate loading avatars with correct structure', () => {
      component.loading = true;
      fixture.detectChanges();
      
      const displayedAvatars = component.displayAvatars();
      expect(displayedAvatars.length).toBe(component.loadingCount);
      expect(displayedAvatars[0].id).toContain('loading-');
      expect(displayedAvatars[0].clickable).toBe(false);
    });
  });

  describe('Empty State', () => {
    beforeEach(() => {
      component.avatars = [];
      component.loading = false;
      component.showEmptyState = true;
      fixture.detectChanges();
    });

    it('should show empty state when no avatars', () => {
      expect(component.shouldShowEmptyState()).toBe(true);
      
      const emptyState = fixture.debugElement.query(By.css('.avatar-group-empty'));
      expect(emptyState).toBeTruthy();
    });

    it('should display empty text', () => {
      const emptyText = fixture.debugElement.query(By.css('.empty-text'));
      expect(emptyText.nativeElement.textContent.trim()).toBe(component.emptyText);
    });

    it('should hide empty state when showEmptyState is false', () => {
      component.showEmptyState = false;
      fixture.detectChanges();
      
      expect(component.shouldShowEmptyState()).toBe(false);
      
      const emptyState = fixture.debugElement.query(By.css('.avatar-group-empty'));
      expect(emptyState).toBeFalsy();
    });
  });

  describe('Event Handling', () => {
    beforeEach(() => {
      component.avatars = mockAvatars.slice(0, 2);
      fixture.detectChanges();
    });

    it('should emit avatarClick when avatar is clicked', () => {
      spyOn(component.avatarClick, 'emit');
      
      const avatar = mockAvatars[0];
      component.onAvatarClick(avatar);
      
      expect(component.avatarClick.emit).toHaveBeenCalledWith(avatar);
    });

    it('should emit legacy avatarSelected when avatar is clicked', () => {
      spyOn(component.avatarSelected, 'emit');
      
      const avatar = mockAvatars[0];
      component.onAvatarClick(avatar);
      
      expect(component.avatarSelected.emit).toHaveBeenCalledWith('1');
    });

    it('should emit avatarHover when avatar is hovered', () => {
      spyOn(component.avatarHover, 'emit');
      
      const avatar = mockAvatars[0];
      component.onAvatarHover(avatar);
      
      expect(component.avatarHover.emit).toHaveBeenCalledWith(avatar);
    });

    it('should emit countClick when count is clicked', () => {
      component.avatars = mockAvatars;
      component.limit = 3;
      spyOn(component.countClick, 'emit');
      
      component.onCountClick();
      
      expect(component.countClick.emit).toHaveBeenCalledWith({
        hiddenCount: 3,
        hiddenAvatars: jasmine.any(Array)
      });
    });

    it('should not emit click events when avatar is not clickable', () => {
      spyOn(component.avatarClick, 'emit');
      
      const nonClickableAvatar = { ...mockAvatars[0], clickable: false };
      component.clickableAvatars = false;
      
      component.onAvatarClick(nonClickableAvatar);
      
      expect(component.avatarClick.emit).not.toHaveBeenCalled();
    });
  });

  describe('Helper Methods', () => {
    it('should generate correct avatar size mapping', () => {
      expect(component.getAvatarSize()).toBe('md');
      
      component.size = 'xs';
      expect(component.getAvatarSize()).toBe('xs');
      
      component.size = 'xl';
      expect(component.getAvatarSize()).toBe('xl');
    });

    it('should get avatar color from variant', () => {
      component.variant = 'primary';
      const avatar = { id: '1', name: 'Test' };
      
      expect(component.getAvatarColor(avatar)).toBe('primary');
    });

    it('should use avatar specific color over variant', () => {
      component.variant = 'primary';
      const avatar = { id: '1', name: 'Test', color: 'success' as const };
      
      expect(component.getAvatarColor(avatar)).toBe('success');
    });

    it('should generate initials from name', () => {
      const avatar1 = { id: '1', name: 'John Doe' };
      expect(component.getInitials(avatar1)).toBe('JD');
      
      const avatar2 = { id: '2', name: 'Single' };
      expect(component.getInitials(avatar2)).toBe('S');
      
      const avatar3 = { id: '3', initials: 'XY' };
      expect(component.getInitials(avatar3)).toBe('XY');
      
      const avatar4 = { id: '4', text: 'AB' };
      expect(component.getInitials(avatar4)).toBe('AB');
      
      const avatar5 = { id: '5' };
      expect(component.getInitials(avatar5)).toBe('?');
    });

    it('should track avatars by ID', () => {
      const avatar = mockAvatars[0];
      expect(component.trackByAvatarId(0, avatar)).toBe('1');
      
      const avatarWithoutId = { name: 'Test' } as AvatarData;
      expect(component.trackByAvatarId(5, avatarWithoutId)).toBe('avatar-5');
    });

    it('should track by index', () => {
      expect(component.trackByIndex(3)).toBe(3);
    });
  });

  describe('Computed Classes', () => {
    it('should generate container classes correctly', () => {
      component.size = 'lg';
      component.variant = 'primary';
      component.spacing = 'tight';
      component.direction = 'left';
      component.stackDirection = 'rtl';
      component.borderWidth = 'thick';
      component.hoverEffect = true;
      component.className = 'custom-class';
      
      const classes = component.containerClasses();
      
      expect(classes).toContain('avatar-group-size-lg');
      expect(classes).toContain('avatar-group-variant-primary');
      expect(classes).toContain('avatar-group-spacing-tight');
      expect(classes).toContain('avatar-group-direction-left');
      expect(classes).toContain('avatar-group-stack-rtl');
      expect(classes).toContain('avatar-group-border-thick');
      expect(classes).toContain('hover-effect');
      expect(classes).toContain('custom-class');
    });

    it('should generate avatar classes correctly', () => {
      component.size = 'sm';
      component.rounded = 'lg';
      component.clickableAvatars = true;
      component.hoverEffect = true;
      
      const classes = component.avatarClasses();
      
      expect(classes).toContain('avatar-wrapper');
      expect(classes).toContain('rounded-lg');
      expect(classes).toContain('size-sm');
      expect(classes).toContain('clickable');
      expect(classes).toContain('hover-effect');
    });

    it('should generate count classes correctly', () => {
      component.size = 'xl';
      component.variant = 'success';
      component.rounded = 'full';
      component.clickableAvatars = true;
      
      const classes = component.countClasses();
      
      expect(classes).toContain('avatar-count');
      expect(classes).toContain('size-xl');
      expect(classes).toContain('variant-success');
      expect(classes).toContain('rounded-full');
      expect(classes).toContain('clickable');
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      component.avatars = mockAvatars.slice(0, 3);
      fixture.detectChanges();
    });

    it('should have proper ARIA labels on container', () => {
      const container = fixture.debugElement.query(By.css('.avatar-group-container'));
      expect(container.nativeElement.getAttribute('aria-label')).toContain('Avatar group with');
      expect(container.nativeElement.getAttribute('role')).toBe('group');
    });

    it('should have proper ARIA labels on avatars', () => {
      const avatarWrappers = fixture.debugElement.queryAll(By.css('.avatar-wrapper'));
      
      avatarWrappers.forEach((wrapper, index) => {
        const ariaLabel = wrapper.nativeElement.getAttribute('aria-label');
        expect(ariaLabel).toBeTruthy();
        
        if (component.clickableAvatars) {
          expect(wrapper.nativeElement.getAttribute('role')).toBe('button');
          expect(wrapper.nativeElement.getAttribute('tabindex')).toBe('0');
        }
      });
    });

    it('should support keyboard navigation on clickable avatars', () => {
      spyOn(component, 'onAvatarClick');
      
      const avatarWrapper = fixture.debugElement.query(By.css('.avatar-wrapper'));
      
      // Test Enter key
      avatarWrapper.triggerEventHandler('keydown.enter', {});
      expect(component.onAvatarClick).toHaveBeenCalled();
      
      // Test Space key
      avatarWrapper.triggerEventHandler('keydown.space', {});
      expect(component.onAvatarClick).toHaveBeenCalledTimes(2);
    });
  });

  describe('Legacy Support', () => {
    it('should maintain legacy sizes mapping', () => {
      expect(component.sizes['xs']).toBe('nui-avatar-group-xs');
      expect(component.sizes['md']).toBe('nui-avatar-group-md');
    });

    it('should support legacy classes input', () => {
      component.classes = {
        wrapper: 'legacy-wrapper-class',
        outer: 'legacy-outer-class',
        count: 'legacy-count-class'
      };
      fixture.detectChanges();
      
      expect(component.classes.wrapper).toBe('legacy-wrapper-class');
    });

    it('should support legacy avatarDisplay computed', () => {
      component.avatars = mockAvatars;
      component.limit = 3;
      
      const legacyDisplay = component.avatarDisplay();
      expect(legacyDisplay.length).toBe(2); // limit - 1
    });

    it('should support legacy helper methods', () => {
      expect(component.isString('test')).toBe(true);
      expect(component.isString(123)).toBe(false);
    });
  });

  describe('Performance', () => {
    it('should use trackBy functions for ngFor performance', () => {
      expect(component.trackByAvatarId).toBeDefined();
      expect(component.trackByIndex).toBeDefined();
    });

    it('should use computed properties for reactive updates', () => {
      expect(component.displayAvatars).toBeDefined();
      expect(component.hiddenAvatars).toBeDefined();
      expect(component.containerClasses).toBeDefined();
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined/null avatars array', () => {
      component.avatars = null as any;
      fixture.detectChanges();
      
      expect(component.shouldShowEmptyState()).toBe(true);
      expect(component.displayAvatars().length).toBe(0);
    });

    it('should handle limit greater than avatar count', () => {
      component.avatars = mockAvatars.slice(0, 2);
      component.limit = 5;
      fixture.detectChanges();
      
      expect(component.shouldShowCount()).toBe(false);
      expect(component.displayAvatars().length).toBe(2);
    });

    it('should handle zero limit', () => {
      component.limit = 0;
      fixture.detectChanges();
      
      expect(component.displayAvatars().length).toBe(0);
      expect(component.shouldShowCount()).toBe(false);
    });

    it('should handle avatars without required properties', () => {
      const incompleteAvatar = { id: 'incomplete' } as AvatarData;
      component.avatars = [incompleteAvatar];
      fixture.detectChanges();
      
      expect(component.getInitials(incompleteAvatar)).toBe('?');
      expect(component.getAvatarColor(incompleteAvatar)).toBe('muted');
    });
  });
});
