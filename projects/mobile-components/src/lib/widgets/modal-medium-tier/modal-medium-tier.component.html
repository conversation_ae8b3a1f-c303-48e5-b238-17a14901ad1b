<!-- Modal Overlay -->
<div *ngIf="isOpen" [class]="computedModalClasses" role="dialog" 
     aria-modal="true" [attr.aria-labelledby]="showHeader ? 'modal-title' : null"
     [attr.aria-describedby]="'modal-content'">
  
  <!-- Backdrop -->
  <div [class]="computedBackdropClasses" 
       (click)="onBackdropClick($event)"
       [attr.aria-hidden]="backdrop === 'none'"></div>
  
  <!-- Modal Content -->
  <div [class]="computedContentClasses" role="document">
    
    <!-- Header -->
    <div *ngIf="showHeader" [class]="computedHeaderClasses">
      <div class="flex items-center">
        <!-- Icon -->
        <div *ngIf="icon" class="mr-3">
          <span [class]="'text-xl ' + icon" aria-hidden="true"></span>
        </div>
        
        <!-- Title -->
        <h2 id="modal-title" class="text-lg font-semibold text-gray-900">
          {{ title }}
        </h2>
      </div>
      
      <!-- Header Actions -->
      <div class="flex items-center space-x-2">
        <!-- Custom header actions -->
        <button *ngFor="let action of headerActions" 
                type="button"
                (click)="onHeaderActionClick(action, $event)"
                class="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md p-1"
                [attr.aria-label]="action">
          <span [class]="action" class="w-4 h-4" aria-hidden="true"></span>
        </button>
        
        <!-- Close button -->
        <button *ngIf="showCloseButton" 
                type="button"
                (click)="onCloseClick($event)"
                class="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md p-1"
                aria-label="Close modal">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>
    
    <!-- Body -->
    <div id="modal-content" [class]="computedBodyClasses">
      <div class="text-sm leading-relaxed" [innerHTML]="content"></div>
      
      <!-- Content slot for more complex content -->
      <ng-content></ng-content>
    </div>
    
    <!-- Footer -->
    <div *ngIf="showFooter" class="modal-footer border-t border-gray-200 px-6 py-4 bg-gray-50">
      <ng-content select="[slot=footer]"></ng-content>
    </div>
    
  </div>
</div>
