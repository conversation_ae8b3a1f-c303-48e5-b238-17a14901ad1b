import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DebugElement } from '@angular/core';
import { By } from '@angular/platform-browser';

import { ModalMediumTierComponent } from './modal-medium-tier.component';

describe('ModalMediumTierComponent', () => {
  let component: ModalMediumTierComponent;
  let fixture: ComponentFixture<ModalMediumTierComponent>;
  let debugElement: DebugElement;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ModalMediumTierComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ModalMediumTierComponent);
    component = fixture.componentInstance;
    debugElement = fixture.debugElement;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should render modal with default values', () => {
    expect(component.title).toBe('Modal Title');
    expect(component.content).toContain('This is a medium-sized modal dialog');
    expect(component.isOpen).toBe(true);
    expect(component.showHeader).toBe(true);
    expect(component.showCloseButton).toBe(true);
    expect(component.size).toBe('md');
    expect(component.variant).toBe('default');
  });

  it('should display modal when isOpen is true', () => {
    component.isOpen = true;
    fixture.detectChanges();
    
    const modalElement = debugElement.query(By.css('.modal-medium-tier-widget'));
    expect(modalElement).toBeTruthy();
  });

  it('should hide modal when isOpen is false', () => {
    component.isOpen = false;
    fixture.detectChanges();
    
    const modalElement = debugElement.query(By.css('.modal-medium-tier-widget'));
    expect(modalElement).toBeFalsy();
  });

  it('should display title and content correctly', () => {
    const testTitle = 'Test Modal Title';
    const testContent = 'Test modal content';
    
    component.title = testTitle;
    component.content = testContent;
    fixture.detectChanges();
    
    const titleElement = debugElement.query(By.css('#modal-title'));
    const contentElement = debugElement.query(By.css('#modal-content'));
    
    expect(titleElement.nativeElement.textContent.trim()).toBe(testTitle);
    expect(contentElement.nativeElement.textContent.trim()).toContain(testContent);
  });

  it('should show/hide header based on showHeader input', () => {
    // Test with header visible
    component.showHeader = true;
    fixture.detectChanges();
    
    let headerElement = debugElement.query(By.css('.modal-header'));
    expect(headerElement).toBeTruthy();
    
    // Test with header hidden
    component.showHeader = false;
    fixture.detectChanges();
    
    headerElement = debugElement.query(By.css('.modal-header'));
    expect(headerElement).toBeFalsy();
  });

  it('should show/hide close button based on showCloseButton input', () => {
    // Test with close button visible
    component.showCloseButton = true;
    fixture.detectChanges();
    
    let closeButton = debugElement.query(By.css('button[aria-label="Close modal"]'));
    expect(closeButton).toBeTruthy();
    
    // Test with close button hidden
    component.showCloseButton = false;
    fixture.detectChanges();
    
    closeButton = debugElement.query(By.css('button[aria-label="Close modal"]'));
    expect(closeButton).toBeFalsy();
  });

  it('should emit closeClick event when close button is clicked', () => {
    spyOn(component.closeClick, 'emit');
    spyOn(component.openChange, 'emit');
    
    component.showCloseButton = true;
    fixture.detectChanges();
    
    const closeButton = debugElement.query(By.css('button[aria-label="Close modal"]'));
    closeButton.nativeElement.click();
    
    expect(component.closeClick.emit).toHaveBeenCalled();
    expect(component.openChange.emit).toHaveBeenCalledWith(false);
  });

  it('should emit backdropClick when backdrop is clicked with clickable backdrop', () => {
    spyOn(component.backdropClick, 'emit');
    spyOn(component.openChange, 'emit');
    
    component.backdrop = 'clickable';
    fixture.detectChanges();
    
    const backdropElement = debugElement.query(By.css('.modal-backdrop'));
    backdropElement.nativeElement.click();
    
    expect(component.backdropClick.emit).toHaveBeenCalled();
    expect(component.openChange.emit).toHaveBeenCalledWith(false);
  });

  it('should not emit backdropClick when backdrop is static', () => {
    spyOn(component.backdropClick, 'emit');
    spyOn(component.openChange, 'emit');
    
    component.backdrop = 'static';
    fixture.detectChanges();
    
    const backdropElement = debugElement.query(By.css('.modal-backdrop'));
    backdropElement.nativeElement.click();
    
    expect(component.backdropClick.emit).not.toHaveBeenCalled();
    expect(component.openChange.emit).not.toHaveBeenCalled();
  });

  it('should apply correct size classes', () => {
    const testCases = [
      { size: 'xs' as const, expectedClass: 'max-w-xs' },
      { size: 'sm' as const, expectedClass: 'max-w-sm' },
      { size: 'md' as const, expectedClass: 'max-w-md' },
      { size: 'lg' as const, expectedClass: 'max-w-lg' },
      { size: 'xl' as const, expectedClass: 'max-w-xl' }
    ];

    testCases.forEach(({ size, expectedClass }) => {
      component.size = size;
      fixture.detectChanges();
      
      const modalContent = debugElement.query(By.css('.modal-content'));
      expect(modalContent.nativeElement.className).toContain(expectedClass);
    });
  });

  it('should apply correct variant classes', () => {
    const testCases = [
      { variant: 'default' as const, expectedClass: 'border-gray-200' },
      { variant: 'primary' as const, expectedClass: 'border-blue-300' },
      { variant: 'secondary' as const, expectedClass: 'border-gray-300' },
      { variant: 'success' as const, expectedClass: 'border-green-300' },
      { variant: 'warning' as const, expectedClass: 'border-yellow-300' },
      { variant: 'danger' as const, expectedClass: 'border-red-300' }
    ];

    testCases.forEach(({ variant, expectedClass }) => {
      component.variant = variant;
      fixture.detectChanges();
      
      const modalContent = debugElement.query(By.css('.modal-content'));
      expect(modalContent.nativeElement.className).toContain(expectedClass);
    });
  });

  it('should apply correct position classes', () => {
    const testCases = [
      { position: 'center' as const, expectedClasses: ['items-center', 'justify-center'] },
      { position: 'top' as const, expectedClasses: ['items-start', 'justify-center', 'pt-16'] },
      { position: 'bottom' as const, expectedClasses: ['items-end', 'justify-center', 'pb-16'] }
    ];

    testCases.forEach(({ position, expectedClasses }) => {
      component.position = position;
      fixture.detectChanges();
      
      const modalElement = debugElement.query(By.css('.modal-medium-tier-widget'));
      expectedClasses.forEach(expectedClass => {
        expect(modalElement.nativeElement.className).toContain(expectedClass);
      });
    });
  });

  it('should display icon when provided', () => {
    const testIcon = 'fas fa-info-circle';
    component.icon = testIcon;
    fixture.detectChanges();
    
    const iconElement = debugElement.query(By.css('.modal-header span[aria-hidden="true"]'));
    expect(iconElement).toBeTruthy();
    expect(iconElement.nativeElement.className).toContain(testIcon);
  });

  it('should render header actions and emit events', () => {
    const testActions = ['fas fa-edit', 'fas fa-share'];
    component.headerActions = testActions;
    spyOn(component.headerActionClick, 'emit');
    fixture.detectChanges();
    
    const actionButtons = debugElement.queryAll(By.css('.modal-header button[aria-label]'));
    // Expecting 2 action buttons + 1 close button
    expect(actionButtons.length).toBe(3);
    
    // Click first action button
    actionButtons[0].nativeElement.click();
    expect(component.headerActionClick.emit).toHaveBeenCalledWith({
      action: testActions[0],
      event: jasmine.any(Event)
    });
  });

  it('should apply custom className', () => {
    const customClass = 'custom-modal-class';
    component.className = customClass;
    fixture.detectChanges();
    
    const modalElement = debugElement.query(By.css('.modal-medium-tier-widget'));
    expect(modalElement.nativeElement.className).toContain(customClass);
  });

  it('should show/hide footer based on showFooter input', () => {
    // Test with footer hidden (default)
    component.showFooter = false;
    fixture.detectChanges();
    
    let footerElement = debugElement.query(By.css('.modal-footer'));
    expect(footerElement).toBeFalsy();
    
    // Test with footer visible
    component.showFooter = true;
    fixture.detectChanges();
    
    footerElement = debugElement.query(By.css('.modal-footer'));
    expect(footerElement).toBeTruthy();
  });

  it('should have proper accessibility attributes', () => {
    fixture.detectChanges();
    
    const modalElement = debugElement.query(By.css('.modal-medium-tier-widget'));
    const modalContent = debugElement.query(By.css('.modal-content'));
    
    expect(modalElement.nativeElement.getAttribute('role')).toBe('dialog');
    expect(modalElement.nativeElement.getAttribute('aria-modal')).toBe('true');
    expect(modalElement.nativeElement.getAttribute('aria-labelledby')).toBe('modal-title');
    expect(modalElement.nativeElement.getAttribute('aria-describedby')).toBe('modal-content');
    expect(modalContent.nativeElement.getAttribute('role')).toBe('document');
  });
});
