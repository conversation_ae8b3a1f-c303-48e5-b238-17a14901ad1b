/* Modal Medium Tier Widget Styles */
.modal-medium-tier-widget {
  animation: fadeIn 0.15s ease-out;
}

.modal-content {
  animation: slideIn 0.2s ease-out;
  transform-origin: center;
}

.modal-backdrop {
  animation: fadeIn 0.15s ease-out;
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .modal-content {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
    max-height: calc(100vh - 2rem);
  }
}

/* Focus management */
.modal-medium-tier-widget:focus-within {
  outline: none;
}

/* Accessibility improvements */
.modal-header button:focus,
.modal-footer button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Close button hover effects */
.modal-header button:hover svg {
  transform: scale(1.1);
  transition: transform 0.15s ease-in-out;
}

/* Content scrolling */
.modal-content {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

.modal-content::-webkit-scrollbar {
  width: 6px;
}

.modal-content::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .modal-content {
    border-width: 3px;
  }
  
  .modal-header {
    border-bottom-width: 2px;
  }
  
  .modal-footer {
    border-top-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .modal-medium-tier-widget,
  .modal-content,
  .modal-backdrop {
    animation: none;
  }
  
  .modal-header button:hover svg {
    transform: none;
    transition: none;
  }
}

/* Print styles */
@media print {
  .modal-medium-tier-widget {
    position: static;
    background: white;
    box-shadow: none;
  }
  
  .modal-backdrop {
    display: none;
  }
  
  .modal-header button {
    display: none;
  }
}