import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

// Interface for modal configuration
export interface ModalMediumTierConfig {
  title: string;
  content: string;
  showCloseButton: boolean;
  showHeader: boolean;
  showFooter: boolean;
  backdrop: 'static' | 'clickable' | 'none';
  position: 'center' | 'top' | 'bottom';
}

@Component({
  selector: 'lib-modal-medium-tier',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './modal-medium-tier.component.html',
  styleUrl: './modal-medium-tier.component.css'
})
export class ModalMediumTierComponent {
  // Modal content inputs
  @Input() title: string = 'Modal Title';
  @Input() content: string = 'This is a medium-sized modal dialog with customizable content. You can configure the title, content, and various display options.';
  @Input() showCloseButton: boolean = true;
  @Input() showHeader: boolean = true;
  @Input() showFooter: boolean = false;
  @Input() backdrop: 'static' | 'clickable' | 'none' = 'clickable';
  @Input() position: 'center' | 'top' | 'bottom' = 'center';
  @Input() isOpen: boolean = true;
  @Input() icon: string = '';
  @Input() headerActions: string[] = [];
  
  // Standard Tailwind inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'lg';
  
  // Events
  @Output() closeClick = new EventEmitter<Event>();
  @Output() backdropClick = new EventEmitter<Event>();
  @Output() openChange = new EventEmitter<boolean>();
  @Output() headerActionClick = new EventEmitter<{ action: string; event: Event }>();

  get computedModalClasses(): string {
    const baseClasses = ['modal-medium-tier-widget', 'fixed', 'inset-0', 'z-50', 'flex', 'items-center', 'justify-center'];
    
    // Position classes
    const positionClasses = {
      center: ['items-center', 'justify-center'],
      top: ['items-start', 'justify-center', 'pt-16'],
      bottom: ['items-end', 'justify-center', 'pb-16']
    };

    const classes = [
      ...baseClasses,
      ...positionClasses[this.position],
      this.className
    ];

    return classes.filter(Boolean).join(' ');
  }

  get computedContentClasses(): string {
    const baseClasses = ['modal-content', 'bg-white', 'shadow-xl', 'w-full', 'max-h-[90vh]', 'overflow-y-auto', 'relative'];
    
    // Size classes for modal width
    const sizeClasses = {
      xs: ['max-w-xs'],
      sm: ['max-w-sm'],
      md: ['max-w-md'],
      lg: ['max-w-lg'],
      xl: ['max-w-xl']
    };

    // Variant border colors  
    const variantClasses = {
      default: ['border-gray-200'],
      primary: ['border-blue-300'],
      secondary: ['border-gray-300'],
      success: ['border-green-300'],
      warning: ['border-yellow-300'],
      danger: ['border-red-300']
    };

    // Rounded classes
    const roundedClasses = {
      none: ['rounded-none'],
      sm: ['rounded-sm'],
      md: ['rounded-md'],
      lg: ['rounded-lg'],
      full: ['rounded-3xl']
    };

    const classes = [
      ...baseClasses,
      ...sizeClasses[this.size],
      'border-2',
      ...variantClasses[this.variant],
      ...roundedClasses[this.rounded]
    ];

    return classes.filter(Boolean).join(' ');
  }

  get computedBackdropClasses(): string {
    const baseClasses = ['modal-backdrop', 'fixed', 'inset-0', 'bg-black', 'bg-opacity-50', 'transition-opacity'];
    
    if (this.backdrop === 'none') {
      return 'hidden';
    }

    return baseClasses.join(' ');
  }

  get computedHeaderClasses(): string {
    const baseClasses = ['modal-header', 'flex', 'items-center', 'justify-between', 'p-6', 'border-b'];
    
    const variantClasses = {
      default: ['border-gray-200', 'bg-gray-50'],
      primary: ['border-blue-200', 'bg-blue-50'],
      secondary: ['border-gray-200', 'bg-gray-100'],
      success: ['border-green-200', 'bg-green-50'],
      warning: ['border-yellow-200', 'bg-yellow-50'],
      danger: ['border-red-200', 'bg-red-50']
    };

    return [...baseClasses, ...variantClasses[this.variant]].join(' ');
  }

  get computedBodyClasses(): string {
    const baseClasses = ['modal-body', 'p-6'];
    
    const variantClasses = {
      default: ['text-gray-900'],
      primary: ['text-blue-900'],
      secondary: ['text-gray-800'],
      success: ['text-green-900'],
      warning: ['text-yellow-900'],
      danger: ['text-red-900']
    };

    return [...baseClasses, ...variantClasses[this.variant]].join(' ');
  }

  onCloseClick(event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.closeClick.emit(event);
    this.openChange.emit(false);
  }

  onBackdropClick(event: Event): void {
    if (this.backdrop === 'clickable') {
      this.backdropClick.emit(event);
      this.openChange.emit(false);
    }
  }

  onHeaderActionClick(action: string, event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.headerActionClick.emit({ action, event });
  }
}
