
.tags-container {
  @apply w-full;
}

/* Search Input Enhancements */
.search-container {
  @apply relative;
}

.search-input {
  @apply transition-all duration-200 ease-in-out;
}

.search-input:focus {
  @apply shadow-md transform scale-[1.01];
}

/* Tags Widget Layout */
.tags-widget {
  @apply items-start;
}

/* Individual Tag Items */
.tag-item {
  @apply relative select-none;
  animation: tagFadeIn 0.2s ease-out;
}

.tag-item:hover {
  @apply transform scale-105 shadow-sm;
}

.tag-item:active {
  @apply transform scale-95;
}

/* Tag Icon */
.tag-icon svg {
  @apply w-4 h-4;
}

/* Tag Label */
.tag-label {
  @apply font-medium leading-none;
}

/* Tag Count Badge */
.tag-count {
  @apply font-semibold leading-none;
  backdrop-filter: blur(2px);
}

/* Tag Remove Button */
.tag-remove {
  @apply -mr-1;
  animation: removeButtonFadeIn 0.15s ease-out;
}

.tag-remove:hover {
  @apply transform scale-110;
}

.tag-remove:active {
  @apply transform scale-95;
}

/* Hidden Count Indicator */
.hidden-count-indicator {
  @apply font-medium;
  animation: tagFadeIn 0.3s ease-out;
}

/* Add Tag Button */
.add-tag-button {
  @apply font-medium;
  animation: tagFadeIn 0.3s ease-out;
}

.add-tag-button:hover {
  @apply transform scale-105;
}

/* Add Tag Input Container */
.add-tag-input-container {
  animation: inputSlideIn 0.3s ease-out;
}

.add-tag-input {
  @apply min-w-32;
}

.add-confirm-button:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.add-confirm-button:disabled:hover {
  @apply bg-blue-600 transform-none;
}

/* Empty State */
.empty-state {
  animation: fadeIn 0.4s ease-out;
}

/* Selection Summary */
.selection-summary {
  animation: slideDown 0.3s ease-out;
}

/* Grid Layout Responsiveness */
.tags-widget.grid {
  @apply auto-rows-max;
}

/* Vertical Layout */
.tags-widget.flex-col .tag-item {
  @apply w-full justify-start;
}

/* Responsive Grid Adjustments */
@screen xs {
  .tags-widget.grid {
    @apply grid-cols-1;
  }
}

@screen sm {
  .tags-widget.grid {
    @apply grid-cols-2;
  }
}

@screen md {
  .tags-widget.grid {
    @apply grid-cols-3;
  }
}

@screen lg {
  .tags-widget.grid {
    @apply grid-cols-4;
  }
}

@screen xl {
  .tags-widget.grid {
    @apply grid-cols-5;
  }
}

/* Animation Keyframes */
@keyframes tagFadeIn {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes removeButtonFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes inputSlideIn {
  from {
    opacity: 0;
    transform: translateX(-8px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus Management */
.tag-item:focus {
  @apply z-10;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .tag-item {
    @apply border-2;
  }
  
  .tag-item:focus {
    @apply outline outline-2 outline-offset-2;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .tag-item,
  .tag-remove,
  .hidden-count-indicator,
  .add-tag-button,
  .add-tag-input-container,
  .empty-state,
  .selection-summary {
    animation: none;
  }
  
  .tag-item:hover,
  .tag-remove:hover,
  .add-tag-button:hover {
    @apply transform-none;
  }
  
  .search-input:focus {
    @apply transform-none;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .search-input {
    @apply bg-gray-800 border-gray-600 text-gray-100;
  }
  
  .hidden-count-indicator {
    @apply bg-gray-800 text-gray-300 border-gray-600;
  }
  
  .add-tag-button {
    @apply bg-gray-800 text-gray-300 border-gray-600;
  }
  
  .add-tag-button:hover {
    @apply bg-gray-700;
  }
  
  .empty-state {
    @apply text-gray-400;
  }
  
  .selection-summary {
    @apply bg-blue-900 border-blue-700 text-blue-100;
  }
}