import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { TagsComponent, Tag } from './tags.component';

describe('TagsComponent', () => {
  let component: TagsComponent;
  let fixture: ComponentFixture<TagsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TagsComponent, FormsModule]
    }).compileComponents();
    
    fixture = TestBed.createComponent(TagsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Component Initialization', () => {
    it('should have default values', () => {
      expect(component.selectable).toBe(true);
      expect(component.multiSelect).toBe(true);
      expect(component.filterMode).toBe(false);
      expect(component.showCounts).toBe(true);
      expect(component.showIcons).toBe(false);
      expect(component.allowRemove).toBe(true);
      expect(component.allowAdd).toBe(false);
      expect(component.layout).toBe('horizontal');
      expect(component.spacing).toBe('normal');
      expect(component.maxDisplay).toBe(0);
      expect(component.searchable).toBe(false);
      expect(component.size).toBe('md');
      expect(component.variant).toBe('default');
      expect(component.rounded).toBe('md');
    });

    it('should have default sample tags', () => {
      expect(component.tags.length).toBe(6);
      expect(component.tags[0].label).toBe('Frontend');
      expect(component.tags[0].selected).toBe(true);
    });
  });

  describe('Tag Interaction', () => {
    let testTags: Tag[];

    beforeEach(() => {
      testTags = [
        { id: 1, label: 'Test1', selected: false, clickable: true, removable: true },
        { id: 2, label: 'Test2', selected: true, clickable: true, removable: true },
        { id: 3, label: 'Test3', selected: false, clickable: false, removable: true }
      ];
      component.tags = testTags;
      component.selectable = true;
    });

    it('should toggle tag selection when clicked', () => {
      spyOn(component.tagClick, 'emit');
      spyOn(component.tagSelect, 'emit');
      spyOn(component.selectionChange, 'emit');

      component.onTagClick(testTags[0]);

      expect(testTags[0].selected).toBe(true);
      expect(component.tagClick.emit).toHaveBeenCalledWith(testTags[0]);
      expect(component.tagSelect.emit).toHaveBeenCalledWith(testTags[0]);
      expect(component.selectionChange.emit).toHaveBeenCalled();
    });

    it('should deselect tag when already selected', () => {
      spyOn(component.tagDeselect, 'emit');
      spyOn(component.selectionChange, 'emit');

      component.onTagClick(testTags[1]);

      expect(testTags[1].selected).toBe(false);
      expect(component.tagDeselect.emit).toHaveBeenCalledWith(testTags[1]);
      expect(component.selectionChange.emit).toHaveBeenCalled();
    });

    it('should not toggle selection for non-clickable tags', () => {
      const initialSelected = testTags[2].selected;
      
      component.onTagClick(testTags[2]);

      expect(testTags[2].selected).toBe(initialSelected);
    });

    it('should not interact with disabled tags', () => {
      testTags[0].disabled = true;
      spyOn(component.tagClick, 'emit');

      component.onTagClick(testTags[0]);

      expect(component.tagClick.emit).not.toHaveBeenCalled();
    });

    it('should handle single select mode', () => {
      component.multiSelect = false;
      testTags[1].selected = true;

      component.onTagClick(testTags[0]);

      expect(testTags[0].selected).toBe(true);
      expect(testTags[1].selected).toBe(false);
    });
  });

  describe('Tag Removal', () => {
    let testTags: Tag[];

    beforeEach(() => {
      testTags = [
        { id: 1, label: 'Test1', removable: true },
        { id: 2, label: 'Test2', removable: false },
        { id: 3, label: 'Test3', disabled: true, removable: true }
      ];
      component.tags = testTags;
    });

    it('should emit tag remove event', () => {
      spyOn(component.tagRemove, 'emit');
      const mockEvent = new Event('click');
      spyOn(mockEvent, 'stopPropagation');

      component.onTagRemove(testTags[0], mockEvent);

      expect(mockEvent.stopPropagation).toHaveBeenCalled();
      expect(component.tagRemove.emit).toHaveBeenCalledWith(testTags[0]);
    });

    it('should not remove non-removable tags', () => {
      spyOn(component.tagRemove, 'emit');
      const mockEvent = new Event('click');

      component.onTagRemove(testTags[1], mockEvent);

      expect(component.tagRemove.emit).not.toHaveBeenCalled();
    });

    it('should not remove disabled tags', () => {
      spyOn(component.tagRemove, 'emit');
      const mockEvent = new Event('click');

      component.onTagRemove(testTags[2], mockEvent);

      expect(component.tagRemove.emit).not.toHaveBeenCalled();
    });
  });

  describe('Search Functionality', () => {
    beforeEach(() => {
      component.tags = [
        { id: 1, label: 'Frontend' },
        { id: 2, label: 'Backend' },
        { id: 3, label: 'JavaScript' },
        { id: 4, label: 'TypeScript' }
      ];
    });

    it('should filter tags based on search term', () => {
      component.searchTerm = 'script';
      const filtered = component.filteredTags;

      expect(filtered.length).toBe(2);
      expect(filtered.map(t => t.label)).toEqual(['JavaScript', 'TypeScript']);
    });

    it('should be case insensitive', () => {
      component.searchTerm = 'FRONT';
      const filtered = component.filteredTags;

      expect(filtered.length).toBe(1);
      expect(filtered[0].label).toBe('Frontend');
    });

    it('should emit search change event', () => {
      spyOn(component.searchChange, 'emit');

      component.onSearchChange('test');

      expect(component.searchTerm).toBe('test');
      expect(component.searchChange.emit).toHaveBeenCalledWith('test');
    });

    it('should respect maxDisplay limit', () => {
      component.maxDisplay = 2;
      const filtered = component.filteredTags;

      expect(filtered.length).toBe(2);
    });

    it('should calculate hidden count correctly', () => {
      component.maxDisplay = 2;
      
      expect(component.hiddenCount).toBe(2);
    });
  });

  describe('Add Tag Functionality', () => {
    beforeEach(() => {
      component.allowAdd = true;
    });

    it('should show add input when requested', () => {
      component.showAddTagInput();

      expect(component.showAddInput).toBe(true);
    });

    it('should hide add input when cancelled', () => {
      component.showAddInput = true;
      component.newTagInput = 'test';

      component.hideAddTagInput();

      expect(component.showAddInput).toBe(false);
      expect(component.newTagInput).toBe('');
    });

    it('should emit tag add event when adding tag', () => {
      spyOn(component.tagAdd, 'emit');
      component.newTagInput = 'New Tag';
      component.showAddInput = true;

      component.onAddTag();

      expect(component.tagAdd.emit).toHaveBeenCalledWith('New Tag');
      expect(component.newTagInput).toBe('');
      expect(component.showAddInput).toBe(false);
    });

    it('should not add empty tag', () => {
      spyOn(component.tagAdd, 'emit');
      component.newTagInput = '   ';

      component.onAddTag();

      expect(component.tagAdd.emit).not.toHaveBeenCalled();
    });
  });

  describe('Computed Properties', () => {
    it('should generate correct classes for horizontal layout', () => {
      component.layout = 'horizontal';
      component.spacing = 'normal';
      component.className = 'custom-class';

      const classes = component.computedClasses;

      expect(classes).toContain('flex');
      expect(classes).toContain('flex-wrap');
      expect(classes).toContain('gap-2');
      expect(classes).toContain('custom-class');
    });

    it('should generate correct classes for vertical layout', () => {
      component.layout = 'vertical';
      component.spacing = 'loose';

      const classes = component.computedClasses;

      expect(classes).toContain('flex-col');
      expect(classes).toContain('space-y-3');
    });

    it('should generate correct classes for grid layout', () => {
      component.layout = 'grid';
      component.spacing = 'tight';

      const classes = component.computedClasses;

      expect(classes).toContain('grid');
      expect(classes).toContain('gap-1');
    });

    it('should get selected tags', () => {
      component.tags = [
        { id: 1, label: 'Tag1', selected: true },
        { id: 2, label: 'Tag2', selected: false },
        { id: 3, label: 'Tag3', selected: true }
      ];

      const selected = component.selectedTags;

      expect(selected.length).toBe(2);
      expect(selected.map(t => t.label)).toEqual(['Tag1', 'Tag3']);
    });
  });

  describe('Tag Classes', () => {
    let tag: Tag;

    beforeEach(() => {
      tag = {
        id: 1,
        label: 'Test',
        color: 'primary',
        selected: false,
        clickable: true,
        disabled: false
      };
    });

    it('should generate base tag classes', () => {
      const classes = component.getTagClasses(tag);

      expect(classes).toContain('tag-item');
      expect(classes).toContain('inline-flex');
      expect(classes).toContain('items-center');
    });

    it('should apply size classes', () => {
      component.size = 'lg';
      const classes = component.getTagClasses(tag);

      expect(classes).toContain('px-4');
      expect(classes).toContain('py-2.5');
      expect(classes).toContain('text-lg');
    });

    it('should apply selected state classes', () => {
      tag.selected = true;
      component.selectable = true;

      const classes = component.getTagClasses(tag);

      expect(classes).toContain('bg-blue-600');
      expect(classes).toContain('text-white');
    });

    it('should apply disabled state classes', () => {
      tag.disabled = true;

      const classes = component.getTagClasses(tag);

      expect(classes).toContain('opacity-50');
      expect(classes).toContain('cursor-not-allowed');
    });

    it('should apply clickable cursor', () => {
      const classes = component.getTagClasses(tag);

      expect(classes).toContain('cursor-pointer');
    });
  });

  describe('Keyboard Navigation', () => {
    let tag: Tag;

    beforeEach(() => {
      tag = { id: 1, label: 'Test', clickable: true };
    });

    it('should handle Enter key', () => {
      spyOn(component, 'onTagClick');
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      spyOn(event, 'preventDefault');

      component.onKeydown(event, tag);

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.onTagClick).toHaveBeenCalledWith(tag);
    });

    it('should handle Space key', () => {
      spyOn(component, 'onTagClick');
      const event = new KeyboardEvent('keydown', { key: ' ' });
      spyOn(event, 'preventDefault');

      component.onKeydown(event, tag);

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.onTagClick).toHaveBeenCalledWith(tag);
    });

    it('should ignore other keys', () => {
      spyOn(component, 'onTagClick');
      const event = new KeyboardEvent('keydown', { key: 'Tab' });

      component.onKeydown(event, tag);

      expect(component.onTagClick).not.toHaveBeenCalled();
    });
  });

  describe('Track By Function', () => {
    it('should return tag id', () => {
      const tag: Tag = { id: 'test-id', label: 'Test' };
      const result = component.trackByFn(0, tag);

      expect(result).toBe('test-id');
    });
  });
});
