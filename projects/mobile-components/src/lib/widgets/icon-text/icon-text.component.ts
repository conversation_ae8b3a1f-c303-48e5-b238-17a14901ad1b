import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'lib-icon-text',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './icon-text.component.html',
  styleUrl: './icon-text.component.css'
})
export class IconTextComponent {
  @Input() icon: string = 'fa fa-info-circle';
  @Input() text: string = 'Icon Text Content';
  @Input() subtext: string = '';
  @Input() showSubtext: boolean = false;
  @Input() iconPosition: 'left' | 'right' | 'top' | 'bottom' = 'left';
  @Input() layout: 'horizontal' | 'vertical' = 'horizontal';
  @Input() alignment: 'start' | 'center' | 'end' = 'start';
  @Input() spacing: 'tight' | 'normal' | 'loose' = 'normal';
  
  // Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'none';

  get computedClasses(): string {
    const baseClasses = ['icon-text-widget', 'flex'];

    // Layout and positioning
    if (this.layout === 'vertical' || this.iconPosition === 'top' || this.iconPosition === 'bottom') {
      baseClasses.push('flex-col');
      if (this.iconPosition === 'bottom') {
        baseClasses.push('flex-col-reverse');
      }
    } else {
      baseClasses.push('flex-row');
      if (this.iconPosition === 'right') {
        baseClasses.push('flex-row-reverse');
      }
    }

    // Alignment
    const alignmentClasses = {
      start: this.layout === 'vertical' ? 'items-start' : 'items-start',
      center: 'items-center',
      end: this.layout === 'vertical' ? 'items-end' : 'items-end'
    };
    baseClasses.push(alignmentClasses[this.alignment]);

    // Spacing
    const spacingClasses = {
      tight: this.layout === 'vertical' ? 'space-y-1' : 'space-x-2',
      normal: this.layout === 'vertical' ? 'space-y-2' : 'space-x-3',
      loose: this.layout === 'vertical' ? 'space-y-4' : 'space-x-4'
    };
    baseClasses.push(spacingClasses[this.spacing]);

    if (this.className) {
      baseClasses.push(this.className);
    }

    return baseClasses.join(' ');
  }

  get iconClasses(): string {
    const sizeClasses = {
      xs: 'text-sm',
      sm: 'text-base',
      md: 'text-lg',
      lg: 'text-xl',
      xl: 'text-2xl'
    };

    const variantClasses = {
      default: 'text-gray-600',
      primary: 'text-blue-600',
      secondary: 'text-gray-500',
      success: 'text-green-600',
      warning: 'text-yellow-600',
      danger: 'text-red-600'
    };

    return `${sizeClasses[this.size]} ${variantClasses[this.variant]} flex-shrink-0`;
  }

  get textClasses(): string {
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };

    const variantClasses = {
      default: 'text-gray-900',
      primary: 'text-blue-900',
      secondary: 'text-gray-700',
      success: 'text-green-900',
      warning: 'text-yellow-900',
      danger: 'text-red-900'
    };

    return `${sizeClasses[this.size]} ${variantClasses[this.variant]} font-medium`;
  }

  get subtextClasses(): string {
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-xs',
      md: 'text-sm',
      lg: 'text-base',
      xl: 'text-lg'
    };

    return `${sizeClasses[this.size]} text-gray-600 mt-1`;
  }
}