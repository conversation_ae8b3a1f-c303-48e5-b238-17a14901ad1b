<!-- Image Gallery Widget -->
<div [class]="computedClasses">
  <!-- Main Image Display -->
  <div class="relative">
    <div [class]="aspectRatioClass + ' relative overflow-hidden bg-gray-100'">
      <img 
        *ngIf="currentImage"
        [src]="currentImage.src"
        [alt]="currentImage.alt"
        [title]="currentImage.title"
        class="w-full h-full object-cover cursor-pointer transition-transform duration-300 hover:scale-105"
        (click)="onImageClick()"
        loading="lazy">
      
      <!-- Placeholder when no images -->
      <div *ngIf="!currentImage" class="flex items-center justify-center h-full">
        <div class="text-center">
          <i class="fas fa-image text-4xl text-gray-400 mb-2"></i>
          <p class="text-gray-500">No images available</p>
        </div>
      </div>
    </div>

    <!-- Navigation arrows -->
    <div *ngIf="showNavigation && images.length > 1" class="absolute inset-y-0 left-0 right-0 flex items-center justify-between pointer-events-none">
      <button 
        type="button"
        (click)="previousImage()"
        class="ml-3 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-all pointer-events-auto focus:outline-none focus:ring-2 focus:ring-blue-500"
        aria-label="Previous image">
        <i class="fas fa-chevron-left"></i>
      </button>
      
      <button 
        type="button"
        (click)="nextImage()"
        class="mr-3 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-all pointer-events-auto focus:outline-none focus:ring-2 focus:ring-blue-500"
        aria-label="Next image">
        <i class="fas fa-chevron-right"></i>
      </button>
    </div>

    <!-- Fullscreen button -->
    <button 
      *ngIf="showFullscreen"
      type="button"
      (click)="toggleFullscreen()"
      class="absolute top-3 right-3 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-all focus:outline-none focus:ring-2 focus:ring-blue-500"
      aria-label="Toggle fullscreen">
      <i [class]="isFullscreen ? 'fas fa-compress' : 'fas fa-expand'"></i>
    </button>

    <!-- Image counter -->
    <div *ngIf="images.length > 1" class="absolute bottom-3 left-3 px-2 py-1 bg-black bg-opacity-50 text-white text-sm rounded">
      {{ currentIndex + 1 }} / {{ images.length }}
    </div>
  </div>

  <!-- Image Info -->
  <div *ngIf="showInfo && currentImage" class="p-4 bg-white border-t">
    <h4 *ngIf="currentImage.title" class="font-semibold text-gray-900 mb-1">
      {{ currentImage.title }}
    </h4>
    <p *ngIf="currentImage.description" class="text-sm text-gray-600 mb-2">
      {{ currentImage.description }}
    </p>
    <div *ngIf="currentImage.tags && currentImage.tags.length > 0" class="flex flex-wrap gap-1">
      <span *ngFor="let tag of currentImage.tags" 
            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
        {{ tag }}
      </span>
    </div>
  </div>

  <!-- Thumbnails -->
  <div *ngIf="showThumbnails && images.length > 1" 
       [class]="'thumbnails-' + thumbnailsPosition + ' p-3 bg-gray-50 border-t'">
    <div class="flex space-x-2 overflow-x-auto">
      <button 
        *ngFor="let image of images; let i = index"
        type="button"
        (click)="goToImage(i)"
        [class]="'flex-shrink-0 w-16 h-16 rounded border-2 overflow-hidden transition-all focus:outline-none focus:ring-2 focus:ring-blue-500 ' + 
                 (i === currentIndex ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-300 hover:border-gray-400')"
        [attr.aria-label]="'Go to image ' + (i + 1)">
        <img 
          [src]="image.thumbnail || image.src"
          [alt]="image.alt"
          class="w-full h-full object-cover">
      </button>
    </div>
  </div>
</div>

<!-- Fullscreen Modal -->
<div *ngIf="isFullscreen" 
     class="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center"
     (click)="toggleFullscreen()">
  <div class="relative max-w-full max-h-full p-4" (click)="$event.stopPropagation()">
    <img 
      *ngIf="currentImage"
      [src]="currentImage.src"
      [alt]="currentImage.alt"
      class="max-w-full max-h-full object-contain">
    
    <!-- Close button -->
    <button 
      type="button"
      (click)="toggleFullscreen()"
      class="absolute top-4 right-4 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-all focus:outline-none focus:ring-2 focus:ring-blue-500"
      aria-label="Close fullscreen">
      <i class="fas fa-times"></i>
    </button>

    <!-- Navigation in fullscreen -->
    <div *ngIf="images.length > 1" class="absolute inset-y-0 left-0 right-0 flex items-center justify-between pointer-events-none">
      <button 
        type="button"
        (click)="previousImage()"
        class="ml-4 p-3 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-all pointer-events-auto focus:outline-none focus:ring-2 focus:ring-blue-500"
        aria-label="Previous image">
        <i class="fas fa-chevron-left text-xl"></i>
      </button>
      
      <button 
        type="button"
        (click)="nextImage()"
        class="mr-4 p-3 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-all pointer-events-auto focus:outline-none focus:ring-2 focus:ring-blue-500"
        aria-label="Next image">
        <i class="fas fa-chevron-right text-xl"></i>
      </button>
    </div>
  </div>
</div>
