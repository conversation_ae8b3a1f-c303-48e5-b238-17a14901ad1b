import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface GalleryImage {
  id: string;
  src: string;
  thumbnail?: string;
  alt: string;
  title?: string;
  description?: string;
  tags?: string[];
}

@Component({
  selector: 'lib-image-gallery',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './image-gallery.component.html',
  styleUrl: './image-gallery.component.css'
})
export class ImageGalleryComponent implements OnInit, OnDestroy {
  // Standard inputs for customization
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() images: GalleryImage[] = [
    {
      id: '1',
      src: 'https://via.placeholder.com/600x400/3B82F6/FFFFFF?text=Image+1',
      thumbnail: 'https://via.placeholder.com/150x150/3B82F6/FFFFFF?text=1',
      alt: 'Sample Image 1',
      title: 'Beautiful Landscape',
      description: 'A stunning view of nature'
    },
    {
      id: '2',
      src: 'https://via.placeholder.com/600x400/10B981/FFFFFF?text=Image+2',
      thumbnail: 'https://via.placeholder.com/150x150/10B981/FFFFFF?text=2',
      alt: 'Sample Image 2',
      title: 'City Skyline',
      description: 'Modern architecture at its finest'
    },
    {
      id: '3',
      src: 'https://via.placeholder.com/600x400/F59E0B/FFFFFF?text=Image+3',
      thumbnail: 'https://via.placeholder.com/150x150/F59E0B/FFFFFF?text=3',
      alt: 'Sample Image 3',
      title: 'Ocean View',
      description: 'Peaceful waves and blue horizon'
    }
  ];

  @Input() showThumbnails: boolean = true;
  @Input() showNavigation: boolean = true;
  @Input() showInfo: boolean = true;
  @Input() autoPlay: boolean = false;
  @Input() autoPlayInterval: number = 3000;
  @Input() showFullscreen: boolean = true;
  @Input() thumbnailsPosition: 'bottom' | 'right' | 'left' = 'bottom';
  @Input() aspectRatio: '16:9' | '4:3' | '1:1' | 'auto' = 'auto';

  // Component state
  currentIndex: number = 0;
  isFullscreen: boolean = false;
  autoPlayTimer?: any;

  // Event outputs
  @Output() imageClick = new EventEmitter<GalleryImage>();
  @Output() imageChange = new EventEmitter<{image: GalleryImage, index: number}>();
  @Output() fullscreenToggle = new EventEmitter<boolean>();

  ngOnInit(): void {
    if (this.autoPlay) {
      this.startAutoPlay();
    }
  }

  ngOnDestroy(): void {
    this.stopAutoPlay();
  }

  // Computed getter for CSS classes
  get computedClasses(): string {
    const baseClasses = 'image-gallery-widget';
    
    const sizeClasses = {
      xs: 'max-w-sm',
      sm: 'max-w-md',
      md: 'max-w-lg',
      lg: 'max-w-2xl',
      xl: 'max-w-4xl'
    };

    const variantClasses = {
      default: 'bg-white border-gray-200',
      primary: 'bg-blue-50 border-blue-200',
      secondary: 'bg-gray-50 border-gray-300',
      success: 'bg-green-50 border-green-200',
      warning: 'bg-yellow-50 border-yellow-200',
      danger: 'bg-red-50 border-red-200'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-xl'
    };

    const fullscreenClass = this.isFullscreen ? 'gallery-fullscreen' : '';
    
    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      fullscreenClass,
      'border shadow-lg',
      this.className
    ].filter(Boolean).join(' ');
  }

  get aspectRatioClass(): string {
    const ratioClasses = {
      '16:9': 'aspect-video',
      '4:3': 'aspect-4/3',
      '1:1': 'aspect-square',
      'auto': ''
    };
    return ratioClasses[this.aspectRatio];
  }

  get currentImage(): GalleryImage | null {
    return this.images[this.currentIndex] || null;
  }

  nextImage(): void {
    this.currentIndex = (this.currentIndex + 1) % this.images.length;
    this.emitImageChange();
  }

  previousImage(): void {
    this.currentIndex = this.currentIndex === 0 ? this.images.length - 1 : this.currentIndex - 1;
    this.emitImageChange();
  }

  goToImage(index: number): void {
    if (index >= 0 && index < this.images.length) {
      this.currentIndex = index;
      this.emitImageChange();
    }
  }

  toggleFullscreen(): void {
    this.isFullscreen = !this.isFullscreen;
    this.fullscreenToggle.emit(this.isFullscreen);
  }

  onImageClick(): void {
    if (this.currentImage) {
      this.imageClick.emit(this.currentImage);
    }
  }

  startAutoPlay(): void {
    this.stopAutoPlay();
    this.autoPlayTimer = setInterval(() => {
      this.nextImage();
    }, this.autoPlayInterval);
  }

  stopAutoPlay(): void {
    if (this.autoPlayTimer) {
      clearInterval(this.autoPlayTimer);
      this.autoPlayTimer = undefined;
    }
  }

  private emitImageChange(): void {
    if (this.currentImage) {
      this.imageChange.emit({
        image: this.currentImage,
        index: this.currentIndex
      });
    }
  }
}
