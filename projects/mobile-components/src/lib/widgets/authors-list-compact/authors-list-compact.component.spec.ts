import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DebugElement } from '@angular/core';
import { By } from '@angular/platform-browser';

import { AuthorsListCompactComponent, Author, PaginationConfig } from './authors-list-compact.component';

describe('AuthorsListCompactComponent', () => {
  let component: AuthorsListCompactComponent;
  let fixture: ComponentFixture<AuthorsListCompactComponent>;

  const mockAuthors: <AUTHORS>
    {
      id: '1',
      name: '<PERSON>',
      avatar: 'https://example.com/avatar1.jpg',
      role: 'Writer',
      bio: 'Professional writer',
      email: '<EMAIL>',
      verified: true,
      online: true,
      socialLinks: {
        twitter: '@johndoe',
        linkedin: 'john-doe'
      }
    },
    {
      id: '2',
      name: '<PERSON>',
      avatar: 'https://example.com/avatar2.jpg',
      role: 'Editor',
      bio: 'Professional editor',
      email: '<EMAIL>',
      verified: false,
      online: false,
      lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      socialLinks: {
        github: 'jane<PERSON>'
      }
    }
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AuthorsListCompactComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(AuthorsListCompactComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display default authors', () => {
    expect(component.authors.length).toBe(3);
    expect(component.authors[0].name).toBe('Sarah Johnson');
  });

  it('should display custom authors when provided', () => {
    component.authors = mockAuthors;
    fixture.detectChanges();

    const authorElements = fixture.debugElement.queryAll(By.css('.author-card'));
    expect(authorElements.length).toBe(2);
  });

  it('should show loading state', () => {
    component.loading = true;
    fixture.detectChanges();

    const loadingElement = fixture.debugElement.query(By.css('.loading-spinner'));
    expect(loadingElement).toBeTruthy();
  });

  it('should show empty state when no authors', () => {
    component.authors = [];
    component.loading = false;
    fixture.detectChanges();

    const emptyElement = fixture.debugElement.query(By.css('.empty-state'));
    expect(emptyElement).toBeTruthy();
    expect(emptyElement.nativeElement.textContent).toContain('No authors found');
  });

  it('should show custom empty message', () => {
    component.authors = [];
    component.loading = false;
    component.emptyMessage = 'Custom empty message';
    fixture.detectChanges();

    const emptyElement = fixture.debugElement.query(By.css('.empty-state'));
    expect(emptyElement.nativeElement.textContent).toContain('Custom empty message');
  });

  describe('Layout modes', () => {
    beforeEach(() => {
      component.authors = mockAuthors;
    });

    it('should apply grid layout classes', () => {
      component.layout = 'grid';
      fixture.detectChanges();

      expect(component.computedClasses).toContain('grid');
    });

    it('should apply list layout classes', () => {
      component.layout = 'list';
      fixture.detectChanges();

      expect(component.computedClasses).toContain('space-y-4');
    });

    it('should apply compact layout classes', () => {
      component.layout = 'compact';
      fixture.detectChanges();

      expect(component.computedClasses).toContain('space-y-2');
    });
  });

  describe('Size variants', () => {
    it('should apply size classes correctly', () => {
      component.size = 'lg';
      expect(component.computedClasses).toContain('p-5');

      component.size = 'sm';
      expect(component.computedClasses).toContain('p-3');
    });

    it('should apply avatar size classes', () => {
      component.size = 'lg';
      expect(component.computedAvatarClasses).toContain('w-16');
      expect(component.computedAvatarClasses).toContain('h-16');
    });

    it('should apply name size classes', () => {
      component.size = 'lg';
      expect(component.computedNameClasses).toContain('text-xl');
    });
  });

  describe('Event emissions', () => {
    beforeEach(() => {
      component.authors = mockAuthors;
      fixture.detectChanges();
    });

    it('should emit authorClick when author is clicked', () => {
      spyOn(component.authorClick, 'emit');
      
      const authorElement = fixture.debugElement.query(By.css('.author-card'));
      authorElement.nativeElement.click();

      expect(component.authorClick.emit).toHaveBeenCalledWith({
        author: jasmine.any(Object),
        event: jasmine.any(Event)
      });
    });

    it('should emit avatarClick when avatar is clicked', () => {
      spyOn(component.avatarClick, 'emit');
      
      const avatarElement = fixture.debugElement.query(By.css('.author-avatar'));
      avatarElement.nativeElement.click();

      expect(component.avatarClick.emit).toHaveBeenCalledWith({
        author: jasmine.any(Object),
        event: jasmine.any(Event)
      });
    });

    it('should emit emailClick when email is clicked', () => {
      spyOn(component.emailClick, 'emit');
      
      const emailElement = fixture.debugElement.query(By.css('.author-email'));
      if (emailElement) {
        emailElement.nativeElement.click();
        expect(component.emailClick.emit).toHaveBeenCalled();
      }
    });
  });

  describe('Helper methods', () => {
    it('should generate correct avatar initials', () => {
      expect(component.getAvatarInitials('John Doe')).toBe('JD');
      expect(component.getAvatarInitials('Jane')).toBe('JA');
      expect(component.getAvatarInitials('Mary Jane Watson')).toBe('MJ');
    });

    it('should format last active time correctly', () => {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      expect(component.formatLastActive(oneHourAgo)).toBe('1h ago');
      expect(component.formatLastActive(oneDayAgo)).toBe('1d ago');
    });

    it('should get social links array correctly', () => {
      const socialLinks = {
        twitter: '@test',
        linkedin: 'test-user',
        github: undefined
      };

      const result = component.getSocialLinksArray(socialLinks);
      expect(result.length).toBe(2);
      expect(result[0].platform).toBe('twitter');
      expect(result[1].platform).toBe('linkedin');
    });

    it('should track authors by id', () => {
      const author = mockAuthors[0];
      expect(component.trackByAuthorId(0, author)).toBe('1');
    });
  });

  describe('Pagination', () => {
    const paginationConfig: PaginationConfig = {
      currentPage: 1,
      totalPages: 3,
      itemsPerPage: 2,
      totalItems: 6
    };

    beforeEach(() => {
      component.authors = [
        ...mockAuthors,
        { id: '3', name: 'User 3', role: 'Writer' },
        { id: '4', name: 'User 4', role: 'Editor' },
        { id: '5', name: 'User 5', role: 'Writer' },
        { id: '6', name: 'User 6', role: 'Editor' }
      ];
      component.pagination = paginationConfig;
      component.showPagination = true;
    });

    it('should display correct number of authors per page', () => {
      const displayedAuthors = component.getDisplayedAuthors();
      expect(displayedAuthors.length).toBe(2);
    });

    it('should show pagination controls when enabled', () => {
      fixture.detectChanges();
      const paginationElement = fixture.debugElement.query(By.css('.pagination-controls'));
      expect(paginationElement).toBeTruthy();
    });

    it('should emit pageChange when page button is clicked', () => {
      spyOn(component.pageChange, 'emit');
      component.onPageChange(2);
      expect(component.pageChange.emit).toHaveBeenCalledWith(2);
    });

    it('should generate correct pagination pages', () => {
      const pages = component.getPaginationPages();
      expect(pages).toEqual([1, 2, 3]);
    });
  });

  describe('Visibility controls', () => {
    beforeEach(() => {
      component.authors = mockAuthors;
    });

    it('should hide bio when showBio is false', () => {
      component.showBio = false;
      fixture.detectChanges();

      const bioElements = fixture.debugElement.queryAll(By.css('.author-bio'));
      expect(bioElements.length).toBe(0);
    });

    it('should hide role when showRole is false', () => {
      component.showRole = false;
      fixture.detectChanges();

      const roleElements = fixture.debugElement.queryAll(By.css('.author-role'));
      expect(roleElements.length).toBe(0);
    });

    it('should hide status when showStatus is false', () => {
      component.showStatus = false;
      fixture.detectChanges();

      const statusElements = fixture.debugElement.queryAll(By.css('.status-indicator'));
      expect(statusElements.length).toBe(0);
    });

    it('should hide social links when showSocialLinks is false', () => {
      component.showSocialLinks = false;
      fixture.detectChanges();

      const socialElements = fixture.debugElement.queryAll(By.css('.social-links'));
      expect(socialElements.length).toBe(0);
    });

    it('should hide verified badges when showVerified is false', () => {
      component.showVerified = false;
      fixture.detectChanges();

      const verifiedElements = fixture.debugElement.queryAll(By.css('.verified-badge'));
      expect(verifiedElements.length).toBe(0);
    });
  });

  describe('Avatar error handling', () => {
    it('should handle avatar loading errors', () => {
      const mockEvent = {
        target: {
          style: { display: '' },
          nextElementSibling: {
            style: { display: '' }
          }
        }
      } as any;

      component.onAvatarError(mockEvent);

      expect(mockEvent.target.style.display).toBe('none');
      expect(mockEvent.target.nextElementSibling.style.display).toBe('flex');
    });
  });
});
