<!-- Authors List Container -->
<div [class]="computedClasses" role="list" [attr.aria-label]="'List of ' + authors.length + ' authors'">
  
  <!-- Loading State -->
  <div *ngIf="loading" class="flex items-center justify-center py-8">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    <span class="ml-3 text-gray-600">Loading authors...</span>
  </div>
  
  <!-- Empty State -->
  <div *ngIf="!loading && authors.length === 0" 
       class="text-center py-8 text-gray-500">
    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
      </path>
    </svg>
    <p class="mt-4 text-lg">{{ emptyMessage }}</p>
  </div>
  
  <!-- Authors List -->
  <div *ngIf="!loading && authors.length > 0">
    
    <!-- Author Cards -->
    <div *ngFor="let author of authors; trackBy: trackByAuthorId" 
         [class]="computedAuthorCardClasses"
         (click)="onAuthorClick(author, $event)"
         role="listitem"
         [attr.aria-label]="'Author: ' + author.name + (author.role ? ', ' + author.role : '')">
      
      <!-- Avatar Section -->
      <div class="flex-shrink-0 relative">
        <img *ngIf="author.avatar" 
             [src]="author.avatar" 
             [alt]="author.name + ' avatar'"
             [class]="computedAvatarClasses"
             (click)="onAvatarClick(author, $event)"              (error)="onAvatarError($event)"
             loading="lazy">
        
        <!-- Fallback Avatar with Initials -->
        <div *ngIf="!author.avatar" 
             [class]="computedAvatarClasses + ' bg-gray-300 flex items-center justify-center text-gray-700 font-medium'"
             (click)="onAvatarClick(author, $event)">
          {{ getAvatarInitials(author.name) }}
        </div>
        
        <!-- Hidden fallback for error cases -->
        <div [class]="computedAvatarClasses + ' bg-gray-300 items-center justify-center text-gray-700 font-medium hidden'"
             (click)="onAvatarClick(author, $event)">
          {{ getAvatarInitials(author.name) }}
        </div>
        
        <!-- Online Status Indicator -->
        <div *ngIf="showStatus && author.online" 
             class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full"
             title="Online now">
        </div>
        
        <!-- Verified Badge -->
        <div *ngIf="showVerified && author.verified" 
             class="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center"
             title="Verified author">
          <svg class="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
          </svg>
        </div>
      </div>
      
      <!-- Author Info Section -->
      <div class="flex-1 min-w-0" [class.ml-3]="layout !== 'grid'" [class.mt-3]="layout === 'grid'">
        
        <!-- Name and Role -->
        <div [class.text-center]="layout === 'grid'">
          <h3 [class]="computedNameClasses">
            {{ author.name }}
            
            <!-- Verified inline badge for compact layout -->
            <svg *ngIf="showVerified && author.verified && layout === 'compact'" 
                 class="inline w-4 h-4 text-blue-500 ml-1" 
                 fill="currentColor" viewBox="0 0 20 20"
                 title="Verified author">
              <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
          </h3>
          
          <p *ngIf="showRole && author.role" 
             class="text-sm text-gray-600 mt-1">
            {{ author.role }}
          </p>
        </div>
        
        <!-- Bio -->
        <p *ngIf="showBio && author.bio" 
           class="text-sm text-gray-600 mt-2 line-clamp-2"
           [class.text-center]="layout === 'grid'">
          {{ author.bio }}
        </p>
        
        <!-- Contact Information -->
        <div class="mt-3 flex items-center space-x-4" 
             [class.justify-center]="layout === 'grid'">
          
          <!-- Email -->
          <button *ngIf="author.email && layout !== 'compact'" 
                  type="button"
                  (click)="onEmailClick(author, $event)"
                  class="text-sm text-blue-600 hover:text-blue-800 flex items-center"
                  [title]="'Email ' + author.name">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
            </svg>
            Contact
          </button>
          
          <!-- Social Links -->
          <div *ngIf="showSocialLinks && author.socialLinks" 
               class="flex items-center space-x-2">
            <button *ngFor="let link of getSocialLinksArray(author.socialLinks)" 
                    type="button"
                    (click)="onSocialLinkClick(author, link.platform, link.url, $event)"
                    class="text-gray-500 hover:text-gray-700 transition-colors"
                    [title]="'Visit ' + author.name + ' on ' + link.platform">
              <i [class]="getSocialIcon(link.platform) + ' w-4 h-4'"></i>
            </button>
          </div>
        </div>
        
        <!-- Status Information -->
        <div *ngIf="showStatus" class="mt-2 text-xs text-gray-500"
             [class.text-center]="layout === 'grid'">
          <span *ngIf="author.online" class="text-green-600 font-medium">
            • Online now
          </span>
          <span *ngIf="!author.online && author.lastActive" class="text-gray-500">
            Last active {{ formatLastActive(author.lastActive) }}
          </span>
        </div>
      </div>
    </div>
    
    <!-- Pagination -->
    <div *ngIf="showPagination && pagination" 
         class="mt-6 flex items-center justify-between border-t border-gray-200 pt-4">
      
      <div class="text-sm text-gray-700">
        Showing {{ ((pagination.currentPage - 1) * pagination.itemsPerPage) + 1 }} to 
        {{ Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems) }} of 
        {{ pagination.totalItems }} authors
      </div>
      
      <div class="flex items-center space-x-2">
        <button type="button"
                (click)="onPageChange(pagination.currentPage - 1)"
                [disabled]="pagination.currentPage <= 1"
                class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
          Previous
        </button>
        
        <div class="flex items-center space-x-1">
          <button *ngFor="let page of getPaginationPages()" 
                  type="button"
                  (click)="onPageChange(page)"
                  [class]="page === pagination.currentPage ? 
                          'px-3 py-1 text-sm bg-blue-600 text-white rounded-md' : 
                          'px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50'">
            {{ page }}
          </button>
        </div>
        
        <button type="button"
                (click)="onPageChange(pagination.currentPage + 1)"
                [disabled]="pagination.currentPage >= pagination.totalPages"
                class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
          Next
        </button>
      </div>
    </div>
  </div>
</div>
