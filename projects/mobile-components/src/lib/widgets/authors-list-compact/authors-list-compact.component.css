/* Authors List Compact Widget Styles */
.authors-list-compact-widget {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Author Card Hover Effects */
.author-card {
  transition: all 0.2s ease-in-out;
}

.author-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.author-card:active {
  transform: translateY(0);
}

/* Avatar Styling */
.author-card img {
  transition: transform 0.2s ease-in-out;
}

.author-card:hover img {
  transform: scale(1.05);
}

/* Loading Animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Bio Text Clamping */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Status Indicator Pulse */
.author-card .bg-green-500 {
  animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Social Link Hover Effects */
.author-card button i {
  transition: all 0.15s ease-in-out;
}

.author-card button:hover i {
  transform: scale(1.2);
}

/* Verified Badge Animation */
.author-card .bg-blue-500 {
  animation: gentle-bounce 3s ease-in-out infinite;
}

@keyframes gentle-bounce {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Responsive Grid Adjustments */
@media (max-width: 640px) {
  .authors-list-compact-widget.grid {
    grid-template-columns: 1fr;
  }
  
  .author-card {
    flex-direction: column;
    text-align: center;
  }
  
  .author-card .flex-1 {
    margin-left: 0;
    margin-top: 0.75rem;
  }
}

/* Focus States for Accessibility */
.author-card:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.author-card button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 4px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .author-card {
    border-width: 2px;
    border-color: #000;
  }
  
  .author-card .bg-gray-50 {
    background-color: #fff;
    border-color: #000;
  }
  
  .text-gray-600 {
    color: #000;
  }
  
  .text-gray-500 {
    color: #333;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .author-card,
  .author-card img,
  .author-card button i,
  .animate-spin {
    transition: none;
    animation: none;
  }
  
  .author-card:hover {
    transform: none;
  }
  
  .author-card:hover img {
    transform: none;
  }
}

/* Print Styles */
@media print {
  .author-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .author-card button {
    display: none;
  }
  
  .bg-green-500,
  .bg-blue-500 {
    background-color: #000 !important;
  }
}

/* Dark Mode Support (if enabled) */
@media (prefers-color-scheme: dark) {
  .authors-list-compact-widget {
    color-scheme: dark;
  }
}

/* Pagination Styling */
.authors-list-compact-widget button {
  transition: all 0.15s ease-in-out;
}

.authors-list-compact-widget button:hover:not(:disabled) {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

.authors-list-compact-widget button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Empty State Styling */
.authors-list-compact-widget svg {
  transition: transform 0.3s ease-in-out;
}

.authors-list-compact-widget svg:hover {
  transform: scale(1.05);
}