import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

// Interface for author data
export interface Author {
  id: string;
  name: string;
  avatar?: string;
  role?: string;
  bio?: string;
  email?: string;
  website?: string;
  socialLinks?: {
    twitter?: string;
    linkedin?: string;
    github?: string;
  };
  verified?: boolean;
  online?: boolean;
  lastActive?: Date;
}

// Interface for pagination data
export interface PaginationConfig {
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;
  totalItems: number;
}

@Component({
  selector: 'lib-authors-list-compact',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './authors-list-compact.component.html',
  styleUrl: './authors-list-compact.component.css'
})
export class AuthorsListCompactComponent {
  // Content inputs
  @Input() authors: Author[] = [
    {
      id: '1',
      name: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b602?w=150',
      role: 'Senior Writer',
      bio: 'Experienced technical writer with expertise in software development and user experience.',
      email: '<EMAIL>',
      verified: true,
      online: true,
      socialLinks: {
        twitter: '@sarahjohnson',
        linkedin: 'sarah-johnson-writer'
      }
    },
    {
      id: '2',
      name: 'Michael Chen',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      role: 'Content Strategist',
      bio: 'Digital marketing expert focused on content strategy and audience engagement.',
      email: '<EMAIL>',
      verified: true,
      online: false,
      lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      socialLinks: {
        linkedin: 'michael-chen-strategist',
        github: 'mchen-dev'
      }
    },
    {
      id: '3',
      name: 'Emily Rodriguez',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
      role: 'Editor',
      bio: 'Professional editor with a passion for clear, compelling content.',
      email: '<EMAIL>',
      verified: false,
      online: true,
      socialLinks: {
        twitter: '@emily_editor'
      }
    }
  ];
  @Input() layout: 'grid' | 'list' | 'compact' = 'compact';
  @Input() showPagination: boolean = false;
  @Input() showBio: boolean = true;
  @Input() showRole: boolean = true;
  @Input() showStatus: boolean = true;
  @Input() showSocialLinks: boolean = false;
  @Input() showVerified: boolean = true;
  @Input() pagination: PaginationConfig | null = null;
  @Input() loading: boolean = false;
  @Input() emptyMessage: string = 'No authors found';
  
  // Standard Tailwind inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'lg';
  
  // Events
  @Output() authorClick = new EventEmitter<{ author: Author; event: Event }>();
  @Output() avatarClick = new EventEmitter<{ author: Author; event: Event }>();
  @Output() socialLinkClick = new EventEmitter<{ author: Author; platform: string; url: string; event: Event }>();
  @Output() pageChange = new EventEmitter<number>();
  @Output() emailClick = new EventEmitter<{ author: Author; event: Event }>();

  get computedClasses(): string {
    const baseClasses = ['authors-list-compact-widget', 'w-full'];
    
    // Layout classes
    const layoutClasses = {
      grid: ['grid', 'gap-4', 'grid-cols-1', 'sm:grid-cols-2', 'lg:grid-cols-3'],
      list: ['space-y-4'],
      compact: ['space-y-2']
    };

    // Size classes for overall spacing
    const sizeClasses = {
      xs: ['p-2'],
      sm: ['p-3'],
      md: ['p-4'],
      lg: ['p-5'],
      xl: ['p-6']
    };

    // Variant classes for container
    const variantClasses = {
      default: ['bg-white', 'border', 'border-gray-200'],
      primary: ['bg-blue-50', 'border', 'border-blue-200'],
      secondary: ['bg-gray-50', 'border', 'border-gray-200'],
      success: ['bg-green-50', 'border', 'border-green-200'],
      warning: ['bg-yellow-50', 'border', 'border-yellow-200'],
      danger: ['bg-red-50', 'border', 'border-red-200']
    };

    // Rounded classes
    const roundedClasses = {
      none: ['rounded-none'],
      sm: ['rounded-sm'],
      md: ['rounded-md'],
      lg: ['rounded-lg'],
      full: ['rounded-2xl']
    };

    const classes = [
      ...baseClasses,
      ...layoutClasses[this.layout],
      ...sizeClasses[this.size],
      ...variantClasses[this.variant],
      ...roundedClasses[this.rounded],
      this.className
    ];

    return classes.filter(Boolean).join(' ');
  }

  get computedAuthorCardClasses(): string {
    const baseClasses = ['author-card', 'flex', 'items-start', 'transition-all', 'duration-200', 'hover:shadow-md', 'cursor-pointer'];
    
    // Layout-specific card classes
    const layoutCardClasses = {
      grid: ['flex-col', 'text-center', 'p-4', 'bg-white', 'border', 'border-gray-200', 'rounded-lg'],
      list: ['flex-row', 'p-4', 'bg-white', 'border', 'border-gray-200', 'rounded-lg'],
      compact: ['flex-row', 'p-3', 'bg-gray-50', 'border-l-4', 'border-blue-500']
    };

    // Size-specific spacing
    const sizeCardClasses = {
      xs: this.layout === 'compact' ? ['space-x-2'] : ['space-y-2'],
      sm: this.layout === 'compact' ? ['space-x-2'] : ['space-y-2'],
      md: this.layout === 'compact' ? ['space-x-3'] : ['space-y-3'],
      lg: this.layout === 'compact' ? ['space-x-4'] : ['space-y-4'],
      xl: this.layout === 'compact' ? ['space-x-4'] : ['space-y-4']
    };

    return [
      ...baseClasses,
      ...layoutCardClasses[this.layout],
      ...sizeCardClasses[this.size]
    ].join(' ');
  }

  get computedAvatarClasses(): string {
    const baseClasses = ['flex-shrink-0', 'rounded-full', 'object-cover'];
    
    // Size classes for avatars
    const sizeClasses = {
      xs: ['w-8', 'h-8'],
      sm: ['w-10', 'h-10'],
      md: ['w-12', 'h-12'],
      lg: ['w-16', 'h-16'],
      xl: ['w-20', 'h-20']
    };

    return [...baseClasses, ...sizeClasses[this.size]].join(' ');
  }

  get computedNameClasses(): string {
    const baseClasses = ['font-semibold', 'text-gray-900'];
    
    // Size classes for names
    const sizeClasses = {
      xs: ['text-sm'],
      sm: ['text-base'],
      md: ['text-lg'],
      lg: ['text-xl'],
      xl: ['text-2xl']
    };

    return [...baseClasses, ...sizeClasses[this.size]].join(' ');
  }

  // Helper methods
  Math = Math; // Expose Math for template use

  getDisplayedAuthors(): Author[] {
    if (!this.pagination) {
      return this.authors;
    }
    
    const startIndex = (this.pagination.currentPage - 1) * this.pagination.itemsPerPage;
    const endIndex = startIndex + this.pagination.itemsPerPage;
    return this.authors.slice(startIndex, endIndex);
  }

  onAvatarError(event: Event): void {
    const target = event.target as HTMLImageElement;
    if (target && target.nextElementSibling) {
      target.style.display = 'none';
      (target.nextElementSibling as HTMLElement).style.display = 'flex';
    }
  }

  onAuthorClick(author: Author, event: Event): void {
    event.preventDefault();
    this.authorClick.emit({ author, event });
  }

  onAvatarClick(author: Author, event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.avatarClick.emit({ author, event });
  }

  onSocialLinkClick(author: Author, platform: string, url: string, event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.socialLinkClick.emit({ author, platform, url, event });
  }

  onEmailClick(author: Author, event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    this.emailClick.emit({ author, event });
  }

  onPageChange(page: number): void {
    this.pageChange.emit(page);
  }

  getAvatarInitials(name: string): string {
    return name
      .split(' ')
      .map(part => part.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  }

  formatLastActive(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffHours < 1) return 'Just now';
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  }

  getSocialIcon(platform: string): string {
    const icons = {
      twitter: 'fab fa-twitter',
      linkedin: 'fab fa-linkedin',
      github: 'fab fa-github'
    };
    return icons[platform as keyof typeof icons] || 'fas fa-link';
  }

  getSocialLinksArray(socialLinks: Author['socialLinks']): Array<{platform: string, url: string}> {
    if (!socialLinks) return [];
    
    return Object.entries(socialLinks)
      .filter(([_, url]) => url)
      .map(([platform, url]) => ({ platform, url: url! }));
  }

  trackByAuthorId(index: number, author: Author): string {
    return author.id;
  }

  getPaginationPages(): number[] {
    if (!this.pagination) return [];
    
    const { currentPage, totalPages } = this.pagination;
    const pages: number[] = [];
    const maxVisible = 5;
    
    if (totalPages <= maxVisible) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      const start = Math.max(1, currentPage - 2);
      const end = Math.min(totalPages, start + maxVisible - 1);
      
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
    }
    
    return pages;
  }
}
