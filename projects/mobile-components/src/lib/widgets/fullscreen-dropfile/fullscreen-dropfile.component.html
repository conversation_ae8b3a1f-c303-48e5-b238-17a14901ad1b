<!-- Fullscreen dropfile overlay -->
<div [ngClass]="containerClasses" [class.hidden]="!isVisible">
  
  <!-- Main dropzone container -->
  <div [ngClass]="dropzoneClasses"
       (dragover)="onDragOver($event)"
       (dragenter)="onDragEnter($event)"
       (dragleave)="onDragLeave($event)"
       (drop)="onDrop($event)">
    
    <!-- Header -->
    <div class="flex justify-between items-center p-6 border-b border-gray-200">
      <div>
        <h2 class="text-2xl font-bold text-gray-900">{{ title }}</h2>
        <p class="text-gray-600 mt-1">{{ subtitle }}</p>
      </div>
      <button 
        (click)="closeDropzone()"
        class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Content Area -->
    <div class="flex-1 p-6">
      
      <!-- No files state / Drop area -->
      <div *ngIf="totalFiles === 0" class="text-center py-16">
        
        <!-- Drop zone -->
        <div class="relative border-2 border-dashed border-gray-300 rounded-lg p-12 transition-colors"
             [class.border-blue-500]="isDragOver()"
             [class.bg-blue-50]="isDragOver()">
          
          <!-- Upload icon -->
          <div class="mx-auto w-16 h-16 text-gray-400 mb-4">
            <svg class="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
            </svg>
          </div>
          
          <!-- Drop text -->
          <p class="text-xl text-gray-600 mb-2">{{ dropzoneText }}</p>
          <p class="text-gray-500 mb-6">{{ browseText }}</p>
          
          <!-- Browse button -->
          <button 
            (click)="openFileBrowser()"
            class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Select Files
          </button>
          
          <!-- Supported formats -->
          <div class="mt-6 text-sm text-gray-500">
            <p class="mb-2">Supported formats:</p>
            <div class="flex flex-wrap justify-center gap-2">
              <span *ngFor="let format of supportedFormats" 
                    class="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                {{ format }}
              </span>
            </div>
          </div>
          
          <!-- Limits info -->
          <div class="mt-4 text-xs text-gray-400 space-y-1">
            <p>Maximum {{ maxFiles }} files</p>
            <p>Up to {{ formatFileSize(maxSize) }} per file</p>
          </div>
        </div>
      </div>

      <!-- Files list -->
      <div *ngIf="totalFiles > 0" class="space-y-4">
        
        <!-- Summary bar -->
        <div class="flex items-center justify-between bg-gray-50 rounded-lg p-4">
          <div class="flex items-center space-x-4">
            <div class="text-sm">
              <span class="font-medium text-gray-900">{{ totalFiles }}</span>
              <span class="text-gray-600">{{ totalFiles === 1 ? 'file' : 'files' }}</span>
            </div>
            <div class="text-sm text-gray-600">
              Total size: {{ totalSize }}
            </div>
          </div>
          
          <div class="flex items-center space-x-2">
            <!-- Add more files button -->
            <button 
              (click)="openFileBrowser()"
              class="px-3 py-1 text-sm text-blue-600 hover:text-blue-700 font-medium">
              Add More
            </button>
            
            <!-- Clear all button -->
            <button 
              (click)="clearFiles()"
              class="px-3 py-1 text-sm text-gray-600 hover:text-gray-700 font-medium">
              {{ clearButtonText }}
            </button>
          </div>
        </div>

        <!-- Files grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
          <div *ngFor="let file of files(); trackBy: trackByFileId"
               class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
            
            <!-- File header -->
            <div class="flex items-start justify-between mb-3">
              <div class="flex items-center space-x-3 flex-1 min-w-0">
                <!-- File icon or preview -->
                <div class="flex-shrink-0">
                  <div *ngIf="file.preview && showPreview" class="w-12 h-12 rounded-lg overflow-hidden bg-gray-100">
                    <img [src]="file.preview" [alt]="file.name" class="w-full h-full object-cover">
                  </div>
                  <div *ngIf="!file.preview || !showPreview" 
                       class="w-12 h-12 flex items-center justify-center bg-gray-100 rounded-lg text-2xl">
                    {{ getFileIcon(file) }}
                  </div>
                </div>
                
                <!-- File info -->
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900 truncate" [title]="file.name">
                    {{ file.name }}
                  </p>
                  <p class="text-xs text-gray-500">
                    {{ formatFileSize(file.size) }}
                  </p>
                </div>
              </div>
              
              <!-- Status and actions -->
              <div class="flex items-center space-x-2 ml-2">
                <!-- Status icon -->
                <span class="text-lg" [title]="file.status">
                  {{ getStatusIcon(file.status) }}
                </span>
                
                <!-- Remove button -->
                <button 
                  (click)="removeFile(file)"
                  class="p-1 text-gray-400 hover:text-red-500 rounded transition-colors"
                  [title]="removeFileText">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>
            </div>
            
            <!-- Progress bar -->
            <div *ngIf="file.status === 'uploading' && showProgress" class="mb-2">
              <div class="flex justify-between text-xs text-gray-600 mb-1">
                <span>Uploading...</span>
                <span>{{ file.progress }}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-1.5">
                <div class="bg-blue-600 h-1.5 rounded-full transition-all duration-300" 
                     [style.width.%]="file.progress"></div>
              </div>
            </div>
            
            <!-- Error message -->
            <div *ngIf="file.status === 'error' && file.error" 
                 class="text-xs text-red-600 bg-red-50 p-2 rounded mt-2">
              {{ file.error }}
            </div>
          </div>
        </div>

        <!-- Overall progress -->
        <div *ngIf="isUploading() && showProgress" class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-blue-900">Overall Progress</span>
            <span class="text-sm text-blue-700">{{ overallProgress }}%</span>
          </div>
          <div class="w-full bg-blue-200 rounded-full h-2">
            <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                 [style.width.%]="overallProgress"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="border-t border-gray-200 p-6">
      <div class="flex justify-between items-center">
        <button 
          (click)="closeDropzone()"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
          {{ closeButtonText }}
        </button>
        
        <div class="flex space-x-3">
          <!-- Upload button -->
          <button 
            *ngIf="totalFiles > 0 && !isUploading() && !autoUpload"
            (click)="uploadFiles()"
            class="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
            {{ uploadButtonText }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Hidden file input -->
<input 
  #fileInput
  type="file"
  class="hidden"
  [multiple]="allowMultiple"
  [accept]="acceptedTypes.join(',')"
  (change)="onFileSelect($event)">

<!-- Backdrop click handler -->
<div *ngIf="isVisible" 
     class="fixed inset-0 z-40 bg-black bg-opacity-50"
     (click)="closeDropzone()"></div>
