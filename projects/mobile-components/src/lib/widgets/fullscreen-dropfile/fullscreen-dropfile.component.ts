import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  ViewChild,
  ElementRef,
  signal,
  computed
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

export interface DropfileFile {
  id: string;
  name: string;
  size: number;
  type: string;
  lastModified: number;
  file: File;
  status: 'pending' | 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
  preview?: string;
}

export interface DropfileEvent {
  files: DropfileFile[];
  action: 'drop' | 'select' | 'remove' | 'clear';
}

export interface DropfileConfig {
  maxFiles: number;
  maxSize: number; // in bytes
  acceptedTypes: string[];
  allowMultiple: boolean;
  autoUpload: boolean;
  showPreview: boolean;
  showProgress: boolean;
}

@Component({
  selector: 'fullscreen-dropfile',
  templateUrl: './fullscreen-dropfile.component.html',
  styleUrls: ['./fullscreen-dropfile.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule
  ]
})
export class FullscreenDropfileComponent implements OnInit {
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'lg';
  
  // Component-specific properties
  @Input() isVisible: boolean = false;
  @Input() title: string = 'Drop Files Here';
  @Input() subtitle: string = 'Drag and drop files or click to browse';
  @Input() acceptedTypes: string[] = ['image/*', 'application/pdf', '.doc', '.docx'];
  @Input() maxFiles: number = 10;
  @Input() maxSize: number = 10 * 1024 * 1024; // 10MB
  @Input() allowMultiple: boolean = true;
  @Input() autoUpload: boolean = false;
  @Input() showPreview: boolean = true;
  @Input() showProgress: boolean = true;
  @Input() uploadUrl: string = '';
  @Input() supportedFormats: string[] = ['JPG', 'PNG', 'PDF', 'DOC', 'DOCX'];
  @Input() dropzoneText: string = 'Drop files here to upload';
  @Input() browseText: string = 'or browse files';
  @Input() closeButtonText: string = 'Close';
  @Input() uploadButtonText: string = 'Upload Files';
  @Input() clearButtonText: string = 'Clear All';
  @Input() removeFileText: string = 'Remove';

  // Events
  @Output() filesDrop = new EventEmitter<DropfileEvent>();
  @Output() fileSelect = new EventEmitter<DropfileEvent>();
  @Output() fileRemove = new EventEmitter<DropfileEvent>();
  @Output() filesUpload = new EventEmitter<DropfileFile[]>();
  @Output() uploadProgress = new EventEmitter<{ file: DropfileFile; progress: number }>();
  @Output() uploadComplete = new EventEmitter<DropfileFile[]>();
  @Output() uploadError = new EventEmitter<{ file: DropfileFile; error: string }>();
  @Output() close = new EventEmitter<void>();
  @Output() clear = new EventEmitter<void>();

  @ViewChild('fileInput', { static: false }) fileInput!: ElementRef<HTMLInputElement>;

  // State
  isDragOver = signal(false);
  isUploading = signal(false);
  files = signal<DropfileFile[]>([]);
  dragCounter = 0;

  get config(): DropfileConfig {
    return {
      maxFiles: this.maxFiles,
      maxSize: this.maxSize,
      acceptedTypes: this.acceptedTypes,
      allowMultiple: this.allowMultiple,
      autoUpload: this.autoUpload,
      showPreview: this.showPreview,
      showProgress: this.showProgress
    };
  }

  get containerClasses(): string {
    const baseClasses = 'fixed inset-0 z-50 flex items-center justify-center transition-all duration-300';
    const sizeClasses = {
      xs: 'p-2',
      sm: 'p-4',
      md: 'p-6',
      lg: 'p-8',
      xl: 'p-12'
    };

    const variantClasses = {
      default: 'bg-black bg-opacity-50',
      primary: 'bg-blue-900 bg-opacity-50',
      secondary: 'bg-gray-900 bg-opacity-50',
      success: 'bg-green-900 bg-opacity-50',
      warning: 'bg-yellow-900 bg-opacity-50',
      danger: 'bg-red-900 bg-opacity-50'
    };

    return `
      ${baseClasses}
      ${sizeClasses[this.size]}
      ${variantClasses[this.variant]}
      ${this.isVisible ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'}
      ${this.className}
    `.trim().replace(/\s+/g, ' ');
  }

  get dropzoneClasses(): string {
    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-2xl'
    };

    const dragOverClasses = this.isDragOver() 
      ? 'border-blue-500 bg-blue-50 scale-105' 
      : 'border-gray-300 bg-white hover:border-gray-400';

    return `
      w-full max-w-4xl max-h-full overflow-auto
      bg-white border-2 border-dashed
      ${roundedClasses[this.rounded]}
      ${dragOverClasses}
      transition-all duration-200
      shadow-2xl
    `.trim().replace(/\s+/g, ' ');
  }

  get totalFiles(): number {
    return this.files().length;
  }

  get totalSize(): string {
    const bytes = this.files().reduce((sum, file) => sum + file.size, 0);
    return this.formatFileSize(bytes);
  }

  get overallProgress(): number {
    const files = this.files();
    if (files.length === 0) return 0;
    const totalProgress = files.reduce((sum, file) => sum + file.progress, 0);
    return Math.round(totalProgress / files.length);
  }

  ngOnInit(): void {
    // Initialize with empty state
    this.files.set([]);
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
  }

  onDragEnter(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.dragCounter++;
    this.isDragOver.set(true);
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.dragCounter--;
    if (this.dragCounter === 0) {
      this.isDragOver.set(false);
    }
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.dragCounter = 0;
    this.isDragOver.set(false);

    const droppedFiles = Array.from(event.dataTransfer?.files || []);
    this.processFiles(droppedFiles, 'drop');
  }

  onFileSelect(event: Event): void {
    const input = event.target as HTMLInputElement;
    const selectedFiles = Array.from(input.files || []);
    this.processFiles(selectedFiles, 'select');
    
    // Reset input
    input.value = '';
  }

  processFiles(fileList: File[], action: 'drop' | 'select'): void {
    const currentFiles = this.files();
    const newFiles: DropfileFile[] = [];

    for (const file of fileList) {
      // Check file count limit
      if (currentFiles.length + newFiles.length >= this.maxFiles) {
        break;
      }

      // Check file size
      if (file.size > this.maxSize) {
        continue;
      }

      // Check file type
      if (this.acceptedTypes.length > 0 && !this.isFileTypeAccepted(file)) {
        continue;
      }

      const dropfileFile: DropfileFile = {
        id: this.generateFileId(),
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified,
        file: file,
        status: 'pending',
        progress: 0
      };

      // Generate preview for images
      if (this.showPreview && file.type.startsWith('image/')) {
        this.generatePreview(dropfileFile);
      }

      newFiles.push(dropfileFile);
    }

    if (newFiles.length > 0) {
      const updatedFiles = this.allowMultiple ? [...currentFiles, ...newFiles] : newFiles;
      this.files.set(updatedFiles);

      const event: DropfileEvent = {
        files: newFiles,
        action
      };

      if (action === 'drop') {
        this.filesDrop.emit(event);
      } else {
        this.fileSelect.emit(event);
      }

      if (this.autoUpload) {
        this.uploadFiles();
      }
    }
  }

  removeFile(fileToRemove: DropfileFile): void {
    const currentFiles = this.files();
    const updatedFiles = currentFiles.filter(file => file.id !== fileToRemove.id);
    this.files.set(updatedFiles);

    this.fileRemove.emit({
      files: [fileToRemove],
      action: 'remove'
    });
  }

  clearFiles(): void {
    this.files.set([]);
    this.clear.emit();
  }

  uploadFiles(): void {
    const pendingFiles = this.files().filter(file => file.status === 'pending');
    if (pendingFiles.length === 0) return;

    this.isUploading.set(true);
    this.filesUpload.emit(pendingFiles);

    // Simulate upload progress (replace with actual upload logic)
    pendingFiles.forEach(file => {
      this.simulateUpload(file);
    });
  }

  openFileBrowser(): void {
    this.fileInput?.nativeElement?.click();
  }

  closeDropzone(): void {
    this.close.emit();
  }

  private isFileTypeAccepted(file: File): boolean {
    return this.acceptedTypes.some(type => {
      if (type.startsWith('.')) {
        return file.name.toLowerCase().endsWith(type.toLowerCase());
      }
      if (type.includes('*')) {
        const baseType = type.split('/')[0];
        return file.type.startsWith(baseType);
      }
      return file.type === type;
    });
  }

  private generateFileId(): string {
    return 'file-' + Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  private generatePreview(file: DropfileFile): void {
    const reader = new FileReader();
    reader.onload = (e) => {
      file.preview = e.target?.result as string;
      // Trigger change detection
      this.files.set([...this.files()]);
    };
    reader.readAsDataURL(file.file);
  }

  private simulateUpload(file: DropfileFile): void {
    file.status = 'uploading';
    let progress = 0;

    const interval = setInterval(() => {
      progress += Math.random() * 20;
      if (progress >= 100) {
        progress = 100;
        file.status = 'success';
        clearInterval(interval);
        
        this.uploadComplete.emit([file]);
        
        // Check if all uploads are complete
        const allFiles = this.files();
        const uploadingFiles = allFiles.filter(f => f.status === 'uploading');
        if (uploadingFiles.length === 0) {
          this.isUploading.set(false);
        }
      }

      file.progress = Math.round(progress);
      this.uploadProgress.emit({ file, progress: file.progress });
      
      // Trigger change detection
      this.files.set([...this.files()]);
    }, 200);
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getFileIcon(file: DropfileFile): string {
    if (file.type.startsWith('image/')) return '🖼️';
    if (file.type.includes('pdf')) return '📄';
    if (file.type.includes('word') || file.name.endsWith('.doc') || file.name.endsWith('.docx')) return '📝';
    if (file.type.includes('excel') || file.name.endsWith('.xls') || file.name.endsWith('.xlsx')) return '📊';
    if (file.type.includes('powerpoint') || file.name.endsWith('.ppt') || file.name.endsWith('.pptx')) return '📽️';
    if (file.type.startsWith('video/')) return '🎥';
    if (file.type.startsWith('audio/')) return '🎵';
    return '📎';
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'pending': return '⏳';
      case 'uploading': return '📤';
      case 'success': return '✅';
      case 'error': return '❌';
      default: return '📎';
    }
  }

  trackByFileId(index: number, file: DropfileFile): string {
    return file.id;
  }
}
