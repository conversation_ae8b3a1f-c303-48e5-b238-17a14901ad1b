import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface PlaceholderConfig {
  lines?: number;
  showAvatar?: boolean;
  showImage?: boolean;
  showButton?: boolean;
  animated?: boolean;
}

@Component({
  selector: 'lib-placeholder-compact',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './placeholder-compact.component.html',
  styleUrl: './placeholder-compact.component.css'
})
export class PlaceholderCompactComponent {
  // Standard Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() type: 'text' | 'card' | 'list' | 'image' | 'custom' = 'text';
  @Input() lines: number = 3;
  @Input() showAvatar: boolean = false;
  @Input() showImage: boolean = false;
  @Input() showButton: boolean = false;
  @Input() animated: boolean = true;
  @Input() title: string = '';
  @Input() subtitle: string = '';
  @Input() config: PlaceholderConfig = {};

  get placeholderLines(): number[] {
    const lineCount = this.config.lines || this.lines;
    return Array.from({ length: lineCount }, (_, i) => i);
  }

  get containerClasses(): string {
    const baseClasses = 'space-y-3';
    const sizeClasses = {
      xs: 'p-2',
      sm: 'p-3',
      md: 'p-4',
      lg: 'p-5',
      xl: 'p-6'
    };

    return [
      baseClasses,
      sizeClasses[this.size],
      this.className
    ].filter(Boolean).join(' ');
  }

  get skeletonClasses(): string {
    const baseClasses = 'bg-gray-200';
    const animatedClasses = this.animated ? 'animate-pulse' : '';
    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    return [
      baseClasses,
      animatedClasses,
      roundedClasses[this.rounded]
    ].filter(Boolean).join(' ');
  }

  getLineClasses(index: number): string {
    const baseClasses = this.skeletonClasses;
    const heightClasses = 'h-4';

    // Vary line widths for more realistic appearance
    const widthVariations = ['w-full', 'w-5/6', 'w-4/5', 'w-3/4', 'w-2/3'];
    const widthClass = index === this.placeholderLines.length - 1
      ? widthVariations[Math.min(index, widthVariations.length - 1)]
      : 'w-full';

    return [baseClasses, heightClasses, widthClass].join(' ');
  }

  get avatarClasses(): string {
    const baseClasses = this.skeletonClasses;
    const sizeClasses = {
      xs: 'w-6 h-6',
      sm: 'w-8 h-8',
      md: 'w-10 h-10',
      lg: 'w-12 h-12',
      xl: 'w-16 h-16'
    };

    return [baseClasses, sizeClasses[this.size], 'rounded-full'].join(' ');
  }

  get imageClasses(): string {
    const baseClasses = this.skeletonClasses;
    const sizeClasses = {
      xs: 'w-16 h-12',
      sm: 'w-20 h-16',
      md: 'w-24 h-20',
      lg: 'w-32 h-24',
      xl: 'w-40 h-32'
    };

    return [baseClasses, sizeClasses[this.size]].join(' ');
  }

  get buttonClasses(): string {
    const baseClasses = this.skeletonClasses;
    const sizeClasses = {
      xs: 'w-16 h-6',
      sm: 'w-20 h-8',
      md: 'w-24 h-10',
      lg: 'w-32 h-12',
      xl: 'w-40 h-14'
    };

    return [baseClasses, sizeClasses[this.size]].join(' ');
  }
}
