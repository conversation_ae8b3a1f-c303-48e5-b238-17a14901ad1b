<div [class]="containerClasses">
  <!-- Text Placeholder -->
  <div *ngIf="type === 'text'" class="space-y-2">
    <div *ngFor="let line of placeholderLines; let i = index"
         [class]="getLineClasses(i)">
    </div>
  </div>

  <!-- Card Placeholder -->
  <div *ngIf="type === 'card'" class="space-y-3">
    <!-- Card Header -->
    <div class="flex items-center space-x-3">
      <div *ngIf="showAvatar || config.showAvatar" [class]="avatarClasses"></div>
      <div class="flex-1 space-y-2">
        <div [class]="skeletonClasses + ' h-4 w-3/4'"></div>
        <div [class]="skeletonClasses + ' h-3 w-1/2'"></div>
      </div>
    </div>

    <!-- Card Image -->
    <div *ngIf="showImage || config.showImage" [class]="imageClasses + ' w-full h-32'"></div>

    <!-- Card Content -->
    <div class="space-y-2">
      <div *ngFor="let line of placeholderLines; let i = index"
           [class]="getLineClasses(i)">
      </div>
    </div>

    <!-- Card Actions -->
    <div *ngIf="showButton || config.showButton" class="flex space-x-2">
      <div [class]="buttonClasses"></div>
      <div [class]="buttonClasses + ' w-16'"></div>
    </div>
  </div>

  <!-- List Placeholder -->
  <div *ngIf="type === 'list'" class="space-y-3">
    <div *ngFor="let item of [1,2,3]" class="flex items-center space-x-3">
      <div *ngIf="showAvatar || config.showAvatar" [class]="avatarClasses"></div>
      <div class="flex-1 space-y-2">
        <div [class]="skeletonClasses + ' h-4 w-3/4'"></div>
        <div [class]="skeletonClasses + ' h-3 w-1/2'"></div>
      </div>
      <div *ngIf="showButton || config.showButton" [class]="buttonClasses + ' w-16'"></div>
    </div>
  </div>

  <!-- Image Placeholder -->
  <div *ngIf="type === 'image'" class="space-y-3">
    <div [class]="imageClasses + ' w-full h-48 mx-auto'"></div>
    <div class="text-center space-y-2">
      <div [class]="skeletonClasses + ' h-4 w-2/3 mx-auto'"></div>
      <div [class]="skeletonClasses + ' h-3 w-1/2 mx-auto'"></div>
    </div>
  </div>

  <!-- Custom Placeholder -->
  <div *ngIf="type === 'custom'" class="space-y-3">
    <!-- Title -->
    <div *ngIf="title" [class]="skeletonClasses + ' h-6 w-1/3'"></div>

    <!-- Subtitle -->
    <div *ngIf="subtitle" [class]="skeletonClasses + ' h-4 w-1/4'"></div>

    <!-- Content based on config -->
    <div class="flex items-start space-x-3">
      <div *ngIf="showAvatar || config.showAvatar" [class]="avatarClasses"></div>

      <div class="flex-1 space-y-2">
        <div *ngIf="showImage || config.showImage" [class]="imageClasses + ' w-full h-24 mb-3'"></div>

        <div *ngFor="let line of placeholderLines; let i = index"
             [class]="getLineClasses(i)">
        </div>

        <div *ngIf="showButton || config.showButton" class="pt-2">
          <div [class]="buttonClasses"></div>
        </div>
      </div>
    </div>
  </div>
</div>
