<div [class]="computedClasses">
  <!-- Tab List -->
  <div [class]="tabListClasses" role="tablist">
    <button
      *ngFor="let tab of tabs; trackBy: trackByFn; let i = index"
      type="button"
      [class]="getTabClasses(tab, i)"
      [attr.aria-selected]="tab.active"
      [attr.aria-controls]="'tabpanel-' + tab.id"
      [attr.id]="'tab-' + tab.id"
      [disabled]="tab.disabled || disabled"
      role="tab"
      (click)="onTabClick(tab)"
      (keydown)="onTabKeyDown($event, tab)"
    >
      <!-- Icon -->
      <i *ngIf="tab.icon" [class]="tab.icon + ' mr-2'" aria-hidden="true"></i>
      
      <!-- Label -->
      <span>{{ tab.label }}</span>
      
      <!-- Badge -->
      <span 
        *ngIf="tab.badge" 
        class="ml-2 px-2 py-0.5 text-xs font-medium bg-gray-200 text-gray-800 rounded-full"
      >
        {{ tab.badge }}
      </span>
    </button>
  </div>

  <!-- Tab Content -->
  <div *ngIf="showContent" [class]="contentClasses">
    <div
      *ngFor="let tab of tabs; trackBy: trackByFn"
      [attr.id]="'tabpanel-' + tab.id"
      [attr.aria-labelledby]="'tab-' + tab.id"
      [hidden]="!tab.active"
      role="tabpanel"
      class="tab-panel"
    >
      <!-- Default content from tab data -->
      <div *ngIf="tab.content && !hasCustomContent()">
        {{ tab.content }}
      </div>
      
      <!-- Custom content via projection -->
      <ng-content></ng-content>
    </div>
  </div>
</div>