import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface TabItem {
  id: string | number;
  label: string;
  content?: string;
  icon?: string;
  disabled?: boolean;
  active?: boolean;
  badge?: string | number;
}

@Component({
  selector: 'lib-tab-slider',
  templateUrl: './tab-slider.component.html',
  styleUrls: ['./tab-slider.component.css'],
  standalone: true,
  imports: [CommonModule]
})
export class TabSliderComponent {
  @Input() tabs: TabItem[] = [
    { id: 1, label: 'Tab 1', active: true, content: 'Content for Tab 1' },
    { id: 2, label: 'Tab 2', content: 'Content for Tab 2' },
    { id: 3, label: 'Tab 3', content: 'Content for Tab 3' }
  ];
  @Input() showContent: boolean = true;
  @Input() animated: boolean = true;
  @Input() disabled: boolean = false;
  @Input() orientation: 'horizontal' | 'vertical' = 'horizontal';
  @Input() justified: boolean = false;
  @Input() sticky: boolean = false;
  @Input() scrollable: boolean = false;
  
  // Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  @Output() tabChange = new EventEmitter<TabItem>();
  @Output() tabClick = new EventEmitter<TabItem>();

  get computedClasses(): string {
    const baseClasses = [
      'tab-slider-widget',
      'w-full'
    ];

    if (this.orientation === 'vertical') {
      baseClasses.push('flex', 'flex-row');
    }

    if (this.sticky) {
      baseClasses.push('sticky', 'top-0', 'z-10');
    }

    if (this.className) {
      baseClasses.push(this.className);
    }

    return baseClasses.join(' ');
  }

  get tabListClasses(): string {
    const baseClasses = [
      'tab-list',
      'flex',
      'border-b',
      'border-gray-200'
    ];

    if (this.orientation === 'vertical') {
      baseClasses.push('flex-col', 'border-b-0', 'border-r', 'min-w-max');
    } else {
      baseClasses.push('flex-row');
    }

    if (this.justified) {
      baseClasses.push('justify-between');
    }

    if (this.scrollable && this.orientation === 'horizontal') {
      baseClasses.push('overflow-x-auto', 'scrollbar-hide');
    }

    // Variant classes for tab list
    const variantClasses = {
      default: ['bg-white'],
      primary: ['bg-blue-50'],
      secondary: ['bg-gray-50'],
      success: ['bg-green-50'],
      warning: ['bg-yellow-50'],
      danger: ['bg-red-50']
    };

    baseClasses.push(...variantClasses[this.variant]);

    return baseClasses.join(' ');
  }

  getTabClasses(tab: TabItem, index: number): string {
    const baseClasses = [
      'tab-item',
      'relative',
      'flex',
      'items-center',
      'justify-center',
      'font-medium',
      'transition-all',
      'duration-200',
      'focus:outline-none',
      'focus:ring-2',
      'focus:ring-offset-2',
      'whitespace-nowrap'
    ];

    // Size classes
    const sizeClasses = {
      xs: ['text-xs', 'py-2', 'px-3'],
      sm: ['text-sm', 'py-2', 'px-4'],
      md: ['text-sm', 'py-3', 'px-6'],
      lg: ['text-base', 'py-3', 'px-6'],
      xl: ['text-lg', 'py-4', 'px-8']
    };

    // Variant classes for active/inactive states
    const variantClasses = {
      default: {
        active: ['text-blue-600', 'border-b-2', 'border-blue-600', 'bg-white'],
        inactive: ['text-gray-600', 'hover:text-gray-900', 'border-b-2', 'border-transparent', 'hover:border-gray-300']
      },
      primary: {
        active: ['text-blue-700', 'border-b-2', 'border-blue-700', 'bg-blue-50'],
        inactive: ['text-blue-600', 'hover:text-blue-700', 'border-b-2', 'border-transparent', 'hover:border-blue-300']
      },
      secondary: {
        active: ['text-gray-700', 'border-b-2', 'border-gray-700', 'bg-gray-50'],
        inactive: ['text-gray-600', 'hover:text-gray-700', 'border-b-2', 'border-transparent', 'hover:border-gray-400']
      },
      success: {
        active: ['text-green-700', 'border-b-2', 'border-green-700', 'bg-green-50'],
        inactive: ['text-green-600', 'hover:text-green-700', 'border-b-2', 'border-transparent', 'hover:border-green-300']
      },
      warning: {
        active: ['text-yellow-700', 'border-b-2', 'border-yellow-700', 'bg-yellow-50'],
        inactive: ['text-yellow-600', 'hover:text-yellow-700', 'border-b-2', 'border-transparent', 'hover:border-yellow-300']
      },
      danger: {
        active: ['text-red-700', 'border-b-2', 'border-red-700', 'bg-red-50'],
        inactive: ['text-red-600', 'hover:text-red-700', 'border-b-2', 'border-transparent', 'hover:border-red-300']
      }
    };

    // Rounded classes for vertical orientation
    if (this.orientation === 'vertical' && this.rounded !== 'none') {
      const roundedClasses = {
        sm: ['rounded-sm'],
        md: ['rounded-md'],
        lg: ['rounded-lg'],
        full: ['rounded-full']
      };
      baseClasses.push(...roundedClasses[this.rounded]);
    }

    if (this.justified) {
      baseClasses.push('flex-1');
    }

    const classes = [
      ...baseClasses,
      ...sizeClasses[this.size],
      ...variantClasses[this.variant][tab.active ? 'active' : 'inactive']
    ];

    // Disabled state
    if (tab.disabled || this.disabled) {
      classes.push('opacity-50', 'cursor-not-allowed', 'pointer-events-none');
    } else {
      classes.push('cursor-pointer');
    }

    // Focus ring color
    const focusRingClasses = {
      default: 'focus:ring-blue-500',
      primary: 'focus:ring-blue-500',
      secondary: 'focus:ring-gray-500',
      success: 'focus:ring-green-500',
      warning: 'focus:ring-yellow-500',
      danger: 'focus:ring-red-500'
    };
    classes.push(focusRingClasses[this.variant]);

    return classes.join(' ');
  }

  get contentClasses(): string {
    const baseClasses = [
      'tab-content',
      'flex-1',
      'p-4'
    ];

    if (this.animated) {
      baseClasses.push('transition-all', 'duration-300', 'ease-in-out');
    }

    return baseClasses.join(' ');
  }

  onTabClick(tab: TabItem): void {
    if (tab.disabled || this.disabled) {
      return;
    }

    // Update active states
    this.tabs.forEach(t => t.active = false);
    tab.active = true;

    this.tabClick.emit(tab);
    this.tabChange.emit(tab);
  }

  onTabKeyDown(event: KeyboardEvent, tab: TabItem): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.onTabClick(tab);
    }
  }

  getActiveTab(): TabItem | undefined {
    return this.tabs.find(tab => tab.active);
  }

  trackByFn(index: number, tab: TabItem): any {
    return tab.id || index;
  }

  hasCustomContent(): boolean {
    // This is a simple check - in a real implementation, you might want to use ViewChild or ContentChild
    // to detect if there's projected content
    return false;
  }
}