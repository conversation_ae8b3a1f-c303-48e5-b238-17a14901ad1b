import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface WelcomeStep {
  id: string;
  title: string;
  description: string;
  icon: string;
  completed: boolean;
}

@Component({
  selector: 'lib-welcome',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './welcome.component.html',
  styleUrl: './welcome.component.css'
})
export class WelcomeComponent {
  // Standard inputs for customization
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'primary';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'lg';

  // Component-specific inputs
  @Input() title: string = 'Welcome to Our Platform';
  @Input() subtitle: string = 'Get started with these simple steps';
  @Input() description: string = 'Follow the steps below to set up your account and start using all the features.';
  @Input() showProgress: boolean = true;
  @Input() showSteps: boolean = true;
  @Input() showActions: boolean = true;
  @Input() primaryButtonLabel: string = 'Get Started';
  @Input() secondaryButtonLabel: string = 'Skip Tour';
  @Input() showSecondaryButton: boolean = true;
  @Input() backgroundImage: string = '';
  @Input() logoUrl: string = '';
  @Input() companyName: string = 'Your Company';

  @Input() steps: WelcomeStep[] = [
    {
      id: '1',
      title: 'Complete Your Profile',
      description: 'Add your personal information and preferences',
      icon: 'fas fa-user-circle',
      completed: false
    },
    {
      id: '2',
      title: 'Connect Your Accounts',
      description: 'Link your social media and business accounts',
      icon: 'fas fa-link',
      completed: false
    },
    {
      id: '3',
      title: 'Explore Features',
      description: 'Discover all the powerful tools available',
      icon: 'fas fa-rocket',
      completed: false
    }
  ];

  // Event outputs
  @Output() primaryAction = new EventEmitter<void>();
  @Output() secondaryAction = new EventEmitter<void>();
  @Output() stepClick = new EventEmitter<WelcomeStep>();
  @Output() dismiss = new EventEmitter<void>();

  // Computed getter for CSS classes
  get computedClasses(): string {
    const baseClasses = 'welcome-widget';
    
    const sizeClasses = {
      xs: 'p-4 text-sm',
      sm: 'p-6 text-base',
      md: 'p-8 text-base',
      lg: 'p-10 text-lg',
      xl: 'p-12 text-xl'
    };

    const variantClasses = {
      default: 'bg-white border-gray-200',
      primary: 'bg-gradient-to-br from-blue-50 to-indigo-100 border-blue-200',
      secondary: 'bg-gradient-to-br from-gray-50 to-gray-100 border-gray-300',
      success: 'bg-gradient-to-br from-green-50 to-emerald-100 border-green-200',
      warning: 'bg-gradient-to-br from-yellow-50 to-orange-100 border-yellow-200',
      danger: 'bg-gradient-to-br from-red-50 to-pink-100 border-red-200'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-xl'
    };
    
    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      'border shadow-lg relative overflow-hidden',
      this.className
    ].filter(Boolean).join(' ');
  }

  get primaryButtonClasses(): string {
    const variantClasses = {
      default: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',
      primary: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',
      secondary: 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500',
      success: 'bg-green-600 hover:bg-green-700 focus:ring-green-500',
      warning: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500',
      danger: 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
    };

    return `inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 transform hover:scale-105 ${variantClasses[this.variant]}`;
  }

  get progressPercentage(): number {
    const completedSteps = this.steps.filter(step => step.completed).length;
    return Math.round((completedSteps / this.steps.length) * 100);
  }

  onPrimaryAction(): void {
    this.primaryAction.emit();
  }

  onSecondaryAction(): void {
    this.secondaryAction.emit();
  }

  onStepClick(step: WelcomeStep): void {
    this.stepClick.emit(step);
  }

  onDismiss(): void {
    this.dismiss.emit();
  }
}
