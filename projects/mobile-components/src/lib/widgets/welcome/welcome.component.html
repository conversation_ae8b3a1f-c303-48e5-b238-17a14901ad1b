<!-- Welcome Widget -->
<div [class]="computedClasses">
  <!-- Background pattern overlay -->
  <div *ngIf="backgroundImage" 
       class="absolute inset-0 opacity-10 bg-cover bg-center"
       [style.background-image]="'url(' + backgroundImage + ')'"></div>
  
  <!-- Dismiss button -->
  <button 
    type="button"
    (click)="onDismiss()"
    class="absolute top-4 right-4 p-2 text-gray-400 hover:text-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-full"
    aria-label="Dismiss welcome message">
    <i class="fas fa-times"></i>
  </button>

  <div class="relative z-10">
    <!-- Header Section -->
    <div class="text-center mb-8">
      <!-- Logo -->
      <div *ngIf="logoUrl" class="mb-4">
        <img [src]="logoUrl" [alt]="companyName + ' logo'" class="h-12 mx-auto">
      </div>
      
      <!-- Welcome Icon -->
      <div *ngIf="!logoUrl" class="mb-4">
        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
          <i class="fas fa-rocket text-2xl text-blue-600"></i>
        </div>
      </div>

      <!-- Title and Subtitle -->
      <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ title }}</h1>
      <h2 class="text-xl text-gray-600 mb-4">{{ subtitle }}</h2>
      <p class="text-gray-500 max-w-md mx-auto">{{ description }}</p>
    </div>

    <!-- Progress Bar -->
    <div *ngIf="showProgress" class="mb-8">
      <div class="flex justify-between items-center mb-2">
        <span class="text-sm font-medium text-gray-700">Setup Progress</span>
        <span class="text-sm text-gray-500">{{ progressPercentage }}% Complete</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div 
          class="bg-blue-600 h-2 rounded-full transition-all duration-500"
          [style.width.%]="progressPercentage"></div>
      </div>
    </div>

    <!-- Steps Section -->
    <div *ngIf="showSteps && steps.length > 0" class="mb-8">
      <div class="space-y-4">
        <div 
          *ngFor="let step of steps; let i = index"
          (click)="onStepClick(step)"
          class="flex items-start p-4 rounded-lg border transition-all cursor-pointer hover:shadow-md"
          [class.bg-green-50]="step.completed"
          [class.border-green-200]="step.completed"
          [class.bg-white]="!step.completed"
          [class.border-gray-200]="!step.completed"
          [class.hover:border-blue-300]="!step.completed">
          
          <!-- Step Icon -->
          <div class="flex-shrink-0 mr-4">
            <div [class]="'w-10 h-10 rounded-full flex items-center justify-center ' + 
                          (step.completed ? 'bg-green-100 text-green-600' : 'bg-blue-100 text-blue-600')">
              <i *ngIf="step.completed" class="fas fa-check"></i>
              <i *ngIf="!step.completed" [class]="step.icon"></i>
            </div>
          </div>
          
          <!-- Step Content -->
          <div class="flex-1">
            <h4 class="font-semibold text-gray-900 mb-1">{{ step.title }}</h4>
            <p class="text-sm text-gray-600">{{ step.description }}</p>
          </div>
          
          <!-- Step Status -->
          <div class="flex-shrink-0 ml-4">
            <i *ngIf="step.completed" class="fas fa-check-circle text-green-500"></i>
            <i *ngIf="!step.completed" class="fas fa-circle text-gray-300"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div *ngIf="showActions" class="flex flex-col sm:flex-row gap-3 justify-center">
      <button 
        type="button"
        (click)="onPrimaryAction()"
        [class]="primaryButtonClasses">
        <i class="fas fa-rocket mr-2"></i>
        {{ primaryButtonLabel }}
      </button>
      
      <button 
        *ngIf="showSecondaryButton"
        type="button"
        (click)="onSecondaryAction()"
        class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
        {{ secondaryButtonLabel }}
      </button>
    </div>

    <!-- Additional Info -->
    <div class="mt-8 text-center">
      <p class="text-xs text-gray-500">
        Welcome to {{ companyName }}. We're excited to have you here!
      </p>
    </div>
  </div>
</div>
