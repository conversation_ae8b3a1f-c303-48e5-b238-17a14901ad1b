/* Menu Icon List Component Styles */
.menu-icon-list-widget {
  @apply w-full;
}

.menu-item {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1;
  transition: all 0.2s ease-in-out;
}

.menu-item:hover:not([aria-disabled="true"]) {
  @apply shadow-sm;
  transform: translateY(-1px);
}

.menu-item[aria-disabled="true"] {
  @apply pointer-events-none;
}

.menu-item[aria-selected="true"] {
  @apply ring-2 ring-current ring-opacity-20;
}

.menu-icon {
  transition: transform 0.2s ease-in-out;
}

.menu-item:hover .menu-icon:not([aria-disabled="true"]) {
  transform: scale(1.1);
}

.menu-badge {
  @apply animate-pulse;
  animation-duration: 2s;
}

.menu-content {
  @apply transition-all duration-200;
}

.empty-state {
  @apply opacity-75;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .menu-icon-list-widget.flex-row {
    @apply flex-wrap;
  }
  
  .menu-item {
    @apply min-w-0 flex-shrink;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .menu-item {
    @apply border border-gray-300;
  }
  
  .menu-item[aria-selected="true"] {
    @apply border-2 border-current;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .menu-item,
  .menu-icon,
  .menu-content {
    transition: none;
  }
  
  .menu-item:hover {
    transform: none;
  }
  
  .menu-item:hover .menu-icon {
    transform: none;
  }
  
  .menu-badge {
    animation: none;
  }
}