<nav [class]="computedClasses" role="navigation" [attr.aria-label]="'Menu with ' + items.length + ' items'">
  <ng-container *ngFor="let item of items; trackBy: trackByFn">
    <!-- Menu Item -->
    <div
      [class]="getItemClasses(item)"
      [attr.role]="clickable ? 'button' : 'listitem'"
      [attr.tabindex]="item.disabled ? -1 : 0"
      [attr.aria-disabled]="item.disabled"
      [attr.aria-selected]="selectable ? item.selected : null"
      [attr.aria-label]="item.label + (item.description ? ': ' + item.description : '')"
      (click)="onItemClick(item, $event)"
      (keydown.enter)="onItemClick(item, $event)"
      (keydown.space)="onItemClick(item, $event)"
    >
      <!-- Icon -->
      <i 
        [class]="item.icon + ' ' + getIconClasses(item)"
        [attr.aria-hidden]="true"
      ></i>

      <!-- Text Content Container -->
      <div 
        class="menu-content flex-1"
        [class.text-center]="iconPosition === 'top'"
        [class.text-left]="iconPosition !== 'top' && iconPosition !== 'right'"
        [class.text-right]="iconPosition === 'right'"
      >
        <!-- Main Label -->
        <div [class]="getTextClasses()" class="font-medium">
          {{ item.label }}
        </div>

        <!-- Description -->
        <div 
          *ngIf="showDescriptions && item.description"
          [class]="getDescriptionClasses()"
          class="mt-0.5"
        >
          {{ item.description }}
        </div>
      </div>

      <!-- Badge -->
      <span
        *ngIf="showBadges && item.badge"
        [class]="getBadgeClasses()"
        [attr.aria-label]="'Badge: ' + item.badge"
      >
        {{ item.badge }}
      </span>

      <!-- Selection Indicator -->
      <div
        *ngIf="selectable && item.selected"
        class="selection-indicator ml-2 flex-shrink-0"
        [attr.aria-hidden]="true"
      >
        <i class="fa fa-check text-current" [class]="'text-' + size"></i>
      </div>
    </div>
  </ng-container>

  <!-- Empty State -->
  <div 
    *ngIf="items.length === 0"
    class="empty-state text-center py-8 text-gray-500"
    role="status"
    aria-live="polite"
  >
    <i class="fa fa-list text-2xl mb-2 block" aria-hidden="true"></i>
    <p class="text-sm">No menu items available</p>
  </div>
</nav>
