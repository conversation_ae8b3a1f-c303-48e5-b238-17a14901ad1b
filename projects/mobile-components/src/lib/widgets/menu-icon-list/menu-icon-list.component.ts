import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface MenuIconItem {
  id: string | number;
  label: string;
  icon: string;
  value?: any;
  href?: string;
  disabled?: boolean;
  selected?: boolean;
  badge?: string | number;
  description?: string;
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
}

@Component({
  selector: 'lib-menu-icon-list',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './menu-icon-list.component.html',
  styleUrl: './menu-icon-list.component.css'
})
export class MenuIconListComponent {
  @Input() items: MenuIconItem[] = [
    { 
      id: 1, 
      label: 'Dashboard', 
      icon: 'fa fa-dashboard',
      description: 'View your dashboard',
      selected: true 
    },
    { 
      id: 2, 
      label: 'Projects', 
      icon: 'fa fa-folder',
      description: 'Manage your projects',
      badge: '5' 
    },
    { 
      id: 3, 
      label: 'Settings', 
      icon: 'fa fa-cog',
      description: 'Configure your account' 
    },
    { 
      id: 4, 
      label: 'Help', 
      icon: 'fa fa-question-circle',
      description: 'Get support',
      disabled: false 
    }
  ];
  
  @Input() orientation: 'vertical' | 'horizontal' = 'vertical';
  @Input() showDescriptions: boolean = true;
  @Input() showBadges: boolean = true;
  @Input() clickable: boolean = true;
  @Input() selectable: boolean = false;
  @Input() allowDeselect: boolean = false;
  @Input() spacing: 'tight' | 'normal' | 'loose' = 'normal';
  @Input() alignment: 'start' | 'center' | 'end' = 'start';
  @Input() iconPosition: 'left' | 'right' | 'top' = 'left';
  
  // Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  @Output() itemClick = new EventEmitter<MenuIconItem>();
  @Output() itemSelect = new EventEmitter<MenuIconItem>();
  @Output() selectionChange = new EventEmitter<MenuIconItem[]>();

  get computedClasses(): string {
    const baseClasses = ['menu-icon-list-widget'];

    // Layout orientation
    if (this.orientation === 'horizontal') {
      baseClasses.push('flex', 'flex-row');
      
      // Spacing for horizontal layout
      const horizontalSpacing = {
        tight: 'space-x-1',
        normal: 'space-x-2',
        loose: 'space-x-4'
      };
      baseClasses.push(horizontalSpacing[this.spacing]);
    } else {
      baseClasses.push('flex', 'flex-col');
      
      // Spacing for vertical layout
      const verticalSpacing = {
        tight: 'space-y-1',
        normal: 'space-y-2',
        loose: 'space-y-4'
      };
      baseClasses.push(verticalSpacing[this.spacing]);
    }

    // Alignment
    if (this.orientation === 'horizontal') {
      const horizontalAlignment = {
        start: 'justify-start',
        center: 'justify-center',
        end: 'justify-end'
      };
      baseClasses.push(horizontalAlignment[this.alignment]);
    }

    // Rounded corners
    const roundedClasses = {
      none: '',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };
    if (roundedClasses[this.rounded]) {
      baseClasses.push(roundedClasses[this.rounded]);
    }

    if (this.className) {
      baseClasses.push(this.className);
    }

    return baseClasses.join(' ');
  }

  getItemClasses(item: MenuIconItem): string {
    const baseClasses = [
      'menu-item',
      'flex',
      'transition-all',
      'duration-200'
    ];

    // Icon position layout
    if (this.iconPosition === 'top') {
      baseClasses.push('flex-col', 'text-center');
    } else if (this.iconPosition === 'right') {
      baseClasses.push('flex-row-reverse');
    } else {
      baseClasses.push('flex-row');
    }

    // Alignment for items
    if (this.iconPosition !== 'top') {
      baseClasses.push('items-center');
    } else {
      baseClasses.push('items-center');
    }

    // Size-based padding
    const sizePadding = {
      xs: 'p-1',
      sm: 'p-2',
      md: 'p-3',
      lg: 'p-4',
      xl: 'p-5'
    };
    baseClasses.push(sizePadding[this.size]);

    // Clickable states
    if (this.clickable && !item.disabled) {
      baseClasses.push('cursor-pointer', 'hover:bg-gray-50');
    }

    // Selection state
    if (item.selected && this.selectable) {
      const selectedVariantClasses = {
        default: 'bg-gray-100 text-gray-900',
        primary: 'bg-blue-50 text-blue-900',
        secondary: 'bg-gray-50 text-gray-900',
        success: 'bg-green-50 text-green-900',
        warning: 'bg-yellow-50 text-yellow-900',
        danger: 'bg-red-50 text-red-900'
      };
      baseClasses.push(selectedVariantClasses[item.variant || this.variant]);
    } else {
      // Default text color
      const variantClasses = {
        default: 'text-gray-700',
        primary: 'text-blue-700',
        secondary: 'text-gray-600',
        success: 'text-green-700',
        warning: 'text-yellow-700',
        danger: 'text-red-700'
      };
      baseClasses.push(variantClasses[item.variant || this.variant]);
    }

    // Disabled state
    if (item.disabled) {
      baseClasses.push('opacity-50', 'cursor-not-allowed');
    }

    // Rounded corners
    const roundedClasses = {
      none: '',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };
    if (roundedClasses[this.rounded]) {
      baseClasses.push(roundedClasses[this.rounded]);
    }

    return baseClasses.join(' ');
  }

  getIconClasses(item: MenuIconItem): string {
    const baseClasses = ['menu-icon', 'flex-shrink-0'];

    // Size classes
    const sizeClasses = {
      xs: 'text-sm',
      sm: 'text-base',
      md: 'text-lg',
      lg: 'text-xl',
      xl: 'text-2xl'
    };
    baseClasses.push(sizeClasses[this.size]);

    // Spacing based on icon position
    if (this.iconPosition === 'left') {
      const leftSpacing = {
        xs: 'mr-1',
        sm: 'mr-2',
        md: 'mr-3',
        lg: 'mr-4',
        xl: 'mr-5'
      };
      baseClasses.push(leftSpacing[this.size]);
    } else if (this.iconPosition === 'right') {
      const rightSpacing = {
        xs: 'ml-1',
        sm: 'ml-2',
        md: 'ml-3',
        lg: 'ml-4',
        xl: 'ml-5'
      };
      baseClasses.push(rightSpacing[this.size]);
    } else if (this.iconPosition === 'top') {
      const topSpacing = {
        xs: 'mb-1',
        sm: 'mb-1',
        md: 'mb-2',
        lg: 'mb-2',
        xl: 'mb-3'
      };
      baseClasses.push(topSpacing[this.size]);
    }

    return baseClasses.join(' ');
  }

  getTextClasses(): string {
    const baseClasses = ['menu-text', 'flex-1'];

    // Size classes for text
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };
    baseClasses.push(sizeClasses[this.size]);

    if (this.iconPosition === 'top') {
      baseClasses.push('text-center');
    }

    return baseClasses.join(' ');
  }

  getDescriptionClasses(): string {
    const baseClasses = ['menu-description', 'text-gray-500'];

    // Smaller size for descriptions
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-xs',
      md: 'text-sm',
      lg: 'text-base',
      xl: 'text-lg'
    };
    baseClasses.push(sizeClasses[this.size]);

    if (this.iconPosition === 'top') {
      baseClasses.push('text-center');
    }

    return baseClasses.join(' ');
  }

  getBadgeClasses(): string {
    const baseClasses = [
      'menu-badge',
      'inline-flex',
      'items-center',
      'justify-center',
      'text-white',
      'bg-red-500',
      'rounded-full',
      'font-medium'
    ];

    // Size-based badge dimensions
    const badgeSizes = {
      xs: 'min-w-[16px] h-4 px-1 text-xs',
      sm: 'min-w-[18px] h-5 px-1.5 text-xs',
      md: 'min-w-[20px] h-5 px-2 text-xs',
      lg: 'min-w-[22px] h-6 px-2 text-sm',
      xl: 'min-w-[24px] h-6 px-2.5 text-sm'
    };
    baseClasses.push(badgeSizes[this.size]);

    // Positioning
    if (this.iconPosition === 'top') {
      baseClasses.push('mt-1');
    } else {
      baseClasses.push('ml-auto');
    }

    return baseClasses.join(' ');
  }

  onItemClick(item: MenuIconItem, event: Event): void {
    if (item.disabled) {
      event.preventDefault();
      return;
    }

    this.itemClick.emit(item);

    if (this.selectable) {
      this.handleSelection(item);
    }

    // Handle navigation for href items
    if (item.href && !event.defaultPrevented) {
      // Allow default navigation behavior
    }
  }

  private handleSelection(item: MenuIconItem): void {
    if (item.disabled) return;

    const wasSelected = item.selected;

    if (!this.allowDeselect && wasSelected) {
      return;
    }

    // Update selection
    this.items.forEach(menuItem => {
      if (menuItem.id === item.id) {
        menuItem.selected = !wasSelected;
      } else {
        menuItem.selected = false; // Single selection
      }
    });

    this.itemSelect.emit(item);
    this.selectionChange.emit(this.items.filter(menuItem => menuItem.selected));
  }

  getSelectedItems(): MenuIconItem[] {
    return this.items.filter(item => item.selected);
  }

  trackByFn(index: number, item: MenuIconItem): any {
    return item.id;
  }
}
