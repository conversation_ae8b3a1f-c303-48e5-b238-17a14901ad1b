import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DebugElement } from '@angular/core';
import { By } from '@angular/platform-browser';

import { MenuIconListComponent, MenuIconItem } from './menu-icon-list.component';

describe('MenuIconListComponent', () => {
  let component: MenuIconListComponent;
  let fixture: ComponentFixture<MenuIconListComponent>;

  const mockItems: MenuIconItem[] = [
    { 
      id: 1, 
      label: 'Dashboard', 
      icon: 'fa fa-dashboard',
      description: 'View your dashboard',
      selected: true 
    },
    { 
      id: 2, 
      label: 'Projects', 
      icon: 'fa fa-folder',
      description: 'Manage your projects',
      badge: '5' 
    },
    { 
      id: 3, 
      label: 'Settings', 
      icon: 'fa fa-cog',
      description: 'Configure your account',
      disabled: true 
    }
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MenuIconListComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(MenuIconListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should have default values', () => {
      expect(component.orientation).toBe('vertical');
      expect(component.showDescriptions).toBe(true);
      expect(component.showBadges).toBe(true);
      expect(component.clickable).toBe(true);
      expect(component.selectable).toBe(false);
      expect(component.size).toBe('md');
      expect(component.variant).toBe('default');
      expect(component.rounded).toBe('md');
      expect(component.items.length).toBe(4);
    });

    it('should render default items when no items provided', () => {
      const menuItems = fixture.debugElement.queryAll(By.css('.menu-item'));
      expect(menuItems.length).toBe(4);
    });
  });

  describe('Input Properties', () => {
    beforeEach(() => {
      component.items = mockItems;
      fixture.detectChanges();
    });

    it('should render custom items when provided', () => {
      const menuItems = fixture.debugElement.queryAll(By.css('.menu-item'));
      expect(menuItems.length).toBe(3);
      
      const firstItem = menuItems[0];
      const label = firstItem.query(By.css('.menu-text'));
      expect(label.nativeElement.textContent.trim()).toBe('Dashboard');
    });

    it('should display icons correctly', () => {
      const icons = fixture.debugElement.queryAll(By.css('.menu-icon'));
      expect(icons.length).toBe(3);
      expect(icons[0].nativeElement).toHaveClass('fa', 'fa-dashboard');
      expect(icons[1].nativeElement).toHaveClass('fa', 'fa-folder');
    });

    it('should display descriptions when showDescriptions is true', () => {
      component.showDescriptions = true;
      fixture.detectChanges();
      
      const descriptions = fixture.debugElement.queryAll(By.css('.menu-description'));
      expect(descriptions.length).toBe(3);
      expect(descriptions[0].nativeElement.textContent.trim()).toBe('View your dashboard');
    });

    it('should hide descriptions when showDescriptions is false', () => {
      component.showDescriptions = false;
      fixture.detectChanges();
      
      const descriptions = fixture.debugElement.queryAll(By.css('.menu-description'));
      expect(descriptions.length).toBe(0);
    });

    it('should display badges when showBadges is true', () => {
      component.showBadges = true;
      fixture.detectChanges();
      
      const badges = fixture.debugElement.queryAll(By.css('.menu-badge'));
      expect(badges.length).toBe(1);
      expect(badges[0].nativeElement.textContent.trim()).toBe('5');
    });

    it('should hide badges when showBadges is false', () => {
      component.showBadges = false;
      fixture.detectChanges();
      
      const badges = fixture.debugElement.queryAll(By.css('.menu-badge'));
      expect(badges.length).toBe(0);
    });
  });

  describe('Layout and Styling', () => {
    beforeEach(() => {
      component.items = mockItems;
      fixture.detectChanges();
    });

    it('should apply vertical orientation by default', () => {
      const container = fixture.debugElement.query(By.css('.menu-icon-list-widget'));
      expect(container.nativeElement).toHaveClass('flex-col');
    });

    it('should apply horizontal orientation when set', () => {
      component.orientation = 'horizontal';
      fixture.detectChanges();
      
      const container = fixture.debugElement.query(By.css('.menu-icon-list-widget'));
      expect(container.nativeElement).toHaveClass('flex-row');
    });

    it('should apply size classes correctly', () => {
      component.size = 'lg';
      fixture.detectChanges();
      
      const icons = fixture.debugElement.queryAll(By.css('.menu-icon'));
      expect(icons[0].nativeElement).toHaveClass('text-xl');
    });

    it('should apply rounded classes correctly', () => {
      component.rounded = 'lg';
      fixture.detectChanges();
      
      const container = fixture.debugElement.query(By.css('.menu-icon-list-widget'));
      expect(container.nativeElement).toHaveClass('rounded-lg');
    });

    it('should apply custom className', () => {
      component.className = 'custom-menu-class';
      fixture.detectChanges();
      
      const container = fixture.debugElement.query(By.css('.menu-icon-list-widget'));
      expect(container.nativeElement).toHaveClass('custom-menu-class');
    });
  });

  describe('Icon Position', () => {
    beforeEach(() => {
      component.items = mockItems;
      fixture.detectChanges();
    });

    it('should position icons on the left by default', () => {
      const menuItems = fixture.debugElement.queryAll(By.css('.menu-item'));
      expect(menuItems[0].nativeElement).toHaveClass('flex-row');
    });

    it('should position icons on the right when set', () => {
      component.iconPosition = 'right';
      fixture.detectChanges();
      
      const menuItems = fixture.debugElement.queryAll(By.css('.menu-item'));
      expect(menuItems[0].nativeElement).toHaveClass('flex-row-reverse');
    });

    it('should position icons on top when set', () => {
      component.iconPosition = 'top';
      fixture.detectChanges();
      
      const menuItems = fixture.debugElement.queryAll(By.css('.menu-item'));
      expect(menuItems[0].nativeElement).toHaveClass('flex-col');
      
      const textContainer = menuItems[0].query(By.css('.menu-content'));
      expect(textContainer.nativeElement).toHaveClass('text-center');
    });
  });

  describe('User Interactions', () => {
    beforeEach(() => {
      component.items = mockItems;
      component.clickable = true;
      fixture.detectChanges();
    });

    it('should emit itemClick when clicking a menu item', () => {
      spyOn(component.itemClick, 'emit');
      
      const menuItem = fixture.debugElement.query(By.css('.menu-item'));
      menuItem.nativeElement.click();
      
      expect(component.itemClick.emit).toHaveBeenCalledWith(mockItems[0]);
    });

    it('should not emit itemClick when clicking disabled item', () => {
      spyOn(component.itemClick, 'emit');
      
      const disabledItem = fixture.debugElement.queryAll(By.css('.menu-item'))[2];
      disabledItem.nativeElement.click();
      
      expect(component.itemClick.emit).not.toHaveBeenCalled();
    });

    it('should handle keyboard navigation (Enter key)', () => {
      spyOn(component.itemClick, 'emit');
      
      const menuItem = fixture.debugElement.query(By.css('.menu-item'));
      menuItem.nativeElement.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter' }));
      
      expect(component.itemClick.emit).toHaveBeenCalledWith(mockItems[0]);
    });

    it('should handle keyboard navigation (Space key)', () => {
      spyOn(component.itemClick, 'emit');
      
      const menuItem = fixture.debugElement.query(By.css('.menu-item'));
      menuItem.nativeElement.dispatchEvent(new KeyboardEvent('keydown', { key: ' ' }));
      
      expect(component.itemClick.emit).toHaveBeenCalledWith(mockItems[0]);
    });
  });

  describe('Selection Functionality', () => {
    beforeEach(() => {
      component.items = mockItems;
      component.selectable = true;
      fixture.detectChanges();
    });

    it('should show selection indicators for selected items', () => {
      const selectionIndicators = fixture.debugElement.queryAll(By.css('.selection-indicator'));
      expect(selectionIndicators.length).toBe(1);
    });

    it('should emit selectionChange when selecting an item', () => {
      spyOn(component.selectionChange, 'emit');
      
      const unselectedItem = fixture.debugElement.queryAll(By.css('.menu-item'))[1];
      unselectedItem.nativeElement.click();
      
      expect(component.selectionChange.emit).toHaveBeenCalled();
    });

    it('should update item selection state correctly', () => {
      const unselectedItem = fixture.debugElement.queryAll(By.css('.menu-item'))[1];
      unselectedItem.nativeElement.click();
      
      expect(component.items[1].selected).toBe(true);
      expect(component.items[0].selected).toBe(false); // Single selection
    });

    it('should get selected items correctly', () => {
      const selectedItems = component.getSelectedItems();
      expect(selectedItems.length).toBe(1);
      expect(selectedItems[0].id).toBe(1);
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      component.items = mockItems;
      fixture.detectChanges();
    });

    it('should have proper ARIA attributes', () => {
      const container = fixture.debugElement.query(By.css('nav'));
      expect(container.nativeElement.getAttribute('role')).toBe('navigation');
      expect(container.nativeElement.getAttribute('aria-label')).toContain('Menu with 3 items');
    });

    it('should have proper ARIA attributes on menu items', () => {
      const menuItems = fixture.debugElement.queryAll(By.css('.menu-item'));
      
      expect(menuItems[0].nativeElement.getAttribute('role')).toBe('button');
      expect(menuItems[0].nativeElement.getAttribute('tabindex')).toBe('0');
      expect(menuItems[0].nativeElement.getAttribute('aria-label')).toContain('Dashboard');
      
      // Disabled item
      expect(menuItems[2].nativeElement.getAttribute('tabindex')).toBe('-1');
      expect(menuItems[2].nativeElement.getAttribute('aria-disabled')).toBe('true');
    });

    it('should have proper ARIA attributes for selectable items', () => {
      component.selectable = true;
      fixture.detectChanges();
      
      const menuItems = fixture.debugElement.queryAll(By.css('.menu-item'));
      expect(menuItems[0].nativeElement.getAttribute('aria-selected')).toBe('true');
      expect(menuItems[1].nativeElement.getAttribute('aria-selected')).toBe('false');
    });

    it('should hide decorative elements from screen readers', () => {
      const icons = fixture.debugElement.queryAll(By.css('.menu-icon'));
      icons.forEach(icon => {
        expect(icon.nativeElement.getAttribute('aria-hidden')).toBe('true');
      });
    });
  });

  describe('Empty State', () => {
    beforeEach(() => {
      component.items = [];
      fixture.detectChanges();
    });

    it('should display empty state when no items provided', () => {
      const emptyState = fixture.debugElement.query(By.css('.empty-state'));
      expect(emptyState).toBeTruthy();
      expect(emptyState.nativeElement.textContent).toContain('No menu items available');
    });

    it('should have proper accessibility for empty state', () => {
      const emptyState = fixture.debugElement.query(By.css('.empty-state'));
      expect(emptyState.nativeElement.getAttribute('role')).toBe('status');
      expect(emptyState.nativeElement.getAttribute('aria-live')).toBe('polite');
    });
  });

  describe('Computed Classes', () => {
    it('should compute correct classes for different configurations', () => {
      component.orientation = 'horizontal';
      component.spacing = 'loose';
      component.alignment = 'center';
      component.className = 'custom-class';
      
      const classes = component.computedClasses;
      expect(classes).toContain('flex-row');
      expect(classes).toContain('space-x-4');
      expect(classes).toContain('justify-center');
      expect(classes).toContain('custom-class');
    });

    it('should compute correct item classes for different states', () => {
      const selectedItem = { ...mockItems[0], selected: true };
      component.selectable = true;
      component.variant = 'primary';
      
      const classes = component.getItemClasses(selectedItem);
      expect(classes).toContain('bg-blue-50');
      expect(classes).toContain('text-blue-900');
    });

    it('should handle disabled items correctly', () => {
      const disabledItem = { ...mockItems[0], disabled: true };
      
      const classes = component.getItemClasses(disabledItem);
      expect(classes).toContain('opacity-50');
      expect(classes).toContain('cursor-not-allowed');
    });
  });

  describe('Track By Function', () => {
    it('should track items by id', () => {
      const result = component.trackByFn(0, mockItems[0]);
      expect(result).toBe(mockItems[0].id);
    });
  });
});
