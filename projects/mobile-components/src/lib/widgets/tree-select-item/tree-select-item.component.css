/* Tree Select Item Component Styles */
.tree-select-item-widget {
  position: relative;
}

/* Hover effects */
.tree-select-item-widget:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Focus styles for accessibility */
.tree-select-item-widget:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: -2px;
}

/* Drag and drop styles */
.tree-select-item-widget[draggable="true"] {
  user-select: none;
}

.tree-select-item-widget.drag-over {
  background-color: #dbeafe;
  border: 2px dashed #3b82f6;
}

/* Selection animation */
.tree-select-item-widget .absolute {
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    width: 0;
  }
  to {
    width: 0.25rem;
  }
}

/* Expand icon rotation */
.tree-select-item-widget .fa-chevron-right {
  transition: transform 0.2s ease;
}

/* Loading spinner animation override for better performance */
.tree-select-item-widget .animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Highlight mark styling */
.tree-select-item-widget mark {
  font-weight: 600;
  color: #92400e;
}

/* Checkbox focus ring enhancement */
.tree-select-item-widget input[type="checkbox"]:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}