import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface TreeSelectItemData {
  id: string | number;
  label: string;
  value?: any;
  icon?: string;
  description?: string;
  disabled?: boolean;
  selected?: boolean;
  expanded?: boolean;
  level?: number;
  hasChildren?: boolean;
  children?: TreeSelectItemData[];
  parentId?: string | number;
  metadata?: any;
}

@Component({
  selector: 'lib-tree-select-item',
  templateUrl: './tree-select-item.component.html',
  styleUrls: ['./tree-select-item.component.css'],
  standalone: true,
  imports: [CommonModule]
})
export class TreeSelectItemComponent {
  @Input() item: TreeSelectItemData = {
    id: 'item-1',
    label: 'Sample Tree Item',
    icon: 'fa fa-folder',
    level: 0,
    hasChildren: true,
    expanded: false,
    selected: false,
    description: 'A sample tree node with children'
  };
  @Input() selectable: boolean = true;
  @Input() showCheckbox: boolean = true;
  @Input() showIcon: boolean = true;
  @Input() showDescription: boolean = false;
  @Input() showExpandCollapse: boolean = true;
  @Input() expandOnClick: boolean = false;
  @Input() indentSize: number = 20;
  @Input() multiSelect: boolean = false;
  @Input() disabled: boolean = false;
  @Input() readonly: boolean = false;
  @Input() loading: boolean = false;
  @Input() draggable: boolean = false;
  @Input() droppable: boolean = false;
  @Input() highlightSearch: string = '';
  
  // Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  @Output() itemClick = new EventEmitter<TreeSelectItemData>();
  @Output() itemSelect = new EventEmitter<TreeSelectItemData>();
  @Output() itemDeselect = new EventEmitter<TreeSelectItemData>();
  @Output() itemToggle = new EventEmitter<TreeSelectItemData>();
  @Output() itemExpand = new EventEmitter<TreeSelectItemData>();
  @Output() itemCollapse = new EventEmitter<TreeSelectItemData>();
  @Output() itemDoubleClick = new EventEmitter<TreeSelectItemData>();
  @Output() itemContextMenu = new EventEmitter<{ item: TreeSelectItemData, event: MouseEvent }>();
  @Output() itemDragStart = new EventEmitter<{ item: TreeSelectItemData, event: DragEvent }>();
  @Output() itemDragOver = new EventEmitter<{ item: TreeSelectItemData, event: DragEvent }>();
  @Output() itemDrop = new EventEmitter<{ item: TreeSelectItemData, event: DragEvent }>();

  get computedClasses(): string {
    const baseClasses = [
      'tree-select-item-widget',
      'flex',
      'items-center',
      'transition-all',
      'duration-200',
      'relative'
    ];

    if (this.className) {
      baseClasses.push(this.className);
    }

    return baseClasses.join(' ');
  }

  get itemClasses(): string {
    const baseClasses = [
      'flex',
      'items-center',
      'w-full',
      'transition-all',
      'duration-200',
      'group'
    ];

    // Size classes
    const sizeClasses = {
      xs: ['text-xs', 'py-1', 'px-2'],
      sm: ['text-sm', 'py-1.5', 'px-2'],
      md: ['text-base', 'py-2', 'px-3'],
      lg: ['text-lg', 'py-2.5', 'px-4'],
      xl: ['text-xl', 'py-3', 'px-4']
    };

    // Variant classes for selection state
    const variantClasses = {
      default: this.item.selected 
        ? ['bg-gray-100', 'text-gray-900', 'border-l-2', 'border-gray-400']
        : ['hover:bg-gray-50', 'text-gray-700'],
      primary: this.item.selected 
        ? ['bg-blue-50', 'text-blue-900', 'border-l-2', 'border-blue-500']
        : ['hover:bg-blue-50', 'text-gray-700'],
      secondary: this.item.selected 
        ? ['bg-gray-100', 'text-gray-900', 'border-l-2', 'border-gray-500']
        : ['hover:bg-gray-100', 'text-gray-700'],
      success: this.item.selected 
        ? ['bg-green-50', 'text-green-900', 'border-l-2', 'border-green-500']
        : ['hover:bg-green-50', 'text-gray-700'],
      warning: this.item.selected 
        ? ['bg-yellow-50', 'text-yellow-900', 'border-l-2', 'border-yellow-500']
        : ['hover:bg-yellow-50', 'text-gray-700'],
      danger: this.item.selected 
        ? ['bg-red-50', 'text-red-900', 'border-l-2', 'border-red-500']
        : ['hover:bg-red-50', 'text-gray-700']
    };

    // Rounded classes
    const roundedClasses = {
      none: [],
      sm: ['rounded-sm'],
      md: ['rounded-md'],
      lg: ['rounded-lg'],
      full: ['rounded-full']
    };

    // State classes
    if (this.item.disabled || this.disabled) {
      baseClasses.push('opacity-50', 'cursor-not-allowed');
    } else {
      baseClasses.push('cursor-pointer');
    }

    if (this.loading) {
      baseClasses.push('opacity-75');
    }

    if (this.draggable) {
      baseClasses.push('select-none');
    }

    return [
      ...baseClasses,
      ...sizeClasses[this.size],
      ...variantClasses[this.variant],
      ...roundedClasses[this.rounded]
    ].join(' ');
  }

  get indentStyle(): { [key: string]: string } {
    const level = this.item.level || 0;
    return {
      'padding-left': `${level * this.indentSize}px`
    };
  }

  get expandIconClasses(): string {
    const baseClasses = [
      'transition-transform',
      'duration-200',
      'text-gray-400',
      'hover:text-gray-600'
    ];

    if (this.item.expanded) {
      baseClasses.push('rotate-90');
    }

    return baseClasses.join(' ');
  }

  onItemClick(event: MouseEvent): void {
    if (this.item.disabled || this.disabled || this.readonly) return;

    event.preventDefault();
    event.stopPropagation();

    if (this.expandOnClick && this.item.hasChildren) {
      this.toggleExpand();
    }

    this.itemClick.emit(this.item);

    if (this.selectable) {
      this.toggleSelection();
    }
  }

  onCheckboxChange(event: Event): void {
    event.stopPropagation();
    
    if (this.item.disabled || this.disabled || this.readonly) return;

    this.toggleSelection();
  }

  toggleSelection(): void {
    if (!this.selectable || this.item.disabled || this.disabled || this.readonly) return;

    this.item.selected = !this.item.selected;
    
    if (this.item.selected) {
      this.itemSelect.emit(this.item);
    } else {
      this.itemDeselect.emit(this.item);
    }
    
    this.itemToggle.emit(this.item);
  }

  onExpandClick(event: MouseEvent): void {
    event.preventDefault();
    event.stopPropagation();
    
    if (!this.item.hasChildren || this.item.disabled || this.disabled) return;
    
    this.toggleExpand();
  }

  toggleExpand(): void {
    if (!this.item.hasChildren) return;

    this.item.expanded = !this.item.expanded;
    
    if (this.item.expanded) {
      this.itemExpand.emit(this.item);
    } else {
      this.itemCollapse.emit(this.item);
    }
  }

  onDoubleClick(event: MouseEvent): void {
    if (this.item.disabled || this.disabled) return;
    
    event.preventDefault();
    event.stopPropagation();
    
    this.itemDoubleClick.emit(this.item);
  }

  onContextMenu(event: MouseEvent): void {
    if (this.item.disabled || this.disabled) return;
    
    event.preventDefault();
    this.itemContextMenu.emit({ item: this.item, event });
  }

  onDragStart(event: DragEvent): void {
    if (!this.draggable || this.item.disabled || this.disabled) {
      event.preventDefault();
      return;
    }
    
    this.itemDragStart.emit({ item: this.item, event });
  }

  onDragOver(event: DragEvent): void {
    if (!this.droppable || this.item.disabled || this.disabled) return;
    
    event.preventDefault();
    this.itemDragOver.emit({ item: this.item, event });
  }

  onDrop(event: DragEvent): void {
    if (!this.droppable || this.item.disabled || this.disabled) return;
    
    event.preventDefault();
    this.itemDrop.emit({ item: this.item, event });
  }

  getHighlightedLabel(): string {
    if (!this.highlightSearch || !this.item.label) {
      return this.item.label;
    }

    const searchTerm = this.highlightSearch.toLowerCase();
    const label = this.item.label;
    const index = label.toLowerCase().indexOf(searchTerm);
    
    if (index === -1) {
      return label;
    }

    const before = label.substring(0, index);
    const match = label.substring(index, index + searchTerm.length);
    const after = label.substring(index + searchTerm.length);
    
    return `${before}<mark class="bg-yellow-200 px-1 rounded">${match}</mark>${after}`;
  }
}
