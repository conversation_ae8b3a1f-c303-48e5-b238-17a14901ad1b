<div [class]="computedClasses" 
     [style]="indentStyle"
     [draggable]="draggable && !item.disabled && !disabled"
     (dragstart)="onDragStart($event)"
     (dragover)="onDragOver($event)" 
     (drop)="onDrop($event)"
     role="treeitem"
     [attr.aria-selected]="item.selected"
     [attr.aria-expanded]="item.hasChildren ? item.expanded : null"
     [attr.aria-level]="(item.level || 0) + 1"
     [attr.aria-disabled]="item.disabled || disabled">

  <!-- Tree Item Content -->
  <div [class]="itemClasses"
       (click)="onItemClick($event)"
       (dblclick)="onDoubleClick($event)"
       (contextmenu)="onContextMenu($event)">

    <!-- Expand/Collapse Button -->
    <button *ngIf="showExpandCollapse && item.hasChildren"
            type="button"
            (click)="onExpandClick($event)"
            class="flex items-center justify-center w-4 h-4 mr-1 text-gray-400 hover:text-gray-600 transition-colors focus:outline-none focus:text-gray-600"
            [attr.aria-label]="item.expanded ? 'Collapse' : 'Expand'"
            [disabled]="item.disabled || disabled">
      <i class="fa fa-chevron-right text-xs"
         [class]="expandIconClasses"></i>
    </button>

    <!-- Spacer for items without children -->
    <div *ngIf="showExpandCollapse && !item.hasChildren" 
         class="w-4 h-4 mr-1"></div>

    <!-- Checkbox for selection -->
    <div *ngIf="showCheckbox && selectable" 
         class="flex items-center mr-2">
      <input type="checkbox"
             [checked]="item.selected"
             (change)="onCheckboxChange($event)"
             [disabled]="item.disabled || disabled || readonly"
             class="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500 focus:ring-2"
             [attr.aria-label]="'Select ' + item.label">
    </div>

    <!-- Loading spinner -->
    <div *ngIf="loading" 
         class="flex items-center mr-2">
      <div class="animate-spin rounded-full h-4 w-4 border-2 border-gray-300 border-t-blue-500"></div>
    </div>

    <!-- Item Icon -->
    <span *ngIf="showIcon && item.icon && !loading" 
          [class]="item.icon + ' mr-2 text-gray-500 flex-shrink-0'"
          [class.text-blue-500]="item.selected && variant === 'primary'"
          [class.text-green-500]="item.selected && variant === 'success'"
          [class.text-yellow-500]="item.selected && variant === 'warning'"
          [class.text-red-500]="item.selected && variant === 'danger'"></span>

    <!-- Default folder/file icon -->
    <span *ngIf="showIcon && !item.icon && !loading" 
          class="mr-2 text-gray-400 flex-shrink-0">
      <i class="fa"
         [class.fa-folder]="item.hasChildren && !item.expanded"
         [class.fa-folder-open]="item.hasChildren && item.expanded"
         [class.fa-file]="!item.hasChildren"></i>
    </span>

    <!-- Item Content -->
    <div class="flex-1 min-w-0">
      <!-- Item Label -->
      <div class="flex items-center">
        <span class="text-sm font-medium truncate"
              [class.font-semibold]="item.selected"
              [innerHTML]="getHighlightedLabel()">
        </span>
        
        <!-- Badge or count if has children -->
        <span *ngIf="item.hasChildren && item.children && item.children.length > 0"
              class="ml-2 px-2 py-0.5 text-xs bg-gray-100 text-gray-600 rounded-full">
          {{ item.children.length }}
        </span>
      </div>

      <!-- Item Description -->
      <div *ngIf="showDescription && item.description" 
           class="text-xs text-gray-500 mt-1 truncate">
        {{ item.description }}
      </div>
    </div>

    <!-- Action indicators -->
    <div class="flex items-center ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
      <!-- Drag handle -->
      <div *ngIf="draggable && !item.disabled && !disabled"
           class="text-gray-400 hover:text-gray-600 cursor-grab active:cursor-grabbing">
        <i class="fa fa-grip-vertical text-xs"></i>
      </div>
      
      <!-- Drop indicator -->
      <div *ngIf="droppable && !item.disabled && !disabled"
           class="text-gray-400">
        <i class="fa fa-plus text-xs"></i>
      </div>
    </div>
  </div>

  <!-- Selection highlight bar -->
  <div *ngIf="item.selected"
       class="absolute left-0 top-0 bottom-0 w-1 transition-all duration-200"
       [class.bg-gray-400]="variant === 'default'"
       [class.bg-blue-500]="variant === 'primary'"
       [class.bg-gray-500]="variant === 'secondary'"
       [class.bg-green-500]="variant === 'success'"
       [class.bg-yellow-500]="variant === 'warning'"
       [class.bg-red-500]="variant === 'danger'"></div>
</div>
