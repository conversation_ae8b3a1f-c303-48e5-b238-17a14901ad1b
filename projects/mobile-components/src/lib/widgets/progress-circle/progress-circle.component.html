<div 
  [class]="computedClasses"
  [attr.role]="'progressbar'"
  [attr.aria-valuenow]="progress"
  [attr.aria-valuemin]="0"
  [attr.aria-valuemax]="100"
  [attr.aria-label]="label || 'Progress: ' + formattedProgress"
>
  <!-- SVG Circle Container -->
  <div class="relative inline-flex items-center justify-center">
    <svg
      [class]="svgClasses"
      [attr.width]="svgSize"
      [attr.height]="svgSize"
      [attr.viewBox]="'0 0 ' + svgSize + ' ' + svgSize"
      xmlns="http://www.w3.org/2000/svg"
    >
      <!-- Gradient Definition (if using gradient) -->
      <defs *ngIf="useGradient">
        <linearGradient [id]="gradientId" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop 
            *ngFor="let color of gradientColors; let i = index"
            [attr.offset]="(i / (gradientColors.length - 1) * 100) + '%'"
            [attr.stop-color]="color"
          />
        </linearGradient>
      </defs>

      <!-- Background Circle -->
      <circle
        [class]="circleBackgroundClasses + ' ' + backgroundProgressColor"
        [attr.cx]="svgSize / 2"
        [attr.cy]="svgSize / 2"
        [attr.r]="radius"
        [attr.stroke-width]="strokeWidth"
        [style.stroke]="backgroundColor || null"
      />

      <!-- Progress Circle -->
      <circle
        [class]="circleProgressClasses + ' ' + progressColor"
        [attr.cx]="svgSize / 2"
        [attr.cy]="svgSize / 2"
        [attr.r]="radius"
        [attr.stroke-width]="strokeWidth"
        [attr.stroke-dasharray]="strokeDasharray"
        [attr.stroke-dashoffset]="strokeDashoffset"
        [attr.stroke-linecap]="lineCap"
        [attr.stroke]="useGradient ? ('url(#' + gradientId + ')') : null"
        [style.transform-origin]="(svgSize / 2) + 'px ' + (svgSize / 2) + 'px'"
        [style.transform]="getRotationTransform()"
      />
    </svg>

    <!-- Center Content -->
    <div [class]="centerContentClasses">
      <!-- Percentage Display -->
      <div 
        *ngIf="showPercentage"
        [class]="percentageClasses"
        [attr.aria-hidden]="true"
      >
        {{ formattedProgress }}
      </div>

      <!-- Label -->
      <div 
        *ngIf="showLabel && label"
        [class]="labelClasses"
        [attr.aria-hidden]="true"
      >
        {{ label }}
      </div>

      <!-- Status Icon for Complete State -->
      <div 
        *ngIf="isComplete && !showPercentage"
        class="complete-icon text-current"
        [attr.aria-hidden]="true"
      >
        <i class="fa fa-check" [class]="'text-' + size"></i>
      </div>
    </div>
  </div>

  <!-- Status Information (Screen Reader) -->
  <div class="sr-only" [attr.aria-live]="'polite'">
    {{ label ? label + ': ' : 'Progress: ' }} {{ formattedProgress }}
    {{ isComplete ? ' - Complete' : '' }}
  </div>
</div>
