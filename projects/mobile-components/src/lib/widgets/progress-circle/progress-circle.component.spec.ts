import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DebugElement } from '@angular/core';
import { By } from '@angular/platform-browser';

import { ProgressCircleComponent } from './progress-circle.component';

describe('ProgressCircleComponent', () => {
  let component: ProgressCircleComponent;
  let fixture: ComponentFixture<ProgressCircleComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ProgressCircleComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ProgressCircleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should have default values', () => {
      expect(component.progress).toBe(65);
      expect(component.showPercentage).toBe(true);
      expect(component.showLabel).toBe(true);
      expect(component.animated).toBe(true);
      expect(component.thickness).toBe('normal');
      expect(component.lineCap).toBe('round');
      expect(component.clockwise).toBe(true);
      expect(component.size).toBe('md');
      expect(component.variant).toBe('primary');
      expect(component.rounded).toBe('full');
    });

    it('should initialize SVG dimensions correctly', () => {
      expect(component.svgSize).toBe(120);
      expect(component.strokeWidth).toBe(8);
      expect(component.radius).toBe(52);
      expect(component.circumference).toBeCloseTo(326.73, 2);
    });
  });

  describe('Progress Calculation', () => {
    it('should calculate stroke dash offset correctly for different progress values', () => {
      component.progress = 50;
      component.ngOnChanges();
      expect(component.strokeDashoffset).toBeCloseTo(163.36, 2);

      component.progress = 100;
      component.ngOnChanges();
      expect(component.strokeDashoffset).toBeCloseTo(0, 2);

      component.progress = 0;
      component.ngOnChanges();
      expect(component.strokeDashoffset).toBeCloseTo(326.73, 2);
    });

    it('should clamp progress values between 0 and 100', () => {
      component.progress = -10;
      component.ngOnChanges();
      expect(component.strokeDashoffset).toBeCloseTo(326.73, 2);

      component.progress = 150;
      component.ngOnChanges();
      expect(component.strokeDashoffset).toBeCloseTo(0, 2);
    });

    it('should handle counter-clockwise direction', () => {
      component.clockwise = false;
      component.progress = 25;
      component.ngOnChanges();
      expect(component.strokeDashoffset).toBeCloseTo(81.68, 2);
    });
  });

  describe('Size and Thickness Configuration', () => {
    it('should adjust dimensions for different sizes', () => {
      component.size = 'xs';
      component.ngOnChanges();
      expect(component.svgSize).toBe(60);
      expect(component.strokeWidth).toBe(4);

      component.size = 'xl';
      component.ngOnChanges();
      expect(component.svgSize).toBe(200);
      expect(component.strokeWidth).toBe(12);
    });

    it('should adjust stroke width for different thickness values', () => {
      component.thickness = 'thin';
      component.ngOnChanges();
      expect(component.strokeWidth).toBe(5); // 8 * 0.6 rounded

      component.thickness = 'thick';
      component.ngOnChanges();
      expect(component.strokeWidth).toBe(11); // 8 * 1.4 rounded

      component.thickness = 'extra-thick';
      component.ngOnChanges();
      expect(component.strokeWidth).toBe(14); // 8 * 1.8 rounded
    });
  });

  describe('Template Rendering', () => {
    beforeEach(() => {
      component.progress = 75;
      component.label = 'Loading';
      fixture.detectChanges();
    });

    it('should render SVG with correct dimensions', () => {
      const svg = fixture.debugElement.query(By.css('svg'));
      expect(svg).toBeTruthy();
      expect(svg.nativeElement.getAttribute('width')).toBe('120');
      expect(svg.nativeElement.getAttribute('height')).toBe('120');
    });

    it('should render background and progress circles', () => {
      const circles = fixture.debugElement.queryAll(By.css('circle'));
      expect(circles.length).toBe(2);
      
      const backgroundCircle = circles[0];
      const progressCircle = circles[1];
      
      expect(backgroundCircle.nativeElement).toHaveClass('progress-background');
      expect(progressCircle.nativeElement).toHaveClass('progress-foreground');
    });

    it('should display percentage when showPercentage is true', () => {
      const percentage = fixture.debugElement.query(By.css('.progress-percentage'));
      expect(percentage).toBeTruthy();
      expect(percentage.nativeElement.textContent.trim()).toBe('75%');
    });

    it('should hide percentage when showPercentage is false', () => {
      component.showPercentage = false;
      fixture.detectChanges();
      
      const percentage = fixture.debugElement.query(By.css('.progress-percentage'));
      expect(percentage).toBeFalsy();
    });

    it('should display label when showLabel is true and label is provided', () => {
      const label = fixture.debugElement.query(By.css('.progress-label'));
      expect(label).toBeTruthy();
      expect(label.nativeElement.textContent.trim()).toBe('Loading');
    });

    it('should hide label when showLabel is false', () => {
      component.showLabel = false;
      fixture.detectChanges();
      
      const label = fixture.debugElement.query(By.css('.progress-label'));
      expect(label).toBeFalsy();
    });

    it('should hide label when no label is provided', () => {
      component.label = '';
      fixture.detectChanges();
      
      const label = fixture.debugElement.query(By.css('.progress-label'));
      expect(label).toBeFalsy();
    });
  });

  describe('Complete State', () => {
    beforeEach(() => {
      component.progress = 100;
      component.showPercentage = false;
      fixture.detectChanges();
    });

    it('should show complete icon when progress is 100% and percentage is hidden', () => {
      const completeIcon = fixture.debugElement.query(By.css('.complete-icon'));
      expect(completeIcon).toBeTruthy();
      
      const checkIcon = completeIcon.query(By.css('.fa-check'));
      expect(checkIcon).toBeTruthy();
    });

    it('should indicate complete status correctly', () => {
      expect(component.isComplete).toBe(true);
    });

    it('should not show complete icon when percentage is shown', () => {
      component.showPercentage = true;
      fixture.detectChanges();
      
      const completeIcon = fixture.debugElement.query(By.css('.complete-icon'));
      expect(completeIcon).toBeFalsy();
    });
  });

  describe('Variant Styling', () => {
    it('should apply correct color classes for different variants', () => {
      component.variant = 'success';
      expect(component.progressColor).toBe('text-green-600');
      expect(component.backgroundProgressColor).toBe('text-green-200');

      component.variant = 'danger';
      expect(component.progressColor).toBe('text-red-600');
      expect(component.backgroundProgressColor).toBe('text-red-200');

      component.variant = 'warning';
      expect(component.progressColor).toBe('text-yellow-600');
      expect(component.backgroundProgressColor).toBe('text-yellow-200');
    });

    it('should apply variant colors to circles', () => {
      component.variant = 'success';
      fixture.detectChanges();
      
      const circles = fixture.debugElement.queryAll(By.css('circle'));
      const backgroundCircle = circles[0];
      const progressCircle = circles[1];
      
      expect(backgroundCircle.nativeElement).toHaveClass('text-green-200');
      expect(progressCircle.nativeElement).toHaveClass('text-green-600');
    });
  });

  describe('Gradient Support', () => {
    beforeEach(() => {
      component.gradientColors = ['#ff0000', '#00ff00', '#0000ff'];
      fixture.detectChanges();
    });

    it('should detect gradient usage correctly', () => {
      expect(component.useGradient).toBe(true);
      
      component.gradientColors = ['#ff0000'];
      expect(component.useGradient).toBe(false);
      
      component.gradientColors = [];
      expect(component.useGradient).toBe(false);
    });

    it('should render gradient definition when using gradients', () => {
      const defs = fixture.debugElement.query(By.css('defs'));
      expect(defs).toBeTruthy();
      
      const gradient = defs.query(By.css('linearGradient'));
      expect(gradient).toBeTruthy();
      
      const stops = gradient.queryAll(By.css('stop'));
      expect(stops.length).toBe(3);
    });

    it('should generate unique gradient ID', () => {
      const gradientId = component.gradientId;
      expect(gradientId).toContain('progress-gradient-primary-md');
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      component.progress = 60;
      component.label = 'File Upload';
      fixture.detectChanges();
    });

    it('should have proper ARIA attributes', () => {
      const container = fixture.debugElement.query(By.css('[role="progressbar"]'));
      expect(container).toBeTruthy();
      expect(container.nativeElement.getAttribute('aria-valuenow')).toBe('60');
      expect(container.nativeElement.getAttribute('aria-valuemin')).toBe('0');
      expect(container.nativeElement.getAttribute('aria-valuemax')).toBe('100');
      expect(container.nativeElement.getAttribute('aria-label')).toBe('File Upload');
    });

    it('should provide default aria-label when no label is provided', () => {
      component.label = '';
      fixture.detectChanges();
      
      const container = fixture.debugElement.query(By.css('[role="progressbar"]'));
      expect(container.nativeElement.getAttribute('aria-label')).toBe('Progress: 60%');
    });

    it('should hide decorative elements from screen readers', () => {
      const percentage = fixture.debugElement.query(By.css('.progress-percentage'));
      const label = fixture.debugElement.query(By.css('.progress-label'));
      
      expect(percentage.nativeElement.getAttribute('aria-hidden')).toBe('true');
      expect(label.nativeElement.getAttribute('aria-hidden')).toBe('true');
    });

    it('should provide screen reader updates', () => {
      const srOnly = fixture.debugElement.query(By.css('.sr-only'));
      expect(srOnly).toBeTruthy();
      expect(srOnly.nativeElement.getAttribute('aria-live')).toBe('polite');
      expect(srOnly.nativeElement.textContent).toContain('File Upload: 60%');
    });
  });

  describe('Progress Status', () => {
    it('should determine progress status correctly', () => {
      component.progress = 25;
      expect(component.progressStatus).toBe('low');

      component.progress = 60;
      expect(component.progressStatus).toBe('medium');

      component.progress = 85;
      expect(component.progressStatus).toBe('high');

      component.progress = 100;
      expect(component.progressStatus).toBe('complete');
    });
  });

  describe('Animation Control', () => {
    it('should apply animation classes when animated is true', () => {
      component.animated = true;
      const svgClasses = component.svgClasses;
      expect(svgClasses).toContain('transition-all');
      expect(svgClasses).toContain('duration-1000');
    });

    it('should not apply animation classes when animated is false', () => {
      component.animated = false;
      const svgClasses = component.svgClasses;
      expect(svgClasses).not.toContain('transition-all');
      expect(svgClasses).not.toContain('duration-1000');
    });
  });

  describe('Custom Styling', () => {
    it('should apply custom className', () => {
      component.className = 'custom-progress-class';
      const classes = component.computedClasses;
      expect(classes).toContain('custom-progress-class');
    });

    it('should apply custom background color', () => {
      component.backgroundColor = '#custom-color';
      fixture.detectChanges();
      
      const backgroundCircle = fixture.debugElement.query(By.css('.progress-background'));
      expect(backgroundCircle.nativeElement.style.stroke).toBe('#custom-color');
    });
  });

  describe('Formatted Progress', () => {
    it('should format progress percentage correctly', () => {
      component.progress = 66.7;
      expect(component.formattedProgress).toBe('67%');

      component.progress = 33.2;
      expect(component.formattedProgress).toBe('33%');
    });
  });

  describe('Rotation Transform', () => {
    it('should provide correct rotation transform for clockwise direction', () => {
      component.clockwise = true;
      expect(component.getRotationTransform()).toBe('rotate(-90)');
    });

    it('should provide correct rotation transform for counter-clockwise direction', () => {
      component.clockwise = false;
      expect(component.getRotationTransform()).toBe('rotate(90)');
    });
  });
});
