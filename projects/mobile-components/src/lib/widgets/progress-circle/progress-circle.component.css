/* Progress Circle Component Styles */
.progress-circle-widget {
  @apply transition-all duration-300 ease-in-out;
}

.progress-svg {
  @apply drop-shadow-sm;
}

.progress-background {
  transition: stroke 0.3s ease;
}

.progress-foreground {
  @apply transition-all;
  transition-property: stroke-dashoffset, stroke, transform;
  transition-duration: 1s;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-content {
  @apply pointer-events-none;
}

.progress-percentage {
  @apply leading-none;
  font-variant-numeric: tabular-nums;
}

.progress-label {
  @apply leading-tight;
  word-break: break-word;
  max-width: 100%;
}

.complete-icon {
  @apply animate-bounce;
  animation-duration: 0.6s;
  animation-iteration-count: 2;
}

/* Smooth rotation animation */
@keyframes rotate {
  from {
    transform: rotate(-90deg);
  }
  to {
    transform: rotate(270deg);
  }
}

/* Pulse effect for low progress */
.progress-circle-widget[aria-valuenow="0"] .progress-foreground {
  @apply animate-pulse;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .progress-background {
    @apply opacity-40;
  }
  
  .progress-foreground {
    @apply drop-shadow-md;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .progress-svg,
  .progress-foreground,
  .progress-background,
  .progress-circle-widget {
    transition: none !important;
    animation: none !important;
  }
  
  .complete-icon {
    animation: none;
  }
}

/* Focus styles for accessibility */
.progress-circle-widget:focus-within {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2 rounded-full;
}

/* Size-specific adjustments */
.progress-circle-widget .progress-content {
  max-width: calc(100% - 20px);
}

/* RTL support */
[dir="rtl"] .progress-svg {
  transform: rotate(90deg) scaleX(-1);
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .progress-background {
    @apply opacity-30;
  }
  
  .progress-label {
    @apply text-gray-300;
  }
}