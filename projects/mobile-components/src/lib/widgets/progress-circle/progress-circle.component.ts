import { Component, Input, OnInit, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'lib-progress-circle',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './progress-circle.component.html',
  styleUrl: './progress-circle.component.css'
})
export class ProgressCircleComponent implements OnInit, OnChanges {
  @Input() progress: number = 65; // Percentage (0-100)
  @Input() label: string = '';
  @Input() showPercentage: boolean = true;
  @Input() showLabel: boolean = true;
  @Input() animated: boolean = true;
  @Input() thickness: 'thin' | 'normal' | 'thick' | 'extra-thick' = 'normal';
  @Input() lineCap: 'round' | 'square' | 'butt' = 'round';
  @Input() clockwise: boolean = true;
  @Input() backgroundColor: string = '';
  @Input() gradientColors: string[] = [];
  
  // Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'primary';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'full';

  // SVG circle properties
  svgSize: number = 120;
  strokeWidth: number = 8;
  radius: number = 52;
  circumference: number = 0;
  strokeDasharray: string = '';
  strokeDashoffset: number = 0;

  ngOnInit(): void {
    this.calculateDimensions();
    this.updateProgress();
  }

  ngOnChanges(): void {
    this.calculateDimensions();
    this.updateProgress();
  }

  private calculateDimensions(): void {
    // Size-based dimensions
    const sizeConfig = {
      xs: { svg: 60, stroke: 4 },
      sm: { svg: 80, stroke: 6 },
      md: { svg: 120, stroke: 8 },
      lg: { svg: 160, stroke: 10 },
      xl: { svg: 200, stroke: 12 }
    };

    const config = sizeConfig[this.size];
    this.svgSize = config.svg;
    
    // Thickness adjustments
    const thicknessMultiplier = {
      thin: 0.6,
      normal: 1,
      thick: 1.4,
      'extra-thick': 1.8
    };
    
    this.strokeWidth = Math.round(config.stroke * thicknessMultiplier[this.thickness]);
    this.radius = (this.svgSize - this.strokeWidth) / 2;
    this.circumference = 2 * Math.PI * this.radius;
  }

  private updateProgress(): void {
    // Clamp progress between 0 and 100
    const clampedProgress = Math.max(0, Math.min(100, this.progress));
    
    // Calculate stroke dash properties
    this.strokeDasharray = `${this.circumference}`;
    
    if (this.clockwise) {
      this.strokeDashoffset = this.circumference - (clampedProgress / 100) * this.circumference;
    } else {
      this.strokeDashoffset = (clampedProgress / 100) * this.circumference;
    }
  }

  get computedClasses(): string {
    const baseClasses = [
      'progress-circle-widget',
      'inline-flex',
      'flex-col',
      'items-center',
      'justify-center'
    ];

    if (this.className) {
      baseClasses.push(this.className);
    }

    return baseClasses.join(' ');
  }

  get svgClasses(): string {
    const baseClasses = ['progress-svg', 'transform', 'rotate-90'];

    if (this.animated) {
      baseClasses.push('transition-all', 'duration-1000', 'ease-out');
    }

    return baseClasses.join(' ');
  }

  get circleBackgroundClasses(): string {
    return 'progress-background fill-none stroke-current opacity-20';
  }

  get circleProgressClasses(): string {
    const baseClasses = [
      'progress-foreground',
      'fill-none',
      'stroke-current',
      'transition-all'
    ];

    if (this.animated) {
      baseClasses.push('duration-1000', 'ease-out');
    }

    return baseClasses.join(' ');
  }

  get progressColor(): string {
    const variantColors = {
      default: 'text-gray-600',
      primary: 'text-blue-600',
      secondary: 'text-gray-500',
      success: 'text-green-600',
      warning: 'text-yellow-600',
      danger: 'text-red-600'
    };

    return variantColors[this.variant];
  }

  get backgroundProgressColor(): string {
    const variantColors = {
      default: 'text-gray-300',
      primary: 'text-blue-200',
      secondary: 'text-gray-300',
      success: 'text-green-200',
      warning: 'text-yellow-200',
      danger: 'text-red-200'
    };

    return variantColors[this.variant];
  }

  get centerContentClasses(): string {
    const baseClasses = [
      'progress-content',
      'absolute',
      'inset-0',
      'flex',
      'flex-col',
      'items-center',
      'justify-center',
      'text-center'
    ];

    return baseClasses.join(' ');
  }

  get percentageClasses(): string {
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-lg',
      lg: 'text-xl',
      xl: 'text-2xl'
    };

    return `progress-percentage font-bold ${this.progressColor} ${sizeClasses[this.size]}`;
  }

  get labelClasses(): string {
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-xs',
      md: 'text-sm',
      lg: 'text-base',
      xl: 'text-lg'
    };

    return `progress-label text-gray-600 ${sizeClasses[this.size]} mt-1`;
  }

  get gradientId(): string {
    return `progress-gradient-${this.variant}-${this.size}`;
  }

  get useGradient(): boolean {
    return this.gradientColors.length >= 2;
  }

  getRotationTransform(): string {
    return this.clockwise ? 'rotate(-90)' : 'rotate(90)';
  }

  get formattedProgress(): string {
    return Math.round(this.progress) + '%';
  }

  // Helper method to determine if progress should be considered complete
  get isComplete(): boolean {
    return this.progress >= 100;
  }

  // Helper method to determine progress status
  get progressStatus(): 'low' | 'medium' | 'high' | 'complete' {
    if (this.progress >= 100) return 'complete';
    if (this.progress >= 75) return 'high';
    if (this.progress >= 50) return 'medium';
    return 'low';
  }
}
