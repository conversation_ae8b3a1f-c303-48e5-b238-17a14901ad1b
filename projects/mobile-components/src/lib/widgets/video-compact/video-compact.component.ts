import { Component, Input, Output, EventEmitter, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface VideoSource {
  src: string;
  type: string;
  quality?: string;
}

export interface VideoData {
  id?: string;
  title: string;
  description?: string;
  thumbnail?: string;
  duration?: string;
  sources: VideoSource[];
  poster?: string;
  author?: string;
  publishDate?: string;
  views?: number;
  likes?: number;
  tags?: string[];
  captions?: string;
}

@Component({
  selector: 'lib-video-compact',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './video-compact.component.html',
  styleUrl: './video-compact.component.css'
})
export class VideoCompactComponent implements AfterViewInit {
  @ViewChild('videoElement') videoElement!: ElementRef<HTMLVideoElement>;

  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  
  // Component-specific properties
  @Input() video: VideoData = {
    id: '1',
    title: 'Introduction to Modern Web Development',
    description: 'Learn the fundamentals of modern web development including HTML5, CSS3, and JavaScript ES6+. This comprehensive tutorial covers best practices and industry standards.',
    thumbnail: 'https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?w=600&h=400&fit=crop',
    duration: '12:45',
    poster: 'https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?w=600&h=400&fit=crop',
    sources: [
      {
        src: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
        type: 'video/mp4',
        quality: '720p'
      }
    ],
    author: 'Tech Academy',
    publishDate: '2024-01-15',
    views: 15420,
    likes: 892,
    tags: ['Web Development', 'Tutorial', 'Frontend'],
    captions: 'https://example.com/captions.vtt'
  };
  @Input() autoplay: boolean = false;
  @Input() muted: boolean = false;
  @Input() loop: boolean = false;
  @Input() controls: boolean = true;
  @Input() preload: 'none' | 'metadata' | 'auto' = 'metadata';
  @Input() playbackSpeed: number = 1;
  @Input() showInfo: boolean = true;
  @Input() showStats: boolean = true;
  @Input() showTags: boolean = true;
  @Input() aspectRatio: '16:9' | '4:3' | '1:1' | '9:16' = '16:9';
  @Input() quality: 'auto' | '240p' | '360p' | '480p' | '720p' | '1080p' = 'auto';

  // Events
  @Output() play = new EventEmitter<Event>();
  @Output() pause = new EventEmitter<Event>();
  @Output() ended = new EventEmitter<Event>();
  @Output() timeUpdate = new EventEmitter<Event>();
  @Output() loadedData = new EventEmitter<Event>();
  @Output() error = new EventEmitter<Event>();
  @Output() tagClick = new EventEmitter<string>();

  // Component state
  isPlaying: boolean = false;
  currentTime: number = 0;
  duration: number = 0;
  bufferedProgress: number = 0;
  volume: number = 1;
  isFullscreen: boolean = false;
  showControls: boolean = true;
  controlsTimeout?: number;

  ngAfterViewInit(): void {
    if (this.videoElement) {
      this.setupVideoEvents();
    }
  }

  get containerClasses(): string {
    const sizeClasses = {
      xs: 'max-w-xs',
      sm: 'max-w-sm',
      md: 'max-w-md',
      lg: 'max-w-lg',
      xl: 'max-w-xl'
    };

    const variantClasses = {
      default: 'bg-black',
      primary: 'bg-blue-900',
      secondary: 'bg-gray-900',
      success: 'bg-green-900',
      warning: 'bg-yellow-900',
      danger: 'bg-red-900'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const aspectClasses = {
      '16:9': 'aspect-video',
      '4:3': 'aspect-[4/3]',
      '1:1': 'aspect-square',
      '9:16': 'aspect-[9/16]'
    };

    return `
      relative overflow-hidden
      ${sizeClasses[this.size]}
      ${variantClasses[this.variant]}
      ${roundedClasses[this.rounded]}
      ${aspectClasses[this.aspectRatio]}
      ${this.className}
    `.trim().replace(/\s+/g, ' ');
  }

  get videoClasses(): string {
    return `
      w-full h-full object-cover
      ${this.rounded !== 'none' ? 'rounded-inherit' : ''}
    `.trim().replace(/\s+/g, ' ');
  }

  setupVideoEvents(): void {
    const video = this.videoElement.nativeElement;
    
    video.addEventListener('play', (event) => {
      this.isPlaying = true;
      this.play.emit(event);
    });

    video.addEventListener('pause', (event) => {
      this.isPlaying = false;
      this.pause.emit(event);
    });

    video.addEventListener('ended', (event) => {
      this.isPlaying = false;
      this.ended.emit(event);
    });

    video.addEventListener('timeupdate', (event) => {
      this.currentTime = video.currentTime;
      this.timeUpdate.emit(event);
    });

    video.addEventListener('loadeddata', (event) => {
      this.duration = video.duration;
      this.loadedData.emit(event);
    });

    video.addEventListener('error', (event) => {
      this.error.emit(event);
    });

    video.addEventListener('progress', () => {
      if (video.buffered.length > 0) {
        this.bufferedProgress = (video.buffered.end(0) / video.duration) * 100;
      }
    });
  }

  togglePlay(): void {
    const video = this.videoElement.nativeElement;
    if (this.isPlaying) {
      video.pause();
    } else {
      video.play();
    }
  }

  seek(time: number): void {
    const video = this.videoElement.nativeElement;
    video.currentTime = time;
  }

  setVolume(volume: number): void {
    const video = this.videoElement.nativeElement;
    this.volume = Math.max(0, Math.min(1, volume));
    video.volume = this.volume;
  }

  toggleMute(): void {
    const video = this.videoElement.nativeElement;
    video.muted = !video.muted;
  }

  toggleFullscreen(): void {
    const container = this.videoElement.nativeElement.parentElement;
    if (!this.isFullscreen && container) {
      container.requestFullscreen?.();
      this.isFullscreen = true;
    } else {
      document.exitFullscreen?.();
      this.isFullscreen = false;
    }
  }

  formatTime(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  formatNumber(num: number): string {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  onTagClick(tag: string): void {
    this.tagClick.emit(tag);
  }

  onProgressClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    if (target && this.duration > 0) {
      const rect = target.getBoundingClientRect();
      const clickX = event.clientX - rect.left;
      const percentage = clickX / rect.width;
      this.seek(percentage * this.duration);
    }
  }

  onQualityChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    if (target) {
      this.quality = target.value as any;
    }
  }

  showControlsTemporarily(): void {
    this.showControls = true;
    if (this.controlsTimeout) {
      clearTimeout(this.controlsTimeout);
    }
    this.controlsTimeout = window.setTimeout(() => {
      if (this.isPlaying) {
        this.showControls = false;
      }
    }, 3000);
  }
}
