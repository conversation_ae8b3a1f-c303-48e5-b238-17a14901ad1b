<div [class]="containerClasses" 
     (mousemove)="showControlsTemporarily()"
     (mouseleave)="showControls = false"
     role="region" 
     [attr.aria-label]="'Video player: ' + video.title">
  
  <!-- Video Element -->
  <video #videoElement
         [class]="videoClasses"
         [poster]="video.poster"
         [autoplay]="autoplay"
         [muted]="muted"
         [loop]="loop"
         [controls]="false"
         [preload]="preload"
         [attr.aria-label]="video.title">
    <source *ngFor="let source of video.sources" 
            [src]="source.src" 
            [type]="source.type">
    <track *ngIf="video.captions" 
           kind="captions" 
           [src]="video.captions" 
           srclang="en" 
           label="English">
    Your browser does not support the video tag.
  </video>

  <!-- Play Button Overlay -->
  <div *ngIf="!isPlaying" 
       class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 cursor-pointer"
       (click)="togglePlay()"
       role="button"
       tabindex="0"
       aria-label="Play video"
       (keydown.enter)="togglePlay()"
       (keydown.space)="togglePlay()">
    <div class="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center hover:bg-opacity-100 transition-all duration-200">
      <svg class="w-8 h-8 text-gray-800 ml-1" fill="currentColor" viewBox="0 0 20 20">
        <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"/>
      </svg>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="duration === 0" 
       class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
  </div>

  <!-- Video Controls -->
  <div *ngIf="controls && showControls" 
       class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4 transition-opacity duration-300"
       [class.opacity-0]="!showControls && isPlaying">
    
    <!-- Progress Bar -->
    <div class="mb-3">
      <div class="relative h-1 bg-white bg-opacity-30 rounded-full cursor-pointer"
           (click)="onProgressClick($event)">
        <!-- Buffered Progress -->
        <div class="absolute h-full bg-white bg-opacity-50 rounded-full"
             [style.width.%]="bufferedProgress">
        </div>
        <!-- Current Progress -->
        <div class="absolute h-full bg-white rounded-full"
             [style.width.%]="(currentTime / duration) * 100">
        </div>
        <!-- Progress Handle -->
        <div class="absolute w-3 h-3 bg-white rounded-full transform -translate-y-1 -translate-x-1/2"
             [style.left.%]="(currentTime / duration) * 100">
        </div>
      </div>
    </div>

    <!-- Control Buttons -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <!-- Play/Pause Button -->
        <button (click)="togglePlay()" 
                class="text-white hover:text-gray-300 transition-colors duration-200"
                [attr.aria-label]="isPlaying ? 'Pause' : 'Play'">
          <svg *ngIf="!isPlaying" class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"/>
          </svg>
          <svg *ngIf="isPlaying" class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path d="M5.25 3a.75.75 0 01.75.75v12.5a.75.75 0 01-1.5 0V3.75A.75.75 0 015.25 3zM14.25 3a.75.75 0 01.75.75v12.5a.75.75 0 01-1.5 0V3.75a.75.75 0 01.75-.75z"/>
          </svg>
        </button>

        <!-- Volume Control -->
        <div class="flex items-center space-x-2">
          <button (click)="toggleMute()" 
                  class="text-white hover:text-gray-300 transition-colors duration-200"
                  aria-label="Toggle mute">
            <svg *ngIf="volume > 0" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.794L4.87 13.65H3a1 1 0 01-1-1V7.35a1 1 0 011-1h1.87l3.513-3.144a1 1 0 011.617.794zM15.657 6.343a1 1 0 011.414 0A7.97 7.97 0 0119 12c0 2.21-.895 4.21-2.343 5.657a1 1 0 01-1.414-1.414A5.972 5.972 0 0017 12c0-1.657-.672-3.157-1.757-4.243a1 1 0 010-1.414z"/>
              <path d="M11.828 7.172a1 1 0 011.414 0A3.987 3.987 0 0115 10c0 1.105-.448 2.105-1.172 2.828a1 1 0 11-1.414-1.414A1.994 1.994 0 0013 10c0-.553-.224-1.053-.586-1.414a1 1 0 010-1.414z"/>
            </svg>
            <svg *ngIf="volume === 0" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.794L4.87 13.65H3a1 1 0 01-1-1V7.35a1 1 0 011-1h1.87l3.513-3.144a1 1 0 011.617.794z"/>
              <path d="M12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z"/>
            </svg>
          </button>
        </div>

        <!-- Time Display -->
        <div class="text-white text-sm font-mono">
          {{ formatTime(currentTime) }} / {{ formatTime(duration) }}
        </div>
      </div>

      <div class="flex items-center space-x-2">
        <!-- Quality Selector -->
        <select *ngIf="video.sources.length > 1" 
                class="bg-black bg-opacity-50 text-white text-sm rounded px-2 py-1 border-none outline-none"
                [value]="quality"
                (change)="onQualityChange($event)"
                aria-label="Video quality">
          <option value="auto">Auto</option>
          <option *ngFor="let source of video.sources" [value]="source.quality">
            {{ source.quality }}
          </option>
        </select>

        <!-- Fullscreen Button -->
        <button (click)="toggleFullscreen()" 
                class="text-white hover:text-gray-300 transition-colors duration-200"
                aria-label="Toggle fullscreen">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"/>
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Video Duration Badge -->
  <div *ngIf="video.duration" 
       class="absolute top-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
    {{ video.duration }}
  </div>
</div>

<!-- Video Information -->
<div *ngIf="showInfo" class="mt-3">
  <!-- Title and Author -->
  <div class="mb-2">
    <h3 class="font-semibold text-gray-900 line-clamp-2 mb-1">
      {{ video.title }}
    </h3>
    <p *ngIf="video.author" class="text-sm text-gray-600">
      {{ video.author }}
      <span *ngIf="video.publishDate" class="text-gray-400">
        · {{ video.publishDate | date:'MMM d, y' }}
      </span>
    </p>
  </div>

  <!-- Stats -->
  <div *ngIf="showStats && (video.views || video.likes)" 
       class="flex items-center space-x-4 text-sm text-gray-500 mb-2">
    <span *ngIf="video.views" class="flex items-center">
      <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
      </svg>
      {{ formatNumber(video.views) }} views
    </span>
    <span *ngIf="video.likes" class="flex items-center">
      <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
      </svg>
      {{ formatNumber(video.likes) }}
    </span>
  </div>

  <!-- Description -->
  <p *ngIf="video.description" 
     class="text-sm text-gray-600 line-clamp-3 mb-2">
    {{ video.description }}
  </p>

  <!-- Tags -->
  <div *ngIf="showTags && video.tags && video.tags.length > 0" 
       class="flex flex-wrap gap-1">
    <button *ngFor="let tag of video.tags"
            class="px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200 transition-colors duration-200"
            (click)="onTagClick(tag)">
      #{{ tag }}
    </button>
  </div>
</div>
