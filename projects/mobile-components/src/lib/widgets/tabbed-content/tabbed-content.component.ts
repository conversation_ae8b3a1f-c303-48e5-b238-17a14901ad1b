import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface TabbedContentTab {
  id: string | number;
  title: string;
  content?: string;
  icon?: string;
  disabled?: boolean;
  active?: boolean;
  badge?: string | number;
  href?: string;
}

@Component({
  selector: 'lib-tabbed-content',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './tabbed-content.component.html',
  styleUrl: './tabbed-content.component.css'
})
export class TabbedContentComponent implements OnInit {
  @Input() tabs: TabbedContentTab[] = [
    { id: 1, title: 'Overview', active: true, content: 'This is the overview content panel with important information.', icon: '📊' },
    { id: 2, title: 'Details', content: 'Detailed information and specifications are shown here.', icon: '📋' },
    { id: 3, title: 'Settings', content: 'Configuration options and preferences panel.', icon: '⚙️' },
    { id: 4, title: 'Help', content: 'Documentation and help resources.', icon: '❓', badge: 'New' }
  ];
  
  @Input() layout: 'horizontal' | 'vertical' = 'horizontal';
  @Input() position: 'top' | 'bottom' | 'left' | 'right' = 'top';
  @Input() justified: boolean = false;
  @Input() centered: boolean = false;
  @Input() bordered: boolean = true;
  @Input() animated: boolean = true;
  @Input() showContent: boolean = true;
  @Input() persistent: boolean = false; // Keep all content in DOM
  @Input() lazy: boolean = false; // Load content only when tab is active
  @Input() closable: boolean = false;
  @Input() addable: boolean = false;
  @Input() scrollable: boolean = false;
  @Input() sticky: boolean = false;
  
  // Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'minimal' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full' = 'md';
  @Input() shadow: 'none' | 'sm' | 'md' | 'lg' | 'xl' = 'sm';
  
  // Content styling
  @Input() contentPadding: 'none' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() tabPadding: 'none' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  
  @Output() tabChange = new EventEmitter<TabbedContentTab>();
  @Output() tabClick = new EventEmitter<TabbedContentTab>();
  @Output() tabClose = new EventEmitter<TabbedContentTab>();
  @Output() tabAdd = new EventEmitter<void>();

  ngOnInit() {
    // Ensure at least one tab is active
    if (this.tabs.length > 0 && !this.tabs.some(tab => tab.active)) {
      this.tabs[0].active = true;
    }
  }

  get containerClasses(): string {
    const baseClasses = [
      'tabbed-content-widget',
      'w-full'
    ];

    if (this.layout === 'horizontal') {
      baseClasses.push('flex', 'flex-col');
    } else {
      baseClasses.push('flex', 'flex-row');
    }

    if (this.sticky) {
      baseClasses.push('sticky', 'top-0', 'z-20');
    }

    // Shadow classes
    const shadowClasses = {
      none: [],
      sm: ['shadow-sm'],
      md: ['shadow-md'],
      lg: ['shadow-lg'],
      xl: ['shadow-xl']
    };
    baseClasses.push(...shadowClasses[this.shadow]);

    // Rounded classes
    const roundedClasses = {
      none: [],
      sm: ['rounded-sm'],
      md: ['rounded-md'],
      lg: ['rounded-lg'],
      xl: ['rounded-xl'],
      full: ['rounded-full']
    };
    baseClasses.push(...roundedClasses[this.rounded]);

    if (this.className) {
      baseClasses.push(this.className);
    }

    return baseClasses.join(' ');
  }

  get tabListClasses(): string {
    const baseClasses = [
      'tab-list',
      'flex'
    ];

    if (this.layout === 'horizontal') {
      baseClasses.push('flex-row');
      if (this.position === 'bottom') {
        baseClasses.push('order-2');
      }
    } else {
      baseClasses.push('flex-col');
      if (this.position === 'right') {
        baseClasses.push('order-2');
      }
    }

    if (this.justified) {
      baseClasses.push('justify-between');
    }

    if (this.centered) {
      baseClasses.push('justify-center');
    }

    if (this.scrollable) {
      if (this.layout === 'horizontal') {
        baseClasses.push('overflow-x-auto', 'scrollbar-hide');
      } else {
        baseClasses.push('overflow-y-auto', 'scrollbar-hide');
      }
    }

    // Variant styling for tab list
    const variantClasses = {
      default: ['bg-gray-50', 'border-b', 'border-gray-200'],
      primary: ['bg-blue-50', 'border-b', 'border-blue-200'],
      secondary: ['bg-gray-100', 'border-b', 'border-gray-300'],
      success: ['bg-green-50', 'border-b', 'border-green-200'],
      warning: ['bg-yellow-50', 'border-b', 'border-yellow-200'],
      danger: ['bg-red-50', 'border-b', 'border-red-200'],
      minimal: ['border-b', 'border-gray-200']
    };

    if (this.bordered) {
      baseClasses.push(...variantClasses[this.variant]);
    }

    return baseClasses.join(' ');
  }

  getTabClasses(tab: TabbedContentTab, index: number): string {
    const baseClasses = [
      'tab-button',
      'relative',
      'flex',
      'items-center',
      'gap-2',
      'font-medium',
      'transition-all',
      'duration-200',
      'focus:outline-none',
      'focus:ring-2',
      'focus:ring-offset-2',
      'whitespace-nowrap'
    ];

    // Size classes
    const sizeClasses = {
      xs: ['text-xs', 'py-1', 'px-2'],
      sm: ['text-sm', 'py-2', 'px-3'],
      md: ['text-sm', 'py-3', 'px-4'],
      lg: ['text-base', 'py-3', 'px-6'],
      xl: ['text-lg', 'py-4', 'px-8']
    };

    // Variant classes for active/inactive states
    const variantClasses = {
      default: {
        active: ['text-blue-600', 'bg-white', 'border-b-2', 'border-blue-600', '-mb-px'],
        inactive: ['text-gray-600', 'hover:text-gray-900', 'hover:bg-gray-100', 'border-b-2', 'border-transparent']
      },
      primary: {
        active: ['text-blue-700', 'bg-white', 'border-b-2', 'border-blue-700', '-mb-px'],
        inactive: ['text-blue-600', 'hover:text-blue-700', 'hover:bg-blue-100', 'border-b-2', 'border-transparent']
      },
      secondary: {
        active: ['text-gray-700', 'bg-white', 'border-b-2', 'border-gray-700', '-mb-px'],
        inactive: ['text-gray-600', 'hover:text-gray-700', 'hover:bg-gray-100', 'border-b-2', 'border-transparent']
      },
      success: {
        active: ['text-green-700', 'bg-white', 'border-b-2', 'border-green-700', '-mb-px'],
        inactive: ['text-green-600', 'hover:text-green-700', 'hover:bg-green-100', 'border-b-2', 'border-transparent']
      },
      warning: {
        active: ['text-yellow-700', 'bg-white', 'border-b-2', 'border-yellow-700', '-mb-px'],
        inactive: ['text-yellow-600', 'hover:text-yellow-700', 'hover:bg-yellow-100', 'border-b-2', 'border-transparent']
      },
      danger: {
        active: ['text-red-700', 'bg-white', 'border-b-2', 'border-red-700', '-mb-px'],
        inactive: ['text-red-600', 'hover:text-red-700', 'hover:bg-red-100', 'border-b-2', 'border-transparent']
      },
      minimal: {
        active: ['text-gray-900', 'border-b-2', 'border-gray-900', '-mb-px'],
        inactive: ['text-gray-600', 'hover:text-gray-900', 'border-b-2', 'border-transparent']
      }
    };

    if (this.justified) {
      baseClasses.push('flex-1', 'justify-center');
    }

    const classes = [
      ...baseClasses,
      ...sizeClasses[this.size],
      ...variantClasses[this.variant][tab.active ? 'active' : 'inactive']
    ];

    // Disabled state
    if (tab.disabled) {
      classes.push('opacity-50', 'cursor-not-allowed', 'pointer-events-none');
    } else {
      classes.push('cursor-pointer');
    }

    // Focus ring color
    const focusRingClasses = {
      default: 'focus:ring-blue-500',
      primary: 'focus:ring-blue-500',
      secondary: 'focus:ring-gray-500',
      success: 'focus:ring-green-500',
      warning: 'focus:ring-yellow-500',
      danger: 'focus:ring-red-500',
      minimal: 'focus:ring-gray-500'
    };
    classes.push(focusRingClasses[this.variant]);

    return classes.join(' ');
  }

  get contentClasses(): string {
    const baseClasses = [
      'tab-content',
      'flex-1',
      'bg-white'
    ];

    if (this.layout === 'horizontal') {
      if (this.position === 'bottom') {
        baseClasses.push('order-1');
      }
    } else {
      if (this.position === 'right') {
        baseClasses.push('order-1');
      }
    }

    // Content padding
    const paddingClasses = {
      none: [],
      sm: ['p-2'],
      md: ['p-4'],
      lg: ['p-6'],
      xl: ['p-8']
    };
    baseClasses.push(...paddingClasses[this.contentPadding]);

    if (this.animated) {
      baseClasses.push('transition-all', 'duration-300', 'ease-in-out');
    }

    if (this.bordered) {
      const borderClasses = {
        default: ['border', 'border-gray-200'],
        primary: ['border', 'border-blue-200'],
        secondary: ['border', 'border-gray-300'],
        success: ['border', 'border-green-200'],
        warning: ['border', 'border-yellow-200'],
        danger: ['border', 'border-red-200'],
        minimal: ['border', 'border-gray-200']
      };
      baseClasses.push(...borderClasses[this.variant]);
    }

    return baseClasses.join(' ');
  }

  onTabClick(tab: TabbedContentTab): void {
    if (tab.disabled) {
      return;
    }

    // Update active states
    this.tabs.forEach(t => t.active = false);
    tab.active = true;

    this.tabClick.emit(tab);
    this.tabChange.emit(tab);
  }

  onTabKeyDown(event: KeyboardEvent, tab: TabbedContentTab): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.onTabClick(tab);
    }
  }

  onTabClose(event: Event, tab: TabbedContentTab): void {
    event.stopPropagation();
    this.tabClose.emit(tab);
  }

  onAddTab(): void {
    this.tabAdd.emit();
  }

  getActiveTab(): TabbedContentTab | undefined {
    return this.tabs.find(tab => tab.active);
  }

  trackByFn(index: number, tab: TabbedContentTab): any {
    return tab.id || index;
  }

  shouldShowContent(tab: TabbedContentTab): boolean {
    if (!this.showContent) return false;
    if (this.persistent) return true;
    return tab.active === true;
  }

  hasCustomContent(): boolean {
    // This would detect if there's projected content
    // In a real implementation, you might use ViewChild or ContentChild
    return false;
  }
}
