<div [class]="containerClasses">
  <!-- Tab List -->
  <div [class]="tabListClasses" role="tablist">
    <button
      *ngFor="let tab of tabs; trackBy: trackByFn; let i = index"
      [class]="getTabClasses(tab, i)"
      [attr.role]="'tab'"
      [attr.aria-selected]="tab.active"
      [attr.aria-controls]="'tabpanel-' + tab.id"
      [attr.id]="'tab-' + tab.id"
      [attr.tabindex]="tab.active ? 0 : -1"
      [disabled]="tab.disabled"
      (click)="onTabClick(tab)"
      (keydown)="onTabKeyDown($event, tab)"
    >
      <!-- Tab Icon -->
      <span *ngIf="tab.icon" class="tab-icon" [innerHTML]="tab.icon"></span>
      
      <!-- Tab Title -->
      <span class="tab-title">{{ tab.title }}</span>
      
      <!-- Tab Badge -->
      <span 
        *ngIf="tab.badge" 
        class="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-500 rounded-full"
      >
        {{ tab.badge }}
      </span>
      
      <!-- Close Button -->
      <button 
        *ngIf="closable" 
        class="ml-2 text-gray-400 hover:text-gray-600 focus:outline-none"
        (click)="onTabClose($event, tab)"
        [attr.aria-label]="'Close ' + tab.title + ' tab'"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </button>
    
    <!-- Add Tab Button -->
    <button 
      *ngIf="addable"
      class="flex items-center justify-center px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500"
      (click)="onAddTab()"
      aria-label="Add new tab"
    >
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
      </svg>
    </button>
  </div>

  <!-- Content Panels -->
  <div [class]="contentClasses" *ngIf="showContent">
    <div
      *ngFor="let tab of tabs; trackBy: trackByFn"
      [class]="'tab-panel ' + (animated ? 'transition-opacity duration-300' : '')"
      [class.hidden]="!shouldShowContent(tab)"
      [class.opacity-0]="animated && !tab.active"
      [class.opacity-100]="animated && tab.active"
      [attr.role]="'tabpanel'"
      [attr.id]="'tabpanel-' + tab.id"
      [attr.aria-labelledby]="'tab-' + tab.id"
      [attr.tabindex]="tab.active ? 0 : -1"
    >
      <!-- Default Content -->
      <div *ngIf="tab.content && !hasCustomContent()" [innerHTML]="tab.content"></div>
      
      <!-- Projected Content -->
      <ng-content></ng-content>
      
      <!-- Fallback Content -->
      <div *ngIf="!tab.content && !hasCustomContent()" class="text-gray-500 italic">
        No content available for {{ tab.title }}
      </div>
    </div>
  </div>
</div>
