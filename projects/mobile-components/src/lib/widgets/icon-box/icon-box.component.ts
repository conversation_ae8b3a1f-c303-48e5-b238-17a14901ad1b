import { Component, Input, computed } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'lib-icon-box',
  templateUrl: './icon-box.component.html',
  styleUrls: ['./icon-box.component.css'],
  standalone: true,
  imports: [CommonModule]
})
export class IconBoxComponent {
  @Input() icon: string = 'fa fa-star';
  @Input() title: string = 'Icon Box Title';
  @Input() description: string = 'This is an icon box description';
  @Input() showTitle: boolean = true;
  @Input() showDescription: boolean = true;
  @Input() showIcon: boolean = true;
  @Input() iconPosition: 'top' | 'left' | 'right' = 'top';
  @Input() layout: 'vertical' | 'horizontal' = 'vertical';
  
  // Legacy support for existing NUI inputs (with fallbacks to standard Tailwind)
  @Input() variant: 'solid' | 'outline' | 'pastel' | 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() item_id: string = Math.random().toString(36).substring(7);
  @Input() color: 
    | 'default'
    | 'default-contrast'
    | 'dark'
    | 'black'
    | 'light'
    | 'primary'
    | 'info'
    | 'success'
    | 'warning'
    | 'danger'
    | 'none' = 'primary';
  @Input() mask: 'hex' | 'hexed' | 'deca' | 'blob' | 'diamond' | 'none' = 'none';
  @Input() bordered: boolean = false;
  
  // Standard Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' = 'md';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  get computedClasses(): string {
    const baseClasses = [
      'icon-box-widget',
      'flex',
      'items-center',
      'justify-center',
      'transition-all',
      'duration-200'
    ];

    // Layout classes
    if (this.layout === 'vertical' || this.iconPosition === 'top') {
      baseClasses.push('flex-col', 'text-center');
    } else {
      baseClasses.push('flex-row');
      if (this.iconPosition === 'right') {
        baseClasses.push('flex-row-reverse');
      }
    }

    // Size classes
    const sizeClasses = {
      xs: ['p-2', 'min-h-[60px]'],
      sm: ['p-3', 'min-h-[80px]'],
      md: ['p-4', 'min-h-[100px]'],
      lg: ['p-6', 'min-h-[120px]'],
      xl: ['p-8', 'min-h-[140px]'],
      '2xl': ['p-10', 'min-h-[160px]']
    };

    // Icon size classes
    const iconSizeClasses = {
      xs: 'text-lg',
      sm: 'text-xl',
      md: 'text-2xl',
      lg: 'text-3xl',
      xl: 'text-4xl',
      '2xl': 'text-5xl'
    };

    // Variant and color classes
    const variantColorClasses = {
      solid: {
        default: ['bg-gray-100', 'text-gray-900'],
        primary: ['bg-blue-600', 'text-white'],
        secondary: ['bg-gray-600', 'text-white'],
        success: ['bg-green-600', 'text-white'],
        warning: ['bg-yellow-600', 'text-white'],
        danger: ['bg-red-600', 'text-white'],
        info: ['bg-blue-500', 'text-white'],
        dark: ['bg-gray-900', 'text-white'],
        light: ['bg-gray-50', 'text-gray-900']
      },
      outline: {
        default: ['border-2', 'border-gray-300', 'text-gray-700', 'bg-transparent'],
        primary: ['border-2', 'border-blue-600', 'text-blue-600', 'bg-transparent'],
        secondary: ['border-2', 'border-gray-600', 'text-gray-600', 'bg-transparent'],
        success: ['border-2', 'border-green-600', 'text-green-600', 'bg-transparent'],
        warning: ['border-2', 'border-yellow-600', 'text-yellow-600', 'bg-transparent'],
        danger: ['border-2', 'border-red-600', 'text-red-600', 'bg-transparent'],
        info: ['border-2', 'border-blue-500', 'text-blue-500', 'bg-transparent'],
        dark: ['border-2', 'border-gray-900', 'text-gray-900', 'bg-transparent'],
        light: ['border-2', 'border-gray-200', 'text-gray-600', 'bg-transparent']
      },
      pastel: {
        default: ['bg-gray-50', 'text-gray-700'],
        primary: ['bg-blue-50', 'text-blue-700'],
        secondary: ['bg-gray-100', 'text-gray-700'],
        success: ['bg-green-50', 'text-green-700'],
        warning: ['bg-yellow-50', 'text-yellow-700'],
        danger: ['bg-red-50', 'text-red-700'],
        info: ['bg-blue-50', 'text-blue-600'],
        dark: ['bg-gray-100', 'text-gray-800'],
        light: ['bg-white', 'text-gray-600']
      }
    };

    // Rounded classes
    const roundedClasses = {
      none: [],
      sm: ['rounded-sm'],
      md: ['rounded-md'],
      lg: ['rounded-lg'],
      full: ['rounded-full']
    };

    // Apply classes
    const normalizedVariant = ['solid', 'outline', 'pastel'].includes(this.variant) ? this.variant as 'solid' | 'outline' | 'pastel' : 'solid';
    
    // Map color to valid colors in variant
    const validColors = Object.keys(variantColorClasses[normalizedVariant]);
    const normalizedColor = validColors.includes(this.color) ? this.color : 'default';
    
    const classes = [
      ...baseClasses,
      ...sizeClasses[this.size],
      ...(variantColorClasses[normalizedVariant][normalizedColor as keyof typeof variantColorClasses.solid] || variantColorClasses[normalizedVariant]['default']),
      ...roundedClasses[this.rounded]
    ];

    if (this.bordered) {
      classes.push('border');
    }

    if (this.className) {
      classes.push(this.className);
    }

    return classes.join(' ');
  }

  get iconClasses(): string {
    const sizeClasses = {
      xs: 'text-lg',
      sm: 'text-xl',
      md: 'text-2xl',
      lg: 'text-3xl',
      xl: 'text-4xl',
      '2xl': 'text-5xl'
    };

    const spacingClasses = {
      top: this.layout === 'vertical' ? 'mb-2' : 'mb-2',
      left: 'mr-3',
      right: 'ml-3'
    };

    return `${sizeClasses[this.size]} ${spacingClasses[this.iconPosition]} flex-shrink-0`;
  }

  get titleClasses(): string {
    const sizeClasses = {
      xs: 'text-sm font-medium',
      sm: 'text-base font-medium',
      md: 'text-lg font-semibold',
      lg: 'text-xl font-semibold',
      xl: 'text-2xl font-bold',
      '2xl': 'text-3xl font-bold'
    };

    const spacingClasses = this.showDescription ? 'mb-1' : '';
    return `${sizeClasses[this.size]} ${spacingClasses}`;
  }

  get descriptionClasses(): string {
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-sm',
      lg: 'text-base',
      xl: 'text-lg',
      '2xl': 'text-xl'
    };

    return `${sizeClasses[this.size]} text-gray-600`;
  }

  // For backward compatibility with computed() usage
  classes = computed(() => this.computedClasses.split(' '));
}