<div [class]="computedClasses">
  <!-- Icon -->
  <div *ngIf="showIcon" [class]="iconClasses">
    <i [class]="icon" aria-hidden="true"></i>
  </div>

  <!-- Content -->
  <div class="flex-1">
    <!-- Title -->
    <h3 *ngIf="showTitle && title" [class]="titleClasses">
      {{ title }}
    </h3>

    <!-- Description -->
    <p *ngIf="showDescription && description" [class]="descriptionClasses">
      {{ description }}
    </p>

    <!-- Slot for additional content -->
    <ng-content></ng-content>
  </div>
</div>