<div [class]="containerClasses" 
     role="region" 
     [attr.aria-label]="'Filter controls for ' + filterId">

  <!-- Filter Header -->
  <div class="flex items-center justify-between mb-4">
    <h3 class="text-lg font-semibold text-gray-900">Filters</h3>
    <div class="flex items-center space-x-3">
      <!-- Selected Count -->
      <span *ngIf="showSelectedCount && totalSelectedCount > 0" 
            class="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded-full">
        {{ totalSelectedCount }} selected
      </span>
      
      <!-- Clear All Button -->
      <button *ngIf="showClearAll && totalSelectedCount > 0"
              (click)="clearAllFilters()"
              class="text-sm text-red-600 hover:text-red-800 hover:underline transition-colors duration-200">
        {{ clearAllText }}
      </button>
    </div>
  </div>

  <!-- Search Input -->
  <div *ngIf="showSearch" class="mb-6">
    <div class="relative">
      <input type="text"
             [placeholder]="searchPlaceholder"
             (input)="onSearchChange($any($event.target).value)"
             class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
        </svg>
      </div>
    </div>
  </div>

  <!-- Vertical Layout -->
  <div *ngIf="layout === 'vertical'" class="space-y-6">
    <div *ngFor="let group of filteredGroups$ | async; trackBy: trackByGroupId"
         class="border border-gray-200 rounded-lg">
      
      <!-- Group Header -->
      <div class="flex items-center justify-between p-4 bg-gray-50 rounded-t-lg border-b border-gray-200">
        <h4 class="font-medium text-gray-900">{{ group.title }}</h4>
        <div class="flex items-center space-x-2">
          <!-- Selected count for this group -->
          <span *ngIf="(internalSelectedValues[group.id] || []).length > 0" 
                class="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full">
            {{ (internalSelectedValues[group.id] || []).length }}
          </span>
          
          <!-- Clear group button -->
          <button *ngIf="(internalSelectedValues[group.id] || []).length > 0"
                  (click)="clearGroup(group.id)"
                  class="text-xs text-gray-500 hover:text-red-600 transition-colors duration-200">
            Clear
          </button>
          
          <!-- Toggle button -->
          <button (click)="toggleGroup(group)"
                  class="p-1 hover:bg-gray-200 rounded transition-colors duration-200">
            <svg class="w-4 h-4 text-gray-500 transition-transform duration-200"
                 [class.rotate-180]="isGroupCollapsed(group.id)"
                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- Group Options -->
      <div *ngIf="!isGroupCollapsed(group.id)" class="p-4">
        <div *ngIf="group.options.length === 0" class="text-sm text-gray-500 text-center py-4">
          {{ noResultsText }}
        </div>
        
        <div *ngIf="group.options.length > 0" class="space-y-3">
          <!-- Multiple selection (checkboxes) -->
          <div *ngIf="group.multiple" class="space-y-2">
            <label *ngFor="let option of group.options; trackBy: trackByOptionId"
                   [class]="option.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'"
                   class="flex items-center space-x-3 p-2 rounded-md hover:bg-gray-50 transition-colors duration-200">
              
              <input type="checkbox"
                     [disabled]="option.disabled"
                     [checked]="isOptionSelected(group.id, option.value)"
                     (change)="onOptionChange(group, option, $any($event.target).checked)"
                     class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
              
              <!-- Icon -->
              <div *ngIf="option.icon" class="text-lg">{{ option.icon }}</div>
              
              <!-- Image -->
              <img *ngIf="option.image" 
                   [src]="option.image" 
                   [alt]="option.label"
                   class="w-6 h-6 rounded object-cover">
              
              <!-- Label and Description -->
              <div class="flex-1">
                <div class="flex items-center space-x-2">
                  <span class="text-sm font-medium text-gray-900">{{ option.label }}</span>
                  <span *ngIf="option.badge" 
                        [class]="getBadgeClasses(option.badge)"
                        class="px-2 py-1 text-xs font-medium rounded-full">
                    {{ option.badge }}
                  </span>
                </div>
                <p *ngIf="option.description" class="text-xs text-gray-600 mt-1">
                  {{ option.description }}
                </p>
              </div>
            </label>
          </div>

          <!-- Single selection (radio buttons) -->
          <div *ngIf="!group.multiple" class="space-y-2">
            <label *ngFor="let option of group.options; trackBy: trackByOptionId"
                   [class]="option.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'"
                   class="flex items-center space-x-3 p-2 rounded-md hover:bg-gray-50 transition-colors duration-200">
              
              <input type="radio"
                     [name]="group.id"
                     [disabled]="option.disabled"
                     [checked]="isOptionSelected(group.id, option.value)"
                     (change)="onRadioChange(group, option)"
                     class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
              
              <!-- Icon -->
              <div *ngIf="option.icon" class="text-lg">{{ option.icon }}</div>
              
              <!-- Image -->
              <img *ngIf="option.image" 
                   [src]="option.image" 
                   [alt]="option.label"
                   class="w-6 h-6 rounded object-cover">
              
              <!-- Label and Description -->
              <div class="flex-1">
                <div class="flex items-center space-x-2">
                  <span class="text-sm font-medium text-gray-900">{{ option.label }}</span>
                  <span *ngIf="option.badge" 
                        [class]="getBadgeClasses(option.badge)"
                        class="px-2 py-1 text-xs font-medium rounded-full">
                    {{ option.badge }}
                  </span>
                </div>
                <p *ngIf="option.description" class="text-xs text-gray-600 mt-1">
                  {{ option.description }}
                </p>
              </div>
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Horizontal Layout -->
  <div *ngIf="layout === 'horizontal'" class="space-y-4">
    <div *ngFor="let group of filteredGroups$ | async; trackBy: trackByGroupId">
      <h4 class="font-medium text-gray-900 mb-3">{{ group.title }}</h4>
      
      <div *ngIf="group.options.length === 0" class="text-sm text-gray-500">
        {{ noResultsText }}
      </div>
      
      <div *ngIf="group.options.length > 0" class="flex flex-wrap gap-2">
        <!-- Multiple selection chips -->
        <label *ngFor="let option of group.options; trackBy: trackByOptionId"
               [class]="option.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'"
               class="inline-flex items-center">
          
          <input type="checkbox"
                 *ngIf="group.multiple"
                 [disabled]="option.disabled"
                 [checked]="isOptionSelected(group.id, option.value)"
                 (change)="onOptionChange(group, option, $any($event.target).checked)"
                 class="sr-only">
          
          <input type="radio"
                 *ngIf="!group.multiple"
                 [name]="group.id"
                 [disabled]="option.disabled"
                 [checked]="isOptionSelected(group.id, option.value)"
                 (change)="onRadioChange(group, option)"
                 class="sr-only">
          
          <span [class]="isOptionSelected(group.id, option.value) ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
                class="inline-flex items-center space-x-2 px-3 py-2 rounded-full text-sm font-medium transition-colors duration-200">
            <span *ngIf="option.icon">{{ option.icon }}</span>
            <img *ngIf="option.image" 
                 [src]="option.image" 
                 [alt]="option.label"
                 class="w-4 h-4 rounded object-cover">
            <span>{{ option.label }}</span>
            <span *ngIf="option.badge" 
                  class="px-1 py-0.5 text-xs bg-white bg-opacity-20 rounded">
              {{ option.badge }}
            </span>
          </span>
        </label>
      </div>
    </div>
  </div>

  <!-- Dropdown Layout -->
  <div *ngIf="layout === 'dropdown'" class="space-y-4">
    <div *ngFor="let group of filteredGroups$ | async; trackBy: trackByGroupId">
      <label class="block text-sm font-medium text-gray-900 mb-2">{{ group.title }}</label>
      
      <select *ngIf="!group.multiple"
              (change)="onDropdownRadioChange(group, $event)"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
        <option value="">Select {{ group.title }}</option>
        <option *ngFor="let option of group.options; trackBy: trackByOptionId"
                [value]="option.value"
                [selected]="isOptionSelected(group.id, option.value)"
                [disabled]="option.disabled">
          {{ option.label }}{{ option.badge ? ' (' + option.badge + ')' : '' }}
        </option>
      </select>
      
      <div *ngIf="group.multiple" class="relative">
        <select multiple
                (change)="onDropdownMultipleChange(group, $event)"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                size="4">
          <option *ngFor="let option of group.options; trackBy: trackByOptionId"
                  [value]="option.value"
                  [selected]="isOptionSelected(group.id, option.value)"
                  [disabled]="option.disabled">
            {{ option.label }}{{ option.badge ? ' (' + option.badge + ')' : '' }}
          </option>
        </select>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="(filteredGroups$ | async)?.length === 0" class="text-center py-8">
    <div class="text-gray-400 text-4xl mb-4">🔍</div>
    <h3 class="text-lg font-medium text-gray-900 mb-2">No filters found</h3>
    <p class="text-gray-600">Try adjusting your search terms.</p>
  </div>
</div>
