import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnChanges,
  SimpleChanges
} from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { debounceTime, distinctUntilChanged, map } from 'rxjs/operators';
import { CommonModule, AsyncPipe } from '@angular/common';
import { FormsModule } from '@angular/forms';

export interface BaseFilterOption {
  id: string | number;
  label: string;
  value: any;
  description?: string;
  icon?: string;
  image?: string;
  badge?: string;
  disabled?: boolean;
  category?: string;
}

export interface BaseFilterGroup {
  id: string;
  title: string;
  options: BaseFilterOption[];
  multiple?: boolean;
  searchable?: boolean;
  collapsed?: boolean;
}

export interface BaseFilterChangeEvent {
  filterId: string;
  selectedOptions: BaseFilterOption[];
  selectedValues: any[];
}

@Component({
  selector: 'base-filter',
  templateUrl: './filter.component.html',
  styleUrls: ['./filter.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AsyncPipe
  ]
})
export class FilterComponent implements OnInit, OnChanges {
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  
  // Component-specific properties
  @Input() filterId: string = 'filter-' + Math.random().toString(36).substring(7);
  @Input() filterGroups: BaseFilterGroup[] = [
    {
      id: 'category',
      title: 'Category',
      searchable: true,
      multiple: true,
      options: [
        { id: 'tech', label: 'Technology', value: 'technology', icon: '💻' },
        { id: 'design', label: 'Design', value: 'design', icon: '🎨' },
        { id: 'marketing', label: 'Marketing', value: 'marketing', icon: '📈' },
        { id: 'sales', label: 'Sales', value: 'sales', icon: '💰' }
      ]
    },
    {
      id: 'status',
      title: 'Status',
      multiple: false,
      options: [
        { id: 'active', label: 'Active', value: 'active', badge: 'success' },
        { id: 'inactive', label: 'Inactive', value: 'inactive', badge: 'warning' },
        { id: 'pending', label: 'Pending', value: 'pending', badge: 'info' }
      ]
    },
    {
      id: 'priority',
      title: 'Priority',
      multiple: true,
      collapsed: false,
      options: [
        { id: 'high', label: 'High Priority', value: 'high', badge: 'danger' },
        { id: 'medium', label: 'Medium Priority', value: 'medium', badge: 'warning' },
        { id: 'low', label: 'Low Priority', value: 'low', badge: 'success' }
      ]
    }
  ];
  @Input() layout: 'vertical' | 'horizontal' | 'dropdown' = 'vertical';
  @Input() showSearch: boolean = true;
  @Input() showClearAll: boolean = true;
  @Input() showSelectedCount: boolean = true;
  @Input() searchPlaceholder: string = 'Search filters...';
  @Input() clearAllText: string = 'Clear All';
  @Input() noResultsText: string = 'No options found';
  @Input() selectedValues: { [groupId: string]: any[] } = {};

  // Events
  @Output() filterChange = new EventEmitter<BaseFilterChangeEvent>();
  @Output() searchChange = new EventEmitter<string>();
  @Output() clearAll = new EventEmitter<void>();
  @Output() groupToggle = new EventEmitter<{ groupId: string; collapsed: boolean }>();

  searchTerm$ = new BehaviorSubject<string>('');
  collapsedGroups: Set<string> = new Set();
  internalSelectedValues: { [groupId: string]: any[] } = {};

  get containerClasses(): string {
    const sizeClasses = {
      xs: 'p-2 text-xs',
      sm: 'p-3 text-sm',
      md: 'p-4 text-base',
      lg: 'p-5 text-lg',
      xl: 'p-6 text-xl'
    };

    const variantClasses = {
      default: 'bg-white border border-gray-200',
      primary: 'bg-blue-50 border border-blue-200',
      secondary: 'bg-gray-50 border border-gray-200',
      success: 'bg-green-50 border border-green-200',
      warning: 'bg-yellow-50 border border-yellow-200',
      danger: 'bg-red-50 border border-red-200'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-xl'
    };

    return `
      ${sizeClasses[this.size]}
      ${variantClasses[this.variant]}
      ${roundedClasses[this.rounded]}
      ${this.className}
    `.trim().replace(/\s+/g, ' ');
  }

  get totalSelectedCount(): number {
    return Object.values(this.internalSelectedValues).reduce((sum, values) => sum + values.length, 0);
  }

  get filteredGroups$(): Observable<BaseFilterGroup[]> {
    return this.searchTerm$.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      map(searchTerm => {
        if (!searchTerm) return this.filterGroups;
        
        return this.filterGroups.map(group => ({
          ...group,
          options: group.options.filter(option =>
            option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
            option.description?.toLowerCase().includes(searchTerm.toLowerCase())
          )
        })).filter(group => group.options.length > 0);
      })
    );
  }

  ngOnInit(): void {
    // Initialize internal selected values
    this.internalSelectedValues = { ...this.selectedValues };
    
    // Initialize collapsed groups
    this.filterGroups.forEach(group => {
      if (group.collapsed) {
        this.collapsedGroups.add(group.id);
      }
    });

    // Set up search change emission
    this.searchTerm$.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(searchTerm => {
      this.searchChange.emit(searchTerm);
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedValues'] && changes['selectedValues'].currentValue) {
      this.internalSelectedValues = { ...this.selectedValues };
    }
  }

  onOptionChange(group: BaseFilterGroup, option: BaseFilterOption, checked: boolean): void {
    if (!this.internalSelectedValues[group.id]) {
      this.internalSelectedValues[group.id] = [];
    }

    if (group.multiple) {
      if (checked) {
        if (!this.internalSelectedValues[group.id].includes(option.value)) {
          this.internalSelectedValues[group.id].push(option.value);
        }
      } else {
        this.internalSelectedValues[group.id] = this.internalSelectedValues[group.id].filter(
          value => value !== option.value
        );
      }
    } else {
      this.internalSelectedValues[group.id] = checked ? [option.value] : [];
    }

    this.emitFilterChange(group.id);
  }

  onRadioChange(group: BaseFilterGroup, option: BaseFilterOption): void {
    this.internalSelectedValues[group.id] = [option.value];
    this.emitFilterChange(group.id);
  }

  // Helper methods for dropdown change events
  onDropdownRadioChange(group: BaseFilterGroup, event: Event): void {
    const selectElement = event.target as HTMLSelectElement;
    const selectedValue = selectElement.value;
    if (selectedValue) {
      const option = group.options.find(o => o.value === selectedValue);
      if (option) {
        this.onRadioChange(group, option);
      }
    }
  }

  onDropdownMultipleChange(group: BaseFilterGroup, event: Event): void {
    const selectElement = event.target as HTMLSelectElement;
    const selectedOptions = Array.from(selectElement.selectedOptions);
    
    // Clear existing selections for this group
    this.internalSelectedValues[group.id] = [];
    
    // Add all selected values
    selectedOptions.forEach(option => {
      const value = option.value;
      if (value) {
        const filterOption = group.options.find(o => o.value === value);
        if (filterOption) {
          this.internalSelectedValues[group.id].push(filterOption.value);
        }
      }
    });
    
    this.emitFilterChange(group.id);
  }

  isOptionSelected(groupId: string, optionValue: any): boolean {
    return this.internalSelectedValues[groupId]?.includes(optionValue) || false;
  }

  toggleGroup(group: BaseFilterGroup): void {
    if (this.collapsedGroups.has(group.id)) {
      this.collapsedGroups.delete(group.id);
    } else {
      this.collapsedGroups.add(group.id);
    }
    
    this.groupToggle.emit({
      groupId: group.id,
      collapsed: this.collapsedGroups.has(group.id)
    });
  }

  isGroupCollapsed(groupId: string): boolean {
    return this.collapsedGroups.has(groupId);
  }

  clearAllFilters(): void {
    this.internalSelectedValues = {};
    this.filterGroups.forEach(group => {
      this.emitFilterChange(group.id);
    });
    this.clearAll.emit();
  }

  clearGroup(groupId: string): void {
    this.internalSelectedValues[groupId] = [];
    this.emitFilterChange(groupId);
  }

  onSearchChange(searchTerm: string): void {
    this.searchTerm$.next(searchTerm);
  }

  getBadgeClasses(badge?: string): string {
    const badgeClasses = {
      success: 'bg-green-100 text-green-800',
      warning: 'bg-yellow-100 text-yellow-800',
      danger: 'bg-red-100 text-red-800',
      info: 'bg-blue-100 text-blue-800',
      primary: 'bg-blue-100 text-blue-800',
      secondary: 'bg-gray-100 text-gray-800'
    };
    return badgeClasses[badge as keyof typeof badgeClasses] || 'bg-gray-100 text-gray-800';
  }

  private emitFilterChange(groupId: string): void {
    const group = this.filterGroups.find(g => g.id === groupId);
    if (!group) return;

    const selectedValues = this.internalSelectedValues[groupId] || [];
    const selectedOptions = group.options.filter(option => 
      selectedValues.includes(option.value)
    );

    this.filterChange.emit({
      filterId: groupId,
      selectedOptions,
      selectedValues
    });
  }

  trackByGroupId(index: number, group: BaseFilterGroup): string {
    return group.id;
  }

  trackByOptionId(index: number, option: BaseFilterOption): string | number {
    return option.id;
  }
}
