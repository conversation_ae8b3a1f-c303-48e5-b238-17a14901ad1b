import {
  Component,
  Input,
  ViewChild,
  ElementRef,
  forwardRef,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA
} from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor, FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'base-radio-headless',
  templateUrl: './radio-headless.component.html',
  styleUrls: ['./radio-headless.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => RadioHeadlessComponent),
      multi: true,
    },
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class RadioHeadlessComponent<T = any> implements ControlValueAccessor {
  @Input() value?: T;
  @Input() item_id: string = Math.random().toString(36).substring(7);
  @Input() id?: string;
  @Input() name?: string;
  @Input() label?: string;

  @ViewChild('inputRef') inputRef!: ElementRef<HTMLInputElement>;

  modelValue: T | undefined;
  onChange: any = () => {};
  onTouched: any = () => {};

  writeValue(value: T): void {
    this.modelValue = value;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    if (this.inputRef && this.inputRef.nativeElement) {
      this.inputRef.nativeElement.disabled = isDisabled;
    }
  }
}
