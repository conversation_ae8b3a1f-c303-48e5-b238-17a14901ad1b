import { ComponentFixture, TestBed } from '@angular/core/testing';

import { RadioHeadlessComponent } from './radio-headless.component';

describe('RadioHeadlessComponent', () => {
  let component: RadioHeadlessComponent;
  let fixture: ComponentFixture<RadioHeadlessComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [RadioHeadlessComponent]
    });
    fixture = TestBed.createComponent(RadioHeadlessComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
