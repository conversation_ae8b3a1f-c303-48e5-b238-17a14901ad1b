<div class="relative group/nui-radio-headless">
  <label
    *ngIf="label"
    [for]="id"
    class="
      inline-block
      mb-1
      font-sans
      text-sm
      cursor-pointer
      select-none
      text-muted-400
    "
  >
    {{ label }}
  </label>
  <div class="relative">
    <input
      [id]="id"
      #inputRef
      [(ngModel)]="modelValue"
      type="radio"
      [value]="value"
      [name]="name || ''"
      class="absolute inset-0 z-20 opacity-0 cursor-pointer peer size-full"
    />
    <ng-content
      [ngTemplateOutlet]="contentTemplate"
      [ngTemplateOutletContext]="{ value: modelValue }"
    ></ng-content>
  </div>
</div>
