import {
  Component,
  Input,
  ViewChild,
  ElementRef,
  Output,
  EventEmitter,
  Inject,
  forwardRef,
  Provider,
} from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';

@Component({
  selector: 'base-input-file-headless',
  templateUrl: './input-file-headless.component.html',
  styleUrls: ['./input-file-headless.component.css'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => InputFileHeadlessComponent),
      multi: true,
    } as Provider,
  ],
  standalone: true,
})
export class InputFileHeadlessComponent implements ControlValueAccessor {
  @Input() id?: string;
  @Input() item_id: string = Math.random().toString(36).substring(7);
  @Input() multiple: boolean = false;
  @Input() filterFileDropped: (file: File) => boolean = () => true;

  @ViewChild('inputRef') inputRef!: ElementRef<HTMLInputElement>;
  @Output() modelChange = new EventEmitter<FileList | null>();

  modelValue: FileList | null = null;
  previewMap = new WeakMap<File, string | undefined>();

  open() {
    this.inputRef.nativeElement?.click();
  }

  drop(event: DragEvent) {
    event.stopPropagation();
    event.preventDefault();

    const dt = event.dataTransfer;
    const filtered = new DataTransfer();
    if (this.inputRef.nativeElement && dt) {
      for (const file of Array.from(dt.files)) {
        if (this.filterFileDropped(file)) {
          filtered.items.add(file);
        }
      }
      this.inputRef.nativeElement.files = filtered.files;
      this.onChange(this.inputRef.nativeElement.files);
    }
  }

  remove(file?: File) {
    if (!file || !this.modelValue || !this.inputRef.nativeElement) return;

    const filtered = new DataTransfer();

    if (this.previewMap.has(file)) {
      this.previewMap.delete(file);
    }

    if (this.modelValue) {
      Array.from(this.modelValue).forEach((f) => {
        if (f !== file) {
          filtered.items.add(f);
        }
      });
    }

    this.inputRef.nativeElement.files = filtered.files;
    this.onChange(this.inputRef.nativeElement.files);
  }

  handleFileChange(event: Event) {
    const newFiles = (event.target as HTMLInputElement).files;
    if (!newFiles) return;

    if (this.multiple && this.modelValue) {
      const existingFiles = Array.from(this.modelValue);
      const updatedFiles = new DataTransfer();

      existingFiles.forEach((file) => updatedFiles.items.add(file));

      Array.from(newFiles).forEach((newFile) => {
        if (
          !existingFiles.some(
            (existingFile) => existingFile.name === newFile.name
          )
        ) {
          updatedFiles.items.add(newFile);
        }
      });

      if (!this.inputRef.nativeElement) return;

      this.inputRef.nativeElement.files = updatedFiles.files;
      this.onChange(updatedFiles.files);
    } else {
      this.onChange(newFiles);
    }
  }

  // ControlValueAccessor methods
  onChange: any = () => {};
  onTouched: any = () => {};

  writeValue(value: any): void {
    this.modelValue = value;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    if (this.inputRef.nativeElement) {
      this.inputRef.nativeElement.disabled = isDisabled;
    }
  }
}
