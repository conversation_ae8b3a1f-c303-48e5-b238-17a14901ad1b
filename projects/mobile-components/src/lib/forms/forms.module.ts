import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { FormsModule as AngularFormsModule } from '@angular/forms'; // Alias to avoid name clash

// Import ALL form components
// Standalone (9)
import { RadioHeadlessComponent } from './radio-headless/radio-headless.component';
import { CheckboxHeadlessComponent } from './checkbox-headless/checkbox-headless.component';
import { InputFilePathComponent } from './input-file-path/input-file-path.component';
import { InputFileComponent } from './input-file/input-file.component';
import { InputNumberComponent } from './input-number/input-number.component';
import { OtpValidatorComponent } from './otp/otp-validator.component';
import { AddressComponent } from './address/address.component';
import { SignupComponent } from './signup/signup.component';
import { ValidateComponent } from './validate/validate.component';
import { CountriesSelectComponent } from './countries-select/countries-select.component';
import { IndustrySelectComponent } from './industry-select/industry-select.component';
import { InputFileHeadlessComponent } from './input-file-headless/input-file-headless.component';
import { OtpComponent } from './otp/otp.component';

@NgModule({
  declarations: [
    // Non-Standalone Components (4)
  ],
  imports: [
    CommonModule,
    IonicModule,
    AngularFormsModule, // Import Angular's FormsModule for template-driven/reactive forms features
    // Standalone Components (9)
    InputFileHeadlessComponent,
    IndustrySelectComponent,
    OtpComponent,
    CountriesSelectComponent,
    RadioHeadlessComponent,
    CheckboxHeadlessComponent,
    InputFilePathComponent,
    InputFileComponent,
    InputNumberComponent,
    OtpValidatorComponent,
    AddressComponent,
    SignupComponent,
    ValidateComponent,
  ],
  exports: [
    // Non-Standalone Components (4)
    CountriesSelectComponent,
    IndustrySelectComponent,
    InputFileHeadlessComponent,
    OtpComponent,
    // Standalone Components (9)
    RadioHeadlessComponent,
    CheckboxHeadlessComponent,
    InputFilePathComponent,
    InputFileComponent,
    InputNumberComponent,
    OtpValidatorComponent,
    AddressComponent,
    SignupComponent,
    ValidateComponent,
  ]
})
export class FormsModule { } // Renamed to avoid conflict with Angular's FormsModule if imported directly
