<div
  class="nui-input-number-wrapper"
  [ngClass]="[
    contrast && contrasts[contrast],
    size && sizes[size],
    rounded && radiuses[rounded],
    error && !loading ? 'nui-input-number-error' : '',
    loading ? 'nui-input-number-loading' : '',
    labelFloat ? 'nui-input-number-label-float' : '',
    icon ? 'nui-has-icon' : '',
    colorFocus ? 'nui-input-number-focus' : '',
    classes.wrapper
  ]"
>
  <label
    *ngIf="label && !labelFloat"
    class="nui-input-number-label"
    [for]="id"
    [ngClass]="classes.label"
  >
    <ng-container *ngIf="labelTemplate; else defaultLabel">
      <ng-content select="[label]"></ng-content>
    </ng-container>
    <ng-template #defaultLabel>{{ label }}</ng-template>
  </label>
  <div class="nui-input-number-outer" [ngClass]="classes.outer">
    <div>
      <input
        [id]="id"
        #inputRef
        [(ngModel)]="modelValue"
        [type]="type"
        class="nui-input-number"
        [ngClass]="classes.input"
        [placeholder]="placeholder"
        [attr.inputmode]="inputmode ? inputmode : defaultInputmode"
        [disabled]="disabled || false"
      />
      <label
        *ngIf="label && labelFloat"
        class="nui-label-float"
        [for]="id"
        [ngClass]="classes.label"
      >
        <ng-container *ngIf="labelTemplate; else defaultLabel">
          <ng-content select="[label]"></ng-content>
        </ng-container>
        <ng-template #defaultLabel>{{ label }}</ng-template>
      </label>
      <div *ngIf="loading" class="nui-input-number-placeload">
        <base-placeload class="nui-placeload"></base-placeload>
      </div>
      <div *ngIf="icon" class="nui-input-number-icon" [ngClass]="classes.icon">
        <ng-content select="[icon]"></ng-content>
      </div>
      <div class="nui-input-number-buttons" [ngClass]="classes.buttons">
        <button
          type="button"
          aria-label="Decrement"
          [disabled]="disabled"
          (pointerdown)="startDecrement()"
          (pointerout)="stopDecrement()"
          (pointerup)="stopDecrement()"
        >
          <base-icon [icon]="iconDecrement"></base-icon>
        </button>
        <button
          type="button"
          aria-label="Increment"
          [disabled]="disabled"
          (pointerdown)="startIncrement()"
          (pointerout)="stopIncrement()"
          (pointerup)="stopIncrement()"
        >
          <base-icon [icon]="iconIncrement"></base-icon>
        </button>
      </div>
    </div>
  </div>
  <span
    *ngIf="error && isErrorString()"
    [ngClass]="classes.error"
    class="nui-input-number-error-text"
  >
    {{ error }}
  </span>
</div>
