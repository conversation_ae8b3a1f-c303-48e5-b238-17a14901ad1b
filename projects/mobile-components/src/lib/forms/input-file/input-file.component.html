<div
  class="nui-input-file-regular"
  [ngClass]="[
    contrast && contrasts[contrast],
    size && sizes[size],
    rounded && radiuses[rounded],
    error && !loading ? 'nui-input-file-error' : '',
    loading ? 'nui-input-file-loading' : '',
    icon ? 'nui-has-icon' : '',
    colorFocus ? 'nui-input-file-color-focus' : '',
    classes?.wrapper
  ]"
>
  <label
    *ngIf="label"
    class="nui-input-file-label"
    [for]="id"
    [ngClass]="classes?.label"
  >
    {{ label }}
  </label>
  <div class="nui-input-file-outer">
    <label
      tabindex="0"
      class="nui-input-file-inner"
      [for]="id"
      [ngClass]="classes?.input"
    >
      <div class="nui-input-file-addon" [ngClass]="classes?.text">
        <span class="text-xs">{{ placeholder }}</span>
        <base-icon
          *ngIf="icon"
          [icon]="icon"
          [ngClass]="classes?.icon"
        ></base-icon>
      </div>

      <div class="nui-input-file-text">
        {{ modelValue && textValue ? textValue(modelValue) : "" }}
      </div>
      <input
        [id]="id"
        #inputRef
        type="file"
        class="hidden"
        (change)="onFileChange($event)"
      />
    </label>

    <div *ngIf="loading" class="nui-input-file-placeload">
      <base-placeload class="nui-placeload"></base-placeload>
    </div>
    <span
      *ngIf="error && isErrorString()"
      class="nui-input-file-error-text"
      [ngClass]="classes?.error"
    >
      {{ error }}
    </span>
  </div>
</div>
