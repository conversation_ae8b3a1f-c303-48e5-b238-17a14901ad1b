import {
  Component,
  Input,
  ViewChild,
  ElementRef,
  ContentChild,
  TemplateRef,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA
} from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor, FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'base-checkbox-headless',
  templateUrl: './checkbox-headless.component.html',
  styleUrls: ['./checkbox-headless.component.css'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: CheckboxHeadlessComponent,
      multi: true,
    },
  ],
  standalone: true,
  imports: [CommonModule, FormsModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class CheckboxHeadlessComponent<T> implements ControlValueAccessor {
  @Input() value?: T;
  @Input() item_id: string = Math.random().toString(36).substring(7);
  @Input() trueValue: T = true as any;
  @Input() falseValue: T = false as any;
  @Input() id?: string;
  @Input() label?: string;

  @ViewChild('inputRef') inputRef!: ElementRef<HTMLInputElement>;
  @ContentChild(TemplateRef) contentTemplate!: TemplateRef<{
    value: T | T[] | undefined;
  }>;

  modelValue: T | T[] | undefined;

  onChange: any = () => {};
  onTouched: any = () => {};

  writeValue(value: T | T[]): void {
    this.modelValue = value;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    if (this.inputRef) {
      this.inputRef.nativeElement.disabled = isDisabled;
    }
  }

  get el(): HTMLInputElement | undefined {
    return this.inputRef?.nativeElement;
  }
}
