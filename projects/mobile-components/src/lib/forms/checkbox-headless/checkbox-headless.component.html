<div class="group/nui-checkbox-headless relative">
  <label
    *ngIf="label"
    [for]="id"
    class="
      text-muted-400
      mb-1
      inline-block
      cursor-pointer
      select-none
      font-sans
      text-sm
    "
  >
    {{ label }}
  </label>
  <div class="relative">
    <input
      [id]="id"
      #inputRef
      [(ngModel)]="modelValue"
      [value]="value"
      [attr.true-value]="trueValue"
      [attr.false-value]="falseValue"
      class="peer absolute inset-0 z-20 size-full cursor-pointer opacity-0"
      type="checkbox"
    />
    <ng-content
      [ngTemplateOutlet]="contentTemplate"
      [ngTemplateOutletContext]="{ value: modelValue }"
    ></ng-content>
  </div>
</div>
