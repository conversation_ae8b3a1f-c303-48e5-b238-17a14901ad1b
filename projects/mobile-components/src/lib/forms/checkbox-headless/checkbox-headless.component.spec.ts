import { ComponentFixture, TestBed } from '@angular/core/testing';

import { CheckboxHeadlessComponent } from './checkbox-headless.component';

describe('CheckboxHeadlessComponent', () => {
  let component: CheckboxHeadlessComponent;
  let fixture: ComponentFixture<CheckboxHeadlessComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [CheckboxHeadlessComponent]
    });
    fixture = TestBed.createComponent(CheckboxHeadlessComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
