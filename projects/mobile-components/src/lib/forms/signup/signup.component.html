<ion-header [translucent]="true" class="p-0 z-50">
  <ion-toolbar class="header-backgrounds">
    <ion-buttons slot="start">
      <ion-button class="" [routerLink]="['/']">
        <ion-icon slot="icon-only" name="arrow-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title> Sign Up </ion-title>
    <ion-buttons slot="end"> </ion-buttons>
  </ion-toolbar>
</ion-header>
  <div class="absolute inset-0 z-0 h-screen bg-blue-200 animated-bg"></div>

<ion-content class="app-background overflow-y-auto absolute inset-0 z-10 pb-24 pt-10 space-y-2">
  <!-- <lib-head-logo
    type="signup"
    [src]="lssConfig.icon"
  /> -->
  <div class="logo-container">
    <img
      class="logo"
      [src]="lssConfig.icon"
      alt=""
    />
  </div>

  <ion-card>
    <ion-card-content>
      <div *ngIf="_formState === _formStateType.create">
        <form [formGroup]="_form">
          <ion-item>
            <!-- <ion-label position="floating">* Last Name:</ion-label> -->
            <ion-icon slot="start" name="id-card-outline"></ion-icon>
            <ion-input
              labelPlacement="floating"
              label="Pre Printed Card"
              type="text"
              formControlName="membershipNumber"
            ></ion-input>
          </ion-item>
          <ion-item
            lines="none"
            *ngIf="isFormComponentInvalid('membershipNumber')"
          >
            <div
              *ngFor="
                let error of getComponentErrors('membershipNumber');
                let i = index
              "
              class="validator-error w-full"
            >
              <div *ngIf="i == 0" class="w-full">
                Card number must be 16 numeric characters long.
              </div>
              <!-- {{ error }} -->
            </div>
          </ion-item>
          <ion-item>
            <ion-icon slot="start" name="ribbon-outline"></ion-icon>

            <ion-select
              label="Title"
              labelPlacement="floating"
              formControlName="title"
              placeholder="Please select.."
            >
              <ion-select-option
                *ngFor="let codeItem of getCodeList('TITL') | async"
                [value]="codeItem.codeId"
                >{{ codeItem.description }}</ion-select-option
              >
            </ion-select>
          </ion-item>
          <ion-item>
            <ion-icon slot="start" name="person"></ion-icon>
            <ion-input
              labelPlacement="floating"
              label="* First Name"
              type="text"
              formControlName="givenNames"
            ></ion-input>
          </ion-item>
          <ion-item lines="none" *ngIf="isFormComponentInvalid('givenNames')">
            <div
              *ngFor="let error of getComponentErrors('givenNames')"
              class="validator-error"
            >
              {{ error }}
            </div>
          </ion-item>
          <ion-item>
            <ion-icon slot="start" name="person"></ion-icon>
            <ion-input
              labelPlacement="floating"
              label="* Surname"
              type="text"
              formControlName="surname"
            ></ion-input>
          </ion-item>
          <ion-item lines="none" *ngIf="isFormComponentInvalid('surname')">
            <div
              *ngFor="let error of getComponentErrors('surname')"
              class="validator-error"
            >
              {{ error }}
            </div>
          </ion-item>

          <ion-item>
            <!-- <ion-label position="floating">* Last Name:</ion-label> -->
            <ion-icon slot="start" name="id-card-outline"></ion-icon>
            <ion-input
              labelPlacement="floating"
              label="South African ID number"
              type="text"
              formControlName="nationalIdNum"
            ></ion-input>
          </ion-item>
          <ion-item
            lines="none"
            *ngIf="isFormComponentInvalid('nationalIdNum')"
          >
            <div
              *ngFor="
                let error of getComponentErrors('nationalIdNum')?.splice(0, 1);
                let i = index
              "
              class="validator-error w-full"
            >
              <div *ngIf="i == 0" class="w-full">
                Id number must be 13 numeric characters long.
              </div>
              <!-- {{ error }} -->
            </div>
          </ion-item>
          <!-- <ion-item button="true" id="open-date-input"> -->
          <!-- <ion-icon slot="start" name="calendar-outline"></ion-icon> -->
          <!-- <ion-input
              labelPlacement="floating"
              label="* Birth Date"
              type="text"
              formControlName="birthDate"
            ></ion-input> -->
          <!-- <ion-label floating>* Birth Date</ion-label>
            <ion-text>{{ hasDate }}</ion-text> -->
          <ion-item id="open-date-input" lines="inset">
            <ion-icon slot="start" name="calendar-outline"></ion-icon>
            <ion-input
              label="Birth Date"
              labelPlacement="floating"
              formControlName="birthDate"
              type="date"
              [max]="todaysDate12YearsAgo()"
            ></ion-input>
          </ion-item>
          <!-- <ion-input
              label="* Birth Date"
              labelPlacement="floating"
              value="{{ hasDate }}"
            ></ion-input>
            <ion-modal trigger="open-date-input">
              <ng-template>
                <ion-content>
                  <ion-datetime
                    presentation="date"
                    formControlName="birthDate"
                    displayFormat="DD.MM.YYYY"
                  >
                    <span slot="title">Birth Date</span>
                  </ion-datetime>
                </ion-content>
              </ng-template>
            </ion-modal> -->
          <!-- <ion-modal trigger="open-date-input" show-backdrop="false">
              <ng-template>
                <ion-header>
                  <ion-toolbar>
                    <ion-title>Enter your Birthdate</ion-title>
                    <ion-buttons slot="end">
                      <ion-button>Close</ion-button>
                    </ion-buttons>
                  </ion-toolbar>
                </ion-header>
                <ion-content class="ion-padding formbackground center">
                  <ion-datetime
                    presentation="date"
                    formControlName="birthDate"
                    displayFormat="DD.MM.YYYY"
                  >
                    <span slot="title">Birth Date</span>
                  </ion-datetime>
                </ion-content>
              </ng-template>
            </ion-modal> -->
          <!-- </ion-item> -->
          <ion-item lines="none" *ngIf="isFormComponentInvalid('birthDate')">
            <div
              *ngFor="let error of getComponentErrors('birthDate')"
              class="validator-error"
            >
              {{ error }}
            </div>
          </ion-item>
          <ion-item>
            <ion-icon slot="start" name="balloon-outline"></ion-icon>

            <ion-select
              label="Gender"
              labelPlacement="floating"
              formControlName="gender"
              placeholder="Please select.."
            >
              <ion-select-option
                *ngFor="let codeItem of getCodeList('SEX') | async"
                [value]="codeItem.codeId"
                >{{ codeItem.description }}</ion-select-option
              >
            </ion-select>
          </ion-item>
          <ion-item lines="inset" class="ion-intl-tel item-has-value" [readonly]="true">
            <ion-icon slot="start" name="phone-portrait-outline"></ion-icon>

            <ion-input
            labelPlacement="floating"
            label="* Mobile Number:"
            type="text"
            [value]="phoneTogether"
          ></ion-input>

            <!-- <ion-label position="floating">* Mobile Number:</ion-label>
            <ion-intl-tel-input
              id="phone-number"
              name="phone-number"
              [minLength]="minLength"
              formControlName="phone"
              labelPlacement="floating"
              label="* Mobile Number:"
              [enableAutoCountrySelect]="true"
              modalTitle="Select Country / Region"
              [disabled]="true"
              [selectFirstCountry]="
                lssConfig.telephone.selectFirstCountry
              "
              [preferredCountries]="
                lssConfig.telephone.preferredCountries
              "
              [onlyCountries]="
              lssConfig.telephone.onlyCountries
            "
            >
            </ion-intl-tel-input> -->
          </ion-item>
          <ion-item lines="none" *ngIf="isFormComponentInvalid('phone')">
            <div
              *ngFor="let error of getComponentErrors('phone')"
              class="validator-error"
            >
              Please enter a valid mobile number
            </div>
          </ion-item>
          <ion-item>
            <!-- <ion-label position="floating">* Email:</ion-label> -->
            <ion-icon slot="start" name="mail"></ion-icon>
            <ion-input
              labelPlacement="floating"
              label="* Email"
              type="email"
              formControlName="emailAddress"
            ></ion-input>
          </ion-item>
          <ion-item lines="none" *ngIf="isFormComponentInvalid('emailAddress')">
            <div
              *ngFor="let error of getComponentErrors('emailAddress')"
              class="validator-error"
            >
              Not a valid email address
            </div>
          </ion-item>
          <lib-stores (updateDataEvent)="updateAddress($event)" />

          <ion-item>
            <ion-icon slot="start" name="keypad-outline"></ion-icon>
            <ion-input
              labelPlacement="floating"
              label="Pin"
              type="text"
              formControlName="pin"
            ></ion-input>
          </ion-item>
          <ion-item *ngIf="isFormComponentInvalid('pin')">
            <div
              *ngFor="let error of getComponentErrors('pin')"
              class="validator-error"
            >
              <!-- {{ error }} -->
              5 Digits needed when you spend points.
            </div>
          </ion-item>
          <ion-item>
            <!-- <ion-label position="floating">* Password:</ion-label> -->
            <ion-icon slot="start" name="lock-closed"></ion-icon>
            <ion-input
              labelPlacement="floating"
              label="* Password"
              type="password"
              formControlName="password"
            ></ion-input>
          </ion-item>

          <ion-item lines="none" *ngIf="isFormComponentInvalid('password')">
            <div
              *ngFor="let error of getComponentErrors('password')"
              class="validator-error"
            >
              {{ error }}
            </div>
          </ion-item>
          <ion-item>
            <!-- <ion-label position="floating">* Confirm Password:</ion-label> -->
            <ion-icon slot="start" name="lock-closed"></ion-icon>
            <ion-input
              labelPlacement="floating"
              label="* Confirm Password"
              type="text"
              type="password"
              formControlName="passwordConfirm"
            ></ion-input>
          </ion-item>
          <ion-item
            lines="none"
            *ngIf="isFormComponentInvalid('passwordConfirm')"
          >
            <div
              *ngFor="let error of getComponentErrors('passwordConfirm')"
              class="validator-error"
            >
              {{ error }}
            </div>
          </ion-item>
          <ion-item>
            <ion-text class="header-button terms-link">
              <a [href]="lssConfig.termsConditions" target="_blank"
                >Terms and Conditions</a
              >
            </ion-text>
          </ion-item>

          <ion-item>
            <!-- <ion-label position="floating">* Confirm Password:</ion-label> -->
            <!-- <ion-icon slot="start" name="lock-closed"></ion-icon>
        <ion-input
          labelPlacement="floating"
          label="T&C's"
          type="radio"
          formControlName="terms"
        ></ion-input> -->
            <ion-checkbox
              labelPlacement="end"
              formControlName="terms"
              labelPlacement="start"
              >Accept Terms and Conditions</ion-checkbox
            >
          </ion-item>
          <ion-button
            expand="block"
            class="save"
            type="submit"
            (click)="signUp()"
            >Sign Up</ion-button
          >
          <!-- [disabled]="!isFormValid()" -->

          <div class="mt-12"></div>
        </form>
      </div>

      <div
        class="ion-padding state-before"
        *ngIf="_formState === _formStateType.terms"
      >
        <ion-text class="header-button">
          <a [href]="lssConfig.termsConditions" target="_blank"
            >Terms and Conditions</a
          >
        </ion-text>
        <ion-button expand="block" class="save" (click)="signUp()"
          >Accept</ion-button
        >
      </div>
    </ion-card-content>
  </ion-card>

  <div class="state-success" *ngIf="_formState === _formStateType.pass">
    <div class="success-text">
      <h3>Sign up complete!</h3>
      <p>
        Your membership number is {{ formData?.newMembershipNumber }}, you will
        also receive an email containing all your account details
      </p>
      <ion-button [routerLink]="['/']">HOME</ion-button>
    </div>
  </div>

  <div class="state-success" *ngIf="_formState === _formStateType.fail">
    <div class="success-text">
      <h3>{{ error }}</h3>
      <p>Please try again or contact the call center for assistance on 012 141 3596</p>
      <ion-button (click)="_formState = _formStateType.create"
        >TRY AGAIN</ion-button
      >
    </div>
  </div>
</ion-content>
