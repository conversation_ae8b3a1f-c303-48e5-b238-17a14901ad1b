import { Component, Injector, OnInit, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import {
  AbstractControlOptions,
  ValidatorFn,
  Validators,
  ReactiveFormsModule
} from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import {
  CustomValidators,
  KeyCloakService,
  LPMemberEntityTools,
  MemberProfile,
  MemberService,
  LssConfig,
} from 'lp-client-api';
import { AbstractFormComponent } from '../../shared/abstract.form.component';

import { Router } from '@angular/router';

@Component({
  selector: 'lib-signup',
  templateUrl: 'signup.component.html',
  styleUrls: ['signup.component.scss'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, IonicModule, RouterModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class SignupComponent
  extends AbstractFormComponent<MemberProfile>
  implements OnInit
{
  error?: string;
  memberProfile?: MemberProfile;
  myDate?: string;
  favourite: any = {};
  minLength: number = 9;
  startForm: any = {};
  phoneTogether: any = '+27';
  @Input() public environment: any;
  constructor(
    injector: Injector,
    protected readonly keyCloakService: KeyCloakService,
    private memberService: MemberService,
    private router: Router,
    public lssConfig: LssConfig
  ) {
    super(injector);
    this.generateForm();
  }
  ngOnInit(): void {
    this.showLoadingModal('Loading..').then(() => {
      this.addGlobalSubscription(
        this.keyCloakService.authStatus.subscribe((value) => {
          if (value != null && value.eventName !== 'init') {
            if (value.eventName !== null && value.eventName === 'login') {
              if (this.keyCloakService.userProfile?.mustRegister) {
                this.memberProfile = {} as MemberProfile;
                this.memberProfile.externalId =
                  this.keyCloakService.userProfile.sub!;
                this.memberProfile.emailAddress =
                  this.keyCloakService.userProfile.email!;
                this.memberProfile.givenNames =
                  this.keyCloakService.userProfile.given_name!;
                this.memberProfile.surname =
                  this.keyCloakService.userProfile.family_name!;
                console.log('this.memberProfile', this.memberProfile);
                this._form.patchValue(this.memberProfile);
              }
            }
            this.dismissLoadingModal();
          } else {
            this.logout();
            this.dismissLoadingModal();
          }
        })
      );
    });

    // Check if memberPhone exists in lssConfig before accessing its properties
    if (this.lssConfig && this.lssConfig.memberPhone) {
      this.startForm.phone = this.lssConfig.memberPhone;
      // Set default value for phoneTogether in case dialCode is undefined
      this.phoneTogether = '+27'; // Default value

      // Only concatenate if both properties exist
      if (this.lssConfig.memberPhone.dialCode && this.lssConfig.memberPhone.nationalNumber !== undefined) {
        this.phoneTogether =
          this.lssConfig.memberPhone.dialCode +
          this.lssConfig.memberPhone.nationalNumber;
      }
    } else {
      console.warn('memberPhone is not defined in lssConfig');
      // Set default values
      this.startForm.phone = { dialCode: '+27', nationalNumber: '' };
    }

    if (this.lssConfig && this.lssConfig.memberCard) {
      this.startForm.membershipNumber = this.lssConfig.memberCard;
    }

    console.log('this.memberProfile', this.startForm);

    this._form.patchValue(this.startForm);
  }
  logout() {
    if (this.keyCloakService.authSuccess) {
      this.keyCloakService.keycloak?.logout();
    }
  }

  generateForm(): void {
    this._form = this._formBuilder.group(
      {
        givenNames: ['', [Validators.required]],
        surname: ['', [Validators.required]],
        title: [''],
        gender: [''],
        pin: ['', [Validators.minLength(5), Validators.maxLength(5)]],

        birthDate: ['', []],
        nationalIdNum: [
          '',
          [
            Validators.pattern('^[0-9]*$'),
            Validators.minLength(13),
            Validators.maxLength(13),
          ],
        ],
        membershipNumber: [
          '',
          [
            Validators.pattern('^[0-9]*$'),
            Validators.minLength(16),
            Validators.maxLength(16),
          ],
        ],
        phone: [
          LPMemberEntityTools.getIONTelephoneFromLPTel(
            LPMemberEntityTools.getEmptyPhone('CELL')
          ),
          [Validators.required],
        ],
        emailAddress: [
          '',
          Validators.compose([
            Validators.required,
            Validators.pattern('^[a-z0-9._%+-]+@[a-z0-9.-]+.[a-z]{2,4}$'),
          ]),
        ],
        password: [
          '',
          Validators.compose([
            Validators.required,
            CustomValidators.passwordValidate(),
          ]),
        ],
        passwordConfirm: ['', [Validators.required]],
        terms: [false, Validators.requiredTrue],
      },
      {
        validators: Validators.compose([
          CustomValidators.matchValue(
            'password',
            'Password',
            'passwordConfirm',
            'Confirm Password'
          ),
        ]),
      } as AbstractControlOptions
    );
  }

  showTerms(): void {
    this._formState = this._formStateType.terms;
  }

  canSignup(): boolean {
    return this.isFormValid() && this.form.terms.value === true;
  }

  // get hasDate() {
  //   this.formData = this.getFormValues();
  //   let date;
  //   if (this.formData?.birthDate) {
  //     let newD: any = this.formData?.birthDate;
  //     date = newD.split('T')[0];
  //   }
  //   return date;
  // }

  updateAddress(data: any) {
    this.favourite = data;
  }

  todaysDate12YearsAgo() {
    let date: any = new Date();
    date.setFullYear(date.getFullYear() - 12);
    //format date to yyyy-mm-dd
    date = date.toISOString().split('T')[0];
    return date;
  }

  signUp(): void {
    this.formData = this.getFormValues();
    let payload: any = this.formData;
    console.log('payload', payload);
    if (payload.phone) {
      payload.personTelephone = [
        {
          telephoneType: 'CELL',
          countryCode: payload.phone.dialCode ? payload.phone.dialCode : '',
          telephoneCode: '',
          telephoneNumber: payload.phone.nationalNumber
            ? payload.phone.nationalNumber
            : '',
          telephoneExtension: '',
          phoneable: '',
          smsable: '',
        },
      ];
    }
    console.log('personTelephone', payload.personTelephone);

    // if (this.favourite.partnerId) {
    //   payload.preferenceList = [
    //     {
    //       level1: 'PART',
    //       level2: this.favourite.partnerId,
    //     },
    //   ];
    // }

    if (payload.phone) delete payload.phone;

    if (payload.birthDate) payload.birthDate = payload.birthDate + 'T00:00:00';
    else if (payload.birthDate === '') delete payload.birthDate;
    console.log('payload', payload);

    if (this.formData) {
      this.showLoadingModal('Please wait while we sign you up!').then(() => {
        this.memberService.register(payload).subscribe({
          error: (error) => {
            this.dismissLoadingModal();
            this._formState = this._formStateType.fail;
            this.error = error.error.detail;
            console.log(error.error.detail);
            this.presentToast({
              message: error.error.detail,
              color: 'danger',
              position: 'bottom',
            }).then();
          },
          next: (body) => {
            console.log('body', body);
            this.dismissLoadingModal();
            this._formState = this._formStateType.pass;
            this.formData = body;
            this.presentToast({
              message: 'Your account has been created',
              position: 'bottom',
            }).then();

            this.router.navigate(['/public/landing']);
          },
        });
      });
    }
  }
}
