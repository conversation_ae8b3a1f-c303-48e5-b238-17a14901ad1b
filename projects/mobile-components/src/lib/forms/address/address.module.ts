import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { AddressComponent } from './address.component';
// Import IonicSelectableWrapperModule
import { IonicSelectableWrapperModule } from '../../shared/ionic-selectable/ionic-selectable.module';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    IonicSelectableWrapperModule,
    AddressComponent
  ],
  exports: [
    AddressComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AddressModule { }
