import {
  Component,
  Inject,
  Input,
  OnInit,
  OnChanges,
  SimpleChanges,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import {
  Address,
  CodeItem,
  CountryItem,
  LssConfig,
  SystemService,
  ValidationService,
} from 'lp-client-api';
import { BehaviorSubject, Observable } from 'rxjs';
import { shareReplay } from 'rxjs/operators';

@Component({
  selector: 'lp-pos-address',
  templateUrl: './address.component.html',
  styleUrls: ['./address.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    IonicModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class AddressComponent implements OnInit, OnChanges {
  @Input()
  type: any;
  _data!: Address;
  @Input()
  required_field = false;
  @Input()
  mainForm!: FormGroup;
  @Input()
  mainAddress!: Address;

  // Create BehaviorSubjects at the class level
  private countriesSubject = new BehaviorSubject<CountryItem[]>([]);
  countries: Observable<CountryItem[]> = this.countriesSubject.asObservable().pipe(shareReplay(1));

  private provincesSubject = new BehaviorSubject<CodeItem[]>([]);
  provinces: Observable<CodeItem[]> = this.provincesSubject.asObservable().pipe(shareReplay(1));

  private districtsSubject = new BehaviorSubject<CodeItem[]>([]);
  districts: Observable<CodeItem[]> = this.districtsSubject.asObservable().pipe(shareReplay(1));

  private placesSubject = new BehaviorSubject<CodeItem[]>([]);
  places: Observable<CodeItem[]> = this.placesSubject.asObservable().pipe(shareReplay(1));

  provincesArray: CodeItem[] = [];
  districtsArray: CodeItem[] = [];
  cities!: CodeItem[];
  placesArray: CodeItem[] = [];
  @Input()
  modalCloseText = 'Close';
  @Input()
  modalCloseButtonSlot: 'start' | 'end' | 'primary' | 'secondary' = 'end';
  currentSelection = {
    country: '',
    province: '',
    district: '',
    city: '',
    places: '',
    line1: '',
    line2: '',
    postalCode: '',
  };
  selectedCity?: CodeItem;
  useDistrict = false;
  countryList: unknown;
  _myForm: FormGroup;
  loading = false;

  // Track API data loading state
  isLoadingCountries = true;
  isUsingApiData = false;

  // Array to store countries directly
  countriesArray: CountryItem[] = [];

  // Hardcoded fallback countries
  private fallbackCountries: CountryItem[] = [
    { code: 'DE', country: 'Germany', productId: '', dialcode: '+49', isoCode: 'de' },
    { code: 'US', country: 'United States', productId: '', dialcode: '+1', isoCode: 'us' },
    { code: 'GB', country: 'United Kingdom', productId: '', dialcode: '+44', isoCode: 'gb' },
    { code: 'ZA', country: 'South Africa', productId: '', dialcode: '+27', isoCode: 'za' },
    { code: 'AU', country: 'Australia', productId: '', dialcode: '+61', isoCode: 'au' },
    { code: 'CA', country: 'Canada', productId: '', dialcode: '+1', isoCode: 'ca' },
    { code: 'FR', country: 'France', productId: '', dialcode: '+33', isoCode: 'fr' },
    { code: 'IT', country: 'Italy', productId: '', dialcode: '+39', isoCode: 'it' },
    { code: 'ES', country: 'Spain', productId: '', dialcode: '+34', isoCode: 'es' },
    { code: 'NL', country: 'Netherlands', productId: '', dialcode: '+31', isoCode: 'nl' }
  ];

  // Hardcoded fallback provinces for common countries
  private fallbackProvinces: { [country: string]: CodeItem[] } = {
    'ZA': [
      { value: 'GP', label: 'Gauteng' },
      { value: 'WC', label: 'Western Cape' },
      { value: 'EC', label: 'Eastern Cape' },
      { value: 'NC', label: 'Northern Cape' },
      { value: 'NW', label: 'North West' },
      { value: 'FS', label: 'Free State' },
      { value: 'KZN', label: 'KwaZulu-Natal' },
      { value: 'MP', label: 'Mpumalanga' },
      { value: 'LP', label: 'Limpopo' }
    ],
    'US': [
      { value: 'AL', label: 'Alabama' },
      { value: 'AK', label: 'Alaska' },
      { value: 'AZ', label: 'Arizona' },
      { value: 'CA', label: 'California' },
      { value: 'CO', label: 'Colorado' },
      { value: 'NY', label: 'New York' },
      { value: 'TX', label: 'Texas' }
    ],
    'DE': [
      { value: 'BW', label: 'Baden-Württemberg' },
      { value: 'BY', label: 'Bavaria' },
      { value: 'BE', label: 'Berlin' },
      { value: 'BB', label: 'Brandenburg' },
      { value: 'HB', label: 'Bremen' },
      { value: 'HH', label: 'Hamburg' },
      { value: 'HE', label: 'Hesse' }
    ]
  };

  constructor(
    private _formBuilder: FormBuilder,
    private _systemService: SystemService,
    public _formValidations: ValidationService,
    public lssConfig: LssConfig
  ) {
    console.log('AddressComponent: Constructor initialized');

    this._myForm = this._formBuilder.group({
      country: ['', Validators.required],
      province: ['', Validators.required],
      city: ['', Validators.required],
      place: ['', Validators.required],
      line1: ['', Validators.required],
      line2: [''],
      postalCode: [''],
    });

    // Start with empty countries array and show loading state
    this.isLoadingCountries = true;
    this.isUsingApiData = false;

    // Don't initialize with fallback countries immediately
    // Instead, wait for API response first

    // Try to fetch countries from the correct API endpoint: group/LAND
    console.log('AddressComponent: Attempting to fetch countries from API using group/LAND endpoint');
    this._systemService.getCodeGroup('LAND').subscribe({
      next: (codeGroupData) => {
        console.log('AddressComponent: Countries API response received from group/LAND endpoint');
        this.isLoadingCountries = false;

        if (codeGroupData && codeGroupData.codeItem && codeGroupData.codeItem.length > 0) {
          console.log(`AddressComponent: Received ${codeGroupData.codeItem.length} countries from API`);

          // Mark that we're using API data
          this.isUsingApiData = true;

          // Convert LPCode items to CountryItem format
          const countryData: CountryItem[] = codeGroupData.codeItem.map(code => ({
            code: code.codeId,
            country: code.description,
            productId: '',
            dialcode: '',
            isoCode: code.codeId.toLowerCase()
          }));

          // Store in both the subject and local array
          this.countriesArray = countryData;
          this.countryList = countryData;
          this.countriesSubject.next(countryData);

          // If we already have a country selected from the address data, make sure it's still valid
          if (this.mainAddress && this.mainAddress.country) {
            this.validateSelectedCountry();
          }
        } else {
          console.warn('AddressComponent: No countries received from API or empty array, using fallback data');
          // Fall back to hardcoded countries if API returns empty data
          this.countriesArray = [...this.fallbackCountries];
          this.countryList = this.fallbackCountries;
          this.countriesSubject.next(this.fallbackCountries);
          console.log('AddressComponent: Using fallback countries due to empty API response');
        }
      },
      error: (error) => {
        console.error('AddressComponent: Error fetching countries from group/LAND endpoint:', error);
        console.error('AddressComponent: Error occurred while fetching countries from API');

        // Fall back to hardcoded countries if API call fails
        this.isLoadingCountries = false;
        this.countriesArray = [...this.fallbackCountries];
        this.countryList = this.fallbackCountries;
        this.countriesSubject.next(this.fallbackCountries);
        console.log('AddressComponent: Using fallback countries due to API error');
      }
    });

    // Initialize the form with the mainAddress data if available
    if (this.mainAddress) {
      // Set initial values for text fields if available
      if (this.mainAddress.province) {
        this._myForm.controls['province'].setValue(this.mainAddress.province);
      }

      if (this.mainAddress.city) {
        this._myForm.controls['city'].setValue(this.mainAddress.city);
      }

      if (this.mainAddress.suburb) {
        this._myForm.controls['place'].setValue(this.mainAddress.suburb);
      }
    }
  }

  // Helper method to validate that the selected country is in the countries list
  private validateSelectedCountry(): void {
    if (this.mainAddress && this.mainAddress.country) {
      const countryCode = this.mainAddress.country;
      const countryExists = this.countriesArray.some(c =>
        c.code === countryCode || c.isoCode.toLowerCase() === countryCode.toLowerCase()
      );

      if (!countryExists) {
        console.warn(`AddressComponent: Selected country ${countryCode} not found in countries list`);
      } else {
        console.log(`AddressComponent: Selected country ${countryCode} validated in countries list`);
      }
    }
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['mainAddress']) {
      if (this.mainAddress && this.mainAddress.country) {
        this.patchFormSeq();
      }
    }
  }

  ngOnInit(): void {
    if (this.mainForm != null) {
      this.mainForm.addControl(`address_${this.type}`, this._myForm);
    }

    // Initialize the form with the mainAddress data if available
    if (this.mainAddress) {
      this.patchFormSeq();
    }
  }

  // Find a country by ISO code or regular code
  findCountryByCode(code: string): Promise<CountryItem | undefined> {
    return new Promise((resolve) => {
      console.log(`AddressComponent: Finding country with code: ${code}`);

      // Always use the fallback countries if no countries are available
      const countries = this.countriesArray.length > 0 ? this.countriesArray : this.fallbackCountries;
      console.log(`AddressComponent: Using ${countries.length} countries for lookup`);

      // First try to find by exact code match
      let country = countries.find(c =>
        (this.lssConfig.useISO ? c.isoCode : c.code) === code
      );

      if (country) {
        console.log(`AddressComponent: Found country by exact match: ${country.country}`);
        resolve(country);
        return;
      }

      // If not found and it looks like an ISO code (2 letters), try to find by ISO code
      if (code && code.length === 2) {
        country = countries.find(c => c.isoCode.toLowerCase() === code.toLowerCase());
        if (country) {
          console.log(`AddressComponent: Found country by ISO code: ${country.country}`);
          resolve(country);
          return;
        }
      }

      // If still not found and we have a code that's not ISO format, try by regular code
      if (code && code.length !== 2) {
        country = countries.find(c => c.code === code);
        if (country) {
          console.log(`AddressComponent: Found country by regular code: ${country.country}`);
          resolve(country);
          return;
        }
      }

      // If still not found, try a case-insensitive search
      if (code) {
        country = countries.find(c =>
          c.code.toLowerCase() === code.toLowerCase() ||
          c.isoCode.toLowerCase() === code.toLowerCase()
        );
        if (country) {
          console.log(`AddressComponent: Found country by case-insensitive search: ${country.country}`);
          resolve(country);
          return;
        }
      }

      // If still not found, create a fallback country object for the given code
      if (code) {
        console.warn(`AddressComponent: No country found with code: ${code}, creating fallback`);
        const fallbackCountry: CountryItem = {
          code: code,
          country: `Country ${code}`,
          productId: '',
          dialcode: '',
          isoCode: code.toLowerCase()
        };
        resolve(fallbackCountry);
        return;
      }

      // If no code provided, resolve with undefined
      console.warn('AddressComponent: No country code provided');
      resolve(undefined);
    });
  }

  async patchFormSeq() {
    if (this.mainAddress == undefined) return;

    this.loading = true;

    if (this.mainAddress.country) {
      // Find the country in our list
      const country = await this.findCountryByCode(this.mainAddress.country);

      if (country) {
        // Get the correct value based on useISO setting
        const countryValue = this.lssConfig.useISO ? country.isoCode : country.code;

        // Set the country value and trigger the change
        this._myForm.controls['country'].patchValue(countryValue);
        await this.countryChange(countryValue);

        // Now handle the rest of the address fields
        if (this.mainAddress.province) {
          this._myForm.controls['province'].patchValue(this.mainAddress.province);
          this.provinceChange(this.mainAddress.province);

          if (this.useDistrict && this.mainAddress.district) {
            this.districtChange(this.mainAddress.district, this.mainAddress.province);
          }

          if (this.mainAddress.city) {
            this.cityChange(this.mainAddress.city);
          }
        }

        // Set the remaining fields
        if (this.mainAddress.suburb) {
          this._myForm.controls['place'].patchValue(this.mainAddress.suburb);
        }

        if (this.mainAddress.line1) {
          this._myForm.controls['line1'].patchValue(this.mainAddress.line1);
        }

        if (this.mainAddress.line2) {
          this._myForm.controls['line2'].patchValue(this.mainAddress.line2);
        }

        if (this.mainAddress.postalCode) {
          this._myForm.controls['postalCode'].patchValue(this.mainAddress.postalCode);
        }
      } else {
        console.warn(`Country with code ${this.mainAddress.country} not found in the list`);
      }
    }

    this.loading = false;
  }
  get form(): any {
    return this._myForm.controls;
  }

  // This method is no longer needed since province is now a text input
  // Keeping a simplified version for backward compatibility
  private getFallbackProvinces(_countryCode: string): CodeItem[] {
    // No need to do anything since province is now a text input
    console.log(`AddressComponent: getFallbackProvinces called but not needed anymore`);
    return [];
  }

  async countryChange(country?: string) {
    if (country) {
      if (this.currentSelection.country === country) {
        return;
      }

      console.log(`AddressComponent: Country changed to: ${country}`);
      this.currentSelection.country = country;

      // Since province is now a text input, we don't need to do anything special
      // Just clear the province field if we're not in the loading process
      if (!this.loading) {
        this.form.province.patchValue('');
      }
    }
  }
  provinceChange(province?: string) {
    if (province) {
      if (this.currentSelection.province === province) {
        return;
      }
      this.currentSelection.province = province;
      console.log(`AddressComponent: Province changed to: ${province}`);

      // Since city is now a text input, we don't need to do anything special
      // Just clear the city field if we're not in the loading process
      if (!this.loading) {
        this.form.city.patchValue('');
      }
    }
  }

  districtChange(district?: string, province?: string) {
    if (district) {
      this.populateCity(district, province);
    }
  }
  // This method is no longer needed since city is now a text input
  // Keeping a simplified version for backward compatibility
  populateCity(_district?: string, _province?: string) {
    // No need to do anything since city is now a text input
    console.log(`AddressComponent: populateCity called but not needed anymore`);
  }
  cityChange(city?: string) {
    if (city) {
      console.log(`AddressComponent: City changed to: ${city}`);

      // Since place/suburb is now a text input, we don't need to do anything special
      // Just clear the place field if we're not in the loading process
      if (!this.loading) {
        this.form.place.patchValue('');
      }
    }
  }

  getEventValue(event: any): any {
    return event.detail.value;
  }
  getEventValueFilter(event: any): any {
    this.selectedCity = event.item;
    if (event.item) {
      return event.item.value;
    }
    return null;
  }
}
