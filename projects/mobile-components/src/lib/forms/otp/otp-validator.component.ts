import { Component, Input, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { KeyCloakService, LssConfig, MemberService } from 'lp-client-api';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

@Component({
  selector: 'lib-otp-validator',
  templateUrl: './otp-validator.component.html',
  styleUrls: ['./otp-validator.component.css'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, IonicModule, RouterModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class OtpValidatorComponent implements OnInit {
  // Form and state
  public otpForm: FormGroup;
  public loading = false;
  public submitted = false;
  public error = '';
  public success = '';
  public step = 1; // 1 = initial, 2 = OTP sent, 3 = verification
  
  // Configuration and services
  @Input() kc?: KeyCloakService;
  @Input() memberService?: MemberService;
  @Input() lssConfig?: LssConfig;
  @Input() router?: Router;

  constructor(private fb: FormBuilder) {
    this.otpForm = this.fb.group({
      cardNumber: ['', [Validators.required, Validators.minLength(16)]],
      otpCode: ['', [Validators.required, Validators.minLength(4)]]
    });
  }

  ngOnInit(): void {
    // Initialize form and services
    console.log('[OtpValidator] Component initialized');
  }

  // Safe getters to prevent undefined errors
  public get lssConfigSafe(): LssConfig {
    return this.lssConfig || {
      pages: { landing: { loggedinIcon: '' } },
      telephone: { 
        selectFirstCountry: true,
        preferredCountries: ['za']
      }
    } as unknown as LssConfig;
  }

  public get kcSafe(): KeyCloakService {
    if (!this.kc) {
      console.warn('[OtpValidator] KeyCloakService not provided');
    }
    return this.kc || {} as KeyCloakService;
  }

  public get memberServiceSafe(): MemberService {
    if (!this.memberService) {
      console.warn('[OtpValidator] MemberService not provided');
    }
    return this.memberService || {} as MemberService;
  }

  // Convenience getter for form fields
  get f() { return this.otpForm.controls; }

  // Request OTP
  requestOtp(): void {
    this.submitted = true;
    
    // Stop if form is invalid
    if (this.otpForm.get('cardNumber')?.invalid) {
      return;
    }
    
    this.loading = true;
    this.error = '';
    
    const ms = this.memberServiceSafe as any;
    if (ms.requestOtp) {
      ms.requestOtp(this.f['cardNumber'].value)
        .subscribe({
          next: (response: any) => {
            this.loading = false;
            this.success = 'OTP sent successfully!';
            this.step = 2;
            console.log('[OtpValidator] OTP requested successfully', response);
          },
          error: (error: any) => {
            this.loading = false;
            this.error = error?.error?.message || 'Failed to send OTP';
            console.error('[OtpValidator] Error requesting OTP', error);
          }
        });
    } else {
      this.loading = false;
      this.error = 'OTP service not available';
      console.error('[OtpValidator] MemberService.requestOtp is not defined');
    }
  }
  
  // Verify OTP
  verifyOtp(): void {
    this.submitted = true;
    
    // Stop if form is invalid
    if (this.otpForm.get('otpCode')?.invalid) {
      return;
    }
    
    this.loading = true;
    this.error = '';
    
    const ms = this.memberServiceSafe as any;
    if (ms.verifyOtp) {
      ms.verifyOtp(this.f['cardNumber'].value, this.f['otpCode'].value)
        .subscribe({
          next: (response: any) => {
            this.loading = false;
            this.success = 'Verification successful!';
            this.step = 3;
            console.log('[OtpValidator] OTP verified successfully', response);
            
            // Navigate to home after successful verification
            setTimeout(() => {
              if (this.router) {
                this.router.navigate(['/public/home']);
              }
            }, 2000);
          },
          error: (error: any) => {
            this.loading = false;
            this.error = error?.error?.message || 'Failed to verify OTP';
            console.error('[OtpValidator] Error verifying OTP', error);
          }
        });
    } else {
      this.loading = false;
      this.error = 'OTP verification service not available';
      console.error('[OtpValidator] MemberService.verifyOtp is not defined');
    }
  }
  
  // Reset to initial state
  reset(): void {
    this.otpForm.reset();
    this.submitted = false;
    this.error = '';
    this.success = '';
    this.step = 1;
  }
}
