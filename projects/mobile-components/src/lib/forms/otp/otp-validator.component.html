<ion-content class="app-background">
  <lib-head-logo
    type="welcome"
    names="OTP Verification"
    [src]="lssConfigSafe.pages.landing.loggedinIcon"
  />
 <div class="m-4 p-4 text-red-500">Hello test</div>
  <ion-card class="m-4 p-4">
    <form [formGroup]="otpForm">
      <div *ngIf="error" class="bg-red-100 p-3 mb-4 rounded text-red-700 text-sm">
        {{ error }}
      </div>

      <div *ngIf="success" class="bg-green-100 p-3 mb-4 rounded text-green-700 text-sm">
        {{ success }}
      </div>

      <!-- Step 1: Enter Card Number -->
      <div *ngIf="step === 1">
        <ion-item>
          <ion-input 
            labelPlacement="floating" 
            label="Card Number" 
            type="text" 
            formControlName="cardNumber" 
            placeholder="Enter 16-digit card number">
          </ion-input>
        </ion-item>
        
        <div *ngIf="submitted && f['cardNumber'].errors" class="text-red-500 text-xs mt-1 ml-2">
          <div *ngIf="f['cardNumber'].errors['required']">Card number is required</div>
          <div *ngIf="f['cardNumber'].errors['minlength']">Card number must be at least 16 digits</div>
        </div>

        <ion-button 
          expand="block" 
          color="primary" 
          class="mt-4" 
          (click)="requestOtp()" 
          [disabled]="loading">
          <span *ngIf="!loading">Request OTP</span>
          <ion-spinner *ngIf="loading" name="dots"></ion-spinner>
        </ion-button>
      </div>

      <!-- Step 2: Enter OTP -->
      <div *ngIf="step === 2">
        <ion-item>
          <ion-input 
            labelPlacement="floating" 
            label="OTP Code" 
            type="text" 
            formControlName="otpCode" 
            placeholder="Enter OTP code">
          </ion-input>
        </ion-item>
        
        <div *ngIf="submitted && f['otpCode'].errors" class="text-red-500 text-xs mt-1 ml-2">
          <div *ngIf="f['otpCode'].errors['required']">OTP code is required</div>
          <div *ngIf="f['otpCode'].errors['minlength']">OTP code must be at least 4 characters</div>
        </div>

        <ion-button 
          expand="block" 
          color="primary" 
          class="mt-4" 
          (click)="verifyOtp()" 
          [disabled]="loading">
          <span *ngIf="!loading">Verify OTP</span>
          <ion-spinner *ngIf="loading" name="dots"></ion-spinner>
        </ion-button>

        <ion-button 
          expand="block" 
          fill="clear" 
          color="medium" 
          class="mt-2" 
          (click)="reset()" 
          [disabled]="loading">
          Back
        </ion-button>
      </div>

      <!-- Step 3: Success -->
      <div *ngIf="step === 3" class="text-center">
        <ion-icon name="checkmark-circle" color="success" class="text-6xl mb-4"></ion-icon>
        <h2 class="text-xl font-semibold mb-2">Verification Successful</h2>
        <p class="text-gray-600 mb-4">You will be redirected to the home page shortly.</p>
        
        <ion-button 
          expand="block" 
          color="primary" 
          class="mt-4" 
          [routerLink]="['/public/home']">Go to Home
        </ion-button>
      </div>
    </form>
  </ion-card>

  <ion-button expand="block" fill="clear" color="medium" class="mx-4" [routerLink]="['/public/home']">
    Back to Home
  </ion-button>
</ion-content>
