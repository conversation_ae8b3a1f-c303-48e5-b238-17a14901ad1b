import { Component, Injector, OnInit, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, Input  } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { AbstractControlOptions, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import {
  CustomValidators,
  KeyCloakService,
  LPMemberEntityTools,
  MemberProfile,
  MemberService,
  LssConfig,
} from 'lp-client-api';
import { AbstractFormComponent } from '../../shared/abstract.form.component';


@Component({
  selector: 'app-validate',
  templateUrl: './validate.component.html',
  styleUrls: ['./validate.component.css'],
  standalone: true,
  imports: [CommonModule, FormsModule, IonicModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class ValidateComponent
  extends AbstractFormComponent<MemberProfile>
  implements OnInit
{
  error?: string;
  memberProfile?: MemberProfile;
  myDate?: string;
  favourite: any = {};
  minLength: number = 9;
  
  // Properties injected by the dynamic component loader
  profile?: MemberProfile; // Injected from DynamicPublicPageComponent
  kc?: KeyCloakService;   // Injected from DynamicPublicPageComponent
  memberService?: MemberService; // Also need to receive this from parent
  router?: Router; // Also need to receive this from parent
  lssConfig?: LssConfig; // Also need to receive this from parent
  @Input() public environment?: any;
  public get keyCloakService(): KeyCloakService {
    if (!this.kc) {
      console.error('KeyCloakService not provided to ValidateComponent');
      // Return a minimal implementation to prevent null errors
      return {
        authStatus: { subscribe: () => ({ unsubscribe: () => {} }) },
        userProfile: {},
        authSuccess: false,
        login: () => Promise.resolve(null),
        lostPassword: () => Promise.resolve(null),
        keycloak: { logout: () => {} }
      } as unknown as KeyCloakService;
    }
    return this.kc;
  }
  
  public get memberServiceSafe(): MemberService {
    if (!this.memberService) {
      console.error('MemberService not provided to ValidateComponent');
      // Return a minimal implementation to prevent null errors
      return {
        validateAccountReference: () => ({ subscribe: () => ({}) }),
        validateAccount: () => ({ subscribe: () => ({}) })
      } as unknown as MemberService;
    }
    return this.memberService;
  }
  
  public get lssConfigSafe(): LssConfig {
    if (!this.lssConfig) {
      console.error('LssConfig not provided to ValidateComponent');
      // Return a minimal implementation to prevent null errors
      return {
        apiId: '',
        apiIdKeyStart: '',
        apiIdKeyEnd: '',
        memberPhone: '',
        memberCard: '',
        pages: { landing: { loggedinIcon: '' } },
        telephone: { 
          selectFirstCountry: true,
          preferredCountries: ['za']
        }
      } as unknown as LssConfig;
    }
    return this.lssConfig;
  }
  
  public get routerSafe(): Router {
    if (!this.router) {
      console.error('Router not provided to ValidateComponent');
      // Return a minimal implementation to prevent null errors
      return { navigate: () => Promise.resolve(true) } as unknown as Router;
    }
    return this.router;
  }
  
  constructor(injector: Injector) {
    super(injector);
    this.generateForm();
  }
  
  showError = false;
  isModalOpen = false;
  showLoginButton = false;
  emailotp = '';
  smsotp = '';
  reference = '';

  ngOnInit(): void {
    this.showLoadingModal('Loading..').then(() => {
      this.addGlobalSubscription(
        this.keyCloakService.authStatus.subscribe((value) => {
          if (value != null && value.eventName !== 'init') {
            if (value.eventName !== null && value.eventName === 'login') {
              if (this.keyCloakService.userProfile?.mustRegister) {
                this.memberProfile = {} as MemberProfile;
                this.memberProfile.externalId =
                  this.keyCloakService.userProfile.sub!;
                this.memberProfile.emailAddress =
                  this.keyCloakService.userProfile.email!;
                this.memberProfile.givenNames =
                  this.keyCloakService.userProfile.given_name!;
                this.memberProfile.surname =
                  this.keyCloakService.userProfile.family_name!;
                this._form.patchValue(this.memberProfile);
              }
            }
            this.dismissLoadingModal();
          } else {
            this.logout();
            this.dismissLoadingModal();
          }
        })
      );
    });
  }
  logout() {
    if (this.keyCloakService.authSuccess) {
      localStorage.removeItem('lastWelcomeDate');
      this.keyCloakService.keycloak?.logout();
    }
  }
  setOpen(isOpen: boolean) {
    this.isModalOpen = isOpen;
  }

  generateForm(): void {
    this._form = this._formBuilder.group({
      Cardnumber: ['', []],
      phone: ['', [Validators.minLength(10), Validators.maxLength(10)]],
      // email: [
      //   '',
      //   Validators.compose([
      //     Validators.required,
      //     Validators.pattern('^[a-z0-9._%+-]+@[a-z0-9.-]+.[a-z]{2,4}$'),
      //   ]),
      // ],
    } as AbstractControlOptions);
  }

  showTerms(): void {
    this._formState = this._formStateType.terms;
  }

  canSignup(): boolean {
    return this.isFormValid() && this.form.terms.value === true;
  }
  login() {
    if (!this.keyCloakService.authSuccess) {
      this.keyCloakService.login().then((data: any) => {
        console.log(data);
      });
    }
    // Preferences.remove({ key: 'login' }).then(() => {
    //   console.log('Token Removed');
    // });
  }

  lostPassword() {
    if (!this.keyCloakService.authSuccess) {
      this.keyCloakService.lostPassword().then((data: any) => {
        console.log(data);
      });
    }
  }

  isMyFormValid(): boolean {
    const formData = this.getFormValues();
    if (formData.phone) {
      if (formData.phone.nationalNumber) {
        return (
          this._form.valid &&
          formData.phone.nationalNumber.replaceAll(' ', '').length === 10
        );
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  isMyPhoneValid(): boolean {
    const formData = this.getFormValues();
    if (formData.phone) {
      if (formData.phone.nationalNumber) {
        return formData.phone.nationalNumber.replaceAll(' ', '').length > 0;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  submit(): void {
    const formData = this.getFormValues();
    console.log('form', formData);
    this.showError = false;

    const payload = {
      Cardnumber: formData.Cardnumber,
      email: '',
      apiId: this.lssConfigSafe.apiId,
      uniqueId: '',
      mobileCountry: '+27',
      mobile: '',
    };
    this.lssConfigSafe.memberPhone = formData.phone;
    this.lssConfigSafe.memberCard = formData.Cardnumber;

    payload.uniqueId = this._systemService.getUniqueId(
      this.lssConfigSafe.apiIdKeyStart,
      this.lssConfigSafe.apiIdKeyEnd,
      formData.phone.nationalNumber.replaceAll(' ', '')
    );
    payload.mobile = formData.phone.nationalNumber.replaceAll(' ', '');

    console.log('payload', payload);
    if (formData) {
      this.showLoadingModal('Please wait while we send your data!').then(() => {
        this.memberServiceSafe.validateAccountReference(payload).subscribe({
          error: (error: any) => {
            console.log('err contact us', error);
            console.log('err contact us', error.error.detail);
            if (error.error.detail == 'Member not found')
              console.log('err contact us', error.error.detail);
            else if (error.error.detail == 'Member already exists') {
              this.dismissLoadingModal();
              this.presentToast({
                message: error.error.detail,
                color: 'danger',
                position: 'bottom',
              }).then();

              this._formState = this._formStateType.fail;
              this.error = error.error.detail;
              this.showLoginButton = true;
            } else {
              this.dismissLoadingModal();
              this.presentToast({
                message: 'Oops, something went wrong!',
                color: 'danger',
                position: 'bottom',
              }).then();

              this._formState = this._formStateType.fail;
              this.error = error.error.detail;
            }
          },
          next: (body: any) => {
            console.log('body contact us', body);
            this.reference = body.reference;
            this.dismissLoadingModal();
            this.presentToast({
              message: 'Your message has been sent',
              position: 'bottom',
            }).then();

            this._formState = this._formStateType.pass;
            this.formData = body;
            this.isModalOpen = true;
          },
        });
      });
    } else {
      this.showError = true;
      console.log('showError', this.showError);
      this.isModalOpen = false;
    }
  }

  submitOtp(): void {
    const formData = this.getFormValues();
    console.log('form', this.formData);
    this.showError = false;

    const payload = {
      smsOtp: this.smsotp,
      emailOtp: this.emailotp,
      apiId: this.lssConfigSafe.apiId,
      uniqueId: '',
    };

    payload.uniqueId = this._systemService.getUniqueId(
      this.lssConfigSafe.apiIdKeyStart,
      this.lssConfigSafe.apiIdKeyEnd,
      this.reference
    );

    console.log('payload', payload);
    if (formData) {
      this.showLoadingModal('Please wait while we validate your OTP!').then(
        () => {
          this.memberServiceSafe.validateAccount(payload, this.reference).subscribe({
            error: (error: any) => {
              console.log('err contact us', error);
              console.log('err contact us', error.error.detail);
              if (error.error.detail == 'Member not found')
                console.log('err contact us', error.error.detail);
              else if (
                error.error.detail ==
                'Unable to verify, provided details are invalid.'
              ) {
                this.dismissLoadingModal();
                this.isModalOpen = false;

                this.presentToast({
                  message: error.error.detail,
                  color: 'danger',
                  position: 'bottom',
                }).then();

                this._formState = this._formStateType.fail;
                this.error = error.error.detail;
              } else {
                this.dismissLoadingModal();
                this.presentToast({
                  message: 'Oops, something went wrong!',
                  color: 'danger',
                  position: 'bottom',
                }).then();

                this._formState = this._formStateType.fail;
                this.error = error.error.detail;
              }
            },
            next: (body: any) => {
              console.log('body contact us', body);
              this.dismissLoadingModal();
              this.presentToast({
                message: 'Your OTP has been sent',
                position: 'bottom',
              }).then();
              this.isModalOpen = false;

              this._formState = this._formStateType.pass;
              this.formData = body;
              setTimeout(() => {
                this.routerSafe.navigate(['/public/signup']);
              }, 2000);
            },
          });
        }
      );
    } else {
      this.showError = true;
      console.log('showError', this.showError);
      this.isModalOpen = false;
    }
  }
}
