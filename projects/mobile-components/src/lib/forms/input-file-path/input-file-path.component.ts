import {
  Component,
  Input,
  ViewChild,
  ElementRef,
  Output,
  EventEmitter,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA
} from '@angular/core';
import { computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IconComponent } from '../../base/icon/icon.component';
import { BasePlaceloadComponent } from '../../base/placeload/placeload.component';

@Component({
  selector: 'base-input-file-path',
  templateUrl: './input-file-path.component.html',
  styleUrls: ['./input-file-path.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconComponent,
    BasePlaceloadComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class InputFilePathComponent {
  @Input() id?: string;
  @Input() item_id: string = Math.random().toString(36).substring(7);
  @Input() rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full';
  @Input() size?: 'sm' | 'md' | 'lg';
  @Input() contrast?: 'default' | 'default-contrast';
  @Input() label?: string;
  @Input() icon?: string;
  @Input() placeholder: string = 'Choose file';
  @Input() error?: string | boolean = false;
  @Input() colorFocus?: boolean;
  @Input() loading?: boolean;
  @Input() textValue?: (fileList?: FileList | null) => string = (
    fileList?: FileList | null
  ) => {
    if (!fileList?.length) {
      return 'No file chosen';
    }
    return fileList.length === 1
      ? fileList[0]?.name ?? 'Invalid file selected'
      : `${fileList.length} files selected`;
  };
  @Input() classes?: {
    wrapper?: string | string[];
    label?: string | string[];
    input?: string | string[];
    text?: string | string[];
    error?: string | string[];
    icon?: string | string[];
  } = {};

  @ViewChild('inputRef') inputRef!: ElementRef<HTMLInputElement>;
  @Output() modelChange = new EventEmitter<FileList | null>();

  modelValue: FileList | null = null;

  public radiuses: Record<string, string> = {
    none: '',
    sm: 'nui-input-rounded-sm',
    md: 'nui-input-rounded-md',
    lg: 'nui-input-rounded-lg',
    full: 'nui-input-rounded-full',
  };

  public sizes: Record<string, string> = {
    sm: 'nui-input-sm',
    md: 'nui-input-md',
    lg: 'nui-input-lg',
  };

  public contrasts: Record<string, string> = {
    default: 'nui-input-default',
    'default-contrast': 'nui-input-default-contrast',
  };

  get textValueComputed(): string {
    return this.textValue?.(this.modelValue) ?? '';
  }

  isErrorString(): boolean {
    return typeof this.error === 'string';
  }

  onFileChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    this.modelValue = input.files;
    this.modelChange.emit(this.modelValue);
  }
}
