import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import Crypto components
// Standalone (1)
import { PopularCryptosComponent } from './popular-cryptos/popular-cryptos.component';

// Non-Standalone (0)
// None

@NgModule({
  declarations: [
    // Non-Standalone Components (0)
    // None
  ],
  imports: [
    CommonModule,
    IonicModule,
    // Standalone Components (1)
    PopularCryptosComponent,
  ],
  exports: [
    // Standalone Components (1)
    PopularCryptosComponent,
  ]
})
export class CryptoModule { }
