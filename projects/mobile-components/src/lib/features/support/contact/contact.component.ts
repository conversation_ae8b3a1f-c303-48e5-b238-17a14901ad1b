import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';

export interface ContactComponentConfig {
  showContactForm?: boolean;
  showSupportChannels?: boolean;
  showTicketStatus?: boolean;
  formFields?: string[];
  theme?: 'light' | 'dark';
  [key: string]: any;
}

export interface ContactComponentData {
  contactInfo?: {
    phone?: string;
    email?: string;
    address?: string;
    hours?: string;
  };
  supportChannels?: {
    chat?: boolean;
    phone?: boolean;
    email?: boolean;
    faq?: boolean;
  };
  tickets?: any[];
  [key: string]: any;
}

@Component({
  selector: 'base-contact',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, IonicModule],
  template: `
    <div 
      class="{{className}} contact-wrapper"
      [class]="computedClasses"
      [attr.aria-label]="title || 'Contact support component'"
      [attr.aria-describedby]="description ? 'contact-desc' : null"
      role="region"
      [attr.aria-expanded]="!loading"
      [attr.aria-busy]="loading"
    >
      <!-- Loading State -->
      <div *ngIf="loading" class="flex justify-center items-center p-4">
        <ion-spinner name="circles"></ion-spinner>
        <span class="ml-2 text-sm text-gray-600">Loading contact information...</span>
      </div>

      <!-- Error State -->
      <div *ngIf="currentError && !loading" class="p-4 bg-red-50 border border-red-200 rounded-lg text-red-700 dark:bg-red-900 dark:border-red-700 dark:text-red-200">
        <ion-icon name="alert-circle" class="mr-2"></ion-icon>
        <span>{{ currentError.message || 'An error occurred while loading contact information' }}</span>
      </div>

      <!-- Main Content -->
      <div *ngIf="!loading && !currentError" class="contact-content">
        <!-- Header -->
        <div *ngIf="showHeader && (title || description)" class="contact-header mb-4">
          <h3 *ngIf="title" class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            <ion-icon name="headset" class="mr-2"></ion-icon>
            {{ title }}
          </h3>
          <p *ngIf="description" 
             [id]="'contact-desc'"
             class="text-sm text-gray-600 dark:text-gray-300">
            {{ description }}
          </p>
        </div>

        <!-- Contact Information -->
        <div *ngIf="contactInfo" class="contact-info mb-6">
          <h4 class="text-md font-medium text-gray-800 dark:text-gray-200 mb-3">Contact Information</h4>
          <div class="space-y-3">
            <ion-item *ngIf="contactInfo.phone" lines="none" class="contact-item">
              <ion-icon name="call" slot="start" color="primary"></ion-icon>
              <ion-label>
                <h3>Phone</h3>
                <p>{{ contactInfo.phone }}</p>
              </ion-label>
              <ion-button fill="clear" slot="end" (click)="callSupport()" [disabled]="disabled">
                <ion-icon name="call" slot="icon-only"></ion-icon>
              </ion-button>
            </ion-item>

            <ion-item *ngIf="contactInfo.email" lines="none" class="contact-item">
              <ion-icon name="mail" slot="start" color="primary"></ion-icon>
              <ion-label>
                <h3>Email</h3>
                <p>{{ contactInfo.email }}</p>
              </ion-label>
              <ion-button fill="clear" slot="end" (click)="emailSupport()" [disabled]="disabled">
                <ion-icon name="mail" slot="icon-only"></ion-icon>
              </ion-button>
            </ion-item>

            <ion-item *ngIf="contactInfo.hours" lines="none" class="contact-item">
              <ion-icon name="time" slot="start" color="primary"></ion-icon>
              <ion-label>
                <h3>Support Hours</h3>
                <p>{{ contactInfo.hours }}</p>
              </ion-label>
            </ion-item>
          </div>
        </div>

        <!-- Support Channels -->
        <div *ngIf="supportChannels?.length" class="support-channels mb-6">
          <h4 class="text-md font-medium text-gray-800 dark:text-gray-200 mb-3">Support Channels</h4>
          <div class="grid grid-cols-2 gap-3">
            <ion-button *ngFor="let channel of supportChannels" 
                       fill="outline" 
                       expand="block"
                       [disabled]="disabled"
                       (click)="selectSupportChannel(channel)">
              <ion-icon [name]="getChannelIcon(channel)" slot="start"></ion-icon>
              {{ getChannelLabel(channel) }}
            </ion-button>
          </div>
        </div>

        <!-- Contact Form -->
        <div *ngIf="formConfig && showContactForm" class="contact-form mb-6">
          <h4 class="text-md font-medium text-gray-800 dark:text-gray-200 mb-3">Send us a message</h4>
          <form [formGroup]="contactForm" (ngSubmit)="onContactSubmit()">
            <ion-item class="mb-3">
              <ion-input 
                formControlName="name"
                labelPlacement="floating"
                label="Your Name *"
                [class.ion-invalid]="contactForm.get('name')?.invalid && contactForm.get('name')?.touched"
                [disabled]="disabled">
              </ion-input>
            </ion-item>

            <ion-item class="mb-3">
              <ion-input 
                formControlName="email"
                type="email"
                labelPlacement="floating"
                label="Email Address *"
                [class.ion-invalid]="contactForm.get('email')?.invalid && contactForm.get('email')?.touched"
                [disabled]="disabled">
              </ion-input>
            </ion-item>

            <ion-item class="mb-3">
              <ion-select 
                formControlName="subject"
                labelPlacement="floating"
                label="Subject *"
                [disabled]="disabled">
                <ion-select-option value="general">General Inquiry</ion-select-option>
                <ion-select-option value="technical">Technical Support</ion-select-option>
                <ion-select-option value="billing">Billing Question</ion-select-option>
                <ion-select-option value="feedback">Feedback</ion-select-option>
              </ion-select>
            </ion-item>

            <ion-item class="mb-4">
              <ion-textarea 
                formControlName="message"
                labelPlacement="floating"
                label="Message *"
                rows="4"
                [class.ion-invalid]="contactForm.get('message')?.invalid && contactForm.get('message')?.touched"
                [disabled]="disabled">
              </ion-textarea>
            </ion-item>

            <ion-button 
              type="submit" 
              expand="block"
              [disabled]="contactForm.invalid || disabled || submitting"
              color="primary">
              <ion-icon name="send" slot="start" *ngIf="!submitting"></ion-icon>
              <ion-spinner name="circles" slot="start" *ngIf="submitting"></ion-spinner>
              {{ submitting ? 'Sending...' : 'Send Message' }}
            </ion-button>
          </form>
        </div>

        <!-- Ticket Status -->
        <div *ngIf="ticketData?.length" class="ticket-status">
          <h4 class="text-md font-medium text-gray-800 dark:text-gray-200 mb-3">Your Support Tickets</h4>
          <ion-list>
            <ion-item *ngFor="let ticket of ticketData" lines="full">
              <ion-label>
                <h3>{{ ticket.subject }}</h3>
                <p>Ticket #{{ ticket.id }}</p>
                <p class="text-sm text-gray-500">{{ ticket.date | date:'short' }}</p>
              </ion-label>
              <ion-chip 
                slot="end" 
                [color]="getTicketStatusColor(ticket.status)">
                {{ ticket.status }}
              </ion-chip>
            </ion-item>
          </ion-list>
        </div>

        <!-- Footer -->
        <div *ngIf="showFooter" class="contact-footer mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div class="text-center">
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">
              Need immediate assistance?
            </p>
            <ion-button 
              fill="clear" 
              size="small"
              [disabled]="disabled"
              (click)="openEmergencyContact()">
              <ion-icon name="warning" slot="start" color="warning"></ion-icon>
              Emergency Contact
            </ion-button>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./contact.component.css']
})
export class ContactComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  // Base properties
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() disabled: boolean = false;
  @Input() loading: boolean = false;

  // Feature-specific properties
  @Input() title?: string = 'Contact Support';
  @Input() description?: string;
  @Input() showHeader: boolean = true;
  @Input() showFooter: boolean = true;
  @Input() showContactForm: boolean = true;
  
  // Feature-specific data
  @Input() contactInfo?: ContactComponentData['contactInfo'];
  @Input() supportChannels?: string[] = ['chat', 'phone', 'email'];
  @Input() ticketData?: any[];
  @Input() formConfig?: any = { enabled: true };

  // Output events
  @Output() configChange = new EventEmitter<ContactComponentConfig>();
  @Output() dataChange = new EventEmitter<ContactComponentData>();
  @Output() actionTriggered = new EventEmitter<string>();
  @Output() error = new EventEmitter<Error>();
  @Output() contactSubmitted = new EventEmitter<any>();
  @Output() supportChannelSelected = new EventEmitter<string>();

  // Component state
  currentError: Error | null = null;
  submitting: boolean = false;
  contactForm: FormGroup;

  constructor(private formBuilder: FormBuilder) {
    this.contactForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      subject: ['', Validators.required],
      message: ['', [Validators.required, Validators.minLength(10)]]
    });
  }

  ngOnInit(): void {
    this.initializeComponent();
    this.setupFormValidation();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeComponent(): void {
    // Set default contact info if not provided
    if (!this.contactInfo) {
      this.contactInfo = {
        phone: '******-SUPPORT',
        email: '<EMAIL>',
        hours: 'Mon-Fri 9AM-5PM EST'
      };
    }
    
    console.log('ContactComponent initialized');
  }

  private setupFormValidation(): void {
    this.contactForm.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(value => {
        this.dataChange.emit({ formData: value });
      });
  }

  get computedClasses(): string {
    return [
      this.getSizeClasses(),
      this.getVariantClasses(),
      this.getRoundedClasses(),
      this.getStateClasses(),
      'contact-component',
      this.className
    ].filter(Boolean).join(' ');
  }

  private getSizeClasses(): string {
    const sizeMap = {
      xs: 'text-xs p-2',
      sm: 'text-sm p-3',
      md: 'text-base p-4',
      lg: 'text-lg p-5',
      xl: 'text-xl p-6'
    };
    return sizeMap[this.size] || sizeMap.md;
  }

  private getVariantClasses(): string {
    const variantMap = {
      default: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white',
      primary: 'bg-primary text-white',
      secondary: 'bg-secondary text-white',
      success: 'bg-green-500 text-white',
      warning: 'bg-yellow-500 text-white',
      danger: 'bg-red-500 text-white'
    };
    return variantMap[this.variant] || variantMap.default;
  }

  private getRoundedClasses(): string {
    const roundedMap = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };
    return roundedMap[this.rounded] || roundedMap.md;
  }

  private getStateClasses(): string {
    const classes = [];
    if (this.disabled) classes.push('opacity-50 cursor-not-allowed');
    if (this.loading) classes.push('animate-pulse');
    return classes.join(' ');
  }

  // Feature-specific methods
  async onContactSubmit(): Promise<void> {
    if (this.contactForm.invalid) return;

    this.submitting = true;
    try {
      const formData = this.contactForm.value;
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      this.contactSubmitted.emit(formData);
      this.actionTriggered.emit('contact-submitted');
      
      // Reset form after successful submission
      this.contactForm.reset();
      
      console.log('Contact form submitted:', formData);
    } catch (error) {
      this.handleError(error as Error);
    } finally {
      this.submitting = false;
    }
  }

  callSupport(): void {
    if (this.contactInfo?.phone) {
      window.open(`tel:${this.contactInfo.phone}`, '_self');
      this.actionTriggered.emit('call-support');
    }
  }

  emailSupport(): void {
    if (this.contactInfo?.email) {
      window.open(`mailto:${this.contactInfo.email}`, '_self');
      this.actionTriggered.emit('email-support');
    }
  }

  selectSupportChannel(channel: string): void {
    this.supportChannelSelected.emit(channel);
    this.actionTriggered.emit(`channel-selected-${channel}`);
    console.log('Support channel selected:', channel);
  }

  openEmergencyContact(): void {
    this.actionTriggered.emit('emergency-contact');
    console.log('Emergency contact opened');
  }

  getChannelIcon(channel: string): string {
    const iconMap: { [key: string]: string } = {
      chat: 'chatbubbles',
      phone: 'call',
      email: 'mail',
      faq: 'help-circle'
    };
    return iconMap[channel] || 'help-circle';
  }

  getChannelLabel(channel: string): string {
    const labelMap: { [key: string]: string } = {
      chat: 'Live Chat',
      phone: 'Phone Support',
      email: 'Email Support',
      faq: 'FAQ & Help'
    };
    return labelMap[channel] || channel;
  }

  getTicketStatusColor(status: string): string {
    const colorMap: { [key: string]: string } = {
      open: 'primary',
      pending: 'warning',
      resolved: 'success',
      closed: 'medium'
    };
    return colorMap[status.toLowerCase()] || 'medium';
  }

  private handleError(error: Error): void {
    this.currentError = error;
    this.error.emit(error);
    console.error('ContactComponent error:', error);
  }
}
