/* Contact Component Styles */
.contact-component {
  @apply transition-all duration-200 ease-in-out;
}

.contact-wrapper {
  @apply w-full;
}

.contact-item {
  @apply bg-gray-50 dark:bg-gray-700 rounded-lg mb-2 border border-gray-200 dark:border-gray-600;
}

.contact-item ion-label h3 {
  @apply font-medium text-gray-800 dark:text-gray-200;
}

.contact-item ion-label p {
  @apply text-gray-600 dark:text-gray-400;
}

.contact-form ion-item {
  @apply mb-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg;
}

.contact-form ion-button[type="submit"] {
  @apply mt-4;
}

.support-channels {
  @apply space-y-2;
}

.ticket-status ion-item {
  @apply border-l-4 border-blue-500 bg-blue-50 dark:bg-blue-900 dark:bg-opacity-20;
}

.contact-footer {
  @apply text-center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .support-channels .grid {
    @apply grid-cols-1 gap-2;
  }
  
  .contact-wrapper {
    @apply p-2;
  }
}

/* Loading state */
.contact-wrapper.animate-pulse {
  @apply opacity-75;
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .contact-component {
    @apply bg-gray-800 text-white;
  }
}

/* Accessibility improvements */
.contact-wrapper:focus-within {
  @apply ring-2 ring-primary ring-opacity-50;
}

ion-button:focus {
  @apply ring-2 ring-offset-2 ring-primary;
}

/* Form validation styles */
.ion-invalid {
  @apply border-red-500 bg-red-50 dark:bg-red-900 dark:bg-opacity-20;
}

/* Animation for form submission */
.submitting {
  @apply opacity-75 pointer-events-none;
}