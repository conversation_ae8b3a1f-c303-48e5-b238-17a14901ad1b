import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormBuilder } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { By } from '@angular/platform-browser';

import { ContactComponent, ContactComponentConfig, ContactComponentData } from './contact.component';

describe('ContactComponent', () => {
  let component: ContactComponent;
  let fixture: ComponentFixture<ContactComponent>;
  let formBuilder: FormBuilder;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ContactComponent, ReactiveFormsModule, IonicModule.forRoot()]
    }).compileComponents();

    fixture = TestBed.createComponent(ContactComponent);
    component = fixture.componentInstance;
    formBuilder = TestBed.inject(FormBuilder);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Component Initialization', () => {
    it('should initialize with default values', () => {
      expect(component.size).toBe('md');
      expect(component.variant).toBe('default');
      expect(component.rounded).toBe('md');
      expect(component.disabled).toBe(false);
      expect(component.loading).toBe(false);
      expect(component.showHeader).toBe(true);
      expect(component.showFooter).toBe(true);
      expect(component.showContactForm).toBe(true);
      expect(component.title).toBe('Contact Support');
    });

    it('should initialize contact form with validators', () => {
      expect(component.contactForm).toBeDefined();
      expect(component.contactForm.get('name')?.hasError('required')).toBe(true);
      expect(component.contactForm.get('email')?.hasError('required')).toBe(true);
      expect(component.contactForm.get('subject')?.hasError('required')).toBe(true);
      expect(component.contactForm.get('message')?.hasError('required')).toBe(true);
    });

    it('should set default contact info if not provided', () => {
      component.ngOnInit();
      expect(component.contactInfo).toBeDefined();
      expect(component.contactInfo?.phone).toBe('******-SUPPORT');
      expect(component.contactInfo?.email).toBe('<EMAIL>');
    });
  });

  describe('Input Properties', () => {
    it('should accept custom className', () => {
      component.className = 'custom-class';
      expect(component.className).toBe('custom-class');
    });

    it('should accept different sizes', () => {
      component.size = 'lg';
      expect(component.size).toBe('lg');
    });

    it('should accept different variants', () => {
      component.variant = 'primary';
      expect(component.variant).toBe('primary');
    });

    it('should accept custom contact info', () => {
      const customContactInfo = {
        phone: '******-TEST',
        email: '<EMAIL>',
        hours: '24/7'
      };
      component.contactInfo = customContactInfo;
      expect(component.contactInfo).toEqual(customContactInfo);
    });

    it('should accept custom support channels', () => {
      const channels = ['chat', 'email'];
      component.supportChannels = channels;
      expect(component.supportChannels).toEqual(channels);
    });
  });

  describe('CSS Classes', () => {
    it('should generate correct size classes', () => {
      component.size = 'lg';
      const classes = component.computedClasses;
      expect(classes).toContain('text-lg p-5');
    });

    it('should generate correct variant classes', () => {
      component.variant = 'primary';
      const classes = component.computedClasses;
      expect(classes).toContain('bg-primary text-white');
    });

    it('should generate correct rounded classes', () => {
      component.rounded = 'full';
      const classes = component.computedClasses;
      expect(classes).toContain('rounded-full');
    });

    it('should apply disabled state classes', () => {
      component.disabled = true;
      const classes = component.computedClasses;
      expect(classes).toContain('opacity-50 cursor-not-allowed');
    });

    it('should apply loading state classes', () => {
      component.loading = true;
      const classes = component.computedClasses;
      expect(classes).toContain('animate-pulse');
    });
  });

  describe('Template Rendering', () => {
    it('should show loading state when loading is true', () => {
      component.loading = true;
      fixture.detectChanges();
      
      const loadingElement = fixture.debugElement.query(By.css('ion-spinner'));
      expect(loadingElement).toBeTruthy();
    });

    it('should show error state when error exists', () => {
      component.currentError = new Error('Test error');
      component.loading = false;
      fixture.detectChanges();
      
      const errorElement = fixture.debugElement.query(By.css('.bg-red-50'));
      expect(errorElement).toBeTruthy();
    });

    it('should show header when showHeader is true and title exists', () => {
      component.showHeader = true;
      component.title = 'Test Title';
      component.loading = false;
      component.currentError = null;
      fixture.detectChanges();
      
      const headerElement = fixture.debugElement.query(By.css('.contact-header h3'));
      expect(headerElement?.nativeElement.textContent).toContain('Test Title');
    });

    it('should show contact information when provided', () => {
      component.contactInfo = {
        phone: '******-TEST',
        email: '<EMAIL>'
      };
      component.loading = false;
      component.currentError = null;
      fixture.detectChanges();
      
      const phoneElement = fixture.debugElement.query(By.css('ion-item ion-label'));
      expect(phoneElement).toBeTruthy();
    });

    it('should show support channels when provided', () => {
      component.supportChannels = ['chat', 'phone'];
      component.loading = false;
      component.currentError = null;
      fixture.detectChanges();
      
      const channelButtons = fixture.debugElement.queryAll(By.css('.support-channels ion-button'));
      expect(channelButtons.length).toBe(2);
    });

    it('should show contact form when showContactForm is true', () => {
      component.showContactForm = true;
      component.formConfig = { enabled: true };
      component.loading = false;
      component.currentError = null;
      fixture.detectChanges();
      
      const formElement = fixture.debugElement.query(By.css('.contact-form form'));
      expect(formElement).toBeTruthy();
    });
  });

  describe('Form Interaction', () => {
    beforeEach(() => {
      component.loading = false;
      component.currentError = null;
      component.showContactForm = true;
      component.formConfig = { enabled: true };
      fixture.detectChanges();
    });

    it('should validate required fields', () => {
      const form = component.contactForm;
      expect(form.invalid).toBe(true);
      
      form.patchValue({
        name: 'John Doe',
        email: '<EMAIL>',
        subject: 'general',
        message: 'This is a test message with enough characters'
      });
      
      expect(form.valid).toBe(true);
    });

    it('should validate email format', () => {
      const emailControl = component.contactForm.get('email');
      emailControl?.setValue('invalid-email');
      expect(emailControl?.hasError('email')).toBe(true);
      
      emailControl?.setValue('<EMAIL>');
      expect(emailControl?.hasError('email')).toBe(false);
    });

    it('should handle form submission', async () => {
      spyOn(component.contactSubmitted, 'emit');
      spyOn(component.actionTriggered, 'emit');
      
      component.contactForm.patchValue({
        name: 'John Doe',
        email: '<EMAIL>',
        subject: 'general',
        message: 'This is a test message with enough characters'
      });
      
      await component.onContactSubmit();
      
      expect(component.contactSubmitted.emit).toHaveBeenCalled();
      expect(component.actionTriggered.emit).toHaveBeenCalledWith('contact-submitted');
    });
  });

  describe('Event Emissions', () => {
    it('should emit configChange when configuration changes', () => {
      spyOn(component.configChange, 'emit');
      
      const newConfig: ContactComponentConfig = { showContactForm: false };
      component.configChange.emit(newConfig);
      
      expect(component.configChange.emit).toHaveBeenCalledWith(newConfig);
    });

    it('should emit dataChange when form data changes', () => {
      spyOn(component.dataChange, 'emit');
      
      component.contactForm.patchValue({ name: 'John' });
      
      expect(component.dataChange.emit).toHaveBeenCalled();
    });

    it('should emit actionTriggered for call support', () => {
      spyOn(component.actionTriggered, 'emit');
      spyOn(window, 'open');
      
      component.contactInfo = { phone: '******-TEST' };
      component.callSupport();
      
      expect(component.actionTriggered.emit).toHaveBeenCalledWith('call-support');
      expect(window.open).toHaveBeenCalledWith('tel:******-TEST', '_self');
    });

    it('should emit actionTriggered for email support', () => {
      spyOn(component.actionTriggered, 'emit');
      spyOn(window, 'open');
      
      component.contactInfo = { email: '<EMAIL>' };
      component.emailSupport();
      
      expect(component.actionTriggered.emit).toHaveBeenCalledWith('email-support');
      expect(window.open).toHaveBeenCalledWith('mailto:<EMAIL>', '_self');
    });

    it('should emit supportChannelSelected when channel is selected', () => {
      spyOn(component.supportChannelSelected, 'emit');
      spyOn(component.actionTriggered, 'emit');
      
      component.selectSupportChannel('chat');
      
      expect(component.supportChannelSelected.emit).toHaveBeenCalledWith('chat');
      expect(component.actionTriggered.emit).toHaveBeenCalledWith('channel-selected-chat');
    });
  });

  describe('Helper Methods', () => {
    it('should return correct channel icons', () => {
      expect(component.getChannelIcon('chat')).toBe('chatbubbles');
      expect(component.getChannelIcon('phone')).toBe('call');
      expect(component.getChannelIcon('email')).toBe('mail');
      expect(component.getChannelIcon('unknown')).toBe('help-circle');
    });

    it('should return correct channel labels', () => {
      expect(component.getChannelLabel('chat')).toBe('Live Chat');
      expect(component.getChannelLabel('phone')).toBe('Phone Support');
      expect(component.getChannelLabel('email')).toBe('Email Support');
      expect(component.getChannelLabel('unknown')).toBe('unknown');
    });

    it('should return correct ticket status colors', () => {
      expect(component.getTicketStatusColor('open')).toBe('primary');
      expect(component.getTicketStatusColor('pending')).toBe('warning');
      expect(component.getTicketStatusColor('resolved')).toBe('success');
      expect(component.getTicketStatusColor('unknown')).toBe('medium');
    });
  });

  describe('Error Handling', () => {
    it('should handle errors properly', () => {
      spyOn(component.error, 'emit');
      spyOn(console, 'error');
      
      const testError = new Error('Test error');
      component['handleError'](testError);
      
      expect(component.currentError).toBe(testError);
      expect(component.error.emit).toHaveBeenCalledWith(testError);
      expect(console.error).toHaveBeenCalledWith('ContactComponent error:', testError);
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      component.title = 'Contact Support';
      component.description = 'Get help from our support team';
      component.loading = false;
      component.currentError = null;
      fixture.detectChanges();
      
      const wrapper = fixture.debugElement.query(By.css('.contact-wrapper'));
      expect(wrapper.nativeElement.getAttribute('aria-label')).toBe('Contact Support');
      expect(wrapper.nativeElement.getAttribute('aria-describedby')).toBe('contact-desc');
      expect(wrapper.nativeElement.getAttribute('role')).toBe('region');
    });

    it('should indicate loading state in ARIA attributes', () => {
      component.loading = true;
      fixture.detectChanges();
      
      const wrapper = fixture.debugElement.query(By.css('.contact-wrapper'));
      expect(wrapper.nativeElement.getAttribute('aria-busy')).toBe('true');
      expect(wrapper.nativeElement.getAttribute('aria-expanded')).toBe('false');
    });
  });

  describe('Component Lifecycle', () => {
    it('should initialize properly on ngOnInit', () => {
      spyOn(component, 'initializeComponent' as any);
      spyOn(component, 'setupFormValidation' as any);
      
      component.ngOnInit();
      
      expect(component['initializeComponent']).toHaveBeenCalled();
      expect(component['setupFormValidation']).toHaveBeenCalled();
    });

    it('should cleanup properly on ngOnDestroy', () => {
      spyOn(component['destroy$'], 'next');
      spyOn(component['destroy$'], 'complete');
      
      component.ngOnDestroy();
      
      expect(component['destroy$'].next).toHaveBeenCalled();
      expect(component['destroy$'].complete).toHaveBeenCalled();
    });
  });
});
