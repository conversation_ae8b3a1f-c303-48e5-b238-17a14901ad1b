import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import Support components
// Standalone (4)
import { ContactComponent } from './contact/contact.component';
import { InfoComponent } from './info/info.component';
import { PendingTicketsComponent } from './pending-tickets/pending-tickets.component';

// Non-Standalone (0)
// None

@NgModule({
  declarations: [
    // Non-Standalone Components (0)
    // None
  ],
  imports: [
    CommonModule,
    IonicModule,
    // Standalone Components (3)
    ContactComponent,
    InfoComponent,
    PendingTicketsComponent,
  ],
  exports: [
    // Standalone Components (3)
    ContactComponent,
    InfoComponent,
    PendingTicketsComponent,
  ]
})
export class SupportModule { }
