<!-- Contact Us Component Template -->
<div class="contactus-container">
  <!-- Header Section -->
  <div class="contactus-header">
    <ion-segment [(ngModel)]="activeSection" mode="md">
      <ion-segment-button *ngIf="config.showContactInfo" value="contact-info">
        <ion-label>Contact Info</ion-label>
      </ion-segment-button>
      <ion-segment-button *ngIf="config.enableContactForm" value="contact-form">
        <ion-label>Send Message</ion-label>
      </ion-segment-button>
    </ion-segment>
  </div>

  <!-- Contact Information Section -->
  <div *ngIf="activeSection === 'contact-info' && config.showContactInfo" class="contact-info-section">
    <ion-card>
      <ion-card-header>
        <ion-card-title>Get in Touch</ion-card-title>
        <ion-card-subtitle>We're here to help you</ion-card-subtitle>
      </ion-card-header>

      <ion-card-content>
        <!-- Contact Details -->
        <div class="contact-details">
          <ion-item lines="none">
            <ion-icon name="call" slot="start" color="primary"></ion-icon>
            <ion-label>
              <h3>Phone</h3>
              <p>{{ data.contactInfo.phone }}</p>
            </ion-label>
          </ion-item>

          <ion-item lines="none">
            <ion-icon name="mail" slot="start" color="primary"></ion-icon>
            <ion-label>
              <h3>Email</h3>
              <p>{{ data.contactInfo.email }}</p>
            </ion-label>
          </ion-item>

          <ion-item lines="none">
            <ion-icon name="location" slot="start" color="primary"></ion-icon>
            <ion-label>
              <h3>Address</h3>
              <p>
                {{ data.contactInfo.address.street }}<br>
                {{ data.contactInfo.address.city }}, {{ data.contactInfo.address.state }} {{ data.contactInfo.address.zipCode }}<br>
                {{ data.contactInfo.address.country }}
              </p>
            </ion-label>
          </ion-item>
        </div>

        <!-- Office Hours -->
        <div *ngIf="config.showOfficeHours" class="office-hours">
          <h3>Office Hours</h3>
          <ion-list>
            <ion-item lines="none">
              <ion-icon name="time" slot="start" color="secondary"></ion-icon>
              <ion-label>
                <h4>Weekdays</h4>
                <p>{{ data.officeHours.weekdays }}</p>
              </ion-label>
            </ion-item>
            <ion-item lines="none">
              <ion-icon name="time" slot="start" color="secondary"></ion-icon>
              <ion-label>
                <h4>Weekends</h4>
                <p>{{ data.officeHours.weekends }}</p>
              </ion-label>
            </ion-item>
            <ion-item lines="none">
              <ion-icon name="calendar" slot="start" color="secondary"></ion-icon>
              <ion-label>
                <h4>Holidays</h4>
                <p>{{ data.officeHours.holidays }}</p>
              </ion-label>
            </ion-item>
          </ion-list>
        </div>

        <!-- Social Links -->
        <div *ngIf="data.socialLinks && data.socialLinks.length > 0" class="social-links">
          <h3>Follow Us</h3>
          <div class="social-buttons">
            <ion-button 
              *ngFor="let social of data.socialLinks"
              fill="outline"
              size="default"
              (click)="onSocialLinkClick(social.url)"
              [attr.aria-label]="'Visit our ' + social.platform + ' page'"
            >
              <ion-icon [name]="social.icon" slot="start"></ion-icon>
              {{ social.platform }}
            </ion-button>
          </div>
        </div>

        <!-- Departments -->
        <div *ngIf="data.departments && data.departments.length > 0" class="departments">
          <h3>Departments</h3>
          <ion-list>
            <ion-item 
              *ngFor="let dept of data.departments"
              button
              (click)="onDepartmentSelect(dept.id)"
              [attr.aria-label]="'Contact ' + dept.name + ' department'"
            >
              <ion-label>
                <h4>{{ dept.name }}</h4>
                <p>{{ dept.description }}</p>
                <p class="department-email">{{ dept.email }}</p>
              </ion-label>
              <ion-icon name="chevron-forward" slot="end"></ion-icon>
            </ion-item>
          </ion-list>
        </div>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Contact Form Section -->
  <div *ngIf="activeSection === 'contact-form' && config.enableContactForm" class="contact-form-section">
    <ion-card>
      <ion-card-header>
        <ion-card-title>Send us a Message</ion-card-title>
        <ion-card-subtitle>
          We typically respond within {{ config.estimatedResponseTime || '24 hours' }}
        </ion-card-subtitle>
      </ion-card-header>

      <ion-card-content>
        <!-- Submission Status -->
        <div *ngIf="data.submissionStatus" class="submission-status">
          <ion-item 
            [color]="data.submissionStatus.success ? 'success' : 'danger'"
            lines="none"
          >
            <ion-icon 
              [name]="data.submissionStatus.success ? 'checkmark-circle' : 'alert-circle'" 
              slot="start"
            ></ion-icon>
            <ion-label>
              <h3>{{ data.submissionStatus.success ? 'Success!' : 'Error' }}</h3>
              <p>{{ data.submissionStatus.message }}</p>
              <p *ngIf="data.submissionStatus.referenceNumber"
                 class="reference-number">
                Reference: {{ data.submissionStatus.referenceNumber }}
              </p>
            </ion-label>
          </ion-item>
        </div>

        <!-- Contact Form -->
        <form [formGroup]="contactForm" (ngSubmit)="onSubmit()" class="contact-form">
          <!-- Name Field -->
          <ion-item class="form-item" [class.item-has-error]="isFieldInvalid('name')">
            <ion-label position="stacked">
              Name <span *ngIf="config.requiredFields.includes('name')" class="required">*</span>
            </ion-label>
            <ion-input 
              type="text"
              formControlName="name"
              placeholder="Enter your full name"
              [class.ion-invalid]="isFieldInvalid('name')"
              [class.ion-touched]="contactForm.get('name')?.touched"
            ></ion-input>
            <ion-note *ngIf="hasFieldError('name')" slot="error">
              {{ getFieldError('name') }}
            </ion-note>
          </ion-item>

          <!-- Email Field -->
          <ion-item class="form-item" [class.item-has-error]="isFieldInvalid('email')">
            <ion-label position="stacked">
              Email <span *ngIf="config.requiredFields.includes('email')" class="required">*</span>
            </ion-label>
            <ion-input 
              type="email"
              formControlName="email"
              placeholder="Enter your email address"
              [class.ion-invalid]="isFieldInvalid('email')"
              [class.ion-touched]="contactForm.get('email')?.touched"
            ></ion-input>
            <ion-note *ngIf="hasFieldError('email')" slot="error">
              {{ getFieldError('email') }}
            </ion-note>
          </ion-item>

          <!-- Phone Field -->
          <ion-item class="form-item" [class.item-has-error]="isFieldInvalid('phone')">
            <ion-label position="stacked">
              Phone <span *ngIf="config.requiredFields.includes('phone')" class="required">*</span>
            </ion-label>
            <ion-input 
              type="tel"
              formControlName="phone"
              placeholder="Enter your phone number"
              [class.ion-invalid]="isFieldInvalid('phone')"
              [class.ion-touched]="contactForm.get('phone')?.touched"
            ></ion-input>
            <ion-note *ngIf="hasFieldError('phone')" slot="error">
              {{ getFieldError('phone') }}
            </ion-note>
          </ion-item>

          <!-- Department Selection -->
          <ion-item class="form-item" [class.item-has-error]="isFieldInvalid('department')">
            <ion-label position="stacked">
              Department <span *ngIf="config.requiredFields.includes('department')" class="required">*</span>
            </ion-label>
            <ion-select 
              formControlName="department"
              placeholder="Select a department"
              [class.ion-invalid]="isFieldInvalid('department')"
              [class.ion-touched]="contactForm.get('department')?.touched"
            >
              <ion-select-option 
                *ngFor="let dept of data.departments" 
                [value]="dept.id"
              >
                {{ dept.name }}
              </ion-select-option>
            </ion-select>
            <ion-note *ngIf="hasFieldError('department')" slot="error">
              {{ getFieldError('department') }}
            </ion-note>
          </ion-item>

          <!-- Subject Field -->
          <ion-item class="form-item" [class.item-has-error]="isFieldInvalid('subject')">
            <ion-label position="stacked">
              Subject <span *ngIf="config.requiredFields.includes('subject')" class="required">*</span>
            </ion-label>
            <ion-input 
              type="text"
              formControlName="subject"
              placeholder="Enter the subject of your message"
              [class.ion-invalid]="isFieldInvalid('subject')"
              [class.ion-touched]="contactForm.get('subject')?.touched"
            ></ion-input>
            <ion-note *ngIf="hasFieldError('subject')" slot="error">
              {{ getFieldError('subject') }}
            </ion-note>
          </ion-item>

          <!-- Priority Field -->
          <ion-item class="form-item">
            <ion-label position="stacked">Priority</ion-label>
            <ion-select formControlName="priority" placeholder="Select priority level">
              <ion-select-option value="low">Low</ion-select-option>
              <ion-select-option value="medium">Medium</ion-select-option>
              <ion-select-option value="high">High</ion-select-option>
              <ion-select-option value="urgent">Urgent</ion-select-option>
            </ion-select>
          </ion-item>

          <!-- Message Field -->
          <ion-item class="form-item message-item" [class.item-has-error]="isFieldInvalid('message')">
            <ion-label position="stacked">
              Message <span *ngIf="config.requiredFields.includes('message')" class="required">*</span>
            </ion-label>
            <ion-textarea 
              formControlName="message"
              placeholder="Enter your message here..."
              rows="4"
              [class.ion-invalid]="isFieldInvalid('message')"
              [class.ion-touched]="contactForm.get('message')?.touched"
            ></ion-textarea>
            <ion-note *ngIf="hasFieldError('message')" slot="error">
              {{ getFieldError('message') }}
            </ion-note>
          </ion-item>

          <!-- File Upload Section -->
          <div *ngIf="config.enableFileUpload" class="file-upload-section">
            <ion-item class="form-item">
              <ion-label position="stacked">Attachments</ion-label>
              <div class="file-input-container">
                <input 
                  type="file"
                  multiple
                  (change)="onFileSelect($event)"
                  [accept]="config.allowedFileTypes.join(',')"
                  class="file-input"
                  id="file-upload"
                />
                <ion-button 
                  fill="outline"
                  size="default"
                  (click)="triggerFileInput()"
                >
                  <ion-icon name="attach" slot="start"></ion-icon>
                  Choose Files
                </ion-button>
              </div>
              <ion-note class="file-info">
                Max size: {{ config.maxFileSize }}MB per file<br>
                Allowed types: {{ config.allowedFileTypes.join(', ') }}
              </ion-note>
            </ion-item>

            <!-- Selected Files Display -->
            <div *ngIf="selectedFiles.length > 0" class="selected-files">
              <h4>Selected Files ({{ selectedFiles.length }})</h4>
              <ion-list>
                <ion-item *ngFor="let file of selectedFiles; let i = index">
                  <ion-icon name="document" slot="start"></ion-icon>
                  <ion-label>
                    <h3>{{ file.name }}</h3>
                    <p>{{ formatFileSize(file.size) }}</p>
                  </ion-label>
                  <ion-button 
                    fill="clear"
                    size="small"
                    (click)="removeFile(i)"
                    slot="end"
                    [attr.aria-label]="'Remove ' + file.name"
                  >
                    <ion-icon name="close" color="danger"></ion-icon>
                  </ion-button>
                </ion-item>
              </ion-list>
              <div class="total-size">
                Total size: {{ formatFileSize(getTotalFileSize()) }}
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="submit-section">
            <ion-button 
              type="submit"
              expand="block"
              size="large"
              [disabled]="contactForm.invalid || isSubmitting"
              class="submit-button"
            >
              <ion-icon 
                *ngIf="!isSubmitting" 
                name="send" 
                slot="start"
              ></ion-icon>
              <ion-spinner 
                *ngIf="isSubmitting" 
                name="crescent" 
                slot="start"
              ></ion-spinner>
              {{ isSubmitting ? 'Sending...' : 'Send Message' }}
            </ion-button>
          </div>

          <!-- Auto-reply Notice -->
          <div *ngIf="config.autoReplyEnabled" class="auto-reply-notice">
            <ion-note>
              <ion-icon name="information-circle" class="info-icon"></ion-icon>
              You will receive an automatic confirmation email once your message is submitted.
            </ion-note>
          </div>
        </form>
      </ion-card-content>
    </ion-card>
  </div>
</div>
