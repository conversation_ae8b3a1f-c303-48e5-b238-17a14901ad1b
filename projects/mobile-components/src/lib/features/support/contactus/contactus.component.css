/* Contact Us Component Styles */

.contactus-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
}

/* Header Section */
.contactus-header {
  margin-bottom: 1.5rem;
}

.contactus-header ion-segment {
  --background: var(--ion-color-light);
  border-radius: 8px;
  padding: 4px;
}

.contactus-header ion-segment-button {
  --color: var(--ion-color-medium);
  --color-checked: var(--ion-color-primary);
  --indicator-color: var(--ion-color-primary);
  --indicator-height: 100%;
  border-radius: 6px;
  margin: 0 2px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.contactus-header ion-segment-button:hover {
  --color: var(--ion-color-primary);
}

/* Contact Info Section */
.contact-info-section {
  animation: fadeIn 0.3s ease-in;
}

.contact-details {
  margin-bottom: 2rem;
}

.contact-details ion-item {
  --padding-start: 0;
  --padding-end: 0;
  --inner-padding-end: 0;
  margin-bottom: 1rem;
  border-radius: 8px;
  --background: var(--ion-color-light);
}

.contact-details ion-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
}

.contact-details h3 {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--ion-color-dark);
}

.contact-details p {
  margin: 0;
  color: var(--ion-color-medium-shade);
  line-height: 1.4;
}

/* Office Hours */
.office-hours {
  margin: 2rem 0;
}

.office-hours h3 {
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--ion-color-dark);
  padding-left: 0.5rem;
}

.office-hours ion-item {
  --padding-start: 0.5rem;
  --inner-padding-end: 0.5rem;
  margin-bottom: 0.5rem;
  border-radius: 6px;
  --background: rgba(var(--ion-color-primary-rgb), 0.05);
}

.office-hours h4 {
  font-weight: 500;
  margin-bottom: 0.25rem;
  color: var(--ion-color-primary);
}

/* Social Links */
.social-links {
  margin: 2rem 0;
}

.social-links h3 {
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--ion-color-dark);
  padding-left: 0.5rem;
}

.social-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  padding: 0 0.5rem;
}

.social-buttons ion-button {
  --border-radius: 20px;
  --padding-start: 1rem;
  --padding-end: 1rem;
  flex: 1;
  min-width: 120px;
  max-width: 200px;
  transition: transform 0.2s ease;
}

.social-buttons ion-button:hover {
  transform: translateY(-2px);
}

/* Departments */
.departments {
  margin: 2rem 0;
}

.departments h3 {
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--ion-color-dark);
  padding-left: 0.5rem;
}

.departments ion-item {
  --border-radius: 8px;
  margin-bottom: 0.5rem;
  --background: var(--ion-color-light);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.departments ion-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.departments h4 {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--ion-color-primary);
}

.department-email {
  font-size: 0.875rem;
  color: var(--ion-color-primary) !important;
  font-weight: 500;
}

/* Contact Form Section */
.contact-form-section {
  animation: fadeIn 0.3s ease-in;
}

.contact-form {
  margin-top: 1rem;
}

/* Form Items */
.form-item {
  --padding-start: 0;
  --padding-end: 0;
  --inner-padding-end: 0;
  margin-bottom: 1.5rem;
  border-radius: 8px;
  --background: var(--ion-color-light);
  --border-width: 1px;
  --border-color: var(--ion-color-light-shade);
  transition: all 0.3s ease;
}

.form-item:focus-within {
  --border-color: var(--ion-color-primary);
  --background: rgba(var(--ion-color-primary-rgb), 0.05);
}

.form-item.item-has-error {
  --border-color: var(--ion-color-danger);
  --background: rgba(var(--ion-color-danger-rgb), 0.05);
}

.form-item ion-label {
  font-weight: 500;
  color: var(--ion-color-dark);
}

.required {
  color: var(--ion-color-danger);
  font-weight: 600;
}

.message-item {
  --min-height: 120px;
}

/* Submission Status */
.submission-status {
  margin-bottom: 2rem;
}

.submission-status ion-item {
  --border-radius: 8px;
  --padding-start: 1rem;
  --padding-end: 1rem;
}

.reference-number {
  font-weight: 600;
  font-family: monospace;
  font-size: 0.875rem;
}

/* File Upload */
.file-upload-section {
  margin: 2rem 0;
}

.file-input-container {
  width: 100%;
  margin-top: 0.5rem;
}

.file-input {
  display: none;
}

.file-input-container ion-button {
  --border-radius: 8px;
  width: 100%;
}

.file-info {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  line-height: 1.4;
}

.selected-files {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(var(--ion-color-light-rgb), 0.5);
  border-radius: 8px;
  border: 1px dashed var(--ion-color-medium);
}

.selected-files h4 {
  margin: 0 0 1rem 0;
  color: var(--ion-color-dark);
  font-weight: 600;
}

.selected-files ion-item {
  --background: white;
  --border-radius: 6px;
  margin-bottom: 0.5rem;
}

.total-size {
  margin-top: 1rem;
  text-align: center;
  font-weight: 500;
  color: var(--ion-color-medium-shade);
}

/* Submit Section */
.submit-section {
  margin: 2rem 0;
}

.submit-button {
  --border-radius: 8px;
  --padding-top: 1rem;
  --padding-bottom: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.submit-button:not([disabled]):hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(var(--ion-color-primary-rgb), 0.3);
}

/* Auto-reply Notice */
.auto-reply-notice {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(var(--ion-color-primary-rgb), 0.05);
  border-radius: 8px;
  border-left: 4px solid var(--ion-color-primary);
}

.auto-reply-notice ion-note {
  display: flex;
  align-items: center;
  color: var(--ion-color-primary-shade);
  font-size: 0.875rem;
  line-height: 1.4;
}

.info-icon {
  margin-right: 0.5rem;
  font-size: 1rem;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .contactus-container {
    padding: 0.5rem;
  }
  
  .social-buttons {
    flex-direction: column;
  }
  
  .social-buttons ion-button {
    max-width: none;
  }
  
  .contact-details ion-icon {
    font-size: 1.25rem;
    margin-right: 0.75rem;
  }
}

@media (max-width: 480px) {
  .contactus-header ion-segment {
    flex-direction: column;
    height: auto;
  }
  
  .contactus-header ion-segment-button {
    margin: 2px 0;
  }
  
  .form-item {
    margin-bottom: 1rem;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .form-item {
    --border-width: 2px;
  }
  
  .contact-details ion-item,
  .office-hours ion-item,
  .departments ion-item {
    border: 1px solid var(--ion-color-medium);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .contact-info-section,
  .contact-form-section {
    animation: none;
  }
  
  .social-buttons ion-button:hover,
  .departments ion-item:hover,
  .submit-button:not([disabled]):hover {
    transform: none;
  }
  
  .form-item {
    transition: none;
  }
}

/* Focus Management */
.form-item ion-input:focus,
.form-item ion-textarea:focus,
.form-item ion-select:focus {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

/* Error States */
.ion-invalid.ion-touched {
  --border-color: var(--ion-color-danger);
}

ion-note[slot="error"] {
  color: var(--ion-color-danger);
  font-size: 0.75rem;
  margin-top: 0.25rem;
  display: block;
}

/* Loading States */
ion-spinner {
  width: 18px;
  height: 18px;
}

/* Card Styling */
ion-card {
  --background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 0;
}

ion-card-header {
  padding-bottom: 0.5rem;
}

ion-card-title {
  font-weight: 600;
  color: var(--ion-color-dark);
}

ion-card-subtitle {
  color: var(--ion-color-medium-shade);
  margin-top: 0.25rem;
}
