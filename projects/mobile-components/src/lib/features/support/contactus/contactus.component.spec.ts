import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

import { ContactusComponent, ContactusComponentConfig, ContactusComponentData, ContactFormData } from './contactus.component';

describe('ContactusComponent', () => {
  let component: ContactusComponent;
  let fixture: ComponentFixture<ContactusComponent>;
  let mockConfig: ContactusComponentConfig;
  let mockData: ContactusComponentData;

  beforeEach(waitForAsync(() => {
    mockConfig = {
      showContactInfo: true,
      enableContactForm: true,
      showOfficeHours: true,
      enableFileUpload: true,
      maxFileSize: 10,
      allowedFileTypes: ['pdf', 'doc', 'docx', 'txt', 'jpg', 'png'],
      requiredFields: ['name', 'email', 'subject', 'message'],
      autoReplyEnabled: true,
      estimatedResponseTime: '24 hours'
    };

    mockData = {
      contactInfo: {
        phone: '+****************',
        email: '<EMAIL>',
        address: {
          street: '123 Test St',
          city: 'Test City',
          state: 'TS',
          zipCode: '12345',
          country: 'USA'
        }
      },
      officeHours: {
        weekdays: 'Monday - Friday: 9:00 AM - 6:00 PM',
        weekends: 'Saturday: 10:00 AM - 4:00 PM',
        holidays: 'Closed on major holidays'
      },
      departments: [
        { id: 'general', name: 'General Inquiry', email: '<EMAIL>', description: 'General questions' },
        { id: 'support', name: 'Technical Support', email: '<EMAIL>', description: 'Technical assistance' }
      ],
      socialLinks: [
        { platform: 'Facebook', url: 'https://facebook.com/test', icon: 'logo-facebook' },
        { platform: 'Twitter', url: 'https://twitter.com/test', icon: 'logo-twitter' }
      ]
    };

    TestBed.configureTestingModule({
      imports: [ContactusComponent, ReactiveFormsModule, FormsModule, IonicModule.forRoot()],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(ContactusComponent);
    component = fixture.componentInstance;
    component.config = mockConfig;
    component.data = mockData;
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default config and data', () => {
    expect(component.config).toBeDefined();
    expect(component.data).toBeDefined();
    expect(component.contactForm).toBeDefined();
  });

  it('should initialize contact form with correct validators', () => {
    expect(component.contactForm.get('name')?.hasError('required')).toBeFalsy();
    expect(component.contactForm.get('email')?.hasError('required')).toBeFalsy();
    
    // Test required validation
    component.contactForm.get('name')?.setValue('');
    component.contactForm.get('name')?.markAsTouched();
    expect(component.contactForm.get('name')?.hasError('required')).toBeTruthy();
  });

  it('should validate email format', () => {
    const emailControl = component.contactForm.get('email');
    emailControl?.setValue('invalid-email');
    emailControl?.markAsTouched();
    expect(emailControl?.hasError('email')).toBeTruthy();

    emailControl?.setValue('<EMAIL>');
    expect(emailControl?.hasError('email')).toBeFalsy();
  });

  it('should handle file selection', () => {
    spyOn(component.fileUpload, 'emit');
    
    const mockFile = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    const mockEvent = {
      target: {
        files: [mockFile]
      }
    };

    component.onFileSelect(mockEvent);
    expect(component.selectedFiles.length).toBe(1);
    expect(component.fileUpload.emit).toHaveBeenCalledWith([mockFile]);
  });

  it('should validate file size', () => {
    const largeMockFile = new File(['x'.repeat(11 * 1024 * 1024)], 'large.pdf', { type: 'application/pdf' });
    const mockEvent = {
      target: {
        files: [largeMockFile]
      }
    };

    spyOn(component.componentError, 'emit');
    component.onFileSelect(mockEvent);
    expect(component.componentError.emit).toHaveBeenCalled();
  });

  it('should remove file from selection', () => {
    const mockFile = new File(['test'], 'test.pdf', { type: 'application/pdf' });
    component.selectedFiles = [mockFile];
    
    spyOn(component.fileUpload, 'emit');
    component.removeFile(0);
    
    expect(component.selectedFiles.length).toBe(0);
    expect(component.fileUpload.emit).toHaveBeenCalledWith([]);
  });

  it('should handle department selection', () => {
    spyOn(component.departmentSelect, 'emit');
    
    component.onDepartmentSelect('support');
    
    expect(component.contactForm.get('department')?.value).toBe('support');
    expect(component.departmentSelect.emit).toHaveBeenCalledWith('support');
  });

  it('should handle social link click', () => {
    spyOn(component.socialLinkClick, 'emit');
    spyOn(window, 'open');
    
    const testUrl = 'https://facebook.com/test';
    component.onSocialLinkClick(testUrl);
    
    expect(component.socialLinkClick.emit).toHaveBeenCalledWith(testUrl);
    expect(window.open).toHaveBeenCalledWith(testUrl, '_blank', 'noopener,noreferrer');
  });

  it('should submit valid form', () => {
    spyOn(component.formSubmit, 'emit');
    
    // Fill form with valid data
    component.contactForm.patchValue({
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '************',
      department: 'support',
      subject: 'Test Subject',
      message: 'This is a test message with more than 10 characters.',
      priority: 'medium'
    });

    component.onSubmit();
    
    expect(component.isSubmitting).toBeTruthy();
    expect(component.formSubmit.emit).toHaveBeenCalled();
  });

  it('should not submit invalid form', () => {
    spyOn(component.formSubmit, 'emit');
    
    // Leave form empty (required fields missing)
    component.onSubmit();
    
    expect(component.formSubmit.emit).not.toHaveBeenCalled();
    expect(component.isSubmitting).toBeFalsy();
  });

  it('should format file size correctly', () => {
    expect(component.formatFileSize(0)).toBe('0 Bytes');
    expect(component.formatFileSize(1024)).toBe('1 KB');
    expect(component.formatFileSize(1024 * 1024)).toBe('1 MB');
    expect(component.formatFileSize(1024 * 1024 * 1024)).toBe('1 GB');
  });

  it('should calculate total file size', () => {
    const file1 = new File(['test1'], 'test1.txt', { type: 'text/plain' });
    const file2 = new File(['test12'], 'test2.txt', { type: 'text/plain' });
    
    // Mock file sizes
    Object.defineProperty(file1, 'size', { value: 100 });
    Object.defineProperty(file2, 'size', { value: 200 });
    
    component.selectedFiles = [file1, file2];
    
    expect(component.getTotalFileSize()).toBe(300);
  });

  it('should get field error messages', () => {
    const nameControl = component.contactForm.get('name');
    nameControl?.setValue('');
    nameControl?.markAsTouched();
    
    expect(component.getFieldError('name')).toContain('Name is required');
    
    const emailControl = component.contactForm.get('email');
    emailControl?.setValue('invalid-email');
    emailControl?.markAsTouched();
    
    expect(component.getFieldError('email')).toContain('valid email address');
  });

  it('should check if field has error', () => {
    const nameControl = component.contactForm.get('name');
    nameControl?.setValue('');
    nameControl?.markAsTouched();
    
    expect(component.hasFieldError('name')).toBeTruthy();
    
    nameControl?.setValue('Valid Name');
    expect(component.hasFieldError('name')).toBeFalsy();
  });

  it('should set active section', () => {
    component.setActiveSection('contact-form');
    expect(component.activeSection).toBe('contact-form');
  });

  it('should handle configuration validation', () => {
    spyOn(component.componentError, 'emit');
    
    // Test with invalid config
    component.config = {} as ContactusComponentConfig;
    component.ngOnInit();
    
    expect(component.componentError.emit).toHaveBeenCalled();
  });

  it('should emit configuration and data changes', () => {
    spyOn(component.configChange, 'emit');
    spyOn(component.dataChange, 'emit');
    
    // Trigger data change through submission status update
    component.data.submissionStatus = {
      success: true,
      message: 'Test message'
    };
    component.dataChange.emit(component.data);
    
    expect(component.dataChange.emit).toHaveBeenCalledWith(component.data);
  });
});
