import { Component, Input, Output, EventEmitter, <PERSON><PERSON><PERSON><PERSON>, OnDestroy, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';

/**
 * Interface for Contact Us component configuration
 */
export interface ContactusComponentConfig {
  showContactInfo: boolean;
  enableContactForm: boolean;
  showOfficeHours: boolean;
  enableFileUpload: boolean;
  maxFileSize: number; // in MB
  allowedFileTypes: string[];
  requiredFields: string[];
  autoReplyEnabled: boolean;
  estimatedResponseTime: string;
  [key: string]: any;
}

/**
 * Interface for Contact Us component data
 */
export interface ContactusComponentData {
  contactInfo: {
    phone: string;
    email: string;
    address: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
      country: string;
    };
  };
  officeHours: {
    weekdays: string;
    weekends: string;
    holidays: string;
  };
  departments: Array<{
    id: string;
    name: string;
    email: string;
    description: string;
  }>;
  socialLinks: Array<{
    platform: string;
    url: string;
    icon: string;
  }>;
  submissionStatus?: {
    success: boolean;
    message: string;
    referenceNumber?: string;
  };
  [key: string]: any;
}

/**
 * Interface for Contact Form data
 */
export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  department: string;
  subject: string;
  message: string;
  files?: File[];
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  [key: string]: any;
}

/**
 * Enhanced Contact Us Component with comprehensive functionality
 * Provides contact information display, contact form, and file upload capabilities
 */
@Component({
  selector: 'lp-contactus',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FormsModule, IonicModule],
  templateUrl: './contactus.component.html',
  styleUrls: ['./contactus.component.css'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ContactusComponent implements OnInit, OnDestroy {
  @Input() config: ContactusComponentConfig = {
    showContactInfo: true,
    enableContactForm: true,
    showOfficeHours: true,
    enableFileUpload: true,
    maxFileSize: 10,
    allowedFileTypes: ['pdf', 'doc', 'docx', 'txt', 'jpg', 'png'],
    requiredFields: ['name', 'email', 'subject', 'message'],
    autoReplyEnabled: true,
    estimatedResponseTime: '24 hours'
  };

  @Input() data: ContactusComponentData = {
    contactInfo: {
      phone: '+****************',
      email: '<EMAIL>',
      address: {
        street: '123 Business St',
        city: 'Business City',
        state: 'BC',
        zipCode: '12345',
        country: 'USA'
      }
    },
    officeHours: {
      weekdays: 'Monday - Friday: 9:00 AM - 6:00 PM',
      weekends: 'Saturday: 10:00 AM - 4:00 PM',
      holidays: 'Closed on major holidays'
    },
    departments: [
      { id: 'general', name: 'General Inquiry', email: '<EMAIL>', description: 'General questions and information' },
      { id: 'support', name: 'Technical Support', email: '<EMAIL>', description: 'Technical assistance and troubleshooting' },
      { id: 'sales', name: 'Sales', email: '<EMAIL>', description: 'Product information and purchases' },
      { id: 'billing', name: 'Billing', email: '<EMAIL>', description: 'Payment and billing inquiries' }
    ],
    socialLinks: [
      { platform: 'Facebook', url: 'https://facebook.com/example', icon: 'logo-facebook' },
      { platform: 'Twitter', url: 'https://twitter.com/example', icon: 'logo-twitter' },
      { platform: 'LinkedIn', url: 'https://linkedin.com/company/example', icon: 'logo-linkedin' }
    ]
  };

  @Output() formSubmit = new EventEmitter<ContactFormData>();
  @Output() fileUpload = new EventEmitter<File[]>();
  @Output() departmentSelect = new EventEmitter<string>();
  @Output() socialLinkClick = new EventEmitter<string>();
  @Output() configChange = new EventEmitter<ContactusComponentConfig>();
  @Output() dataChange = new EventEmitter<ContactusComponentData>();
  @Output() componentError = new EventEmitter<Error>();

  contactForm!: FormGroup;
  selectedFiles: File[] = [];
  isSubmitting = false;
  activeSection = 'contact-info';
  
  private destroy$ = new Subject<void>();

  constructor(private formBuilder: FormBuilder) {}

  ngOnInit(): void {
    this.initializeContactForm();
    this.validateConfiguration();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Initialize the reactive contact form
   */
  private initializeContactForm(): void {
    try {
      const formControls: any = {
        name: ['', this.config.requiredFields.includes('name') ? [Validators.required, Validators.minLength(2)] : []],
        email: ['', this.config.requiredFields.includes('email') ? [Validators.required, Validators.email] : [Validators.email]],
        phone: ['', this.config.requiredFields.includes('phone') ? [Validators.required] : []],
        department: ['', this.config.requiredFields.includes('department') ? [Validators.required] : []],
        subject: ['', this.config.requiredFields.includes('subject') ? [Validators.required, Validators.minLength(5)] : []],
        message: ['', this.config.requiredFields.includes('message') ? [Validators.required, Validators.minLength(10)] : []],
        priority: ['medium']
      };

      this.contactForm = this.formBuilder.group(formControls);

      // Subscribe to form value changes
      this.contactForm.valueChanges
        .pipe(takeUntil(this.destroy$))
        .subscribe(value => {
          this.onFormValueChange(value);
        });

    } catch (error) {
      this.handleError(new Error('Failed to initialize contact form: ' + (error as Error).message));
    }
  }

  /**
   * Validate component configuration
   */
  private validateConfiguration(): void {
    try {
      if (!this.config) {
        throw new Error('Configuration is required');
      }

      if (this.config.enableFileUpload && (!this.config.allowedFileTypes || this.config.allowedFileTypes.length === 0)) {
        throw new Error('Allowed file types must be specified when file upload is enabled');
      }

      if (this.config.maxFileSize <= 0) {
        throw new Error('Maximum file size must be greater than 0');
      }

      if (!this.config.requiredFields || !Array.isArray(this.config.requiredFields)) {
        this.config.requiredFields = ['name', 'email', 'subject', 'message'];
      }

    } catch (error) {
      this.handleError(error as Error);
    }
  }

  /**
   * Handle form submission
   */
  onSubmit(): void {
    if (!this.config.enableContactForm) {
      return;
    }

    if (this.contactForm.valid) {
      try {
        this.isSubmitting = true;

        const formData: ContactFormData = {
          ...this.contactForm.value,
          files: this.selectedFiles.length > 0 ? this.selectedFiles : undefined
        };

        this.formSubmit.emit(formData);

        // Simulate submission success for demo
        setTimeout(() => {
          this.isSubmitting = false;
          this.data.submissionStatus = {
            success: true,
            message: 'Thank you! Your message has been sent successfully.',
            referenceNumber: 'REF-' + Math.random().toString(36).substr(2, 9).toUpperCase()
          };
          this.dataChange.emit(this.data);
          this.resetForm();
        }, 2000);

      } catch (error) {
        this.isSubmitting = false;
        this.handleError(new Error('Form submission failed: ' + (error as Error).message));
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  /**
   * Handle file selection
   */
  onFileSelect(event: any): void {
    if (!this.config.enableFileUpload) {
      return;
    }

    try {
      const files = Array.from(event.target.files) as File[];
      const validFiles: File[] = [];

      for (const file of files) {
        if (this.validateFile(file)) {
          validFiles.push(file);
        }
      }

      this.selectedFiles = [...this.selectedFiles, ...validFiles];
      this.fileUpload.emit(this.selectedFiles);

    } catch (error) {
      this.handleError(new Error('File selection failed: ' + (error as Error).message));
    }
  }

  /**
   * Validate uploaded file
   */
  private validateFile(file: File): boolean {
    try {
      // Check file size
      const maxSizeBytes = this.config.maxFileSize * 1024 * 1024;
      if (file.size > maxSizeBytes) {
        throw new Error(`File ${file.name} exceeds maximum size of ${this.config.maxFileSize}MB`);
      }

      // Check file type
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      if (fileExtension && !this.config.allowedFileTypes.includes(fileExtension)) {
        throw new Error(`File type ${fileExtension} is not allowed`);
      }

      return true;

    } catch (error) {
      this.handleError(error as Error);
      return false;
    }
  }

  /**
   * Remove selected file
   */
  removeFile(index: number): void {
    this.selectedFiles.splice(index, 1);
    this.fileUpload.emit(this.selectedFiles);
  }

  /**
   * Handle department selection
   */
  onDepartmentSelect(departmentId: string): void {
    this.contactForm.patchValue({ department: departmentId });
    this.departmentSelect.emit(departmentId);
  }

  /**
   * Handle social link click
   */
  onSocialLinkClick(url: string): void {
    this.socialLinkClick.emit(url);
    window.open(url, '_blank', 'noopener,noreferrer');
  }

  /**
   * Handle section navigation
   */
  setActiveSection(section: string): void {
    this.activeSection = section;
  }

  /**
   * Handle form value changes
   */
  private onFormValueChange(value: any): void {
    // Custom form validation or processing can be added here
  }

  /**
   * Mark all form fields as touched to show validation errors
   */
  private markFormGroupTouched(): void {
    Object.keys(this.contactForm.controls).forEach(key => {
      const control = this.contactForm.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }

  /**
   * Reset the contact form
   */
  private resetForm(): void {
    this.contactForm.reset();
    this.selectedFiles = [];
    Object.keys(this.contactForm.controls).forEach(key => {
      const control = this.contactForm.get(key);
      if (control) {
        control.markAsUntouched();
      }
    });
  }

  /**
   * Handle component errors
   */
  private handleError(error: Error): void {
    console.error('ContactusComponent Error:', error);
    this.componentError.emit(error);
    
    // Update component data with error state
    this.data.submissionStatus = {
      success: false,
      message: error.message || 'An error occurred. Please try again.'
    };
    this.dataChange.emit(this.data);
  }

  /**
   * Get form field error message
   */
  getFieldError(fieldName: string): string {
    const control = this.contactForm.get(fieldName);
    if (control && control.touched && control.errors) {
      if (control.errors['required']) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;
      }
      if (control.errors['email']) {
        return 'Please enter a valid email address';
      }
      if (control.errors['minlength']) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is too short`;
      }
    }
    return '';
  }

  /**
   * Check if form field has error
   */
  hasFieldError(fieldName: string): boolean {
    const control = this.contactForm.get(fieldName);
    return !!(control && control.touched && control.errors);
  }

  /**
   * Check if form field is invalid and touched
   */
  isFieldInvalid(fieldName: string): boolean {
    const control = this.contactForm.get(fieldName);
    return !!(control && control.invalid && control.touched);
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get total file size
   */
  getTotalFileSize(): number {
    return this.selectedFiles.reduce((total, file) => total + file.size, 0);
  }

  /**
   * Trigger file input click
   */
  triggerFileInput(): void {
    const fileInput = document.getElementById('file-upload') as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  }
}
