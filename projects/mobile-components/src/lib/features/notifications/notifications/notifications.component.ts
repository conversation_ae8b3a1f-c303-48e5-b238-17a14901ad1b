import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import {
  MemberService,
  Statement,
  KeyCloakService,
  MemberProfile,
} from 'lp-client-api';
import { firstValueFrom, filter, take } from 'rxjs';

@Component({
  selector: 'app-notifications',
  templateUrl: 'notifications.component.html',
  styleUrls: ['notifications.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class NotificationsComponent implements OnInit {
  searchRender = false;
  notifications: any = [];
  count?: any = 0;
  beginDate?: any;
  endDate?: Date;
  profile?: MemberProfile;

  constructor(
    private memberService: MemberService,
    private kc: KeyCloakService
  ) {}

  ngOnInit(): void {
    this.kc.authStatus
      .pipe(
        filter(() => this.kc.authSuccess),
        take(1)
      )
      .subscribe(() => {
        console.log('[NotificationsComponent] Auth success detected, calling loadMember.');
        this.loadMember();
      });
  }

  ionViewDidEnter(): void {
    this.search();
  }

  async search(): Promise<void> {
    // try {
    //   const body = await firstValueFrom(
    //     this.memberService.getNotifications(this.kc.lpUniueReference)
    //   );
    //   if (body !== undefined) {
    //     this.notifications = body;
    //   }
    // } catch (error: any) {
    //   console.log(error.error.detail);
    // }
  }

  async notiRead(notificationId: any) {
    // try {
    //   const body = await firstValueFrom(
    //     this.memberService.readNotification(
    //       this.kc.lpUniueReference,
    //       notificationId
    //     )
    //   );

    //   let index = this.notifications.findIndex(
    //     (item: any) => item.noteSeq === notificationId
    //   );
    //   this.notifications.splice(index, 1);

    //   if (body !== undefined) {
    //     this.count = body;
    //   }
    // } catch (error: any) {
    //   console.log(error.error.detail);
    // }
  }

  async getNotification(notificationId: any) {
    // try {
    //   const body = await firstValueFrom(
    //     this.memberService.getNotification(
    //       this.kc.lpUniueReference,
    //       notificationId
    //     )
    //   );

    //   if (body !== undefined) {
    //     this.count = body;
    //   }
    // } catch (error: any) {
    //   console.log(error.error.detail);
    // }
  }

  async loadMember() {
    // try {
    //   const data = await firstValueFrom(
    //     this.memberService.getProfile(this.kc.lpUniueReference)
    //   );
    //   this.profile = data;
    // } catch (error) {
    //   console.error('Error loading member profile:', error);
    // }
  }
}
