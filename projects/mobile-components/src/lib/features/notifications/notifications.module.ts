import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import Notifications components
// Standalone (2)
import { NotificationsComponent } from './notifications/notifications.component';
import { NotificationsCompactComponent } from './notifications-compact/notifications-compact.component';

// Non-Standalone (0)
// None

@NgModule({
  declarations: [
    // Non-Standalone Components (0)
    // None
  ],
  imports: [
    CommonModule,
    IonicModule,
    // Standalone Components (2)
    NotificationsComponent,
    NotificationsCompactComponent,
  ],
  exports: [
    // Standalone Components (2)
    NotificationsComponent,
    NotificationsCompactComponent,
  ]
})
export class NotificationsModule { }
