import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import Products components
// Standalone (2)
import { ProductCompactComponent } from './product-compact/product-compact.component';
import { ProductsComponent } from './products/products.component';

// Non-Standalone (0)
// None

@NgModule({
  declarations: [
    // Non-Standalone Components (0)
    // None
  ],
  imports: [
    CommonModule,
    IonicModule,
    // Standalone Components (2)
    ProductCompactComponent,
    ProductsComponent,
  ],
  exports: [
    // Standalone Components (2)
    ProductCompactComponent,
    ProductsComponent,
  ]
})
export class ProductsModule { }
