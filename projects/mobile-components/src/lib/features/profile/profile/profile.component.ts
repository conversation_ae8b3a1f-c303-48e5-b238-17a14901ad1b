import { Component, Injector, OnInit, ChangeDetectorRef, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
// Import modules conditionally to avoid circular references
import { AddressComponent } from '../../../forms/address/address.component';
// import { StoresComponent } from '../stores/stores.component';
import { HeadLogoComponent } from '../../../layout/head-logo/head-logo.component';

import {
  LPMemberEntityTools,
  MemberProfile,
  MemberService,
  ValidationService,
  KeyCloakService,
  Address,
  LssConfig,
} from 'lp-client-api';
import { Router } from '@angular/router';
import { AbstractFormComponent } from '../../../shared/abstract.form.component';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styles: [`
    .profile-container {
      padding: 1rem;
    }
    ion-input, ion-select {
      margin-bottom: 1rem;
      --padding-start: 16px;
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.7);
    }
    .form-group {
      margin-bottom: 1.5rem;
    }
    .error-message {
      color: red;
      font-size: 0.8rem;
      margin-top: 0.25rem;
    }
  `],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    IonicModule,
    AddressComponent,
    HeadLogoComponent,
    // StoresComponent,
  ],
})
export class ProfileComponent extends AbstractFormComponent<MemberProfile> {
  @Input() profile?: MemberProfile;
  @Input() kc?: KeyCloakService;
  @Input() lssConfig?: LssConfig;
  
  addr!: Address;
  profileForm: FormGroup;
  validated: boolean = false;
  favourite: any = {};
  favourite_id: string = '';
  birthDate?: any = '';
  status_loading: boolean = true;
  phoneTogether: any = '+27';

  static emptyForm = {
    phone: LPMemberEntityTools.getIONTelephoneFromLPTel(
      LPMemberEntityTools.getEmptyPhone('CELL')
    ),
    address_POST: LPMemberEntityTools.getEmptyAddress('POST'),
    givenNames: '',
    surname: '',
    nationalIdNum: '',
    emailAddress: '',
    title: '',
    gender: '',
    preferenceList: [],
  };

  constructor(
    injector: Injector,
    private memberService: MemberService,
    public override _formValidations: ValidationService,
    public override _formBuilder: FormBuilder,
    protected readonly router: Router,
  ) {
    super(injector);

    this.profileForm = this._formBuilder.group({
      phone: [
        LPMemberEntityTools.getIONTelephoneFromLPTel(
          LPMemberEntityTools.getEmptyPhone('CELL')
        ),
        [],
      ],
      givenNames: [''],
      title: [''],
      gender: [''],
      surname: ['', [Validators.required, Validators.minLength(3)]],
      nationalIdNum: [''],
      birthDate: ['', []],
      membershipNumber: [
        '',
        [
          Validators.pattern('^[0-9]*$'),
          Validators.minLength(16),
          Validators.maxLength(16),
        ],
      ],
      emailAddress: [
        '',
        [
          Validators.required,
          Validators.pattern('^[a-z0-9._%+-]+@[a-z0-9.-]+.[a-z]{2,4}$'),
        ],
      ],
    });
  }

  ionViewWillEnter() {
    this.loading = true;

    // console.log('MER', MergedClass)
    this.showLoadingModal('Fetching updated Account').then(() => {
      if (!this.kc || !this.kc.lpUniueReference) {
        console.error('KeyCloak service or lpUniqueReference is not available');
        this.dismissLoadingModal();
        this.loading = false;
        return;
      }
      
      this.memberService
        .getProfile(true)
        .subscribe((data) => {
          this.dismissLoadingModal();
          this.profile = data;
          if (data) {
            let tel = null;
            if (data.personTelephone) {
              for (let i = 0; i < data.personTelephone.length; i++) {
                if (data.personTelephone[i].telephoneType == 'CELL')
                  tel = data.personTelephone[i];
              }
              if (tel) {
                let pl = {
                  internationalNumber:
                    tel.countryCode + ' ' + tel.telephoneNumber?.substring(1),
                  nationalNumber: tel.telephoneNumber,
                  isoCode: 'za',
                  dialCode: tel.countryCode,
                };
                this.phoneTogether = pl.internationalNumber;
                if (data.preferenceList) {
                  for (let i = 0; i < data.preferenceList.length; i++) {
                    console.log(
                      'data.preferenceList[i].level1',
                      data.preferenceList[i].level1
                    );
                    if (data.preferenceList[i].level1 == 'PART')
                      this.favourite_id = data.preferenceList[i].level2;
                  }
                }
                this.profileForm.controls['phone'].patchValue(pl);
              }
            }

            if (data.personAddress) {
              if (data.personAddress.length > 0) {
                let addr = data.personAddress.filter(
                  (item: Address) => item.addressType == 'POST'
                )[0];
                this.addr = addr;
              }
            }

            this.profileForm.patchValue(data);
            this.loading = false;
            this.detectChanges();
          }
        });
    });
  }

  get hasDate() {
    this.formData = this.getFormValues();
    let date;
    if (this.formData?.birthDate) {
      let newD: any = this.formData?.birthDate;
      date = newD.split('T')[0];
    }
    return date;
  }

  updateAddress(data: any) {
    this.favourite = data;
  }
  todaysDate12YearsAgo() {
    let date: any = new Date();
    date.setFullYear(date.getFullYear() - 12);
    //format date to yyyy-mm-dd
    date = date.toISOString().split('T')[0];
    return date;
  }
  override get form(): any {
    return this.profileForm.controls;
  }
  get isValid(): boolean {
    return this.profileForm.valid;
  }
  doLoad() {
    if (!this.profileForm.valid) {
      this.profileForm.markAllAsTouched();
      return;
    }
    this.profileForm.value.personAddress = [
      {
        addressType: 'POST',
        addressTypeDesc: '',
        line1: this.profileForm.value.address_POST.line1,
        line2: this.profileForm.value.address_POST.line2,
        line3: '',
        suburb: this.profileForm.value.address_POST.place,
        city: this.profileForm.value.address_POST.city.value,
        district: this.profileForm.value.address_POST.district,
        province: this.profileForm.value.address_POST.province,
        country: this.profileForm.value.address_POST.country,
        countryDesc: '',
        postalCode: this.profileForm.value.address_POST.postalCode,
        mailable: true,
        returnToSenderCount: '',
        mailPreference: true,
        landmark: '',
      },
    ];

    let payload: any = this.profileForm.value;
    if (payload.phone) {
      payload.personTelephone = [
        {
          telephoneType: 'CELL',
          countryCode: payload.phone.dialCode,
          telephoneCode: '',
          telephoneNumber: payload.phone.nationalNumber,
          telephoneExtension: '',
          phoneable: '',
          smsable: '',
        },
      ];
    }

    payload.preferenceList = [
      {
        level1: 'PART',
        level2: this.favourite.partnerId,
      },
    ];
    if (payload.birthDate) payload.birthDate = payload.birthDate + 'T00:00:00';
    else if (payload.birthDate === '') delete payload.birthDate;

    console.log('payload', payload);

    this.showLoadingModal('Updating your account').then(() => {
      if (!this.kc || !this.kc.lpUniueReference) {
        console.error('KeyCloak service or lpUniqueReference is not available');
        this.dismissLoadingModal();
        return;
      }
      
      this.memberService.update(payload).subscribe({
        error: (error) => {
          console.log('error', error);
          console.log('error', error.message);

          this.dismissLoadingModal();
          this.presentToast({
            message: 'Oops, something went wrong!',
            color: 'danger',
            position: 'bottom',
          }).then();

          // this._formState = this._formStateType.fail;
          // this.error = error.error.detail;
        },
        next: (body) => {
          console.log('BOdy', body);
          this.dismissLoadingModal();
          this.presentToast({
            message: 'Your account has been updated',
            position: 'bottom',
          }).then();
          this.profileForm = body;
          this.router.navigate(['/app/account']);
        },
      });
    });
  }
}
