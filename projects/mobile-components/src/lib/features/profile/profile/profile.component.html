<div class="app-background">
  <div class="absolute inset-0 z-0 h-screen bg-blue-200 animated-bg"></div>
  <div class="overflow-y-auto absolute inset-0 z-10 pb-24 space-y-2">
  <lib-head-logo
    [balance]="profile?.currentBalance"
    [src]="lssConfig?.pages?.landing?.loggedinIcon"
  />

  <ion-card class="">
    <form [formGroup]="profileForm" (ngSubmit)="doLoad()">
      <ion-item lines="none" *ngIf="isFormComponentInvalid('membershipNumber')">
        <div
          *ngFor="
            let error of getComponentErrors('membershipNumber');
            let i = index
          "
          class="validator-error w-full"
        >
          <div *ngIf="i == 0" class="w-full">
            Card number must be 16 numeric characters long.
          </div>
        </div>
      </ion-item>
      <ion-item>
        <ion-icon slot="start" name="ribbon-outline"></ion-icon>

        <ion-select
          label="Title"
          labelPlacement="floating"
          formControlName="title"
          placeholder="Please select.."
        >
          <ion-select-option
            *ngFor="let codeItem of getCodeList('TITL') | async"
            [value]="codeItem.codeId"
            >{{ codeItem.description }}</ion-select-option
          >
        </ion-select>
      </ion-item>
      <ion-item>
        <ion-icon slot="start" name="person"></ion-icon>

        <ion-input
          [readonly]="status_loading"
          labelPlacement="floating"
          label="* Firstname"
          fill="solid"
          type="text"
          formControlName="givenNames"
        ></ion-input>
      </ion-item>
      <ion-item
        *ngIf="!form.givenNames.valid && form.givenNames.touched"
        class="validator-error"
      >
        <div
          *ngFor="
            let error of _formValidations.doErrors(
              form.givenNames.errors,
              'Firstname'
            )
          "
        >
          {{ error }}
        </div>
      </ion-item>
      <ion-item>
        <ion-icon slot="start" name="person"></ion-icon>

        <ion-input
          labelPlacement="floating"
          label="* Surname"
          type="text"
          fill="solid"
          formControlName="surname"
          [readonly]="status_loading"
        ></ion-input>
      </ion-item>
      <ion-item
        *ngIf="!form.surname.valid && form.surname.touched"
        class="validator-error"
      >
        <div
          *ngFor="
            let error of _formValidations.doErrors(
              form.surname.errors,
              'Lastname'
            )
          "
        >
          {{ error }}
        </div>
      </ion-item>

      <ion-item>
        <ion-icon slot="start" name="id-card-outline"></ion-icon>

        <ion-input
          labelPlacement="floating"
          label="South African ID number"
          type="text"
          formControlName="nationalIdNum"
          fill="solid"
          [readonly]="status_loading"
        >
        </ion-input>
      </ion-item>
      <ion-item *ngIf="!form.nationalIdNum.valid && form.nationalIdNum.touched">
        <div
          *ngFor="
            let error of _formValidations.doErrors(
              form.nationalIdNum.errors,
              'ID/Passport number'
            )
          "
          class="validator-error"
        >
          {{ error }}
        </div>
      </ion-item>
      <ion-item id="open-date-input" lines="inset">
        <ion-icon slot="start" name="calendar-outline"></ion-icon>
        <ion-input
          label="Birth Date"
          labelPlacement="floating"
          formControlName="birthDate"
          type="date"
          [max]="todaysDate12YearsAgo()"
        ></ion-input>
      </ion-item>
      <ion-item lines="none" *ngIf="isFormComponentInvalid('birthDate')">
        <div
          *ngFor="let error of getComponentErrors('birthDate')"
          class="validator-error"
        >
          {{ error }}
        </div>
      </ion-item>
      <ion-item>
        <ion-icon slot="start" name="balloon-outline"></ion-icon>

        <ion-select
          label="Gender"
          labelPlacement="floating"
          formControlName="gender"
          placeholder="Please select.."
        >
          <ion-select-option
            *ngFor="let codeItem of getCodeList('SEX') | async"
            [value]="codeItem.codeId"
            >{{ codeItem.description }}</ion-select-option
          >
        </ion-select>
      </ion-item>
      <div class="form-spacer"></div>
      <lp-pos-address
        type="POST"
        [mainAddress]="addr"
        [mainForm]="profileForm"
        #address_post
        [required_field]="true"

      ></lp-pos-address>
      <ion-item lines="inset" class="ion-intl-tel item-has-value">
        <ion-icon slot="start" name="phone-portrait-outline"></ion-icon>


          <ion-input
          labelPlacement="floating"
          label="* Mobile Number:"
          type="text"
          [value]="phoneTogether"
          disabled
        ></ion-input>

      </ion-item>
      <ion-item *ngIf="!form.phone.valid && form.phone.touched">
        <div
          *ngFor="
            let error of _formValidations.doErrors(
              form.phone.errors,
              'Mobile Number'
            )
          "
          class="validator-error"
        >
          {{ error }}
        </div>
      </ion-item>
      <!-- <lib-stores
        [favourite_id]="favourite_id"
        (updateDataEvent)="updateAddress($event)"
        [required_field]="true"
      /> -->

      <ion-item>
        <ion-icon slot="start" name="mail"></ion-icon>

        <ion-input
          labelPlacement="floating"
          label="Email"
          type="email"
          formControlName="emailAddress"
        >
        </ion-input>
      </ion-item>
      <ion-item *ngIf="!form.emailAddress.valid && form.emailAddress.touched">
        <div
          *ngFor="
            let error of _formValidations.doErrors(
              form.emailAddress.errors,
              'Email'
            )
          "
          class="validator-error"
        >
          {{ error }}
        </div>
      </ion-item>
      <div class="form-spacer"></div>
      <ion-button expand="block" class="save" type="submit">Save</ion-button>

    </form>
  </ion-card>
  </div>
</div>
