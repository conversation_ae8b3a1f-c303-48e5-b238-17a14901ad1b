import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import Profile components
// Standalone (2)
import { ProfileComponent } from './profile/profile.component';
import { ProfileremoveComponent } from './profileremove/profileremove.component';

// Non-Standalone (0)
// None

@NgModule({
  declarations: [
    // Non-Standalone Components (0)
    // None
  ],
  imports: [
    CommonModule,
    IonicModule,
    // Standalone Components (2)
    ProfileComponent,
    ProfileremoveComponent,
  ],
  exports: [
    // Standalone Components (2)
    ProfileComponent,
    ProfileremoveComponent,
  ]
})
export class ProfileModule { }
