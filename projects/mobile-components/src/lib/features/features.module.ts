import { NgModule } from '@angular/core';

// Feature Sub-Modules
import { AuthenticationModule } from './authentication/authentication.module';
import { CalendarModule } from './calendar/calendar.module';
import { CartModule } from './cart/cart.module';
import { ChatModule } from './chat/chat.module';
import { CryptoModule } from './crypto/crypto.module';
import { CustomizerModule } from './customizer/customizer.module';
import { GeolocationModule } from './geolocation/geolocation.module';
import { InvestmentsModule } from './investments/investments.module';
import { InvitesModule } from './invites/invites.module';
import { LeaguesModule } from './leagues/leagues.module';
import { NotificationsModule } from './notifications/notifications.module';
import { OffersModule } from './offers/offers.module';
import { PaymentsModule } from './payments/payments.module';
import { ProductsModule } from './products/products.module';
import { ProfileModule } from './profile/profile.module';
import { ProjectsModule } from './projects/projects.module';
import { SearchModule } from './search/search.module';
import { SettingsModule } from './settings/settings.module';
import { SkillsModule } from './skills/skills.module';
import { SocialsModule } from './socials/socials.module';
// import { StoresModule } from './stores/stores.module';
import { SupportModule } from './support/support.module';
import { TeamsModule } from './teams/teams.module';
import { TodosModule } from './todos/todos.module';
import { TopicsModule } from './topics/topics.module';
import { TransactionsModule } from './transactions/transactions.module';
import { UsersModule } from './users/users.module';
import { VirtualCardModule } from './virtual-card/virtual-card.module';

@NgModule({
  imports: [
    AuthenticationModule,
    CalendarModule,
    CartModule,
    ChatModule,
    CryptoModule,
    CustomizerModule,
    GeolocationModule,
    InvestmentsModule,
    InvitesModule,
    LeaguesModule,
    NotificationsModule,
    OffersModule,
    PaymentsModule,
    ProductsModule,
    ProfileModule,
    ProjectsModule,
    SearchModule,
    SettingsModule,
    SkillsModule,
    SocialsModule,
    // StoresModule,
    SupportModule,
    TeamsModule,
    TodosModule,
    TopicsModule,
    TransactionsModule,
    UsersModule,
    VirtualCardModule
  ],
  exports: [
    AuthenticationModule,
    CalendarModule,
    CartModule,
    ChatModule,
    CryptoModule,
    CustomizerModule,
    GeolocationModule,
    InvestmentsModule,
    InvitesModule,
    LeaguesModule,
    NotificationsModule,
    OffersModule,
    PaymentsModule,
    ProductsModule,
    ProfileModule,
    ProjectsModule,
    SearchModule,
    SettingsModule,
    SkillsModule,
    SocialsModule,
    // StoresModule,
    SupportModule,
    TeamsModule,
    TodosModule,
    TopicsModule,
    TransactionsModule,
    UsersModule,
    VirtualCardModule
  ]
})
export class FeaturesModule { }
