import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import Calendar components
// Standalone (3)
import { CalendarEventComponent } from './calendar-event/calendar-event.component';
import { CalendarEventPendingComponent } from './calendar-event-pending/calendar-event-pending.component';
import { DaysSquareComponent } from './days-square/days-square.component';

// Non-Standalone (0)
// None

@NgModule({
  declarations: [
    // Non-Standalone Components (0)
    // None
  ],
  imports: [
    CommonModule,
    IonicModule,
    // Standalone Components (3)
    CalendarEventComponent,
    CalendarEventPendingComponent,
    DaysSquareComponent,
  ],
  exports: [
    // Standalone Components (3)
    CalendarEventComponent,
    CalendarEventPendingComponent,
    DaysSquareComponent,
  ]
})
export class CalendarModule { }
