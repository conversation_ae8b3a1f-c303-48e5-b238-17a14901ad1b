import { Component, Input, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { BaseModule } from '../../../base/base.module';

type RoundedType = 'none' | 'sm' | 'md' | 'lg' | 'full';

interface Product {
  id: number;
  name: string;
  image: string;
  price: number;
  quantity: number;
  link: string;
}

@Component({
  selector: 'widget-shopping-cart-compact',
  templateUrl: './shopping-cart-compact.component.html',
  styleUrl: './shopping-cart-compact.component.css',
  standalone: true,
  imports: [CommonModule, IonicModule, BaseModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class ShoppingCartCompactComponent {
  @Input() rounded: RoundedType = 'sm';

  products: Product[] = [
    {
      id: 0,
      name: '<PERSON><PERSON><PERSON> ROUTER GR 1400W POF 1400 ACE',
      image: 'assets/images/shop/bosch.webp',
      link: 'https://micashop.co.za/product/bosch-router-gr-1400w-pof-1400-ace/',
      price: 2199.99,
      quantity: 1,
    },
    {
      id: 1,
      name: 'CAT 18V 4.0Ah Brand Battery',
      image: 'assets/images/shop/cat.webp',
      link: 'https://micashop.co.za/product/18v-4-0ah-brand-battery/',
      price: 2199.99,
      quantity: 1,
    },
    {
      id: 2,
      name: 'MATWELD WELDER INVERTER 220V 150A ALUMINIUM CASE',
      image: 'assets/images/shop/matweld.webp',
      link: 'https://micashop.co.za/product/matweld-welder-inverter-220v-150a-aluminium-case/',
      price: 2799.99,
      quantity: 1,
    },
  ];

  get roundedClass(): string {
    switch (this.rounded) {
      case 'sm':
        return 'rounded';
      case 'md':
        return 'rounded-lg';
      case 'lg':
        return 'rounded-xl';
      case 'full':
        return 'rounded-full';
      default:
        return '';
    }
  }

  visitStore() {
    window.open('https://micashop.co.za/', '_blank');
  }

  visitProduct(link: string) {
    window.open(link, '_blank');
  }
}
