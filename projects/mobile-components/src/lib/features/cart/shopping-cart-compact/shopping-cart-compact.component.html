<div>
  <div class="mb-6 flex items-center justify-between">
    <base-heading
      as="h3"
      size="sm"
      weight="medium"
      lead="none"
      class="text-muted-800 dark:text-white"
    >
      Tools
    </base-heading>
    <base-tag color="warning" size="sm" rounded="full">
      {{ products.length }} Promotion items
    </base-tag>
  </div>
  <div class="space-y-2 border-b border-muted-200 dark:border-muted-700 pb-2">
    <div
      *ngFor="let product of products; let i = index"
      class="
        flex
        gap-2
        group
        bg-slate-200
        dark:bg-slate-700/50
        rounded-lg
        shadow-inner
        p-1
      "
      (click)="visitProduct(product.link)"
    >
      <div
        [ngClass]="[
          'bg-white dark:bg-muted-700 flex size-16 shrink-0 items-center justify-center',
          roundedClass
        ]"
      >
        <img
          class="size-12 object-cover object-center"
          [src]="product.image"
          [alt]="product.name"
        />
      </div>
      <div>
        <p
          class="
            text-muted-500
            dark:text-muted-400
            mt-1
            font-sans
            text-xs
            group-hover:text-primary-500
          "
        >
          {{ product.name }}
        </p>
        <p
          class="
            font-sans font-semibold
            text-primary-500
            dark:text-primary-400
            group-hover:text-primary-600
          "
        >
          R {{ product.price.toFixed(2) }}
        </p>
      </div>
    </div>
  </div>
  <div class="mt-4">
    <button
      class="
        bg-gradient-to-r
        from-orange-600
        to-yellow-600
        text-white
        px-8
        py-3
        rounded-full
        hover:from-orange-700 hover:to-yellow-700
        transition-all
        transform
        hover:scale-105
        duration-300
        flex
        items-center
        justify-center
        gap-2
        mx-auto
        shadow-lg
      "
      (click)="visitStore()"
    >
      <ion-icon name="cart-outline"></ion-icon>
      Visit Store
    </button>
  </div>
</div>
