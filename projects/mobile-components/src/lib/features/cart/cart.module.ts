import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import Cart components
// Standalone (1)
import { ShoppingCartCompactComponent } from './shopping-cart-compact/shopping-cart-compact.component';

// Non-Standalone (0)
// None

@NgModule({
  declarations: [
    // Non-Standalone Components (0)
    // None
  ],
  imports: [
    CommonModule,
    IonicModule,
    // Standalone Components (1)
    ShoppingCartCompactComponent,
  ],
  exports: [
    // Standalone Components (1)
    ShoppingCartCompactComponent,
  ]
})
export class CartModule { }
