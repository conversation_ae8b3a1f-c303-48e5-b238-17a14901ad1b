import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import Projects components
// Standalone (1)
import { ProjectListCompactComponent } from './project-list-compact/project-list-compact.component';

// Non-Standalone (0)
// None

@NgModule({
  declarations: [
    // Non-Standalone Components (0)
    // None
  ],
  imports: [
    CommonModule,
    IonicModule,
    // Standalone Components (1)
    ProjectListCompactComponent,
  ],
  exports: [
    // Standalone Components (1)
    ProjectListCompactComponent,
  ]
})
export class ProjectsModule { }
