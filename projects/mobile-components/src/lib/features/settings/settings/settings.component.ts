import { Component, Input, Output, EventEmitter, computed, signal, inject, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

// Security and validation imports
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';

// Types for settings
export interface SettingItem {
  id: string;
  title: string;
  description?: string;
  icon?: string;
  type: 'toggle' | 'select' | 'input' | 'button' | 'link' | 'slider' | 'radio';
  value?: any;
  options?: { label: string; value: any }[];
  route?: string;
  action?: string;
  disabled?: boolean;
  visible?: boolean;
  required?: boolean;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
  security?: {
    requireAuth?: boolean;
    permissions?: string[];
    encryptValue?: boolean;
  };
}

export interface SettingSection {
  id: string;
  title: string;
  description?: string;
  icon?: string;
  items: SettingItem[];
  collapsible?: boolean;
  collapsed?: boolean;
  visible?: boolean;
}

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
  role?: string;
  verified?: boolean;
}

export interface SettingsConfig {
  showProfile: boolean;
  showSections: boolean;
  enableSearch: boolean;
  enableExport: boolean;
  enableImport: boolean;
  enableReset: boolean;
  autoSave: boolean;
  saveDelay: number;
}

@Component({
  selector: 'app-settings',
  templateUrl: 'settings.component.html',
  styleUrls: ['settings.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule
  ]
})
export class SettingsComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();
  private readonly formBuilder = inject(FormBuilder);
  private readonly sanitizer = inject(DomSanitizer);

  // Input properties for LP-GO builder integration
  @Input() className: string = '';
  @Input() size: 'sm' | 'md' | 'lg' = 'md';
  @Input() variant: 'default' | 'compact' | 'detailed' | 'minimal' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() disabled: boolean = false;

  // Settings configuration
  @Input() config: Partial<SettingsConfig> = {};
  @Input() sections: SettingSection[] = [];
  @Input() userProfile: UserProfile | null = null;
  @Input() allowEditing: boolean = true;
  @Input() showAdvanced: boolean = false;

  // Layout options
  @Input() layout: 'list' | 'grid' | 'accordion' = 'list';
  @Input() showHeader: boolean = true;
  @Input() headerTitle: string = 'Settings';
  @Input() showFooter: boolean = false;
  @Input() footerText: string = '';

  // Event outputs
  @Output() settingChanged = new EventEmitter<{ item: SettingItem; value: any; section: string }>();
  @Output() settingsSaved = new EventEmitter<{ [key: string]: any }>();
  @Output() settingsReset = new EventEmitter<void>();
  @Output() profileUpdated = new EventEmitter<UserProfile>();
  @Output() sectionToggled = new EventEmitter<{ sectionId: string; collapsed: boolean }>();

  // Component state
  private readonly componentId = signal<string>(`settings-${Math.random().toString(36).substr(2, 9)}`);
  private readonly searchTerm = signal<string>('');
  private readonly hasUnsavedChanges = signal<boolean>(false);
  private readonly isLoading = signal<boolean>(false);

  // Forms
  public readonly settingsForm: FormGroup;
  private readonly validationErrors = signal<string[]>([]);

  // Default configuration
  private readonly defaultConfig: SettingsConfig = {
    showProfile: true,
    showSections: true,
    enableSearch: true,
    enableExport: false,
    enableImport: false,
    enableReset: true,
    autoSave: false,
    saveDelay: 1000
  };

  // Default sections
  private readonly defaultSections: SettingSection[] = [
    {
      id: 'account',
      title: 'Account',
      description: 'Manage your account settings',
      icon: 'person',
      items: [
        {
          id: 'notifications',
          title: 'Push Notifications',
          description: 'Receive push notifications',
          type: 'toggle',
          value: true,
          icon: 'notifications'
        },
        {
          id: 'email-notifications',
          title: 'Email Notifications',
          description: 'Receive email notifications',
          type: 'toggle',
          value: false,
          icon: 'mail'
        }
      ]
    },
    {
      id: 'security',
      title: 'Security & Privacy',
      description: 'Security and privacy settings',
      icon: 'lock-closed',
      items: [
        {
          id: 'two-factor',
          title: 'Two-Factor Authentication',
          description: 'Enable 2FA for extra security',
          type: 'toggle',
          value: false,
          icon: 'shield-checkmark',
          security: { requireAuth: true }
        },
        {
          id: 'biometric',
          title: 'Biometric Login',
          description: 'Use fingerprint or face recognition',
          type: 'toggle',
          value: true,
          icon: 'finger-print'
        }
      ]
    },
    {
      id: 'preferences',
      title: 'Preferences',
      description: 'App preferences and customization',
      icon: 'settings',
      items: [
        {
          id: 'theme',
          title: 'Theme',
          description: 'Choose your preferred theme',
          type: 'select',
          value: 'system',
          icon: 'color-palette',
          options: [
            { label: 'System', value: 'system' },
            { label: 'Light', value: 'light' },
            { label: 'Dark', value: 'dark' }
          ]
        },
        {
          id: 'language',
          title: 'Language',
          description: 'Select your language',
          type: 'select',
          value: 'en',
          icon: 'language',
          options: [
            { label: 'English', value: 'en' },
            { label: 'Spanish', value: 'es' },
            { label: 'French', value: 'fr' }
          ]
        }
      ]
    }
  ];

  // Default user profile
  private readonly defaultProfile: UserProfile = {
    id: 'user-1',
    name: 'Alan Montgomery',
    email: '<EMAIL>',
    phone: '123456789',
    avatar: './assets/images/avatar.png',
    role: 'user',
    verified: true
  };

  // Computed properties
  public readonly resolvedConfig = computed(() => ({
    ...this.defaultConfig,
    ...this.config
  }));

  public readonly resolvedSections = computed(() => 
    this.sections.length > 0 ? this.sections : this.defaultSections
  );

  public readonly resolvedProfile = computed(() => 
    this.userProfile || this.defaultProfile
  );

  public readonly filteredSections = computed(() => {
    const term = this.searchTerm().toLowerCase();
    if (!term) return this.resolvedSections();

    return this.resolvedSections().map(section => ({
      ...section,
      items: section.items.filter(item =>
        item.title.toLowerCase().includes(term) ||
        item.description?.toLowerCase().includes(term)
      )
    })).filter(section => section.items.length > 0);
  });

  public readonly computedClasses = computed(() => {
    const baseClasses = [
      'settings-component',
      'w-full',
      'transition-all duration-200'
    ];

    // Size classes
    const sizeClasses = {
      sm: ['text-sm', 'space-y-3'],
      md: ['text-base', 'space-y-4'],
      lg: ['text-lg', 'space-y-5']
    };

    // Variant classes
    const variantClasses = {
      default: ['bg-white dark:bg-gray-800', 'rounded-lg', 'p-6'],
      compact: ['bg-gray-50 dark:bg-gray-900', 'rounded-md', 'p-4'],
      detailed: ['bg-white dark:bg-gray-800', 'rounded-xl', 'p-8', 'shadow-lg'],
      minimal: ['bg-transparent', 'p-2']
    };

    // Layout classes
    const layoutClasses = {
      list: ['space-y-4'],
      grid: ['grid', 'grid-cols-1', 'md:grid-cols-2', 'gap-4'],
      accordion: ['divide-y', 'divide-gray-200', 'dark:divide-gray-700']
    };

    // Rounded classes
    const roundedClasses = {
      none: ['rounded-none'],
      sm: ['rounded-sm'],
      md: ['rounded-md'],
      lg: ['rounded-lg'],
      full: ['rounded-full']
    };

    // State classes
    const stateClasses = this.disabled ? ['opacity-50', 'pointer-events-none'] : [];

    return [
      ...baseClasses,
      ...sizeClasses[this.size],
      ...variantClasses[this.variant],
      ...layoutClasses[this.layout],
      ...roundedClasses[this.rounded],
      ...stateClasses,
      this.className
    ].filter(Boolean).join(' ');
  });

  public readonly sectionClasses = computed(() => [
    'setting-section',
    'bg-white dark:bg-gray-800',
    'border border-gray-200 dark:border-gray-700',
    'rounded-lg',
    'overflow-hidden',
    'transition-all duration-200'
  ].join(' '));

  public readonly itemClasses = computed(() => [
    'setting-item',
    'flex items-center justify-between',
    'p-4',
    'border-b border-gray-100 dark:border-gray-700 last:border-b-0',
    'hover:bg-gray-50 dark:hover:bg-gray-700',
    'transition-colors duration-200'
  ].join(' '));

  constructor() {
    // Initialize reactive form
    this.settingsForm = this.formBuilder.group({});
    this.initializeForm();
  }

  ngOnInit(): void {
    this.setupFormSubscription();
    this.validateConfiguration();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    const formControls: { [key: string]: any } = {};
    
    this.resolvedSections().forEach(section => {
      section.items.forEach(item => {
        formControls[item.id] = [item.value || ''];
      });
    });

    Object.keys(formControls).forEach(key => {
      this.settingsForm.addControl(key, this.formBuilder.control(formControls[key]));
    });
  }

  private setupFormSubscription(): void {
    if (!this.resolvedConfig().autoSave) return;

    this.settingsForm.valueChanges.pipe(
      debounceTime(this.resolvedConfig().saveDelay),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(() => {
      this.autoSaveSettings();
    });
  }

  private validateConfiguration(): void {
    const errors: string[] = [];
    
    if (this.resolvedConfig().saveDelay < 0) {
      errors.push('Save delay must be non-negative');
    }

    this.validationErrors.set(errors);
  }

  private autoSaveSettings(): void {
    if (this.settingsForm.valid) {
      this.saveSettings();
    }
  }

  // Public methods
  public onSettingChange(item: SettingItem, value: any, sectionId: string): void {
    if (this.disabled || item.disabled) return;

    // Update form control
    this.settingsForm.patchValue({ [item.id]: value });
    
    // Update item value
    item.value = value;
    
    // Mark as having unsaved changes
    this.hasUnsavedChanges.set(true);
    
    // Emit change event
    this.settingChanged.emit({ item, value, section: sectionId });
  }

  public saveSettings(): void {
    if (!this.settingsForm.valid) return;

    this.isLoading.set(true);
    
    // Simulate save operation
    setTimeout(() => {
      const settings = this.buildSettingsObject();
      this.settingsSaved.emit(settings);
      this.hasUnsavedChanges.set(false);
      this.isLoading.set(false);
    }, 500);
  }

  public resetSettings(): void {
    // Reset form to default values
    this.resolvedSections().forEach(section => {
      section.items.forEach(item => {
        const defaultValue = this.getDefaultValue(item);
        this.settingsForm.patchValue({ [item.id]: defaultValue });
        item.value = defaultValue;
      });
    });

    this.hasUnsavedChanges.set(false);
    this.settingsReset.emit();
  }

  public toggleSection(sectionId: string): void {
    const section = this.resolvedSections().find(s => s.id === sectionId);
    if (section && section.collapsible) {
      section.collapsed = !section.collapsed;
      this.sectionToggled.emit({ sectionId, collapsed: section.collapsed });
    }
  }

  public updateProfile(updates: Partial<UserProfile>): void {
    if (!this.userProfile) return;

    const updatedProfile = { ...this.userProfile, ...updates };
    this.profileUpdated.emit(updatedProfile);
  }

  public searchSettings(term: string): void {
    this.searchTerm.set(term);
  }

  public exportSettings(): string {
    return JSON.stringify(this.buildSettingsObject(), null, 2);
  }

  public importSettings(settingsJson: string): void {
    try {
      const settings = JSON.parse(settingsJson);
      this.applySettings(settings);
    } catch (error) {
      console.error('Invalid settings format:', error);
    }
  }

  private buildSettingsObject(): { [key: string]: any } {
    const settings: { [key: string]: any } = {};
    
    this.resolvedSections().forEach(section => {
      section.items.forEach(item => {
        settings[item.id] = item.value;
      });
    });

    return settings;
  }

  private applySettings(settings: { [key: string]: any }): void {
    Object.keys(settings).forEach(key => {
      if (this.settingsForm.controls[key]) {
        this.settingsForm.patchValue({ [key]: settings[key] });
        
        // Update item value
        this.resolvedSections().forEach(section => {
          const item = section.items.find(i => i.id === key);
          if (item) {
            item.value = settings[key];
          }
        });
      }
    });
  }

  private getDefaultValue(item: SettingItem): any {
    switch (item.type) {
      case 'toggle':
        return false;
      case 'select':
        return item.options?.[0]?.value || '';
      case 'slider':
        return item.validation?.min || 0;
      default:
        return '';
    }
  }

  // Security methods
  private sanitizeInput(input: string): string {
    if (!input) return '';
    return input.trim().replace(/[<>'"]/g, '');
  }

  public sanitizeHtml(html: string): SafeHtml {
    return this.sanitizer.sanitize(1, html) || '';
  }

  // Getters for template
  get hasChanges(): boolean {
    return this.hasUnsavedChanges();
  }

  get isFormValid(): boolean {
    return this.settingsForm.valid && this.validationErrors().length === 0;
  }

  get loadingState(): boolean {
    return this.isLoading();
  }

  get searchQuery(): string {
    return this.searchTerm();
  }

  get profile(): UserProfile {
    return this.resolvedProfile();
  }

  get settingSections(): SettingSection[] {
    return this.filteredSections();
  }

  get hasValidationErrors(): boolean {
    return this.validationErrors().length > 0;
  }

  get errors(): string[] {
    return this.validationErrors();
  }
}
