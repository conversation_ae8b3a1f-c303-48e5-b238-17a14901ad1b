<div [class]="computedClasses()" [attr.data-testid]="'settings-' + componentId()">
  
  <!-- Header Section -->
  @if (showHeader) {
    <div class="settings-header mb-6">
      <div class="flex items-center justify-between">
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
          {{ headerTitle }}
        </h1>
        
        @if (hasChanges && !resolvedConfig().autoSave) {
          <div class="flex items-center space-x-2">
            <button
              type="button"
              (click)="resetSettings()"
              class="px-3 py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
              [disabled]="disabled || loadingState">
              Reset
            </button>
            <button
              type="button"
              (click)="saveSettings()"
              class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
              [disabled]="disabled || !isFormValid || loadingState"
              [class.opacity-50]="loadingState">
              @if (loadingState) {
                <div class="flex items-center space-x-2">
                  <div class="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                  <span>Saving...</span>
                </div>
              } @else {
                Save Changes
              }
            </button>
          </div>
        }
      </div>

      <!-- Search (if enabled) -->
      @if (resolvedConfig().enableSearch) {
        <div class="mt-4">
          <div class="relative">
            <input
              type="text"
              placeholder="Search settings..."
              #searchInput
              (input)="searchSettings(searchInput.value)"
              class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              [disabled]="disabled">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
        </div>
      }
    </div>
  }

  <!-- User Profile Section -->
  @if (resolvedConfig().showProfile && profile) {
    <div class="profile-section mb-8">
      <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-4">
          <div class="relative">
            @if (profile.avatar) {
              <img
                [src]="profile.avatar"
                [alt]="profile.name + ' avatar'"
                class="w-16 h-16 rounded-full object-cover">
            } @else {
              <div class="w-16 h-16 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                <svg class="w-8 h-8 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                </svg>
              </div>
            }
            @if (profile.verified) {
              <div class="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                </svg>
              </div>
            }
          </div>
          
          <div class="flex-1 min-w-0">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate">
              {{ profile.name }}
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 truncate">
              {{ profile.email }}
            </p>
            @if (profile.phone) {
              <p class="text-sm text-gray-600 dark:text-gray-400">
                {{ profile.phone }}
              </p>
            }
            @if (profile.role) {
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 mt-1">
                {{ profile.role }}
              </span>
            }
          </div>
          
          @if (allowEditing) {
            <button
              type="button"
              class="px-3 py-1 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
              [disabled]="disabled">
              Edit Profile
            </button>
          }
        </div>
      </div>
    </div>
  }

  <!-- Settings Sections -->
  @if (resolvedConfig().showSections) {
    <form [formGroup]="settingsForm" class="settings-sections">
      @for (section of settingSections; track section.id) {
        <div [class]="sectionClasses()" class="mb-4">
          
          <!-- Section Header -->
          <div class="section-header p-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                @if (section.icon) {
                  <div class="w-6 h-6 text-gray-500 dark:text-gray-400" [attr.data-icon]="section.icon">
                    <!-- Icon placeholder -->
                  </div>
                }
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {{ section.title }}
                  </h3>
                  @if (section.description) {
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {{ section.description }}
                    </p>
                  }
                </div>
              </div>
              
              @if (section.collapsible) {
                <button
                  type="button"
                  (click)="toggleSection(section.id)"
                  class="p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  [disabled]="disabled"
                  [attr.aria-expanded]="!section.collapsed">
                  <svg class="w-5 h-5 text-gray-500 transition-transform" 
                       [class.rotate-180]="section.collapsed"
                       fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
              }
            </div>
          </div>

          <!-- Section Items -->
          @if (!section.collapsed) {
            <div class="section-items">
              @for (item of section.items; track item.id) {
                @if (item.visible !== false) {
                  <div [class]="itemClasses()">
                    
                    <!-- Item Info -->
                    <div class="flex items-center space-x-3 flex-1 min-w-0">
                      @if (item.icon) {
                        <div class="w-5 h-5 text-gray-500 dark:text-gray-400 flex-shrink-0" [attr.data-icon]="item.icon">
                          <!-- Icon placeholder -->
                        </div>
                      }
                      
                      <div class="flex-1 min-w-0">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {{ item.title }}
                          @if (item.required) {
                            <span class="text-red-500 ml-1">*</span>
                          }
                        </h4>
                        @if (item.description) {
                          <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                            {{ item.description }}
                          </p>
                        }
                      </div>
                    </div>

                    <!-- Item Control -->
                    <div class="flex-shrink-0 ml-4">
                      
                      <!-- Toggle Control -->
                      @if (item.type === 'toggle') {
                        <label class="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            [formControlName]="item.id"
                            (change)="onSettingChange(item, $event.target.checked, section.id)"
                            class="sr-only peer"
                            [disabled]="disabled || item.disabled">
                          <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        </label>
                      }

                      <!-- Select Control -->
                      @if (item.type === 'select') {
                        <select
                          [formControlName]="item.id"
                          (change)="onSettingChange(item, $event.target.value, section.id)"
                          class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          [disabled]="disabled || item.disabled">
                          @for (option of item.options; track option.value) {
                            <option [value]="option.value">{{ option.label }}</option>
                          }
                        </select>
                      }

                      <!-- Input Control -->
                      @if (item.type === 'input') {
                        <input
                          type="text"
                          [formControlName]="item.id"
                          (input)="onSettingChange(item, $event.target.value, section.id)"
                          class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          [disabled]="disabled || item.disabled"
                          [placeholder]="item.description || ''"
                          [pattern]="item.validation?.pattern">
                      }

                      <!-- Button Control -->
                      @if (item.type === 'button') {
                        <button
                          type="button"
                          class="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
                          [disabled]="disabled || item.disabled">
                          {{ item.title }}
                        </button>
                      }

                      <!-- Link Control -->
                      @if (item.type === 'link') {
                        <a
                          [routerLink]="item.route"
                          class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors">
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                          </svg>
                        </a>
                      }

                      <!-- Slider Control -->
                      @if (item.type === 'slider') {
                        <div class="flex items-center space-x-2">
                          <input
                            type="range"
                            [formControlName]="item.id"
                            (input)="onSettingChange(item, $event.target.value, section.id)"
                            class="w-20 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                            [min]="item.validation?.min || 0"
                            [max]="item.validation?.max || 100"
                            [disabled]="disabled || item.disabled">
                          <span class="text-xs text-gray-600 dark:text-gray-400 min-w-8">
                            {{ settingsForm.get(item.id)?.value }}
                          </span>
                        </div>
                      }

                    </div>
                  </div>
                }
              }
            </div>
          }
        </div>
      }
    </form>
  }

  <!-- Footer Section -->
  @if (showFooter && footerText) {
    <div class="settings-footer mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
      <p class="text-sm text-gray-600 dark:text-gray-400 text-center">
        {{ footerText }}
      </p>
    </div>
  }

  <!-- Action Buttons (if not auto-save) -->
  @if (!resolvedConfig().autoSave && hasChanges) {
    <div class="fixed bottom-4 right-4 flex space-x-2">
      <button
        type="button"
        (click)="resetSettings()"
        class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500"
        [disabled]="disabled || loadingState">
        Cancel
      </button>
      <button
        type="button"
        (click)="saveSettings()"
        class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
        [disabled]="disabled || !isFormValid || loadingState"
        [class.opacity-50]="loadingState">
        @if (loadingState) {
          <div class="flex items-center space-x-2">
            <div class="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
            <span>Saving...</span>
          </div>
        } @else {
          Save All
        }
      </button>
    </div>
  }

  <!-- Validation Errors -->
  @if (hasValidationErrors) {
    <div class="validation-errors mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
      <h4 class="text-sm font-medium text-red-800 dark:text-red-200 mb-1">Configuration Errors:</h4>
      <ul class="text-sm text-red-700 dark:text-red-300 list-disc list-inside">
        @for (error of errors; track error) {
          <li>{{ error }}</li>
        }
      </ul>
    </div>
  }

  <!-- Loading Overlay -->
  @if (loadingState) {
    <div class="absolute inset-0 bg-white dark:bg-gray-800 bg-opacity-50 dark:bg-opacity-50 flex items-center justify-center z-50 rounded-lg">
      <div class="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
        <div class="animate-spin h-6 w-6 border-2 border-blue-500 border-t-transparent rounded-full"></div>
        <span>Saving settings...</span>
      </div>
    </div>
  }

</div>
        <ion-icon name="person-outline"></ion-icon>
      </ion-avatar>
      <ion-label>
        <h2>Profile</h2>
      </ion-label>
    </ion-item>
    <ion-item routerLink="/public/password">
      <ion-avatar slot="start" style="background: var(--ion-color-warning)">
        <ion-icon name="lock-closed-outline"></ion-icon>
      </ion-avatar>
      <ion-label>
        <h2>Password</h2>
      </ion-label>
    </ion-item>
    <ion-item>
      <ion-avatar slot="start" style="background: #97c66b">
        <ion-icon name="shield-outline"></ion-icon>
      </ion-avatar>
      <ion-label>
        <h2>2-Step Verification</h2>
      </ion-label>
    </ion-item>
    <ion-item>
      <ion-avatar slot="start" style="background: var(--ion-color-danger)">
        <ion-icon name="notifications-outline"></ion-icon>
      </ion-avatar>
      <ion-label>
        <h2>Login Alerts</h2>
      </ion-label>
    </ion-item>
    <ion-item>
      <ion-avatar slot="start" style="background: #ac85d4">
        <ion-icon name="laptop-outline"></ion-icon>
      </ion-avatar>
      <ion-label>
        <h2>Device History</h2>
      </ion-label>
    </ion-item>
  </ion-list>

  <!-- Social section -->
  <ion-list class="mt-6">
    <ion-list-header>
      <ion-label class="text-lg font-medium">Social</ion-label>
    </ion-list-header>
    <ion-item>
      <ion-avatar slot="start" style="background: #567aa3">
        <ion-icon name="logo-facebook"></ion-icon>
      </ion-avatar>
      <ion-label>
        <h2>Facebook</h2>
      </ion-label>
    </ion-item>
    <ion-item>
      <ion-avatar slot="start" style="background: #48cff5">
        <ion-icon name="logo-twitter"></ion-icon>
      </ion-avatar>
      <ion-label>
        <h2>Twitter</h2>
      </ion-label>
    </ion-item>
    <ion-item>
      <ion-avatar slot="start" style="background: #0077b5">
        <ion-icon name="logo-linkedin"></ion-icon>
      </ion-avatar>
      <ion-label>
        <h2>LinkedIn</h2>
      </ion-label>
    </ion-item>
  </ion-list>

  <!-- Help and Support section -->
  <ion-list class="mt-6">
    <ion-list-header>
      <ion-label class="text-lg font-medium">Help and Support</ion-label>
    </ion-list-header>
    <ion-item routerLink="/secure/contactus">
      <ion-avatar slot="start" style="background: #97c66b">
        <ion-icon name="call-outline"></ion-icon>
      </ion-avatar>
      <ion-label>
        <h2>Contact Us</h2>
      </ion-label>
    </ion-item>
    <ion-item>
      <ion-avatar slot="start" style="background: var(--ion-color-warning)">
        <ion-icon name="document-outline"></ion-icon>
      </ion-avatar>
      <ion-label>
        <h2>Terms and Conditions</h2>
      </ion-label>
    </ion-item>
  </ion-list>
</ion-content>
