.avatar {
    display: flex;
    justify-content: center;
    position: relative;

    img {
        border-radius: 50%;
        height: 120px;
    }
}

ion-item {
    
    ion-avatar {
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--ion-color-primary);

        ion-icon {
            color: #fff;
            font-size: 1.5rem;
        }
    }

    ion-label {
        --color: initial;
        display: block;
        font-size: inherit;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;

        h2 {
            margin-left: 0;
            margin-right: 0;
            margin-top: 2px;
            margin-bottom: 2px;
            font-size: 16px;
            font-weight: normal;
        }
    }
}