
/* Base Component Styles */
.theme-switch {
  position: relative;
  font-family: inherit;
  line-height: 1.5;
}

/* Theme Button Styles */
.theme-button {
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.theme-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.theme-button:active:not(:disabled) {
  transform: translateY(0);
}

.theme-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Theme Button Animation */
.theme-button:focus {
  animation: pulse-ring 0.6s ease-out;
}

@keyframes pulse-ring {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  100% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
}

/* Active Theme Indicator */
.theme-button .absolute {
  animation: scale-in 0.2s ease-out;
}

@keyframes scale-in {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Theme Preview Styles */
.theme-preview {
  transition: all 0.2s ease-in-out;
}

.theme-preview:hover {
  transform: scale(1.05);
}

.theme-preview .w-3 {
  transition: all 0.2s ease-in-out;
}

.theme-preview:hover .w-3 {
  transform: scale(1.1);
}

/* Current Theme Display */
.current-theme-display {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(147, 51, 234, 0.05));
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 0.5rem;
  padding: 0.75rem;
}

.dark .current-theme-display {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  border-color: rgba(59, 130, 246, 0.2);
}

/* System Status Indicator */
.system-status .w-2 {
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 0 4px currentColor;
  }
  50% {
    opacity: 0.7;
    box-shadow: 0 0 8px currentColor;
  }
}

/* Quick Toggle Button */
.quick-toggle button {
  transition: all 0.2s ease-in-out;
}

.quick-toggle button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quick-toggle button:active:not(:disabled) {
  transform: translateY(0);
}

/* Transition Effects */
.theme-switch.transitioning {
  pointer-events: none;
}

.theme-switch.transitioning .theme-button {
  animation: button-fade 0.3s ease-in-out;
}

@keyframes button-fade {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Layout Specific Styles */
.theme-switch .theme-options.flex {
  flex-wrap: wrap;
}

.theme-switch .theme-options.grid {
  align-items: start;
}

/* Size Variants */
.theme-switch.size-sm .theme-button {
  padding: 0.25rem;
}

.theme-switch.size-lg .theme-button {
  padding: 0.75rem;
}

/* Variant Specific Styles */
.theme-switch.variant-compact {
  background: rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.dark .theme-switch.variant-compact {
  background: rgba(255, 255, 255, 0.02);
  border-color: rgba(255, 255, 255, 0.05);
}

.theme-switch.variant-detailed {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
}

.theme-switch.variant-icon-only {
  background: transparent;
  border: none;
  padding: 0;
}

.theme-switch.variant-icon-only .theme-button {
  margin: 0.125rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .theme-switch .theme-options.flex {
    flex-direction: column;
    space-x: 0;
    gap: 0.5rem;
  }
  
  .theme-switch .theme-options.grid {
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
  }
  
  .theme-button {
    min-width: 60px;
  }
  
  .current-theme-display {
    padding: 0.5rem;
  }
}

@media (max-width: 480px) {
  .theme-switch {
    padding: 0.5rem;
  }
  
  .theme-switch .theme-options.grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .current-theme-display .flex {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .theme-button {
    border-width: 3px;
    font-weight: 600;
  }
  
  .theme-switch.variant-default,
  .theme-switch.variant-detailed {
    border-width: 2px;
  }
  
  .current-theme-display {
    border-width: 2px;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .theme-switch *,
  .theme-switch *::before,
  .theme-switch *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .theme-button:hover:not(:disabled) {
    transform: none;
  }
  
  .theme-preview:hover {
    transform: none;
  }
}

/* Print Styles */
@media print {
  .theme-switch {
    background: white !important;
    color: black !important;
    border: 1px solid black;
    box-shadow: none;
  }
  
  .theme-button {
    border: 1px solid black !important;
    background: white !important;
    color: black !important;
  }
  
  .theme-button.active::after {
    content: ' (Selected)';
    font-weight: bold;
  }
  
  .quick-toggle,
  .system-status {
    display: none;
  }
}

/* Focus Visible Support */
.theme-switch .theme-button:focus-visible {
  outline: 2px solid theme('colors.blue.500');
  outline-offset: 2px;
}

.theme-switch .quick-toggle button:focus-visible {
  outline: 2px solid theme('colors.blue.500');
  outline-offset: 2px;
}

/* Dark Mode Enhancements */
.dark .theme-switch {
  color-scheme: dark;
}

.dark .theme-button:hover:not(:disabled) {
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
}

.dark .theme-preview:hover {
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
}

/* Animation Performance */
.theme-switch {
  contain: layout style paint;
}

.theme-button {
  contain: layout style paint;
  will-change: transform, box-shadow;
}

.theme-preview {
  contain: layout style paint;
  will-change: transform;
}

/* Custom Properties for Theming */
.theme-switch {
  --theme-transition-duration: 0.2s;
  --theme-button-hover-scale: translateY(-1px);
  --theme-button-active-scale: translateY(0);
  --theme-glow-color: rgba(59, 130, 246, 0.3);
}

/* Loading State Enhancement */
.theme-switch .animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Theme Switching Animation */
@media (prefers-reduced-motion: no-preference) {
  .theme-switch.transitioning::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle, transparent 0%, rgba(255, 255, 255, 0.8) 100%);
    animation: theme-transition 0.3s ease-in-out;
    pointer-events: none;
    z-index: 5;
  }
  
  .dark .theme-switch.transitioning::before {
    background: radial-gradient(circle, transparent 0%, rgba(0, 0, 0, 0.8) 100%);
  }
  
  @keyframes theme-transition {
    0% {
      opacity: 0;
      transform: scale(0.8);
    }
    50% {
      opacity: 1;
      transform: scale(1.1);
    }
    100% {
      opacity: 0;
      transform: scale(1);
    }
  }
}