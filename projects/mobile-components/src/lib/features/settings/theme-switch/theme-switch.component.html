<div [class]="computedClasses()" [attr.data-testid]="'theme-switch-' + componentId()">
  
  <!-- Current Theme Display -->
  @if (showCurrentTheme && variant !== 'icon-only') {
    <div class="current-theme-display mb-3">
      <div class="flex items-center space-x-2">
        @if (showIcons && current.icon) {
          <div class="w-5 h-5 text-gray-600 dark:text-gray-400">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="w-full h-full">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="getThemeIcon(current)" />
            </svg>
          </div>
        }
        <span class="text-sm font-medium text-gray-900 dark:text-gray-100">
          Current: {{ current.displayName }}
        </span>
        @if (current.id === 'system') {
          <span class="text-xs text-gray-500 dark:text-gray-400 px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded">
            ({{ effective }})
          </span>
        }
      </div>
      @if (current.description && variant === 'detailed') {
        <p class="text-xs text-gray-600 dark:text-gray-400 mt-1 ml-7">
          {{ current.description }}
        </p>
      }
    </div>
  }

  <!-- Theme Options -->
  <div class="theme-options" 
       [class.flex]="layout === 'horizontal'"
       [class.flex-col]="layout === 'vertical'"
       [class.grid]="layout === 'grid'"
       [class.space-x-2]="layout === 'horizontal'"
       [class.space-y-2]="layout === 'vertical'"
       [class.gap-2]="layout === 'grid'">
    
    @for (theme of availableThemes; track theme.id) {
      @if (!theme.disabled || isThemeActive(theme.id)) {
        <div class="theme-option">
          
          <!-- Theme Button -->
          <button
            type="button"
            (click)="setTheme(theme.id)"
            [class]="isThemeActive(theme.id) ? activeButtonClasses() : inactiveButtonClasses()"
            [disabled]="disabled || theme.disabled"
            [attr.aria-pressed]="isThemeActive(theme.id)"
            [attr.aria-label]="'Switch to ' + theme.displayName + ' theme'"
            [title]="theme.description || theme.displayName">
            
            <!-- Icon -->
            @if (showIcons && theme.icon) {
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" 
                   class="w-5 h-5" 
                   [class.w-4]="size === 'sm'"
                   [class.h-4]="size === 'sm'"
                   [class.w-6]="size === 'lg'"
                   [class.h-6]="size === 'lg'">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="getThemeIcon(theme)" />
              </svg>
            }
            
            <!-- Label (for non-icon-only variants) -->
            @if (showLabels && variant !== 'icon-only') {
              <span class="ml-2 font-medium">{{ theme.displayName }}</span>
            }
            
            <!-- Active Indicator -->
            @if (isThemeActive(theme.id)) {
              <div class="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full border-2 border-white dark:border-gray-800"></div>
            }
          </button>

          <!-- Theme Preview (detailed variant) -->
          @if (showPreview && theme.preview) {
            <div class="theme-preview mt-2 p-2 border border-gray-200 dark:border-gray-600 rounded text-center">
              <div class="flex space-x-1 mb-1">
                <div class="w-3 h-3 rounded-full" [style.background]="theme.preview.primary"></div>
                <div class="w-3 h-3 rounded-full" [style.background]="theme.preview.secondary"></div>
              </div>
              <div class="w-full h-4 rounded" [style.background]="theme.preview.background"></div>
              <div class="text-xs mt-1" [style.color]="theme.preview.text">{{ theme.displayName }}</div>
            </div>
          }

          <!-- Label Below (for icon-only compact mode) -->
          @if (variant === 'icon-only' && showLabels) {
            <div class="text-xs text-center mt-1 text-gray-600 dark:text-gray-400">
              {{ theme.displayName }}
            </div>
          }
        </div>
      }
    }
  </div>

  <!-- Quick Toggle Button (for compact layouts) -->
  @if (compactMode && canToggle) {
    <div class="quick-toggle mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
      <button
        type="button"
        (click)="toggleTheme()"
        class="w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
        [disabled]="disabled">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        <span>Toggle Theme</span>
      </button>
    </div>
  }

  <!-- System Theme Detection Status -->
  @if (resolvedConfig().enableSystemDetection && variant === 'detailed') {
    <div class="system-status mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400">
        <span>System Preference:</span>
        <div class="flex items-center space-x-1">
          <div class="w-2 h-2 rounded-full" 
               [class.bg-yellow-400]="effective === 'light'"
               [class.bg-blue-400]="effective === 'dark'"></div>
          <span class="capitalize">{{ effective }}</span>
        </div>
      </div>
    </div>
  }

  <!-- Transition Overlay -->
  @if (transitioning && animateTransition) {
    <div class="absolute inset-0 bg-white dark:bg-gray-800 bg-opacity-50 dark:bg-opacity-50 flex items-center justify-center rounded-lg z-10 transition-opacity">
      <div class="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
        <div class="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
        <span class="text-sm">Switching theme...</span>
      </div>
    </div>
  }

  <!-- Screen Reader Announcements -->
  <div class="sr-only" aria-live="polite">
    Theme switched to {{ current.displayName }}
    @if (current.id === 'system') {
      using {{ effective }} system preference
    }
  </div>

</div>
