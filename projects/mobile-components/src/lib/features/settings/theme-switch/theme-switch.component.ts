import { Component, Input, Output, EventEmitter, computed, signal, inject, OnInit, On<PERSON><PERSON>roy, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

// Security and utility imports
import { Subject, fromEvent, takeUntil } from 'rxjs';

// Types for theme management
export interface ThemeOption {
  id: string;
  name: string;
  displayName: string;
  icon?: string;
  preview?: {
    primary: string;
    secondary: string;
    background: string;
    text: string;
  };
  description?: string;
  disabled?: boolean;
}

export interface ThemeConfig {
  enableSystemDetection: boolean;
  enableTransitions: boolean;
  transitionDuration: number;
  persistPreference: boolean;
  storageKey: string;
  showLabels: boolean;
  showIcons: boolean;
  showPreview: boolean;
}

@Component({
  selector: 'base-theme-switch',
  templateUrl: './theme-switch.component.html',
  styleUrls: ['./theme-switch.component.css'],
  standalone: true,
  imports: [CommonModule]
})
export class ThemeSwitchComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();
  private readonly sanitizer = inject(DomSanitizer);
  private readonly platformId = inject(PLATFORM_ID);
  private readonly isBrowser = isPlatformBrowser(this.platformId);

  // Input properties for LP-GO builder integration
  @Input() className: string = '';
  @Input() size: 'sm' | 'md' | 'lg' = 'md';
  @Input() variant: 'default' | 'compact' | 'detailed' | 'icon-only' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() disabled: boolean = false;

  // Theme configuration
  @Input() config: Partial<ThemeConfig> = {};
  @Input() themes: ThemeOption[] = [];
  @Input() defaultTheme: string = 'system';
  @Input() allowCustomThemes: boolean = false;

  // Layout and styling
  @Input() layout: 'horizontal' | 'vertical' | 'grid' = 'horizontal';
  @Input() showCurrentTheme: boolean = true;
  @Input() animateTransition: boolean = true;
  @Input() compactMode: boolean = false;

  // Event outputs
  @Output() themeChanged = new EventEmitter<ThemeOption>();
  @Output() systemThemeDetected = new EventEmitter<'light' | 'dark'>();
  @Output() transitionStart = new EventEmitter<void>();
  @Output() transitionEnd = new EventEmitter<void>();

  // Component state
  public readonly componentId = signal<string>(`theme-switch-${Math.random().toString(36).substr(2, 9)}`);
  private readonly currentTheme = signal<string>('system');
  private readonly systemTheme = signal<'light' | 'dark'>('light');
  private readonly isTransitioning = signal<boolean>(false);

  // Default configuration
  private readonly defaultConfig: ThemeConfig = {
    enableSystemDetection: true,
    enableTransitions: true,
    transitionDuration: 300,
    persistPreference: true,
    storageKey: 'app-theme',
    showLabels: true,
    showIcons: true,
    showPreview: false
  };

  // Default theme options
  private readonly defaultThemes: ThemeOption[] = [
    {
      id: 'system',
      name: 'system',
      displayName: 'System',
      icon: 'desktop-computer',
      description: 'Use system preference',
      preview: {
        primary: '#3B82F6',
        secondary: '#6B7280',
        background: 'linear-gradient(45deg, #F9FAFB, #1F2937)',
        text: '#374151'
      }
    },
    {
      id: 'light',
      name: 'light',
      displayName: 'Light',
      icon: 'sun',
      description: 'Light theme',
      preview: {
        primary: '#3B82F6',
        secondary: '#6B7280',
        background: '#FFFFFF',
        text: '#374151'
      }
    },
    {
      id: 'dark',
      name: 'dark',
      displayName: 'Dark',
      icon: 'moon',
      description: 'Dark theme',
      preview: {
        primary: '#60A5FA',
        secondary: '#9CA3AF',
        background: '#1F2937',
        text: '#F9FAFB'
      }
    }
  ];

  // Computed properties
  public readonly resolvedConfig = computed(() => ({
    ...this.defaultConfig,
    ...this.config
  }));

  public readonly resolvedThemes = computed(() => 
    this.themes.length > 0 ? this.themes : this.defaultThemes
  );

  public readonly activeTheme = computed(() => {
    const themes = this.resolvedThemes();
    return themes.find(theme => theme.id === this.currentTheme()) || themes[0];
  });

  public readonly effectiveTheme = computed(() => {
    const current = this.currentTheme();
    if (current === 'system') {
      return this.systemTheme();
    }
    return current;
  });

  public readonly computedClasses = computed(() => {
    const baseClasses = [
      'theme-switch',
      'transition-all duration-200'
    ];

    // Size classes
    const sizeClasses = {
      sm: ['text-sm', 'space-x-1'],
      md: ['text-base', 'space-x-2'],
      lg: ['text-lg', 'space-x-3']
    };

    // Variant classes
    const variantClasses = {
      default: ['bg-white dark:bg-gray-800', 'border border-gray-200 dark:border-gray-700', 'rounded-lg', 'p-3'],
      compact: ['bg-gray-50 dark:bg-gray-900', 'rounded-md', 'p-2'],
      detailed: ['bg-white dark:bg-gray-800', 'border border-gray-200 dark:border-gray-700', 'rounded-xl', 'p-4', 'shadow-sm'],
      'icon-only': ['bg-transparent', 'p-1']
    };

    // Layout classes
    const layoutClasses = {
      horizontal: ['flex', 'items-center'],
      vertical: ['flex', 'flex-col', 'space-y-2'],
      grid: ['grid', 'grid-cols-2', 'md:grid-cols-3', 'gap-2']
    };

    // Rounded classes
    const roundedClasses = {
      none: ['rounded-none'],
      sm: ['rounded-sm'],
      md: ['rounded-md'],
      lg: ['rounded-lg'],
      full: ['rounded-full']
    };

    // State classes
    const stateClasses = this.disabled ? ['opacity-50', 'pointer-events-none'] : [];
    const transitionClasses = this.isTransitioning() ? ['transitioning'] : [];

    return [
      ...baseClasses,
      ...sizeClasses[this.size],
      ...variantClasses[this.variant],
      ...layoutClasses[this.layout],
      ...roundedClasses[this.rounded],
      ...stateClasses,
      ...transitionClasses,
      this.className
    ].filter(Boolean).join(' ');
  });

  public readonly themeButtonClasses = computed(() => {
    const baseClasses = [
      'theme-button',
      'flex items-center justify-center',
      'border-2 transition-all duration-200',
      'focus:outline-none focus:ring-2 focus:ring-blue-500',
      'hover:shadow-sm'
    ];

    const sizeClasses = {
      sm: ['h-8 w-8', 'text-xs'],
      md: ['h-10 w-10', 'text-sm'],
      lg: ['h-12 w-12', 'text-base']
    };

    const roundedClasses = {
      none: ['rounded-none'],
      sm: ['rounded-sm'],
      md: ['rounded-md'],
      lg: ['rounded-lg'],
      full: ['rounded-full']
    };

    return [
      ...baseClasses,
      ...sizeClasses[this.size],
      ...roundedClasses[this.rounded]
    ].filter(Boolean).join(' ');
  });

  public readonly activeButtonClasses = computed(() => [
    this.themeButtonClasses(),
    'border-blue-500 bg-blue-50 dark:bg-blue-900',
    'text-blue-700 dark:text-blue-300'
  ].join(' '));

  public readonly inactiveButtonClasses = computed(() => [
    this.themeButtonClasses(),
    'border-gray-300 dark:border-gray-600',
    'bg-white dark:bg-gray-800',
    'text-gray-700 dark:text-gray-300',
    'hover:border-gray-400 dark:hover:border-gray-500',
    'hover:bg-gray-50 dark:hover:bg-gray-700'
  ].join(' '));

  ngOnInit(): void {
    this.initializeTheme();
    this.setupSystemThemeDetection();
    this.loadStoredTheme();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeTheme(): void {
    const defaultTheme = this.resolvedThemes().find(t => t.id === this.defaultTheme) || this.resolvedThemes()[0];
    this.currentTheme.set(defaultTheme.id);
  }

  private setupSystemThemeDetection(): void {
    if (!this.isBrowser || !this.resolvedConfig().enableSystemDetection) return;

    // Initial system theme detection
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    this.updateSystemTheme(mediaQuery.matches);

    // Listen for changes
    fromEvent(mediaQuery, 'change').pipe(
      takeUntil(this.destroy$)
    ).subscribe((event: any) => {
      this.updateSystemTheme(event.matches);
    });
  }

  private updateSystemTheme(isDark: boolean): void {
    const theme = isDark ? 'dark' : 'light';
    this.systemTheme.set(theme);
    this.systemThemeDetected.emit(theme);

    // Apply theme if current is system
    if (this.currentTheme() === 'system') {
      this.applyTheme(theme);
    }
  }

  private loadStoredTheme(): void {
    if (!this.isBrowser || !this.resolvedConfig().persistPreference) return;

    const stored = localStorage.getItem(this.resolvedConfig().storageKey);
    if (stored && this.resolvedThemes().some(t => t.id === stored)) {
      this.setTheme(stored);
    }
  }

  private storeTheme(themeId: string): void {
    if (!this.isBrowser || !this.resolvedConfig().persistPreference) return;

    localStorage.setItem(this.resolvedConfig().storageKey, themeId);
  }

  private applyTheme(themeName: string): void {
    if (!this.isBrowser) return;

    const body = document.body;
    const html = document.documentElement;

    // Remove existing theme classes
    body.classList.remove('light', 'dark');
    html.classList.remove('light', 'dark');

    // Add new theme class
    body.classList.add(themeName);
    html.classList.add(themeName);

    // Update CSS custom properties if needed
    this.updateThemeProperties(themeName);
  }

  private updateThemeProperties(themeName: string): void {
    const theme = this.resolvedThemes().find(t => t.id === themeName);
    if (!theme?.preview) return;

    const root = document.documentElement;
    root.style.setProperty('--theme-primary', theme.preview.primary);
    root.style.setProperty('--theme-secondary', theme.preview.secondary);
    root.style.setProperty('--theme-background', theme.preview.background);
    root.style.setProperty('--theme-text', theme.preview.text);
  }

  // Public methods
  public setTheme(themeId: string): void {
    if (this.disabled) return;

    const theme = this.resolvedThemes().find(t => t.id === themeId);
    if (!theme || theme.disabled) return;

    if (this.resolvedConfig().enableTransitions) {
      this.startTransition();
    }

    this.currentTheme.set(themeId);
    this.storeTheme(themeId);

    // Apply the actual theme
    const effectiveTheme = themeId === 'system' ? this.systemTheme() : themeId;
    this.applyTheme(effectiveTheme);

    this.themeChanged.emit(theme);

    if (this.resolvedConfig().enableTransitions) {
      setTimeout(() => {
        this.endTransition();
      }, this.resolvedConfig().transitionDuration);
    }
  }

  public toggleTheme(): void {
    const themes = this.resolvedThemes().filter(t => !t.disabled);
    const currentIndex = themes.findIndex(t => t.id === this.currentTheme());
    const nextIndex = (currentIndex + 1) % themes.length;
    this.setTheme(themes[nextIndex].id);
  }

  public isThemeActive(themeId: string): boolean {
    return this.currentTheme() === themeId;
  }

  public getThemeIcon(theme: ThemeOption): string {
    if (!theme.icon) return '';
    
    // Return SVG path or icon class based on theme
    const icons: { [key: string]: string } = {
      'sun': 'M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z',
      'moon': 'M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z',
      'desktop-computer': 'M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z'
    };

    return icons[theme.icon] || '';
  }

  private startTransition(): void {
    this.isTransitioning.set(true);
    this.transitionStart.emit();
  }

  private endTransition(): void {
    this.isTransitioning.set(false);
    this.transitionEnd.emit();
  }

  // Security methods
  public sanitizeHtml(html: string): SafeHtml {
    return this.sanitizer.sanitize(1, html) || '';
  }

  // Getters for template
  get availableThemes(): ThemeOption[] {
    return this.resolvedThemes();
  }

  get current(): ThemeOption {
    return this.activeTheme();
  }

  get effective(): string {
    return this.effectiveTheme();
  }

  get transitioning(): boolean {
    return this.isTransitioning();
  }

  get showLabels(): boolean {
    return this.resolvedConfig().showLabels && this.variant !== 'icon-only';
  }

  get showIcons(): boolean {
    return this.resolvedConfig().showIcons;
  }

  get showPreview(): boolean {
    return this.resolvedConfig().showPreview && this.variant === 'detailed';
  }

  get canToggle(): boolean {
    return !this.disabled && this.resolvedThemes().filter(t => !t.disabled).length > 1;
  }
}
