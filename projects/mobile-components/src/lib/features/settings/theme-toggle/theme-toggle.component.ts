import { Component, Input, Output, EventEmitter, computed, signal, inject, OnInit, OnDestroy, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

// Security and utility imports
import { Subject, fromEvent, takeUntil } from 'rxjs';

// Types for theme toggle
export interface ToggleTheme {
  id: 'light' | 'dark' | 'system';
  name: string;
  icon: string;
  description?: string;
}

export interface ThemeToggleConfig {
  enableSystemDetection: boolean;
  enableTransitions: boolean;
  transitionDuration: number;
  persistPreference: boolean;
  storageKey: string;
  showTooltip: boolean;
  showLabel: boolean;
  animateIcon: boolean;
}

@Component({
  selector: 'base-theme-toggle',
  templateUrl: './theme-toggle.component.html',
  styleUrls: ['./theme-toggle.component.css'],
  standalone: true,
  imports: [CommonModule]
})
export class ThemeToggleComponent implements OnInit, OnD<PERSON>roy {
  private readonly destroy$ = new Subject<void>();
  private readonly sanitizer = inject(DomSanitizer);
  private readonly platformId = inject(PLATFORM_ID);
  private readonly isBrowser = isPlatformBrowser(this.platformId);

  // Input properties for LP-GO builder integration
  @Input() className: string = '';
  @Input() size: 'sm' | 'md' | 'lg' = 'md';
  @Input() variant: 'default' | 'compact' | 'minimal' | 'floating' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'full';
  @Input() disabled: boolean = false;

  // Theme toggle configuration
  @Input() config: Partial<ThemeToggleConfig> = {};
  @Input() defaultTheme: 'light' | 'dark' | 'system' = 'system';
  @Input() enableSystemTheme: boolean = true;
  @Input() showSystemOption: boolean = false;

  // Styling and behavior
  @Input() position: 'static' | 'fixed' | 'sticky' = 'static';
  @Input() tooltipPosition: 'top' | 'bottom' | 'left' | 'right' = 'bottom';
  @Input() hideWhenSystem: boolean = false;

  // Event outputs
  @Output() themeChanged = new EventEmitter<'light' | 'dark' | 'system'>();
  @Output() systemThemeDetected = new EventEmitter<'light' | 'dark'>();
  @Output() toggleClicked = new EventEmitter<void>();

  // Component state
  public readonly componentId = signal<string>(`theme-toggle-${Math.random().toString(36).substr(2, 9)}`);
  public readonly currentTheme = signal<'light' | 'dark' | 'system'>('system');
  private readonly systemTheme = signal<'light' | 'dark'>('light');
  private readonly isTransitioning = signal<boolean>(false);
  private readonly isHovered = signal<boolean>(false);

  // Default configuration
  private readonly defaultConfig: ThemeToggleConfig = {
    enableSystemDetection: true,
    enableTransitions: true,
    transitionDuration: 200,
    persistPreference: true,
    storageKey: 'theme-preference',
    showTooltip: true,
    showLabel: false,
    animateIcon: true
  };

  // Theme definitions
  private readonly themes: ToggleTheme[] = [
    {
      id: 'light',
      name: 'Light',
      icon: 'sun',
      description: 'Light theme'
    },
    {
      id: 'dark',
      name: 'Dark',
      icon: 'moon',
      description: 'Dark theme'
    },
    {
      id: 'system',
      name: 'System',
      icon: 'desktop',
      description: 'Follow system preference'
    }
  ];

  // Computed properties
  public readonly resolvedConfig = computed(() => ({
    ...this.defaultConfig,
    ...this.config
  }));

  public readonly currentThemeInfo = computed(() => 
    this.themes.find(t => t.id === this.currentTheme()) || this.themes[0]
  );

  public readonly effectiveTheme = computed(() => {
    const current = this.currentTheme();
    if (current === 'system') {
      return this.systemTheme();
    }
    return current;
  });

  public readonly nextTheme = computed(() => {
    const current = this.currentTheme();
    const availableThemes = this.showSystemOption || this.enableSystemTheme 
      ? ['light', 'dark', 'system'] as const
      : ['light', 'dark'] as const;
    
    // Type-safe indexOf with fallback for non-existent themes
    const currentIndex = (availableThemes as readonly string[]).indexOf(current);
    const safeCurrentIndex = currentIndex === -1 ? 0 : currentIndex;
    const nextIndex = (safeCurrentIndex + 1) % availableThemes.length;
    return availableThemes[nextIndex];
  });

  public readonly shouldHide = computed(() => 
    this.hideWhenSystem && this.currentTheme() === 'system'
  );

  public readonly computedClasses = computed(() => {
    const baseClasses = [
      'theme-toggle',
      'inline-flex items-center justify-center',
      'transition-all duration-200',
      'focus:outline-none focus:ring-2 focus:ring-blue-500',
      'cursor-pointer select-none'
    ];

    // Size classes
    const sizeClasses = {
      sm: ['w-8 h-8', 'text-sm'],
      md: ['w-10 h-10', 'text-base'],
      lg: ['w-12 h-12', 'text-lg']
    };

    // Variant classes
    const variantClasses = {
      default: [
        'bg-white dark:bg-gray-800',
        'border border-gray-300 dark:border-gray-600',
        'shadow-sm hover:shadow-md',
        'text-gray-700 dark:text-gray-300'
      ],
      compact: [
        'bg-gray-100 dark:bg-gray-700',
        'hover:bg-gray-200 dark:hover:bg-gray-600',
        'text-gray-600 dark:text-gray-400'
      ],
      minimal: [
        'bg-transparent',
        'hover:bg-gray-100 dark:hover:bg-gray-800',
        'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
      ],
      floating: [
        'bg-white dark:bg-gray-800',
        'border border-gray-200 dark:border-gray-700',
        'shadow-lg hover:shadow-xl',
        'text-gray-700 dark:text-gray-300',
        'backdrop-blur-sm'
      ]
    };

    // Position classes
    const positionClasses = {
      static: [],
      fixed: ['fixed', 'z-50'],
      sticky: ['sticky', 'z-40']
    };

    // Rounded classes
    const roundedClasses = {
      none: ['rounded-none'],
      sm: ['rounded-sm'],
      md: ['rounded-md'],
      lg: ['rounded-lg'],
      full: ['rounded-full']
    };

    // State classes
    const stateClasses = this.disabled ? ['opacity-50', 'pointer-events-none', 'cursor-not-allowed'] : ['hover:scale-105 active:scale-95'];
    const transitionClasses = this.isTransitioning() ? ['animate-spin'] : [];
    const hiddenClasses = this.shouldHide() ? ['opacity-0', 'pointer-events-none'] : [];

    return [
      ...baseClasses,
      ...sizeClasses[this.size],
      ...variantClasses[this.variant],
      ...positionClasses[this.position],
      ...roundedClasses[this.rounded],
      ...stateClasses,
      ...transitionClasses,
      ...hiddenClasses,
      this.className
    ].filter(Boolean).join(' ');
  });

  public readonly iconClasses = computed(() => {
    const baseClasses = ['transition-transform duration-200'];
    
    if (this.resolvedConfig().animateIcon && this.isHovered()) {
      baseClasses.push('rotate-12 scale-110');
    }
    
    return baseClasses.join(' ');
  });

  public readonly tooltipClasses = computed(() => [
    'absolute z-50 px-2 py-1',
    'text-xs font-medium text-white',
    'bg-gray-900 dark:bg-gray-700',
    'rounded shadow-lg',
    'pointer-events-none',
    'transition-opacity duration-200',
    this.tooltipPosition === 'top' ? 'bottom-full mb-2 left-1/2 transform -translate-x-1/2' : '',
    this.tooltipPosition === 'bottom' ? 'top-full mt-2 left-1/2 transform -translate-x-1/2' : '',
    this.tooltipPosition === 'left' ? 'right-full mr-2 top-1/2 transform -translate-y-1/2' : '',
    this.tooltipPosition === 'right' ? 'left-full ml-2 top-1/2 transform -translate-y-1/2' : '',
    this.isHovered() ? 'opacity-100' : 'opacity-0'
  ].filter(Boolean).join(' '));

  ngOnInit(): void {
    this.initializeTheme();
    this.setupSystemThemeDetection();
    this.loadStoredTheme();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeTheme(): void {
    this.currentTheme.set(this.defaultTheme);
  }

  private setupSystemThemeDetection(): void {
    if (!this.isBrowser || !this.resolvedConfig().enableSystemDetection) return;

    // Initial detection
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    this.updateSystemTheme(mediaQuery.matches);

    // Listen for changes
    fromEvent(mediaQuery, 'change').pipe(
      takeUntil(this.destroy$)
    ).subscribe((event: any) => {
      this.updateSystemTheme(event.matches);
    });
  }

  private updateSystemTheme(isDark: boolean): void {
    const theme = isDark ? 'dark' : 'light';
    this.systemTheme.set(theme);
    this.systemThemeDetected.emit(theme);

    // Apply theme if current is system
    if (this.currentTheme() === 'system') {
      this.applyTheme(theme);
    }
  }

  private loadStoredTheme(): void {
    if (!this.isBrowser || !this.resolvedConfig().persistPreference) return;

    const stored = localStorage.getItem(this.resolvedConfig().storageKey) as 'light' | 'dark' | 'system';
    if (stored && ['light', 'dark', 'system'].includes(stored)) {
      this.setTheme(stored, false);
    }
  }

  private storeTheme(theme: 'light' | 'dark' | 'system'): void {
    if (!this.isBrowser || !this.resolvedConfig().persistPreference) return;

    localStorage.setItem(this.resolvedConfig().storageKey, theme);
  }

  private applyTheme(themeName: 'light' | 'dark'): void {
    if (!this.isBrowser) return;

    const body = document.body;
    const html = document.documentElement;

    // Remove existing theme classes
    body.classList.remove('light', 'dark');
    html.classList.remove('light', 'dark');

    // Add new theme class
    body.classList.add(themeName);
    html.classList.add(themeName);
  }

  // Public methods
  public setTheme(theme: 'light' | 'dark' | 'system', animate: boolean = true): void {
    if (this.disabled) return;

    if (animate && this.resolvedConfig().enableTransitions) {
      this.isTransitioning.set(true);
      setTimeout(() => {
        this.isTransitioning.set(false);
      }, this.resolvedConfig().transitionDuration);
    }

    this.currentTheme.set(theme);
    this.storeTheme(theme);

    // Apply the actual theme
    const effectiveTheme = theme === 'system' ? this.systemTheme() : theme;
    this.applyTheme(effectiveTheme);

    this.themeChanged.emit(theme);
  }

  public toggle(): void {
    if (this.disabled) return;

    this.toggleClicked.emit();
    const next = this.nextTheme();
    this.setTheme(next);
  }

  public onMouseEnter(): void {
    if (!this.disabled && this.resolvedConfig().showTooltip) {
      this.isHovered.set(true);
    }
  }

  public onMouseLeave(): void {
    this.isHovered.set(false);
  }

  public getThemeIcon(themeId?: 'light' | 'dark' | 'system'): string {
    const theme = themeId || this.currentTheme();
    const icons = {
      light: 'M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z',
      dark: 'M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z',
      system: 'M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z'
    };
    return icons[theme] || icons.light;
  }

  public getTooltipText(): string {
    const current = this.currentThemeInfo();
    const next = this.themes.find(t => t.id === this.nextTheme());
    
    if (this.resolvedConfig().showLabel) {
      return `Current: ${current.name}`;
    }
    
    return `Switch to ${next?.name || 'next theme'}`;
  }

  // Security methods
  public sanitizeHtml(html: string): SafeHtml {
    return this.sanitizer.sanitize(1, html) || '';
  }

  // Getters for template
  get currentThemeName(): string {
    return this.currentThemeInfo().name;
  }

  get currentIcon(): string {
    return this.getThemeIcon();
  }

  get tooltipText(): string {
    return this.getTooltipText();
  }

  get showTooltip(): boolean {
    return this.resolvedConfig().showTooltip;
  }

  get showLabel(): boolean {
    return this.resolvedConfig().showLabel;
  }

  get isAnimating(): boolean {
    return this.isTransitioning();
  }

  get hovering(): boolean {
    return this.isHovered();
  }
}
