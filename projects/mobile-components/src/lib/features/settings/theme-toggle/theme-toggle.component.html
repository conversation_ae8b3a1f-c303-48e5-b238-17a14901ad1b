<button
  type="button"
  [class]="computedClasses()"
  (click)="toggle()"
  (mouseenter)="onMouseEnter()"
  (mouseleave)="onMouseLeave()"
  [disabled]="disabled"
  [attr.data-testid]="'theme-toggle-' + componentId()"
  [attr.aria-label]="tooltipText"
  [attr.title]="showTooltip ? '' : tooltipText">
  
  <!-- Theme Icon -->
  <div class="relative flex items-center justify-center">
    @if (!isAnimating) {
      <svg 
        [class]="iconClasses()"
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
        class="w-5 h-5"
        [class.w-4]="size === 'sm'"
        [class.h-4]="size === 'sm'"
        [class.w-6]="size === 'lg'"
        [class.h-6]="size === 'lg'"
        aria-hidden="true">
        <path 
          stroke-linecap="round" 
          stroke-linejoin="round" 
          stroke-width="2" 
          [attr.d]="currentIcon" />
      </svg>
    } @else {
      <!-- Loading/Transition Icon -->
      <div class="animate-spin">
        <svg 
          class="w-5 h-5"
          [class.w-4]="size === 'sm'"
          [class.h-4]="size === 'sm'"
          [class.w-6]="size === 'lg'"
          [class.h-6]="size === 'lg'"
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
          aria-hidden="true">
          <path 
            stroke-linecap="round" 
            stroke-linejoin="round" 
            stroke-width="2" 
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
      </div>
    }
    
    <!-- Theme Label (if enabled) -->
    @if (showLabel && variant !== 'minimal') {
      <span class="ml-2 text-sm font-medium hidden sm:inline">
        {{ currentThemeName }}
      </span>
    }
  </div>

  <!-- Tooltip -->
  @if (showTooltip && !disabled) {
    <div [class]="tooltipClasses()">
      {{ tooltipText }}
      
      <!-- Tooltip Arrow -->
      @if (tooltipPosition === 'top') {
        <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-700"></div>
      }
      @if (tooltipPosition === 'bottom') {
        <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900 dark:border-b-gray-700"></div>
      }
      @if (tooltipPosition === 'left') {
        <div class="absolute right-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-l-4 border-transparent border-l-gray-900 dark:border-l-gray-700"></div>
      }
      @if (tooltipPosition === 'right') {
        <div class="absolute left-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-r-4 border-transparent border-r-gray-900 dark:border-r-gray-700"></div>
      }
    </div>
  }

  <!-- Accessibility Announcements -->
  <span class="sr-only">
    Theme toggle button. Current theme: {{ currentThemeName }}.
    @if (currentTheme() === 'system') {
      Using {{ effectiveTheme() }} from system preference.
    }
    Click to switch theme.
  </span>

</button>
