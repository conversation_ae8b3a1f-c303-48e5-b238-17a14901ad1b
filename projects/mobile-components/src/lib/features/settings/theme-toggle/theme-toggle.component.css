
/* Base Component Styles */
.theme-toggle {
  position: relative;
  font-family: inherit;
  border: none;
  outline: none;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover Effects */
.theme-toggle:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dark .theme-toggle:hover:not(:disabled) {
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
}

/* Active State */
.theme-toggle:active:not(:disabled) {
  transform: scale(0.95);
  transition-duration: 0.1s;
}

/* Focus State */
.theme-toggle:focus-visible {
  outline: 2px solid theme('colors.blue.500');
  outline-offset: 2px;
}

/* Disabled State */
.theme-toggle:disabled {
  cursor: not-allowed;
  opacity: 0.5;
  transform: none !important;
  box-shadow: none !important;
}

/* Icon Animation */
.theme-toggle svg {
  transition: all 0.2s ease-in-out;
}

.theme-toggle:hover:not(:disabled) svg {
  color: theme('colors.blue.500');
}

.dark .theme-toggle:hover:not(:disabled) svg {
  color: theme('colors.blue.400');
}

/* Variant Specific Styles */
.theme-toggle.variant-default {
  backdrop-filter: blur(8px);
}

.theme-toggle.variant-floating {
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .theme-toggle.variant-floating {
  border-color: rgba(255, 255, 255, 0.05);
}

.theme-toggle.variant-minimal:hover:not(:disabled) {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark .theme-toggle.variant-minimal:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.05);
}

.theme-toggle.variant-compact {
  border-radius: theme('borderRadius.md');
}

/* Size Specific Styles */
.theme-toggle.size-sm {
  padding: 0.25rem;
}

.theme-toggle.size-md {
  padding: 0.5rem;
}

.theme-toggle.size-lg {
  padding: 0.75rem;
}

/* Position Specific Styles */
.theme-toggle.position-fixed {
  top: 1rem;
  right: 1rem;
}

.theme-toggle.position-sticky {
  top: 0;
}

/* Theme Transition Effects */
.theme-toggle.transitioning {
  pointer-events: none;
}

.theme-toggle.transitioning svg {
  animation: theme-transition 0.2s ease-in-out;
}

@keyframes theme-transition {
  0% {
    transform: rotate(0deg) scale(1);
    opacity: 1;
  }
  50% {
    transform: rotate(180deg) scale(0.8);
    opacity: 0.5;
  }
  100% {
    transform: rotate(360deg) scale(1);
    opacity: 1;
  }
}

/* Icon Rotation Animation */
.theme-toggle svg.rotate-12 {
  transform: rotate(12deg) scale(1.1);
}

/* Tooltip Styles */
.theme-toggle [class*="tooltip"] {
  white-space: nowrap;
  font-weight: 500;
  letter-spacing: 0.025em;
}

/* Tooltip Animations */
.theme-toggle [class*="tooltip"].opacity-100 {
  animation: tooltip-fade-in 0.2s ease-out;
}

.theme-toggle [class*="tooltip"].opacity-0 {
  animation: tooltip-fade-out 0.2s ease-in;
}

@keyframes tooltip-fade-in {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes tooltip-fade-out {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(5px);
  }
}

/* Pulse Animation for Loading */
.theme-toggle .animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Glow Effect for Dark Mode */
.dark .theme-toggle {
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05);
}

.dark .theme-toggle:hover:not(:disabled) {
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.1),
    0 4px 12px rgba(255, 255, 255, 0.1),
    0 0 20px rgba(59, 130, 246, 0.2);
}

/* Label Animation */
.theme-toggle span {
  transition: all 0.2s ease-in-out;
}

.theme-toggle:hover:not(:disabled) span {
  color: theme('colors.blue.600');
}

.dark .theme-toggle:hover:not(:disabled) span {
  color: theme('colors.blue.400');
}

/* Responsive Design */
@media (max-width: 768px) {
  .theme-toggle {
    min-width: 44px;
    min-height: 44px;
  }
  
  .theme-toggle span {
    display: none !important;
  }
  
  .theme-toggle.position-fixed {
    top: 0.5rem;
    right: 0.5rem;
  }
}

@media (max-width: 480px) {
  .theme-toggle.size-lg {
    width: 40px;
    height: 40px;
    padding: 0.5rem;
  }
  
  .theme-toggle.size-md {
    width: 36px;
    height: 36px;
    padding: 0.4rem;
  }
  
  .theme-toggle.size-sm {
    width: 32px;
    height: 32px;
    padding: 0.3rem;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .theme-toggle {
    border: 2px solid currentColor;
  }
  
  .theme-toggle:hover:not(:disabled) {
    border-width: 3px;
  }
  
  .theme-toggle svg {
    stroke-width: 2.5;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .theme-toggle,
  .theme-toggle svg,
  .theme-toggle [class*="tooltip"] {
    transition: none !important;
    animation: none !important;
  }
  
  .theme-toggle:hover:not(:disabled) {
    transform: none;
  }
  
  .theme-toggle.transitioning svg {
    animation: none !important;
  }
}

/* Print Styles */
@media print {
  .theme-toggle {
    display: none !important;
  }
}

/* Performance Optimizations */
.theme-toggle {
  contain: layout style paint;
  will-change: transform, box-shadow;
}

.theme-toggle svg {
  contain: layout style paint;
  will-change: transform, color;
}

/* Custom Scrollbar (if needed for tooltip) */
.theme-toggle [class*="tooltip"]::-webkit-scrollbar {
  display: none;
}

/* Theme-specific Icon Colors */
.theme-toggle[data-theme="light"] svg {
  color: theme('colors.yellow.500');
}

.theme-toggle[data-theme="dark"] svg {
  color: theme('colors.blue.400');
}

.theme-toggle[data-theme="system"] svg {
  color: theme('colors.gray.500');
}

/* Gradient Background for Floating Variant */
.theme-toggle.variant-floating {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.9) 0%, 
    rgba(255, 255, 255, 0.7) 100%);
}

.dark .theme-toggle.variant-floating {
  background: linear-gradient(135deg, 
    rgba(0, 0, 0, 0.9) 0%, 
    rgba(0, 0, 0, 0.7) 100%);
}

/* Accessibility Enhancement */
.theme-toggle:focus-visible {
  animation: focus-pulse 0.3s ease-in-out;
}

@keyframes focus-pulse {
  0%, 100% {
    outline-offset: 2px;
  }
  50% {
    outline-offset: 4px;
  }
}

/* Theme Switch Success Animation */
.theme-toggle.theme-switched {
  animation: theme-success 0.4s ease-out;
}

@keyframes theme-success {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.3);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

/* Hidden State */
.theme-toggle.opacity-0 {
  visibility: hidden;
  pointer-events: none;
}

/* Loading State Enhancement */
.theme-toggle .animate-spin svg {
  color: theme('colors.blue.500');
}

.dark .theme-toggle .animate-spin svg {
  color: theme('colors.blue.400');
}