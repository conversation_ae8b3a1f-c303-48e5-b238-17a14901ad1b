import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import Settings components
// Standalone (1)
import { SettingsComponent } from './settings/settings.component';

// Non-Standalone (2)
import { ThemeSwitchComponent } from './theme-switch/theme-switch.component';
import { ThemeToggleComponent } from './theme-toggle/theme-toggle.component';


@NgModule({
  declarations: [
    // Non-Standalone Components (2)
   
  ],
  imports: [
    CommonModule,
    IonicModule,
    // Standalone Components (1)
    SettingsComponent,
    ThemeSwitchComponent,
    ThemeToggleComponent,
  ],
  exports: [
    // Non-Standalone Components (2)
    ThemeSwitchComponent,
    ThemeToggleComponent,
    // Standalone Components (1)
    SettingsComponent,
  ]
})
export class SettingsModule { }
