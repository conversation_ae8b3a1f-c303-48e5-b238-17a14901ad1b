import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Observable, of } from 'rxjs';
// TODO: Fix missing dependencies - ChatService and Message interface need to be moved/created in mobile-components/shared
// import { ChatService } from '../../services/chat.service'; // Import the actual service
// Remove the local Message interface
// import { Message } from '../../models/message.interface'; // Add this import

// TEMP: Placeholder Message interface until dependency is fixed
interface Message { id: number; text: string; sender: string; } 

@Component({
  selector: 'app-chat',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './chat.component.html',
  styleUrl: './chat.component.css',
})
export class ChatComponent implements OnInit {
  messages$: Observable<Message[]> = of([]); // TEMP: Use placeholder
  newMessage: string = '';

  // constructor(private chatService: ChatService) {}

  ngOnInit() {
    // this.messages$ = this.chatService.getMessages();
  }

  sendMessage() {
    if (this.newMessage.trim()) {
      console.log('TEMP: Sending message:', this.newMessage.trim()); // TEMP: Log instead of sending
      // this.chatService.sendMessage(this.newMessage.trim());
      this.newMessage = '';
    }
  }
}
