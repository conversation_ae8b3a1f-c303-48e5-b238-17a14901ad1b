<div class="chat-container">
  <!-- <div class="messages">
    <div
      *ngFor="let msg of messages$ | async"
      [ngClass]="{ 'user-message': msg.isUser, 'ai-message': !msg.isUser }"
    >
      {{ msg.content }}
    </div>
  </div>
  <div class="input-area">
    <input [(ngModel)]="newMessage" placeholder="Type your message..." />
    <button (click)="sendMessage()">Send</button>
  </div> -->
</div>
