import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import Chat components
// Standalone (1)
import { ChatComponent } from './chat/chat.component';

// Non-Standalone (0)
// None

@NgModule({
  declarations: [
    // Non-Standalone Components (0)
    // None
  ],
  imports: [
    CommonModule,
    IonicModule,
    // Standalone Components (1)
    ChatComponent,
  ],
  exports: [
    // Standalone Components (1)
    ChatComponent,
  ]
})
export class ChatModule { }
