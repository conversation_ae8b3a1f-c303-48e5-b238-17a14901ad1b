import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import Leagues components
// Standalone (1)
import { LeagueListCompactComponent } from './league-list-compact/league-list-compact.component';

// Non-Standalone (0)
// None

@NgModule({
  declarations: [
    // Non-Standalone Components (0)
    // None
  ],
  imports: [
    CommonModule,
    IonicModule,
    // Standalone Components (1)
    LeagueListCompactComponent,
  ],
  exports: [
    // Standalone Components (1)
    LeagueListCompactComponent,
  ]
})
export class LeaguesModule { }
