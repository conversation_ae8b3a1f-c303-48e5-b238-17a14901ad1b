import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import Search components
// Standalone (2)
import { AppSearchComponent } from './app-search/app-search.component';
import { AppSearchResultComponent } from './app-search-result/app-search-result.component';

// Non-Standalone (0)
// None

@NgModule({
  declarations: [
    // Non-Standalone Components (0)
    // None
  ],
  imports: [
    CommonModule,
    IonicModule,
    // Standalone Components (2)
    AppSearchComponent,
    AppSearchResultComponent,
  ],
  exports: [
    // Standalone Components (2)
    AppSearchComponent,
    AppSearchResultComponent,
  ]
})
export class SearchModule { }
