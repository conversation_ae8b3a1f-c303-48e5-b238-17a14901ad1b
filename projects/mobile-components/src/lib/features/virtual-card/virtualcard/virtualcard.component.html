<ion-content>
    <div class="absolute inset-0 z-0 h-screen bg-blue-200 animated-bg"></div>
    <div class="overflow-y-auto absolute inset-0 z-10 pb-24 space-y-2">
    <lib-head-logo [balance]="profile?.currentBalance" [src]="lssConfig.icon" />
    <div class=" ion-padding">
        <img src="{{ profile?.virtualCard }}" alt="">
      </div>
    
   
    

    <ion-list [inset]="false" class="ion-padding transaction-history">
        <!-- <ion-card class="card clay">
            <ion-card-content>
                <ion-grid>
                    <ion-row class="ion-align-items-center">
                        <ion-col size="2" style="text-align: start;">
                            <ion-icon name="airplane-outline"></ion-icon>
                        </ion-col>
                        <ion-col size="8" style="text-align: center;">
                            <ion-label>
                                <h2>Flight</h2>
                            </ion-label>
                        </ion-col>
                        <ion-col size="2" style="text-align: end;">
                            <h3 class="pcu-earned">800</h3>
                        </ion-col>
                    </ion-row>
                    <ion-row colspan="12" class="ion-align-items-center">
                        <ion-col style="text-align: center;">
                            <p>JHB - CPT | Economy | April 20, 2022</p>
                        </ion-col>
                    </ion-row>
                </ion-grid>
            </ion-card-content>
        </ion-card> -->
 
        <!-- <ion-card class="card clay">
            <ion-card-content>
                <ion-grid>
                    <ion-row class="ion-align-items-center">
                        <ion-col size="2" style="text-align: start;">
                            <ion-icon name="airplane-outline"></ion-icon>
                        </ion-col>
                        <ion-col size="8" style="text-align: center;">
                            <ion-label>
                                <h2>Redemption</h2>
                            </ion-label>
                        </ion-col>
                        <ion-col size="2" style="text-align: end;">
                            <h3 class="pcu-spent">800</h3>
                        </ion-col>
                    </ion-row>
                    <ion-row colspan="12" class="ion-align-items-center">
                        <ion-col style="text-align: center;">
                            <p>JHB - CPT | Economy | April 20, 2022</p>
                        </ion-col>
                    </ion-row>
                </ion-grid>
            </ion-card-content>
        </ion-card> -->
    </ion-list>
  </div>
</ion-content>