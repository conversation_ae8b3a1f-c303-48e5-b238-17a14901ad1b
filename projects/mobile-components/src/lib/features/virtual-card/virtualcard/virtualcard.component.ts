import { Component, Injector, OnInit, CUSTOM_ELEMENTS_SCHEMA, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { HeadLogoComponent } from '../../../layout/head-logo/head-logo.component';
import {
  MemberProfile,
  MemberService,
  KeyCloakService,
  LssConfig,
} from 'lp-client-api';
import { AbstractComponent } from '../../../shared/abstract.component';

@Component({
  selector: 'app-virtualcard',
  templateUrl: 'virtualcard.component.html',
  styleUrls: ['virtualcard.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    IonicModule,
    HeadLogoComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class VirtualCardComponent extends AbstractComponent {
  profile?: MemberProfile;

  @Input() environment?: string;
  constructor(
    injector: Injector,
    private memberService: MemberService,
    private kc: KeyCloakService,
    public lssConfig: LssConfig
  ) {
    super(injector);
  }

  ngOnInit() {
    this.addGlobalSubscription(
      this.memberService.profileSubject.subscribe((data) => {
        this.profile = data;
        this.loading = false;
        this.detectChanges();
      })
    );
  }
}
