import { Component, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AbstractComponent } from '../../../shared/abstract.component';

interface Card {
  id: number;
  image: string;
}

@Component({
  selector: 'app-virtual',
  templateUrl: './virtual.component.html',
  styleUrls: ['./virtual.component.scss'],
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class VirtualComponent extends AbstractComponent {
  cards: Card[] = [
    { id: 1, image: 'assets/images/virtualcard.jpg' },
    { id: 2, image: 'assets/images/virtualcard.jpg' },
    { id: 3, image: 'assets/images/virtualcard.jpg' },
  ];

  selectedCard: Card | null = null;

  selectCard(card: Card): void {
    this.selectedCard = card;
  }

  closeCard(): void {
    this.selectedCard = null;
  }

  addCard(): void {
    const newId = this.cards.length + 1;
    this.cards.push({ id: newId, image: 'assets/images/virtual/card.png' });
  }
}
