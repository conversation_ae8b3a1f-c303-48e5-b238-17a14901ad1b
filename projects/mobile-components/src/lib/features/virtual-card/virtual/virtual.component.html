<div
  class="
    p-6
    min-h-screen
    bg-gradient-to-br
    from-green-400
    to-green-600
    animated-bg
  "
>
  <h1 class="mb-8 text-4xl font-bold text-green-500">Virtual Wallet</h1>

  <div class="relative mb-8 h-80">
    <ng-container *ngFor="let card of cards; let i = index">
      <div
        [ngClass]="{
          'absolute w-64 h-40 rounded-xl shadow-lg transition-all duration-300 cursor-pointer transform hover:scale-105': true,
          'bg-gradient-to-r': true,
          'from-red-400 to-red-600': i % 3 === 0,
          'from-green-400 to-green-600': i % 3 === 1,
          'from-blue-400 to-blue-600': i % 3 === 2
        }"
        [style.top.px]="i * 20"
        [style.left.px]="i * 10"
        (click)="selectCard(card)"
      >
        <img [src]="card.image" alt="Virtual Card" />
      </div>
    </ng-container>
  </div>

  <!-- Add Card Button -->
  <button
    class="
      px-6
      py-2
      font-semibold
      text-purple-600
      bg-white
      rounded-full
      shadow-lg
      transition-colors
      duration-300
      hover:bg-purple-100
    "
    (click)="addCard()"
  >
    Add New Card
  </button>

  <!-- Full Screen Card Modal -->
  <div
    *ngIf="selectedCard"
    class="
      flex
      fixed
      inset-0
      z-50
      justify-center
      items-center
      bg-black bg-opacity-50
    "
    (click)="closeCard()"
  >
    <div
      class="
        w-full
        bg-gradient-to-br
        from-blue-200
        to-indigo-200
        rounded-xl
        shadow-2xl
        transition-all
        duration-300
        transform
        scale-110
      "
      (click)="$event.stopPropagation()"
    >
      <div class="flex justify-center items-center p-1 text-white rounded-lg">
        <img [src]="selectedCard.image || ''" alt="Virtual Card" class="w-2/3" />
      </div>
    </div>
  </div>
</div>
