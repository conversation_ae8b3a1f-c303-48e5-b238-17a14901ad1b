import { Component, Injector, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Router } from '@angular/router';
import { AbstractComponent } from '../../../../shared/abstract.component';

import {
  KeyCloakService,
  MemberProfile,
  MemberService,
  LssConfig,
} from 'lp-client-api';
import { ChangeDetectorRef } from '@angular/core';

@Component({
  selector: 'app-categories',
  templateUrl: './categories.component.html',
  styleUrls: ['./categories.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class CategoriesComponent extends AbstractComponent {
  constructor(
    injector: Injector,
    public kc: KeyCloakService,
    private memberService: MemberService,
    protected readonly router: Router,
    private cd: ChangeDetectorRef,
    public lssConfig: LssConfig
  ) {
    super(injector);
  }
}
