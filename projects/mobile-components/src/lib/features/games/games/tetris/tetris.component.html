<div class="game-container">
  <div class="game-header">
    <div class="score-panel">
      <div class="score-item">
        <h3>Score</h3>
        <div class="score">{{score}}</div>
      </div>
      <div class="score-item">
        <h3>Level</h3>
        <div class="level">{{level}}</div>
      </div>
      <div class="score-item">
        <h3>Lines</h3>
        <div class="lines">{{linesCleared}}</div>
      </div>
    </div>
  </div>

  <div class="game-area">
    <div class="side-panel hold-panel">
      <h3>Hold</h3>
      <div class="hold-piece-grid">
        <div *ngFor="let row of heldPieceGrid; let i = index">
          <div *ngFor="let cell of row; let j = index"
               class="hold-piece-cell"
               [ngClass]="getPieceClass(cell, heldPieceType)">
          </div>
        </div>
      </div>
    </div>

    <div class="game-grid">
      <div *ngIf="!gameStarted" class="start-overlay">
        <button class="start-button" (click)="startGame()">
          <ion-icon name="play"></ion-icon>
          Start Game
        </button>
      </div>
      <div *ngFor="let row of grid; let i = index" class="grid-row">
        <div *ngFor="let cell of row; let j = index"
             class="grid-cell"
             [class.cell-empty]="cell === EMPTY"
             [class.cell-filled]="cell === FILLED && !isActivePieceCell(i, j)"
             [class.active-piece]="isActivePieceCell(i, j)"
             [class.ghost-piece]="isGhostPieceCell(i, j)"
             [ngClass]="isActivePieceCell(i, j) ? getPieceClass(FILLED, currentPieceType) : {}">
        </div>
      </div>

      <div *ngIf="gameOver" class="game-over">
        <h2>Game Over!</h2>
        <div class="final-score">Final Score: {{score}}</div>
        <button class="restart-button" (click)="resetGame()">
          <ion-icon name="refresh"></ion-icon>
          Play Again
        </button>
      </div>
    </div>

    <div class="side-panel next-panel">
      <h3>Next</h3>
      <div class="next-pieces">
        <div *ngFor="let row of nextPieceGrid; let i = index">
          <div *ngFor="let cell of row; let j = index"
               class="hold-piece-cell"
               [ngClass]="getPieceClass(cell, nextPieceType)">
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="game-controls">
    <button class="control-button"
            (click)="moveLeft()"
            aria-label="Move Left">
      <ion-icon name="arrow-back"></ion-icon>
    </button>
    <button class="control-button"
            (click)="rotatePiece(true)"
            aria-label="Rotate">
      <ion-icon name="refresh"></ion-icon>
    </button>
    <button class="control-button"
            (click)="moveRight()"
            aria-label="Move Right">
      <ion-icon name="arrow-forward"></ion-icon>
    </button>
    <button class="control-button"
            (click)="moveDown()"
            aria-label="Soft Drop">
      <ion-icon name="arrow-down"></ion-icon>
    </button>
    <button class="control-button"
            (click)="hardDrop()"
            aria-label="Hard Drop">
      <ion-icon name="arrow-down-circle"></ion-icon>
    </button>
    <button class="control-button"
            (click)="holdCurrentPiece()"
            aria-label="Hold Piece">
      <ion-icon name="swap-horizontal"></ion-icon>
    </button>
  </div>

  <div *ngIf="showLevelUp" class="level-up-animation">
    <div class="level-up-text">
      Level {{level}}!
    </div>
  </div>

  <div *ngIf="showCombo" class="combo-animation">
    <div class="combo-text">
      {{combo}}x Combo!
    </div>
  </div>

  <div *ngIf="showTetris" class="tetris-animation">
    <div class="tetris-text">
      TETRIS!
    </div>
  </div>
</div>
