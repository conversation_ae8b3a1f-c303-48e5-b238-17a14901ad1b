:host {
  @apply block w-full h-full;
}

.game-container {
  @apply flex flex-col items-center justify-start w-full h-full p-2;
  min-height: calc(100vh - 180px); /* Account for header and bottom nav */
}

.game-area {
  @apply flex flex-col items-center gap-4 w-full;
  max-width: 100vw;
}

.grid-container {
  @apply p-3 rounded-xl bg-orange-500/20 shadow-lg;
  width: fit-content;
  max-width: 95vw;
}

.tetris-grid {
  display: grid;
  grid-template-columns: repeat(10, minmax(20px, 25px));
  @apply bg-orange-950/30 p-2 rounded-lg;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.cell {
  width: 20px;
  height: 20px;
  @apply rounded-sm transition-colors duration-100;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.side-panels {
  @apply flex flex-row justify-between items-center w-full gap-2 px-2;
}

.side-panel {
  @apply flex-1 p-2 rounded-xl bg-orange-500/20;
  min-width: 80px;
  max-width: 120px;
}

.hold-piece {
  @apply p-2 rounded-lg bg-orange-950/30;
}

.hold-piece-grid {
  display: grid;
  grid-template-columns: repeat(4, 15px);
  gap: 1px;
  @apply mx-auto;
}

.hold-piece-cell {
  width: 15px;
  height: 15px;
  @apply rounded-sm;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Piece Colors */
.cell-empty {
  @apply bg-orange-950/50;
}

.piece-I {
  @apply bg-cyan-500 border-cyan-400;
  box-shadow: 0 0 5px rgba(34, 211, 238, 0.3);
}

.piece-O {
  @apply bg-yellow-500 border-yellow-400;
  box-shadow: 0 0 5px rgba(234, 179, 8, 0.3);
}

.piece-T {
  @apply bg-purple-500 border-purple-400;
  box-shadow: 0 0 5px rgba(168, 85, 247, 0.3);
}

.piece-L {
  @apply bg-orange-500 border-orange-400;
  box-shadow: 0 0 5px rgba(249, 115, 22, 0.3);
}

.piece-J {
  @apply bg-blue-500 border-blue-400;
  box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
}

.piece-S {
  @apply bg-green-500 border-green-400;
  box-shadow: 0 0 5px rgba(34, 197, 94, 0.3);
}

.piece-Z {
  @apply bg-red-500 border-red-400;
  box-shadow: 0 0 5px rgba(239, 68, 68, 0.3);
}

.start-button {
  @apply px-6 py-3 text-lg font-semibold text-white
         bg-orange-500 hover:bg-orange-600
         rounded-lg shadow-lg
         transition-all duration-200
         transform hover:scale-105 active:scale-95;
  margin: 1rem auto;
}

.controls {
  @apply w-full max-w-md mt-2 transform-gpu scale-90;
}

/* Tablet and larger screens */
@media (min-width: 768px) {
  .game-container {
    @apply p-4;
  }

  .game-area {
    @apply flex-row justify-center gap-8;
  }

  .grid-container {
    @apply p-6;
  }

  .tetris-grid {
    grid-template-columns: repeat(10, 25px);
    @apply p-3;
  }

  .cell {
    width: 25px;
    height: 25px;
  }

  .side-panels {
    @apply flex-col h-full;
    width: auto;
  }

  .side-panel {
    @apply p-4;
    max-width: none;
  }

  .hold-piece-grid {
    grid-template-columns: repeat(4, 20px);
  }

  .hold-piece-cell {
    width: 20px;
    height: 20px;
  }

  .controls {
    @apply scale-100 mt-4;
  }
}

/* Animations */
@keyframes drop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.piece-drop {
  animation: drop 0.3s ease-out;
}
