import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ementRef,
  Inject,
  PLATFORM_ID,
  HostListener,
  NgZone,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA
} from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, isPlatformBrowser } from '@angular/common';
import { Subject, firstValueFrom } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { GameService } from 'lp-client-api';
import {
  GameProgress,
  TetrisConfig,
  GameParticipation,
  GameConfigs,
  Game,
  BaseGameProgress,
} from 'lp-client-api';

// Piece types and rotation states
type PieceType = 'I' | 'O' | 'T' | 'L' | 'J' | 'S' | 'Z';
type RotationState = 0 | 1 | 2 | 3;
const PIECE_TYPES: PieceType[] = ['I', 'O', 'T', 'L', 'J', 'S', 'Z'];

// Game specific interfaces
interface TetrisProgress extends BaseGameProgress {
  id: number;
  score: number;
  level: number;
  state: string;
  payload: any;
  gameId: number;
  userId: string;
  lastPlayed: string;
  attemptsRemaining: number;
  currentLevel: number;
  highScore: number;
  gameSpecificProgress: {
    linesCleared: number;
    levelReached: number;
    tetrisCount: number;
    pieceStats: { [K in PieceType]: number };
    state: string;
  };
}

interface TetrisControl {
  name: string;
  label: string;
  icon: string;
}

// SRS Wall Kick Data
const WALL_KICK_DATA = {
  JLSTZ: [
    [
      [0, 0],
      [-1, 0],
      [-1, 1],
      [0, -2],
      [-1, -2],
    ], // 0->1
    [
      [0, 0],
      [1, 0],
      [1, -1],
      [0, 2],
      [1, 2],
    ], // 1->2
    [
      [0, 0],
      [1, 0],
      [1, 1],
      [0, -2],
      [1, -2],
    ], // 2->3
    [
      [0, 0],
      [-1, 0],
      [-1, -1],
      [0, 2],
      [-1, 2],
    ], // 3->0
  ],
  I: [
    [
      [0, 0],
      [-2, 0],
      [1, 0],
      [-2, -1],
      [1, 2],
    ], // 0->1
    [
      [0, 0],
      [-1, 0],
      [2, 0],
      [-1, 2],
      [2, -1],
    ], // 1->2
    [
      [0, 0],
      [2, 0],
      [-1, 0],
      [2, 1],
      [-1, -2],
    ], // 2->3
    [
      [0, 0],
      [1, 0],
      [-2, 0],
      [1, -2],
      [-2, 1],
    ], // 3->0
  ],
};

// Scoring system
const SCORING = {
  SOFT_DROP: 1,
  HARD_DROP: 2,
  SINGLE: 100,
  DOUBLE: 300,
  TRIPLE: 500,
  TETRIS: 800,
  MINI_TSPIN: 100,
  TSPIN: 400,
  MINI_TSPIN_SINGLE: 200,
  TSPIN_SINGLE: 800,
  MINI_TSPIN_DOUBLE: 400,
  TSPIN_DOUBLE: 1200,
  TSPIN_TRIPLE: 1600,
  BACK_TO_BACK_MULTIPLIER: 1.5,
  COMBO_MULTIPLIER: 50,
};

@Component({
  selector: 'lib-tetris',
  templateUrl: './tetris.component.html',
  styleUrls: ['./tetris.component.scss'],
  standalone: true,
  imports: [NgIf, NgClass, NgFor, IonicModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class TetrisComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Game state and progress tracking
  gameId = 392; // Tetris game ID
  gameProgress: GameProgress | null = null;
  instance: NonNullable<GameParticipation['gameInstance']>[number] | null =
    null;
  configId: number = 0;

  // Configuration
  config: TetrisConfig = {
    boardSize: { width: 10, height: 20 },
    difficulty: 'EASY',
    frequency: 'DAILY',
    frequencyAttempts: 3,
    ghostPiece: true,
    showNext: 1,
    startLevel: 1,
    maxLevel: 10,
    holdPiece: true,
    id: 0, // This will be set during initialization
  };

  // Game state
  public score: number = 0;
  public level: number = 1;
  public linesCleared: number = 0;
  public combo: number = 0;
  public gameStarted: boolean = false;
  public gameOver: boolean = false;
  public canHold: boolean = true;
  public canPlay = true;
  public attemptsRemaining = 3;
  public isInitializing = false;
  public dropInterval = 1000; // milliseconds
  public tetrisCount = 0;
  public gameResult: 'win' | 'lose' | null = null;
  private startTime = 0;
  private dropTimer: number | undefined;

  // Piece statistics with proper typing
  pieceStats: { [K in PieceType]: number } = {
    I: 0,
    O: 0,
    T: 0,
    L: 0,
    J: 0,
    S: 0,
    Z: 0,
  };

  // Grid constants and state
  readonly EMPTY = 0;
  readonly FILLED = 1;
  grid: number[][] = [];

  // Current piece properties
  currentPiece: number[][] = [];
  currentPosition = { x: 0, y: 0 };
  currentPieceType: PieceType = 'I';

  // Hold piece properties
  heldPieceGrid: number[][] | null = null;
  heldPieceType: PieceType | null = null;

  // Next piece properties
  nextPieceGrid: number[][] | null = null;
  nextPieceType: PieceType | null = null;
  nextPieces: PieceType[] = [];

  // Ghost piece position
  ghostPosition: { x: number; y: number } | null = null;

  // Piece type tracking
  private gridPieceTypes: Map<string, PieceType> = new Map();

  // Difficulty settings
  readonly difficultySettings = {
    EASY: {
      startLevel: 1,
      dropInterval: 1000,
      maxLevel: 10,
      pointMultiplier: 1,
    },
    MEDIUM: {
      startLevel: 5,
      dropInterval: 750,
      maxLevel: 15,
      pointMultiplier: 1.5,
    },
    HARD: {
      startLevel: 10,
      dropInterval: 500,
      maxLevel: 20,
      pointMultiplier: 2,
    },
  };

  // Piece definitions
  readonly PIECES: { [key in PieceType]: number[][] } = {
    I: [[1, 1, 1, 1]],
    O: [
      [1, 1],
      [1, 1],
    ],
    T: [
      [0, 1, 0],
      [1, 1, 1],
    ],
    L: [
      [1, 0],
      [1, 0],
      [1, 1],
    ],
    J: [
      [0, 1],
      [0, 1],
      [1, 1],
    ],
    S: [
      [0, 1, 1],
      [1, 1, 0],
    ],
    Z: [
      [1, 1, 0],
      [0, 1, 1],
    ],
  };

  readonly pieceTypes = PIECE_TYPES;

  // Game controls
  gameControls: TetrisControl[] = [
    { name: 'left', label: '', icon: 'chevron-back-circle-outline' },
    { name: 'right', label: '', icon: 'chevron-forward-circle-outline' },
    { name: 'rotate', label: '', icon: 'refresh-circle-outline' },
    { name: 'drop', label: '', icon: 'arrow-down-circle-outline' },
    { name: 'hold', label: '', icon: 'swap-horizontal-outline' },
    { name: 'reset', label: 'Reset', icon: 'refresh-outline' },
  ];

  // New properties for enhanced mechanics
  private rotationState: RotationState = 0;
  private backToBack: boolean = false;
  private lastMoveWasRotation: boolean = false;
  private touchStartX: number = 0;
  private touchStartY: number = 0;
  private lastTouchTime: number = 0;
  private readonly SWIPE_THRESHOLD = 30;
  private readonly DOUBLE_TAP_DELAY = 300;

  // Add to component properties
  public showLevelUp: boolean = false;
  public showCombo: boolean = false;
  public showTetris: boolean = false;

  constructor(
    private gameService: GameService,
    private cdr: ChangeDetectorRef,
    private el: ElementRef,
    private ngZone: NgZone,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  ngOnInit(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.setupKeyboardControls();
      this.initializeEmptyGrid();
      this.loadGameConfig();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.stopTimer();
  }

  private initializeEmptyGrid(): void {
    const height = this.config.boardSize.height;
    const width = this.config.boardSize.width;
    this.grid = Array(height)
      .fill(null)
      .map(() => Array(width).fill(this.EMPTY));
    // Run change detection only if we're not in the initialization phase
    if (!this.isInitializing) {
      this.cdr.detectChanges();
    }
  }

  // Game initialization and control methods
  public startGame(): void {
    if (this.gameStarted) {
      return;
    }

    console.log('Starting game...');

    try {
      this.initializeGame();
      this.gameStarted = true;
      this.startTime = Date.now();
      this.startTimer();
      this.cdr.detectChanges();
    } catch (error) {
      console.error('Error starting game:', error);
      this.gameStarted = false;
      this.cdr.detectChanges();
    }
  }

  public stopGame(): void {
    this.gameStarted = false;
    this.gameOver = true;
    this.stopTimer();
  }

  private initializeGame(): void {
    this.isInitializing = true;
    this.resetGameState();
    this.initializeEmptyGrid();
    this.initializeNextPieces();
    this.spawnNewPiece();
    this.applyDifficultySettings(this.config.difficulty);
    this.isInitializing = false;
    this.cdr.detectChanges();

    // Log game start event
    this.logGameEvent('START', {
      difficulty: this.config.difficulty,
      startLevel: this.level,
    });
  }

  private resetGameState(): void {
    this.score = 0;
    this.level = this.config.startLevel;
    this.linesCleared = 0;
    this.gameOver = false;
    this.tetrisCount = 0;
    this.pieceStats = {
      I: 0,
      O: 0,
      T: 0,
      L: 0,
      J: 0,
      S: 0,
      Z: 0,
    };
    this.dropInterval = 1000;
    this.currentPiece = [];
    this.currentPosition = { x: 0, y: 0 };
    this.heldPieceGrid = null;
    this.heldPieceType = null;
    this.ghostPosition = null;
    this.gridPieceTypes.clear();
  }

  // Timer control methods
  private startTimer(): void {
    if (this.dropTimer) {
      clearInterval(this.dropTimer);
    }

    this.dropTimer = window.setInterval(() => {
      this.moveDown();
      this.cdr.detectChanges();
    }, this.dropInterval);
  }

  private stopTimer(): void {
    if (this.dropTimer) {
      clearInterval(this.dropTimer);
      this.dropTimer = undefined;
    }
  }

  // Game mechanics methods
  public moveLeft(): void {
    if (this.canMove(-1, 0)) {
      this.currentPosition.x--;
      this.updateGhostPiece();
    }
  }

  public moveRight(): void {
    if (this.canMove(1, 0)) {
      this.currentPosition.x++;
      this.updateGhostPiece();
    }
  }

  public moveDown(): void {
    if (this.canMove(0, 1)) {
      this.currentPosition.y++;
      this.updateGhostPiece();
    } else {
      this.lockPiece();
    }
  }

  public hardDrop(): void {
    while (this.canMove(0, 1)) {
      this.currentPosition.y++;
    }
    this.lockPiece();
  }

  public holdCurrentPiece(): void {
    if (!this.config?.holdPiece || this.gameOver || !this.canHold) return;

    const tempPiece = this.currentPiece;
    const tempType = this.currentPieceType;

    if (this.heldPieceGrid === null) {
      this.heldPieceGrid = tempPiece;
      this.heldPieceType = tempType;
      this.spawnNewPiece();
    } else {
      this.currentPiece = this.heldPieceGrid;
      this.currentPieceType = this.heldPieceType!;
      this.heldPieceGrid = tempPiece;
      this.heldPieceType = tempType;

      this.currentPosition = {
        x: Math.floor((this.grid[0].length - this.currentPiece[0].length) / 2),
        y: 0,
      };
    }

    this.canHold = false;
    this.updateGhostPiece();
  }

  private checkCollision(
    piece: number[][] = this.currentPiece,
    pos: { x: number; y: number } = this.currentPosition
  ): boolean {
    for (let y = 0; y < piece.length; y++) {
      for (let x = 0; x < piece[y].length; x++) {
        if (piece[y][x]) {
          const newY = pos.y + y;
          const newX = pos.x + x;

          if (
            newY < 0 ||
            newY >= this.grid.length ||
            newX < 0 ||
            newX >= this.grid[0].length ||
            this.grid[newY][newX] === this.FILLED
          ) {
            return true;
          }
        }
      }
    }
    return false;
  }

  private lockPiece(): void {
    if (!this.currentPiece || !this.currentPieceType) return;

    for (let y = 0; y < this.currentPiece.length; y++) {
      for (let x = 0; x < this.currentPiece[y].length; x++) {
        if (this.currentPiece[y][x]) {
          const gridY = this.currentPosition.y + y;
          const gridX = this.currentPosition.x + x;
          this.grid[gridY][gridX] = this.FILLED;
          this.gridPieceTypes.set(`${gridY}-${gridX}`, this.currentPieceType);
        }
      }
    }

    // Update piece statistics
    this.pieceStats[this.currentPieceType]++;

    this.checkLines();
    this.spawnNewPiece();

    // Update progress when piece is locked
    this.updateGameProgress();
  }

  // Helper methods
  private applyDifficultySettings(difficulty: string): void {
    const settings =
      this.difficultySettings[
        difficulty as keyof typeof this.difficultySettings
      ];
    if (settings) {
      this.dropInterval = settings.dropInterval;
      this.level = settings.startLevel;
      this.config.maxLevel = settings.maxLevel;
    }
  }

  private getDifficultyMultiplier(difficulty: string): number {
    const settings =
      this.difficultySettings[
        difficulty as keyof typeof this.difficultySettings
      ];
    return settings?.pointMultiplier || 1;
  }

  private checkLevelUp(): void {
    const newLevel = Math.floor(this.linesCleared / 10) + 1;
    if (newLevel > this.level) {
      this.level = newLevel;
      this.dropInterval = Math.max(100, 1000 - (this.level - 1) * 50);
      this.startTimer();
    }
  }

  // UI helper methods
  getPieceClass(
    cell: number,
    pieceType: string | null
  ): { [key: string]: boolean } {
    if (!pieceType || cell !== this.FILLED) {
      return {};
    }
    return {
      [`piece-${pieceType}`]: true,
      'cell-filled': true,
    };
  }

  isActivePieceCell(row: number, col: number): boolean {
    if (!this.currentPiece) return false;

    const pieceRow = row - this.currentPosition.y;
    const pieceCol = col - this.currentPosition.x;

    return (
      pieceRow >= 0 &&
      pieceRow < this.currentPiece.length &&
      pieceCol >= 0 &&
      pieceCol < this.currentPiece[0].length &&
      this.currentPiece[pieceRow][pieceCol] === 1
    );
  }

  isGhostPieceCell(row: number, col: number): boolean {
    if (!this.ghostPosition || !this.currentPiece) return false;

    const pieceRow = row - this.ghostPosition.y;
    const pieceCol = col - this.ghostPosition.x;

    return (
      pieceRow >= 0 &&
      pieceRow < this.currentPiece.length &&
      pieceCol >= 0 &&
      pieceCol < this.currentPiece[0].length &&
      this.currentPiece[pieceRow][pieceCol] === 1
    );
  }

  private updateGhostPiece(): void {
    if (!this.config?.ghostPiece) return;

    let ghostY = this.currentPosition.y;
    while (
      !this.checkCollision(this.currentPiece, {
        x: this.currentPosition.x,
        y: ghostY + 1,
      })
    ) {
      ghostY++;
    }
    this.ghostPosition = { x: this.currentPosition.x, y: ghostY };
  }

  private initializeNextPieces(): void {
    this.nextPieces = Array(this.config?.showNext || 3)
      .fill(null)
      .map(
        () =>
          this.pieceTypes[Math.floor(Math.random() * this.pieceTypes.length)]
      );

    this.nextPieceType = this.nextPieces[0];
    this.nextPieceGrid = this.PIECES[this.nextPieceType];
  }

  private spawnNewPiece(): void {
    if (this.nextPieces.length === 0) {
      this.initializeNextPieces();
    }

    this.currentPieceType = this.nextPieces.shift() || 'I';
    this.currentPiece = this.PIECES[this.currentPieceType];

    this.nextPieceType = this.nextPieces[0];
    this.nextPieceGrid = this.PIECES[this.nextPieceType];

    this.currentPosition = {
      x: Math.floor((this.grid[0].length - this.currentPiece[0].length) / 2),
      y: 0,
    };

    if (this.checkCollision(this.currentPiece, this.currentPosition)) {
      this.gameOver = true;
      this.stopGame();
    }

    this.updateGhostPiece();
  }

  private checkLines(): void {
    let linesCleared = 0;

    for (let y = this.grid.length - 1; y >= 0; y--) {
      if (this.grid[y].every((cell) => cell === this.FILLED)) {
        this.grid.splice(y, 1);
        this.grid.unshift(Array(this.grid[0].length).fill(this.EMPTY));
        linesCleared++;
        y++; // Check the same line again
      }
    }

    if (linesCleared > 0) {
      this.linesCleared += linesCleared;
      this.score += this.calculateLineScore(linesCleared);
      this.checkLevelUp();
      this.cdr.detectChanges();
    }
  }

  private calculateLineScore(lines: number): number {
    let score = 0;
    const isTspin = this.lastMoveWasRotation && this.isTSpin();
    const isMiniTspin = this.lastMoveWasRotation && this.isMiniTSpin();

    switch (lines) {
      case 1:
        if (isTspin) score = SCORING.TSPIN_SINGLE;
        else if (isMiniTspin) score = SCORING.MINI_TSPIN_SINGLE;
        else score = SCORING.SINGLE;
        break;
      case 2:
        if (isTspin) score = SCORING.TSPIN_DOUBLE;
        else if (isMiniTspin) score = SCORING.MINI_TSPIN_DOUBLE;
        else score = SCORING.DOUBLE;
        break;
      case 3:
        if (isTspin) score = SCORING.TSPIN_TRIPLE;
        else score = SCORING.TRIPLE;
        break;
      case 4:
        score = SCORING.TETRIS;
        break;
    }

    // Apply back-to-back bonus
    if (this.backToBack && (lines === 4 || isTspin || isMiniTspin)) {
      score = Math.floor(score * SCORING.BACK_TO_BACK_MULTIPLIER);
    }

    // Apply combo bonus
    if (lines > 0) {
      this.combo++;
      score += SCORING.COMBO_MULTIPLIER * this.combo;
    } else {
      this.combo = 0;
    }

    // Update back-to-back status
    this.backToBack = lines === 4 || isTspin || isMiniTspin;

    // Apply level multiplier
    return score * this.level;
  }

  public rotatePiece(clockwise: boolean = true): void {
    if (!this.currentPiece || !this.gameStarted || this.gameOver) return;

    const oldState = this.rotationState;
    const oldPiece = this.currentPiece;
    const oldPos = { ...this.currentPosition };

    // Update rotation state
    this.rotationState = ((clockwise
      ? this.rotationState + 1
      : this.rotationState + 3) % 4) as RotationState;

    // Get new rotated piece
    this.currentPiece = this.getRotatedPiece(oldPiece, clockwise);

    // Get wall kick data
    const kicks =
      this.currentPieceType === 'I'
        ? WALL_KICK_DATA.I[oldState]
        : WALL_KICK_DATA.JLSTZ[oldState];

    // Try each wall kick
    for (const [x, y] of kicks) {
      const newPos = {
        x: this.currentPosition.x + x,
        y: this.currentPosition.y - y,
      };

      if (!this.checkCollision(this.currentPiece, newPos)) {
        this.currentPosition = newPos;
        this.lastMoveWasRotation = true;
        this.updateGhostPiece();
        return;
      }
    }

    // If no wall kick worked, revert
    this.currentPiece = oldPiece;
    this.currentPosition = oldPos;
    this.rotationState = oldState;
  }

  private endGame() {
    this.gameOver = true;
    this.stopTimer();

    // Log game end event
    this.logGameEvent('END', {
      finalScore: this.score,
      finalLevel: this.level,
      linesCleared: this.linesCleared,
      tetrisCount: this.tetrisCount,
      pieceStats: this.pieceStats,
      duration: Date.now() - this.startTime,
    });
  }

  private logGameEvent(eventType: string, eventData: any = {}): void {
    if (!this.instance) {
      console.warn('Cannot log game event: No instance available');
      return;
    }

    const gameEvent = {
      id: 0,
      level: this.level,
      score: this.score,
      duration: Date.now() - this.startTime,
      state: eventType,
      payload: JSON.stringify(eventData),
    };

    this.gameService
      .createGameEvent(this.configId, this.instance.id, gameEvent)
      .subscribe({
        next: (event: any) => {
          console.log('Game event logged:', event);
        },
        error: (error: Error) => {
          console.error('Error logging game event:', error);
        },
      });
  }

  // Update keyboard handler
  @HostListener('window:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent): void {
    if (!this.gameStarted || this.gameOver) return;

    switch (event.key) {
      case 'ArrowLeft':
        event.preventDefault();
        this.moveLeft();
        break;
      case 'ArrowRight':
        event.preventDefault();
        this.moveRight();
        break;
      case 'ArrowDown':
        event.preventDefault();
        this.moveDown();
        break;
      case 'ArrowUp':
        event.preventDefault();
        this.rotatePiece(true);
        break;
      case ' ': // Spacebar
        event.preventDefault();
        this.hardDrop();
        break;
      case 'Shift':
        event.preventDefault();
        this.holdCurrentPiece();
        break;
    }
  }

  // Update touch handler
  handleTouch(event: TouchEvent): void {
    const touch = event.touches[0];
    const element = event.target as HTMLElement;
    const rect = element.getBoundingClientRect();
    const relativeX = touch.clientX - rect.left;
    const relativeY = touch.clientY - rect.top;
    const width = rect.width;
    const height = rect.height;

    if (relativeY > height * 0.7) {
      this.moveDown();
    } else if (relativeY < height * 0.3) {
      this.rotatePiece(true);
    } else if (relativeX < width * 0.4) {
      this.moveLeft();
    } else if (relativeX > width * 0.6) {
      this.moveRight();
    }
  }

  // Update control handler
  handleControlClick(control: string): void {
    if (!this.gameStarted || this.gameOver) return;

    switch (control) {
      case 'left':
        this.moveLeft();
        break;
      case 'right':
        this.moveRight();
        break;
      case 'down':
        this.moveDown();
        break;
      case 'rotate':
        this.rotatePiece(true);
        break;
      case 'drop':
        this.hardDrop();
        break;
      case 'hold':
        this.holdCurrentPiece();
        break;
      case 'reset':
        this.resetGame();
        break;
    }
  }

  private setupKeyboardControls(): void {
    // Add keyboard controls
  }

  public resetGame(): void {
    if (!this.instance) return;

    this.gameService
      .resetGameProgress(this.gameId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.initializeGame();
          this.startGame();
        },
        error: (error) => {
          console.error('Error resetting game progress:', error);
        },
      });
  }

  private async loadGameConfig(): Promise<void> {
    try {
      const game = await firstValueFrom(
        this.gameService.getGameById(this.gameId)
      );
      if (!game?.gameConfig?.[0]) {
        console.error('No game config found');
        return;
      }

      this.configId = game.gameConfig[0].id;
      this.config = { ...this.config, ...game.gameConfig[0] } as TetrisConfig;

      const canPlay = await firstValueFrom(
        this.gameService.checkGameAvailability(this.gameId)
      );
      if (!canPlay) {
        this.canPlay = false;
        return;
      }

      await this.loadGameProgress();
    } catch (error) {
      console.error('Error loading game config:', error);
    }
  }

  private async loadGameProgress(): Promise<void> {
    try {
      const progress = await firstValueFrom(
        this.gameService.getGameProgress<TetrisProgress>(this.gameId)
      );
      if (progress) {
        this.gameProgress = progress;
        this.attemptsRemaining = progress.attemptsRemaining;

        if (progress.gameSpecificProgress) {
          this.level =
            progress.gameSpecificProgress.levelReached ||
            this.config.startLevel;
          this.linesCleared = progress.gameSpecificProgress.linesCleared || 0;
          this.tetrisCount = progress.gameSpecificProgress.tetrisCount || 0;
          this.pieceStats =
            progress.gameSpecificProgress.pieceStats || this.pieceStats;
        }

        // Restore game state if it exists
        if (progress.gameSpecificProgress?.state) {
          this.restoreGameState(
            JSON.parse(progress.gameSpecificProgress.state)
          );
        }
      }
    } catch (error) {
      console.error('Error loading game progress:', error);
    }
  }

  private async updateGameProgress(): Promise<void> {
    if (!this.gameProgress) return;

    try {
      const progress: Partial<GameProgress> = {
        lastPlayed: new Date(),
        attemptsRemaining: this.attemptsRemaining,
        highScore: Math.max(this.score, this.gameProgress.highScore || 0),
        currentLevel: this.level,
        gameSpecificProgress: {
          linesCleared: this.linesCleared,
          levelReached: this.level,
          tetrisCount: this.tetrisCount,
          pieceStats: this.pieceStats,
          state: JSON.stringify({
            grid: this.grid,
            currentPiece: this.currentPiece,
            currentPosition: this.currentPosition,
            nextPieces: this.nextPieces,
            heldPiece: this.heldPieceGrid,
            score: this.score,
          }),
        },
      };

      const updatedProgress = await firstValueFrom(
        this.gameService.updateGameProgress(this.gameId, progress)
      );
      this.gameProgress = updatedProgress;

      // Log progress event
      this.logGameEvent('PROGRESS', progress.gameSpecificProgress);
    } catch (error) {
      console.error('Error updating game progress:', error);
    }
  }

  private restoreGameState(state: any): void {
    if (state.grid) this.grid = state.grid;
    if (state.currentPiece) this.currentPiece = state.currentPiece;
    if (state.currentPosition) this.currentPosition = state.currentPosition;
    if (state.nextPieces) this.nextPieces = state.nextPieces;
    if (state.heldPiece) this.heldPieceGrid = state.heldPiece;
    if (state.score) this.score = state.score;
  }

  // Add touch event handlers
  @HostListener('touchstart', ['$event'])
  onTouchStart(event: TouchEvent) {
    const touch = event.touches[0];
    this.touchStartX = touch.clientX;
    this.touchStartY = touch.clientY;
    const now = Date.now();

    if (now - this.lastTouchTime < this.DOUBLE_TAP_DELAY) {
      this.hardDrop();
    }

    this.lastTouchTime = now;
  }

  @HostListener('touchmove', ['$event'])
  onTouchMove(event: TouchEvent) {
    if (!this.gameStarted || this.gameOver) return;

    const touch = event.touches[0];
    const deltaX = touch.clientX - this.touchStartX;
    const deltaY = touch.clientY - this.touchStartY;

    if (Math.abs(deltaX) > this.SWIPE_THRESHOLD) {
      if (deltaX > 0) {
        this.moveRight();
      } else {
        this.moveLeft();
      }
      this.touchStartX = touch.clientX;
    }

    if (deltaY > this.SWIPE_THRESHOLD) {
      this.moveDown();
      this.touchStartY = touch.clientY;
    }
  }

  // T-Spin detection
  private isTSpin(): boolean {
    if (this.currentPieceType !== 'T' || !this.lastMoveWasRotation)
      return false;

    const corners = [
      [-1, -1],
      [1, -1],
      [-1, 1],
      [1, 1],
    ];

    let filledCorners = 0;
    for (const [x, y] of corners) {
      const checkX = this.currentPosition.x + x;
      const checkY = this.currentPosition.y + y;

      if (
        checkX < 0 ||
        checkX >= this.config.boardSize.width ||
        checkY < 0 ||
        checkY >= this.config.boardSize.height ||
        this.grid[checkY][checkX] === this.FILLED
      ) {
        filledCorners++;
      }
    }

    return filledCorners >= 3;
  }

  private isMiniTSpin(): boolean {
    return (
      this.currentPieceType === 'T' &&
      this.lastMoveWasRotation &&
      !this.isTSpin()
    );
  }

  // Game events
  private logSignificantEvent(type: string, data: any = {}): void {
    const significantEvents = [
      'START',
      'END',
      'LEVEL_UP',
      'TETRIS',
      'HIGH_SCORE',
    ];
    if (!significantEvents.includes(type)) return;

    this.logGameEvent(type, {
      ...data,
      score: this.score,
      level: this.level,
      linesCleared: this.linesCleared,
    });
  }

  private getRotatedPiece(
    piece: number[][],
    clockwise: boolean = true
  ): number[][] {
    const N = piece.length;
    const rotated = Array(N)
      .fill(0)
      .map(() => Array(N).fill(0));

    for (let i = 0; i < N; i++) {
      for (let j = 0; j < N; j++) {
        if (clockwise) {
          rotated[j][N - 1 - i] = piece[i][j];
        } else {
          rotated[N - 1 - j][i] = piece[i][j];
        }
      }
    }

    return rotated;
  }

  private showAnimation(type: 'levelUp' | 'combo' | 'tetris'): void {
    switch (type) {
      case 'levelUp':
        this.showLevelUp = true;
        setTimeout(() => (this.showLevelUp = false), 1500);
        break;
      case 'combo':
        this.showCombo = true;
        setTimeout(() => (this.showCombo = false), 1500);
        break;
      case 'tetris':
        this.showTetris = true;
        setTimeout(() => (this.showTetris = false), 1500);
        break;
    }
  }

  private canMove(deltaX: number, deltaY: number): boolean {
    if (!this.currentPiece || !this.gameStarted || this.gameOver) return false;

    const newPosition = {
      x: this.currentPosition.x + deltaX,
      y: this.currentPosition.y + deltaY,
    };

    return !this.checkCollision(this.currentPiece, newPosition);
  }
}
