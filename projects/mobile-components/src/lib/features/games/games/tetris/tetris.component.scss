// Import Sass color module
@use 'sass:color';

// Modern bright color palette
$background: #1a1a2e;
$surface: #16213e;
$primary: #0f3460;
$accent: #e94560;
$text: #ffffff;

// Bright piece colors
$piece-colors: (
  'I': #00ffff,  // <PERSON>an
  'O': #ffff00,  // Yellow
  'T': #ff00ff,  // Magenta
  'L': #ff8800,  // Orange
  'J': #0088ff,  // Blue
  'S': #00ff00,  // Green
  'Z': #ff0000   // Red
);

:host {
  display: block;
  width: 100%;
  height: 100vh;
  background-color: $background;
  overflow: hidden;
  touch-action: none; // Prevent browser handling of touch events
}

.game-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 1rem;
  height: 100%;
  color: $text;
  font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
  max-width: 600px;
  margin: 0 auto;

  @media (max-width: 600px) {
    padding: 0.5rem;
  }
}

.game-header {
  width: 100%;
  margin-bottom: 1rem;
}

.score-panel {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  background-color: $surface;
  padding: 1rem;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

  @media (max-width: 600px) {
    gap: 0.5rem;
    padding: 0.75rem;
  }
}

.score-item {
  text-align: center;

  h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 500;
    color: rgba($text, 0.9);

    @media (max-width: 600px) {
      font-size: 0.875rem;
    }
  }

  .score, .level, .lines {
    font-size: 1.5rem;
    font-weight: 600;
    color: $accent;
    text-shadow: 0 0 10px rgba($accent, 0.5);

    @media (max-width: 600px) {
      font-size: 1.25rem;
    }
  }
}

.game-area {
  display: grid;
  grid-template-columns: auto minmax(auto, 300px) auto;
  gap: 1rem;
  width: 100%;
  flex: 1;
  margin: 0 auto;
  align-items: start;

  @media (max-width: 600px) {
    grid-template-columns: 60px minmax(auto, 300px) 60px;
    gap: 0.5rem;
  }
}

.side-panel {
  background-color: $surface;
  padding: 1rem;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  height: fit-content;

  @media (max-width: 600px) {
    padding: 0.5rem;
  }

  h3 {
    margin: 0 0 0.5rem;
    font-size: 1rem;
    text-align: center;
    color: rgba($text, 0.9);

    @media (max-width: 600px) {
      font-size: 0.875rem;
      margin: 0 0 0.25rem;
    }
  }
}

.game-grid {
  position: relative;
  display: grid;
  grid-template-rows: repeat(20, 1fr);
  gap: 1px;
  background-color: $surface;
  border-radius: 12px;
  padding: 0.5rem;
  aspect-ratio: 1/2;
  width: 100%;
  max-width: 300px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
  margin: 0 auto;
  overflow: hidden;

  @media (max-width: 600px) {
    padding: 0.25rem;
  }
}

.grid-row {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  gap: 1px;
  height: 100%;
}

.grid-cell {
  aspect-ratio: 1;
  border-radius: 2px;
  background-color: rgba($primary, 0.3);
  transition: all 0.15s ease;
  border: 1px solid rgba($surface, 0.3);
  position: relative;

  &.cell-empty {
    background-color: rgba($primary, 0.1);
  }

  &.cell-filled {
    background-color: $primary;
    box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.2);

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 50%);
      border-radius: inherit;
    }
  }

  &.active-piece {
    box-shadow: 0 0 12px rgba(255, 255, 255, 0.2);
    animation: glow 1.5s ease-in-out infinite alternate;
  }

  &.ghost-piece {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px dashed rgba(255, 255, 255, 0.3);
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.2);
  }
  to {
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.4);
  }
}

// Generate piece color classes with enhanced styling
@each $piece, $color in $piece-colors {
  .piece-#{$piece} {
    background-color: $color !important;
    box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.2),
                0 0 12px rgba($color, 0.3);

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 60%);
      border-radius: inherit;
    }

    &.active-piece {
      animation: glow-#{$piece} 1.5s ease-in-out infinite alternate;
    }
  }

  @keyframes glow-#{$piece} {
    from {
      box-shadow: 0 0 5px rgba($color, 0.3);
    }
    to {
      box-shadow: 0 0 20px rgba($color, 0.6);
    }
  }
}

.hold-piece-grid,
.next-pieces {
  display: grid;
  grid-template-rows: repeat(4, 1fr);
  gap: 2px;
  background-color: rgba($primary, 0.1);
  border-radius: 8px;
  padding: 8px;
  aspect-ratio: 1;
  width: 100%;
  margin: 0 auto;

  @media (max-width: 600px) {
    gap: 1px;
    padding: 4px;
  }

  > div {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2px;

    @media (max-width: 600px) {
      gap: 1px;
    }
  }
}

.hold-piece-cell {
  aspect-ratio: 1;
  border-radius: 2px;
  background-color: rgba($primary, 0.1);

  &.cell-filled {
    box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.2);
  }
}

.game-controls {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
  padding: 1rem;
  background-color: rgba($surface, 0.95);
  backdrop-filter: blur(10px);

  @media (max-width: 600px) {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    grid-template-columns: repeat(6, 1fr);
    gap: 0.5rem;
    padding: 0.75rem;
    z-index: 100;
  }
}

.control-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  aspect-ratio: 1;
  background-color: rgba($primary, 0.3);
  border: none;
  border-radius: 12px;
  color: $text;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    border-radius: inherit;
  }

  &:active {
    transform: scale(0.9);
    background-color: rgba($primary, 0.5);
  }

  ion-icon {
    font-size: 1.5rem;

    @media (max-width: 600px) {
      font-size: 1.25rem;
    }
  }

  @media (max-width: 600px) {
    border-radius: 8px;
  }
}

.start-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba($background, 0.9);
  z-index: 10;
  border-radius: 12px;
  backdrop-filter: blur(5px);
}

.start-button,
.restart-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  font-size: 1.2rem;
  background-color: $accent;
  color: $text;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba($accent, 0.3);

  &:hover {
    background-color: color.adjust($accent, $lightness: 5%);
    transform: translateY(-1px);
  }

  &:active {
    transform: scale(0.98);
  }

  ion-icon {
    font-size: 1.5rem;
  }
}

.game-over {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba($surface, 0.95);
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  backdrop-filter: blur(5px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 20;

  h2 {
    margin: 0 0 1rem;
    color: $accent;
    font-size: 2rem;
    text-shadow: 0 0 10px rgba($accent, 0.5);
  }

  .final-score {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: $text;
  }
}

// Animations for game events
.level-up-animation,
.combo-animation,
.tetris-animation {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 30;
  pointer-events: none;
  text-align: center;
}

.level-up-text {
  font-size: 3rem;
  font-weight: bold;
  color: #ffeb3b;
  text-shadow: 0 0 20px rgba(#ffeb3b, 0.8);
  animation: levelUpAnimation 1.5s cubic-bezier(0.4, 0, 0.2, 1);

  @media (max-width: 600px) {
    font-size: 2rem;
  }
}

.combo-text {
  font-size: 2.5rem;
  font-weight: bold;
  color: #ff9800;
  text-shadow: 0 0 15px rgba(#ff9800, 0.8);
  animation: comboAnimation 1s cubic-bezier(0.4, 0, 0.2, 1);

  @media (max-width: 600px) {
    font-size: 1.75rem;
  }
}

.tetris-text {
  font-size: 4rem;
  font-weight: bold;
  color: #f50057;
  text-shadow: 0 0 30px rgba(#f50057, 0.8);
  animation: tetrisAnimation 2s cubic-bezier(0.4, 0, 0.2, 1);

  @media (max-width: 600px) {
    font-size: 2.5rem;
  }
}

@keyframes levelUpAnimation {
  0% {
    opacity: 0;
    transform: scale(0.5) translateY(50px);
  }
  20% {
    opacity: 1;
    transform: scale(1.2) translateY(0);
  }
  80% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  100% {
    opacity: 0;
    transform: scale(1.5) translateY(-50px);
  }
}

@keyframes comboAnimation {
  0% {
    opacity: 0;
    transform: scale(0.8) rotate(-10deg);
  }
  20% {
    opacity: 1;
    transform: scale(1.2) rotate(5deg);
  }
  80% {
    opacity: 1;
    transform: scale(1) rotate(0);
  }
  100% {
    opacity: 0;
    transform: scale(0.8) rotate(10deg);
  }
}

@keyframes tetrisAnimation {
  0% {
    opacity: 0;
    transform: scale(0.5);
    letter-spacing: -10px;
  }
  20% {
    opacity: 1;
    transform: scale(1.2);
    letter-spacing: 10px;
  }
  40% {
    transform: scale(1);
    letter-spacing: 5px;
  }
  80% {
    opacity: 1;
    transform: scale(1);
    letter-spacing: 5px;
  }
  100% {
    opacity: 0;
    transform: scale(1.5);
    letter-spacing: 20px;
  }
}

// Enhanced game over screen
.game-over {
  animation: gameOverAnimation 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes gameOverAnimation {
  0% {
    opacity: 0;
    transform: translate(-50%, -40%);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}
