<div class="game-container">
  <!-- Score Display -->
  <games-score
    [scores]="[
      { title: 'White Score', number: whiteScore },
      { title: 'Black Score', number: blackScore },
      { title: 'Moves', number: moveCount }
    ]"
  ></games-score>

  <!-- Game Title -->
  <div class="game-title">
    <h1>Chess</h1>
    <p>
      Current Player: {{ currentPlayer }}
      <span *ngIf="isCheck" class="text-red-600 font-bold ml-2">(CHECK!)</span>
    </p>
  </div>

  <!-- Error Message -->
  <div *ngIf="errorMessage" class="error-message">
    {{ errorMessage }}
  </div>

  <!-- Game Board Container -->
  <div class="game-board-container">
    <div class="game-board">
      <div class="chess-board">
        <ng-container *ngFor="let row of board; let i = index">
          <ng-container *ngFor="let piece of row; let j = index">
            <div
              (click)="onSquareClick(i, j)"
              [class]="getSquareColor(i, j) + ' chess-square'"
              [class.selected]="selectedPiece && selectedPiece.row === i && selectedPiece.col === j"
              [style.grid-row]="i+1"
              [style.grid-column]="j+1"
            >
              <span class="chess-piece">{{ piece }}</span>
            </div>
          </ng-container>
        </ng-container>
      </div>
    </div>
  </div>

  <!-- Game Controls -->
  <div class="controls">
    <lib-games-play-button
      [controls]="gameControls"
      (controlClicked)="handleControlClick($event)"
    ></lib-games-play-button>
  </div>

  <!-- Win/Lose Overlay -->
  <lib-win-lose-overlay
    *ngIf="gameOver"
    [status]="winner === 'white' ? 'win' : 'lose'"
    (restart)="resetGame()"
    [message]="
      gameEndReason === 'checkmate'
        ? winner + ' wins by checkmate!'
        : winner + ' wins by capturing the king!'
    "
  ></lib-win-lose-overlay>
</div>
