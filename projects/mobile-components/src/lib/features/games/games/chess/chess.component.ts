import { Component, OnInit, Input, Output, EventEmitter, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgIf, NgFor } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { WinLoseOverlayComponent } from '../components/win-lose-overlay/win-lose-overlay.component';
import { GameControl, GamesPlayButtonComponent } from '../components/games-play-button/games-play-button.component';
import { Game, GameEvent } from 'lp-client-api';

@Component({
  selector: 'lib-chess',
  templateUrl: './chess.component.html',
  styleUrls: ['./chess.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    NgIf,
    NgFor,
    GamesScoreComponent,
    WinLoseOverlayComponent,
    GamesPlayButtonComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class ChessComponent implements OnInit {
  @Input() gameId: number = 0;
  @Input() config: any;
  @Input() gameInstance: any;
  @Input() game: Game | undefined;

  @Output() gameEvent = new EventEmitter<GameEvent>();

  board: string[][] = [];
  selectedPiece: { row: number; col: number } | null = null;
  currentPlayer: 'white' | 'black' = 'white';
  gameOver: boolean = false;
  winner: 'white' | 'black' | null = null;
  errorMessage: string | null = null;
  isCheck: boolean = false;
  gameEndReason: 'checkmate' | 'capture' | null = null;

  // Game stats
  moveCount: number = 0;
  whiteScore: number = 0;
  blackScore: number = 0;

  // Game controls
  gameControls: GameControl[] = [
    { name: 'reset', label: 'New Game', icon: 'refresh-outline' }
  ];

  ngOnInit() {
    this.initializeBoard();
  }

  initializeBoard() {
    // Initialize the chess board
    this.board = [
      ['♜', '♞', '♝', '♛', '♚', '♝', '♞', '♜'],
      ['♟', '♟', '♟', '♟', '♟', '♟', '♟', '♟'],
      ['', '', '', '', '', '', '', ''],
      ['', '', '', '', '', '', '', ''],
      ['', '', '', '', '', '', '', ''],
      ['', '', '', '', '', '', '', ''],
      ['♙', '♙', '♙', '♙', '♙', '♙', '♙', '♙'],
      ['♖', '♘', '♗', '♕', '♔', '♗', '♘', '♖'],
    ];
  }

  onSquareClick(row: number, col: number) {
    if (this.gameOver) {
      this.errorMessage = 'Game is over. Please start a new game.';
      return;
    }

    console.log(`Clicked: row ${row}, col ${col}`);
    console.log(`Current player: ${this.currentPlayer}`);
    console.log(`Selected piece: ${JSON.stringify(this.selectedPiece)}`);

    const piece = this.board[row][col];
    console.log(`Piece at clicked position: ${piece}`);

    if (this.selectedPiece) {
      if (this.isValidMove(row, col)) {
        this.movePiece(row, col);
        this.errorMessage = null; // Clear any previous error messages
      } else {
        this.errorMessage = 'Invalid move. Please try again.';
        console.log('Invalid move, deselecting piece');
      }
      this.selectedPiece = null;
    } else if (piece && this.isCurrentPlayerPiece(piece)) {
      this.selectPiece(row, col);
      this.errorMessage = null; // Clear any previous error messages
    } else {
      this.errorMessage =
        "Cannot select this piece. It's not your turn or the square is empty.";
    }
  }

  selectPiece(row: number, col: number) {
    const piece = this.board[row][col];
    if (piece && this.isCurrentPlayerPiece(piece)) {
      this.selectedPiece = { row, col };
      console.log(`Selected piece: ${piece} at row ${row}, col ${col}`);
    }
  }

  isCurrentPlayerPiece(piece: string): boolean {
    if (!piece) return false;
    const isWhitePiece = '♔♕♖♗♘♙'.includes(piece);
    const isBlackPiece = '♚♛♜♝♞♟'.includes(piece);
    const result =
      (this.currentPlayer === 'white' && isWhitePiece) ||
      (this.currentPlayer === 'black' && isBlackPiece);
    console.log(`Is current player's piece: ${result}`);
    return result;
  }

  isValidMove(row: number, col: number): boolean {
    if (!this.selectedPiece) return false;

    // First check if the move is valid according to piece rules
    const basicValidMove = this.isValidMoveForPiece(row, col);

    if (basicValidMove) {
      // Simulate the move and check if it puts/leaves own king in check
      const simulatedBoard = this.getSimulatedBoard(row, col);
      if (this.isKingInCheck(this.currentPlayer, simulatedBoard)) {
        this.errorMessage = 'This move would put or leave your king in check!';
        return false;
      }
      return true;
    }
    return false;
  }

  isValidPawnMove(row: number, col: number): boolean {
    if (!this.selectedPiece) return false;

    const { row: fromRow, col: fromCol } = this.selectedPiece;
    const direction = this.currentPlayer === 'white' ? -1 : 1;
    const startRow = this.currentPlayer === 'white' ? 6 : 1;

    // Move one square forward
    if (
      col === fromCol &&
      row === fromRow + direction &&
      !this.board[row][col]
    ) {
      return true;
    }

    // Move two squares forward from the starting position
    if (
      fromRow === startRow &&
      col === fromCol &&
      row === fromRow + 2 * direction &&
      !this.board[fromRow + direction][col] &&
      !this.board[row][col]
    ) {
      return true;
    }

    // Capture diagonally
    if (
      Math.abs(col - fromCol) === 1 &&
      row === fromRow + direction &&
      this.board[row][col] &&
      !this.isCurrentPlayerPiece(this.board[row][col])
    ) {
      return true;
    }

    return false;
  }

  isValidRookMove(row: number, col: number): boolean {
    if (!this.selectedPiece) return false;
    const { row: fromRow, col: fromCol } = this.selectedPiece;

    if (row !== fromRow && col !== fromCol) return false;

    const rowStep = row > fromRow ? 1 : row < fromRow ? -1 : 0;
    const colStep = col > fromCol ? 1 : col < fromCol ? -1 : 0;

    let currentRow = fromRow + rowStep;
    let currentCol = fromCol + colStep;

    while (currentRow !== row || currentCol !== col) {
      if (this.board[currentRow][currentCol] !== '') return false;
      currentRow += rowStep;
      currentCol += colStep;
    }

    return true;
  }

  isValidKnightMove(row: number, col: number): boolean {
    if (!this.selectedPiece) return false;
    const { row: fromRow, col: fromCol } = this.selectedPiece;

    const rowDiff = Math.abs(row - fromRow);
    const colDiff = Math.abs(col - fromCol);

    return (rowDiff === 2 && colDiff === 1) || (rowDiff === 1 && colDiff === 2);
  }

  isValidBishopMove(row: number, col: number): boolean {
    if (!this.selectedPiece) return false;
    const { row: fromRow, col: fromCol } = this.selectedPiece;

    if (Math.abs(row - fromRow) !== Math.abs(col - fromCol)) return false;

    const rowStep = row > fromRow ? 1 : -1;
    const colStep = col > fromCol ? 1 : -1;

    let currentRow = fromRow + rowStep;
    let currentCol = fromCol + colStep;

    while (currentRow !== row && currentCol !== col) {
      if (this.board[currentRow][currentCol] !== '') return false;
      currentRow += rowStep;
      currentCol += colStep;
    }

    return true;
  }

  isValidQueenMove(row: number, col: number): boolean {
    return this.isValidRookMove(row, col) || this.isValidBishopMove(row, col);
  }

  isValidKingMove(row: number, col: number): boolean {
    if (!this.selectedPiece) return false;
    const { row: fromRow, col: fromCol } = this.selectedPiece;

    const rowDiff = Math.abs(row - fromRow);
    const colDiff = Math.abs(col - fromCol);

    return rowDiff <= 1 && colDiff <= 1;
  }

  movePiece(row: number, col: number) {
    if (this.selectedPiece) {
      const movingPiece =
        this.board[this.selectedPiece.row][this.selectedPiece.col];
      const targetPiece = this.board[row][col];

      // Make the move
      this.board[row][col] = movingPiece;
      this.board[this.selectedPiece.row][this.selectedPiece.col] = '';
      console.log(`Moved piece ${movingPiece} to row ${row}, col ${col}`);

      // Increment move count
      this.moveCount++;

      // Update score if a piece was captured
      if (targetPiece) {
        if (this.currentPlayer === 'white') {
          this.whiteScore += this.getPieceValue(targetPiece);
        } else {
          this.blackScore += this.getPieceValue(targetPiece);
        }
      }

      const nextPlayer = this.currentPlayer === 'white' ? 'black' : 'white';
      this.isCheck = this.isKingInCheck(nextPlayer);

      // Check for checkmate first
      if (this.isCheck && this.isCheckmate()) {
        this.gameEndReason = 'checkmate';
        this.endGame();
      }
      // If not checkmate, check if a king was captured
      else if (targetPiece === '♚' || targetPiece === '♔') {
        this.gameEndReason = 'capture';
        this.endGame();
      }
      // If game continues, handle check state
      else if (this.isCheck) {
        this.errorMessage = `${nextPlayer} is in check!`;
      }

      if (!this.gameOver) {
        this.switchTurn();
      }

      // Emit move event
      this.emitGameEvent('move', {
        from: { row: this.selectedPiece.row, col: this.selectedPiece.col },
        to: { row, col },
        piece: movingPiece,
        captured: targetPiece || null
      });
    }
  }

  // Helper method to get piece value for scoring
  getPieceValue(piece: string): number {
    switch(piece) {
      case '♟': case '♙': return 1;  // Pawn
      case '♞': case '♘': return 3;  // Knight
      case '♝': case '♗': return 3;  // Bishop
      case '♜': case '♖': return 5;  // Rook
      case '♛': case '♕': return 9;  // Queen
      case '♚': case '♔': return 0;  // King (no point value)
      default: return 0;
    }
  }

  endGame() {
    this.gameOver = true;
    this.winner = this.currentPlayer;
    if (!this.gameEndReason) {
      this.gameEndReason = 'capture';
    }

    // Emit game over event
    this.emitGameEvent(this.winner === 'white' ? 'win' : 'lose', {
      reason: this.gameEndReason,
      winner: this.winner,
      whiteScore: this.whiteScore,
      blackScore: this.blackScore,
      moveCount: this.moveCount
    });
  }

  switchTurn() {
    this.currentPlayer = this.currentPlayer === 'white' ? 'black' : 'white';
    console.log(`Switched turn to ${this.currentPlayer}`);
  }

  resetGame() {
    this.initializeBoard();
    this.currentPlayer = 'white';
    this.selectedPiece = null;
    this.gameOver = false;
    this.winner = null;
    this.gameEndReason = null;
    this.moveCount = 0;
    this.whiteScore = 0;
    this.blackScore = 0;
    this.errorMessage = null;

    // Emit reset event
    this.emitGameEvent('reset');
  }

  // Handle game control clicks
  handleControlClick(controlName: string) {
    if (controlName === 'reset') {
      this.resetGame();
    }
  }

  // Emit game events
  emitGameEvent(type: string, data: any = {}) {
    // Create a GameEvent object according to the interface
    const gameEvent: GameEvent = {
      id: 0, // This will be set by the backend
      score: this.currentPlayer === 'white' ? this.whiteScore : this.blackScore,
      level: 1,
      duration: 0, // Calculate duration if needed
      state: type, // Use the event type as state
      payload: JSON.stringify({
        ...data,
        currentPlayer: this.currentPlayer,
        moveCount: this.moveCount,
        whiteScore: this.whiteScore,
        blackScore: this.blackScore,
        gameId: this.gameId
      })
    };

    this.gameEvent.emit(gameEvent);
  }

  getSquareColor(row: number, col: number): string {
    return (row + col) % 2 === 0 ? 'bg-gray-300' : 'bg-gray-600';
  }

  isValidMoveForPiece(row: number, col: number): boolean {
    if (!this.selectedPiece) return false;

    const piece = this.board[this.selectedPiece.row][this.selectedPiece.col];
    const targetPiece = this.board[row][col];

    // Check if the target square is occupied by a piece of the same color
    if (targetPiece && this.isCurrentPlayerPiece(targetPiece)) {
      return false;
    }

    // Check specific piece movement
    switch (piece) {
      case '♟':
      case '♙':
        console.log('Pawn move');
        return this.isValidPawnMove(row, col);
      case '♜':
      case '♖':
        console.log('Rook move');
        return this.isValidRookMove(row, col);
      case '♞':
      case '♘':
        console.log('Knight move');
        return this.isValidKnightMove(row, col);
      case '♝':
      case '♗':
        console.log('Bishop move');
        return this.isValidBishopMove(row, col);
      case '♛':
      case '♕':
        console.log('Queen move');
        return this.isValidQueenMove(row, col);
      case '♚':
      case '♔':
        console.log('King move');
        return this.isValidKingMove(row, col);
      default:
        console.log('Invalid move');
        return false;
    }
  }

  getSimulatedBoard(toRow: number, toCol: number): string[][] {
    const simulatedBoard = this.board.map((row) => [...row]);
    if (this.selectedPiece) {
      const piece =
        simulatedBoard[this.selectedPiece.row][this.selectedPiece.col];
      simulatedBoard[toRow][toCol] = piece;
      simulatedBoard[this.selectedPiece.row][this.selectedPiece.col] = '';
    }
    return simulatedBoard;
  }

  isKingInCheck(
    player: 'white' | 'black',
    boardState: string[][] = this.board
  ): boolean {
    // Find king's position
    const kingSymbol = player === 'white' ? '♔' : '♚';
    let kingRow = -1,
      kingCol = -1;

    for (let i = 0; i < 8; i++) {
      for (let j = 0; j < 8; j++) {
        if (boardState[i][j] === kingSymbol) {
          kingRow = i;
          kingCol = j;
          break;
        }
      }
      if (kingRow !== -1) break;
    }

    // Check if any opponent's piece can capture the king
    const opponent = player === 'white' ? 'black' : 'white';
    for (let i = 0; i < 8; i++) {
      for (let j = 0; j < 8; j++) {
        const piece = boardState[i][j];
        if (piece && this.isPieceColor(piece, opponent)) {
          const savedSelectedPiece = this.selectedPiece;
          this.selectedPiece = { row: i, col: j };
          const canCapture = this.isValidMoveForPiece(kingRow, kingCol);
          this.selectedPiece = savedSelectedPiece;
          if (canCapture) return true;
        }
      }
    }
    return false;
  }

  isPieceColor(piece: string, color: 'white' | 'black'): boolean {
    return color === 'white'
      ? '♔♕♖♗♘♙'.includes(piece)
      : '♚♛♜♝♞♟'.includes(piece);
  }

  isCheckmate(): boolean {
    // Try all possible moves for the current player
    for (let fromRow = 0; fromRow < 8; fromRow++) {
      for (let fromCol = 0; fromCol < 8; fromCol++) {
        const piece = this.board[fromRow][fromCol];
        if (piece && this.isCurrentPlayerPiece(piece)) {
          for (let toRow = 0; toRow < 8; toRow++) {
            for (let toCol = 0; toCol < 8; toCol++) {
              const savedSelectedPiece = this.selectedPiece;
              this.selectedPiece = { row: fromRow, col: fromCol };
              if (this.isValidMove(toRow, toCol)) {
                this.selectedPiece = savedSelectedPiece;
                return false; // Found at least one valid move
              }
              this.selectedPiece = savedSelectedPiece;
            }
          }
        }
      }
    }
    return true; // No valid moves found
  }
}
