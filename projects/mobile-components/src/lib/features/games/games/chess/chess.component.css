/* Import shared game styles */
@import '../styles/game-styles.css';

:host {
  @apply block w-full h-full;
}

.game-container {
  @apply flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800;
}

/* Game Title */
.game-title {
  @apply text-center mb-1;
}

.game-title h1 {
  @apply text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-indigo-500;
}

.game-title p {
  @apply text-xs text-slate-400 mb-1;
}

/* Error Message */
.error-message {
  @apply text-red-500 font-bold mx-auto my-2 p-2 bg-red-100/20 border border-red-500/50 rounded-md max-w-md text-center;
}

/* Game Board Container */
.game-board-container {
  @apply flex-grow flex justify-center items-center p-2;
  position: relative;
  z-index: 1;
}

.game-board {
  @apply p-4 rounded-xl bg-slate-700/50 border border-slate-600 shadow-xl;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.game-board::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-blue-500/10;
  filter: blur(20px);
  z-index: 0;
}

/* Chess Board */
.chess-board {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  grid-template-rows: repeat(8, 1fr);
  width: 320px;
  height: 320px;
  border: 2px solid #1e293b;
  border-radius: 4px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 10;
  overflow: hidden;
}

.chess-square {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.chess-square::before {
  content: '';
  @apply absolute inset-0 opacity-30;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
  z-index: 1;
}

.chess-square.selected {
  @apply border-2 border-blue-500 shadow-lg;
  transform: scale(1.05);
  z-index: 10;
}

.chess-piece {
  position: relative;
  z-index: 10;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  font-size: 28px;
  line-height: 1;
}

/* Controls */
.controls {
  @apply flex justify-center p-4;
  position: relative;
  z-index: 1;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .chess-board {
    width: 280px;
    height: 280px;
  }

  .chess-square {
    width: 35px;
    height: 35px;
  }

  .chess-piece {
    font-size: 24px;
  }
}
