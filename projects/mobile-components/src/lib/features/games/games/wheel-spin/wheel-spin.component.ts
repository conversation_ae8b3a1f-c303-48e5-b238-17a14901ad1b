import {
  Component,
  OnInit,
  ViewChild,
  ElementRef,
  AfterViewInit,
  Input,
  Output,
  EventEmitter,
  NgZone,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
  ChangeDetectorRef
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { GameService } from 'lp-client-api';
import {
  Game,
  SpinWheelConfig,
  WheelSection,
  SpinWheelProgress,
  GameProgressType,
  GameEvent,
  GameInstance
} from 'lp-client-api';

@Component({
  selector: 'app-wheel-spin',
  templateUrl: './wheel-spin.component.html',
  styleUrls: ['./wheel-spin.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    IonicModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class WheelSpinComponent implements OnInit, AfterViewInit {
  @ViewChild('wheelCanvas', { static: true })
  wheelCanvas!: ElementRef<HTMLCanvasElement>;

  @Input() gameId: string = '390'; // Default Spin Wheel game ID
  @Input() game: Game | null = null;
  @Input() gameInstance: GameInstance | null = null;
  @Input() config: SpinWheelConfig | undefined;
  @Input() globalConfig: any;

  @Output() gameEvent = new EventEmitter<GameEvent>();

  isSpinning = false;
  currentReward: string = '';
  remainingSpins = 0;
  nextSpinTime: Date | null = null;
  demoMode: boolean = false; // Flag for demo mode when API fails
  gameResult: 'win' | 'lose' | null = null;

  private ctx!: CanvasRenderingContext2D;
  private wheelRadius = 175;
  private wheelCenterX!: number;
  private wheelCenterY!: number;
  private rotationAngle = 0;
  private spinSpeed = 0;
  private deceleration = 0.99;
  private readonly STOP_DECELERATION = 0.995;
  private readonly MIN_SPEED = 0.001;
  private centerImage: HTMLImageElement | null = null;
  private pointerImage: HTMLImageElement | null = null;
  private centerImageLoaded = false;
  private pointerImageLoaded = false;

  showRewardAnimation = false;
  timeUntilNextSpin = '';
  private updateTimer: any;
  private gameProgress: GameProgressType<SpinWheelProgress> | undefined;

  canStop = false;
  stopRequested = false;

  constructor(
    private gameService: GameService,
    private zone: NgZone,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit() {
    console.log('WheelSpinComponent: Initializing...', {
      gameId: this.gameId,
      game: this.game,
      gameInstance: this.gameInstance,
      config: this.config
    });

    // If the provided config doesn't have wheelSections, initialize demo mode
    if (this.config && (!this.config.wheelSections || !Array.isArray(this.config.wheelSections))) {
      console.log('Config provided but missing wheelSections array, initializing demo mode');
      this.demoMode = true;
      this.initializeDemoMode();
    }
    // If config is provided via @Input with valid wheelSections, use it directly
    else if (this.config && Array.isArray(this.config.wheelSections)) {
      console.log('Using provided config with valid wheelSections:', this.config);
      this.initializeGame();
    } else {
      // Otherwise, try to load from API
      this.loadGameConfig();
    }

    // Set background image if available from game input
    if (this.game?.backgroundImage) {
      console.log('Setting background image from game input:', this.game.backgroundImage);
      document.documentElement.style.setProperty(
        '--wheel-game-bg',
        `url(${this.game.backgroundImage})`
      );
    }

    // Load game progress or use demo mode if API fails
    this.loadGameProgress();
  }

  private loadGameConfig() {
    // Convert string gameId to number for API call
    const numericGameId = parseInt(this.gameId, 10);

    this.gameService.getGameById(numericGameId).subscribe({
      next: (game) => {
        console.log('Game config loaded:', game);
        if (game && game.gameConfig[0]) {
          this.config = game.gameConfig[0] as SpinWheelConfig;
          console.log('Wheel configuration:', this.config);

          if (game.backgroundImage) {
            console.log('Setting background image:', game.backgroundImage);
            document.documentElement.style.setProperty(
              '--wheel-game-bg',
              `url(${game.backgroundImage})`
            );
          }

          this.initializeGame();
        }
      },
      error: (error) => {
        console.error('Error loading game config:', error);
        this.demoMode = true;
        this.initializeDemoMode();
      }
    });
  }

  private loadGameProgress() {
    // Convert string gameId to number for API call
    const numericGameId = parseInt(this.gameId, 10);

    this.gameService.getGameProgress<SpinWheelProgress>(numericGameId).subscribe({
      next: (progress) => {
        this.gameProgress = progress;
        this.remainingSpins = progress.attemptsRemaining;
        console.log('Remaining spins:', this.remainingSpins);

        if (progress.lastPlayed) {
          const resetTime = new Date(progress.lastPlayed);
          resetTime.setHours(resetTime.getHours() + 24);
          console.log('Next reset time:', resetTime);
          if (new Date() < resetTime) {
            this.nextSpinTime = resetTime;
          }
        }

        this.updateNextSpinTimer();
      },
      error: (error) => {
        console.error('Error loading game progress:', error);
        this.demoMode = true;
        this.remainingSpins = 3; // Default attempts in demo mode
        this.initializeDemoMode();
      }
    });
  }

  private initializeDemoMode() {
    console.log('Initializing demo mode');

    // Create a demo configuration
    this.config = {
      id: 0,
      difficulty: 'EASY',
      frequency: 'DAILY',
      frequencyAttempts: 3,
      sectionAmount: 8,
      spinDuration: 5000,
      minSpinSpeed: 0.01,
      maxSpinSpeed: 0.3,
      wheelSections: [
        { id: 1, probability: 0.2, value: 10, label: '10 Points', backgroundColor: '#FF5252', createdBy: '', createdOn: '', version: 0 },
        { id: 2, probability: 0.2, value: 20, label: '20 Points', backgroundColor: '#FF9800', createdBy: '', createdOn: '', version: 0 },
        { id: 3, probability: 0.15, value: 30, label: '30 Points', backgroundColor: '#FFEB3B', createdBy: '', createdOn: '', version: 0 },
        { id: 4, probability: 0.15, value: 40, label: '40 Points', backgroundColor: '#4CAF50', createdBy: '', createdOn: '', version: 0 },
        { id: 5, probability: 0.1, value: 50, label: '50 Points', backgroundColor: '#2196F3', createdBy: '', createdOn: '', version: 0 },
        { id: 6, probability: 0.1, value: 75, label: '75 Points', backgroundColor: '#673AB7', createdBy: '', createdOn: '', version: 0 },
        { id: 7, probability: 0.05, value: 100, label: '100 Points', backgroundColor: '#9C27B0', createdBy: '', createdOn: '', version: 0 },
        { id: 8, probability: 0.05, value: 200, label: '200 Points', backgroundColor: '#E91E63', createdBy: '', createdOn: '', version: 0 }
      ],
      wheelDesign: {
        borderColor: '#333',
        borderWidth: 3
      }
    };

    this.initializeGame();
  }

  private initializeGame() {
    console.log('initializeGame', this.config);
    if (!this.config) return;

    // Check if wheelSections exists and is an array
    if (!this.config.wheelSections || !Array.isArray(this.config.wheelSections) || this.config.wheelSections.length === 0) {
      console.error('Invalid wheel sections in config, switching to demo mode');
      this.demoMode = true;
      this.initializeDemoMode();
      return;
    }

    // Load images
    if (this.config.wheelDesign?.centerImage) {
      this.centerImage = new Image();
      this.centerImage.onload = () => {
        this.centerImageLoaded = true;
        this.drawWheel();
      };
      this.centerImage.src = this.config.wheelDesign.centerImage;
    }

    if (this.config.wheelDesign?.pointerImage) {
      this.pointerImage = new Image();
      this.pointerImage.onload = () => {
        this.pointerImageLoaded = true;
        this.drawWheel();
      };
      this.pointerImage.src = this.config.wheelDesign.pointerImage;
    }
  }

  ngAfterViewInit() {
    const canvas = this.wheelCanvas.nativeElement;
    this.ctx = canvas.getContext('2d')!;

    // Set canvas size
    canvas.width = this.wheelRadius * 2;
    canvas.height = this.wheelRadius * 2;

    this.wheelCenterX = canvas.width / 2;
    this.wheelCenterY = canvas.height / 2;

    this.drawWheel();
  }

  private drawWheel() {
    if (!this.ctx || !this.config) return;

    this.ctx.clearRect(
      0,
      0,
      this.wheelCanvas.nativeElement.width,
      this.wheelCanvas.nativeElement.height
    );

    // Check if wheelSections exists, is an array, and has length
    if (!this.config.wheelSections || !Array.isArray(this.config.wheelSections) || this.config.wheelSections.length === 0) {
      console.error('No wheel sections defined in config or wheelSections is not an array', this.config);

      // If in demo mode but wheelSections is missing, reinitialize demo mode
      if (this.demoMode) {
        this.initializeDemoMode();
        return;
      }

      // Otherwise, switch to demo mode
      this.demoMode = true;
      this.initializeDemoMode();
      return;
    }

    const sections = this.config.wheelSections;
    const anglePerSection = (2 * Math.PI) / sections.length;
    const wheelDesign = this.config.wheelDesign || {
      borderColor: '#000',
      borderWidth: 2,
    };

    // Draw a basic wheel background
    this.ctx.save();
    this.ctx.beginPath();
    this.ctx.arc(this.wheelCenterX, this.wheelCenterY, this.wheelRadius, 0, 2 * Math.PI);
    this.ctx.fillStyle = '#333';
    this.ctx.fill();
    this.ctx.restore();

    // Draw each section
    sections.forEach((section, index) => {
      this.ctx.save();
      this.ctx.beginPath();
      this.ctx.moveTo(this.wheelCenterX, this.wheelCenterY);
      this.ctx.arc(
        this.wheelCenterX,
        this.wheelCenterY,
        this.wheelRadius,
        index * anglePerSection + this.rotationAngle,
        (index + 1) * anglePerSection + this.rotationAngle
      );
      this.ctx.closePath();

      this.ctx.fillStyle = section.backgroundColor;
      this.ctx.fill();

      this.ctx.strokeStyle = wheelDesign.borderColor || '#000';
      this.ctx.lineWidth = wheelDesign.borderWidth || 2;
      this.ctx.stroke();

      this.ctx.save();
      this.ctx.translate(this.wheelCenterX, this.wheelCenterY);
      this.ctx.rotate(
        index * anglePerSection + anglePerSection / 2 + this.rotationAngle
      );
      this.ctx.textAlign = 'center';
      this.ctx.fillStyle = '#fff';
      this.ctx.font = 'bold 14px Arial';
      this.ctx.fillText(section.label, this.wheelRadius - 60, 0);
      this.ctx.restore();
    });

    if (this.pointerImage && this.pointerImageLoaded) {
      const pointerSize = 40;
      this.ctx.save();
      this.ctx.translate(this.wheelCenterX, 20);
      this.ctx.rotate(Math.PI);
      this.ctx.drawImage(
        this.pointerImage,
        -pointerSize / 2,
        -pointerSize / 2,
        pointerSize,
        pointerSize
      );
      this.ctx.restore();
    }

    if (this.centerImage && this.centerImageLoaded) {
      const imageSize = 60;
      this.ctx.drawImage(
        this.centerImage,
        this.wheelCenterX - imageSize / 2,
        this.wheelCenterY - imageSize / 2,
        imageSize,
        imageSize
      );
    }
  }

  private updateNextSpinTimer() {
    if (this.nextSpinTime) {
      this.updateTimer = setInterval(() => {
        const now = new Date();
        const diff = this.nextSpinTime!.getTime() - now.getTime();

        if (diff <= 0) {
          this.timeUntilNextSpin = '';
          this.loadSpinFrequency();
          clearInterval(this.updateTimer);
        } else {
          const hours = Math.floor(diff / (1000 * 60 * 60));
          const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
          const seconds = Math.floor((diff % (1000 * 60)) / 1000);

          this.timeUntilNextSpin = `${hours}h ${minutes}m ${seconds}s`;
        }
      }, 1000);
    }
  }

  private determineReward() {
    console.log('Determining reward...');
    if (!this.config || !this.config.wheelSections || !Array.isArray(this.config.wheelSections) || this.config.wheelSections.length === 0) {
      console.error('Cannot determine reward: wheelSections is missing or not an array');

      // If we're already in demo mode but still having issues, reinitialize
      if (this.demoMode) {
        this.initializeDemoMode();
      } else {
        // Switch to demo mode
        this.demoMode = true;
        this.initializeDemoMode();
      }
      return;
    }

    const sections = this.config.wheelSections;
    const anglePerSection = (2 * Math.PI) / sections.length;
    let normalizedAngle =
      (this.rotationAngle + anglePerSection / 2) % (2 * Math.PI);
    if (normalizedAngle < 0) normalizedAngle += 2 * Math.PI;

    const sectionIndex =
      (sections.length - Math.floor(normalizedAngle / anglePerSection) - 1) %
      sections.length;

    // Make sure the section exists
    if (sectionIndex < 0 || sectionIndex >= sections.length) {
      console.error('Invalid section index:', sectionIndex);
      this.currentReward = 'Prize';
      return;
    }

    const wonSection = sections[sectionIndex];
    this.currentReward = wonSection.label || 'Prize';

    console.log('Reward determined:', {
      sectionIndex,
      reward: wonSection,
      currentAngle: this.rotationAngle,
      normalizedAngle,
    });

    // Emit game event with the reward
    this.emitGameEvent('reward', {
      reward: wonSection.label,
      value: wonSection.value,
      sectionIndex
    });

    if (this.demoMode) {
      // In demo mode, just show the reward animation
      setTimeout(() => {
        this.showRewardAnimation = true;
        this.drawWheel();
      }, 500);
      return;
    }

    // In normal mode, update the progress via API
    const numericGameId = parseInt(this.gameId, 10);
    const progress: Partial<GameProgressType<SpinWheelProgress>> = {
      highScore: Math.max(this.gameProgress?.highScore || 0, wonSection.value),
      gameSpecificProgress: {
        ...this.gameProgress?.gameSpecificProgress,
        totalPointsWon:
          (this.gameProgress?.gameSpecificProgress?.totalPointsWon || 0) +
          wonSection.value,
        totalSpins: this.gameProgress?.gameSpecificProgress?.totalSpins || 0,
        lastSpinTime: new Date().toISOString(),
        spinsToday: this.gameProgress?.gameSpecificProgress?.spinsToday || 0,
      },
    };

    console.log('Updating progress with reward:', progress);
    this.gameService
      .updateGameProgress<SpinWheelProgress>(numericGameId, progress)
      .subscribe({
        next: (updatedProgress) => {
          console.log('Reward progress updated:', updatedProgress);
          this.gameProgress = updatedProgress;

          setTimeout(() => {
            this.showRewardAnimation = true;
            this.drawWheel();
          }, 500);
        },
        error: (error) => {
          console.error('Error updating progress:', error);
          // Still show the reward animation even if the API call fails
          setTimeout(() => {
            this.showRewardAnimation = true;
            this.drawWheel();
          }, 500);
        }
      });
  }

  dismissReward() {
    this.zone.run(() => {
      this.showRewardAnimation = false;
    });
  }

  // Update the spin method to add some excitement
  spin() {
    if (this.isSpinning || !this.config || this.remainingSpins <= 0) {
      return;
    }

    this.isSpinning = true;
    this.stopRequested = false;
    this.spinSpeed = this.config.maxSpinSpeed || 0.3; // Use default if not defined
    this.remainingSpins--;

    // Emit game event for spin start
    this.emitGameEvent('spin', {
      remainingSpins: this.remainingSpins
    });

    // Allow stopping after 1 second
    setTimeout(() => {
      this.canStop = true;
    }, 1000);

    if (this.demoMode) {
      // In demo mode, just start the animation
      this.animate();
      return;
    }

    // In normal mode, update the progress via API
    const numericGameId = parseInt(this.gameId, 10);
    const progress: Partial<GameProgressType<SpinWheelProgress>> = {
      lastPlayed: new Date(),
      attemptsRemaining: this.remainingSpins,
      gameSpecificProgress: {
        totalSpins:
          (this.gameProgress?.gameSpecificProgress?.totalSpins || 0) + 1,
        lastSpinTime: new Date().toISOString(),
        spinsToday:
          (this.gameProgress?.gameSpecificProgress?.spinsToday || 0) + 1,
        totalPointsWon:
          this.gameProgress?.gameSpecificProgress?.totalPointsWon || 0,
      },
    };

    this.gameService
      .updateGameProgress<SpinWheelProgress>(numericGameId, progress)
      .subscribe({
        next: (updatedProgress) => {
          this.gameProgress = updatedProgress;
          this.animate();
        },
        error: (error) => {
          console.error('Error updating progress:', error);
          // Still start the animation even if the API call fails
          this.animate();
        }
      });
  }

  // Add stop method
  stopSpin() {
    if (!this.canStop || this.stopRequested) return;

    this.stopRequested = true;
    this.canStop = false;
    // Use slower deceleration for stopping
    this.deceleration = this.STOP_DECELERATION;
  }

  // Update animate method
  private animate() {
    if (!this.config) return;

    this.rotationAngle += this.spinSpeed;
    this.spinSpeed *= this.deceleration;

    this.drawWheel();

    // Use lower minimum speed threshold
    // Make sure minSpinSpeed exists, otherwise use a default value
    const minSpinSpeed = this.config.minSpinSpeed || 0.01;

    if (
      this.spinSpeed >
      (this.stopRequested ? this.MIN_SPEED : minSpinSpeed)
    ) {
      requestAnimationFrame(() => this.animate());
    } else {
      this.isSpinning = false;
      this.canStop = false;
      this.stopRequested = false;
      this.deceleration = 0.99; // Reset to original deceleration
      this.determineReward();
    }
  }

  private loadSpinFrequency() {
    console.log('Loading spin frequency...');

    if (this.demoMode) {
      // In demo mode, just set default values
      this.remainingSpins = 3;
      return;
    }

    const numericGameId = parseInt(this.gameId, 10);
    this.gameService
      .getGameProgress<SpinWheelProgress>(numericGameId)
      .subscribe({
        next: (progress) => {
          console.log('Spin frequency progress:', progress);
          if (progress) {
            this.gameProgress = progress;
            this.remainingSpins = progress.attemptsRemaining;
            console.log('Updated remaining spins:', this.remainingSpins);

            if (progress.lastPlayed) {
              const lastPlayed = new Date(progress.lastPlayed);
              const now = new Date();
              console.log('Last played:', lastPlayed);
              console.log('Current time:', now);

              if (
                lastPlayed.getDate() !== now.getDate() ||
                lastPlayed.getMonth() !== now.getMonth() ||
                lastPlayed.getFullYear() !== now.getFullYear()
              ) {
                console.log('Resetting progress due to new day');
                this.gameService
                  .resetGameProgress(numericGameId)
                  .subscribe({
                    next: (resetProgress) => {
                      console.log('Reset progress:', resetProgress);
                      this.gameProgress =
                        resetProgress as GameProgressType<SpinWheelProgress>;
                      this.remainingSpins = resetProgress.attemptsRemaining;
                      this.nextSpinTime = null;
                      this.timeUntilNextSpin = '';
                    },
                    error: (error) => {
                      console.error('Error resetting progress:', error);
                    }
                  });
              }
            }
          }
        },
        error: (error) => {
          console.error('Error loading spin frequency:', error);
          this.demoMode = true;
          this.remainingSpins = 3; // Default in demo mode
        }
      });
  }

  // Add method to emit game events
  emitGameEvent(eventType: string, data: any = {}) {
    // Create a GameEvent object according to the interface
    const gameEvent: GameEvent = {
      id: 0, // This will be set by the backend
      score: data.value || 0,
      level: 1,
      duration: 0, // Calculate duration if needed
      state: eventType, // Use the event type as state
      payload: JSON.stringify({
        ...data,
        demoMode: this.demoMode,
        remainingSpins: this.remainingSpins,
        gameId: this.gameId
      })
    };

    this.gameEvent.emit(gameEvent);
  }
}
