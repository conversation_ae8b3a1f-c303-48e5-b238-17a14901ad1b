.game-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  min-height: 100vh;
  background: linear-gradient(
    135deg,
    #1a1a2e 0%,
    #16213e 50%,
    #0f3460 100%
  );
  background-image: var(--wheel-game-bg);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
}

/* Add an overlay to ensure text remains readable */
.game-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 0;
}

/* Ensure content stays above overlay */
.game-container > * {
  position: relative;
  z-index: 1;
}

.header {
  text-align: center;
  color: white;
  margin-bottom: 2rem;
  width: 100%;
}

.title {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  font-weight: bold;
}

.spins-remaining {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 1.2rem;
}

.spin-remaining {
  font-size: 0.8rem;
  margin-top: 0.25rem;
  opacity: 0.8;
}

.wheel-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
}

/* Wheel Pointer */
.wheel-pointer {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  width: 30px;
  height: 40px;
  display: flex;
  justify-content: center;
}

.pointer-triangle {
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-top: 25px solid #f59e0b;
  filter: drop-shadow(0 0 10px rgba(245, 158, 11, 0.7));
  animation: pointerPulse 1.5s infinite;
}

@keyframes pointerPulse {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 10px rgba(245, 158, 11, 0.7));
  }
  50% {
    transform: scale(1.1);
    filter: drop-shadow(0 0 15px rgba(245, 158, 11, 0.9));
  }
}

.controls {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.spin-button,
.stop-button {
  padding: 2rem 1rem;
  border-radius: 9999px;
  font-size: 1.25rem;
  font-weight: bold;
  transition: all 0.2s;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  min-width: 120px;
  backdrop-filter: blur(8px);
}

.spin-button {
  background-color: rgba(249, 115, 22, 0.9);
  color: white;
  border: 2px solid white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.spin-button:hover {
  background-color: rgb(234, 88, 12);
}

.stop-button {
  background-color: rgba(239, 68, 68, 0.9);
  color: white;
  border: 2px solid white;
  animation: pulse 1.5s infinite;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.stop-button:hover {
  background-color: rgb(220, 38, 38);
}

.stop-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

/* Add glow effect for buttons */
.spin-button,
.stop-button {
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.spin-button:hover,
.stop-button:hover {
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

/* Game Popup Overlay - Consistent with other games */
.game-popup-overlay {
  display: flex;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999; /* Increased z-index to ensure it's above everything */
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease-out;
}

.game-popup-dialog {
  padding: 2rem;
  text-align: center;
  background: linear-gradient(to bottom, #1e293b, #0f172a);
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transform: translateZ(0);
  border: 1px solid #334155;
  max-width: 28rem;
  width: 91.666667%;
  animation: scaleIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative; /* Ensure proper stacking context */
}

/* Mascot Container */
.mascot-container {
  margin-bottom: 1rem;
  position: relative;
}

.mascot-glow {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(to bottom, rgba(245, 158, 11, 0.2), transparent);
  border-radius: 9999px;
  filter: blur(15px);
}

.mascot-bounce {
  margin: 0 auto;
  width: 6rem;
  height: 6rem;
  position: relative;
  z-index: 10;
  animation: bounce 2s ease-in-out infinite;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.2));
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Trophy Icon */
.trophy-container {
  position: relative;
  display: inline-block;
  margin-bottom: 1rem;
}

.trophy-icon {
  font-size: 4rem;
  color: #ffc409;
  filter: drop-shadow(0 0 15px rgba(255, 196, 9, 0.5));
  animation: pulse 2s infinite;
}

.reward-text {
  font-size: 1.5rem;
  color: #ffffff;
  margin: 1rem 0;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  background: linear-gradient(to right, #f59e0b, #fbbf24, #f59e0b);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  background-size: 200% auto;
  animation: textShine 3s linear infinite;
}

@keyframes textShine {
  0% {
    background-position: 0% center;
  }
  100% {
    background-position: 200% center;
  }
}

.dismiss-button {
  padding: 0.75rem 2rem;
  font-size: 1.25rem;
  font-weight: bold;
  color: white;
  border-radius: 9999px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  transform: translateZ(0);
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3), 0 0 15px rgba(59, 130, 246, 0.5);
  outline: none;
}

.dismiss-button:hover {
  transform: scale(1.05);
}

.dismiss-button:focus {
  outline: none;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5);
}

/* Confetti Effect */
.confetti-container {
  position: absolute;
  inset: 0;
  pointer-events: none;
  z-index: 100;
  overflow: hidden;
}

.confetti {
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: #ff6b6b;
  animation: confettiFall 3s linear infinite;
  top: -10px;
}

.confetti:nth-child(2n) {
  background-color: #3b82f6;
  animation-delay: 0.2s;
  left: 20%;
}

.confetti:nth-child(3n) {
  background-color: #10b981;
  animation-delay: 0.4s;
  left: 30%;
}

.confetti:nth-child(4n) {
  background-color: #f59e0b;
  animation-delay: 0.6s;
  left: 40%;
}

.confetti:nth-child(5n) {
  background-color: #8b5cf6;
  animation-delay: 0.8s;
  left: 50%;
}

.confetti:nth-child(6n) {
  background-color: #ec4899;
  animation-delay: 1s;
  left: 60%;
}

.confetti:nth-child(7n) {
  background-color: #06b6d4;
  animation-delay: 1.2s;
  left: 70%;
}

.confetti:nth-child(8n) {
  background-color: #84cc16;
  animation-delay: 1.4s;
  left: 80%;
}

.confetti:nth-child(9n) {
  background-color: #eab308;
  animation-delay: 1.6s;
  left: 90%;
}

.confetti:nth-child(10n) {
  background-color: #ef4444;
  animation-delay: 1.8s;
  left: 100%;
}

@keyframes confettiFall {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(1000px) rotate(720deg);
    opacity: 0;
  }
}

.dismiss-button:hover {
  opacity: 0.9;
}

.next-spin-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  margin-top: 1rem;
  background: rgba(0, 0, 0, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 1rem;
  backdrop-filter: blur(4px);
}

.demo-mode-indicator {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  font-weight: bold;
  padding: 0.25rem 0.75rem;
  border-radius: 0.5rem;
  margin-top: 0.5rem;
  font-size: 0.8rem;
  letter-spacing: 0.05em;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  animation: pulse 2s infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-popup {
  animation: popIn 0.3s ease-out forwards;
}

@keyframes popIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Animation for confetti */
@keyframes confettiFall {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(1000px) rotate(720deg);
    opacity: 0;
  }
}

/* Animation for pulse effect */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Popup title styling */
.popup-title {
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: bold;
  color: white;
  margin-bottom: 0.5rem;
}
