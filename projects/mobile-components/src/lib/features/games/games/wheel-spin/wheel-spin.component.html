<div class="game-container">
  <div class="header">
    <h2 class="title">Spin the Wheel!</h2>
    <div class="spins-remaining">
      <ion-icon name="refresh-outline"></ion-icon>
      Remaining Spins: {{ remainingSpins }}
    </div>
    <div *ngIf="demoMode" class="demo-mode-indicator">
      DEMO MODE
    </div>
  </div>

  <div class="wheel-container">
    <canvas #wheelCanvas></canvas>

    <!-- Wheel Pointer -->
    <div class="wheel-pointer">
      <div class="pointer-triangle"></div>
    </div>

    <div class="controls">
      <button
        *ngIf="!isSpinning && remainingSpins > 0"
        (click)="spin()"
        class="spin-button"
      >
        SPIN!
        <div class="spin-remaining">
          {{ remainingSpins }} spins left
        </div>
      </button>

      <button
        *ngIf="isSpinning && canStop"
        (click)="stopSpin()"
        class="stop-button"
        [disabled]="stopRequested"
      >
        STOP!
      </button>
    </div>
  </div>

  <!-- Next Spin Timer -->
  <div class="next-spin-info" *ngIf="nextSpinTime">
    <ion-icon name="time-outline"></ion-icon>
    Next spin available in: {{ timeUntilNextSpin }}
  </div>
</div>

<!-- Reward Animation Container - Moved outside game-container -->
<div class="game-popup-overlay" *ngIf="showRewardAnimation">
  <div class="game-popup-dialog animate-popup">
    <!-- Mascot -->
    <div class="mascot-container">
      <div class="mascot-glow"></div>
      <img src="assets/images/games/mascot.png" alt="Game Mascot" class="mascot-bounce">
    </div>

    <!-- Trophy Icon with Glow -->
    <div class="trophy-container">
      <ion-icon name="trophy-outline" class="trophy-icon"></ion-icon>
    </div>

    <h2 class="popup-title">Congratulations!</h2>
    <p class="reward-text">You won {{ currentReward }}!</p>

    <!-- Confetti Effect -->
    <div class="confetti-container">
      <div class="confetti" *ngFor="let i of [1,2,3,4,5,6,7,8,9,10]"></div>
    </div>

    <button (click)="dismissReward()" class="dismiss-button">Continue</button>
  </div>
</div>
