<div
  class="flex sticky top-0 z-50 justify-between items-center p-2 shadow-2xl"
  [ngClass]="getBgColorClass()"
>
  <button class="p-2 text-white rounded-full" (click)="goBack()">
    <ion-icon name="arrow-back-outline" class="text-xl"></ion-icon>
  </button>
  <div class="flex items-center space-x-1">
    <a
      [routerLink]="['/public/games/single']"
      [queryParams]="{ game: 'spin-the-wheel' }"
    >
      <ion-icon
        name="refresh-circle"
        class="text-2xl text-yellow-300 cursor-pointer"
      ></ion-icon>
    </a>
    <span class="text-xs text-white">SPIN!</span>
  </div>
  <div class="relative">
    <a
      [routerLink]="['/public/games/single']"
      [queryParams]="{ game: 'location-based-photo' }"
    >
      <!-- <div class="p-2 text-xs text-white bg-blue-500 rounded-full">10</div> -->
      <ion-icon
        name="camera"
        class="absolute -right-1 -bottom-1 text-lg text-pink-500"
      ></ion-icon>
    </a>
  </div>
  <div class="flex items-center px-2 py-1 bg-yellow-300 rounded-full">
    <ion-icon name="cash" class="mr-1 text-yellow-600"></ion-icon>
    <span class="text-xs">50 FREE</span>
  </div>
  <div class="flex items-center">
    <span class="mr-1 text-white">106</span>
    <ion-icon name="diamond" class="mr-1 text-yellow-300"></ion-icon>
    <button
      class="
        flex
        justify-center
        items-center
        w-5
        h-5
        text-xs text-white
        bg-green-500
        rounded-full
      "
    >
      +
    </button>
  </div>
</div>
