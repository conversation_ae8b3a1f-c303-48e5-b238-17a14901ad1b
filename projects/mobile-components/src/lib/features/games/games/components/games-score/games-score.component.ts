import { Component, Input, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';

/**
 * Games Score Component
 *
 * Displays game scores in a consistent format across all games.
 * Uses the shared game-styles.css for styling.
 *
 * Score titles are used to determine the color of the score:
 * - Score: green
 * - Time/Speed: amber
 * - Level: blue
 * - Moves/Matches: purple
 * - Attempts/Remaining: red
 * - High: yellow
 * - Progress: teal
 */
@Component({
  selector: 'games-score',
  templateUrl: './games-score.component.html',
  styleUrls: ['./games-score.component.css'],
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class GamesScoreComponent {
  @Input() scores: { title: string; number: any }[] = [];
}
