import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'games-name',
  templateUrl: './games-name.component.html',
  styleUrls: ['./games-name.component.css'],
  standalone: true,
  imports: [CommonModule, RouterModule]
})
export class GamesNameComponent implements OnInit {
  selectedGame: string = ''; // Change this line
  showAnimation: boolean = false;

  constructor(private route: ActivatedRoute, private router: Router) {}
  
  ngOnInit() {
    this.route.queryParams.subscribe((params) => {
      console.log('Query params:', params);
      const game = params['game'];
      console.log('Game:', game);
      this.selectedGame = game;
      console.log('Selected game:', this.selectedGame);
      this.showAnimation = true;
    });

    setTimeout(() => {
      this.showAnimation = false;
    }, 5000);
  }

  getTitleLetterClass(index: number): string {
    const colors = [
      'bg-purple-500 border-b-4 border-purple-700 shadow-lg',
      'bg-pink-500 border-b-4 border-pink-700 shadow-lg',
      'bg-blue-500 border-b-4 border-blue-700 shadow-lg',
      'bg-green-500 border-b-4 border-green-700 shadow-lg',
      'bg-yellow-500 border-b-4 border-yellow-700 shadow-lg',
      'bg-red-500 border-b-4 border-red-700 shadow-lg',
      'bg-orange-500 border-b-4 border-orange-700 shadow-lg',
    ];
    return `${
      colors[index % colors.length]
    } text-white rounded-full px-3 py-1 mx-1`;
  }

  onGameChange(event: Event) {
    const selectedValue = (event.target as HTMLSelectElement).value;
    if (selectedValue) {
      this.router.navigate([], {
        relativeTo: this.route,
        queryParams: { game: selectedValue },
      });
    }
  }

  getTitleClass(): { [key: string]: boolean } {
    return {
      'text-xl': this.selectedGame.length > 6,
    };
  }
}
