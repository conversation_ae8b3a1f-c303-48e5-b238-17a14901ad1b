<div class="flex justify-around gap-2 my-3">
  <button
    *ngFor="let control of controls"
    (click)="onControlClick(control.name)"
    class="p-3 text-white rounded-full shadow-lg flex items-center justify-center transition-all hover:scale-110"
    [ngClass]="control.color ? 'bg-gradient-to-br from-' + control.color + '-400 to-' + control.color + '-600' : 'bg-gradient-to-br from-orange-400 to-orange-600'"
    style="min-width: 100px; min-height: 44px;"
  >
    <!-- Icon with improved styling -->
    <ion-icon [name]="control.icon" *ngIf="control.icon" class="text-2xl mr-1"></ion-icon>
    <span>{{ control.label || control.name }}</span>
  </button>
</div>
