import { Component, Input } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-games-card',
  templateUrl: './games-card.component.html',
  styleUrls: ['./games-card.component.css'],
  standalone: true,
  imports: [CommonModule, RouterModule]
})
export class GamesCardComponent {
  @Input() game: any; // Replace 'any' with a proper Game interface
  @Input() bgColor: string = 'orange';
  @Input() borderColor: string = 'yellow';
  @Input() textColor: string = 'green';
  @Input() buttonColor: string = 'red';

  constructor(private router: Router) {}

  navigateToGame(gameName: string) {
    this.router.navigate(['/public/games/dashboard'], {
      queryParams: { game: gameName },
    });
  }
}
