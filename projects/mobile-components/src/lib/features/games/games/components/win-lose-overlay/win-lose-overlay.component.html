<div class="game-popup-overlay" *ngIf="status">
  <div class="game-popup-dialog">
    <!-- Mascot -->
    <div class="mascot-container">
      <div class="absolute inset-0 bg-gradient-to-b from-amber-500/20 to-transparent rounded-full blur-xl"></div>
      <img src="assets/images/games/mascot.png" alt="Game Mascot" class="mx-auto w-24 h-24 mascot-bounce relative z-10">
    </div>

    <!-- Result Message -->
    <h2 class="game-over-title" [ngStyle]="{'background': status === 'win' ? 'linear-gradient(to right, #10b981, #34d399, #10b981)' : 'linear-gradient(to right, #ef4444, #f97316, #ef4444)'}">
      {{ getMessage() }}
    </h2>

    <!-- Message -->
    <div *ngIf="message" class="mb-6 p-4 bg-slate-700/50 rounded-xl border border-slate-600 text-slate-300">
      <p>{{ message }}</p>
    </div>

    <!-- Action Buttons -->
    <button
      (click)="onRestart()"
      class="game-action-button game-action-button-primary">
      Play Again
    </button>
  </div>
</div>
