import { Component, Input } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';

interface Category {
  name: string;
  icon: string;
}

@Component({
  selector: 'app-games-card-small',
  templateUrl: './games-card-small.component.html',
  styleUrls: ['./games-card-small.component.css'],
  standalone: true,
  imports: [CommonModule, RouterModule]
})
export class GamesCardSmallComponent {
  @Input() category!: Category;
  @Input() bgColor!: string;
  @Input() hoverBgColor!: string;
  @Input() textColor!: string;
  
  constructor(private router: Router) {}
  
  navigateToCategory(categoryName: string) {
    this.router.navigate(['/public/games/categories'], {
      queryParams: { category: categoryName },
    });
  }
}
