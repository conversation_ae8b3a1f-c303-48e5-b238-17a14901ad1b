<div class="flex transition-transform duration-300 ease-in-out" #gameCarousel>
  <div class="flex-shrink-0 px-2 w-full shadow-lg">
    <div
      class="
        overflow-hidden
        bg-white
        rounded-lg
        border-2
        shadow-lg
        transition-colors
        duration-300
      "
    >
      <img [src]="game.image" [alt]="game.name" class="p-2 w-full h-48" />
      <div
        class="p-4"
        [ngClass]="[
          'bg-' + bgColor + '-500',
          'border-' + borderColor + '-100',
          'hover:border-' + borderColor + '-500'
        ]"
      >
        <h3
          class="mb-2 text-2xl font-bold"
          [ngClass]="'text-' + textColor + '-400'"
        >
          {{ game.name }}
        </h3>
        <p class="mb-4 text-gray-300">{{ game.description }}</p>
        <button
          class="
            px-6
            py-3
            text-lg
            font-bold
            text-white
            rounded-full
            shadow-md
            transition
            transform
            hover:shadow-lg hover:scale-105
          "
          [ngClass]="[
            'bg-' + buttonColor + '-500',
            'hover:bg-' + buttonColor + '-600'
          ]"
          (click)="navigateToGame(game.name)"
        >
          Play Now
        </button>
      </div>
    </div>
  </div>
</div>
