import { Component, Input } from '@angular/core';
import { Location, CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'games-header',
  templateUrl: './games-header.component.html',
  styleUrls: ['./games-header.component.css'],
  standalone: true,
  imports: [CommonModule, RouterModule, IonicModule]
})
export class GamesHeaderComponent {
  @Input() bgColor?: string;
  
  constructor(private location: Location) {}
  
  goBack() {
    this.location.back();
  }
  
  getBgColorClass(): string {
    const validColors = [
      'orange',
      'blue',
      'navy',
      'red',
      'green',
      'yellow',
      'purple',
      'pink',
      'indigo',
      'teal',
    ];
    return validColors.includes(this.bgColor?.toLowerCase() || '')
      ? `bg-${this.bgColor?.toLowerCase()}-500`
      : 'bg-orange-500';
  }
}
