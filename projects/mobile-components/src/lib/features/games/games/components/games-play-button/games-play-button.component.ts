import { Component, Input, Output, EventEmitter, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface GameControl {
  name: string;
  label: string;
  icon?: string; // Optional: for icons if needed
  action?: () => void; // Optional: for custom actions if needed
  color?: string; // Optional: for custom colors if needed
}

@Component({
  selector: 'lib-games-play-button',
  templateUrl: './games-play-button.component.html',
  styleUrls: ['./games-play-button.component.css'],
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class GamesPlayButtonComponent {
  @Input() controls: GameControl[] = [];
  @Output() controlClicked = new EventEmitter<string>();

  onControlClick(controlName: string) {
    console.log('But<PERSON> clicked:', controlName);
    this.controlClicked.emit(controlName);
  }
}
