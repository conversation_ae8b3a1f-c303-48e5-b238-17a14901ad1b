/* Import shared game styles */
@import '../../styles/game-styles.css';

/* Score component specific styles - these override any conflicting styles from game-styles.css */
.games-score-title {
  @apply text-xs font-medium text-slate-400 mb-1;
}

.games-score-number {
  @apply text-base font-bold;
}

/* Score type specific colors - matching the stat-value colors in game-styles.css */
.score-type-Score {
  @apply text-green-400;
}

.score-type-Time, .score-type-Speed {
  @apply text-amber-400;
}

.score-type-Level {
  @apply text-blue-400;
}

.score-type-Moves, .score-type-Matches {
  @apply text-purple-400;
}

.score-type-Attempts, .score-type-Remaining {
  @apply text-red-400;
}

.score-type-High {
  @apply text-yellow-300;
}

.score-type-Progress {
  @apply text-teal-400;
}