import { Component, Input, Output, EventEmitter, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'lib-win-lose-overlay',
  templateUrl: './win-lose-overlay.component.html',
  styleUrls: ['./win-lose-overlay.component.css'],
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class WinLoseOverlayComponent {
  @Input() status: 'win' | 'lose' | null = null;
  @Input() message: string = '';
  @Output() restart = new EventEmitter<void>();

  getMessage(): string {
    if (this.status === 'win') {
      return 'Congratulations! You Won!';
    } else {
      return 'Game Over!';
    }
  }

  onRestart(): void {
    this.restart.emit();
  }

  onGoHome(): void {}
}
