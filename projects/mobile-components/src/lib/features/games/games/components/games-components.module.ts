import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';

// Import standalone components
import { GamesHeaderComponent } from './games-header/games-header.component';
import { GamesNameComponent } from './games-name/games-name.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    IonicModule,
    // Import standalone components
    GamesHeaderComponent,
    GamesNameComponent
  ],
  exports: [
    GamesHeaderComponent,
    GamesNameComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA] // Add schema for web components like ion-icon
})
export class GamesComponentsModule { }
