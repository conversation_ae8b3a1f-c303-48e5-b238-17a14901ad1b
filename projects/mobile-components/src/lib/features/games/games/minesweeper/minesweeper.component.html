<div class="game-container" [ngClass]="{'flag-mode-active': plantFlag}">
  <!-- Header with Scores -->
  <games-score
    [scores]="[
      { title: 'Score', number: score },
      { title: 'Level', number: level },
      { title: 'Moves', number: moveCount }
    ]"
  ></games-score>

  <!-- Game Title -->
  <div class="game-title">
    <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-indigo-500">Minesweeper</h1>
    <p class="text-xs text-slate-400 mb-1">Find all mines without triggering them!</p>
  </div>

  <!-- Game Board Container -->
  <div class="game-board-container">
    <div class="game-grid">
      <div class="grid">
        <div *ngFor="let row of board; let i = index" class="row">
          <div
            *ngFor="let cell of row; let j = index"
            (click)="onCellClick(i, j)"
            (contextmenu)="onRightClick($event, i, j)"
            class="cell"
            [ngClass]="{
              revealed: revealed[i][j],
              mine: revealed[i][j] && cell === -1,
              flagged: !revealed[i][j] && flagged[i][j]
            }"
          >
            <ng-container *ngIf="revealed[i][j]">
              <span
                *ngIf="cell > 0"
                class="number"
                [ngClass]="'number-' + cell"
                >{{ cell }}</span
              >
              <span *ngIf="cell === -1" class="text-xl">💥</span>
            </ng-container>
            <span *ngIf="!revealed[i][j] && flagged[i][j]" class="text-lg"
              >🚩</span
            >
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Game Controls -->
  <div class="controls">
    <lib-games-play-button
      [controls]="gameControls"
      (controlClicked)="handleControlClick($event)"
    ></lib-games-play-button>

    <!-- Flag Mode Indicator -->
    <div *ngIf="plantFlag" class="mt-2 text-center text-red-400 text-sm font-bold p-3 bg-slate-800/70 rounded-lg border-2 border-red-500/50 animate-pulse">
      <span class="inline-block mr-1 text-lg">🚩</span> FLAG MODE ACTIVE - Click cells to place flags
    </div>

    <!-- Flag Mode Instructions (always visible) -->
    <div *ngIf="!plantFlag" class="mt-2 text-center text-slate-300 text-xs p-2 bg-slate-800/30 rounded-lg">
      <span class="inline-block mr-1">💡</span> Tip: Use Flag Mode to mark potential mines
    </div>
  </div>

  <!-- Win/Lose Overlay -->
  <lib-win-lose-overlay
    [status]="gameResult"
    (restart)="dismissPopup()"
  ></lib-win-lose-overlay>
</div>
