/* Import shared game styles */
@import '../styles/game-styles.css';

:host {
  @apply block w-full h-full;
}

.game-container {
  @apply flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800;
}

.score-container {
  @apply w-full max-w-md mb-2 px-4 py-2;
}

/* Game Title */
.game-title {
  @apply text-center mb-1;
}

.game-title h1 {
  @apply text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-indigo-500;
}

.game-title p {
  @apply text-xs text-slate-400 mb-1;
}

.game-grid {
  @apply p-4 rounded-xl bg-slate-700/50 border border-slate-600 shadow-xl;
  min-width: min-content;
  max-width: 100%;
  overflow: auto;
  position: relative;
}

.game-grid::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-purple-500/10;
  filter: blur(20px);
  z-index: 0;
}

.grid {
  @apply flex flex-col gap-1 relative z-10;
}

.row {
  @apply flex gap-1;
}

.cell {
  @apply flex justify-center items-center
         text-lg font-semibold rounded-md
         transition-all duration-200 select-none shadow-md;
  width: 2.5rem;
  height: 2.5rem;
  min-width: 2.5rem;
  min-height: 2.5rem;
  position: relative;
  overflow: hidden;
}

.cell::before {
  content: '';
  @apply absolute inset-0 opacity-30;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
}

.cell:not(.revealed) {
  @apply bg-gradient-to-br from-blue-600 to-blue-800 hover:from-blue-500 hover:to-blue-700 active:scale-95 cursor-pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.cell.revealed {
  @apply bg-gradient-to-br from-slate-700 to-slate-800 border border-slate-600/50;
}

.cell.mine {
  @apply bg-gradient-to-br from-red-600 to-red-800 border border-red-500/50;
  animation: explode 0.5s ease-out;
  box-shadow: 0 0 15px theme("colors.red.500/50");
}

.cell.flagged {
  @apply bg-gradient-to-br from-yellow-600 to-yellow-800 border border-yellow-500/50;
  box-shadow: 0 0 10px theme("colors.yellow.500/30");
}

.number {
  @apply font-bold text-lg;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.number-1 {
  @apply text-blue-400;
}
.number-2 {
  @apply text-green-400;
}
.number-3 {
  @apply text-red-400;
}
.number-4 {
  @apply text-purple-400;
}
.number-5 {
  @apply text-yellow-400;
}
.number-6 {
  @apply text-pink-400;
}
.number-7 {
  @apply text-orange-400;
}
.number-8 {
  @apply text-gray-400;
}

.controls {
  @apply w-full max-w-md mt-4 px-4;
}

/* Game Board Container */
.game-board-container {
  @apply flex overflow-hidden flex-col flex-grow items-center p-2;
}

@keyframes explode {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
    box-shadow: 0 0 30px theme("colors.red.500/70");
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes flagPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.flag-mode-active .cell:not(.revealed):not(.flagged):hover {
  @apply bg-gradient-to-br from-yellow-600 to-yellow-800;
  animation: flagPulse 1s infinite;
}

@media (max-width: 640px) {
  .cell {
    width: 2rem;
    height: 2rem;
    min-width: 2rem;
    min-height: 2rem;
    @apply text-base;
  }

  .game-grid {
    @apply p-2;
  }

  .controls {
    @apply mt-2;
  }
}
