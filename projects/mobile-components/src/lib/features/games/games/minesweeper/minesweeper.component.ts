import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>D<PERSON><PERSON><PERSON><PERSON>, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, AfterViewInit } from '@angular/core';
import { NgIf, NgFor, NgClass,  CommonModule } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { WinLoseOverlayComponent } from '../components/win-lose-overlay/win-lose-overlay.component';
import { GameService } from 'lp-client-api';
import { Game, MinesweeperConfig, GameProgress } from 'lp-client-api';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { GameControl, GamesPlayButtonComponent } from '../components/games-play-button/games-play-button.component';

interface MinesweeperParticipation {
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  frequencyAttempts: number;
  lastParticipation: Date;
}

interface GameBonus {
  type: 'PERFECT_GAME' | 'SPEED_MASTER' | 'MINE_MASTER';
  points: number;
}

@Component({
  selector: 'lib-minesweeper',
  templateUrl: './minesweeper.component.html',
  styleUrls: ['./minesweeper.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    NgIf,
    NgFor,
    NgClass,
    GamesPlayButtonComponent,
    GamesScoreComponent,
    WinLoseOverlayComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class MinesweeperComponent implements OnInit, OnDestroy, AfterViewInit {
  private destroy$ = new Subject<void>();

  // Game state
  gameId = 395; // Minesweeper game ID
  config: MinesweeperConfig | undefined;
  gameProgress: GameProgress | undefined;
  canPlay: boolean = false;
  attemptsRemaining: number = 0;

  // Game metrics
  rows: number = 5;
  cols: number = 5;
  mines: number = 5;
  board: number[][] = [];
  revealed: boolean[][] = [];
  flagged: boolean[][] = [];
  score: number = 0;
  highScore: number = 0;
  level: number = 1;
  moveCount: number = 0;
  gameStarted: boolean = false;
  gameOver: boolean = false;
  startTime: number = 0;
  plantFlag: boolean = false;

  // Difficulty settings
  difficultySettings = {
    EASY: {
      width: 8,
      height: 8,
      mines: 10,
      pointMultiplier: 1,
    },
    MEDIUM: {
      width: 12,
      height: 12,
      mines: 20,
      pointMultiplier: 2,
    },
    HARD: {
      width: 16,
      height: 16,
      mines: 40,
      pointMultiplier: 3,
    },
  };

  // Default configuration
  private readonly DEFAULT_CONFIG: MinesweeperConfig = {
    id: 0,
    difficulty: 'EASY',
    frequency: 'DAILY',
    frequencyAttempts: 3,
    width: 8,
    height: 8,
    mines: 10,
  };

  gameControls: GameControl[] = [
    {
      name: 'plantFlag',
      label: 'Flag Mode',
      icon: 'flag-outline',
      color: 'yellow'
    },
    {
      name: 'reset',
      label: 'Reset Game',
      icon: 'refresh-outline',
      color: 'blue'
    }
  ];

  // Initialize these controls in ngOnInit to ensure they're properly set up
  private initializeGameControls(): void {
    this.gameControls = [
      {
        name: 'plantFlag',
        label: 'Flag Mode',
        icon: 'flag-outline',
        color: 'yellow'
      },
      {
        name: 'reset',
        label: 'Reset Game',
        icon: 'refresh-outline',
        color: 'blue'
      }
    ];
    console.log('Game controls initialized:', this.gameControls);
  }

  // Add missing properties
  win: boolean = false;
  gameResult: 'win' | 'lose' | null = null;

  constructor(
    private gameService: GameService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    console.log('Minesweeper component initializing...');

    // Initialize game controls
    this.initializeGameControls();

    // Initialize with default values first to ensure the board is created
    this.initializeGame();

    // Then load the game config
    this.loadGameConfig();
  }

  ngAfterViewInit(): void {
    console.log('Minesweeper component view initialized');
    // Force change detection to update the view
    this.cdr.detectChanges();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadGameConfig(): void {
    this.gameService
      .getGameById(this.gameId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (game) => {
          if (game?.gameConfig[0]) {
            this.config = game.gameConfig[0] as MinesweeperConfig;
            this.applyDifficultySettings(this.config.difficulty);
            this.loadGameProgress();
          }
        },
        error: (error) => {
          console.error('Error loading game config:', error);
          this.config = this.DEFAULT_CONFIG;
          this.applyDifficultySettings('EASY');
          this.loadGameProgress();
        },
      });
  }

  private loadGameProgress(): void {
    this.gameService
      .checkGameAvailability(this.gameId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (canPlay) => {
          this.canPlay = canPlay;
          if (canPlay) {
            this.loadProgress();
          }
        },
        error: (error) => {
          console.error('Error checking game availability:', error);
        },
      });
  }

  private loadProgress(): void {
    this.gameService
      .getGameProgress(this.gameId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (progress) => {
          if (progress) {
            this.gameProgress = progress;
            this.highScore = progress.highScore;
            this.attemptsRemaining = progress.attemptsRemaining;
            this.level = progress.currentLevel;
          }
          this.initializeGame();
        },
        error: (error) => {
          console.error('Error loading game progress:', error);
          this.initializeGame();
        },
      });
  }

  private applyDifficultySettings(difficulty: string): void {
    const settings =
      this.difficultySettings[
        difficulty as keyof typeof this.difficultySettings
      ];
    if (settings) {
      this.config = {
        ...this.config,
        width: settings.width,
        height: settings.height,
        mines: settings.mines,
      } as MinesweeperConfig;
    }
  }

  private calculateBonuses(): GameBonus[] {
    const bonuses: GameBonus[] = [];
    const timeElapsed = (Date.now() - this.startTime) / 1000;
    const revealedCount = this.getRevealedCount();

    // Perfect game bonus (no wrong flags)
    if (this.getFlaggedMineCount() === this.mines) {
      bonuses.push({
        type: 'PERFECT_GAME',
        points: Math.floor(this.score * 0.2),
      });
    }

    // Speed bonus
    if (timeElapsed < this.getTimeLimit()) {
      bonuses.push({
        type: 'SPEED_MASTER',
        points: Math.floor(this.score * 0.15),
      });
    }

    // Efficiency bonus
    if (this.moveCount < this.rows * this.cols * 0.4) {
      bonuses.push({
        type: 'MINE_MASTER',
        points: Math.floor(this.score * 0.25),
      });
    }

    return bonuses;
  }

  initializeGame(): void {
    // Initialize game state based on config or default values
    this.rows = this.config?.height || 8;
    this.cols = this.config?.width || 8;
    this.mines = this.config?.mines || 10;

    console.log('Initializing Minesweeper game with:', {
      rows: this.rows,
      cols: this.cols,
      mines: this.mines
    });

    // Create board
    this.board = this.createBoard();
    this.revealed = Array(this.rows)
      .fill(null)
      .map(() => Array(this.cols).fill(false));
    this.flagged = Array(this.rows)
      .fill(null)
      .map(() => Array(this.cols).fill(false));

    // Reset game state
    this.gameOver = false;
    this.win = false;
    this.moveCount = 0;
    this.plantFlag = false;
    this.gameResult = null;
    this.gameStarted = false;
    this.score = 0;

    // Force change detection to update the view
    this.cdr.detectChanges();
  }

  private createBoard(): number[][] {
    // Create empty board
    const board = Array(this.rows)
      .fill(null)
      .map(() => Array(this.cols).fill(0));

    // Place mines randomly
    let minesPlaced = 0;
    while (minesPlaced < this.mines) {
      const row = Math.floor(Math.random() * this.rows);
      const col = Math.floor(Math.random() * this.cols);

      if (board[row][col] !== -1) {
        board[row][col] = -1;
        minesPlaced++;

        // Update adjacent cell counts
        this.updateAdjacentCells(board, row, col);
      }
    }

    return board;
  }

  private updateAdjacentCells(
    board: number[][],
    row: number,
    col: number
  ): void {
    for (let i = -1; i <= 1; i++) {
      for (let j = -1; j <= 1; j++) {
        const newRow = row + i;
        const newCol = col + j;

        if (
          newRow >= 0 &&
          newRow < this.rows &&
          newCol >= 0 &&
          newCol < this.cols &&
          board[newRow][newCol] !== -1
        ) {
          board[newRow][newCol]++;
        }
      }
    }
  }

  nextLevel(): void {
    this.level++;
    if (this.config) {
      // Increase difficulty for next level
      this.mines = Math.min(
        this.mines + 2,
        Math.floor(this.rows * this.cols * 0.3)
      );
    }
    this.initializeGame();
  }

  resetGame(): void {
    this.level = 1;
    this.score = 0;

    // Initialize the game immediately to ensure the board is visible
    this.initializeGame();

    // Then reset the game progress in the background
    this.gameService
      .resetGameProgress(this.gameId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (resetProgress) => {
          this.gameProgress = resetProgress;
          console.log('Game progress reset successfully');
        },
        error: (error) => {
          console.error('Error resetting game progress:', error);
        }
      });
  }

  // Helper method to reveal all mines on the board
  private revealAllMines(): void {
    for (let i = 0; i < this.rows; i++) {
      for (let j = 0; j < this.cols; j++) {
        // Reveal only mines that haven't been revealed yet
        if (this.board[i][j] === -1 && !this.revealed[i][j]) {
          this.revealed[i][j] = true;
        }
      }
    }
    // Force change detection to update the view
    this.cdr.detectChanges();
  }

  // Helper method to reveal a cell
  revealCell(row: number, col: number): void {
    if (this.gameOver || this.revealed[row][col] || this.flagged[row][col])
      return;

    this.revealed[row][col] = true;
    this.moveCount++;

    if (this.board[row][col] === -1) {
      // Hit a mine
      this.gameOver = true;
      this.win = false;
      this.gameResult = 'lose';
      // Reveal all mines when game is over
      this.revealAllMines();
      this.updateProgress();
      return;
    }

    if (this.board[row][col] === 0) {
      // Reveal adjacent cells for empty cell
      this.revealAdjacentCells(row, col);
    }

    this.checkWin();
  }

  private revealAdjacentCells(row: number, col: number): void {
    for (let i = -1; i <= 1; i++) {
      for (let j = -1; j <= 1; j++) {
        const newRow = row + i;
        const newCol = col + j;

        if (
          newRow >= 0 &&
          newRow < this.rows &&
          newCol >= 0 &&
          newCol < this.cols &&
          !this.revealed[newRow][newCol] &&
          !this.flagged[newRow][newCol]
        ) {
          this.revealCell(newRow, newCol);
        }
      }
    }
  }

  private updateGameProgress() {
    if (!this.gameProgress) return;

    const progress: Partial<GameProgress> = {
      highScore: Math.max(this.score, this.gameProgress.highScore),
      currentLevel: this.level,
      gameSpecificProgress: {
        gamesWon: this.win
          ? (this.gameProgress.gameSpecificProgress?.gamesWon || 0) + 1
          : this.gameProgress.gameSpecificProgress?.gamesWon || 0,
        bestTimes: {
          ...this.gameProgress.gameSpecificProgress?.bestTimes,
          beginner: this.win
            ? Math.min(
                this.moveCount,
                this.gameProgress.gameSpecificProgress?.bestTimes?.beginner ||
                  Infinity
              )
            : this.gameProgress.gameSpecificProgress?.bestTimes?.beginner,
        },
        currentStreak: this.win
          ? (this.gameProgress.gameSpecificProgress?.currentStreak || 0) + 1
          : 0,
      },
    };

    this.gameService
      .updateGameProgress(this.gameId, progress)
      .subscribe((updatedProgress) => {
        this.gameProgress = updatedProgress;
      });
  }

  private checkWin(): void {
    if (this.gameOver) return;

    let allSafeRevealed = true;
    for (let i = 0; i < this.rows; i++) {
      for (let j = 0; j < this.cols; j++) {
        if (this.board[i][j] !== -1 && !this.revealed[i][j]) {
          allSafeRevealed = false;
          break;
        }
      }
      if (!allSafeRevealed) break;
    }

    if (allSafeRevealed) {
      this.win = true;
      this.gameOver = true;
      this.gameResult = 'win';
      this.score += 50 * this.level;
      this.updateProgress();
    }
  }

  public dismissPopup(): void {
    this.gameResult = null;
    if (this.win) {
      this.nextLevel();
    } else {
      this.resetGame();
    }
  }

  onCellClick(row: number, col: number): void {
    if (this.plantFlag) {
      this.toggleFlag(row, col);
    } else {
      this.revealCell(row, col);
    }
  }

  onRightClick(event: Event, row: number, col: number): void {
    event.preventDefault(); // Prevent context menu
    this.toggleFlag(row, col);
  }

  toggleFlag(row: number, col: number): void {
    if (!this.revealed[row][col] && !this.gameOver) {
      this.flagged[row][col] = !this.flagged[row][col];
    }
  }

  handleControlClick(controlName: string): void {
    console.log('Control clicked:', controlName);

    switch (controlName) {
      case 'reset':
        console.log('Resetting game...');
        this.resetGame();
        break;
      case 'plantFlag':
        this.plantFlag = !this.plantFlag;
        // Update control appearance based on state
        this.gameControls[0].color = this.plantFlag ? 'red' : 'yellow';
        this.gameControls[0].icon = this.plantFlag ? 'flag' : 'flag-outline';
        this.gameControls[0].label = this.plantFlag ? 'Flag Mode (ON)' : 'Flag Mode';
        // Add visual feedback
        console.log('Flag mode ' + (this.plantFlag ? 'activated' : 'deactivated'));
        break;
      default:
        console.warn('Unknown control:', controlName);
    }

    // Force change detection to update the view
    this.cdr.detectChanges();
  }

  private updateProgress(): void {
    const finalScore = this.calculateFinalScore();

    const progress: Partial<GameProgress> = {
      gameId: this.gameId,
      userId: this.gameProgress?.userId || 'demo-user',
      lastPlayed: new Date(),
      attemptsRemaining: this.attemptsRemaining - 1,
      highScore: Math.max(finalScore, this.gameProgress?.highScore || 0),
      currentLevel: this.level,
      gameSpecificProgress: {
        gamesWon:
          (this.gameProgress?.gameSpecificProgress?.gamesWon || 0) +
          (this.gameOver ? 0 : 1),
        bestTimes: {
          ...(this.gameProgress?.gameSpecificProgress?.bestTimes || {}),
          [this.config?.difficulty || 'EASY']: Math.min(
            (Date.now() - this.startTime) / 1000,
            this.gameProgress?.gameSpecificProgress?.bestTimes?.[
              this.config?.difficulty || 'EASY'
            ] || Infinity
          ),
        },
        currentStreak: this.gameOver
          ? 0
          : (this.gameProgress?.gameSpecificProgress?.currentStreak || 0) + 1,
        difficulty: this.config?.difficulty || 'EASY',
        pointsEarned: finalScore,
        bonusesAwarded: this.calculateBonuses(),
      },
    };

    this.gameService
      .updateGameProgress(this.gameId, progress)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (updatedProgress) => {
          this.gameProgress = updatedProgress;
          this.highScore = updatedProgress.highScore;
          this.attemptsRemaining = updatedProgress.attemptsRemaining;
          this.cdr.detectChanges();
        },
        error: (error) => console.error('Error updating progress:', error),
      });
  }

  // Helper methods
  private getRevealedCount(): number {
    return this.revealed.reduce(
      (count, row) => count + row.filter((cell) => cell).length,
      0
    );
  }

  private getFlaggedMineCount(): number {
    let count = 0;
    for (let i = 0; i < this.rows; i++) {
      for (let j = 0; j < this.cols; j++) {
        if (this.flagged[i][j] && this.board[i][j] === -1) {
          count++;
        }
      }
    }
    return count;
  }

  private getTimeLimit(): number {
    const difficulty = this.config
      ?.difficulty as keyof typeof this.difficultySettings;
    switch (difficulty) {
      case 'EASY':
        return 120;
      case 'MEDIUM':
        return 240;
      case 'HARD':
        return 480;
      default:
        return 240;
    }
  }

  // Public methods for template
  public getDifficultyMultiplier(difficulty: string): number {
    const validDifficulty = difficulty as keyof typeof this.difficultySettings;
    return this.difficultySettings[validDifficulty]?.pointMultiplier || 1;
  }

  public getBonuses(): GameBonus[] {
    return (
      (this.gameProgress?.gameSpecificProgress as any)?.bonusesAwarded || []
    );
  }

  private calculateFinalScore(): number {
    const difficulty = this.config
      ?.difficulty as keyof typeof this.difficultySettings;
    const multiplier =
      this.difficultySettings[difficulty]?.pointMultiplier || 1;
    const baseScore = this.score * multiplier;

    const bonuses = this.calculateBonuses();
    const bonusPoints = bonuses.reduce(
      (total, bonus) => total + bonus.points,
      0
    );

    return baseScore + bonusPoints;
  }
}
