@import '../styles/game-styles.css';

.quiz-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.question-container {
  position: relative;
  overflow: hidden;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.question-container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, rgba(30, 64, 175, 0) 70%);
  animation: pulse 8s infinite;
  z-index: -1;
}

.option-button {
  position: relative;
  overflow: hidden;
}

.option-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

.option-button:focus::after {
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  100% {
    transform: scale(20, 20);
    opacity: 0;
  }
}

@keyframes pulse {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.timer-container {
  position: relative;
  width: 100%;
  height: 4px;
  background-color: rgba(100, 116, 139, 0.3);
  border-radius: 2px;
  overflow: hidden;
}

.timer-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(to right, #3b82f6, #6366f1);
  transition: width 1s linear;
}

.score-animation {
  animation: scorePopup 0.5s ease-out;
}

@keyframes scorePopup {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}