import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { GameControl, GamesPlayButtonComponent } from '../components/games-play-button/games-play-button.component';
import { WinLoseOverlayComponent } from '../components/win-lose-overlay/win-lose-overlay.component';
import { Game } from 'lp-client-api';

interface Question {
  text: string;
  options: string[];
  correctAnswer: number;
}

interface QuizConfig {
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  maxAttempts: number;
  timeLimit: number; // in seconds
  categories: string[];
  questionsPerRound: number;
  passingScore: number; // percentage needed to pass
}

interface QuizGameEvent {
  type: string;
  data: any;
  timestamp: string;
  gameId: string;
}

@Component({
  selector: 'lib-quiz',
  templateUrl: './quiz.component.html',
  styleUrls: ['./quiz.component.css'],
  standalone: true,
  imports: [CommonModule, GamesScoreComponent, GamesPlayButtonComponent, WinLoseOverlayComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class QuizComponent implements OnInit {
  @Input() gameId: string = '';
  @Input() config: QuizConfig = {
    difficulty: 'MEDIUM',
    maxAttempts: 3,
    timeLimit: 60,
    categories: ['General Knowledge', 'Science', 'Geography'],
    questionsPerRound: 5,
    passingScore: 60
  };
  @Input() gameInstance: any = null;
  @Input() game?: Game;

  @Output() gameEventEmitter = new EventEmitter<QuizGameEvent>();

  // Default questions for demo mode
  defaultQuestions: Question[] = [
    {
      text: 'What is the capital of France?',
      options: ['London', 'Berlin', 'Paris', 'Madrid'],
      correctAnswer: 2,
    },
    {
      text: 'Which planet is known as the Red Planet?',
      options: ['Venus', 'Mars', 'Jupiter', 'Saturn'],
      correctAnswer: 1,
    },
    {
      text: 'What is the largest mammal in the world?',
      options: ['Elephant', 'Blue Whale', 'Giraffe', 'Hippopotamus'],
      correctAnswer: 1,
    },
    {
      text: 'Which element has the chemical symbol "O"?',
      options: ['Gold', 'Oxygen', 'Osmium', 'Oganesson'],
      correctAnswer: 1,
    },
    {
      text: 'Which country is home to the Great Barrier Reef?',
      options: ['Brazil', 'Australia', 'Thailand', 'Mexico'],
      correctAnswer: 1,
    },
    {
      text: 'What is the largest ocean on Earth?',
      options: ['Atlantic Ocean', 'Indian Ocean', 'Arctic Ocean', 'Pacific Ocean'],
      correctAnswer: 3,
    },
    {
      text: 'Who wrote "Romeo and Juliet"?',
      options: ['Charles Dickens', 'William Shakespeare', 'Jane Austen', 'Mark Twain'],
      correctAnswer: 1,
    },
    {
      text: 'What is the tallest mountain in the world?',
      options: ['K2', 'Mount Everest', 'Kilimanjaro', 'Denali'],
      correctAnswer: 1,
    },
    {
      text: 'Which of these is not a primary color?',
      options: ['Red', 'Blue', 'Green', 'Yellow'],
      correctAnswer: 3,
    },
    {
      text: 'What is the capital of Japan?',
      options: ['Seoul', 'Beijing', 'Tokyo', 'Bangkok'],
      correctAnswer: 2,
    }
  ];

  questions: Question[] = [];
  currentQuestionIndex: number = 0;
  selectedAnswer: number | null = null;
  score: number = 0;
  quizCompleted: boolean = false;
  attemptsRemaining: number = 3;
  timeRemaining: number = 0;
  timerInterval: any = null;
  gameResult: 'win' | 'lose' | null = null;
  isDemoMode: boolean = false;
  errorMessage: string = '';

  // Game controls configuration
  gameControls: GameControl[] = [
    { name: 'restart', icon: 'refresh-outline', label: 'Restart' },
    { name: 'newGame', icon: 'add-circle-outline', label: 'New Game' }
  ];

  ngOnInit() {
    this.initializeGame();
  }

  ngOnDestroy() {
    this.clearTimer();
  }

  initializeGame() {
    try {
      // Check if we have a valid configuration from the input
      if (this.config && this.config.questionsPerRound) {
        console.log('Initializing quiz with config:', this.config);
      } else {
        console.warn('No valid config provided, using default config');
        this.initializeDemoMode();
      }

      // Set attempts remaining from config
      this.attemptsRemaining = this.config.maxAttempts || 3;

      // Initialize questions
      this.loadQuestions();

      // Reset game state
      this.currentQuestionIndex = 0;
      this.selectedAnswer = null;
      this.score = 0;
      this.quizCompleted = false;
      this.gameResult = null;

      // Start timer if time limit is set
      this.startTimer();

      // Emit game start event
      this.emitGameEvent('start', {
        difficulty: this.config.difficulty,
        questionsCount: this.questions.length,
        isDemoMode: this.isDemoMode
      });
    } catch (error) {
      console.error('Error initializing quiz game:', error);
      this.initializeDemoMode();
    }
  }

  initializeDemoMode() {
    console.log('Initializing quiz in demo mode');
    this.isDemoMode = true;
    this.errorMessage = 'Playing in demo mode';

    // Set default configuration
    this.config = {
      difficulty: 'MEDIUM',
      maxAttempts: 3,
      timeLimit: 60,
      categories: ['General Knowledge'],
      questionsPerRound: 5,
      passingScore: 60
    };

    this.attemptsRemaining = this.config.maxAttempts;
  }

  loadQuestions() {
    // In a real implementation, this would load questions from an API
    // For now, we'll use the default questions
    this.questions = this.getRandomQuestions(this.config.questionsPerRound);
    this.shuffleQuestions();
  }

  getRandomQuestions(count: number): Question[] {
    // Shuffle the default questions and take the requested number
    const shuffled = [...this.defaultQuestions].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.min(count, shuffled.length));
  }

  shuffleQuestions() {
    for (let i = this.questions.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [this.questions[i], this.questions[j]] = [
        this.questions[j],
        this.questions[i],
      ];
    }
  }

  startTimer() {
    this.clearTimer();

    if (this.config.timeLimit > 0) {
      this.timeRemaining = this.config.timeLimit;
      this.timerInterval = setInterval(() => {
        this.timeRemaining--;
        if (this.timeRemaining <= 0) {
          this.clearTimer();
          this.handleTimeUp();
        }
      }, 1000);
    }
  }

  clearTimer() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
  }

  handleTimeUp() {
    if (!this.quizCompleted) {
      this.quizCompleted = true;
      this.checkResult();
      this.emitGameEvent('timeUp', {
        score: this.score,
        totalQuestions: this.questions.length
      });
    }
  }

  selectAnswer(index: number) {
    if (this.quizCompleted) return;
    this.selectedAnswer = index;
  }

  submitAnswer() {
    if (this.selectedAnswer === null || this.quizCompleted) return;

    const isCorrect = this.selectedAnswer === this.questions[this.currentQuestionIndex].correctAnswer;

    if (isCorrect) {
      this.score++;
    }

    // Emit answer event
    this.emitGameEvent('answer', {
      questionIndex: this.currentQuestionIndex,
      question: this.questions[this.currentQuestionIndex].text,
      selectedAnswer: this.selectedAnswer,
      correctAnswer: this.questions[this.currentQuestionIndex].correctAnswer,
      isCorrect: isCorrect
    });

    this.selectedAnswer = null;
    this.currentQuestionIndex++;

    if (this.currentQuestionIndex >= this.questions.length) {
      this.quizCompleted = true;
      this.clearTimer();
      this.checkResult();
    }
  }

  checkResult() {
    const scorePercentage = (this.score / this.questions.length) * 100;
    const passed = scorePercentage >= this.config.passingScore;

    this.gameResult = passed ? 'win' : 'lose';

    // Decrement attempts if failed
    if (!passed) {
      this.attemptsRemaining--;
    }

    // Emit game end event
    this.emitGameEvent('end', {
      score: this.score,
      totalQuestions: this.questions.length,
      scorePercentage: scorePercentage,
      passed: passed,
      attemptsRemaining: this.attemptsRemaining,
      timeRemaining: this.timeRemaining
    });
  }

  restartQuiz() {
    if (this.attemptsRemaining <= 0) {
      this.emitGameEvent('outOfAttempts', {});
      return;
    }

    this.currentQuestionIndex = 0;
    this.score = 0;
    this.quizCompleted = false;
    this.gameResult = null;
    this.loadQuestions();
    this.startTimer();

    this.emitGameEvent('restart', {
      attemptsRemaining: this.attemptsRemaining
    });
  }

  handleControlClick(control: any) {
    // Convert the event to a string if it's not already
    const controlName = typeof control === 'string' ? control : control.toString();

    console.log('Control clicked:', controlName);

    switch (controlName) {
      case 'restart':
        this.restartQuiz();
        break;
      case 'newGame':
        this.attemptsRemaining = this.config.maxAttempts;
        this.initializeGame();
        break;
      default:
        console.warn('Unknown control:', controlName);
    }
  }

  emitGameEvent(type: string, data: any) {
    const event: QuizGameEvent = {
      type: type,
      data: data,
      timestamp: new Date().toISOString(),
      gameId: this.gameId
    };

    this.gameEventEmitter.emit(event);
    console.log('Quiz game event:', event);
  }

  getScoreItems() {
    return [
      { title: 'Score', number: `${this.score}/${this.questions.length}` },
      { title: 'Time', number: `${this.timeRemaining}s` },
      { title: 'Attempts', number: this.attemptsRemaining }
    ];
  }
}
