<div class="flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800">
  <!-- Score Display -->
  <div class="w-full max-w-md mx-auto px-4 py-2">
    <games-score [scores]="getScoreItems()"></games-score>
  </div>

  <!-- Game Title -->
  <div class="text-center mb-1">
    <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-indigo-500">Quiz Game</h1>
    <p *ngIf="isDemoMode" class="text-xs text-amber-400 mb-1">Demo Mode</p>
    <p class="text-xs text-slate-400 mb-1">Test your knowledge!</p>
  </div>

  <!-- Game Board -->
  <div class="flex overflow-hidden flex-col flex-grow items-center p-4">
    <div *ngIf="!quizCompleted" class="w-full max-w-md space-y-4">
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold text-slate-300">
          Question {{ currentQuestionIndex + 1 }} of {{ questions.length }}
        </h2>
      </div>

      <div class="p-4 bg-gradient-to-br from-blue-900/70 to-indigo-900/70 rounded-lg border border-blue-700/50 shadow-lg">
        <p class="text-lg text-slate-200">
          {{ questions[currentQuestionIndex].text }}
        </p>
      </div>

      <div class="space-y-3">
        <button
          *ngFor="let option of questions[currentQuestionIndex].options; let i = index"
          (click)="selectAnswer(i)"
          class="p-3 w-full text-left rounded-lg transition-all duration-200 ease-in-out shadow-md"
          [ngClass]="{
            'bg-gradient-to-r from-blue-600 to-blue-800 text-white border border-blue-500 transform scale-105': selectedAnswer === i,
            'bg-gradient-to-r from-slate-700 to-slate-800 text-slate-200 border border-slate-600 hover:from-slate-600 hover:to-slate-700': selectedAnswer !== i
          }"
        >
          {{ option }}
        </button>
      </div>

      <button
        (click)="submitAnswer()"
        [disabled]="selectedAnswer === null"
        class="w-full px-6 py-3 text-white bg-gradient-to-r from-green-600 to-green-700 rounded-lg shadow-lg hover:from-green-500 hover:to-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 ease-in-out transform hover:scale-105"
      >
        Submit Answer
      </button>
    </div>
  </div>

  <!-- Game Controls -->
  <div class="p-2 my-2 space-y-2 border-t border-slate-700">
    <lib-games-play-button
      [controls]="gameControls"
      (controlClicked)="handleControlClick($event)"
    ></lib-games-play-button>
  </div>

  <!-- Win/Lose Overlay -->
  <lib-win-lose-overlay
    *ngIf="gameResult"
    [status]="gameResult"
    [message]="
      gameResult === 'win'
        ? 'Congratulations! You passed the quiz!'
        : 'Better luck next time! Keep practicing!'
    "
    (restart)="restartQuiz()"
  ></lib-win-lose-overlay>
</div>
