/* Import shared game styles */
@import '../styles/game-styles.css';

:host {
  @apply block w-full h-full;
}

/* Demo Mode Indicator */
.demo-mode-indicator {
  @apply flex items-center justify-center p-1 mb-1 text-xs text-amber-300 bg-amber-900/50 rounded-md;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 0.5;
  }
}

/* Card Styling */
.playing-card {
  @apply relative w-16 h-24 rounded-lg overflow-hidden shadow-lg;
  perspective: 1000px;
  transition: transform 0.3s;
}

.playing-card:hover {
  transform: translateY(-5px);
}

.card-inner {
  @apply relative w-full h-full transition-transform duration-500;
  transform-style: preserve-3d;
}

.card-hidden .card-inner {
  transform: rotateY(180deg);
}

.card-front, .card-back {
  @apply absolute inset-0 w-full h-full;
  backface-visibility: hidden;
}

.card-front {
  @apply bg-white text-black p-1 flex flex-col justify-between;
}

.card-back {
  @apply bg-blue-800;
  transform: rotateY(180deg);
  background-image: linear-gradient(45deg, #2563eb 25%, transparent 25%, transparent 50%, #2563eb 50%, #2563eb 75%, transparent 75%, transparent);
  background-size: 10px 10px;
}

.card-pattern {
  @apply w-full h-full border-4 border-white rounded-sm m-1;
}

.card-corner {
  @apply flex flex-col items-center;
}

.top-left {
  @apply absolute top-1 left-1;
}

.bottom-right {
  @apply absolute bottom-1 right-1;
  transform: rotate(180deg);
}

.card-value {
  @apply text-sm font-bold;
}

.card-suit {
  @apply text-sm;
}

.card-center {
  @apply flex-grow flex items-center justify-center text-4xl;
}

.card-red {
  @apply text-red-600;
}

/* Betting Controls */
.betting-controls {
  @apply text-center;
}

.bet-button {
  @apply px-4 py-2 m-1 font-bold text-white rounded-full shadow-lg transition-all duration-300;
  @apply bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700;
  @apply transform hover:scale-105 active:scale-95;
}

.bet-button-disabled {
  @apply opacity-50 cursor-not-allowed;
  @apply bg-gradient-to-r from-gray-500 to-gray-600;
  transform: none;
}

/* Game Controls */
.game-controls {
  @apply text-center;
}

.game-action-button-disabled {
  @apply opacity-50 cursor-not-allowed;
  @apply bg-gradient-to-r from-gray-500 to-gray-600;
  transform: none !important;
}