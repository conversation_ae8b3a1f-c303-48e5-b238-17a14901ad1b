import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { WinLoseOverlayComponent } from '../components/win-lose-overlay/win-lose-overlay.component';
import { GameControl, GamesPlayButtonComponent } from '../components/games-play-button/games-play-button.component';
import { Game } from 'lp-client-api';

interface Card {
  suit: string;
  value: string;
  numericValue: number;
  hidden?: boolean;
}

interface BlackjackConfig {
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  maxAttempts: number;
  startingChips?: number;
  minBet?: number;
  maxBet?: number;
}

interface GameEvent {
  type: string;
  data: any;
  timestamp: string;
  gameId: string;
}

@Component({
  selector: 'lib-blackjack',
  templateUrl: './blackjack.component.html',
  styleUrls: ['./blackjack.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    GamesScoreComponent,
    WinLoseOverlayComponent,
    GamesPlayButtonComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class BlackjackComponent implements OnInit {
  // Required inputs for all games
  @Input() gameId: string = '';
  @Input() config: any = {};
  @Input() gameInstance: any = null;
  @Input() game?: Game;

  // Output for game events
  @Output() gameEvent = new EventEmitter<GameEvent>();

  // Game state
  deck: Card[] = [];
  playerHand: Card[] = [];
  dealerHand: Card[] = [];
  playerScore = 0;
  dealerScore = 0;
  gameOver = false;
  message = '';
  wins = 0;
  losses = 0;
  ties = 0;
  gameResult: 'win' | 'lose' | null = null;
  demoMode: boolean = false;
  errorMessage: string = '';
  chips = 1000;
  currentBet = 0;
  betPlaced = false;
  bettingPhase = true;

  // Default configuration
  private readonly DEFAULT_CONFIG: BlackjackConfig = {
    difficulty: 'MEDIUM',
    maxAttempts: 3,
    startingChips: 1000,
    minBet: 10,
    maxBet: 100
  };

  // Game controls
  gameControls: GameControl[] = [
    { name: 'restart', label: 'New Game', icon: 'refresh-outline', color: 'green' }
  ];
  ngOnInit() {
    console.log('Blackjack component initialized');
    console.log('Game ID:', this.gameId);
    console.log('Config:', this.config);
    console.log('Game Instance:', this.gameInstance);

    try {
      this.initializeGame();
    } catch (error) {
      console.error('Error initializing game:', error);
      this.initializeDemoMode();
    }
  }

  /**
   * Initialize the game with configuration
   */
  private initializeGame(): void {
    // Set up configuration
    if (this.config) {
      // Set difficulty
      if (this.config.difficulty) {
        switch (this.config.difficulty) {
          case 'EASY':
            this.chips = this.config.startingChips || 1500;
            break;
          case 'MEDIUM':
            this.chips = this.config.startingChips || 1000;
            break;
          case 'HARD':
            this.chips = this.config.startingChips || 500;
            break;
          default:
            this.chips = this.DEFAULT_CONFIG.startingChips || 1000;
        }
      } else {
        this.chips = this.DEFAULT_CONFIG.startingChips || 1000;
      }
    } else {
      // Use default configuration
      this.chips = this.DEFAULT_CONFIG.startingChips || 1000;
    }

    this.initializeDeck();
    this.startNewGame();
  }

  /**
   * Initialize demo mode when API fails
   */
  private initializeDemoMode(): void {
    console.log('Initializing demo mode for Blackjack game');
    this.demoMode = true;
    this.chips = this.DEFAULT_CONFIG.startingChips || 1000;
    this.initializeDeck();
    this.startNewGame();
  }

  /**
   * Handle control button clicks
   */
  handleControlClick(controlName: string) {
    if (controlName === 'restart') {
      this.startNewGame();
    }
  }

  initializeDeck() {
    const suits = ['♠', '♥', '♦', '♣'];
    const values = [
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '10',
      'J',
      'Q',
      'K',
      'A',
    ];

    for (const suit of suits) {
      for (const value of values) {
        this.deck.push({
          suit,
          value,
          numericValue: this.getNumericValue(value),
        });
      }
    }

    this.shuffleDeck();
  }

  shuffleDeck() {
    for (let i = this.deck.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [this.deck[i], this.deck[j]] = [this.deck[j], this.deck[i]];
    }
  }

  getNumericValue(value: string): number {
    if (['J', 'Q', 'K'].includes(value)) return 10;
    if (value === 'A') return 11;
    return parseInt(value, 10);
  }

  startNewGame() {
    // Reset game state
    this.playerHand = [];
    this.dealerHand = [];
    this.playerScore = 0;
    this.dealerScore = 0;
    this.gameOver = false;
    this.message = '';
    this.gameResult = null;
    this.bettingPhase = true;
    this.betPlaced = false;
    this.currentBet = 0;

    // If deck is running low, reinitialize
    if (this.deck.length < 10) {
      this.initializeDeck();
    }

    // Emit game start event
    this.emitGameEvent('game_start', {
      chips: this.chips,
      wins: this.wins,
      losses: this.losses,
      ties: this.ties
    });
  }

  placeBet(amount: number) {
    if (!this.bettingPhase || this.betPlaced || amount > this.chips) {
      return;
    }

    this.currentBet = amount;
    this.chips -= amount;
    this.betPlaced = true;
    this.bettingPhase = false;

    // Deal initial cards
    this.dealCard(this.playerHand);
    this.dealCard(this.dealerHand, true); // Dealer's first card is hidden
    this.dealCard(this.playerHand);

    this.updateScores();

    // Check for natural blackjack
    if (this.playerScore === 21) {
      this.dealerHand[0].hidden = false; // Reveal dealer's hidden card
      this.updateScores();

      if (this.dealerScore === 21) {
        this.endGame("Both have Blackjack! It's a tie!", 'tie');
      } else {
        this.endGame('Blackjack! Player wins!', 'win', 2.5); // Blackjack pays 3:2
      }
    }

    // Emit bet placed event
    this.emitGameEvent('bet_placed', {
      amount: this.currentBet,
      remainingChips: this.chips
    });
  }

  dealCard(hand: Card[], hidden: boolean = false) {
    if (this.deck.length > 0) {
      const card = this.deck.pop()!;
      card.hidden = hidden;
      hand.push(card);
    }
  }

  updateScores() {
    this.playerScore = this.calculateScore(this.playerHand);
    this.dealerScore = this.calculateScore(this.dealerHand.filter(card => !card.hidden));
  }

  calculateScore(hand: Card[]): number {
    let score = 0;
    let aces = 0;

    for (const card of hand) {
      if (card.value === 'A') {
        aces++;
      }
      score += card.numericValue;
    }

    // Adjust aces value (1 or 11) to get the best score
    while (score > 21 && aces > 0) {
      score -= 10; // Change an ace from 11 to 1
      aces--;
    }

    return score;
  }

  hit() {
    if (this.gameOver || this.bettingPhase || !this.betPlaced) {
      return;
    }

    this.dealCard(this.playerHand);
    this.updateScores();

    // Emit hit event
    this.emitGameEvent('player_hit', {
      newCard: this.playerHand[this.playerHand.length - 1],
      newScore: this.playerScore
    });

    if (this.playerScore > 21) {
      this.endGame('Player busts! Dealer wins.', 'lose');
    } else if (this.playerScore === 21) {
      this.stand(); // Automatically stand on 21
    }
  }

  stand() {
    if (this.gameOver || this.bettingPhase || !this.betPlaced) {
      return;
    }

    // Reveal dealer's hidden card
    if (this.dealerHand.length > 0 && this.dealerHand[0].hidden) {
      this.dealerHand[0].hidden = false;
      this.updateScores();
    }

    // Dealer draws until 17 or higher
    while (this.dealerScore < 17) {
      this.dealCard(this.dealerHand);
      this.updateScores();
    }

    // Emit dealer play event
    this.emitGameEvent('dealer_play', {
      dealerHand: this.dealerHand,
      dealerScore: this.dealerScore
    });

    // Determine winner
    if (this.dealerScore > 21) {
      this.endGame('Dealer busts! Player wins.', 'win');
    } else if (this.dealerScore > this.playerScore) {
      this.endGame('Dealer wins!', 'lose');
    } else if (this.dealerScore < this.playerScore) {
      this.endGame('Player wins!', 'win');
    } else {
      this.endGame("It's a tie!", 'tie');
    }
  }

  doubleDown() {
    if (this.gameOver || this.bettingPhase || !this.betPlaced || this.playerHand.length > 2 || this.chips < this.currentBet) {
      return;
    }

    // Double the bet
    this.chips -= this.currentBet;
    this.currentBet *= 2;

    // Deal one more card and stand
    this.dealCard(this.playerHand);
    this.updateScores();

    // Emit double down event
    this.emitGameEvent('double_down', {
      newBet: this.currentBet,
      newCard: this.playerHand[this.playerHand.length - 1],
      newScore: this.playerScore
    });

    if (this.playerScore > 21) {
      this.endGame('Player busts! Dealer wins.', 'lose');
    } else {
      this.stand();
    }
  }

  endGame(message: string, result: 'win' | 'lose' | 'tie', payoutMultiplier: number = 2) {
    this.gameOver = true;
    this.message = message;

    // Set game result for the overlay
    if (result === 'win') {
      this.gameResult = 'win';
      this.wins++;
      // Pay out winnings (default 1:1, blackjack 3:2)
      this.chips += Math.floor(this.currentBet * payoutMultiplier);
    } else if (result === 'lose') {
      this.gameResult = 'lose';
      this.losses++;
      // Bet is already deducted
    } else {
      // Tie - return the bet
      this.ties++;
      this.chips += this.currentBet;
    }

    // Emit game end event
    this.emitGameEvent('game_end', {
      result: result,
      message: message,
      playerScore: this.playerScore,
      dealerScore: this.dealerScore,
      playerHand: this.playerHand,
      dealerHand: this.dealerHand,
      winnings: result === 'win' ? Math.floor(this.currentBet * payoutMultiplier) - this.currentBet : 0,
      chips: this.chips
    });
  }

  /**
   * Emit a game event
   */
  private emitGameEvent(eventType: string, data: any): void {
    const gameEvent: GameEvent = {
      type: eventType,
      data: data,
      timestamp: new Date().toISOString(),
      gameId: this.gameId
    };

    this.gameEvent.emit(gameEvent);
    console.log('Game event emitted:', gameEvent);
  }
}
