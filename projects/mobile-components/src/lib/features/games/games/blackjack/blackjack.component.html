<div class="flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800">
  <!-- Demo Mode Indicator -->
  <div *ngIf="demoMode" class="demo-mode-indicator">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
    <span>DEMO MODE</span>
  </div>

  <!-- Score Display -->
  <games-score
    [scores]="[
      { title: 'Chips', number: chips },
      { title: 'Bet', number: currentBet },
      { title: 'Wins', number: wins },
      { title: 'Losses', number: losses }
    ]"
  ></games-score>

  <!-- Game Title -->
  <div class="text-center mb-1">
    <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-red-400 to-amber-500">Blackjack</h1>
    <p class="text-xs text-slate-400 mb-1">Try to beat the dealer without going over 21</p>
  </div>

  <!-- Game Board Container -->
  <div class="game-board-container">
    <!-- Dealer's Hand -->
    <div class="dealer-area mb-4">
      <h2 class="text-xl font-semibold text-slate-300">Dealer's Hand</h2>
      <div class="flex justify-center space-x-2 my-2">
        <div
          *ngFor="let card of dealerHand"
          class="playing-card"
          [ngClass]="{'card-red': card.suit === '♥' || card.suit === '♦', 'card-hidden': card.hidden}"
        >
          <div class="card-inner">
            <div class="card-front">
              <div class="card-corner top-left">
                <div class="card-value">{{ card.value }}</div>
                <div class="card-suit">{{ card.suit }}</div>
              </div>
              <div class="card-center">{{ card.suit }}</div>
              <div class="card-corner bottom-right">
                <div class="card-value">{{ card.value }}</div>
                <div class="card-suit">{{ card.suit }}</div>
              </div>
            </div>
            <div class="card-back">
              <div class="card-pattern"></div>
            </div>
          </div>
        </div>
      </div>
      <p class="text-slate-300">Dealer's Score: {{ dealerScore }}</p>
    </div>

    <!-- Player's Hand -->
    <div class="player-area mb-4">
      <h2 class="text-xl font-semibold text-slate-300">Player's Hand</h2>
      <div class="flex justify-center space-x-2 my-2">
        <div
          *ngFor="let card of playerHand"
          class="playing-card"
          [ngClass]="{'card-red': card.suit === '♥' || card.suit === '♦'}"
        >
          <div class="card-inner">
            <div class="card-front">
              <div class="card-corner top-left">
                <div class="card-value">{{ card.value }}</div>
                <div class="card-suit">{{ card.suit }}</div>
              </div>
              <div class="card-center">{{ card.suit }}</div>
              <div class="card-corner bottom-right">
                <div class="card-value">{{ card.value }}</div>
                <div class="card-suit">{{ card.suit }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <p class="text-slate-300">Player's Score: {{ playerScore }}</p>
    </div>

    <!-- Betting Controls -->
    <div *ngIf="bettingPhase && !gameOver" class="betting-controls mb-4">
      <h3 class="text-lg font-semibold text-slate-300 mb-2">Place Your Bet</h3>
      <div class="flex justify-center space-x-2">
        <button
          *ngFor="let amount of [10, 25, 50, 100]"
          (click)="placeBet(amount)"
          [disabled]="amount > chips"
          class="bet-button"
          [ngClass]="{'bet-button-disabled': amount > chips}"
        >
          {{ amount }}
        </button>
      </div>
    </div>

    <!-- Game Controls -->
    <div *ngIf="!bettingPhase && !gameOver" class="game-controls mb-4">
      <div class="flex justify-center space-x-2">
        <button
          (click)="hit()"
          class="game-action-button game-action-button-primary"
        >
          Hit
        </button>
        <button
          (click)="stand()"
          class="game-action-button game-action-button-secondary"
        >
          Stand
        </button>
        <button
          *ngIf="playerHand.length === 2 && chips >= currentBet"
          (click)="doubleDown()"
          class="game-action-button"
          [ngClass]="{'game-action-button-primary': chips >= currentBet, 'game-action-button-disabled': chips < currentBet}"
        >
          Double
        </button>
      </div>
    </div>

    <!-- Game Controls -->
    <div class="mt-auto">
      <lib-games-play-button
        [controls]="gameControls"
        (controlClicked)="handleControlClick($event)"
      ></lib-games-play-button>
    </div>
  </div>

  <!-- Win/Lose Overlay -->
  <lib-win-lose-overlay
    [status]="gameResult"
    (restart)="startNewGame()"
    [message]="message"
  ></lib-win-lose-overlay>
</div>
