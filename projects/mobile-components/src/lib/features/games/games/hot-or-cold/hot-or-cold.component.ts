import { Component, OnInit, OnDestroy, Input, Output, EventEmitter, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { GamesPlayButtonComponent } from '../components/games-play-button/games-play-button.component';
import { Game } from 'lp-client-api';
import { Subject, Subscription, interval } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

interface HotOrColdConfig {
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  maxAttempts: number;
  timeLimit: number; // in seconds
  targetRadius: number; // in meters
  maxDistance: number; // maximum initial distance in meters
  hintsAllowed: number;
}

interface GameControl {
  name: string;
  label: string;
  icon?: string;
  color?: string;
}

interface GameEvent {
  type: string;
  data: any;
  timestamp: string;
  gameId: string;
}

@Component({
  selector: 'lib-hot-or-cold',
  templateUrl: './hot-or-cold.component.html',
  styleUrls: ['./hot-or-cold.component.css'],
  standalone: true,
  imports: [CommonModule, GamesScoreComponent, GamesPlayButtonComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class HotOrColdComponent implements OnInit, OnDestroy {
  // Required inputs for all games
  @Input() gameId: string = '';
  @Input() config: any = {};
  @Input() gameInstance: any = null;
  @Input() game?: Game;

  // Output for game events
  @Output() gameEventEmitter = new EventEmitter<GameEvent>();

  // Game state
  private destroy$ = new Subject<void>();
  private timerSubscription?: Subscription;
  private watchId?: number;
  private goalLocation: GeolocationCoordinates | null = null;
  private defaultConfig: HotOrColdConfig = {
    difficulty: 'MEDIUM',
    maxAttempts: 3,
    timeLimit: 300, // 5 minutes
    targetRadius: 20, // 20 meters
    maxDistance: 1000, // 1 kilometer
    hintsAllowed: 3
  };

  currentPosition: GeolocationPosition | null = null;
  currentDistance: number = Infinity;
  previousDistance: number = Infinity;
  timeLeft: number = 300;
  score: number = 0;
  level: number = 1;
  attemptsRemaining: number = 3;
  hintsRemaining: number = 3;
  gameStarted: boolean = false;
  gameOver: boolean = false;
  gameWon: boolean = false;
  isDemoMode: boolean = false;
  errorMessage: string = '';
  compassHeading: number = 0;
  showHint: boolean = false;
  hintDirection: string = '';
  hintTimeout: any;
  Math = Math; // Make Math available to the template
  Infinity = Infinity; // Make Infinity available to the template

  // Game controls
  gameControls: GameControl[] = [
    { name: 'start', label: 'Start Game', icon: 'play-outline', color: 'green' },
    { name: 'restart', label: 'Restart', icon: 'refresh-outline', color: 'red' }
  ];

  constructor() {}

  ngOnInit() {
    this.initializeGame();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    this.stopLocationTracking();
    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe();
    }
    if (this.hintTimeout) {
      clearTimeout(this.hintTimeout);
    }
  }

  // Initialize the game
  private initializeGame() {
    // Check if we have a valid configuration
    if (this.config && this.config.difficulty) {
      console.log('Using provided configuration:', this.config);
    } else {
      console.log('Using default configuration');
      this.config = this.defaultConfig;
      this.isDemoMode = true;
    }

    // Set initial game state
    this.timeLeft = this.config.timeLimit || this.defaultConfig.timeLimit;
    this.attemptsRemaining = this.config.maxAttempts || this.defaultConfig.maxAttempts;
    this.hintsRemaining = this.config.hintsAllowed || this.defaultConfig.hintsAllowed;
    this.level = 1;
    this.score = 0;
    this.gameStarted = false;
    this.gameOver = false;
    this.gameWon = false;
    this.currentDistance = Infinity;
    this.previousDistance = Infinity;

    // Make sure game controls are set up
    if (this.gameControls.length === 0) {
      this.gameControls = [
        { name: 'start', label: 'Start Game', icon: 'play-outline', color: 'green' },
        { name: 'restart', label: 'Restart', icon: 'refresh-outline', color: 'red' }
      ];
    }

    // Emit game initialization event
    this.emitGameEvent('init', { config: this.config, isDemoMode: this.isDemoMode });
  }

  // Game control handlers
  handleControlClick(control: any) {
    const controlName = typeof control === 'string' ? control : control.toString();
    console.log('Control clicked:', controlName);

    switch (controlName) {
      case 'start':
        this.startGame();
        break;
      case 'hint':
        this.useHint();
        break;
      case 'restart':
        this.restartGame();
        break;
      default:
        console.warn('Unknown control:', controlName);
    }
  }

  // Start the game
  startGame() {
    if (this.gameStarted) return;

    this.gameStarted = true;
    this.setRandomGoalLocation();
    this.startLocationTracking();
    this.startTimer();

    // Update controls
    this.gameControls = [
      { name: 'hint', label: 'Use Hint', icon: 'compass-outline', color: 'blue' },
      { name: 'restart', label: 'Restart', icon: 'refresh-outline', color: 'red' }
    ];

    // Emit game start event
    this.emitGameEvent('start', { level: this.level, attempts: this.attemptsRemaining });
  }

  // Restart the game
  restartGame() {
    this.stopLocationTracking();
    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe();
    }

    this.initializeGame();

    // Update controls
    this.gameControls = [
      { name: 'start', label: 'Start Game', icon: 'play-outline', color: 'green' },
      { name: 'restart', label: 'Restart', icon: 'refresh-outline', color: 'red' }
    ];

    // Emit game restart event
    this.emitGameEvent('restart', { level: this.level, attempts: this.attemptsRemaining });
  }

  // Use a hint
  useHint() {
    if (this.hintsRemaining <= 0 || !this.gameStarted || this.gameOver) return;

    this.hintsRemaining--;
    this.showHint = true;

    if (this.currentPosition && this.goalLocation) {
      // Calculate direction to target
      const bearing = this.calculateBearing(
        this.currentPosition.coords.latitude,
        this.currentPosition.coords.longitude,
        this.goalLocation.latitude,
        this.goalLocation.longitude
      );

      // Convert bearing to cardinal direction
      this.hintDirection = this.getCardinalDirection(bearing);
    } else {
      this.hintDirection = 'Unknown';
    }

    // Clear previous timeout if exists
    if (this.hintTimeout) {
      clearTimeout(this.hintTimeout);
    }

    // Hide hint after 5 seconds
    this.hintTimeout = setTimeout(() => {
      this.showHint = false;
    }, 5000);

    // Emit hint used event
    this.emitGameEvent('hint_used', {
      hintsRemaining: this.hintsRemaining,
      direction: this.hintDirection
    });
  }

  // Get score items for display
  getScoreItems() {
    // Format distance display to handle Infinity
    let distanceDisplay = this.currentDistance === Infinity ? '---' : `${Math.round(this.currentDistance)}m`;

    return [
      { title: 'Distance', number: distanceDisplay },
      { title: 'Time', number: this.formatTime(this.timeLeft) },
      { title: 'Hints', number: this.hintsRemaining }
    ];
  }

  // Format time as MM:SS
  private formatTime(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }

  // Get temperature color based on distance
  get temperatureColor(): string {
    if (!this.gameStarted) return 'blue'; // Default color before game starts
    if (this.currentDistance < this.config.targetRadius * 2) return 'red'; // Very hot
    if (this.currentDistance < this.config.targetRadius * 5) return 'orange'; // Hot
    if (this.currentDistance < this.config.targetRadius * 10) return 'yellow'; // Warm
    if (this.currentDistance < this.config.targetRadius * 20) return 'green'; // Cool
    return 'blue'; // Cold
  }

  // Get temperature text based on distance
  get temperatureText(): string {
    if (!this.gameStarted) return 'Start Game';
    if (this.currentDistance < this.config.targetRadius * 2) return 'Very Hot!';
    if (this.currentDistance < this.config.targetRadius * 5) return 'Hot';
    if (this.currentDistance < this.config.targetRadius * 10) return 'Warm';
    if (this.currentDistance < this.config.targetRadius * 20) return 'Cool';
    return 'Cold';
  }

  // Get direction indicator (getting closer or further)
  get directionIndicator(): string {
    if (this.currentDistance < this.previousDistance) return '↓'; // Getting closer
    if (this.currentDistance > this.previousDistance) return '↑'; // Getting further
    return '→'; // Same distance
  }
  // Set random goal location
  private setRandomGoalLocation() {
    // If we're in demo mode, generate a random location
    if (this.isDemoMode) {
      // Create a random location within a reasonable range
      const coords: GeolocationCoordinates = {
        latitude: Math.random() * 0.1 + 51.5, // Random latitude around London
        longitude: Math.random() * 0.1 - 0.1, // Random longitude around London
        accuracy: 0,
        altitude: null,
        altitudeAccuracy: null,
        heading: null,
        speed: null,
        toJSON: function() {
          return {
            latitude: this.latitude,
            longitude: this.longitude,
            accuracy: this.accuracy,
            altitude: this.altitude,
            altitudeAccuracy: this.altitudeAccuracy,
            heading: this.heading,
            speed: this.speed
          };
        }
      };
      this.goalLocation = coords;
      console.log('Demo mode: Random goal location set', this.goalLocation);
      return;
    }

    // If we have a current position, generate a location within maxDistance
    if (this.currentPosition) {
      const maxDistance = this.config.maxDistance || this.defaultConfig.maxDistance;
      const randomDistance = Math.random() * maxDistance;
      const randomBearing = Math.random() * 360;

      // Calculate new coordinates based on distance and bearing
      const newCoords = this.calculateDestinationPoint(
        this.currentPosition.coords.latitude,
        this.currentPosition.coords.longitude,
        randomDistance,
        randomBearing
      );

      // Create GeolocationCoordinates object
      const coords: GeolocationCoordinates = {
        latitude: newCoords.latitude,
        longitude: newCoords.longitude,
        accuracy: 0,
        altitude: null,
        altitudeAccuracy: null,
        heading: null,
        speed: null,
        toJSON: function() {
          return {
            latitude: this.latitude,
            longitude: this.longitude,
            accuracy: this.accuracy,
            altitude: this.altitude,
            altitudeAccuracy: this.altitudeAccuracy,
            heading: this.heading,
            speed: this.speed
          };
        }
      };
      this.goalLocation = coords;
      console.log('Goal location set based on current position', this.goalLocation);
    } else {
      // Fallback to demo mode if we don't have a current position
      this.isDemoMode = true;
      this.setRandomGoalLocation();
    }
  }

  // Start location tracking
  private startLocationTracking() {
    // If we're in demo mode, simulate location updates
    if (this.isDemoMode) {
      console.log('Demo mode: Simulating location updates');
      this.simulateLocationUpdates();
      return;
    }

    // Check if geolocation is available
    if ('geolocation' in navigator) {
      try {
        // Get initial position
        navigator.geolocation.getCurrentPosition(
          (position) => {
            console.log('Initial position:', position);
            this.currentPosition = position;

            // If we don't have a goal location yet, set one now
            if (!this.goalLocation) {
              this.setRandomGoalLocation();
            }

            // Start watching position
            this.watchId = navigator.geolocation.watchPosition(
              (position) => this.handlePositionUpdate(position),
              (error) => this.handleLocationError(error),
              { enableHighAccuracy: true, timeout: 10000, maximumAge: 0 }
            );
          },
          (error) => this.handleLocationError(error),
          { enableHighAccuracy: true, timeout: 10000, maximumAge: 0 }
        );
      } catch (error) {
        console.error('Error starting location tracking:', error);
        this.errorMessage = 'Error accessing location services. Playing in demo mode.';
        this.isDemoMode = true;
        this.simulateLocationUpdates();
      }
    } else {
      console.error('Geolocation is not supported by this browser.');
      this.errorMessage = 'Geolocation is not supported by your browser. Playing in demo mode.';
      this.isDemoMode = true;
      this.simulateLocationUpdates();
    }
  }

  // Handle location error
  private handleLocationError(error: GeolocationPositionError) {
    console.error('Geolocation error:', error);

    let errorMsg = 'Location error. Playing in demo mode.';
    switch (error.code) {
      case error.PERMISSION_DENIED:
        errorMsg = 'Location permission denied. Playing in demo mode.';
        break;
      case error.POSITION_UNAVAILABLE:
        errorMsg = 'Location information unavailable. Playing in demo mode.';
        break;
      case error.TIMEOUT:
        errorMsg = 'Location request timed out. Playing in demo mode.';
        break;
    }

    this.errorMessage = errorMsg;
    this.isDemoMode = true;
    this.simulateLocationUpdates();
  }

  // Simulate location updates for demo mode
  private simulateLocationUpdates() {
    // Make sure we have a goal location
    if (!this.goalLocation) {
      this.setRandomGoalLocation();
    }

    // Create initial simulated position
    if (!this.currentPosition) {
      // Start at a random position within maxDistance of the goal
      const maxDistance = this.config.maxDistance || this.defaultConfig.maxDistance;
      const initialDistance = Math.random() * maxDistance;
      const initialBearing = Math.random() * 360;

      const initialCoords = this.calculateDestinationPoint(
        this.goalLocation!.latitude,
        this.goalLocation!.longitude,
        initialDistance,
        initialBearing
      );

      // Create a simulated position
      this.currentPosition = {
        coords: {
          latitude: initialCoords.latitude,
          longitude: initialCoords.longitude,
          accuracy: 10,
          altitude: null,
          altitudeAccuracy: null,
          heading: null,
          speed: null,
          toJSON: function() {
            return {
              latitude: this.latitude,
              longitude: this.longitude,
              accuracy: this.accuracy,
              altitude: this.altitude,
              altitudeAccuracy: this.altitudeAccuracy,
              heading: this.heading,
              speed: this.speed
            };
          }
        },
        timestamp: Date.now(),
        toJSON: function() {
          return {
            coords: this.coords,
            timestamp: this.timestamp
          };
        }
      };

      // Calculate initial distance
      if (this.goalLocation) {
        this.currentDistance = this.calculateDistance(
          this.currentPosition.coords,
          this.goalLocation
        );
        this.previousDistance = this.currentDistance;
      }
    }

    // Set up interval to simulate movement
    const simulationInterval = interval(2000); // Update every 2 seconds
    this.timerSubscription = simulationInterval
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        if (this.gameOver) return;

        // Simulate movement towards the goal with some randomness
        const moveTowardsGoal = Math.random() > 0.3; // 70% chance to move towards goal

        if (moveTowardsGoal) {
          // Move towards the goal
          const bearing = this.calculateBearing(
            this.currentPosition!.coords.latitude,
            this.currentPosition!.coords.longitude,
            this.goalLocation!.latitude,
            this.goalLocation!.longitude
          );

          // Add some randomness to the bearing
          const randomizedBearing = bearing + (Math.random() * 40 - 20);

          // Move a random distance between 5 and 20 meters
          const moveDistance = Math.random() * 15 + 5;

          const newCoords = this.calculateDestinationPoint(
            this.currentPosition!.coords.latitude,
            this.currentPosition!.coords.longitude,
            moveDistance,
            randomizedBearing
          );

          // Update current position
          this.currentPosition = {
            coords: {
              latitude: newCoords.latitude,
              longitude: newCoords.longitude,
              accuracy: 10,
              altitude: null,
              altitudeAccuracy: null,
              heading: null,
              speed: null,
              toJSON: function() {
                return {
                  latitude: this.latitude,
                  longitude: this.longitude,
                  accuracy: this.accuracy,
                  altitude: this.altitude,
                  altitudeAccuracy: this.altitudeAccuracy,
                  heading: this.heading,
                  speed: this.speed
                };
              }
            },
            timestamp: Date.now(),
            toJSON: function() {
              return {
                coords: this.coords,
                timestamp: this.timestamp
              };
            }
          };
        } else {
          // Move in a random direction
          const randomBearing = Math.random() * 360;
          const moveDistance = Math.random() * 10 + 2;

          const newCoords = this.calculateDestinationPoint(
            this.currentPosition!.coords.latitude,
            this.currentPosition!.coords.longitude,
            moveDistance,
            randomBearing
          );

          // Update current position
          this.currentPosition = {
            coords: {
              latitude: newCoords.latitude,
              longitude: newCoords.longitude,
              accuracy: 10,
              altitude: null,
              altitudeAccuracy: null,
              heading: null,
              speed: null,
              toJSON: function() {
                return {
                  latitude: this.latitude,
                  longitude: this.longitude,
                  accuracy: this.accuracy,
                  altitude: this.altitude,
                  altitudeAccuracy: this.altitudeAccuracy,
                  heading: this.heading,
                  speed: this.speed
                };
              }
            },
            timestamp: Date.now(),
            toJSON: function() {
              return {
                coords: this.coords,
                timestamp: this.timestamp
              };
            }
          };
        }

        // Handle the position update
        if (this.currentPosition) {
          this.handlePositionUpdate(this.currentPosition);
        }
      });
  }

  // Stop location tracking
  private stopLocationTracking() {
    if (this.watchId) {
      navigator.geolocation.clearWatch(this.watchId);
      this.watchId = undefined;
    }

    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe();
    }
  }

  // Handle position update
  private handlePositionUpdate(position: GeolocationPosition) {
    if (!this.goalLocation || this.gameOver) return;

    // Store previous distance for comparison
    this.previousDistance = this.currentDistance;

    // Calculate new distance
    const distance = this.calculateDistance(position.coords, this.goalLocation);
    this.currentDistance = distance;

    // Update score based on movement
    if (this.previousDistance > this.currentDistance) {
      // Getting closer, add points
      this.score += Math.floor((this.previousDistance - this.currentDistance) / 10);
    }

    // Check if player has reached the target
    const targetRadius = this.config.targetRadius || this.defaultConfig.targetRadius;
    if (distance <= targetRadius) {
      this.handleGameWin();
    }

    // Emit position update event
    this.emitGameEvent('position_update', {
      distance: this.currentDistance,
      previousDistance: this.previousDistance,
      position: {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude
      }
    });
  }

  // Handle game win
  private handleGameWin() {
    this.gameOver = true;
    this.gameWon = true;
    this.stopLocationTracking();

    // Calculate bonus points based on time left and hints remaining
    const timeBonus = Math.floor(this.timeLeft / 10);
    const hintBonus = this.hintsRemaining * 50;
    this.score += timeBonus + hintBonus;

    // Emit game win event
    this.emitGameEvent('game_win', {
      score: this.score,
      timeLeft: this.timeLeft,
      hintsRemaining: this.hintsRemaining,
      timeBonus: timeBonus,
      hintBonus: hintBonus
    });
  }

  // Handle game loss (time ran out)
  private handleGameLoss() {
    this.gameOver = true;
    this.gameWon = false;
    this.stopLocationTracking();

    // Emit game loss event
    this.emitGameEvent('game_loss', {
      score: this.score,
      distance: this.currentDistance
    });
  }

  // Start the game timer
  private startTimer() {
    const timer = interval(1000);
    this.timerSubscription = timer
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        if (this.gameOver) return;

        this.timeLeft--;

        if (this.timeLeft <= 0) {
          this.handleGameLoss();
        }
      });
  }

  // Calculate distance between two coordinates using Haversine formula
  private calculateDistance(pos1: GeolocationCoordinates, pos2: GeolocationCoordinates): number {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = this.toRadians(pos1.latitude);
    const φ2 = this.toRadians(pos2.latitude);
    const Δφ = this.toRadians(pos2.latitude - pos1.latitude);
    const Δλ = this.toRadians(pos2.longitude - pos1.longitude);

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c; // Distance in meters
  }

  // Calculate bearing between two points
  private calculateBearing(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const φ1 = this.toRadians(lat1);
    const φ2 = this.toRadians(lat2);
    const Δλ = this.toRadians(lon2 - lon1);

    const y = Math.sin(Δλ) * Math.cos(φ2);
    const x = Math.cos(φ1) * Math.sin(φ2) -
              Math.sin(φ1) * Math.cos(φ2) * Math.cos(Δλ);

    let bearing = Math.atan2(y, x);
    bearing = this.toDegrees(bearing);
    bearing = (bearing + 360) % 360; // Normalize to 0-360

    return bearing;
  }

  // Calculate destination point given distance and bearing
  private calculateDestinationPoint(lat: number, lon: number, distance: number, bearing: number): {latitude: number, longitude: number} {
    const R = 6371e3; // Earth's radius in meters
    const δ = distance / R; // Angular distance
    const θ = this.toRadians(bearing);
    const φ1 = this.toRadians(lat);
    const λ1 = this.toRadians(lon);

    const φ2 = Math.asin(
      Math.sin(φ1) * Math.cos(δ) +
      Math.cos(φ1) * Math.sin(δ) * Math.cos(θ)
    );

    const λ2 = λ1 + Math.atan2(
      Math.sin(θ) * Math.sin(δ) * Math.cos(φ1),
      Math.cos(δ) - Math.sin(φ1) * Math.sin(φ2)
    );

    return {
      latitude: this.toDegrees(φ2),
      longitude: this.toDegrees(λ2)
    };
  }

  // Convert degrees to radians
  private toRadians(degrees: number): number {
    return degrees * Math.PI / 180;
  }

  // Convert radians to degrees
  private toDegrees(radians: number): number {
    return radians * 180 / Math.PI;
  }

  // Convert bearing to cardinal direction
  private getCardinalDirection(bearing: number): string {
    const directions = ['North', 'Northeast', 'East', 'Southeast', 'South', 'Southwest', 'West', 'Northwest'];
    const index = Math.round(bearing / 45) % 8;
    return directions[index];
  }

  // Emit game event
  private emitGameEvent(type: string, data: any): void {
    const event: GameEvent = {
      type: type,
      data: data,
      timestamp: new Date().toISOString(),
      gameId: this.gameId
    };

    this.gameEventEmitter.emit(event);
    console.log('Hot or Cold game event:', event);
  }
}