<div class="flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800">
  <!-- Score Display -->
  <games-score [scores]="getScoreItems()"></games-score>

  <!-- Game Title -->
  <div class="text-center mb-1">
    <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600">Hot or Cold</h1>
    <p class="text-xs text-slate-400 mb-1">Find the hidden location before time runs out!</p>
    <div *ngIf="isDemoMode" class="text-xs text-amber-500 mb-1">Demo Mode</div>
  </div>

  <!-- Game Board -->
  <div class="flex overflow-hidden flex-col flex-grow items-center p-1">
    <!-- Error Message -->
    <div *ngIf="errorMessage" class="bg-red-500 text-white p-2 rounded-md mb-2">
      {{ errorMessage }}
    </div>

    <!-- Game Start Prompt -->
    <div *ngIf="!gameStarted && !gameOver" class="flex flex-col items-center justify-center h-full">
      <div class="text-white text-center mb-4">
        <p class="text-lg mb-2">Welcome to Hot or Cold!</p>
        <p class="text-sm mb-4">Use your device's location to find the hidden target.</p>
        <p class="text-sm">Press "Start Game" when you're ready to begin.</p>
      </div>
    </div>

    <!-- Game Content -->
    <div *ngIf="gameStarted && !gameOver" class="flex flex-col items-center justify-center h-full w-full">
      <!-- Temperature Indicator -->
      <div class="relative w-64 h-64 mb-4">
        <div class="absolute inset-0 rounded-full" [ngStyle]="{'background-color': temperatureColor, 'opacity': '0.3'}"></div>
        <div class="absolute inset-0 flex items-center justify-center">
          <div class="text-center">
            <div class="text-4xl font-bold text-white">{{ temperatureText }}</div>
            <div class="text-lg text-white">{{ currentDistance === Infinity ? '---' : Math.round(currentDistance) + 'm' }}</div>
            <div class="text-3xl text-white">{{ directionIndicator }}</div>
          </div>
        </div>
      </div>

      <!-- Hint Display -->
      <div *ngIf="showHint" class="bg-blue-900 text-white p-3 rounded-lg mb-4 animate-pulse">
        <div class="text-center">
          <p class="text-lg font-bold">Hint</p>
          <p class="text-2xl">Target is {{ hintDirection }}</p>
        </div>
      </div>
    </div>

    <!-- Game Over Screen -->
    <win-lose-overlay
      *ngIf="gameOver"
      [won]="gameWon"
      [score]="score"
      [message]="gameWon ? 'You found the location!' : 'Time ran out!'"
      (restart)="restartGame()">
    </win-lose-overlay>
  </div>

  <!-- Game Controls -->
  <div class="game-controls mt-2">
    <lib-games-play-button [controls]="gameControls" (controlClicked)="handleControlClick($event)"></lib-games-play-button>

    <!-- Fallback Start Button (only shown if game not started) -->
    <div *ngIf="!gameStarted && !gameOver" class="flex justify-center mt-2">
      <button
        (click)="startGame()"
        class="p-3 text-white rounded-lg shadow-lg bg-gradient-to-br from-green-400 to-green-600 hover:scale-105 transition-all"
        style="min-width: 200px;"
      >
        Start Game
      </button>
    </div>
  </div>
</div>
