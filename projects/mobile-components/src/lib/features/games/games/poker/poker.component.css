/* Import shared game styles */
@import '../styles/game-styles.css';

:host {
  @apply block w-full h-full;
}

.game-container {
  @apply flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800;
}

/* Game Title */
.game-title {
  @apply text-center mb-1;
}

.game-title h1 {
  @apply text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-red-400 to-amber-500;
}

.game-title p {
  @apply text-xs text-slate-400 mb-1;
}

/* Game Info */
.game-info {
  @apply text-center text-sm text-slate-300 mb-2;
}

/* Game Board Container */
.game-board-container {
  @apply flex-grow flex justify-center items-center p-2;
  position: relative;
  z-index: 1;
}

.game-board {
  @apply p-4 rounded-xl bg-gradient-to-br from-green-900/80 to-green-800/80 border border-green-700 shadow-xl;
  width: 100%;
  max-width: 800px;
  overflow: auto;
}

/* Community Cards */
.community-cards {
  @apply mb-4 p-3 rounded-lg bg-green-800/50 border border-green-700;
}

.cards-container {
  @apply flex justify-center flex-wrap gap-2;
}

/* Card Styling */
.card {
  @apply flex flex-col justify-center items-center bg-white rounded-md shadow-md;
  width: 60px;
  height: 80px;
  position: relative;
  border: 1px solid #ccc;
  transition: transform 0.2s;
}

.card:hover {
  transform: translateY(-5px);
}

.card-value {
  @apply text-lg font-bold;
}

.card-suit {
  @apply text-xl;
}

.red-card {
  @apply text-red-600;
}

.card-back {
  @apply bg-gradient-to-br from-blue-800 to-blue-600;
}

.card-back-design {
  @apply w-full h-full flex items-center justify-center text-white text-opacity-50 text-2xl;
}

.card-back-design::after {
  content: "♠♥♦♣";
}

/* Players Container */
.players-container {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4 mt-4;
}

.player-box {
  @apply p-3 rounded-lg bg-slate-800/70 border border-slate-700 transition-all duration-300;
}

.active-player {
  @apply border-2 border-blue-500 bg-slate-800/90 shadow-lg shadow-blue-500/20;
}

.player-info {
  @apply mb-2;
}

.player-name {
  @apply text-lg font-bold text-white mb-1;
}

.player-stats {
  @apply flex justify-between text-sm text-slate-300;
}

.folded-status {
  @apply text-red-500 font-bold mt-1;
}

.player-cards {
  @apply flex justify-center gap-2 my-2;
}

/* Action Buttons */
.action-buttons {
  @apply flex justify-center gap-2 mt-3;
}

.action-button {
  @apply px-3 py-1 rounded-full text-white font-medium shadow-md transition-all duration-200 hover:scale-105;
}

.fold-button {
  @apply bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600;
}

.call-button {
  @apply bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600;
}

.raise-button {
  @apply bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600;
}

/* Winners Display */
.winners-display {
  @apply mt-4 p-3 rounded-lg bg-amber-900/30 border border-amber-800 text-center;
}

.winners-display h2 {
  @apply text-xl font-bold text-amber-400 mb-2;
}

.winners-display ul {
  @apply list-none p-0;
}

.winners-display li {
  @apply text-amber-300;
}

/* Controls */
.controls {
  @apply w-full max-w-md mt-4 px-4 mx-auto;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .card {
    width: 50px;
    height: 70px;
  }

  .card-value {
    @apply text-base;
  }

  .card-suit {
    @apply text-lg;
  }
}