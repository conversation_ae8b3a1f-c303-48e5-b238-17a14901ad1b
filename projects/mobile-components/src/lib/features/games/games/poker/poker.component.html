<div class="game-container">
  <!-- Score Display -->
  <games-score
    [scores]="[
      { title: 'Pot', number: pot },
      { title: 'Bet', number: currentBet },
      { title: 'Stage', number: gameStage }
    ]"
  ></games-score>

  <!-- Game Title -->
  <div class="game-title">
    <h1>Poker</h1>
    <p>Texas Hold'em</p>
  </div>

  <!-- Game Info -->
  <div class="game-info" *ngIf="showGameInfo">
    <p>Dealer: Player {{ dealerIndex + 1 }}</p>
  </div>

  <!-- Game Board -->
  <div class="game-board-container">
    <div class="game-board">
      <!-- Community Cards -->
      <div class="community-cards" *ngIf="communityCards.length > 0">
        <div class="cards-container">
          <div
            *ngFor="let card of communityCards"
            class="card"
            [ngClass]="{'red-card': card.suit === '♥' || card.suit === '♦'}"
          >
            <div class="card-value">{{ card.value }}</div>
            <div class="card-suit">{{ card.suit }}</div>
          </div>
        </div>
      </div>

      <!-- Players -->
      <div class="players-container">
        <div
          *ngFor="let player of players; let i = index"
          class="player-box"
          [class.active-player]="i === currentPlayerIndex"
        >
          <div class="player-info">
            <h3 class="player-name">{{ player.name }}</h3>
            <div class="player-stats">
              <span class="chips">Chips: {{ player.chips }}</span>
              <span class="bet">Bet: {{ player.currentBet }}</span>
            </div>
            <p *ngIf="player.folded" class="folded-status">Folded</p>
          </div>

          <div class="player-cards">
            <div
              *ngFor="let card of player.hand"
              class="card"
              [ngClass]="{'red-card': (player.isHuman || gameStage === 'showdown') && (card.suit === '♥' || card.suit === '♦'), 'card-back': !(player.isHuman || gameStage === 'showdown')}"
            >
              <ng-container *ngIf="player.isHuman || gameStage === 'showdown'; else hiddenCard">
                <div class="card-value">{{ card.value }}</div>
                <div class="card-suit">{{ card.suit }}</div>
              </ng-container>
              <ng-template #hiddenCard>
                <div class="card-back-design"></div>
              </ng-template>
            </div>
          </div>

          <!-- Action Buttons -->
          <div
            *ngIf="i === currentPlayerIndex && !player.folded && player.chips > 0 && player.isHuman"
            class="action-buttons"
          >
            <button
              (click)="playerAction('fold')"
              class="action-button fold-button"
            >
              Fold
            </button>
            <button
              (click)="playerAction('call')"
              class="action-button call-button"
            >
              Call
            </button>
            <button
              (click)="playerAction('raise', currentBet + minRaise)"
              class="action-button raise-button"
            >
              Raise
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Game Controls -->
  <div class="controls">
    <lib-games-play-button
      [controls]="gameControls"
      (controlClicked)="handleControlClick($event)"
    ></lib-games-play-button>
  </div>

  <!-- Winners Display -->
  <div *ngIf="winners.length > 0 && !isGameOver" class="winners-display">
    <h2>Winners</h2>
    <ul>
      <li *ngFor="let winner of winners">
        {{ winner.name }} wins {{ pot / winners.length }} chips!
      </li>
    </ul>
  </div>

  <!-- Win/Lose Overlay -->
  <lib-win-lose-overlay
    *ngIf="gameResult"
    [status]="gameResult"
    (restart)="startNewGame()"
    [message]="gameResult === 'win' ? 'You won the poker game!' : 'You lost all your chips!'"
  ></lib-win-lose-overlay>
</div>
