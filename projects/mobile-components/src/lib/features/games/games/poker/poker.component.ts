import { Component, OnInit, Input, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule, NgIf, NgFor } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { WinLoseOverlayComponent } from '../components/win-lose-overlay/win-lose-overlay.component';
import { GameControl, GamesPlayButtonComponent } from '../components/games-play-button/games-play-button.component';
import { Game, GameEvent } from 'lp-client-api';

interface Card {
  suit: string;
  value: string;
}

interface Player {
  name: string;
  hand: Card[];
  chips: number;
  currentBet: number;
  folded: boolean;
  isHuman: boolean;
}

@Component({
  selector: 'lib-poker',
  templateUrl: './poker.component.html',
  styleUrls: ['./poker.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    NgIf,
    NgF<PERSON>,
    GamesScoreComponent,
    WinLoseOverlayComponent,
    GamesPlayButtonComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class PokerComponent implements OnInit {
  @Input() gameId: number = 0;
  @Input() config: any;
  @Input() gameInstance: any;
  @Input() game: Game | undefined;

  deck: Card[] = [];
  players: Player[] = [];
  communityCards: Card[] = [];
  currentPlayerIndex: number = 0;
  pot: number = 0;
  currentBet: number = 0;
  gameStage: 'preflop' | 'flop' | 'turn' | 'river' | 'showdown' = 'preflop';
  smallBlind: number = 10;
  bigBlind: number = 20;
  dealerIndex: number = 0;
  minRaise: number = this.bigBlind;
  activePlayerCount: number = 0;
  winners: Player[] = [];
  isGameOver: boolean = false;
  showGameInfo: boolean = true;
  humanPlayerIndex: number = 0;
  gameResult: 'win' | 'lose' | null = null;

  // Game controls for the play button component
  gameControls: GameControl[] = [
    { name: 'restart', label: 'New Game', icon: 'refresh-outline', color: 'green' }
  ];

  ngOnInit() {
    this.startNewGame();
  }

  // Handle control button clicks
  handleControlClick(controlName: string) {
    if (controlName === 'restart') {
      this.startNewGame();
    }
  }

  initializeDeck() {
    const suits = ['♠', '♥', '♦', '♣'];
    const values = [
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '10',
      'J',
      'Q',
      'K',
      'A',
    ];
    for (const suit of suits) {
      for (const value of values) {
        this.deck.push({ suit, value });
      }
    }
    this.shuffleDeck();
  }

  shuffleDeck() {
    for (let i = this.deck.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [this.deck[i], this.deck[j]] = [this.deck[j], this.deck[i]];
    }
  }

  initializePlayers() {
    this.players = [
      {
        name: 'You',
        hand: [],
        chips: 1000,
        currentBet: 0,
        folded: false,
        isHuman: true,
      },
      {
        name: 'CPU 1',
        hand: [],
        chips: 1000,
        currentBet: 0,
        folded: false,
        isHuman: false,
      },
      {
        name: 'CPU 2',
        hand: [],
        chips: 1000,
        currentBet: 0,
        folded: false,
        isHuman: false,
      },
      {
        name: 'CPU 3',
        hand: [],
        chips: 1000,
        currentBet: 0,
        folded: false,
        isHuman: false,
      },
    ];
  }

  startNewGame() {
    this.isGameOver = false;
    this.gameResult = null;
    this.winners = [];
    this.initializePlayers();
    this.dealerIndex = Math.floor(Math.random() * this.players.length);
    this.startNewRound();
  }

  startNewRound() {
    this.resetRound();
    this.dealCards();
    this.postBlinds();
    this.activePlayerCount = this.players.length;
    this.startBettingRound();
  }

  resetRound() {
    this.deck = [];
    this.initializeDeck();
    this.communityCards = [];
    this.pot = 0;
    this.currentBet = 0;
    this.gameStage = 'preflop';
    this.players.forEach((player) => {
      player.hand = [];
      player.currentBet = 0;
      player.folded = false;
    });
  }

  dealCards() {
    for (const player of this.players) {
      player.hand = [this.deck.pop()!, this.deck.pop()!];
    }
  }

  postBlinds() {
    const smallBlindIndex = (this.dealerIndex + 1) % this.players.length;
    const bigBlindIndex = (this.dealerIndex + 2) % this.players.length;

    this.players[smallBlindIndex].chips -= this.smallBlind;
    this.players[smallBlindIndex].currentBet = this.smallBlind;
    this.players[bigBlindIndex].chips -= this.bigBlind;
    this.players[bigBlindIndex].currentBet = this.bigBlind;

    this.pot = this.smallBlind + this.bigBlind;
    this.currentBet = this.bigBlind;
    this.currentPlayerIndex = (bigBlindIndex + 1) % this.players.length;
    this.minRaise = this.bigBlind;
  }

  startBettingRound() {
    this.resetBetsForNewStage();
    this.currentPlayerIndex = (this.dealerIndex + 1) % this.players.length;
    this.moveToNextActivePlayer();
  }

  playerAction(action: 'fold' | 'call' | 'raise', raiseAmount?: number) {
    const player = this.players[this.currentPlayerIndex];

    switch (action) {
      case 'fold':
        player.folded = true;
        this.activePlayerCount--;
        break;
      case 'call':
        const callAmount = this.currentBet - player.currentBet;
        if (callAmount > 0) {
          this.betChips(player, callAmount);
        }
        break;
      case 'raise':
        if (raiseAmount && raiseAmount > this.currentBet) {
          const totalBetNeeded = raiseAmount - player.currentBet;
          if (totalBetNeeded <= player.chips) {
            this.betChips(player, totalBetNeeded);
            this.currentBet = raiseAmount;
            this.minRaise = this.bigBlind; // Reset minRaise for next betting round
          }
        }
        break;
    }

    if (this.activePlayerCount === 1) {
      this.endRound();
    } else if (this.isRoundComplete()) {
      this.advanceGameStage();
    } else {
      this.moveToNextActivePlayer();
    }
  }

  betChips(player: Player, amount: number) {
    const actualBet = Math.min(amount, player.chips);
    player.chips -= actualBet;
    player.currentBet += actualBet;
    this.pot += actualBet;
  }

  moveToNextActivePlayer() {
    do {
      this.currentPlayerIndex =
        (this.currentPlayerIndex + 1) % this.players.length;

      if (
        !this.players[this.currentPlayerIndex].isHuman &&
        !this.players[this.currentPlayerIndex].folded &&
        this.players[this.currentPlayerIndex].chips > 0
      ) {
        this.makeComputerMove(this.players[this.currentPlayerIndex]);
      }
    } while (
      this.players[this.currentPlayerIndex].folded ||
      this.players[this.currentPlayerIndex].chips === 0
    );
  }

  makeComputerMove(player: Player) {
    const random = Math.random();
    const callAmount = this.currentBet - player.currentBet;

    // Don't fold if there's no bet to call
    if (callAmount === 0) {
      if (random < 0.3) {
        this.playerAction('raise', this.currentBet + this.bigBlind);
      } else {
        this.playerAction('call');
      }
      return;
    }

    // More realistic betting behavior
    if (random < 0.2) {
      this.playerAction('fold');
    } else if (random < 0.8) {
      this.playerAction('call');
    } else {
      const raiseAmount = this.currentBet + this.bigBlind;
      this.playerAction('raise', raiseAmount);
    }
  }

  isRoundComplete(): boolean {
    const activePlayers = this.players.filter((p) => !p.folded && p.chips > 0);
    return activePlayers.every(
      (p) =>
        p.currentBet === this.currentBet || // Player has matched the current bet
        p.chips === 0 || // Player is all-in
        p.folded // Player has folded
    );
  }

  advanceGameStage() {
    switch (this.gameStage) {
      case 'preflop':
        this.gameStage = 'flop';
        this.dealCommunityCards(3);
        break;
      case 'flop':
        this.gameStage = 'turn';
        this.dealCommunityCards(1);
        break;
      case 'turn':
        this.gameStage = 'river';
        this.dealCommunityCards(1);
        break;
      case 'river':
        this.gameStage = 'showdown';
        this.determineWinner();
        return;
    }

    this.startBettingRound();
  }

  dealCommunityCards(count: number) {
    for (let i = 0; i < count; i++) {
      this.communityCards.push(this.deck.pop()!);
    }
  }

  determineWinner() {
    const activePlayers = this.players.filter((p) => !p.folded);
    // Implement hand evaluation logic here
    // For now, we'll just pick a random winner
    this.winners = [
      activePlayers[Math.floor(Math.random() * activePlayers.length)],
    ];
    this.distributeWinnings();
    this.endRound();
  }

  distributeWinnings() {
    const winAmount = Math.floor(this.pot / this.winners.length);
    this.winners.forEach((winner) => {
      winner.chips += winAmount;
    });
    this.pot = 0;
  }

  endRound() {
    if (this.players.filter((p) => p.chips > 0).length > 1) {
      this.dealerIndex = (this.dealerIndex + 1) % this.players.length;
      setTimeout(() => this.startNewRound(), 3000);
    } else {
      this.isGameOver = true;

      // Set game result based on whether the human player won
      const humanPlayer = this.players.find(p => p.isHuman);
      if (humanPlayer && humanPlayer.chips > 0) {
        this.gameResult = 'win';
      } else {
        this.gameResult = 'lose';
      }
    }
  }

  resetBetsForNewStage() {
    this.players.forEach((player) => {
      player.currentBet = 0;
    });
    this.currentBet = 0;
    this.minRaise = this.bigBlind; // Reset minimum raise amount
  }

  // Implement a basic hand evaluation function (this is a simplified version)
  evaluateHand(player: Player): number {
    const allCards = [...player.hand, ...this.communityCards];
    // Implement hand ranking logic here
    // For now, we'll just return a random score
    return Math.random();
  }

  toggleGameInfo() {
    this.showGameInfo = !this.showGameInfo;
  }
}
