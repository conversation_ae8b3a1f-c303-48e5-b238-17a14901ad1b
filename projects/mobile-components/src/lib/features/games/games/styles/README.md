# Game Styles

This directory contains reusable styling patterns for LP Angular games to maintain a consistent look and feel across all games.

## Usage

### 1. Import the styles in your component's CSS file:

```css
@import '../../styles/game-styles.css';
```

### 2. Use the provided CSS classes in your component's HTML:

#### For Game Popups:

```html
<div class="game-popup-overlay">
  <div class="game-popup-dialog">
    <!-- Mascot -->
    <div class="mascot-container">
      <img src="assets/images/games/mascot.png" alt="Game Mascot" class="mascot-bounce">
    </div>

    <!-- Title -->
    <h2 class="game-over-title">Game Over!</h2>

    <!-- Stats -->
    <div class="stats-container">
      <div class="grid grid-cols-2 gap-4">
        <div class="stat-item">
          <span class="stat-label">Score</span>
          <span class="stat-value stat-value-score">{{ score }}</span>
        </div>
        <!-- More stats... -->
      </div>
    </div>

    <!-- Action Button -->
    <button class="game-action-button game-action-button-primary">
      Play Again
    </button>
  </div>
</div>
```

#### For Score Component:

The games-score component already uses these styles. Just use it as normal:

```html
<games-score
  [scores]="[
    { title: 'Score', number: score },
    { title: 'Level', number: level },
    { title: 'Moves', number: moveCount }
  ]"
></games-score>
```

## Available Classes

### Popup Styles
- `game-popup-overlay`: Full-screen overlay for popups
- `game-popup-dialog`: Dialog container with dark gradient background
- `mascot-container`: Container for the mascot with glow effect
- `mascot-bounce`: Bouncing animation for the mascot
- `game-over-title`: Title with gradient text animation
- `stats-container`: Container for game stats
- `stat-item`: Individual stat item
- `stat-label`: Label for a stat
- `stat-value`: Value for a stat
- `stat-value-score`, `stat-value-time`, etc.: Color variants for stat values
- `game-action-button`: Base button style
- `game-action-button-primary`: Green gradient button
- `game-action-button-secondary`: Blue gradient button

### Score Component Styles
- `games-score-container`: Container for the score component
- `games-score-item`: Individual score item
- `games-score-title`: Title for a score
- `games-score-number`: Number for a score
- `score-type-Score`, `score-type-Time`, etc.: Color variants for score types

### Game Board Styles
- `game-board-container`: Container for the game board
- `game-board`: Game board with border and shadow

### Animations
- `scaleIn`: Scale in animation for popups
- `bounce`: Bounce animation for mascot
- `textShine`: Text shine animation for titles
- `slideIn`: Slide in animation for stats
- `popIn`: Pop in animation for result face
- `result-face`: Class for result face with animation
