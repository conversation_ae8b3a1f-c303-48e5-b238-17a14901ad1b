/* Game Styles - Reusable styling patterns for LP Angular games
 * This file contains common styling patterns that can be used across all games
 * to maintain a consistent look and feel.
 */

/* ===== Popup Styles ===== */

/* Game Over Dialog */
.game-popup-overlay {
  @apply flex fixed inset-0 z-50 justify-center items-center bg-black bg-opacity-70 backdrop-blur-sm;
}

.game-popup-dialog {
  @apply p-8 text-center bg-gradient-to-b from-slate-800 to-slate-900 rounded-2xl shadow-2xl transform border border-slate-700 max-w-md w-11/12;
  animation: scaleIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Mascot Container */
.mascot-container {
  @apply mb-4 relative;
}

.mascot-container::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-b from-green-500/20 to-transparent rounded-full blur-xl;
}

.mascot-bounce {
  @apply mx-auto w-24 h-24 relative z-10;
  animation: bounce 2s ease-in-out infinite;
}

/* Game Over Title */
.game-over-title {
  @apply mb-4 text-4xl font-bold text-white;
  animation: textShine 3s linear infinite;
  background: linear-gradient(to right, #ef4444, #f97316, #ef4444);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  background-size: 200% auto;
  text-shadow: 0 0 20px rgba(239, 68, 68, 0.7);
}

/* Stats Container */
.stats-container {
  @apply mb-6 p-4 bg-slate-700/50 rounded-xl border border-slate-600;
}

.stat-item {
  @apply bg-slate-800/70 p-3 rounded-lg border border-slate-700;
  animation: slideIn 0.5s ease-out both;
}

.stat-item:nth-child(2) {
  animation-delay: 0.1s;
}

.stat-item:nth-child(3) {
  animation-delay: 0.2s;
}

.stat-item:nth-child(4) {
  animation-delay: 0.3s;
}

.stat-label {
  @apply block text-slate-400;
}

.stat-value {
  @apply block text-2xl font-bold;
}

.stat-value-score {
  @apply text-green-400;
}

.stat-value-time {
  @apply text-amber-400;
}

.stat-value-level {
  @apply text-blue-400;
}

.stat-value-moves {
  @apply text-purple-400;
}

.stat-value-attempts {
  @apply text-red-400;
}

/* Action Buttons */
.game-action-button {
  @apply px-8 py-3 w-full text-xl font-bold text-white rounded-lg shadow-lg transition-all duration-300 transform;
}

.game-action-button-primary {
  @apply bg-gradient-to-r from-green-500 to-emerald-600 hover:scale-105 hover:from-green-600 hover:to-emerald-700 focus:outline-none focus:ring-4 focus:ring-green-400/50;
}

.game-action-button-secondary {
  @apply bg-gradient-to-r from-blue-500 to-indigo-600 hover:scale-105 hover:from-blue-600 hover:to-indigo-700 focus:outline-none focus:ring-4 focus:ring-blue-400/50;
}

/* ===== Score Component Styles ===== */
.games-score-container {
  @apply flex justify-between items-center px-4 py-2 space-x-2 h-full text-sm font-bold rounded-lg shadow-lg;
  @apply bg-gradient-to-r from-slate-800 to-slate-900 border border-slate-700;
  @apply transition-all duration-300 hover:shadow-slate-700/50;
}

.games-score-item {
  @apply flex flex-col items-center justify-center p-1 rounded-md transition-all duration-300;
  @apply bg-slate-700/50 border border-slate-600;
  @apply hover:bg-slate-700 hover:scale-105;
}

.games-score-title {
  @apply text-xs text-slate-400 mb-1;
}

.games-score-number {
  @apply text-base font-bold;
}

/* ===== Animations ===== */
@keyframes scaleIn {
  from {
    transform: scale(0.7);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes textShine {
  0% {
    background-position: 0% center;
  }
  100% {
    background-position: 200% center;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes popIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  70% {
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.result-face {
  animation: popIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) 0.2s both;
}

/* ===== Game Board Styles ===== */
.game-board-container {
  @apply flex overflow-hidden flex-col flex-grow items-center p-2;
}

.game-board {
  @apply w-full max-w-4xl rounded-lg overflow-hidden shadow-lg;
  @apply border-4 border-slate-700;
}
