<div [ngClass]="containerClasses" [class.win-animation]="gameWon">
  <!-- Header with Scores -->
  <games-score
    [scores]="[
      { title: 'Score', number: score },
      { title: 'High Score', number: highScore },
      { title: 'Moves', number: moveCount }
    ]"
  ></games-score>

  <!-- Game Title -->
  <div class="text-center mb-1">
    <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-amber-400 to-orange-500">2048</h1>
    <p class="text-xs text-slate-400 mb-1">Combine tiles to reach {{effectiveWinningTile}}!</p>
  </div>

  <!-- Game Board -->
  <div class="flex overflow-hidden flex-col flex-grow items-center justify-center p-1">
    <div class="w-full max-w-md mx-auto">
      <div class="game-board grid gap-2" 
           [ngStyle]="{
             'grid-template-columns': 'repeat(' + effectiveGridSize + ', 1fr)',
             'grid-auto-rows': '1fr'
           }"
           role="grid"
           [attr.aria-label]="'2048 game board with ' + effectiveGridSize + ' by ' + effectiveGridSize + ' cells'"
           [attr.aria-rowcount]="effectiveGridSize"
           [attr.aria-colcount]="effectiveGridSize">
        <ng-container *ngFor="let row of grid; let i = index">
          <div *ngFor="let cell of row; let j = index"
               class="tile relative aspect-square flex items-center justify-center font-bold rounded-xl transition-all"
               [ngClass]="{
                 'duration-200': !reducedMotion,
                 'duration-0': reducedMotion,
                 'tile-2': cell === 2,
                 'tile-4': cell === 4,
                 'tile-8': cell === 8,
                 'tile-16': cell === 16,
                 'tile-32': cell === 32,
                 'tile-64': cell === 64,
                 'tile-128': cell === 128,
                 'tile-256': cell === 256,
                 'tile-512': cell === 512,
                 'tile-1024': cell === 1024,
                 'tile-2048': cell === 2048,
                 'tile-new': isNewTile(i, j) && !reducedMotion,
                 'tile-merged': isMergedTile(i, j) && !reducedMotion,
                 'text-lg': largeText,
                 'text-base': !largeText
               }"
               [attr.aria-label]="cell !== 0 ? 'Tile with value ' + cell : 'Empty tile'"
               role="gridcell"
               [attr.aria-rowindex]="i + 1"
               [attr.aria-colindex]="j + 1">
            {{ cell !== 0 ? cell : '' }}
          </div>
        </ng-container>
      </div>

      <!-- Game Controls -->
      <div class="game-controls mt-2">
        <lib-games-play-button
          [controls]="gameControls"
          (controlClicked)="handleControlClick($event)"
        ></lib-games-play-button>
        
        <!-- Pause/Resume Button -->
        <div class="mt-2 flex justify-center" *ngIf="allowPause && gameStarted && !gameOver && !gameWon">
          <button 
            (click)="togglePause()"
            class="px-4 py-2 rounded-lg font-medium text-sm transition-colors"
            [ngClass]="{
              'bg-yellow-600 hover:bg-yellow-700 text-white': gameState === 'playing',
              'bg-green-600 hover:bg-green-700 text-white': gameState === 'paused'
            }"
            [attr.aria-label]="gameState === 'playing' ? 'Pause game' : 'Resume game'">
            {{ gameState === 'playing' ? 'Pause' : 'Resume' }}
          </button>
        </div>
      </div>

      <!-- Game Instructions -->
      <div class="mt-4 p-3 bg-slate-700/30 rounded-lg border border-slate-600/50 text-center"
           role="region"
           aria-label="Game controls">
        <p class="text-xs text-slate-400" *ngIf="!keyboardControls">
          <span class="text-amber-400">↑</span> <span class="text-slate-300">Swipe up</span> |
          <span class="text-amber-400">↓</span> <span class="text-slate-300">Swipe down</span> |
          <span class="text-amber-400">←</span> <span class="text-slate-300">Swipe left</span> |
          <span class="text-amber-400">→</span> <span class="text-slate-300">Swipe right</span>
        </p>
        <p class="text-xs text-slate-400" *ngIf="keyboardControls">
          <span class="text-amber-400">↑ ↓ ← →</span> <span class="text-slate-300">Arrow keys</span> |
          <span class="text-amber-400">WASD</span> <span class="text-slate-300">Movement</span> |
          <span class="text-amber-400">R</span> <span class="text-slate-300">Reset</span>
        </p>
      </div>
    </div>
  </div>

  <!-- Start Game Button -->
  <div *ngIf="!gameStarted && !gameOver && !gameWon" class="game-over-container">
    <div class="game-over-dialog">
      <!-- Mascot -->
      <div class="mb-4 mascot-container relative">
        <div class="absolute inset-0 bg-gradient-to-b from-amber-500/20 to-transparent rounded-full blur-xl"></div>
        <img src="assets/images/games/mascot.png" alt="Game Mascot" class="mx-auto w-24 h-24 mascot-bounce relative z-10">
      </div>

      <!-- Welcome Message -->
      <h2 class="mb-4 text-4xl font-bold text-white game-over-title">
        2048 Challenge
      </h2>

      <div class="mb-6 p-4 bg-slate-700/50 rounded-xl border border-slate-600 text-slate-300">
        <p class="mb-3">Combine tiles with the same number to create a tile with the sum. Reach the {{effectiveWinningTile}} tile to win!</p>
        <div class="grid grid-cols-4 gap-2 mt-4">
          <div class="tile-2 w-12 h-12 rounded-lg flex items-center justify-center text-sm">2</div>
          <div class="tile-2 w-12 h-12 rounded-lg flex items-center justify-center text-sm">2</div>
          <div class="text-amber-400 flex items-center justify-center">=</div>
          <div class="tile-4 w-12 h-12 rounded-lg flex items-center justify-center text-sm">4</div>
        </div>
      </div>

      <!-- Start Button -->
      <button (click)="startGame()"
              class="action-button primary-button"
              [attr.aria-label]="'Start 2048 game'">
        Start Game
      </button>
    </div>
  </div>

  <!-- Paused Game Dialog -->
  <div *ngIf="gameState === 'paused'" class="game-over-container">
    <div class="game-over-dialog">
      <!-- Pause Icon -->
      <div class="mb-4 text-center">
        <div class="w-16 h-16 mx-auto bg-yellow-600 rounded-full flex items-center justify-center">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M6 4a1 1 0 00-1 1v10a1 1 0 001 1h1a1 1 0 001-1V5a1 1 0 00-1-1H6zM12 4a1 1 0 00-1 1v10a1 1 0 001 1h1a1 1 0 001-1V5a1 1 0 00-1-1h-1z" clip-rule="evenodd"></path>
          </svg>
        </div>
      </div>

      <h2 class="mb-4 text-3xl font-bold text-white text-center">Game Paused</h2>
      
      <div class="mb-6 text-center text-slate-300">
        <p>Your progress is saved. Click resume to continue playing.</p>
      </div>

      <div class="flex gap-3 justify-center">
        <button (click)="togglePause()"
                class="action-button primary-button">
          Resume Game
        </button>
        <button (click)="resetGame()"
                class="action-button secondary-button">
          New Game
        </button>
      </div>
    </div>
  </div>

  <!-- Game Over Dialog -->
  <div *ngIf="gameOver || gameWon" class="game-over-container">
    <div class="game-over-dialog">
      <!-- Mascot -->
      <div class="mb-4 mascot-container relative">
        <div class="absolute inset-0 bg-gradient-to-b from-amber-500/20 to-transparent rounded-full blur-xl"></div>
        <img src="assets/images/games/mascot.png" alt="Game Mascot" class="mx-auto w-24 h-24 mascot-bounce relative z-10">
      </div>

      <!-- Result Message -->
      <h2 class="mb-4 text-4xl font-bold text-white game-over-title"
          [ngStyle]="{'background': gameWon ? 'linear-gradient(to right, #10b981, #34d399, #10b981)' : 'linear-gradient(to right, #ef4444, #f97316, #ef4444)'}">
        {{ gameWon ? 'You Win!' : 'Game Over!' }}
      </h2>

      <!-- Stats -->
      <div class="stats-display">
        <div class="stat-item">
          <span class="block text-slate-400">Score</span>
          <span class="block text-2xl font-bold text-green-400">{{ score }}</span>
        </div>
        <div class="stat-item">
          <span class="block text-slate-400">High Score</span>
          <span class="block text-2xl font-bold text-amber-400">{{ highScore }}</span>
        </div>
        <div class="stat-item">
          <span class="block text-slate-400">Moves</span>
          <span class="block text-2xl font-bold text-purple-400">{{ moveCount }}</span>
        </div>
        <div class="stat-item">
          <span class="block text-slate-400">Highest Tile</span>
          <span class="block text-2xl font-bold text-blue-400">{{ getHighestTile() }}</span>
        </div>
      </div>

      <!-- Restart Button -->
      <button (click)="resetGame()"
              class="action-button primary-button">
        Play Again
      </button>
    </div>

    <!-- Fireworks for win -->
    <div *ngIf="gameWon" class="fireworks">
      <div class="before"></div>
      <div class="after"></div>
    </div>
  </div>
</div>
