import { Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef, Input, Output, EventEmitter, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, computed, signal, HostListener } from '@angular/core';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ng<PERSON>lass, CommonModule } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { Game, GameEvent } from 'lp-client-api';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { GameControl, GamesPlayButtonComponent } from '../components/games-play-button/games-play-button.component';

// Enhanced interfaces for game events
interface GameStartEvent {
  gameId: string;
  timestamp: Date;
  config: Game2048Config;
}

interface GameEndEvent {
  gameId: string;
  timestamp: Date;
  score: number;
  moveCount: number;
  duration: number;
  result: 'win' | 'lose' | 'quit';
  highestTile: number;
}

interface GameScoreEvent {
  gameId: string;
  timestamp: Date;
  score: number;
  previousScore: number;
  moveCount: number;
}

interface GamePauseEvent {
  gameId: string;
  timestamp: Date;
  currentState: 'paused' | 'resumed';
}

interface GameErrorEvent {
  gameId: string;
  timestamp: Date;
  error: string;
  context?: any;
}

interface GameLevelEvent {
  gameId: string;
  timestamp: Date;
  level: number;
  previousLevel: number;
}

interface GameAchievementEvent {
  gameId: string;
  timestamp: Date;
  achievement: string;
  points: number;
}

interface Game2048Config {
  id: number;
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  frequencyAttempts: number;
  gridSize: number;
  initialTiles: number;
  winningTile: number;
  animationSpeed: number;
}

interface EmptyCell {
  i: number;
  j: number;
}

@Component({
  selector: 'lib-2048',
  templateUrl: './2048.component.html',
  styleUrls: ['./2048.component.css'],
  standalone: true,
  imports: [CommonModule, NgIf, NgFor, NgStyle, NgClass, GamesPlayButtonComponent, GamesScoreComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class Twenty48Component implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  private defaultConfig: Game2048Config = {
    id: 0,
    difficulty: 'EASY',
    frequency: 'DAILY',
    frequencyAttempts: 3,
    gridSize: 4,
    initialTiles: 2,
    winningTile: 2048,
    animationSpeed: 200
  };

  // === STANDARD GAME INPUTS ===
  // Base styling and configuration
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'accent' = 'default';
  @Input() theme: 'light' | 'dark' | 'auto' = 'auto';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Game mechanics
  @Input() difficulty: 'easy' | 'medium' | 'hard' | 'expert' = 'medium';
  @Input() maxLevel: number = 10;
  @Input() timeLimit: number = 0; // 0 = no limit
  @Input() lives: number = 3;
  @Input() maxLives: number = 5;

  // Scoring and rewards
  @Input() targetScore: number = 0;
  @Input() pointsMultiplier: number = 1;
  @Input() rewardPoints: number = 10;
  @Input() bonusPoints: number = 0;

  // Game state management
  @Input() gameState: 'idle' | 'playing' | 'paused' | 'completed' | 'failed' = 'idle';
  @Input() autoStart: boolean = false;
  @Input() autoReset: boolean = false;
  @Input() saveProgress: boolean = true;
  @Input() allowPause: boolean = true;

  // Sound and effects
  @Input() soundEnabled: boolean = true;
  @Input() effectsEnabled: boolean = true;
  @Input() musicEnabled: boolean = false;
  @Input() vibrationEnabled: boolean = true;

  // Performance settings
  @Input() frameRate: number = 60;
  @Input() enableOptimizations: boolean = true;
  @Input() reducedMotion: boolean = false;

  // Accessibility
  @Input() keyboardControls: boolean = true;
  @Input() screenReaderSupport: boolean = true;
  @Input() highContrast: boolean = false;
  @Input() largeText: boolean = false;

  // === GAME-SPECIFIC INPUTS ===
  @Input() customGridSize: number = 4;
  @Input() customWinningTile: number = 2048;
  @Input() customAnimationSpeed: number = 200;
  @Input() allowUndo: boolean = false;
  @Input() showBestScore: boolean = true;
  @Input() customInitialTiles: number = 2;

  // === GAME EVENT OUTPUTS ===
  @Output() gameStart = new EventEmitter<GameStartEvent>();
  @Output() gameEnd = new EventEmitter<GameEndEvent>();
  @Output() gameScore = new EventEmitter<GameScoreEvent>();
  @Output() gamePause = new EventEmitter<GamePauseEvent>();
  @Output() levelChange = new EventEmitter<GameLevelEvent>();
  @Output() gameAchievement = new EventEmitter<GameAchievementEvent>();
  @Output() gameError = new EventEmitter<GameErrorEvent>();

  // Game-specific events
  @Output() tileMove = new EventEmitter<{direction: string, moved: boolean}>();
  @Output() tilesMerge = new EventEmitter<{position: {i: number, j: number}, value: number}>();
  @Output() gameWin = new EventEmitter<{score: number, moves: number, duration: number}>();
  @Output() gameComplete = new EventEmitter<{score: number, moves: number, duration: number, highestTile: number}>();

  // Legacy inputs for backward compatibility
  @Input() gameId?: string;
  @Input() game: Game | undefined;
  @Input() gameInstance?: any;
  @Input()
  set config(value: Game2048Config) {
    console.log('Config input set with value:', value);
    this._config = {
      ...this.defaultConfig,
      ...value
    };
    console.log('Merged config:', this._config);
  }
  get config(): Game2048Config {
    return this._config;
  }
  private _config: Game2048Config;

  @Output() gameEvent = new EventEmitter<GameEvent>();

  // Computed styling properties
  get containerClasses(): string {
    const baseClasses = 'flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800';
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm', 
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };
    const variantClasses = {
      default: 'from-slate-900 to-slate-800',
      primary: 'from-blue-900 to-blue-800',
      secondary: 'from-gray-900 to-gray-800', 
      accent: 'from-purple-900 to-purple-800'
    };
    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-lg',
      lg: 'rounded-xl',
      full: 'rounded-full'
    };
    
    return `${baseClasses} ${sizeClasses[this.size]} ${variantClasses[this.variant]} ${roundedClasses[this.rounded]} ${this.className}`.trim();
  }

  get effectiveGridSize(): number {
    return this.customGridSize || this.config?.gridSize || 4;
  }

  get effectiveWinningTile(): number {
    return this.customWinningTile || this.config?.winningTile || 2048;
  }

  get effectiveAnimationSpeed(): number {
    return this.customAnimationSpeed || this.config?.animationSpeed || 200;
  }

  get effectiveInitialTiles(): number {
    return this.customInitialTiles || this.config?.initialTiles || 2;
  }

  // Game state
  grid: number[][] = [];
  score = 0;
  moveCount = 0;
  gameStarted = false;
  gameOver = false;
  gameWon = false;
  duration = 0;
  startTime = 0;
  highScore = 0;
  level = 1;

  // Animation tracking
  newTiles: {i: number, j: number}[] = [];
  mergedTiles: {i: number, j: number}[] = [];

  gameControls: GameControl[] = [
    { name: 'left', label: '', icon: 'chevron-back-outline' },
    { name: 'up', label: '', icon: 'chevron-up-outline' },
    { name: 'down', label: '', icon: 'chevron-down-outline' },
    { name: 'right', label: '', icon: 'chevron-forward-outline' },
  ];

  gameControls2: GameControl[] = [
    { name: 'reset', label: 'Reset', icon: 'refresh-outline' },
  ];

  constructor(private cdr: ChangeDetectorRef) {
    this._config = { ...this.defaultConfig };
  }

  @HostListener('window:keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    if (!this.keyboardControls || !this.gameStarted) return;

    const key = event.key.toLowerCase();
    let direction: 'left' | 'right' | 'up' | 'down' | null = null;

    switch (key) {
      case 'arrowleft':
      case 'a':
        direction = 'left';
        break;
      case 'arrowright': 
      case 'd':
        direction = 'right';
        break;
      case 'arrowup':
      case 'w':
        direction = 'up';
        break;
      case 'arrowdown':
      case 's':
        direction = 'down';
        break;
      case 'r':
        if (event.ctrlKey || event.metaKey) return; // Don't interfere with browser refresh
        this.resetGame();
        event.preventDefault();
        return;
      case ' ':
        if (this.allowPause) {
          this.togglePause();
          event.preventDefault();
        }
        return;
    }

    if (direction) {
      this.move(direction);
      event.preventDefault();
    }
  }

  public togglePause(): void {
    if (!this.allowPause) return;
    
    if (this.gameState === 'playing') {
      this.gameState = 'paused';
      this.gamePause.emit({
        gameId: this.gameId || 'game-2048',
        timestamp: new Date(),
        currentState: 'paused'
      });
    } else if (this.gameState === 'paused') {
      this.gameState = 'playing';
      this.gamePause.emit({
        gameId: this.gameId || 'game-2048',
        timestamp: new Date(),
        currentState: 'resumed'
      });
    }
  }

  ngOnInit(): void {
    console.log('Initializing 2048 game with config:', this.config);

    if (this.game?.gameConfig) {
      const gameConfig = this.game.gameConfig.find(config =>
        config.hasOwnProperty('gridSize') || config.hasOwnProperty('winningTile')
      ) as unknown as {
        id: number;
        difficulty: string;
        frequency: string;
        frequencyAttempts: number;
        gridSize: number;
        initialTiles: number;
        winningTile: number;
        animationSpeed: number;
      };

      if (gameConfig) {
        console.log('Found game config:', gameConfig);
        this.config = {
          ...this.config,
          id: gameConfig.id ?? this.config.id,
          difficulty: (gameConfig.difficulty as 'EASY' | 'MEDIUM' | 'HARD') ?? this.config.difficulty,
          frequency: (gameConfig.frequency as 'DAILY' | 'WEEKLY' | 'MONTHLY') ?? this.config.frequency,
          frequencyAttempts: gameConfig.frequencyAttempts ?? this.config.frequencyAttempts,
          gridSize: gameConfig.gridSize ?? this.config.gridSize,
          initialTiles: gameConfig.initialTiles ?? this.config.initialTiles,
          winningTile: gameConfig.winningTile ?? this.config.winningTile,
          animationSpeed: gameConfig.animationSpeed ?? this.config.animationSpeed
        };
      }
    }

    console.log('Final config before game init:', this.config);
    this.initializeGame();
    
    if (this.autoStart) {
      this.startGame();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  initializeGame(): void {
    const size = this.effectiveGridSize;
    console.log('Initializing game with grid size:', size);

    this.grid = Array(size)
      .fill(null)
      .map(() => Array(size).fill(0));

    this.score = 0;
    this.moveCount = 0;
    this.gameStarted = false;
    this.gameOver = false;
    this.gameWon = false;
    this.duration = 0;
    this.startTime = Date.now();
    console.log('Initial grid:', this.grid);
  }

  startGame(): void {
    console.log('Starting game');
    this.gameStarted = true;
    this.gameState = 'playing';

    // Add initial tiles
    for (let i = 0; i < this.effectiveInitialTiles; i++) {
      this.addRandomTile();
    }

    // Emit game start event
    this.gameStart.emit({
      gameId: this.gameId || 'game-2048',
      timestamp: new Date(),
      config: this.config
    });

    console.log('Game started with grid:', this.grid);
    this.cdr.detectChanges();
  }

  private addInitialTiles(): void {
    for (let i = 0; i < this.effectiveInitialTiles; i++) {
      this.addRandomTile();
    }
  }

  private addRandomTile(): void {
    const emptyCells: EmptyCell[] = [];
    for (let i = 0; i < this.grid.length; i++) {
      for (let j = 0; j < this.grid[i].length; j++) {
        if (this.grid[i][j] === 0) {
          emptyCells.push({ i, j });
        }
      }
    }

    if (emptyCells.length > 0) {
      const { i, j } = emptyCells[Math.floor(Math.random() * emptyCells.length)];
      const value = Math.random() < 0.9 ? 2 : 4;
      this.grid[i][j] = value;

      // Add to new tiles for animation
      this.newTiles.push({ i, j });

      // Clear new tiles after animation duration
      setTimeout(() => {
        this.newTiles = this.newTiles.filter(tile => !(tile.i === i && tile.j === j));
        this.cdr.detectChanges();
      }, this.effectiveAnimationSpeed + 100);

      console.log(`Added tile ${value} at position [${i}, ${j}]`);
      console.log('Current grid:', this.grid);
    }
  }

  // Methods for tile animations
  public isNewTile(i: number, j: number): boolean {
    return this.newTiles.some(tile => tile.i === i && tile.j === j);
  }

  public isMergedTile(i: number, j: number): boolean {
    return this.mergedTiles.some(tile => tile.i === i && tile.j === j);
  }

  private saveGameProgress(): void {
    this.duration = Math.floor((Date.now() - this.startTime) / 1000);

    const gameEvent: GameEvent = {
      id: 0, // Will be set by backend
      score: this.score,
      level: this.level,
      duration: this.duration,
      state: this.gameWon ? 'win' : (this.gameOver ? 'lose' : 'in_progress'),
      payload: JSON.stringify({
        grid: this.grid,
        moveCount: this.moveCount,
        highScore: Math.max(this.score, this.highScore)
      })
    };

    this.gameEvent.emit(gameEvent);
  }

  private checkGameState(): void {
    const highestTile = this.getHighestTile();
    const winningTile = this.effectiveWinningTile;

    if (highestTile >= winningTile && !this.gameWon) {
      this.gameWon = true;
      this.gameState = 'completed';
      this.gameWin.emit({
        score: this.score,
        moves: this.moveCount,
        duration: Math.floor((Date.now() - this.startTime) / 1000)
      });
      this.saveGameProgress();
    }

    // Check if no moves are possible
    if (this.isGameOver() && !this.gameOver) {
      this.gameOver = true;
      this.gameState = 'failed';
      this.gameComplete.emit({
        score: this.score,
        moves: this.moveCount,
        duration: Math.floor((Date.now() - this.startTime) / 1000),
        highestTile: this.getHighestTile()
      });
      this.saveGameProgress();
    }
  }

  private isGameOver(): boolean {
    const gridSize = this.grid.length;

    // Check for empty cells
    for (let i = 0; i < gridSize; i++) {
      for (let j = 0; j < gridSize; j++) {
        if (this.grid[i][j] === 0) {
          return false;
        }
      }
    }

    // Check for possible merges horizontally
    for (let i = 0; i < gridSize; i++) {
      for (let j = 0; j < gridSize - 1; j++) {
        if (this.grid[i][j] === this.grid[i][j + 1]) {
          return false;
        }
      }
    }

    // Check for possible merges vertically
    for (let i = 0; i < gridSize - 1; i++) {
      for (let j = 0; j < gridSize; j++) {
        if (this.grid[i][j] === this.grid[i + 1][j]) {
          return false;
        }
      }
    }

    return true;
  }

  // Make this public so it can be used in the template
  public getHighestTile(): number {
    return Math.max(
      ...this.grid.reduce<number[]>((acc, row) => [...acc, ...row], [])
    );
  }

  public handleControlClick(controlName: string): void {
    console.log('Control clicked:', controlName);
    if (controlName === 'reset') {
      this.resetGame();
      return;
    }

    this.move(controlName as 'left' | 'right' | 'up' | 'down');
  }

  private move(direction: 'left' | 'right' | 'up' | 'down'): void {
    if (!this.gameStarted || this.gameOver || this.gameWon || this.gameState === 'paused') {
      console.log('Game not in playable state:', { 
        started: this.gameStarted, 
        over: this.gameOver, 
        won: this.gameWon, 
        state: this.gameState 
      });
      return;
    }

    console.log('Moving:', direction);
    let moved = false;
    const newGrid = this.grid.map(row => [...row]);
    const previousScore = this.score;

    // Clear merged tiles from previous move
    this.mergedTiles = [];

    switch (direction) {
      case 'left':
        moved = this.moveLeft(newGrid);
        break;
      case 'right':
        moved = this.moveRight(newGrid);
        break;
      case 'up':
        moved = this.moveUp(newGrid);
        break;
      case 'down':
        moved = this.moveDown(newGrid);
        break;
    }

    // Emit tile move event
    this.tileMove.emit({ direction, moved });

    console.log('Move result:', { moved, direction });
    if (moved) {
      this.grid = newGrid;
      this.moveCount++;
      
      // Emit score change if score changed
      if (this.score !== previousScore) {
        this.gameScore.emit({
          gameId: this.gameId || 'game-2048',
          timestamp: new Date(),
          score: this.score,
          previousScore: previousScore,
          moveCount: this.moveCount
        });
      }

      this.addRandomTile();
      this.checkGameState();

      // Clear merged tiles after animation duration
      setTimeout(() => {
        this.mergedTiles = [];
        this.cdr.detectChanges();
      }, this.effectiveAnimationSpeed);

      console.log('Updated grid after move:', this.grid);
      this.cdr.detectChanges();
    }
  }

  public resetGame(): void {
    console.log('Resetting game');
    
    // Emit game end event if game was in progress
    if (this.gameStarted) {
      this.gameEnd.emit({
        gameId: this.gameId || 'game-2048',
        timestamp: new Date(),
        score: this.score,
        moveCount: this.moveCount,
        duration: Math.floor((Date.now() - this.startTime) / 1000),
        result: 'quit',
        highestTile: this.getHighestTile()
      });
    }

    this.gameStarted = false;
    this.gameOver = false;
    this.gameWon = false;
    this.score = 0;
    this.moveCount = 0;
    this.duration = 0;
    this.gameState = 'idle';
    this.initializeGame();

    // Auto restart if enabled
    if (this.autoReset && this.autoStart) {
      setTimeout(() => this.startGame(), 100);
    }
  }

  // Movement helper methods
  private moveLeft(grid: number[][]): boolean {
    let moved = false;
    const size = grid.length;

    for (let i = 0; i < size; i++) {
      // Remove zeros and get actual numbers
      let row = grid[i].filter(cell => cell !== 0);

      // Track original positions for merge animation
      const originalPositions = new Map<number, number>();
      row.forEach((value, index) => {
        // Find the original position in the grid
        for (let j = 0; j < size; j++) {
          if (grid[i][j] === value && !originalPositions.has(j)) {
            originalPositions.set(index, j);
            break;
          }
        }
      });

      // Merge tiles
      for (let j = 0; j < row.length - 1; j++) {
        if (row[j] === row[j + 1]) {
          row[j] *= 2;
          this.score += row[j];

          // Track merged tile for animation and emit event
          this.mergedTiles.push({ i, j });
          this.tilesMerge.emit({ position: { i, j }, value: row[j] });

          row.splice(j + 1, 1);
          moved = true;
        }
      }

      // Fill with zeros
      while (row.length < size) {
        row.push(0);
      }

      // Check if anything changed
      if (!moved) {
        moved = grid[i].some((cell, index) => cell !== row[index]);
      }

      // Update grid
      grid[i] = row;
    }

    return moved;
  }

  private moveRight(grid: number[][]): boolean {
    let moved = false;
    const size = grid.length;

    for (let i = 0; i < size; i++) {
      // Remove zeros and get actual numbers
      let row = grid[i].filter(cell => cell !== 0);

      // Merge tiles
      for (let j = row.length - 1; j > 0; j--) {
        if (row[j] === row[j - 1]) {
          row[j] *= 2;
          this.score += row[j];
          row.splice(j - 1, 1);
          row.unshift(0);
          moved = true;
        }
      }

      // Fill with zeros
      while (row.length < size) {
        row.unshift(0);
      }

      // Check if anything changed
      if (!moved) {
        moved = grid[i].some((cell, index) => cell !== row[index]);
      }

      // Update grid
      grid[i] = row;
    }

    return moved;
  }

  private moveUp(grid: number[][]): boolean {
    let moved = false;
    const size = grid.length;

    for (let j = 0; j < size; j++) {
      // Get column
      let column = grid.map(row => row[j]).filter(cell => cell !== 0);

      // Merge tiles
      for (let i = 0; i < column.length - 1; i++) {
        if (column[i] === column[i + 1]) {
          column[i] *= 2;
          this.score += column[i];
          column.splice(i + 1, 1);
          moved = true;
        }
      }

      // Fill with zeros
      while (column.length < size) {
        column.push(0);
      }

      // Check if anything changed
      if (!moved) {
        moved = grid.some((row, index) => row[j] !== column[index]);
      }

      // Update grid
      for (let i = 0; i < size; i++) {
        grid[i][j] = column[i];
      }
    }

    return moved;
  }

  private moveDown(grid: number[][]): boolean {
    let moved = false;
    const size = grid.length;

    for (let j = 0; j < size; j++) {
      // Get column
      let column = grid.map(row => row[j]).filter(cell => cell !== 0);

      // Merge tiles
      for (let i = column.length - 1; i > 0; i--) {
        if (column[i] === column[i - 1]) {
          column[i] *= 2;
          this.score += column[i];
          column.splice(i - 1, 1);
          column.unshift(0);
          moved = true;
        }
      }

      // Fill with zeros
      while (column.length < size) {
        column.unshift(0);
      }

      // Check if anything changed
      if (!moved) {
        moved = grid.some((row, index) => row[j] !== column[index]);
      }

      // Update grid
      for (let i = 0; i < size; i++) {
        grid[i][j] = column[i];
      }
    }

    return moved;
  }
}
