/* Import shared game styles */
@import '../styles/game-styles.css';

/* Game Board Styles */
.game-board {
  @apply bg-gradient-to-br from-slate-700 to-slate-800 p-4 rounded-xl shadow-xl border-2 border-slate-600;
  position: relative;
  overflow: hidden;
}

.game-board::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-purple-500/10;
  filter: blur(20px);
  z-index: 0;
}

/* Tile Base Styles */
.tile {
  @apply rounded-xl shadow-lg flex items-center justify-center font-bold text-center;
  transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;
  overflow: hidden;
  min-width: 60px;
  min-height: 60px;
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

.tile::before {
  content: '';
  @apply absolute inset-0 opacity-30;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
}

.tile::after {
  content: '';
  @apply absolute inset-0 opacity-20;
  background: linear-gradient(to bottom right, transparent 40%, rgba(0, 0, 0, 0.2) 100%);
}

/* Empty Tile */
.tile:empty {
  @apply bg-slate-700/50 border border-slate-600/50;
}

/* Tile Number Styles with Modern Gradients */
.tile-2 {
  @apply text-slate-700 text-2xl;
  background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.tile-4 {
  @apply text-slate-700 text-2xl;
  background: linear-gradient(135deg, #ede7e3, #e6d5c1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.tile-8 {
  @apply text-white text-2xl;
  background: linear-gradient(135deg, #ffaf7b, #f7936f);
  box-shadow: 0 4px 10px -2px rgba(247, 147, 111, 0.5);
}

.tile-16 {
  @apply text-white text-2xl;
  background: linear-gradient(135deg, #ff9966, #ff5e62);
  box-shadow: 0 4px 10px -2px rgba(255, 94, 98, 0.5);
}

.tile-32 {
  @apply text-white text-2xl;
  background: linear-gradient(135deg, #ff5e62, #ff2957);
  box-shadow: 0 4px 10px -2px rgba(255, 41, 87, 0.5);
}

.tile-64 {
  @apply text-white text-2xl;
  background: linear-gradient(135deg, #ff2957, #d61a4a);
  box-shadow: 0 4px 10px -2px rgba(214, 26, 74, 0.5);
}

.tile-128 {
  @apply text-white text-xl;
  background: linear-gradient(135deg, #ffd86f, #ffc641);
  box-shadow: 0 4px 10px -2px rgba(255, 198, 65, 0.6), 0 0 15px 2px rgba(255, 198, 65, 0.3);
}

.tile-256 {
  @apply text-white text-xl;
  background: linear-gradient(135deg, #ffc641, #ffb52e);
  box-shadow: 0 4px 10px -2px rgba(255, 181, 46, 0.6), 0 0 15px 2px rgba(255, 181, 46, 0.3);
}

.tile-512 {
  @apply text-white text-xl;
  background: linear-gradient(135deg, #ffb52e, #ff9900);
  box-shadow: 0 4px 10px -2px rgba(255, 153, 0, 0.6), 0 0 15px 2px rgba(255, 153, 0, 0.3);
}

.tile-1024 {
  @apply text-white text-lg;
  background: linear-gradient(135deg, #ff9900, #ff8000);
  box-shadow: 0 4px 10px -2px rgba(255, 128, 0, 0.6), 0 0 20px 2px rgba(255, 128, 0, 0.3);
}

.tile-2048 {
  @apply text-white text-lg;
  background: linear-gradient(135deg, #ff8000, #ff6600);
  box-shadow: 0 4px 10px -2px rgba(255, 102, 0, 0.6), 0 0 25px 5px rgba(255, 102, 0, 0.4);
  animation: glow 1.5s ease-in-out infinite alternate;
}

/* Animations */
.win-animation {
  animation: win-pulse 1.5s ease-in-out infinite;
}

@keyframes win-pulse {
  0% { transform: scale(1); box-shadow: 0 0 0 rgba(255, 102, 0, 0); }
  50% { transform: scale(1.02); box-shadow: 0 0 30px rgba(255, 102, 0, 0.5); }
  100% { transform: scale(1); box-shadow: 0 0 0 rgba(255, 102, 0, 0); }
}

@keyframes glow {
  from { box-shadow: 0 0 10px -5px rgba(255, 102, 0, 0.8); }
  to { box-shadow: 0 0 20px 5px rgba(255, 102, 0, 0.8); }
}

@keyframes appear {
  0% { opacity: 0; transform: scale(0.5); }
  60% { transform: scale(1.1); }
  100% { opacity: 1; transform: scale(1); }
}

@keyframes merge {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.tile-new {
  animation: appear 0.3s ease-in-out;
}

.tile-merged {
  animation: merge 0.3s ease-in-out;
}

/* Game Controls */
.game-controls {
  @apply flex justify-center;
}

/* Custom Game Over Dialog */
.game-over-container {
  @apply fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70 backdrop-blur-sm;
}

.game-over-dialog {
  @apply p-8 text-center bg-gradient-to-b from-slate-800 to-slate-900 rounded-2xl shadow-2xl border border-slate-700 max-w-md w-11/12;
  animation: scaleIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Stats Display */
.stats-display {
  @apply mb-6 p-4 bg-slate-700/50 rounded-xl border border-slate-600 grid grid-cols-2 gap-4;
}

.stat-item {
  @apply bg-slate-800/70 p-3 rounded-lg border border-slate-700;
  animation: slideIn 0.5s ease-out both;
}

.stat-item:nth-child(2) {
  animation-delay: 0.1s;
}

/* Action Buttons */
.action-button {
  @apply px-6 py-3 w-full text-lg font-bold text-white rounded-lg shadow-lg transition-all duration-300 transform;
}

.primary-button {
  @apply bg-gradient-to-r from-amber-500 to-orange-600 hover:scale-105 hover:from-amber-600 hover:to-orange-700 focus:outline-none focus:ring-4 focus:ring-orange-400/50;
}

.secondary-button {
  @apply bg-gradient-to-r from-blue-500 to-indigo-600 hover:scale-105 hover:from-blue-600 hover:to-indigo-700 focus:outline-none focus:ring-4 focus:ring-blue-400/50;
}

/* Fireworks Animation */
.fireworks {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.fireworks .before, .fireworks .after {
  position: absolute;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  box-shadow: 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff;
  animation: 1s bang ease-out infinite backwards, 1s gravity ease-in infinite backwards, 5s position linear infinite backwards;
}

.fireworks .after {
  animation-delay: 1.25s, 1.25s, 1.25s;
  animation-duration: 1.25s, 1.25s, 6.25s;
}

@keyframes bang {
  to {
    box-shadow: -70px -33px #ff3d00, 46px -5px #ff9500, 47px 0px #ffea00, 64px -69px #48ff00, -119px -76px #00ffd5, -98px -89px #00a2ff, 5px 6px #0400ff, 124px 3px #c800ff;
  }
}

@keyframes gravity {
  to {
    transform: translateY(200px);
    opacity: 0;
  }
}

@keyframes position {
  0%, 19.9% {
    margin-top: 10%;
    margin-left: 40%;
  }
  20%, 39.9% {
    margin-top: 40%;
    margin-left: 30%;
  }
  40%, 59.9% {
    margin-top: 20%;
    margin-left: 70%;
  }
  60%, 79.9% {
    margin-top: 30%;
    margin-left: 20%;
  }
  80%, 99.9% {
    margin-top: 30%;
    margin-left: 80%;
  }
}
