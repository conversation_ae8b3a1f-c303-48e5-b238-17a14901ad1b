<div
  #gameContainer
  class="game-container flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800"
>
  <!-- Game Header -->
  <div class="game-title text-center p-4">
    <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-500">Platformer</h1>
    <p class="text-xs text-slate-400 mb-1" *ngIf="isDemoMode">Playing in demo mode</p>
  </div>

  <!-- Score section -->
  <div class="score-container px-4">
    <games-score
      [scores]="[
        { title: 'Score', number: score },
        { title: 'High Score', number: highScore },
        { title: 'Time', number: formatTime(gameTime) },
        { title: 'Level', number: currentLevel },
        { title: 'Progress', number: levelProgress * 100 | number: '1.0-0' }
      ]"
    ></games-score>
  </div>

  <!-- Game World Container -->
  <div class="flex-1 overflow-hidden relative">
    <!-- Game World -->
    <div
      class="game-world absolute"
      [ngStyle]="{ transform: getGameWorldTransform() }"
      [style.width.px]="gameWorldWidth"
      [style.height.px]="gameWorldHeight"
    >
      <!-- Platforms -->
      <div
        *ngFor="let platform of platforms"
        class="platform absolute"
        [style.left.px]="platform.x"
        [style.top.px]="platform.y"
        [style.width.px]="platform.width"
        [style.height.px]="platform.height"
      ></div>

      <!-- Enemies -->
      <div
        *ngFor="let enemy of enemies"
        class="enemy absolute"
        [style.left.px]="enemy.x"
        [style.top.px]="enemy.y"
        [style.width.px]="enemy.width"
        [style.height.px]="enemy.height"
      ></div>

      <!-- Coins -->
      <div
        *ngFor="let coin of coins"
        class="coin absolute"
        [ngClass]="{ 'collected': coin.collected }"
        [style.left.px]="coin.x"
        [style.top.px]="coin.y"
        [style.width.px]="coin.width"
        [style.height.px]="coin.height"
      ></div>

      <!-- Player -->
      <div
        class="player absolute"
        [style.left.px]="player.x"
        [style.top.px]="player.y"
        [style.width.px]="player.width"
        [style.height.px]="player.height"
      ></div>
    </div>
  </div>

  <!-- Controls for Mobile -->
  <div class="controls-container">
    <div class="controls-wrapper">
      <button
        (touchstart)="onTouchStart('left')"
        (touchend)="onTouchEnd('left')"
        class="control-button control-left"
        aria-label="Move Left"
      >
        <span class="arrow-left"></span>
      </button>

      <button
        (touchstart)="onTouchStart('jump')"
        (touchend)="onTouchEnd('jump')"
        class="control-button control-jump"
        aria-label="Jump"
      >
        <span class="arrow-up"></span>
      </button>

      <button
        (touchstart)="onTouchStart('right')"
        (touchend)="onTouchEnd('right')"
        class="control-button control-right"
        aria-label="Move Right"
      >
        <span class="arrow-right"></span>
      </button>
    </div>
  </div>

  <!-- Game Result Overlay -->
  <lib-win-lose-overlay
    [status]="gameResult"
    (restart)="resetGame()"
    [message]="gameResult === 'lose' ? 'Your score was: ' + score : 'You completed all levels!'"
  ></lib-win-lose-overlay>
</div>
