/* Import shared game styles */
@import '../styles/game-styles.css';

:host {
  @apply block w-full h-full;
}

.game-container {
  @apply flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800;
}

.game-title {
  @apply text-center mb-1;
}

.game-title h1 {
  @apply text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-500;
}

.game-title p {
  @apply text-xs text-slate-400 mb-1;
}

.score-container {
  @apply w-full max-w-md mb-2 px-4 py-2;
}

.game-world {
  position: absolute;
  top: 0;
  left: 0;
  transition: transform 0.2s ease;
}

.platform {
  background: linear-gradient(135deg, #4d7c0f, #3f6212);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  border-top: 2px solid rgba(255, 255, 255, 0.2);
}

.enemy {
  background: radial-gradient(circle, #ef4444 0%, #b91c1c 100%);
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
  animation: pulse 2s infinite;
}

.coin {
  background: radial-gradient(circle, #fbbf24 0%, #d97706 100%);
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(251, 191, 36, 0.7);
  animation: spin 3s linear infinite;
}

.coin.collected {
  opacity: 0.3;
  animation: fadeOut 0.5s forwards;
}

.player {
  background: radial-gradient(circle, #10b981 0%, #047857 100%);
  border-radius: 10%;
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
  transition: transform 0.1s ease;
}

.controls-container {
  @apply fixed bottom-20 left-1/2 transform -translate-x-1/2 z-50;
  width: 280px;
}

.controls-wrapper {
  @apply flex justify-between items-center;
}

.control-button {
  @apply w-16 h-16 rounded-full flex justify-center items-center;
  background: rgba(30, 41, 59, 0.7);
  backdrop-filter: blur(4px);
  border: 2px solid rgba(100, 116, 139, 0.3);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.control-button:active {
  transform: scale(0.95);
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
}

.control-left {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.7), rgba(15, 23, 42, 0.7));
}

.control-jump {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.7), rgba(5, 150, 105, 0.7));
  width: 18px;
  height: 18px;
}

.control-right {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.7), rgba(15, 23, 42, 0.7));
}

.arrow-left, .arrow-right, .arrow-up {
  @apply block w-0 h-0;
  border-style: solid;
}

.arrow-left {
  border-width: 8px 12px 8px 0;
  border-color: transparent white transparent transparent;
}

.arrow-right {
  border-width: 8px 0 8px 12px;
  border-color: transparent transparent transparent white;
}

.arrow-up {
  border-width: 0 8px 12px 8px;
  border-color: transparent transparent white transparent;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes fadeOut {
  from { opacity: 0.5; }
  to { opacity: 0; }
}
