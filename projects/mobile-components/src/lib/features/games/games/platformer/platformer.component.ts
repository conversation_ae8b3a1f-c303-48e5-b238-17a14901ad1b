import {
  <PERSON>mpo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Host<PERSON><PERSON><PERSON>,
  ElementRef,
  ViewChild,
  AfterViewInit,
  ChangeDetectorRef,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
  Input,
  Output,
  EventEmitter
} from '@angular/core';
import { CommonModule, DecimalPipe } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { Game } from 'lp-client-api';
import { WinLoseOverlayComponent } from '../components/win-lose-overlay/win-lose-overlay.component';

interface Platform {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface Enemy {
  x: number;
  y: number;
  width: number;
  height: number;
  direction: number;
  speed: number;
}

interface Coin {
  x: number;
  y: number;
  width: number;
  height: number;
  collected: boolean;
}

interface PlatformerConfig {
  difficulty?: string;
  maxAttempts?: number;
  totalLevels?: number;
  jumpForce?: number;
  playerSpeed?: number;
  platformGapMin?: number;
  platformGapMax?: number;
  platformHeightVariance?: number;
  enemySpeed?: number;
  coinValue?: number;
}

interface PlatformerGameEvent {
  type: string;
  data: any;
  timestamp: string;
  gameId: string;
}

@Component({
  selector: 'lib-platformer',
  templateUrl: './platformer.component.html',
  styleUrls: ['./platformer.component.css'],
  standalone: true,
  imports: [CommonModule, DecimalPipe, GamesScoreComponent, WinLoseOverlayComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class PlatformerComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('gameContainer', { static: true }) gameContainer!: ElementRef;
  @Input() gameId: string = '';
  @Input() config: PlatformerConfig = {
    difficulty: 'MEDIUM',
    maxAttempts: 3,
    totalLevels: 3,
    jumpForce: 15,
    playerSpeed: 5,
    platformGapMin: 150,
    platformGapMax: 300,
    platformHeightVariance: 100,
    enemySpeed: 2,
    coinValue: 10
  };
  @Input() gameInstance: any = null;
  @Input() game?: Game;
  @Output() gameEventEmitter = new EventEmitter<PlatformerGameEvent>();

  constructor(private cdr: ChangeDetectorRef) {}

  // Player properties
  player = {
    x: 100,
    y: 100,
    width: 50,
    height: 50,
    velocityY: 0,
    velocityX: 0,
  };

  // Game elements
  platforms: Platform[] = [
    { x: 0, y: 300, width: 200, height: 20 },
    { x: 250, y: 250, width: 200, height: 20 },
    { x: 500, y: 200, width: 200, height: 20 },
  ];

  enemies: Enemy[] = [];
  coins: Coin[] = [];

  // Game loop
  gameLoop!: number;

  // Game state
  isJumping = false;
  showStats: boolean = true;
  score: number = 0;
  gameTime: number = 0;
  highScore: number = 0;
  levelProgress: number = 0; // Value between 0 and 1
  currentLevel: number = 1;
  totalLevels: number = 3;
  isGameOver: boolean = false;
  gameResult: 'win' | 'lose' | null = null;
  isDemoMode = false;
  errorMessage = '';
  startTime: Date | null = null;
  endTime: Date | null = null;

  // Control flags
  keys = { left: false, right: false };
  touchControls = { left: false, right: false, jump: false };

  // Game world dimensions
  gameWorldWidth = 1000; // Initial width covering starting platforms
  gameWorldHeight = 1500; // Adjust as needed

  // Double jump properties
  private jumpsRemaining: number = 2;
  private jumpForce: number = 15; // Adjust as needed

  // Platform generation parameters
  private platformGapMin: number = 150;
  private platformGapMax: number = 300;
  private platformHeightVariance: number = 100;

  // Dynamic buffer for world expansion
  private dynamicBuffer: number = 500;

  ngOnInit(): void {
    this.startTime = new Date();
    this.totalLevels = this.config.totalLevels || 3;
    this.jumpForce = this.config.jumpForce || 15;
    this.platformGapMin = this.config.platformGapMin || 150;
    this.platformGapMax = this.config.platformGapMax || 300;
    this.platformHeightVariance = this.config.platformHeightVariance || 100;

    if (this.gameInstance) {
      try {
        // Try to load game state from gameInstance
        this.loadGameState();
      } catch (error) {
        console.error('Error loading game state:', error);
        this.initializeDemoMode();
      }
    } else {
      this.initializeDemoMode();
    }
  }

  ngAfterViewInit(): void {
    this.cdr.detectChanges();
    console.log('View Initialized.');
  }

  ngOnDestroy(): void {
    // Clean up all timers and intervals
    this.cleanupTimers();
  }

  /**
   * Clean up all timers and intervals
   */
  cleanupTimers() {
    if (this.gameLoop) {
      clearInterval(this.gameLoop);
      this.gameLoop = 0;
    }
  }

  /**
   * Initialize the game in demo mode with default settings
   */
  initializeDemoMode() {
    console.log('Initializing Platformer game in demo mode');
    this.isDemoMode = true;
    this.errorMessage = 'Playing in demo mode';

    this.loadLevel(this.currentLevel);
    this.startGameLoop();

    this.emitGameEvent('game_start', {
      mode: 'demo',
      level: this.currentLevel
    });
  }

  /**
   * Load saved game state from gameInstance
   */
  loadGameState() {
    if (!this.gameInstance || !this.gameInstance.state) {
      throw new Error('No valid game state found');
    }

    const state = JSON.parse(this.gameInstance.state);
    this.currentLevel = state.level || 1;
    this.score = state.score || 0;
    this.gameTime = state.gameTime || 0;
    this.highScore = state.highScore || 0;

    // Load the level based on the saved state
    this.loadLevel(this.currentLevel);

    // If there's a saved player state, restore it
    if (state.player) {
      this.player = state.player;
    }

    // If there's a saved platforms state, restore it
    if (state.platforms) {
      this.platforms = state.platforms;
    }

    // If there's a saved enemies state, restore it
    if (state.enemies) {
      this.enemies = state.enemies;
    }

    // If there's a saved coins state, restore it
    if (state.coins) {
      this.coins = state.coins;
    }

    this.startGameLoop();

    this.emitGameEvent('game_resume', {
      level: this.currentLevel,
      score: this.score,
      gameTime: this.gameTime
    });
  }

  /**
   * Save the current game state
   */
  saveGameState() {
    if (this.isDemoMode) {
      console.log('Demo mode - game state not saved');
      return;
    }

    const state = {
      level: this.currentLevel,
      score: this.score,
      gameTime: this.gameTime,
      highScore: this.highScore,
      player: this.player,
      platforms: this.platforms,
      enemies: this.enemies,
      coins: this.coins
    };

    this.emitGameEvent('game_save', state);
  }

  /**
   * Emit a game event
   */
  emitGameEvent(type: string, data: any) {
    const event: PlatformerGameEvent = {
      type: type,
      data: data,
      timestamp: new Date().toISOString(),
      gameId: this.gameId
    };

    this.gameEventEmitter.emit(event);
    console.log('Platformer game event:', event);
  }

  // HostListeners for keyboard controls
  @HostListener('window:keydown', ['$event'])
  handleKeyDown(event: KeyboardEvent) {
    if (event.code === 'ArrowLeft') {
      this.keys.left = true;
    } else if (event.code === 'ArrowRight') {
      this.keys.right = true;
    } else if (event.code === 'Space') {
      if (this.jumpsRemaining > 0) {
        this.jump();
      }
    }
  }

  @HostListener('window:keyup', ['$event'])
  handleKeyUp(event: KeyboardEvent) {
    if (event.code === 'ArrowLeft') {
      this.keys.left = false;
    } else if (event.code === 'ArrowRight') {
      this.keys.right = false;
    }
  }

  // Touch control handlers
  onTouchStart(control: string) {
    if (control === 'left') {
      this.touchControls.left = true;
      // console.log('Touch start: left');
    }
    if (control === 'right') {
      this.touchControls.right = true;
      // console.log('Touch start: right');
    }
    if (control === 'jump' && this.jumpsRemaining > 0) {
      this.jump();
      // console.log('Touch start: jump');
    }
  }

  onTouchEnd(control: string) {
    if (control === 'left') {
      this.touchControls.left = false;
      // console.log('Touch end: left');
    }
    if (control === 'right') {
      this.touchControls.right = false;
      // console.log('Touch end: right');
    }
  }

  // Load level data
  loadLevel(level: number): void {
    // For simplicity, using static platforms
    this.platforms = [
      { x: 0, y: 300, width: 200, height: 20 },
      { x: 250, y: 250, width: 200, height: 20 },
      { x: 500, y: 200, width: 200, height: 20 },
    ];

    // Reset enemies and coins
    this.enemies = this.generateEnemies(level);
    this.coins = this.generateCoins(level);

    console.log(`Level ${level} loaded.`);
  }

  // Generate enemies based on level
  generateEnemies(level: number): Enemy[] {
    // Example: 3 enemies per level
    const enemies: Enemy[] = [];
    for (let i = 0; i < level + 2; i++) {
      enemies.push({
        x: Math.random() * 800,
        y: 200,
        width: 30,
        height: 30,
        direction: Math.random() < 0.5 ? -1 : 1,
        speed: 1 + level,
      });
    }
    return enemies;
  }

  // Generate coins based on level
  generateCoins(level: number): Coin[] {
    // Example: 5 coins per level
    const coins: Coin[] = [];
    for (let i = 0; i < level * 2 + 1; i++) {
      coins.push({
        x: Math.random() * 800,
        y: 150,
        width: 20,
        height: 20,
        collected: false,
      });
    }
    return coins;
  }

  // Start the game loop
  startGameLoop(): void {
    this.gameLoop = window.setInterval(() => {
      this.update();
    }, 1000 / 60); // 60 FPS
  }

  // Stop the game loop
  stopGameLoop(): void {
    clearInterval(this.gameLoop);
  }

  // Update game state
  update() {
    if (this.isGameOver) return;

    // Update game time
    this.gameTime += 1 / 60;

    // Update player horizontal movement
    const previousX = this.player.x;
    const previousY = this.player.y;

    if (this.keys.left || this.touchControls.left) {
      this.player.velocityX = -(this.config.playerSpeed || 5);
    } else if (this.keys.right || this.touchControls.right) {
      this.player.velocityX = (this.config.playerSpeed || 5);
    } else {
      this.player.velocityX = 0;
    }
    this.player.x += this.player.velocityX;

    // Apply gravity
    this.player.velocityY += 0.5;
    this.player.y += this.player.velocityY;

    // Collision detection with platforms
    this.checkPlatformCollisions();

    // Collision detection with enemies
    this.checkEnemyCollisions();

    // Collision detection with coins
    this.checkCoinCollisions();

    // Generate new platforms, enemies, and coins as needed
    this.generatePlatforms();

    // Update enemies positions
    this.updateEnemies();

    // Update level progress
    this.updateLevelProgress();

    // Check if player has fallen off
    this.checkPlayerFall();

    // Emit player movement event if position changed significantly
    if (Math.abs(this.player.x - previousX) > 1 || Math.abs(this.player.y - previousY) > 1) {
      this.emitGameEvent('player_move', {
        from: { x: previousX, y: previousY },
        to: { x: this.player.x, y: this.player.y },
        velocityX: this.player.velocityX,
        velocityY: this.player.velocityY
      });
    }

    // Save game state periodically (every 5 seconds)
    if (Math.floor(this.gameTime) % 5 === 0 && Math.floor(this.gameTime) > 0) {
      this.saveGameState();
    }

    // Manually trigger change detection
    this.cdr.detectChanges();
  }

  // Check collision with platforms
  checkPlatformCollisions(): void {
    let onPlatform = false;
    for (const platform of this.platforms) {
      if (
        this.player.x < platform.x + platform.width &&
        this.player.x + this.player.width > platform.x &&
        this.player.y + this.player.height < platform.y + 10 &&
        this.player.y + this.player.height + this.player.velocityY >= platform.y
      ) {
        this.player.y = platform.y - this.player.height;
        this.player.velocityY = 0;
        this.jumpsRemaining = 2;
        onPlatform = true;
        break;
      }
    }

    // If not on any platform, let gravity pull the player down
    if (!onPlatform && !this.isGameOver) {
      this.jumpsRemaining = 1; // Allow one jump if in the air
    }
  }

  // Check collision with enemies
  checkEnemyCollisions(): void {
    for (const enemy of this.enemies) {
      if (
        this.player.x < enemy.x + enemy.width &&
        this.player.x + this.player.width > enemy.x &&
        this.player.y < enemy.y + enemy.height &&
        this.player.y + this.player.height > enemy.y
      ) {
        this.triggerGameOver('lose');
        break;
      }
    }
  }

  // Check collision with coins
  checkCoinCollisions(): void {
    for (const coin of this.coins) {
      if (
        !coin.collected &&
        this.player.x < coin.x + coin.width &&
        this.player.x + this.player.width > coin.x &&
        this.player.y < coin.y + coin.height &&
        this.player.y + this.player.height > coin.y
      ) {
        coin.collected = true;
        const coinValue = this.config.coinValue || 10;
        this.score += coinValue;

        // Emit coin collected event
        this.emitGameEvent('coin_collected', {
          position: { x: coin.x, y: coin.y },
          value: coinValue,
          totalScore: this.score
        });
      }
    }
  }

  // Generate new platforms, enemies, and coins dynamically
  generatePlatforms(): void {
    let lastPlatform = this.platforms[this.platforms.length - 1];

    while (
      lastPlatform.x + lastPlatform.width <
      this.player.x + this.gameContainer.nativeElement.offsetWidth * 2
    ) {
      const gap =
        Math.random() * (this.platformGapMax - this.platformGapMin) +
        this.platformGapMin;
      const nextX = lastPlatform.x + lastPlatform.width + gap;
      const heightVariation =
        Math.random() * this.platformHeightVariance -
        this.platformHeightVariance / 2;
      const nextY = Math.min(
        Math.max(lastPlatform.y + heightVariation, 100),
        this.gameWorldHeight - 100
      );

      const newPlatform: Platform = {
        x: nextX,
        y: nextY,
        width: 200,
        height: 20,
      };
      this.platforms.push(newPlatform);
      // console.log(`Generated new platform at x: ${newPlatform.x}, y: ${newPlatform.y}`);

      // Generate enemies
      const addEnemy = Math.random() < 0.3;
      if (addEnemy) {
        const enemy: Enemy = {
          x: newPlatform.x + Math.random() * (newPlatform.width - 30),
          y: newPlatform.y - 30,
          width: 30,
          height: 30,
          direction: Math.random() < 0.5 ? -1 : 1,
          speed: 1 + this.currentLevel,
        };
        this.enemies.push(enemy);
        // console.log(`Generated enemy at x: ${enemy.x}, y: ${enemy.y}`);
      }

      // Generate coins
      const addCoin = Math.random() < 0.5;
      if (addCoin) {
        const coin: Coin = {
          x: newPlatform.x + Math.random() * (newPlatform.width - 20),
          y: newPlatform.y - 20,
          width: 20,
          height: 20,
          collected: false,
        };
        this.coins.push(coin);
        // console.log(`Generated coin at x: ${coin.x}, y: ${coin.y}`);
      }

      // Update lastPlatform reference
      lastPlatform = this.platforms[this.platforms.length - 1];
    }
  }

  // Update enemies positions
  updateEnemies(): void {
    for (const enemy of this.enemies) {
      enemy.x += enemy.speed * enemy.direction;
      // Reverse direction upon hitting platform boundaries
      const platform = this.platforms.find(
        (p) => enemy.x + enemy.width >= p.x && enemy.x <= p.x + p.width
      );
      if (!platform) {
        enemy.direction *= -1;
      }
    }
  }
  toggleStats(): void {
    this.showStats = !this.showStats;
  }
  // Update level progress
  updateLevelProgress(): void {
    this.levelProgress =
      this.player.x /
      (this.gameWorldWidth - this.gameContainer.nativeElement.offsetWidth);
    if (this.levelProgress >= 1 && this.currentLevel < this.totalLevels) {
      this.currentLevel++;
      this.loadLevel(this.currentLevel);
    } else if (
      this.levelProgress >= 1 &&
      this.currentLevel === this.totalLevels
    ) {
      this.triggerGameOver('win');
    }
  }
  formatTime(time: number): string {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds
      .toString()
      .padStart(2, '0')}`;
  }
  // Check if the player has fallen off the platforms
  checkPlayerFall(): void {
    if (
      this.player.y > this.gameWorldHeight ||
      this.player.y + this.player.height < 0
    ) {
      this.triggerGameOver('lose');
    }
  }

  // Handle jumping
  jump(): void {
    if (this.jumpsRemaining > 0) {
      this.player.velocityY = -this.jumpForce;
      this.jumpsRemaining--;
      // console.log(`Jump performed. Jumps remaining: ${this.jumpsRemaining}`);
    }
  }

  // Trigger game over
  triggerGameOver(result: 'win' | 'lose'): void {
    this.isGameOver = true;
    this.gameResult = result;
    this.endTime = new Date();
    this.stopGameLoop();

    // Calculate game duration in seconds
    let duration = 0;
    if (this.startTime && this.endTime) {
      duration = Math.floor((this.endTime.getTime() - this.startTime.getTime()) / 1000);
    }

    console.log(`Game Over: ${result}`);

    if (result === 'lose') {
      // Update high score if current score is higher
      if (this.score > this.highScore) {
        this.highScore = this.score;
      }

      // Emit game over event
      this.emitGameEvent('game_over', {
        result: 'lose',
        score: this.score,
        highScore: this.highScore,
        level: this.currentLevel,
        duration: duration
      });
    } else {
      // Emit game complete event
      this.emitGameEvent('game_complete', {
        result: 'win',
        score: this.score,
        highScore: this.highScore,
        level: this.currentLevel,
        duration: duration
      });
    }
  }

  // Reset the game
  resetGame(): void {
    this.isGameOver = false;
    this.gameResult = null;
    this.currentLevel = 1;
    this.score = 0;
    this.gameTime = 0;
    this.startTime = new Date();
    this.endTime = null;

    this.player.x =
      this.platforms[0].x + (this.platforms[0].width - this.player.width) / 2;
    this.player.y = this.platforms[0].y - this.player.height;
    this.player.velocityY = 0;
    this.jumpsRemaining = 2;

    this.platforms = [
      { x: 0, y: 300, width: 200, height: 20 },
      { x: 250, y: 250, width: 200, height: 20 },
      { x: 500, y: 200, width: 200, height: 20 },
    ];

    this.enemies = this.generateEnemies(this.currentLevel);
    this.coins = this.generateCoins(this.currentLevel);
    this.gameWorldWidth = 1000;
    this.gameWorldHeight = 1500;

    this.startGameLoop();

    // Emit game reset event
    this.emitGameEvent('game_reset', {
      level: this.currentLevel,
      score: this.score,
      highScore: this.highScore
    });

    console.log('Game reset completed.');
  }

  // Calculate transform for the game world to center the player
  getGameWorldTransform(): string {
    if (!this.gameContainer) {
      return 'translate(0px, 0px)';
    }

    const viewportWidth = this.gameContainer.nativeElement.offsetWidth;
    const viewportHeight = this.gameContainer.nativeElement.offsetHeight;

    // Center positions
    const centerX = viewportWidth / 2 - this.player.width / 2;
    const centerY = viewportHeight / 2 - this.player.height / 2;

    let offsetX = centerX - this.player.x;
    let offsetY = centerY - this.player.y;

    // Constrain offsetX and offsetY to prevent showing empty space
    const maxOffsetX = 0;
    const maxOffsetY = 0;
    const minOffsetX = viewportWidth - this.gameWorldWidth;
    const minOffsetY = viewportHeight - this.gameWorldHeight;

    offsetX = Math.min(Math.max(offsetX, minOffsetX), maxOffsetX);
    offsetY = Math.min(Math.max(offsetY, minOffsetY), maxOffsetY);

    // If game is over, lock the camera to show the game over message
    if (this.isGameOver) {
      offsetY = 0;
    }

    // console.log(`OffsetX: ${offsetX}, OffsetY: ${offsetY}`);

    return `translate(${offsetX}px, ${offsetY}px)`;
  }
}

// Define interfaces for better type safety
interface GameObject {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface Platform extends GameObject {}

interface Enemy extends GameObject {
  direction: number;
  speed: number;
}

interface Coin extends GameObject {
  collected: boolean;
}
