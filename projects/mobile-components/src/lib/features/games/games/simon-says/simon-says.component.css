/* Import shared game styles */
@import '../styles/game-styles.css';

:host {
  @apply block w-full h-full;
}

.game-container {
  @apply flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800;
}

/* Game Title */
.game-title {
  @apply text-center mb-1;
}

.game-title h1 {
  @apply text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-red-400 to-blue-500;
}

.game-title p {
  @apply text-xs text-slate-400 mb-1;
}

/* Simon Board */
.simon-board-container {
  display: flex;
  flex-grow: 1;
  justify-content: center;
  align-items: center;
  padding: 1rem;
}

.simon-board {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 9999px;
  background-color: #1e293b; /* slate-800 */
  border: 4px solid #334155; /* slate-700 */
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  width: min(90vw, 350px);
  height: min(90vw, 350px);
  position: relative;
}

.simon-board::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(to right, rgba(51, 65, 85, 0.3), rgba(71, 85, 105, 0.3));
  border-radius: 9999px;
  filter: blur(10px);
  z-index: 0;
}

.simon-button {
  width: 100%;
  height: 100%;
  border-radius: 9999px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-width: 4px;
  position: relative;
  z-index: 10;
  overflow: hidden;
  cursor: pointer;
}

.simon-button::before {
  content: '';
  position: absolute;
  inset: 0;
  opacity: 0.3;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
}

.simon-red {
  background: linear-gradient(to bottom right, #ef4444, #b91c1c); /* from-red-500 to-red-700 */
  border-color: #f87171; /* border-red-400 */
  border-top-left-radius: 9999px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0;
}

.simon-blue {
  background: linear-gradient(to bottom right, #3b82f6, #1d4ed8); /* from-blue-500 to-blue-700 */
  border-color: #60a5fa; /* border-blue-400 */
  border-top-right-radius: 9999px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}

.simon-green {
  background: linear-gradient(to bottom right, #22c55e, #15803d); /* from-green-500 to-green-700 */
  border-color: #4ade80; /* border-green-400 */
  border-bottom-left-radius: 9999px;
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}

.simon-yellow {
  background: linear-gradient(to bottom right, #eab308, #a16207); /* from-yellow-500 to-yellow-700 */
  border-color: #facc15; /* border-yellow-400 */
  border-bottom-right-radius: 9999px;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}

.simon-button.active {
  filter: brightness(1.5);
  transform: scale(1.05);
  box-shadow: 0 0 20px currentColor;
  transition: all 0.2s ease;
}

/* Controls */
.controls {
  @apply w-full max-w-md mt-4 px-4 mx-auto;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .simon-board {
    width: min(85vw, 300px);
    height: min(85vw, 300px);
    @apply gap-2 p-4;
  }
}