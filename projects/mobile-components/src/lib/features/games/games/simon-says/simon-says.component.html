<div class="game-container">
  <!-- Score Display -->
  <games-score
    [scores]="[
      { title: 'Score', number: score },
      { title: 'High Score', number: highScore },
      { title: 'Level', number: level }
    ]"
  ></games-score>

  <!-- Game Title -->
  <div class="game-title">
    <h1><PERSON></h1>
    <p>Repeat the pattern to advance!</p>
  </div>

  <!-- <PERSON> Says Game Board -->
  <div class="simon-board-container">
    <div class="simon-board">
      <!-- <PERSON> (top-left) -->
      <button
        id="color-0"
        (click)="onColorClick(0)"
        [disabled]="isPlaying"
        class="simon-button simon-red"
        [class.active]="activeColor === 0"
      ></button>

      <!-- <PERSON> (top-right) -->
      <button
        id="color-1"
        (click)="onColorClick(1)"
        [disabled]="isPlaying"
        class="simon-button simon-blue"
        [class.active]="activeColor === 1"
      ></button>

      <!-- <PERSON> (bottom-left) -->
      <button
        id="color-2"
        (click)="onColorClick(2)"
        [disabled]="isPlaying"
        class="simon-button simon-green"
        [class.active]="activeColor === 2"
      ></button>

      <!-- Yellow Button (bottom-right) -->
      <button
        id="color-3"
        (click)="onColorClick(3)"
        [disabled]="isPlaying"
        class="simon-button simon-yellow"
        [class.active]="activeColor === 3"
      ></button>
    </div>
  </div>

  <!-- Game Controls -->
  <div class="controls">
    <lib-games-play-button
      [controls]="gameControls"
      (controlClicked)="handleControlClick($event)"
    ></lib-games-play-button>
  </div>

  <!-- Win/Lose Overlay -->
  <lib-win-lose-overlay
    *ngIf="gameResult"
    [status]="gameResult"
    (restart)="restartGame()"
    [message]="gameResult === 'win' ? 'You completed all 10 levels!' : 'You reached level ' + level"
  ></lib-win-lose-overlay>
</div>
