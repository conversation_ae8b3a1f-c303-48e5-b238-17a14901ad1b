# Simon Says Game

A modern, visually appealing memory game where players must repeat an increasingly complex sequence of colors.

![<PERSON> Says Game](assets/images/games/simon-says.jpeg)

## Game Overview

Simon Says is a classic memory game where players must watch and remember a sequence of colors, then repeat it back in the correct order. The game features:

- Sequence-based gameplay with increasing difficulty
- Score tracking and high score persistence
- Modern UI with gradient buttons and visual effects
- Win/lose conditions
- Responsive design for different screen sizes

## How to Play

1. **Objective**: Successfully repeat the sequence of colors shown by the game
2. **Starting**: Click the "Start" button to begin the game
3. **Gameplay**:
   - Watch as the game highlights a sequence of colors
   - After the sequence is shown, click the colors in the same order
   - If you get the sequence correct, the game will add one more color
   - Each level adds one more step to the sequence
4. **Scoring**:
   - You earn points for each level completed
   - Score increases based on the level (level * 10 points)
   - High score is saved between game sessions
5. **Winning**: Complete 10 levels to win the game
6. **Losing**: Make a mistake in the sequence to lose the game

## Game Controls

- **Start**: Begins a new game
- **Reset**: Restarts the game from the beginning

## Game Mechanics

### Sequence Generation

- Each level adds one random color to the sequence
- Colors are chosen randomly from red, blue, green, and yellow
- The sequence is played back to the player with visual highlighting
- The player must then repeat the sequence by clicking the colors

### Scoring System

- Score increases by (level * 10) points for each completed level
- High score is saved to localStorage for persistence between sessions
- Current level is displayed to track progress

### Win/Lose Conditions

- **Win**: Successfully complete 10 levels
- **Lose**: Make a mistake in the sequence

## UI Components

The Simon Says game uses several shared components:

1. **GamesScoreComponent**: Displays score, high score, and level information
2. **GamesPlayButtonComponent**: Provides game control buttons
3. **WinLoseOverlayComponent**: Shows game over screen with win/lose message

## Technical Implementation

### Component Structure

The Simon Says game is implemented as a standalone Angular component with the following files:

- `simon-says.component.ts`: Game logic and state management
- `simon-says.component.html`: Game UI template
- `simon-says.component.css`: Game-specific styling

### Key Features

1. **Modern UI Design**:
   - Gradient backgrounds and borders
   - Rounded corners and shadows
   - Consistent color scheme with other games
   - Responsive layout for different screen sizes

2. **Game State Management**:
   - Tracks sequence, player input, score, and level
   - Manages game lifecycle (start, play sequence, player input, game over)
   - Handles win/lose conditions

3. **Interactive Elements**:
   - Color buttons with visual feedback
   - Game control buttons
   - Win/lose overlay

4. **Sequence Playback**:
   - Asynchronous sequence playback with visual highlighting
   - Delay between colors for better visibility
   - Visual feedback for player input

### Styling

The game uses a combination of:
- Shared game styles from `game-styles.css`
- Game-specific styles in `simon-says.component.css`
- Tailwind CSS utility classes

## Integration

The Simon Says game is registered in the game system with the code `simon-says` and can be accessed through:

1. The All Games View
2. Direct URL: `/games/single?game=simon-says`

## Future Enhancements

Potential improvements for future versions:

1. Add sound effects for button presses
2. Implement difficulty levels (faster sequences at higher levels)
3. Add more visual feedback and animations
4. Create different game modes (e.g., reverse mode, speed mode)
5. Add a practice mode for learning

## Troubleshooting

If the game doesn't load properly, check:

1. The URL parameter format (should be `game=simon-says`)
2. Console for any JavaScript errors
3. Network requests for missing assets
4. Game configuration in `game.service.ts`
