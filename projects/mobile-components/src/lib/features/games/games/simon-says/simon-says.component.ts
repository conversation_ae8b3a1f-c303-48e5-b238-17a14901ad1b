import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule, NgIf } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { WinLoseOverlayComponent } from '../components/win-lose-overlay/win-lose-overlay.component';
import { GameControl, GamesPlayButtonComponent } from '../components/games-play-button/games-play-button.component';
import { Subject } from 'rxjs';

@Component({
  selector: 'lib-simon-says',
  templateUrl: './simon-says.component.html',
  styleUrls: ['./simon-says.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    NgIf,
    GamesScoreComponent,
    WinLoseOverlayComponent,
    GamesPlayButtonComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class Simon<PERSON>aysComponent implements OnInit, On<PERSON><PERSON>roy {
  private destroy$ = new Subject<void>();
  private gameInterval: any;

  // Game state
  colors: string[] = ['red', 'blue', 'green', 'yellow'];
  sequence: number[] = [];
  playerSequence: number[] = [];
  isPlaying: boolean = false;
  gameOver: boolean = false;
  level: number = 1;
  score: number = 0;
  highScore: number = 0;
  gameResult: 'win' | 'lose' | null = null;
  activeColor: number | null = null;

  // Game controls
  gameControls: GameControl[] = [
    { name: 'start', label: 'Start', icon: 'play-outline' },
    { name: 'reset', label: 'Reset', icon: 'refresh-outline' }
  ];

  ngOnInit() {
    // Load high score from localStorage if available
    const savedHighScore = localStorage.getItem('simonSaysHighScore');
    if (savedHighScore) {
      this.highScore = parseInt(savedHighScore);
    }

    this.resetGame();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    this.clearGameInterval();
  }

  handleControlClick(controlName: string) {
    switch (controlName) {
      case 'start':
        if (!this.isPlaying && !this.gameOver) {
          this.startGame();
        }
        break;
      case 'reset':
        this.resetGame();
        break;
    }
  }

  resetGame() {
    this.clearGameInterval();
    this.sequence = [];
    this.playerSequence = [];
    this.isPlaying = false;
    this.gameOver = false;
    this.gameResult = null;
    this.level = 1;
    this.score = 0;
    this.activeColor = null;
  }

  startGame() {
    this.resetGame();
    this.addToSequence();
  }

  addToSequence() {
    const randomIndex = Math.floor(Math.random() * this.colors.length);
    this.sequence.push(randomIndex);
    this.playSequence();
  }

  async playSequence() {
    this.isPlaying = true;

    // Delay before starting the sequence
    await this.delay(500);

    for (let i = 0; i < this.sequence.length; i++) {
      const colorIndex = this.sequence[i];
      await this.flashColor(colorIndex);
      await this.delay(300); // Delay between colors
    }

    this.isPlaying = false;
  }

  async flashColor(index: number) {
    // Set the active color to highlight the button
    this.activeColor = index;

    // Keep the button highlighted for 500ms
    await this.delay(500);

    // Remove the highlight
    this.activeColor = null;

    // Small delay after turning off the highlight
    return this.delay(100);
  }

  delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  onColorClick(index: number) {
    if (this.isPlaying || this.gameOver) return;

    this.playerSequence.push(index);
    this.flashColor(index);

    // Check if the player's move is correct
    const currentIndex = this.playerSequence.length - 1;
    if (this.playerSequence[currentIndex] !== this.sequence[currentIndex]) {
      this.endGame('lose');
      return;
    }

    // Check if the player has completed the current sequence
    if (this.playerSequence.length === this.sequence.length) {
      this.score += this.level * 10;
      this.level++;

      // Update high score if needed
      if (this.score > this.highScore) {
        this.highScore = this.score;
        localStorage.setItem('simonSaysHighScore', this.highScore.toString());
      }

      // Reset player sequence for the next round
      this.playerSequence = [];

      // Check if player has reached level 10 (win condition)
      if (this.level > 10) {
        this.endGame('win');
      } else {
        // Add a new color to the sequence after a delay
        setTimeout(() => this.addToSequence(), 1000);
      }
    }
  }

  endGame(result: 'win' | 'lose') {
    this.gameOver = true;
    this.gameResult = result;
    this.clearGameInterval();
  }

  restartGame() {
    this.resetGame();
    setTimeout(() => this.startGame(), 500);
  }

  clearGameInterval() {
    if (this.gameInterval) {
      clearInterval(this.gameInterval);
      this.gameInterval = null;
    }
  }

  getColorClass(index: number): string {
    const baseClasses = 'simon-button transition-all duration-200';
    const activeClass = this.activeColor === index ? 'active' : '';

    switch (index) {
      case 0: return `${baseClasses} simon-red ${activeClass}`;
      case 1: return `${baseClasses} simon-blue ${activeClass}`;
      case 2: return `${baseClasses} simon-green ${activeClass}`;
      case 3: return `${baseClasses} simon-yellow ${activeClass}`;
      default: return baseClasses;
    }
  }
}
