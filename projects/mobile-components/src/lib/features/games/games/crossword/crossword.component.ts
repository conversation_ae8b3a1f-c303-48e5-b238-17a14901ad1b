import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { Game } from 'lp-client-api';

interface CrosswordCell {
  letter: string;
  isBlack: boolean;
  number?: number;
  isRevealed?: boolean;
  isCorrect?: boolean;
  isWrong?: boolean;
}

interface CrosswordClues {
  across: string[];
  down: string[];
}

interface CrosswordConfig {
  difficulty?: string;
  maxAttempts?: number;
  hintsAllowed?: number;
  timeLimit?: number;
  levels?: number;
}

interface CrosswordGameEvent {
  type: string;
  data: any;
  timestamp: string;
  gameId: string;
}

@Component({
  selector: 'lib-crossword',
  templateUrl: './crossword.component.html',
  styleUrls: ['./crossword.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    GamesScoreComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class CrosswordComponent implements OnInit {
  @Input() gameId: string = '';
  @Input() config: CrosswordConfig = {
    difficulty: 'MEDIUM',
    maxAttempts: 3,
    hintsAllowed: 3,
    timeLimit: 300,
    levels: 10
  };
  @Input() gameInstance: any = null;
  @Input() game?: Game;
  @Output() gameEventEmitter = new EventEmitter<CrosswordGameEvent>();

  grid: CrosswordCell[][] = [];
  clues: CrosswordClues = { across: [], down: [] };
  currentLevel = 1;
  hintsRemaining = 3;
  solution: string[][] = [];
  isDemoMode = false;
  errorMessage = '';
  score = 0;
  startTime: Date | null = null;
  endTime: Date | null = null;
  gameOver = false;

  keyboard = [
    ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],
    ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'],
    ['Z', 'X', 'C', 'V', 'B', 'N', 'M'],
  ];

  selectedCell: { row: number; col: number } | null = null;

  private levels = [
    {
      // Level 1 - Very Easy (3x3)
      grid: [
        ['C', 'A', 'T'],
        ['O', '*', 'O'],
        ['W', 'E', 'T'],
      ],
      clues: {
        across: ['1. Feline pet', '3. Opposite of dry'],
        down: ['1. Milk producer', '2. Used to eat soup'],
      },
    },
    {
      // Level 2 - Easy (4x4)
      grid: [
        ['P', 'A', 'W', '*'],
        ['E', '*', 'I', 'N'],
        ['T', 'O', 'Y', '*'],
        ['*', '*', '*', 'S'],
      ],
      clues: {
        across: [
          '1. Animal foot',
          '2. Victory (W _ _)',
          '3. Not out but ...',
          "4. Child's plaything",
        ],
        down: [
          '1. Animal companion',
          '2. On top ___ (preposition)',
          '3. Yourself',
        ],
      },
    },
    {
      // Level 3 - Easy-Medium (4x4)
      grid: [
        ['B', 'O', 'A', 'T'],
        ['I', '*', 'I', '*'],
        ['R', 'A', 'R', 'E'],
        ['D', '*', '*', 'D'],
      ],
      clues: {
        across: [
          '1. Water vessel',
          '2. Letter after H',
          '3. Not common',
          '4. Feathered friend',
        ],
        down: [
          '1. Flying creature',
          '2. Atmosphere',
          '3. Color of roses',
          '4. Past tense of eat',
        ],
      },
    },
    {
      // Level 4 - Medium (5x5)
      grid: [
        ['S', 'T', 'A', 'R', '*'],
        ['P', '*', 'P', '*', 'B'],
        ['A', 'P', 'E', '*', 'E'],
        ['M', '*', 'S', '*', 'E'],
        ['*', 'T', 'O', 'Y', 'S'],
      ],
      clues: {
        across: [
          '1. Twinkle in the sky',
          '2. Father',
          '3. Monkey',
          '4. Unwanted email',
          "5. Children's playthings",
        ],
        down: [
          '1. Canned meat',
          '2. Hot drink',
          '3. Animal doctor',
          '4. Honey maker',
          '5. Before',
        ],
      },
    },
    {
      // Level 5 - Medium (5x5)
      grid: [
        ['C', 'L', 'A', 'P', '*'],
        ['L', 'I', 'M', 'E', '*'],
        ['O', 'O', '*', 'A', 'R'],
        ['W', 'N', '*', 'R', '*'],
        ['*', 'S', 'U', 'N', '*'],
      ],
      clues: {
        across: [
          '1. Applaud sound',
          '2. Green citrus',
          '3. Look at',
          '4. Down below',
          '5. Star in sky',
        ],
        down: [
          '1. Circus performer',
          "2. Lion's home",
          '3. Measure of time',
          '4. Fruit or pear',
          '5. Run away',
        ],
      },
    },
    {
      // Level 6 - Medium-Hard (6x6)
      grid: [
        ['S', 'P', 'A', 'C', 'E', '*'],
        ['T', '*', '*', 'A', '*', 'F'],
        ['A', 'P', 'P', 'L', 'E', 'S'],
        ['R', '*', 'E', 'M', '*', 'H'],
        ['T', 'I', 'N', '*', '*', '*'],
        ['*', 'N', '*', 'S', 'E', 'T'],
      ],
      clues: {
        across: [
          '1. Galaxy area',
          '2. Beginning',
          '3. Red fruits',
          '4. Early morning',
          '5. Metal container',
          '6. Group of items',
        ],
        down: [
          '1. Begin',
          '2. Writing tool',
          '3. Writing implement',
          '4. Quiet!',
          '5. Not difficult',
          '6. Quick swim',
        ],
      },
    },
    {
      // Level 7 - Hard (6x6)
      grid: [
        ['B', 'R', 'E', 'A', 'D', '*'],
        ['L', '*', 'A', '*', 'O', 'T'],
        ['U', 'N', 'T', 'I', 'L', '*'],
        ['E', '*', '*', 'R', '*', 'P'],
        ['*', 'M', 'A', 'P', '*', 'E'],
        ['S', 'E', 'T', '*', 'N', 'T'],
      ],
      clues: {
        across: [
          '1. Baked food',
          '2. Not here',
          '3. Up to when',
          '4. Color',
          '5. Navigation guide',
          '6. Collection',
        ],
        down: [
          '1. Sad color',
          '2. Not out',
          '3. Consume',
          '4. Ireland',
          '5. Night bird',
          '6. Animal doctor',
        ],
      },
    },
    {
      // Level 8 - Hard (7x7)
      grid: [
        ['P', 'L', 'A', 'N', 'E', 'T', '*'],
        ['A', '*', '*', 'E', '*', 'R', 'A'],
        ['R', 'A', 'I', 'N', '*', 'E', '*'],
        ['K', '*', 'R', '*', 'S', 'E', 'T'],
        ['*', 'S', '*', 'S', '*', '*', '*'],
        ['U', 'P', '*', 'T', 'A', 'P', 'E'],
        ['P', '*', 'S', 'O', 'N', 'G', '*'],
      ],
      clues: {
        across: [
          '1. Earth is one',
          '2. Time period',
          '3. Weather type',
          '4. Collection',
          '5. Direction',
          '6. Adhesive',
          '7. Musical piece',
        ],
        down: [
          '1. Vehicle space',
          '2. Above',
          '3. In the air',
          '4. Network',
          '5. Snake sound',
          '6. Tree',
          '7. Exit',
        ],
      },
    },
    {
      // Level 9 - Very Hard (7x7)
      grid: [
        ['S', 'T', 'A', 'M', 'P', '*', '*'],
        ['T', '*', 'R', '*', 'A', 'R', 'T'],
        ['A', 'P', 'T', '*', 'R', '*', '*'],
        ['R', '*', '*', 'W', 'K', '*', 'B'],
        ['*', '*', 'N', '*', '*', 'I', 'E'],
        ['D', 'O', 'G', '*', 'C', 'A', 'T'],
        ['*', '*', '*', 'S', 'E', 'T', '*'],
      ],
      clues: {
        across: [
          '1. Mail item',
          '2. Creative work',
          '3. Suitable',
          '4. Work',
          '5. Be',
          '6. Pet animal',
          '7. Collection',
        ],
        down: [
          '1. Begin',
          '2. Top',
          '3. Insect',
          '4. Seven days',
          '5. Writing tool',
          '6. Ice cream ___',
          '7. Insect',
        ],
      },
    },
    {
      // Level 10 - Expert (8x8)
      grid: [
        ['S', 'T', 'A', 'R', '*', 'S', 'K', 'Y'],
        ['P', '*', '*', 'A', 'R', 'E', '*', '*'],
        ['A', 'P', 'P', 'L', 'E', '*', 'D', 'O'],
        ['C', '*', '*', '*', 'A', '*', '*', 'G'],
        ['E', 'A', 'T', '*', 'D', 'A', 'T', 'E'],
        ['*', '*', 'O', '*', '*', '*', '*', '*'],
        ['U', 'P', 'P', 'E', 'R', '*', 'T', 'O'],
        ['P', '*', '*', '*', '*', 'G', 'O', 'T'],
      ],
      clues: {
        across: [
          '1. Night light',
          '2. Exist',
          '3. Fruit',
          '4. Room',
          '5. Consume food',
          '6. Calendar entry',
          '7. Higher',
          '8. Received',
        ],
        down: [
          '1. Between planets',
          '2. Writing tool',
          '3. Top',
          '4. Reading material',
          '5. Not early',
          '6. Look at',
          '7. Animal',
          '8. Direction',
        ],
      },
    },
  ];

  ngOnInit() {
    this.startTime = new Date();
    this.hintsRemaining = this.config.hintsAllowed || 3;

    if (this.gameInstance) {
      try {
        // Try to load game state from gameInstance
        this.loadGameState();
      } catch (error) {
        console.error('Error loading game state:', error);
        this.initializeDemoMode();
      }
    } else {
      this.initializeDemoMode();
    }
  }

  /**
   * Initialize the game in demo mode with default settings
   */
  initializeDemoMode() {
    console.log('Initializing Crossword game in demo mode');
    this.isDemoMode = true;
    this.errorMessage = 'Playing in demo mode';
    this.emitGameEvent('game_start', { mode: 'demo', level: this.currentLevel });
    this.loadLevel(this.currentLevel);
  }

  /**
   * Load saved game state from gameInstance
   */
  loadGameState() {
    if (!this.gameInstance || !this.gameInstance.state) {
      throw new Error('No valid game state found');
    }

    const state = JSON.parse(this.gameInstance.state);
    this.currentLevel = state.level || 1;
    this.hintsRemaining = state.hintsRemaining || this.config.hintsAllowed || 3;
    this.score = state.score || 0;

    // Load the level based on the saved state
    this.loadLevel(this.currentLevel);

    // If there's a saved grid state, restore it
    if (state.grid) {
      this.restoreGridState(state.grid);
    }

    this.emitGameEvent('game_resume', {
      level: this.currentLevel,
      hintsRemaining: this.hintsRemaining,
      score: this.score
    });
  }

  /**
   * Restore the grid state from saved data
   */
  restoreGridState(savedGrid: any[][]) {
    if (!savedGrid || !Array.isArray(savedGrid) || savedGrid.length === 0) {
      return;
    }

    for (let i = 0; i < Math.min(this.grid.length, savedGrid.length); i++) {
      for (let j = 0; j < Math.min(this.grid[i].length, savedGrid[i].length); j++) {
        if (!this.grid[i][j].isBlack) {
          this.grid[i][j].letter = savedGrid[i][j].letter || '';
          this.grid[i][j].isRevealed = savedGrid[i][j].isRevealed || false;
        }
      }
    }
  }

  /**
   * Save the current game state
   */
  saveGameState() {
    if (this.isDemoMode) {
      console.log('Demo mode - game state not saved');
      return;
    }

    const state = {
      level: this.currentLevel,
      hintsRemaining: this.hintsRemaining,
      score: this.score,
      grid: this.grid.map(row =>
        row.map(cell => ({
          letter: cell.letter,
          isRevealed: cell.isRevealed
        }))
      )
    };

    this.emitGameEvent('game_save', state);
  }

  /**
   * Emit a game event
   */
  emitGameEvent(type: string, data: any) {
    const event: CrosswordGameEvent = {
      type: type,
      data: data,
      timestamp: new Date().toISOString(),
      gameId: this.gameId
    };

    this.gameEventEmitter.emit(event);
    console.log('Crossword game event:', event);
  }

  loadLevel(level: number) {
    const levelData = this.levels[level - 1];
    this.solution = levelData.grid;
    this.grid = this.solution.map((row) =>
      row.map((cell) => ({
        letter: '',
        isBlack: cell === '*',
        number: undefined,
      }))
    );
    this.clues = levelData.clues;
    this.assignNumbers();
  }

  onCellInput(i: number, j: number, event: any) {
    if (this.grid[i][j].isBlack) return;

    this.selectedCell = { row: i, col: j };

    const input = event.target.value.toUpperCase();
    if (/^[A-Z]$/.test(input)) {
      this.grid[i][j].letter = input;
      this.checkCell(i, j);
      this.moveToNextCell(i, j);
    }
  }

  getHint() {
    if (this.hintsRemaining > 0) {
      // Find empty cell and reveal it
      for (let i = 0; i < this.grid.length; i++) {
        for (let j = 0; j < this.grid[i].length; j++) {
          if (!this.grid[i][j].isBlack && !this.grid[i][j].letter) {
            this.grid[i][j].letter = this.solution[i][j];
            this.grid[i][j].isRevealed = true;
            this.hintsRemaining--;

            // Emit hint used event
            this.emitGameEvent('hint_used', {
              level: this.currentLevel,
              hintsRemaining: this.hintsRemaining,
              position: { row: i, col: j },
              letter: this.solution[i][j]
            });

            // Save game state
            this.saveGameState();
            return;
          }
        }
      }
    }
  }

  checkPuzzle() {
    let isComplete = true;
    let hasAllLetters = true;
    let correctCount = 0;
    let totalCells = 0;

    // First check if all cells are filled
    for (let i = 0; i < this.grid.length; i++) {
      for (let j = 0; j < this.grid[i].length; j++) {
        if (!this.grid[i][j].isBlack) {
          totalCells++;
          if (!this.grid[i][j].letter) {
            hasAllLetters = false;
            break;
          }
        }
      }
    }

    if (!hasAllLetters) {
      alert('Please fill in all the letters before checking.');
      return;
    }

    // Check all cells for correctness
    for (let i = 0; i < this.grid.length; i++) {
      for (let j = 0; j < this.grid[i].length; j++) {
        if (!this.grid[i][j].isBlack) {
          const isCorrect = this.grid[i][j].letter === this.solution[i][j];
          this.grid[i][j].isCorrect = isCorrect;
          this.grid[i][j].isWrong = !isCorrect;
          if (isCorrect) {
            correctCount++;
          } else {
            isComplete = false;
          }
        }
      }
    }

    // Calculate score based on correct answers and hints used
    const accuracy = totalCells > 0 ? (correctCount / totalCells) * 100 : 0;
    const baseScore = Math.round(accuracy * 10);
    const hintPenalty = (this.config.hintsAllowed || 3) - this.hintsRemaining;
    this.score = Math.max(0, baseScore - (hintPenalty * 5));

    if (isComplete) {
      this.endTime = new Date();
      this.gameOver = true;

      // Calculate time bonus
      if (this.startTime && this.endTime) {
        const timeElapsed = (this.endTime.getTime() - this.startTime.getTime()) / 1000; // in seconds
        const timeLimit = this.config.timeLimit || 300;
        if (timeElapsed < timeLimit) {
          const timeBonus = Math.round((1 - (timeElapsed / timeLimit)) * 50);
          this.score += timeBonus;
        }
      }

      // Emit level complete event
      this.emitGameEvent('level_complete', {
        level: this.currentLevel,
        score: this.score,
        accuracy: accuracy,
        hintsUsed: (this.config.hintsAllowed || 3) - this.hintsRemaining
      });

      setTimeout(() => {
        alert(`Congratulations! Level complete! Score: ${this.score}`);
        this.currentLevel++;

        if (this.currentLevel <= this.levels.length) {
          // Reset for next level
          this.startTime = new Date();
          this.loadLevel(this.currentLevel);
          this.gameOver = false;

          // Emit new level event
          this.emitGameEvent('level_start', {
            level: this.currentLevel
          });
        } else {
          // Game complete
          this.emitGameEvent('game_complete', {
            totalScore: this.score,
            levelsCompleted: this.currentLevel - 1
          });

          alert(`Congratulations! You've completed all levels! Final score: ${this.score}`);
        }

        // Save game state
        this.saveGameState();
      }, 500);
    } else {
      // Emit check event
      this.emitGameEvent('check_answers', {
        level: this.currentLevel,
        correctCount: correctCount,
        totalCells: totalCells,
        accuracy: accuracy
      });

      alert(`Some answers are incorrect. Keep trying! Accuracy: ${accuracy.toFixed(1)}%`);
    }
  }

  private assignNumbers() {
    let number = 1;
    // First pass: clear all numbers
    for (let i = 0; i < this.grid.length; i++) {
      for (let j = 0; j < this.grid[i].length; j++) {
        this.grid[i][j].number = undefined;
      }
    }
    // Second pass: assign new numbers
    for (let i = 0; i < this.grid.length; i++) {
      for (let j = 0; j < this.grid[i].length; j++) {
        if (this.shouldNumberCell(i, j)) {
          this.grid[i][j].number = number++;
        }
      }
    }
  }

  private shouldNumberCell(i: number, j: number): boolean {
    // A cell should be numbered if it's the start of an across word OR a down word
    return (
      !this.grid[i][j].isBlack &&
      (this.isStartOfAcrossWord(i, j) || this.isStartOfDownWord(i, j))
    );
  }

  private isStartOfAcrossWord(i: number, j: number): boolean {
    // A cell starts an across word if:
    // 1. It's not a black cell
    // 2. Either it's the first cell in the row OR the cell to its left is black
    // 3. The cell to its right exists and is not black
    return (
      !this.grid[i][j].isBlack &&
      (j === 0 || (j > 0 && this.grid[i][j - 1].isBlack)) &&
      j < this.grid[i].length - 1 &&
      !this.grid[i][j + 1].isBlack
    );
  }

  private isStartOfDownWord(i: number, j: number): boolean {
    // A cell starts a down word if:
    // 1. It's not a black cell
    // 2. Either it's the first cell in the column OR the cell above it is black
    // 3. The cell below it exists and is not black
    return (
      !this.grid[i][j].isBlack &&
      (i === 0 || (i > 0 && this.grid[i - 1][j].isBlack)) &&
      i < this.grid.length - 1 &&
      !this.grid[i + 1][j].isBlack
    );
  }

  private focusNextCell(i: number, j: number) {
    // Implementation for moving to next cell
  }

  highlightWord(direction: 'across' | 'down', index: number) {
    // Implementation for highlighting selected word
  }

  private checkCell(i: number, j: number) {
    if (!this.grid[i][j].isBlack) {
      const isCorrect = this.grid[i][j].letter === this.solution[i][j];
      this.grid[i][j].isCorrect = isCorrect;
      this.grid[i][j].isWrong = !isCorrect;
    }
  }

  onKeyPress(key: string) {
    if (!this.selectedCell) return;

    const { row, col } = this.selectedCell;

    if (this.grid[row][col].isBlack) return; // Don't allow typing in black cells

    if (key === 'DELETE') {
      if (this.grid[row][col].letter) {
        this.grid[row][col].letter = '';

        // Emit key press event
        this.emitGameEvent('key_press', {
          key: 'DELETE',
          position: { row, col }
        });

        // Save game state
        this.saveGameState();
      } else {
        this.moveToPreviousCell(row, col);
      }
    } else {
      // Update the current cell first
      this.grid[row][col].letter = key;
      this.checkCell(row, col);

      // Emit key press event
      this.emitGameEvent('key_press', {
        key: key,
        position: { row, col }
      });

      // Save game state
      this.saveGameState();

      // Then move to next cell
      this.moveToNextCell(row, col);
    }
  }

  moveToNextCell(currentRow: number, currentCol: number) {
    // Try to move right first
    if (currentCol < this.grid[0].length - 1) {
      if (!this.grid[currentRow][currentCol + 1].isBlack) {
        this.selectedCell = { row: currentRow, col: currentCol + 1 };
        return;
      }
    }
    // Try to move to the start of next row
    if (currentRow < this.grid.length - 1) {
      for (let col = 0; col < this.grid[0].length; col++) {
        if (!this.grid[currentRow + 1][col].isBlack) {
          this.selectedCell = { row: currentRow + 1, col };
          return;
        }
      }
    }
  }

  moveToPreviousCell(currentRow: number, currentCol: number) {
    // Try to move left first
    if (currentCol > 0) {
      if (!this.grid[currentRow][currentCol - 1].isBlack) {
        this.selectedCell = { row: currentRow, col: currentCol - 1 };
        return;
      }
    }
    // Try to move to the end of previous row
    if (currentRow > 0) {
      for (let col = this.grid[0].length - 1; col >= 0; col--) {
        if (!this.grid[currentRow - 1][col].isBlack) {
          this.selectedCell = { row: currentRow - 1, col };
          return;
        }
      }
    }
  }

  onCellClick(i: number, j: number) {
    if (!this.grid[i][j].isBlack) {
      this.selectedCell = { row: i, col: j };
    }
  }
}
