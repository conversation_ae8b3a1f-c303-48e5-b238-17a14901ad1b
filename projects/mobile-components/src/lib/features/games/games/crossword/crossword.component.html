<div class="game-container flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800">
  <!-- Game Header -->
  <div class="game-title text-center p-4">
    <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600">Crossword Puzzle</h1>
    <p class="text-xs text-slate-400 mb-1" *ngIf="isDemoMode">Playing in demo mode</p>
  </div>

  <!-- Game Score -->
  <div class="px-4">
    <games-score
      [scores]="[
        { title: 'Level', number: currentLevel },
        { title: 'Score', number: score },
        { title: 'Hints', number: hintsRemaining }
      ]"
    ></games-score>
  </div>

  <!-- Game Board -->
  <div class="flex-1 overflow-auto p-4">
    <div class="game-board p-4 rounded-xl bg-slate-700/50 border border-slate-600 shadow-xl mx-auto max-w-md">
      <div class="flex justify-center mb-6 mt-2">
        <div class="inline-block">
          <div
            class="grid rounded-lg shadow-lg overflow-hidden"
            [style.grid-template-columns]="
              'repeat(' + solution[0].length + ', 40px)'
            "
          >
            <ng-container *ngFor="let row of grid; let i = index">
              <div
                *ngFor="let cell of row; let j = index"
                class="relative w-10 h-10 border border-slate-600/30"
                [ngClass]="{
                  'bg-black': cell.isBlack,
                  'bg-green-400/80': cell.isCorrect,
                  'bg-red-400/80': cell.isWrong,
                  'bg-blue-400/80': selectedCell?.row === i && selectedCell?.col === j,
                  'bg-gradient-to-br from-slate-200 to-white': !cell.isBlack && !cell.isCorrect && !cell.isWrong && !(selectedCell?.row === i && selectedCell?.col === j)
                }"
                (click)="onCellClick(i, j)"
              >
                <input
                  *ngIf="!cell.isBlack"
                  type="text"
                  maxlength="1"
                  class="w-full h-full text-center text-lg font-bold uppercase bg-transparent"
                  [value]="cell.letter"
                  (input)="onCellInput(i, j, $event)"
                  [disabled]="cell.isRevealed"
                />
                <span
                  *ngIf="cell.number"
                  class="absolute top-0 left-0 text-xs font-semibold p-1 text-slate-700"
                  >{{ cell.number }}</span
                >
              </div>
            </ng-container>
          </div>
        </div>
      </div>

      <div class="flex justify-center space-x-4 mb-6">
        <button
          class="px-4 py-2 bg-gradient-to-br from-blue-500 to-blue-700 text-white rounded-lg hover:from-blue-600 hover:to-blue-800 shadow-md transition-all duration-200 transform hover:scale-105"
          (click)="getHint()"
          [disabled]="hintsRemaining <= 0"
        >
          Get Hint ({{ hintsRemaining }} left)
        </button>
        <button
          class="px-4 py-2 bg-gradient-to-br from-green-500 to-green-700 text-white rounded-lg hover:from-green-600 hover:to-green-800 shadow-md transition-all duration-200 transform hover:scale-105"
          (click)="checkPuzzle()"
        >
          Check Answers
        </button>
      </div>

      <div class="flex flex-col md:flex-row md:space-x-8 space-y-4 md:space-y-0">
        <div class="md:w-1/2 bg-slate-800/50 p-4 rounded-lg shadow-inner">
          <h2 class="text-xl font-semibold mb-2 text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">Across</h2>
          <ol class="list-decimal list-inside text-white">
            <li
              *ngFor="let clue of clues.across; let i = index"
              class="mb-1 cursor-pointer hover:text-blue-300 transition-colors"
              (click)="highlightWord('across', i)"
            >
              {{ clue }}
            </li>
          </ol>
        </div>
        <div class="md:w-1/2 bg-slate-800/50 p-4 rounded-lg shadow-inner">
          <h2 class="text-xl font-semibold mb-2 text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-400">Down</h2>
          <ol class="list-decimal list-inside text-white">
            <li
              *ngFor="let clue of clues.down; let i = index"
              class="mb-1 cursor-pointer hover:text-blue-300 transition-colors"
              (click)="highlightWord('down', i)"
            >
              {{ clue }}
            </li>
          </ol>
        </div>
      </div>
    </div>
  </div>

  <!-- Virtual Keyboard -->
  <div class="virtual-keyboard p-4 bg-slate-800/90 shadow-lg">
    <div class="flex justify-center gap-1 mb-1">
      <button
        *ngFor="let key of keyboard[0]"
        (click)="onKeyPress(key)"
        class="keyboard-key bg-gradient-to-br from-slate-200 to-white text-slate-800 shadow-md transition-all duration-200 hover:from-blue-100 hover:to-blue-200"
      >
        {{ key }}
      </button>
    </div>
    <div class="flex justify-center gap-1 mb-1">
      <button
        *ngFor="let key of keyboard[1]"
        (click)="onKeyPress(key)"
        class="keyboard-key bg-gradient-to-br from-slate-200 to-white text-slate-800 shadow-md transition-all duration-200 hover:from-blue-100 hover:to-blue-200"
      >
        {{ key }}
      </button>
    </div>
    <div class="flex justify-center gap-1">
      <button
        *ngFor="let key of keyboard[2]"
        (click)="onKeyPress(key)"
        class="keyboard-key bg-gradient-to-br from-slate-200 to-white text-slate-800 shadow-md transition-all duration-200 hover:from-blue-100 hover:to-blue-200"
      >
        {{ key }}
      </button>
    </div>
    <div class="flex justify-center gap-1 mt-1">
      <button
        class="keyboard-key keyboard-special bg-gradient-to-br from-red-500 to-red-700 text-white shadow-md transition-all duration-200 hover:from-red-600 hover:to-red-800"
        (click)="onKeyPress('DELETE')"
      >
        Delete
      </button>
    </div>
  </div>
</div>
