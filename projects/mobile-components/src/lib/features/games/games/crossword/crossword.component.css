/* Import shared game styles */
@import '../styles/game-styles.css';

:host {
  @apply block w-full h-full;
}

.game-container {
  @apply flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800;
}

.game-title {
  @apply text-center mb-1;
}

.game-title h1 {
  @apply text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600;
}

.game-title p {
  @apply text-xs text-slate-400 mb-1;
}

.game-board {
  position: relative;
  overflow: hidden;
  max-width: 100%;
  margin: 0 auto;
}

.game-board::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10;
  filter: blur(20px);
  z-index: 0;
}

input {
  outline: none;
  caret-color: transparent;
  background: transparent;
  border: none;
  text-transform: uppercase;
}

input:focus {
  background-color: transparent;
}

.highlighted {
  @apply bg-yellow-200/50 !important;
}

input:disabled {
  @apply bg-slate-200/50 text-slate-700;
}

.grid {
  display: grid;
  gap: 0;
  position: relative;
  z-index: 10;
}

.bg-black {
  background-color: #000;
}

/* Virtual Keyboard Styles */
.virtual-keyboard {
  @apply w-full bg-slate-800/90 p-4 shadow-lg;
  z-index: 1000;
}

.keyboard-key {
  min-width: 32px;
  height: 40px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  @apply flex items-center justify-center p-2 m-0.5 shadow-md transition-all duration-200;
}

.keyboard-key:active {
  @apply transform scale-95;
}

.keyboard-key:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.keyboard-special {
  min-width: 80px;
}

/* Animation for correct answers */
@keyframes pulse-success {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.bg-green-400\/80 {
  animation: pulse-success 0.5s ease-in-out;
}

/* Animation for wrong answers */
@keyframes shake {
  10%, 90% { transform: translate3d(-1px, 0, 0); }
  20%, 80% { transform: translate3d(2px, 0, 0); }
  30%, 50%, 70% { transform: translate3d(-2px, 0, 0); }
  40%, 60% { transform: translate3d(2px, 0, 0); }
}

.bg-red-400\/80 {
  animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

/* Animation for selected cell */
@keyframes pulse-selected {
  0% { box-shadow: 0 0 0 0 rgba(96, 165, 250, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(96, 165, 250, 0); }
  100% { box-shadow: 0 0 0 0 rgba(96, 165, 250, 0); }
}

.bg-blue-400\/80 {
  animation: pulse-selected 2s infinite;
}
