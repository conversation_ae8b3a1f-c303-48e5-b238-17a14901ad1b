import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { GameControl, GamesPlayButtonComponent } from '../components/games-play-button/games-play-button.component';
import { WinLoseOverlayComponent } from '../components/win-lose-overlay/win-lose-overlay.component';
import { Game } from 'lp-client-api';

interface TicTacToeConfig {
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  gameMode: 'PVP' | 'AI';
  aiStarts: boolean;
  boardSize: number;
  maxAttempts?: number;
}

interface TicTacToeGameEvent {
  type: string;
  data: any;
  timestamp: string;
  gameId: string;
}

@Component({
  selector: 'lib-tic-tac-toe',
  templateUrl: './tic-tac-toe.component.html',
  styleUrls: ['./tic-tac-toe.component.css'],
  standalone: true,
  imports: [CommonModule, GamesScoreComponent, GamesPlayButtonComponent, WinLoseOverlayComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class TicTacToeComponent implements OnInit, OnDestroy {
  @Input() gameId: string = '';
  @Input() config: TicTacToeConfig = {
    difficulty: 'MEDIUM',
    gameMode: 'AI',
    aiStarts: false,
    boardSize: 3,
    maxAttempts: 3
  };
  @Input() gameInstance: any = null;
  @Input() game?: Game;

  @Output() gameEventEmitter = new EventEmitter<TicTacToeGameEvent>();

  // Game state
  squares: (string | null)[] = Array(9).fill(null);
  xIsNext: boolean = true;
  winner: string | null = null;
  winningLine: number[] = [];
  gameOver: boolean = false;
  gameResult: 'win' | 'lose' | null = null;
  attemptsRemaining: number = 3;
  isDemoMode: boolean = false;
  errorMessage: string = '';
  moveCount: number = 0;
  playerSymbol: string = 'X';
  aiSymbol: string = 'O';

  // Game controls configuration
  gameControls: GameControl[] = [
    { name: 'restart', icon: 'refresh-outline', label: 'Restart' },
    { name: 'newGame', icon: 'add-circle-outline', label: 'New Game' },
    { name: 'switchMode', icon: 'people-outline', label: 'Switch Mode' }
  ];

  // Winning patterns
  winningPatterns = [
    [0, 1, 2], [3, 4, 5], [6, 7, 8], // Rows
    [0, 3, 6], [1, 4, 7], [2, 5, 8], // Columns
    [0, 4, 8], [2, 4, 6]             // Diagonals
  ];

  ngOnInit() {
    this.initializeGame();
  }

  ngOnDestroy() {
    // Clean up any resources
  }

  initializeGame() {
    try {
      // Check if we have a valid configuration from the input
      if (this.config && this.config.difficulty) {
        console.log('Initializing Tic-Tac-Toe with config:', this.config);
      } else {
        console.warn('No valid config provided, using default config');
        this.initializeDemoMode();
      }

      // Set attempts remaining from config
      this.attemptsRemaining = this.config.maxAttempts || 3;

      // Reset game state
      this.resetBoard();

      // If AI mode and AI starts, make the first move
      if (this.config.gameMode === 'AI' && this.config.aiStarts) {
        this.xIsNext = false; // AI is O, so player is X
        setTimeout(() => this.makeAIMove(), 500);
      }

      // Emit game start event
      this.emitGameEvent('start', {
        difficulty: this.config.difficulty,
        gameMode: this.config.gameMode,
        aiStarts: this.config.aiStarts,
        isDemoMode: this.isDemoMode
      });
    } catch (error) {
      console.error('Error initializing Tic-Tac-Toe game:', error);
      this.initializeDemoMode();
    }
  }

  initializeDemoMode() {
    console.log('Initializing Tic-Tac-Toe in demo mode');
    this.isDemoMode = true;
    this.errorMessage = 'Playing in demo mode';

    // Set default configuration
    this.config = {
      difficulty: 'MEDIUM',
      gameMode: 'AI',
      aiStarts: false,
      boardSize: 3,
      maxAttempts: 3
    };

    this.attemptsRemaining = this.config.maxAttempts || 3;
  }

  resetBoard() {
    this.squares = Array(9).fill(null);
    this.winner = null;
    this.xIsNext = true;
    this.winningLine = [];
    this.gameOver = false;
    this.gameResult = null;
    this.moveCount = 0;

    // Remove winning classes from all squares
    const boardElement = document.querySelector('.game-board');
    if (boardElement) {
      const allSquares = boardElement.querySelectorAll('button');
      allSquares.forEach((square) => {
        square.classList.remove(
          'winning-square',
          'horizontal',
          'vertical',
          'diagonal-1',
          'diagonal-2'
        );
      });
    }
  }

  get player(): string {
    return this.xIsNext ? 'X' : 'O';
  }

  get currentPlayerName(): string {
    if (this.config.gameMode === 'PVP') {
      return this.xIsNext ? 'Player 1 (X)' : 'Player 2 (O)';
    } else {
      return this.xIsNext ? 'You (X)' : 'AI (O)';
    }
  }

  makeMove(idx: number): void {
    // If square is already filled or game is over, do nothing
    if (this.squares[idx] || this.gameOver) return;

    // If it's AI's turn and game mode is AI, do nothing
    if (!this.xIsNext && this.config.gameMode === 'AI') return;

    // Make the move
    this.squares[idx] = this.player;
    this.moveCount++;

    // Emit move event
    this.emitGameEvent('move', {
      player: this.player,
      position: idx,
      moveCount: this.moveCount
    });

    // Check for winner
    this.checkGameState();

    // Switch turns
    this.xIsNext = !this.xIsNext;

    // If game is not over and it's AI's turn, make AI move
    if (!this.gameOver && !this.xIsNext && this.config.gameMode === 'AI') {
      setTimeout(() => this.makeAIMove(), 500);
    }
  }

  makeAIMove(): void {
    if (this.gameOver) return;

    let move: number;

    // Choose move based on difficulty
    switch (this.config.difficulty) {
      case 'EASY':
        move = this.getRandomMove();
        break;
      case 'MEDIUM':
        // 50% chance of making a smart move, 50% chance of random move
        move = Math.random() < 0.5 ? this.getSmartMove() : this.getRandomMove();
        break;
      case 'HARD':
        move = this.getSmartMove();
        break;
      default:
        move = this.getRandomMove();
    }

    // Make the move
    if (move !== -1) {
      this.squares[move] = this.player;
      this.moveCount++;

      // Emit move event
      this.emitGameEvent('move', {
        player: this.player,
        position: move,
        moveCount: this.moveCount,
        isAI: true
      });

      // Check for winner
      this.checkGameState();

      // Switch turns
      this.xIsNext = !this.xIsNext;
    }
  }

  getRandomMove(): number {
    // Get all empty squares
    const emptySquares = this.squares
      .map((square, index) => (square === null ? index : -1))
      .filter(index => index !== -1);

    // If no empty squares, return -1
    if (emptySquares.length === 0) return -1;

    // Return random empty square
    return emptySquares[Math.floor(Math.random() * emptySquares.length)];
  }

  getSmartMove(): number {
    // Try to win
    for (let i = 0; i < this.squares.length; i++) {
      if (this.squares[i] === null) {
        // Try this move
        this.squares[i] = this.player;

        // If this move wins, make it
        if (this.checkWinner()) {
          this.squares[i] = null; // Undo the move
          return i;
        }

        // Undo the move
        this.squares[i] = null;
      }
    }

    // Try to block opponent
    const opponent = this.xIsNext ? 'O' : 'X';
    for (let i = 0; i < this.squares.length; i++) {
      if (this.squares[i] === null) {
        // Try this move for opponent
        this.squares[i] = opponent;

        // If this move would let opponent win, block it
        if (this.checkWinner()) {
          this.squares[i] = null; // Undo the move
          return i;
        }

        // Undo the move
        this.squares[i] = null;
      }
    }

    // Try to take center
    if (this.squares[4] === null) return 4;

    // Try to take corners
    const corners = [0, 2, 6, 8];
    const emptyCorners = corners.filter(corner => this.squares[corner] === null);
    if (emptyCorners.length > 0) {
      return emptyCorners[Math.floor(Math.random() * emptyCorners.length)];
    }

    // Take any available edge
    const edges = [1, 3, 5, 7];
    const emptyEdges = edges.filter(edge => this.squares[edge] === null);
    if (emptyEdges.length > 0) {
      return emptyEdges[Math.floor(Math.random() * emptyEdges.length)];
    }

    // If no moves available, return -1
    return -1;
  }

  checkWinner(): boolean {
    for (let i = 0; i < this.winningPatterns.length; i++) {
      const [a, b, c] = this.winningPatterns[i];
      if (
        this.squares[a] &&
        this.squares[a] === this.squares[b] &&
        this.squares[a] === this.squares[c]
      ) {
        return true;
      }
    }
    return false;
  }

  checkGameState(): void {
    const winner = this.calculateWinner();

    if (winner) {
      this.winner = winner;
      this.gameOver = true;

      // Determine game result
      if (this.config.gameMode === 'PVP') {
        this.gameResult = winner === 'X' ? 'win' : 'lose';
      } else {
        this.gameResult = winner === this.playerSymbol ? 'win' : 'lose';
      }

      // If player lost, decrement attempts
      if (this.gameResult === 'lose') {
        this.attemptsRemaining--;
      }

      // Emit game end event
      this.emitGameEvent('end', {
        winner: winner,
        moveCount: this.moveCount,
        gameResult: this.gameResult,
        attemptsRemaining: this.attemptsRemaining
      });
    } else if (this.moveCount === 9) {
      // Game is a tie
      this.gameOver = true;
      this.gameResult = null; // Use null for tie to match WinLoseOverlay component

      // Emit game end event
      this.emitGameEvent('end', {
        winner: null,
        moveCount: this.moveCount,
        gameResult: 'tie',
        attemptsRemaining: this.attemptsRemaining
      });
    }
  }

  calculateWinner(): string | null {
    for (let i = 0; i < this.winningPatterns.length; i++) {
      const [a, b, c] = this.winningPatterns[i];
      if (
        this.squares[a] &&
        this.squares[a] === this.squares[b] &&
        this.squares[a] === this.squares[c]
      ) {
        this.winningLine = this.winningPatterns[i];

        // Add the appropriate direction class to the winning squares
        const direction =
          i < 3
            ? 'horizontal'
            : i < 6
            ? 'vertical'
            : i === 6
            ? 'diagonal-1'
            : 'diagonal-2';

        // Use a more reliable way to select the squares
        const boardElement = document.querySelector('.game-board');
        if (boardElement) {
          this.winningLine.forEach((index) => {
            const squareElement = boardElement.querySelector(`button:nth-child(${index + 1})`);
            if (squareElement) {
              squareElement.classList.add('winning-square', direction);
            }
          });
        }

        return this.squares[a];
      }
    }
    return null;
  }

  newGame(): void {
    if (this.attemptsRemaining <= 0) {
      this.emitGameEvent('outOfAttempts', {});
      return;
    }

    this.resetBoard();

    // If AI mode and AI starts, make the first move
    if (this.config.gameMode === 'AI' && this.config.aiStarts) {
      this.xIsNext = false; // AI is O, so player is X
      setTimeout(() => this.makeAIMove(), 500);
    }

    this.emitGameEvent('restart', {
      attemptsRemaining: this.attemptsRemaining
    });
  }

  switchGameMode(): void {
    this.config.gameMode = this.config.gameMode === 'PVP' ? 'AI' : 'PVP';
    this.resetBoard();

    this.emitGameEvent('switchMode', {
      gameMode: this.config.gameMode
    });
  }

  handleControlClick(control: any): void {
    // Convert the event to a string if it's not already
    const controlName = typeof control === 'string' ? control : control.toString();

    console.log('Control clicked:', controlName);

    switch (controlName) {
      case 'restart':
        this.newGame();
        break;
      case 'newGame':
        this.attemptsRemaining = this.config.maxAttempts || 3;
        this.initializeGame();
        break;
      case 'switchMode':
        this.switchGameMode();
        break;
      default:
        console.warn('Unknown control:', controlName);
    }
  }

  emitGameEvent(type: string, data: any): void {
    const event: TicTacToeGameEvent = {
      type: type,
      data: data,
      timestamp: new Date().toISOString(),
      gameId: this.gameId
    };

    this.gameEventEmitter.emit(event);
    console.log('Tic-Tac-Toe game event:', event);
  }

  getScoreItems() {
    return [
      { title: 'Mode', number: this.config.gameMode === 'PVP' ? 'Player vs Player' : 'Player vs AI' },
      { title: 'Difficulty', number: this.config.difficulty },
      { title: 'Moves', number: `${this.moveCount}/9` },
      { title: 'Attempts', number: this.attemptsRemaining }
    ];
  }
}
