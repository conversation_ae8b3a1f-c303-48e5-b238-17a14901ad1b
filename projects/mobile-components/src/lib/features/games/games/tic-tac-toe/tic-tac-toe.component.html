<div class="flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800">
  <!-- Score Display -->
  <div class="w-full max-w-md mx-auto px-4 py-2">
    <games-score [scores]="getScoreItems()"></games-score>
  </div>

  <!-- Game Title -->
  <div class="text-center mb-1">
    <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-indigo-500">Tic Tac <PERSON>e</h1>
    <p *ngIf="isDemoMode" class="text-xs text-amber-400 mb-1">Demo Mode</p>
    <p class="text-xs text-slate-400 mb-1">{{ config.gameMode === 'PVP' ? 'Player vs Player' : 'Player vs AI' }}</p>
  </div>

  <!-- Game Board -->
  <div class="flex overflow-hidden flex-col flex-grow items-center p-4">
    <div class="mb-4 text-xl font-semibold text-slate-300">
      <p *ngIf="!gameOver" class="animate-pulse">Current player: {{ currentPlayerName }}</p>
    </div>

    <div class="grid grid-cols-3 gap-3 mb-4 game-board">
      <button
        *ngFor="let square of squares; let i = index"
        (click)="makeMove(i)"
        [disabled]="square || gameOver || (!xIsNext && config.gameMode === 'AI')"
        class="
          flex justify-center items-center
          w-20 h-20 md:w-24 md:h-24
          text-4xl font-bold
          text-slate-200
          bg-gradient-to-br from-slate-700/80 to-slate-800/80
          border border-slate-600/50
          rounded-lg shadow-lg
          transition-all duration-200
          hover:from-slate-600/80 hover:to-slate-700/80
          focus:outline-none focus:ring-2 focus:ring-blue-500/50
          disabled:opacity-70 disabled:cursor-not-allowed
          game-square
        "
      >
        <span [ngClass]="{
          'text-blue-400': square === 'X',
          'text-red-400': square === 'O'
        }">{{ square }}</span>
      </button>
    </div>
  </div>

  <!-- Game Controls -->
  <div class="p-2 my-2 space-y-2 border-t border-slate-700">
    <lib-games-play-button
      [controls]="gameControls"
      (controlClicked)="handleControlClick($event)"
    ></lib-games-play-button>
  </div>

  <!-- Win/Lose Overlay -->
  <lib-win-lose-overlay
    *ngIf="gameResult !== null || (gameOver && moveCount === 9)"
    [status]="gameResult"
    [message]="
      gameResult === 'win'
        ? 'Congratulations! You won!'
        : gameResult === 'lose'
        ? 'Better luck next time!'
        : 'It\'s a tie!'
    "
    (restart)="newGame()"
  ></lib-win-lose-overlay>
</div>
