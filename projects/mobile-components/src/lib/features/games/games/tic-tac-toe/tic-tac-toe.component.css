@import '../styles/game-styles.css';

.game-board {
  position: relative;
  overflow: hidden;
}

.game-board::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.05) 0%, rgba(30, 64, 175, 0) 70%);
  animation: pulse 8s infinite;
  z-index: -1;
}

.game-square {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.game-square::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

.game-square:focus::after {
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  100% {
    transform: scale(20, 20);
    opacity: 0;
  }
}

@keyframes pulse {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.winning-square {
  background: linear-gradient(to bottom right, rgba(34, 197, 94, 0.3), rgba(16, 185, 129, 0.3)) !important;
  border-color: rgba(34, 197, 94, 0.5) !important;
  position: relative;
  z-index: 1;
}

.winning-square::after {
  content: '';
  position: absolute;
  background: linear-gradient(to right, #22c55e, #10b981);
  z-index: 2;
  box-shadow: 0 0 10px rgba(34, 197, 94, 0.5);
}

/* Horizontal line */
.winning-square.horizontal::after {
  width: 100%;
  height: 4px;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  animation: fadeInHorizontal 0.5s ease-out forwards;
}

/* Vertical line */
.winning-square.vertical::after {
  width: 4px;
  height: 100%;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  animation: fadeInVertical 0.5s ease-out forwards;
}

/* Diagonal line (top-left to bottom-right) */
.winning-square.diagonal-1::after {
  width: 141%; /* sqrt(2) * 100% */
  height: 4px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  animation: fadeInDiagonal1 0.5s ease-out forwards;
}

/* Diagonal line (top-right to bottom-left) */
.winning-square.diagonal-2::after {
  width: 141%; /* sqrt(2) * 100% */
  height: 4px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  animation: fadeInDiagonal2 0.5s ease-out forwards;
}

@keyframes fadeInHorizontal {
  from {
    width: 0;
    left: 50%;
  }
  to {
    width: 100%;
    left: 0;
  }
}

@keyframes fadeInVertical {
  from {
    height: 0;
    top: 50%;
  }
  to {
    height: 100%;
    top: 0;
  }
}

@keyframes fadeInDiagonal1 {
  from {
    width: 0;
  }
  to {
    width: 141%;
  }
}

@keyframes fadeInDiagonal2 {
  from {
    width: 0;
  }
  to {
    width: 141%;
  }
}
