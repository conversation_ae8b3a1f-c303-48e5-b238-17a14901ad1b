<div class="flex overflow-hidden flex-col h-screen rounded-lg" [class.win-animation]="showAnimation">
  <!-- Demo Mode Indicator -->
  <div *ngIf="demoMode" class="bg-gradient-to-r from-amber-400 to-yellow-500 text-black text-center py-1 px-2 text-sm font-bold rounded-b-md shadow-md flex items-center justify-center space-x-1">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
    <span>DEMO MODE</span>
  </div>

  <!-- Header -->
  <games-score
    [scores]="[
      { title: 'Score', number: score },
      { title: 'High Score', number: highScore },
      { title: 'Level', number: level },
      { title: 'Speed', number: getSpeedPercentage() },
      { title: 'Attempts', number: attemptsRemaining }
    ]"
  ></games-score>

  <div class="flex overflow-hidden flex-col flex-grow items-center p-2">
    <div class="canvas-wrapper w-full max-w-md">
      <canvas
        #gameCanvas
        [attr.width]="canvasSize"
        [attr.height]="canvasSize"
        class="w-full h-full rounded-lg shadow-md"
      ></canvas>
    </div>
  </div>

  <!-- Game over overlay -->
  <div *ngIf="gameOver" class="flex fixed inset-0 z-50 justify-center items-center bg-black bg-opacity-70 backdrop-blur-sm">
    <div class="p-8 text-center bg-gradient-to-b from-slate-800 to-slate-900 rounded-2xl shadow-2xl transform game-over-dialog border border-slate-700 max-w-md w-11/12">
      <!-- Mascot -->
      <div class="mb-4 mascot-container relative">
        <div class="absolute inset-0 bg-gradient-to-b from-green-500/20 to-transparent rounded-full blur-xl"></div>
        <img src="assets/images/games/mascot.png" alt="Game Mascot" class="mx-auto w-24 h-24 mascot-bounce relative z-10">
      </div>

      <h2 class="mb-4 text-4xl font-bold text-white game-over-title">Game Over!</h2>

      <div class="mb-6 p-4 bg-slate-700/50 rounded-xl border border-slate-600">
        <p class="text-2xl font-semibold text-white">Final Score: <span class="text-green-400">{{ score }}</span></p>
        <p class="text-lg text-slate-300">High Score: <span class="text-amber-400">{{ highScore }}</span></p>
        <p class="text-md text-slate-400">Level Reached: {{ level }}</p>
      </div>

      <!-- Bonuses -->
      <div class="mb-6" *ngIf="getBonuses().length">
        <h3 class="mb-2 text-lg font-semibold text-slate-300">Bonuses Earned:</h3>
        <ul class="space-y-2">
          <li *ngFor="let bonus of getBonuses()"
              class="p-3 text-sm bg-gradient-to-r from-green-900/50 to-green-800/50 rounded-lg text-green-400 border border-green-700/50 flex justify-between items-center">
            <span>{{ bonus.type }}</span>
            <span class="font-bold">+{{ bonus.points }} points</span>
          </li>
        </ul>
      </div>

      <!-- Play Again Button -->
      <button
        *ngIf="attemptsRemaining > 0"
        (click)="restartGame()"
        class="px-8 py-4 text-lg font-bold text-white bg-gradient-to-r from-green-600 to-green-500 rounded-xl hover:from-green-500 hover:to-green-400 transition-all duration-300 shadow-lg shadow-green-600/30 transform hover:scale-105"
      >
        Play Again ({{ attemptsRemaining }} left)
      </button>

      <!-- No Attempts Message -->
      <div *ngIf="attemptsRemaining <= 0" class="p-4 bg-red-900/30 rounded-xl border border-red-800/50 text-red-400 font-medium mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 inline-block mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
        No attempts remaining today
      </div>

      <!-- Back to Games Button -->
      <button
        *ngIf="attemptsRemaining <= 0"
        (click)="navigateToGames()"
        class="px-8 py-4 text-lg font-bold text-white bg-gradient-to-r from-blue-600 to-blue-500 rounded-xl hover:from-blue-500 hover:to-blue-400 transition-all duration-300 shadow-lg shadow-blue-600/30 transform hover:scale-105 w-full"
      >
        Back to Games
      </button>
    </div>
  </div>

  <!-- Not available overlay -->
  <div *ngIf="!canPlay" class="flex fixed inset-0 z-50 justify-center items-center bg-black bg-opacity-70 backdrop-blur-sm">
    <div class="p-8 text-center bg-gradient-to-b from-slate-800 to-slate-900 rounded-2xl shadow-2xl border border-slate-700 max-w-md w-11/12">
      <div class="mb-6 text-red-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>
      <h2 class="mb-4 text-3xl font-bold text-white">Game Not Available</h2>
      <p class="text-slate-300 mb-4">Please try again later.</p>
      <div class="mt-6 p-4 bg-slate-700/50 rounded-xl border border-slate-600 text-slate-300 text-sm">
        <p>The game server is currently unavailable. Our team has been notified and is working to resolve the issue.</p>
      </div>
    </div>
  </div>

  <!-- Start game button -->
  <div
    *ngIf="!gameStarted && canPlay && attemptsRemaining > 0"
    class="fixed inset-0 z-40 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
    <div class="p-8 text-center bg-gradient-to-b from-slate-800 to-slate-900 rounded-2xl shadow-2xl border border-slate-700 max-w-md w-11/12">
      <div class="mb-6">
        <img src="assets/images/games/mascot.png" alt="Game Mascot" class="mx-auto w-24 h-24 mascot-bounce">
      </div>
      <h2 class="mb-4 text-3xl font-bold text-white">Snake Game</h2>
      <p class="mb-6 text-slate-300">Control the snake to eat food and grow longer. Avoid hitting the walls or yourself!</p>

      <div class="mb-6 p-4 bg-slate-700/50 rounded-xl border border-slate-600 text-left">
        <h3 class="text-lg font-semibold text-white mb-2">How to Play:</h3>
        <ul class="text-slate-300 space-y-2 text-sm">
          <li class="flex items-center">
            <span class="mr-2">🎮</span> Use arrow keys or swipe to control the snake
          </li>
          <li class="flex items-center">
            <span class="mr-2">🍎</span> Eat food to grow longer and score points
          </li>
          <li class="flex items-center">
            <span class="mr-2">⚠️</span> Don't hit the walls or yourself!
          </li>
        </ul>
      </div>

      <div class="mb-6 text-slate-300">
        <p>You have <span class="text-amber-400 font-bold">{{ attemptsRemaining }}</span> attempts remaining today</p>
      </div>

      <button
        (click)="startGame()"
        class="px-8 py-4 text-lg font-bold text-white bg-gradient-to-r from-green-600 to-green-500 rounded-xl hover:from-green-500 hover:to-green-400 transition-all duration-300 shadow-lg shadow-green-600/30 transform hover:scale-105 w-full"
      >
        Start Game
      </button>
    </div>
  </div>

  <!-- Joystick -->
  <div
    *ngIf="gameStarted && !gameOver"
    #joystickArea
    class="joystick-container"
    (mousedown)="initJoystick($event)"
    (mousemove)="moveJoystick($event)"
    (mouseup)="stopJoystick()"
    (touchstart)="initJoystick($event)"
    (touchmove)="moveJoystick($event)"
    (touchend)="stopJoystick()"
  >
    <!-- Joystick background elements -->
    <div class="absolute inset-0 rounded-full border-2 border-slate-600/50"></div>
    <div class="absolute inset-2 rounded-full border border-slate-500/30"></div>

    <!-- Direction indicators -->
    <div class="absolute top-2 left-1/2 transform -translate-x-1/2 text-slate-400 text-xs font-bold">UP</div>
    <div class="absolute bottom-2 left-1/2 transform -translate-x-1/2 text-slate-400 text-xs font-bold">DOWN</div>
    <div class="absolute left-2 top-1/2 transform -translate-y-1/2 text-slate-400 text-xs font-bold">LEFT</div>
    <div class="absolute right-2 top-1/2 transform -translate-y-1/2 text-slate-400 text-xs font-bold">RIGHT</div>

    <!-- Joystick handle with improved styling -->
    <div
      #joystickHandle
      class="joystick-handle"
      [ngStyle]="joystickPosition"
    >
      <!-- Inner circle for depth effect -->
      <div class="absolute inset-2 rounded-full bg-gradient-to-br from-slate-600 to-slate-700 shadow-inner"></div>
    </div>
  </div>
</div>
