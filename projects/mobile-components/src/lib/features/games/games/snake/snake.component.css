:host {
  display: block;
  width: 100%;
  height: 100%;
  font-family: "Inter", sans-serif;
}

.game-container {
  @apply flex flex-col items-center w-full h-full p-2 bg-gradient-to-br from-orange-500/50 to-red-500/50;
}

.score-container {
  @apply w-full mb-2 px-4 py-2 bg-black/30 rounded-lg;
}

.canvas-wrapper {
  position: relative;
  aspect-ratio: 1;
  @apply w-full max-w-[min(100vw,400px)] rounded-xl overflow-hidden shadow-xl;
  border: 4px solid theme("colors.emerald.500");
  box-shadow: 0 0 30px theme("colors.emerald.500/30");
  transition: all 0.3s ease;
  animation: canvasPulse 4s infinite alternate;
}

.canvas-wrapper::before {
  content: "";
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, #10b981, #059669, #047857, #059669, #10b981);
  background-size: 400% 400%;
  z-index: -1;
  border-radius: inherit;
  animation: borderGlow 8s ease infinite;
  filter: blur(8px);
  opacity: 0.7;
}

canvas {
  @apply block w-full h-full;
  image-rendering: pixelated;
}

@keyframes canvasPulse {
  0% {
    box-shadow: 0 0 20px theme("colors.emerald.500/20");
  }
  100% {
    box-shadow: 0 0 30px theme("colors.emerald.500/40");
  }
}

@keyframes borderGlow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.game-over-overlay {
  @apply flex flex-col justify-center items-center absolute inset-0
         bg-black/85 backdrop-blur-sm text-center px-4;
  animation: fadeIn 0.5s ease-out;
}

.game-over-title {
  @apply text-6xl font-bold text-red-500 mb-6;
  text-shadow: 0 0 20px theme("colors.red.500/70");
  animation: pulse 1.5s infinite;
  background: linear-gradient(to right, #ef4444, #f97316, #ef4444);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  background-size: 200% auto;
  animation: textShine 3s linear infinite;
}

.final-score {
  @apply text-3xl font-semibold text-yellow-400 mb-8;
  text-shadow: 0 0 10px theme("colors.yellow.400/50");
}

@keyframes textShine {
  0% {
    background-position: 0% center;
  }
  100% {
    background-position: 200% center;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.restart-button {
  @apply px-8 py-3 text-xl font-bold text-white bg-blue-500 rounded-full
         shadow-lg transition-all duration-300 transform hover:scale-105
         hover:bg-blue-600 focus:outline-none focus:ring-4 focus:ring-blue-400/50;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.start-button {
  @apply px-8 py-3 mt-4 text-xl font-bold text-white bg-green-500 rounded-full
         shadow-lg transition-all duration-300 transform hover:scale-105
         hover:bg-green-600 focus:outline-none focus:ring-4 focus:ring-green-400/50;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.joystick-container {
  @apply fixed bottom-20 left-4 w-32 h-32 rounded-full bg-slate-800/80 backdrop-blur-md;
  border: 2px solid theme("colors.slate.700");
  box-shadow: 0 0 20px theme("colors.slate.900/50"), inset 0 0 15px theme("colors.slate.900/50");
  touch-action: none; /* Prevent scrolling while using joystick */
  z-index: 40; /* Make sure it's above other elements */
}

.joystick-handle {
  @apply absolute w-16 h-16 left-1/2 top-1/2 rounded-full
         bg-gradient-to-br from-slate-500 to-slate-800 shadow-lg;
  border: 2px solid theme("colors.slate.600");
  box-shadow: 0 4px 10px theme("colors.black/50");
  will-change: transform;
  touch-action: none;
  transition: box-shadow 0.1s ease;
}

.joystick-handle:active {
  box-shadow: 0 2px 5px theme("colors.black/50");
}

/* Add visual guide for joystick movement */
.joystick-container::after {
  content: "";
  @apply absolute rounded-full;
  width: calc(100% - 4px);
  height: calc(100% - 4px);
  top: 2px;
  left: 2px;
  border: 1px dashed theme("colors.gray.500/30");
  pointer-events: none;
}

/* Add center dot */
.joystick-container::before {
  content: "";
  @apply absolute w-2 h-2 rounded-full bg-gray-500/50;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.scale-in {
  animation: scaleIn 0.3s ease-out forwards;
}

.mascot-bounce {
  animation: bounce 2s ease-in-out infinite;
}

.win-animation {
  animation: speed-increase-flash 0.5s ease-in-out;
}

@keyframes speed-increase-flash {
  0% {
    box-shadow: 0 0 0 rgba(16, 185, 129, 0);
    border-color: theme("colors.emerald.500");
  }
  50% {
    box-shadow: 0 0 30px rgba(16, 185, 129, 0.8);
    border-color: theme("colors.emerald.300");
  }
  100% {
    box-shadow: 0 0 0 rgba(16, 185, 129, 0);
    border-color: theme("colors.emerald.500");
  }
}

@keyframes rainbow-bg {
  0% { background: rgba(255, 0, 0, 0.1); }
  17% { background: rgba(255, 165, 0, 0.1); }
  33% { background: rgba(255, 255, 0, 0.1); }
  50% { background: rgba(0, 128, 0, 0.1); }
  67% { background: rgba(0, 0, 255, 0.1); }
  83% { background: rgba(75, 0, 130, 0.1); }
  100% { background: rgba(238, 130, 238, 0.1); }
}

/* Snake and Food styles */
.snake-segment {
  @apply absolute rounded-sm;
  transition: all 0.1s linear;
}

.food {
  @apply absolute rounded-full;
  box-shadow: 0 0 10px theme("colors.yellow.500/50");
}

/* Updated responsive adjustments */
@media (max-width: 640px) {
  .game-container {
    @apply p-0;
  }

  .score-container {
    @apply mb-1 px-2 py-1;
  }

  .canvas-wrapper {
    @apply rounded-lg;
    border-width: 2px;
  }

  .joystick-container {
    @apply w-24 h-24 mt-2;
  }

  .joystick-handle {
    @apply w-12 h-12;
  }
}

/* Add landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 500px) {
  .game-container {
    @apply flex-row justify-around items-center;
  }

  .score-container {
    @apply w-1/4 mb-0;
  }

  .canvas-wrapper {
    @apply max-h-[90vh] w-auto;
  }

  .joystick-container {
    @apply mt-0;
  }
}

.no-attempts {
  @apply text-xl font-semibold text-red-400 mb-4;
  text-shadow: 0 0 10px theme("colors.red.400/50");
}

.difficulty-indicator {
  @apply text-sm font-medium text-white/80 mt-1 text-center;
}

.rewards-summary {
  @apply mb-6 text-yellow-400;
}

.rewards-summary h3 {
  @apply text-lg font-semibold mb-2;
}

.rewards-summary ul {
  @apply list-none p-0;
}

.rewards-summary li {
  @apply text-sm mb-1;
}

/* Touch Controls */
.touch-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
}

.joystick-area {
  position: relative;
  width: 120px;
  height: 120px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  touch-action: none;
}

.joystick-handle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 48px;
  height: 48px;
  background: #4a5568;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

@media (hover: none) {
  .joystick-area {
    display: block;
  }
}

@media (hover: hover) {
  .joystick-area {
    display: none;
  }
}
