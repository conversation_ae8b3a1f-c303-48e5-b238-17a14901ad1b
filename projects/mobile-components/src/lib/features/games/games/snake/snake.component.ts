import {
  <PERSON>mpo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ViewChild,
  ElementRef,
  NgZone,
  HostListener,
  Input,
  Output,
  EventEmitter,
} from '@angular/core';
import { NgIf, NgStyle, NgFor } from '@angular/common';
import { Router } from '@angular/router';
import { GameService } from 'lp-client-api';
import {
  Game,
  SnakeConfig,
  GameProgressType,
  SnakeProgress,
  GameEvent,
  GameInstance,
} from 'lp-client-api';
import { Subject, Observable, of } from 'rxjs';
import { takeUntil, catchError, switchMap } from 'rxjs/operators';
import { GamesScoreComponent } from '../components/games-score/games-score.component';

interface SnakeParticipation {
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  frequencyAttempts: number;
  lastParticipation: Date;
  rewards: {
    points: number;
    bonuses: any[];
  };
}

// Add interface for bonus types
interface GameBonus {
  type: 'PERFECT_GAME' | 'SPEED_MASTER' | 'SNAKE_MASTER';
  points: number;
}

@Component({
  selector: 'app-games-snake',
  templateUrl: './snake.component.html',
  styleUrls: ['./snake.component.css'],
  standalone: true,
  imports: [NgIf, NgStyle, NgFor, GamesScoreComponent]
})
export class SnakeComponent implements OnInit, OnDestroy {
  @ViewChild('gameCanvas', { static: false })
  gameCanvas!: ElementRef<HTMLCanvasElement>;
  @ViewChild('joystickArea') joystickArea!: ElementRef;
  @ViewChild('joystickHandle') joystickHandle!: ElementRef;

  @Input() gameId?: string;
  @Input() game?: Game;
  @Input() gameInstance?: GameInstance;
  @Input() config: any = {};

  @Output() gameEvent = new EventEmitter<GameEvent>();

  private destroy$ = new Subject<void>();
  private gameLoop$: any;
  private joystickActive = false;
  private joystickCenter = { x: 0, y: 0 };
  private readonly JOYSTICK_MAX_DISTANCE = 40;

  // Game state
  gameConfigId: number | null = null;
  gameProgress: GameProgressType<SnakeProgress> | undefined;
  canPlay: boolean = false;
  gameInstanceId: number | null = null;
  showAnimation: boolean = false;
  demoMode: boolean = false;

  // Game metrics
  canvasSize: number = 400;
  ctx: CanvasRenderingContext2D | null = null;
  snake: { x: number; y: number }[] = [{ x: 10, y: 10 }];
  food: { x: number; y: number } = { x: 0, y: 0 };
  direction: string = 'right';
  gameOver: boolean = false;
  score: number = 0;
  highScore: number = 0;
  level: number = 1;
  speed: number = 100;
  gameStarted: boolean = false;
  canvasReady: boolean = false;
  attemptsRemaining: number = 0;
  joystickPosition = { transform: 'translate(-50%, -50%)' };
  startTime: number | null = null;

  // Default configuration
  private readonly DEFAULT_CONFIG = {
    gridSize: 20,
    initialSpeed: 150, // Slower initial speed (higher number = slower)
    speedIncrease: 0.97, // More gradual speed increase (closer to 1 = more gradual)
    foodValue: 10,
    maxSpeed: 50,
    speedIncreaseInterval: 3, // Increase speed every X food items
  };

  // Difficulty settings
  difficultySettings = {
    EASY: {
      gridSize: 15,
      initialSpeed: 200, // Slower initial speed
      speedIncrease: 0.98, // More gradual speed increase
      pointMultiplier: 1,
      speedIncreaseInterval: 4, // Increase speed every 4 food items
    },
    MEDIUM: {
      gridSize: 20,
      initialSpeed: 150, // Slower initial speed
      speedIncrease: 0.97, // More gradual speed increase
      pointMultiplier: 2,
      speedIncreaseInterval: 3, // Increase speed every 3 food items
    },
    HARD: {
      gridSize: 25,
      initialSpeed: 120, // Slower initial speed
      speedIncrease: 0.95, // More gradual speed increase
      pointMultiplier: 3,
      speedIncreaseInterval: 2, // Increase speed every 2 food items
    },
  };

  constructor(private ngZone: NgZone, private gameService: GameService, private router: Router) {}

  ngOnInit(): void {
    if (!this.game?.id) {
      console.error('No game ID found');
      return;
    }

    // Check if game is available to play
    this.gameService
      .checkGameAvailability(this.game.id)
      .pipe(
        catchError((error: Error) => {
          console.warn('Error checking game availability, allowing play:', error);
          this.demoMode = true; // Set demo mode when API fails
          return of(true); // Allow play on error
        })
      )
      .subscribe({
        next: (canPlay: boolean) => {
          this.canPlay = canPlay;
          if (canPlay) {
            this.initGame();
          }
        },
      });

    // Set default attempts for demo mode
    this.attemptsRemaining = 3;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    if (this.gameLoop$) {
      clearTimeout(this.gameLoop$);
    }
  }

  private loadGameProgress(): Observable<any> {
    if (!this.game?.id) {
      return of(null);
    }

    // If no instance exists, we'll create one when starting the game
    if (!this.gameInstanceId) {
      return of(null);
    }

    // Get existing instance details
    return this.gameService
      .getGameInstance(this.game.id, this.gameInstanceId)
      .pipe(
        catchError((error) => {
          console.warn('Error loading game instance:', error);
          return of(null);
        })
      );
  }

  startGame(): void {
    if (!this.game?.id || !this.canPlay) {
      console.warn('Cannot start game - invalid configuration or cannot play');
      return;
    }

    // Reset game state
    this.gameOver = false;
    this.score = 0;
    this.level = 1;
    this.speed = this.DEFAULT_CONFIG.initialSpeed;
    this.snake = [{ x: 10, y: 10 }];
    this.direction = 'right';
    this.gameStarted = true;

    // If in demo mode, start game loop directly without API calls
    if (this.demoMode) {
      this.gameInstanceId = Math.floor(Math.random() * 1000) + 1; // Generate a random ID for demo mode
      this.startGameLoop();
      return;
    }

    // Create new game instance if needed
    if (!this.gameInstanceId) {
      this.gameService.createGameInstance(this.game.id).subscribe({
        next: (instance) => {
          this.gameInstanceId = instance.id;
          this.startGameLoop();
        },
        error: (error) => {
          console.error('Error creating game instance:', error);
          // Fall back to demo mode if API call fails
          this.demoMode = true;
          this.gameInstanceId = Math.floor(Math.random() * 1000) + 1;
          this.startGameLoop();
        },
      });
    } else {
      this.startGameLoop();
    }
  }

  private startGameLoop(): void {
    if (!this.gameStarted || !this.ctx) return;

    // Generate initial food position
    this.generateFood();

    // Start game loop
    const gameLoop = () => {
      if (!this.gameStarted || this.gameOver) return;

      this.moveSnake();
      this.checkCollision();
      this.draw();

      // Save game state periodically
      if (this.game?.id && this.gameInstanceId) {
        const gameEvent: GameEvent = {
          id: this.gameInstanceId,
          level: this.level,
          score: this.score,
          duration: Date.now() - this.startTime!,
          state: JSON.stringify({
            snake: this.snake,
            food: this.food,
            direction: this.direction,
          }),
          payload: JSON.stringify({
            configId: this.game.id,
            completed: false,
          }),
        };
        this.gameEvent.emit(gameEvent);
      }

      // Schedule next frame
      this.gameLoop$ = setTimeout(() => {
        this.ngZone.run(() => gameLoop());
      }, this.speed);
    };

    // Start the loop
    this.startTime = Date.now();
    this.ngZone.run(gameLoop);
  }

  private updateGameState(): void {
    // Skip API calls in demo mode
    if (this.demoMode) {
      if (!this.gameOver) {
        this.gameLoop();
      }
      return;
    }

    if (!this.gameConfigId || !this.gameInstanceId) {
      console.error('Missing game config or instance ID', {
        gameConfigId: this.gameConfigId,
        gameInstanceId: this.gameInstanceId,
      });
      return;
    }

    console.log('Current game state before update:', {
      config: this.config,
      snake: this.snake,
      food: this.food,
      score: this.score,
      level: this.level,
      gameOver: this.gameOver,
    });

    const bonuses = this.calculateBonuses();
    const multiplier = this.getDifficultyMultiplier(
      this.config.difficulty || 'EASY'
    );

    const gameEvent = {
      level: this.level,
      score: Math.floor(this.score * multiplier),
      duration: 0,
      payload: JSON.stringify({
        snake: this.snake,
        food: this.food,
        direction: this.direction,
        bonuses: bonuses,
        gameOver: this.gameOver,
      }),
      state: '8ea3e53a6d8f75dcb6429bf23bce5c29', // Required by API
    };

    console.log('Sending game event:', {
      url: `/mobile-games/api/v1/config/${this.gameConfigId}/instance/${this.gameInstanceId}/event`,
      gameEvent: gameEvent,
      bonuses: bonuses,
      multiplier: multiplier,
    });

    this.gameService
      .createGameEvent(this.gameConfigId, this.gameInstanceId, gameEvent)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (event) => {
          console.log('Game state updated successfully:', event);
        },
        error: (error) => {
          console.error('Error updating game state:', {
            error: error,
            request: gameEvent,
            response: error.error,
          });
          // Set demo mode if API call fails
          this.demoMode = true;
          // Continue game even if update fails
          if (!this.gameOver) {
            this.gameLoop();
          }
        },
      });
  }

  private handleWin(): void {
    this.gameOver = true;
    this.gameStarted = false;
    this.showAnimation = true;

    if (this.score > this.highScore) {
      this.highScore = this.score;
    }

    // Emit final game event
    if (this.game?.id && this.gameInstanceId) {
      const gameEvent: GameEvent = {
        id: this.gameInstanceId,
        level: this.level,
        score: this.score,
        duration: Date.now() - this.startTime!,
        state: JSON.stringify({
          snake: this.snake,
          food: this.food,
          direction: this.direction,
          gameOver: true,
          won: true
        }),
        payload: JSON.stringify({
          configId: this.game.id,
          completed: true,
          won: true
        })
      };
      this.gameEvent.emit(gameEvent);
    }

    // Clear game loop
    if (this.gameLoop$) {
      clearTimeout(this.gameLoop$);
    }
  }

  private handleLoss(): void {
    this.gameOver = true;
    this.gameStarted = false;

    if (this.score > this.highScore) {
      this.highScore = this.score;
    }

    // In demo mode, just decrement attempts
    if (this.demoMode) {
      this.attemptsRemaining = Math.max(0, this.attemptsRemaining - 1);

      // Clear game loop
      if (this.gameLoop$) {
        clearTimeout(this.gameLoop$);
      }
      return;
    }

    // Emit final game event
    if (this.game?.id && this.gameInstanceId) {
      const gameEvent: GameEvent = {
        id: this.gameInstanceId,
        level: this.level,
        score: this.score,
        duration: Date.now() - this.startTime!,
        state: JSON.stringify({
          snake: this.snake,
          food: this.food,
          direction: this.direction,
          gameOver: true,
          won: false
        }),
        payload: JSON.stringify({
          configId: this.game.id,
          completed: true,
          won: false
        })
      };
      this.gameEvent.emit(gameEvent);
    }

    // Clear game loop
    if (this.gameLoop$) {
      clearTimeout(this.gameLoop$);
    }
  }

  private initGame(): void {
    // Initialize canvas after view is ready
    setTimeout(() => {
      if (this.gameCanvas && this.gameCanvas.nativeElement) {
        this.ctx = this.gameCanvas.nativeElement.getContext('2d');
        this.canvasReady = true;
        this.draw();
      }
    });

    // Initialize game with instance if provided
    if (this.gameInstance) {
      console.log('Initializing with game instance:', this.gameInstance);
      this.gameInstanceId = this.gameInstance.id;
      if (this.gameInstance.gameEvents?.length > 0) {
        // Load saved state from last event
        try {
          const lastEvent = this.gameInstance.gameEvents[this.gameInstance.gameEvents.length - 1];
          const state = JSON.parse(lastEvent.state);
          this.snake = state.snake;
          this.food = state.food;
          this.direction = state.direction;
          this.score = lastEvent.score;
          this.level = lastEvent.level;
        } catch (e) {
          console.warn('Could not parse saved game state:', e);
        }
      }
    }
  }

  private initializeGame(): void {
    // Set initial game state
    this.snake = [{ x: 10, y: 10 }];
    this.direction = 'right';
    this.gameOver = false;
    this.score = 0;
    this.level = 1;
    this.speed = this.config?.initialSpeed || this.DEFAULT_CONFIG.initialSpeed;

    // Generate initial food
    this.food = this.generateFood();

    // Draw initial state
    this.draw();
  }

  private gameLoop(): void {
    if (!this.gameStarted || this.gameOver) return;

    this.moveSnake();
    this.checkCollision();
    this.draw();

    // Schedule next frame
    this.gameLoop$ = setTimeout(() => {
      this.ngZone.run(() => this.gameLoop());
    }, this.speed);
  }

  private moveSnake(): void {
    if (!this.gameStarted || this.gameOver) return;

    // Calculate new head position
    const head = { ...this.snake[0] };
    switch (this.direction) {
      case 'up':
        head.y--;
        break;
      case 'down':
        head.y++;
        break;
      case 'left':
        head.x--;
        break;
      case 'right':
        head.x++;
        break;
    }

    // Check for wall collision
    const gridSize = this.config.gridSize || this.DEFAULT_CONFIG.gridSize;
    if (head.x < 0 || head.x >= gridSize || head.y < 0 || head.y >= gridSize) {
      this.handleLoss();
      return;
    }

    // Check for self collision
    if (
      this.snake.some((segment) => segment.x === head.x && segment.y === head.y)
    ) {
      this.handleLoss();
      return;
    }

    // Add new head
    this.snake.unshift(head);

    // Check for food collision
    if (head.x === this.food.x && head.y === this.food.y) {
      // Increase score
      this.score += 10;

      // Generate new food
      this.food = this.generateFood();

      // Get the speed increase interval from config or default
      const speedIncreaseInterval = this.config.speedIncreaseInterval || this.DEFAULT_CONFIG.speedIncreaseInterval;

      // Only increase speed at certain intervals based on food eaten (score/10 = food count)
      if ((this.score / 10) % speedIncreaseInterval === 0) {
        // Increase speed (multiply by speedIncrease factor - lower number = faster)
        this.speed *= this.config.speedIncrease || this.DEFAULT_CONFIG.speedIncrease;

        // Add visual feedback for speed increase
        this.showAnimation = true;
        setTimeout(() => {
          this.showAnimation = false;
        }, 500);
      }

      // Level up every 5 food items
      if (this.score % 50 === 0) {
        this.level++;
      }

      // Update game state
      this.updateGameState();
    } else {
      // Remove tail if no food eaten
      this.snake.pop();
    }
  }

  private checkCollision(): void {
    const head = this.snake[0];
    const gridSize = this.config.gridSize || this.DEFAULT_CONFIG.gridSize;

    // Check wall collision
    if (head.x < 0 || head.x >= gridSize || head.y < 0 || head.y >= gridSize) {
      this.handleLoss();
      return;
    }

    // Check self collision (skip head)
    for (let i = 1; i < this.snake.length; i++) {
      if (this.snake[i].x === head.x && this.snake[i].y === head.y) {
        this.handleLoss();
        return;
      }
    }
  }

  private draw(): void {
    if (!this.ctx || !this.canvasReady) return;

    const gridSize = this.config.gridSize || this.DEFAULT_CONFIG.gridSize;
    const cellSize = this.canvasSize / gridSize;
    const ctx = this.ctx; // Store context to avoid null checks

    // Create gradient background
    const gradient = ctx.createLinearGradient(0, 0, this.canvasSize, this.canvasSize);
    gradient.addColorStop(0, '#1a2a3a');
    gradient.addColorStop(1, '#0f172a');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, this.canvasSize, this.canvasSize);

    // Draw grid lines (subtle)
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.05)';
    ctx.lineWidth = 1;

    // Draw vertical grid lines
    for (let x = 0; x <= gridSize; x++) {
      ctx.beginPath();
      ctx.moveTo(x * cellSize, 0);
      ctx.lineTo(x * cellSize, this.canvasSize);
      ctx.stroke();
    }

    // Draw horizontal grid lines
    for (let y = 0; y <= gridSize; y++) {
      ctx.beginPath();
      ctx.moveTo(0, y * cellSize);
      ctx.lineTo(this.canvasSize, y * cellSize);
      ctx.stroke();
    }

    // Draw snake with gradient and rounded corners
    const head = this.snake[0];

    // Draw snake body segments
    for (let i = this.snake.length - 1; i > 0; i--) {
      const segment = this.snake[i];
      const segmentGradient = ctx.createRadialGradient(
        segment.x * cellSize + cellSize / 2,
        segment.y * cellSize + cellSize / 2,
        0,
        segment.x * cellSize + cellSize / 2,
        segment.y * cellSize + cellSize / 2,
        cellSize / 2
      );
      segmentGradient.addColorStop(0, '#4ade80'); // Light green
      segmentGradient.addColorStop(1, '#16a34a'); // Dark green

      ctx.fillStyle = segmentGradient;
      ctx.beginPath();
      ctx.roundRect(
        segment.x * cellSize + 1,
        segment.y * cellSize + 1,
        cellSize - 2,
        cellSize - 2,
        cellSize / 4 // Rounded corners
      );
      ctx.fill();
    }

    // Draw snake head with special color
    const headGradient = ctx.createRadialGradient(
      head.x * cellSize + cellSize / 2,
      head.y * cellSize + cellSize / 2,
      0,
      head.x * cellSize + cellSize / 2,
      head.y * cellSize + cellSize / 2,
      cellSize / 2
    );
    headGradient.addColorStop(0, '#84cc16'); // Lighter green
    headGradient.addColorStop(1, '#65a30d'); // Darker green

    ctx.fillStyle = headGradient;
    ctx.beginPath();
    ctx.roundRect(
      head.x * cellSize + 1,
      head.y * cellSize + 1,
      cellSize - 2,
      cellSize - 2,
      cellSize / 3 // More rounded corners for head
    );
    ctx.fill();

    // Add eyes to snake head
    ctx.fillStyle = 'white';

    // Position eyes based on direction
    let leftEyeX, leftEyeY, rightEyeX, rightEyeY;
    const eyeSize = cellSize / 6;
    const eyeOffset = cellSize / 3;

    switch (this.direction) {
      case 'up':
        leftEyeX = head.x * cellSize + cellSize / 3;
        leftEyeY = head.y * cellSize + cellSize / 3;
        rightEyeX = head.x * cellSize + cellSize * 2/3;
        rightEyeY = head.y * cellSize + cellSize / 3;
        break;
      case 'down':
        leftEyeX = head.x * cellSize + cellSize / 3;
        leftEyeY = head.y * cellSize + cellSize * 2/3;
        rightEyeX = head.x * cellSize + cellSize * 2/3;
        rightEyeY = head.y * cellSize + cellSize * 2/3;
        break;
      case 'left':
        leftEyeX = head.x * cellSize + cellSize / 3;
        leftEyeY = head.y * cellSize + cellSize / 3;
        rightEyeX = head.x * cellSize + cellSize / 3;
        rightEyeY = head.y * cellSize + cellSize * 2/3;
        break;
      case 'right':
        leftEyeX = head.x * cellSize + cellSize * 2/3;
        leftEyeY = head.y * cellSize + cellSize / 3;
        rightEyeX = head.x * cellSize + cellSize * 2/3;
        rightEyeY = head.y * cellSize + cellSize * 2/3;
        break;
      default:
        leftEyeX = head.x * cellSize + cellSize / 3;
        leftEyeY = head.y * cellSize + cellSize / 3;
        rightEyeX = head.x * cellSize + cellSize * 2/3;
        rightEyeY = head.y * cellSize + cellSize / 3;
    }

    // Draw eyes
    ctx.beginPath();
    ctx.arc(leftEyeX, leftEyeY, eyeSize, 0, Math.PI * 2);
    ctx.fill();

    ctx.beginPath();
    ctx.arc(rightEyeX, rightEyeY, eyeSize, 0, Math.PI * 2);
    ctx.fill();

    // Draw pupils (black)
    ctx.fillStyle = 'black';
    ctx.beginPath();
    ctx.arc(leftEyeX, leftEyeY, eyeSize / 2, 0, Math.PI * 2);
    ctx.fill();

    ctx.beginPath();
    ctx.arc(rightEyeX, rightEyeY, eyeSize / 2, 0, Math.PI * 2);
    ctx.fill();

    // Draw food with glow effect
    ctx.fillStyle = '#ef4444';
    ctx.shadowColor = '#ef4444';
    ctx.shadowBlur = 10;
    ctx.beginPath();
    ctx.arc(
      this.food.x * cellSize + cellSize / 2,
      this.food.y * cellSize + cellSize / 2,
      cellSize / 2 - 2,
      0,
      Math.PI * 2
    );
    ctx.fill();

    // Reset shadow
    ctx.shadowBlur = 0;

    // Add shine to food
    ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
    ctx.beginPath();
    ctx.arc(
      this.food.x * cellSize + cellSize / 3,
      this.food.y * cellSize + cellSize / 3,
      cellSize / 6,
      0,
      Math.PI * 2
    );
    ctx.fill();
  }

  private generateFood(): { x: number; y: number } {
    const gridSize = this.config.gridSize || this.DEFAULT_CONFIG.gridSize;
    let food: { x: number; y: number };
    do {
      food = {
        x: Math.floor(Math.random() * gridSize),
        y: Math.floor(Math.random() * gridSize),
      };
      // Make sure food doesn't spawn on snake
    } while (
      this.snake.some((segment) => segment.x === food.x && segment.y === food.y)
    );
    return food;
  }

  @HostListener('window:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent): void {
    if (!this.gameStarted || this.gameOver) return;

    switch (event.key) {
      case 'ArrowUp':
        if (this.direction !== 'down') this.direction = 'up';
        break;
      case 'ArrowDown':
        if (this.direction !== 'up') this.direction = 'down';
        break;
      case 'ArrowLeft':
        if (this.direction !== 'right') this.direction = 'left';
        break;
      case 'ArrowRight':
        if (this.direction !== 'left') this.direction = 'right';
        break;
    }
  }

  // Touch control methods
  @HostListener('touchstart', ['$event'])
  public onTouchStart(event: TouchEvent): void {
    if (!this.gameStarted || this.gameOver) return;
    event.preventDefault();
    this.initJoystick(event);
  }

  @HostListener('touchmove', ['$event'])
  public onTouchMove(event: TouchEvent): void {
    if (!this.gameStarted || this.gameOver) return;
    event.preventDefault();
    this.moveJoystick(event);
  }

  @HostListener('touchend')
  public onTouchEnd(): void {
    this.stopJoystick();
  }

  public initJoystick(event: TouchEvent | MouseEvent): void {
    if (!this.joystickArea || !this.joystickHandle) return;

    this.joystickActive = true;
    const areaRect = this.joystickArea.nativeElement.getBoundingClientRect();
    this.joystickCenter = {
      x: areaRect.left + areaRect.width / 2,
      y: areaRect.top + areaRect.height / 2
    };

    if (event instanceof TouchEvent) {
      this.updateJoystickPosition(event.touches[0].clientX, event.touches[0].clientY);
    } else {
      this.updateJoystickPosition(event.clientX, event.clientY);
    }
  }

  public moveJoystick(event: TouchEvent | MouseEvent): void {
    if (!this.joystickActive) return;

    if (event instanceof TouchEvent) {
      this.updateJoystickPosition(event.touches[0].clientX, event.touches[0].clientY);
    } else {
      this.updateJoystickPosition(event.clientX, event.clientY);
    }
  }

  public stopJoystick(): void {
    this.joystickActive = false;
    if (this.joystickHandle) {
      this.joystickHandle.nativeElement.style.transform = 'translate(-50%, -50%)';
    }
  }

  private updateJoystickPosition(clientX: number, clientY: number): void {
    if (!this.joystickHandle || !this.joystickActive) return;

    const deltaX = clientX - this.joystickCenter.x;
    const deltaY = clientY - this.joystickCenter.y;
    const angle = Math.atan2(deltaY, deltaX);
    const distance = Math.min(
      this.JOYSTICK_MAX_DISTANCE,
      Math.sqrt(deltaX * deltaX + deltaY * deltaY)
    );

    // Update joystick visual position
    const moveX = Math.cos(angle) * distance;
    const moveY = Math.sin(angle) * distance;
    this.joystickHandle.nativeElement.style.transform =
      `translate(calc(-50% + ${moveX}px), calc(-50% + ${moveY}px))`;

    // Determine direction based on angle
    const deg = angle * (180 / Math.PI);
    if (Math.abs(deltaX) > 10 || Math.abs(deltaY) > 10) {
      if (deg >= -45 && deg < 45 && this.direction !== 'left') {
        this.direction = 'right';
      } else if (deg >= 45 && deg < 135 && this.direction !== 'up') {
        this.direction = 'down';
      } else if ((deg >= 135 || deg < -135) && this.direction !== 'right') {
        this.direction = 'left';
      } else if (deg >= -135 && deg < -45 && this.direction !== 'down') {
        this.direction = 'up';
      }
    }
  }

  private applyDifficultySettings(difficulty: string): void {
    const settings =
      this.difficultySettings[
        difficulty as keyof typeof this.difficultySettings
      ];
    if (settings) {
      // Update the config at root level
      this.config = {
        ...this.config,
        gridSize: settings.gridSize,
        initialSpeed: settings.initialSpeed,
        speedIncrease: settings.speedIncrease,
      };
    }
  }

  public getDifficultyMultiplier(difficulty: string): number {
    const validDifficulty = difficulty as keyof typeof this.difficultySettings;
    return this.difficultySettings[validDifficulty]?.pointMultiplier || 1;
  }

  public getBonuses(): GameBonus[] {
    return (
      (this.gameProgress?.gameSpecificProgress as any)?.bonusesAwarded || []
    );
  }

  public getSpeedPercentage(): number {
    // Convert the speed value (where higher = slower) to a percentage (where higher = faster)
    // 150 (initial) = 0%, 50 (max) = 100%
    return Math.round(100 - (this.speed - 50) / 1.5);
  }

  private calculateBonuses(): GameBonus[] {
    const bonuses: GameBonus[] = [];
    const maxSpeed = this.config.maxSpeed ?? this.DEFAULT_CONFIG.maxSpeed;

    if (this.snake.length === this.score / 10 + 1) {
      bonuses.push({
        type: 'PERFECT_GAME',
        points: Math.floor(this.score * 0.1),
      });
    }
    if (this.speed <= maxSpeed) {
      bonuses.push({
        type: 'SPEED_MASTER',
        points: Math.floor(this.score * 0.05),
      });
    }
    if (this.snake.length >= 20) {
      bonuses.push({
        type: 'SNAKE_MASTER',
        points: Math.floor(this.score * 0.15),
      });
    }

    return bonuses;
  }

  public resetGameProgress(): void {
    if (!this.game?.id) {
      console.warn('Cannot reset progress - no game ID');
      return;
    }

    this.gameService
      .resetGameProgress(this.game.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          console.log('Game progress reset');
          this.score = 0;
          this.level = 1;
          this.highScore = 0;
        },
        error: (error) => {
          console.error('Error resetting game progress:', error);
        },
      });
  }

  public restartGame(): void {
    if (!this.game?.id || this.attemptsRemaining <= 0) {
      console.warn('Cannot restart game - no attempts remaining or no game ID');
      return;
    }

    // In demo mode, restart directly without API calls
    if (this.demoMode) {
      this.gameInstanceId = Math.floor(Math.random() * 1000) + 1;
      this.score = 0;
      this.level = 1;
      this.snake = [{ x: 10, y: 10 }];
      this.direction = 'right';
      this.gameOver = false;
      this.startGame();
      return;
    }

    // Create new game instance
    this.gameService.createGameInstance(this.game.id).subscribe({
      next: (instance) => {
        this.gameInstanceId = instance.id;
        this.score = 0;
        this.level = 1;
        this.snake = [{ x: 10, y: 10 }];
        this.direction = 'right';
        this.gameOver = false;
        this.startGame();
      },
      error: (error) => {
        console.error('Error creating new game instance:', error);
        // Fall back to demo mode if API call fails
        this.demoMode = true;
        this.gameInstanceId = Math.floor(Math.random() * 1000) + 1;
        this.score = 0;
        this.level = 1;
        this.snake = [{ x: 10, y: 10 }];
        this.direction = 'right';
        this.gameOver = false;
        this.startGame();
      },
    });
  }

  public navigateToGames(): void {
    // Navigate back to the games list
    this.router.navigate(['/public/games/all']);
  }
}
