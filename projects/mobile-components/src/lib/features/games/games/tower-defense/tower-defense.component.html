<div class="game-container">
  <!-- Score Display -->
  <games-score
    [scores]="[
      { title: 'Score', number: score },
      { title: 'Gold', number: gold },
      { title: 'Lives', number: lives },
      { title: 'Wave', number: wave }
    ]"
  ></games-score>

  <!-- Game Title -->
  <div class="game-title">
    <h1>Tower Defense</h1>
    <p>Build towers to defend against waves of enemies!</p>
  </div>

  <!-- Tower Selection -->
  <div class="tower-selection">
    <div class="tower-types">
      <div
        *ngFor="let type of towerTypes"
        class="tower-type"
        [ngClass]="{'selected': selectedTowerType === type.name}"
        (click)="selectTowerType(type.name)"
      >
        <div class="tower-icon" [ngStyle]="{'background': type.color}"></div>
        <div class="tower-info">
          <div class="tower-name">{{ type.name }}</div>
          <div class="tower-cost">{{ type.cost }} Gold</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Game Board -->
  <div class="game-board">
    <!-- Path -->
    <div class="path"></div>

    <!-- Enemies -->
    <div
      *ngFor="let enemy of enemies"
      class="enemy"
      [ngClass]="'enemy-' + enemy.type"
      [style.left.%]="enemy.x"
      [style.top.%]="enemy.y"
    >
      <div class="health-bar">
        <div class="health-fill" [style.width.%]="getHealthPercentage(enemy)"></div>
      </div>
    </div>

    <!-- Towers -->
    <div
      *ngFor="let tower of towers"
      class="tower"
      [ngClass]="'tower-level-' + tower.level"
      [style.left.%]="tower.x"
      [style.top.%]="tower.y"
    >
      <div class="tower-range" [style.width.px]="tower.range * 20" [style.height.px]="tower.range * 20"></div>
    </div>

    <!-- Tower placement grid -->
    <div class="placement-grid">
      <div
        *ngFor="let _ of [].constructor(100); let i = index"
        class="grid-cell"
        (click)="placeTower((i % 10) * 10, calculateY(i))"
      ></div>
    </div>
  </div>

  <!-- Game Controls -->
  <div class="controls">
    <lib-games-play-button
      [controls]="gameControls"
      (controlClicked)="handleControlClick($event)"
    ></lib-games-play-button>
  </div>

  <!-- Win/Lose Overlay -->
  <lib-win-lose-overlay
    *ngIf="gameResult"
    [status]="gameResult"
    (restart)="restartGame()"
    [message]="gameResult === 'win' ? 'You defended all waves!' : 'Your base was overrun!'"
  ></lib-win-lose-overlay>
</div>
