/* Import shared game styles */
@import '../styles/game-styles.css';

:host {
  @apply block w-full h-full;
}

.game-container {
  @apply flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800;
}

/* Game Title */
.game-title {
  @apply text-center mb-1;
}

.game-title h1 {
  @apply text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-indigo-500;
}

.game-title p {
  @apply text-xs text-slate-400 mb-1;
}

/* Tower Selection */
.tower-selection {
  @apply p-2 mb-2;
}

.tower-types {
  @apply flex justify-center gap-2 p-2 rounded-xl bg-slate-700/50 border border-slate-600 shadow-xl;
  max-width: 100%;
  overflow-x: auto;
}

.tower-type {
  @apply flex flex-col items-center p-2 rounded-lg bg-slate-800/70 border border-slate-700 cursor-pointer transition-all duration-200;
  min-width: 80px;
}

.tower-type:hover {
  @apply bg-slate-700/70 border-slate-500;
  transform: translateY(-2px);
}

.tower-type.selected {
  @apply bg-indigo-900/70 border-indigo-500;
  transform: translateY(-2px);
}

.tower-icon {
  @apply w-10 h-10 rounded-full mb-1;
  position: relative;
  overflow: hidden;
}

.tower-icon::before {
  content: '';
  @apply absolute inset-0 opacity-30;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
}

.tower-info {
  @apply text-center;
}

.tower-name {
  @apply text-sm font-bold text-white;
}

.tower-cost {
  @apply text-xs text-yellow-400;
}

/* Game Board */
.game-board {
  @apply p-4 rounded-xl bg-slate-700/50 border border-slate-600 shadow-xl relative;
  min-width: min-content;
  max-width: 100%;
  overflow: hidden;
  height: 400px;
  margin: 0 auto;
}

.game-board::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-purple-500/10;
  filter: blur(20px);
  z-index: 0;
}

/* Path */
.path {
  @apply absolute h-16 bg-slate-600/50 rounded-md;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  z-index: 1;
}

/* Placement Grid */
.placement-grid {
  @apply grid absolute inset-0 grid-cols-10 gap-1 z-10;
}

.grid-cell {
  @apply bg-transparent cursor-pointer transition-colors duration-200;
  aspect-ratio: 1;
}

.grid-cell:hover {
  @apply bg-white/10;
}

/* Towers */
.tower {
  @apply absolute w-12 h-12 rounded-full shadow-lg z-20 flex items-center justify-center;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.tower::before {
  content: '';
  @apply absolute inset-0 rounded-full opacity-30;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
}

.tower-level-1 {
  @apply bg-gradient-to-br from-blue-500 to-blue-700 border-2 border-blue-400;
}

.tower-level-2 {
  @apply bg-gradient-to-br from-purple-500 to-purple-700 border-2 border-purple-400;
}

.tower-level-3 {
  @apply bg-gradient-to-br from-red-500 to-red-700 border-2 border-red-400;
}

.tower-range {
  @apply absolute rounded-full border border-dashed border-white/30 opacity-0;
  transform: translate(-50%, -50%);
  transition: opacity 0.2s ease;
}

.tower:hover .tower-range {
  @apply opacity-100;
}

/* Enemies */
.enemy {
  @apply absolute w-8 h-8 rounded-full shadow-lg z-30 flex items-center justify-center;
  transform: translate(-50%, -50%);
}

.enemy::before {
  content: '';
  @apply absolute inset-0 rounded-full opacity-30;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
}

.enemy-normal {
  @apply bg-gradient-to-br from-orange-500 to-orange-700 border-2 border-orange-400;
}

.enemy-fast {
  @apply bg-gradient-to-br from-green-500 to-green-700 border-2 border-green-400;
}

.enemy-tank {
  @apply bg-gradient-to-br from-red-500 to-red-700 border-2 border-red-400;
}

.health-bar {
  @apply absolute -top-3 left-0 w-full h-1 bg-gray-700 rounded-full overflow-hidden;
}

.health-fill {
  @apply h-full bg-gradient-to-r from-red-500 to-green-500;
  transition: width 0.2s ease;
}

/* Controls */
.controls {
  @apply w-full max-w-md mt-4 px-4 mx-auto;
}

/* Animations */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.tower:active {
  animation: pulse 0.3s ease-in-out;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .game-board {
    height: 300px;
  }

  .tower {
    @apply w-8 h-8;
  }

  .enemy {
    @apply w-6 h-6;
  }
}