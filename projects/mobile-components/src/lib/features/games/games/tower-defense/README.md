# Tower Defense Game

A modern, visually appealing tower defense game where players strategically place towers to defend against waves of enemies.

![Tower Defense Game](assets/images/games/tower-defense.jpeg)

## Game Overview

Tower Defense is a strategy game where players must defend a path from waves of enemies by building defensive towers. The game features:

- Wave-based gameplay with increasing difficulty
- Multiple tower types with different abilities
- Various enemy types with unique characteristics
- Resource management (gold)
- Score tracking and lives system
- Win/lose conditions

## How to Play

1. **Objective**: Survive 10 waves of enemies without losing all your lives
2. **Starting Resources**: 100 gold, 20 lives
3. **Building Towers**:
   - Select a tower type from the tower selection panel
   - Click on an empty grid cell to place the tower
   - Each tower costs gold to build
4. **Tower Types**:
   - **Basic Tower** (50 gold): Balanced damage and range
   - **Sniper Tower** (100 gold): High damage, long range, slow attack speed
   - **Splash Tower** (150 gold): Area damage, short range
5. **Enemies**:
   - Enemies follow the path from left to right
   - If an enemy reaches the end of the path, you lose a life
   - Defeating enemies earns gold and score
6. **Waves**:
   - Click "Start Wave" to begin a new wave of enemies
   - Each wave contains more and stronger enemies
   - Complete all 10 waves to win the game

## Game Controls

- **Start Wave**: Begins the next wave of enemies
- **Reset Game**: Restarts the game from the beginning

## Game Mechanics

### Towers

| Tower Type | Cost | Damage | Range | Attack Speed | Special Ability |
|------------|------|--------|-------|--------------|-----------------|
| Basic      | 50   | 10     | 3     | 1000ms       | None            |
| Sniper     | 100  | 25     | 5     | 2000ms       | None            |
| Splash     | 150  | 15     | 2     | 1500ms       | None            |

### Enemies

| Enemy Type | Health                | Speed               | Reward              | Special Ability |
|------------|----------------------|---------------------|---------------------|-----------------|
| Normal     | 50 + (wave * 10)     | 0.5 + (wave * 0.1) | 10 + (wave * 2)     | None            |
| Fast       | 50 + (wave * 10)     | 0.5 + (wave * 0.1) | 10 + (wave * 2)     | Moves faster    |
| Tank       | 50 + (wave * 10)     | 0.5 + (wave * 0.1) | 10 + (wave * 2)     | More health     |

### Waves

- Each wave spawns `5 + (wave * 2)` enemies
- Enemies are spawned at 1-second intervals
- After completing a wave, you receive `50 + (wave * 10)` bonus gold
- Your score increases by `100 + (wave * 20)` for each completed wave

### Win/Lose Conditions

- **Win**: Successfully defend against all 10 waves
- **Lose**: Run out of lives (enemies reach the end of the path)

## UI Components

The Tower Defense game uses several shared components:

1. **GamesScoreComponent**: Displays score, gold, lives, and wave information
2. **GamesPlayButtonComponent**: Provides game control buttons
3. **WinLoseOverlayComponent**: Shows game over screen with win/lose message

## Technical Implementation

### Component Structure

The Tower Defense game is implemented as a standalone Angular component with the following files:

- `tower-defense.component.ts`: Game logic and state management
- `tower-defense.component.html`: Game UI template
- `tower-defense.component.css`: Game-specific styling

### Key Features

1. **Modern UI Design**:
   - Gradient backgrounds and borders
   - Rounded corners and shadows
   - Consistent color scheme with other games
   - Responsive layout for different screen sizes

2. **Game State Management**:
   - Tracks towers, enemies, gold, lives, score, and wave
   - Manages game lifecycle (start, waves, game over)
   - Handles win/lose conditions

3. **Interactive Elements**:
   - Tower selection with visual feedback
   - Tower placement on grid
   - Wave control buttons
   - Health bars for enemies
   - Tower range indicators

4. **Game Loop**:
   - Runs at 60 FPS for smooth animation
   - Handles enemy movement
   - Manages tower attacks
   - Checks game state

### Styling

The game uses a combination of:
- Shared game styles from `game-styles.css`
- Game-specific styles in `tower-defense.component.css`
- Tailwind CSS utility classes

## Integration

The Tower Defense game is registered in the game system with the code `tower-defense` and can be accessed through:

1. The All Games View
2. Direct URL: `/games/single?game=tower-defense`

## Configuration

The game configuration is defined in `game.service.ts`:

```typescript
'tower-defense': {
  name: 'Tower Defense',
  backgroundImage: 'assets/images/games/tower-defense.jpeg',
  config: {
    initialGold: 100,
    initialLives: 20,
    mapSize: { width: 10, height: 10 },
    waves: 10,
    towerTypes: [
      { name: 'Basic', cost: 50, damage: 10, range: 3 },
      { name: 'Sniper', cost: 100, damage: 25, range: 5 },
      { name: 'Splash', cost: 150, damage: 15, range: 2 }
    ]
  },
},
```

## Future Enhancements

Potential improvements for future versions:

1. Add more tower types with unique abilities
2. Implement tower upgrades
3. Create more diverse enemy types
4. Add sound effects and additional visual feedback
5. Implement difficulty levels
6. Add path customization
7. Create a level editor

## Troubleshooting

If the game doesn't load properly, check:

1. The URL parameter format (should be `game=tower-defense`)
2. Console for any JavaScript errors
3. Network requests for missing assets
4. Game configuration in `game.service.ts`
