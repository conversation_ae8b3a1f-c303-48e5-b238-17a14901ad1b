import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule, NgF<PERSON>, Ng<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { WinLoseOverlayComponent } from '../components/win-lose-overlay/win-lose-overlay.component';
import { GameControl, GamesPlayButtonComponent } from '../components/games-play-button/games-play-button.component';
import { Subject } from 'rxjs';


interface Tower {
  x: number;
  y: number;
  damage: number;
  range: number;
  level: number;
  lastAttack: number;
}

interface Enemy {
  x: number;
  y: number;
  health: number;
  maxHealth: number;
  speed: number;
  reward: number;
  type: string;
  id: number;
}

interface TowerType {
  name: string;
  cost: number;
  damage: number;
  range: number;
  attackSpeed: number;
  color: string;
}

@Component({
  selector: 'lib-tower-defense',
  templateUrl: './tower-defense.component.html',
  styleUrls: ['./tower-defense.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    NgFor,
    NgIf,
    NgClass,
    NgStyle,
    GamesScoreComponent,
    WinLoseOverlayComponent,
    GamesPlayButtonComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class TowerDefenseComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private gameLoopInterval: any;
  private nextEnemyId = 1;

  // Game state
  towers: Tower[] = [];
  enemies: Enemy[] = [];
  gold = 100;
  lives = 20;
  score = 0;
  wave = 1;
  gameStarted = false;
  gameOver = false;
  gameWon = false;
  selectedTowerType: string = 'Basic';
  gameResult: 'win' | 'lose' | null = null;

  // Tower types
  towerTypes: TowerType[] = [
    { name: 'Basic', cost: 50, damage: 10, range: 3, attackSpeed: 1000, color: 'blue' },
    { name: 'Sniper', cost: 100, damage: 25, range: 5, attackSpeed: 2000, color: 'purple' },
    { name: 'Splash', cost: 150, damage: 15, range: 2, attackSpeed: 1500, color: 'red' }
  ];

  // Game controls
  gameControls: GameControl[] = [
    { name: 'start', label: 'Start Wave', icon: 'play-outline' },
    { name: 'reset', label: 'Reset Game', icon: 'refresh-outline' }
  ];

  ngOnInit() {
    this.initGame();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    this.stopGameLoop();
  }

  initGame() {
    this.towers = [];
    this.enemies = [];
    this.gold = 100;
    this.lives = 20;
    this.score = 0;
    this.wave = 1;
    this.gameStarted = false;
    this.gameOver = false;
    this.gameWon = false;
    this.gameResult = null;
  }

  startGame() {
    if (this.gameStarted) return;

    this.gameStarted = true;
    this.startWave();
    this.startGameLoop();
  }

  startWave() {
    const enemyCount = 5 + (this.wave * 2);

    // Schedule enemy spawns
    for (let i = 0; i < enemyCount; i++) {
      setTimeout(() => {
        if (!this.gameOver) this.spawnEnemy();
      }, i * 1000);
    }
  }

  startGameLoop() {
    this.stopGameLoop();
    this.gameLoopInterval = setInterval(() => this.gameLoop(), 1000 / 60); // 60 FPS
  }

  stopGameLoop() {
    if (this.gameLoopInterval) {
      clearInterval(this.gameLoopInterval);
      this.gameLoopInterval = null;
    }
  }

  gameLoop() {
    this.moveEnemies();
    this.towerAttack();
    this.checkGameState();
  }

  spawnEnemy() {
    const health = 50 + (this.wave * 10);
    const enemy: Enemy = {
      id: this.nextEnemyId++,
      x: 0,
      y: 50,
      health: health,
      maxHealth: health,
      speed: 0.5 + (this.wave * 0.1),
      reward: 10 + (this.wave * 2),
      type: this.getRandomEnemyType()
    };
    this.enemies.push(enemy);
  }

  getRandomEnemyType(): string {
    const types = ['normal', 'fast', 'tank'];
    return types[Math.floor(Math.random() * types.length)];
  }

  moveEnemies() {
    this.enemies.forEach((enemy) => {
      enemy.x += enemy.speed;

      // Check if enemy reached the end
      if (enemy.x >= 100) {
        this.lives--;
        this.enemies = this.enemies.filter((e) => e !== enemy);
      }
    });
  }

  towerAttack() {
    const now = Date.now();

    this.towers.forEach((tower) => {
      const towerType = this.towerTypes.find(t => t.name === tower.level.toString());
      if (!towerType) return;

      // Check if tower can attack
      if (now - tower.lastAttack < towerType.attackSpeed) return;

      // Find target
      const target = this.enemies.find(
        (enemy) =>
          Math.sqrt(
            Math.pow(enemy.x - tower.x, 2) + Math.pow(enemy.y - tower.y, 2)
          ) <= tower.range
      );

      if (target) {
        // Attack enemy
        target.health -= tower.damage;
        tower.lastAttack = now;

        // Check if enemy is defeated
        if (target.health <= 0) {
          this.gold += target.reward;
          this.score += target.reward;
          this.enemies = this.enemies.filter((e) => e !== target);
        }
      }
    });
  }

  checkGameState() {
    // Check if player lost
    if (this.lives <= 0) {
      this.endGame('lose');
    }

    // Check if wave is complete
    if (this.enemies.length === 0 && this.gameStarted) {
      this.completeWave();
    }
  }

  completeWave() {
    this.wave++;
    this.gold += 50 + (this.wave * 10);
    this.score += 100 + (this.wave * 20);

    // Check if player won (reached wave 10)
    if (this.wave > 10) {
      this.endGame('win');
    } else {
      this.gameStarted = false;
    }
  }

  endGame(result: 'win' | 'lose') {
    this.gameOver = true;
    this.gameResult = result;
    this.stopGameLoop();
  }

  placeTower(x: number, y: number) {
    // Check if there's already a tower at this position
    const existingTower = this.towers.find(t => t.x === x && t.y === y);
    if (existingTower) return;

    // Find selected tower type
    const towerType = this.towerTypes.find(t => t.name === this.selectedTowerType);
    if (!towerType) return;

    // Check if player has enough gold
    if (this.gold >= towerType.cost) {
      this.towers.push({
        x,
        y,
        damage: towerType.damage,
        range: towerType.range,
        level: 1,
        lastAttack: 0
      });
      this.gold -= towerType.cost;
    }
  }

  selectTowerType(type: string) {
    this.selectedTowerType = type;
  }

  handleControlClick(controlName: string) {
    switch (controlName) {
      case 'start':
        if (!this.gameStarted) {
          this.startGame();
        }
        break;
      case 'reset':
        this.initGame();
        break;
    }
  }

  restartGame() {
    this.initGame();
  }

  calculateY(index: number): number {
    return Math.floor(index / 10) * 10;
  }

  getHealthPercentage(enemy: Enemy): number {
    return (enemy.health / enemy.maxHealth) * 100;
  }

  getTowerColor(tower: Tower): string {
    const towerType = this.towerTypes.find(t => t.name === tower.level.toString());
    return towerType ? towerType.color : 'blue';
  }

  getEnemyColor(enemy: Enemy): string {
    switch (enemy.type) {
      case 'fast': return 'green';
      case 'tank': return 'red';
      default: return 'orange';
    }
  }
}
