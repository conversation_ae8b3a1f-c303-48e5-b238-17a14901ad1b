/* Import shared game styles */
@import '../styles/game-styles.css';

.letter-tile {
  perspective: 1000px;
  transform-style: preserve-3d;
  position: relative;
  cursor: pointer;
  user-select: none;
}

.letter-tile.flip {
  animation: flip 0.6s ease-in-out;
}

@keyframes flip {
  0% {
    transform: rotateX(0deg);
  }
  50% {
    transform: rotateX(90deg);
  }
  100% {
    transform: rotateX(0deg);
  }
}

.letter-tile.shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  20% { transform: translateX(-5px); }
  40% { transform: translateX(5px); }
  60% { transform: translateX(-5px); }
  80% { transform: translateX(5px); }
}

.letter-tile span,
.letter-tile img {
  transition: opacity 0.3s ease-in-out;
  pointer-events: none;
}

.letter-tile img.face-image {
  z-index: 1;
}

.letter-tile span {
  z-index: 2;
}

.keyboard-button {
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease-in-out;
}

.keyboard-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgb(253, 224, 71); /* Yellow-300 */
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.keyboard-button.spark::after {
  animation: sparkAnimation 0.5s ease-out;
}

@keyframes sparkAnimation {
  0% {
    opacity: 1;
    transform: scale(0, 0) translate(-50%, -50%);
    box-shadow: 0 0 0 0 rgba(253, 224, 71, 0.8);
  }
  50% {
    opacity: 0.9;
  }
  100% {
    opacity: 0;
    transform: scale(10, 10) translate(-50%, -50%);
    box-shadow: 0 0 40px 25px rgba(253, 224, 71, 0);
  }
}

.keyboard-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(253, 224, 71, 0.6) 0%, rgba(253, 224, 71, 0.2) 100%);
  border-radius: 50%;
  transform: scale(0) translate(-50%, -50%);
  transform-origin: 0% 0%;
}

.keyboard-button.ripple::before {
  animation: rippleAnimation 0.6s ease-out;
}

@keyframes rippleAnimation {
  0% {
    transform: scale(0) translate(-50%, -50%);
    opacity: 0.8;
  }
  60% {
    transform: scale(1.8) translate(-50%, -50%);
    opacity: 0.6;
  }
  100% {
    opacity: 0;
    transform: scale(2) translate(-50%, -50%);
  }
}

.keyboard-button:active {
  transform: scale(0.95);
}

.win-animation {
  animation: bounce 1s ease-in-out;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-20px); }
  60% { transform: translateY(-10px); }
}

/* Victory Confetti */
.confetti-container {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 100;
}

.confetti {
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: #ff6b6b;
  animation: confettiFall 3s linear infinite;
}

/* Distribute confetti across the screen with different colors and delays */
.confetti:nth-child(1) { left: 10%; animation-delay: 0s; background-color: #ff6b6b; }
.confetti:nth-child(2) { left: 20%; animation-delay: -0.5s; background-color: #4ecdc4; }
.confetti:nth-child(3) { left: 30%; animation-delay: -1s; background-color: #45b7d1; }
.confetti:nth-child(4) { left: 40%; animation-delay: -1.5s; background-color: #96ceb4; }
.confetti:nth-child(5) { left: 50%; animation-delay: -2s; background-color: #ffeead; }
.confetti:nth-child(6) { left: 60%; animation-delay: -2.5s; background-color: #ff9999; }
.confetti:nth-child(7) { left: 70%; animation-delay: -1.75s; background-color: #ffd93d; }
.confetti:nth-child(8) { left: 80%; animation-delay: -2.25s; background-color: #ff6b6b; }
.confetti:nth-child(9) { left: 90%; animation-delay: -3s; background-color: #4ecdc4; }
.confetti:nth-child(10) { left: 15%; animation-delay: -1.25s; background-color: #45b7d1; }
.confetti:nth-child(11) { left: 25%; animation-delay: -2.75s; background-color: #96ceb4; }
.confetti:nth-child(12) { left: 35%; animation-delay: -0.25s; background-color: #ffeead; }
.confetti:nth-child(13) { left: 45%; animation-delay: -1.25s; background-color: #ff9999; }
.confetti:nth-child(14) { left: 55%; animation-delay: -2.25s; background-color: #ffd93d; }
.confetti:nth-child(15) { left: 65%; animation-delay: -0.75s; background-color: #ff6b6b; }
.confetti:nth-child(16) { left: 75%; animation-delay: -1.75s; background-color: #4ecdc4; }
.confetti:nth-child(17) { left: 85%; animation-delay: -2.75s; background-color: #45b7d1; }
.confetti:nth-child(18) { left: 95%; animation-delay: -0.5s; background-color: #96ceb4; }
.confetti:nth-child(19) { left: 5%; animation-delay: -1.5s; background-color: #ffeead; }
.confetti:nth-child(20) { left: 15%; animation-delay: -2.5s; background-color: #ff9999; }

@keyframes confettiFall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(360deg);
    opacity: 0;
  }
}

/* Enhanced Fireworks */
.fireworks {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 99;
  opacity: 1;
}

.fireworks .before,
.fireworks .after {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  box-shadow:
    -120px -218.66667px #ff3d3d,
    248px -16.66667px #00ff00,
    190px 16.33333px #00ffff,
    -113px -308.66667px #ff3d3d,
    -109px -287.66667px #ffff00,
    -50px -313.66667px #00ff00,
    226px -31.66667px #ff3d3d,
    180px -351.66667px #ffff00,
    -12px -338.66667px #00ffff,
    220px -388.66667px #ff3d3d,
    -69px -27.66667px #00ff00,
    -111px -339.66667px #00ffff,
    155px -237.66667px #ffff00,
    -200px -200px #ff3d3d,
    150px -150px #00ffff,
    -150px -150px #ffff00,
    100px -100px #ff3d3d,
    -100px -100px #00ff00,
    50px -150px #00ffff,
    -50px -150px #ffff00;
  animation: fireworksRain 2s linear infinite;
  transform-origin: center center;
}

.fireworks .after {
  animation-delay: 0.5s;
}

@keyframes fireworksRain {
  from {
    opacity: 0;
    transform: translateY(0) scale(0) rotate(0deg);
  }
  10% {
    opacity: 1;
  }
  50% {
    opacity: 1;
    transform: translateY(50vh) scale(1) rotate(180deg);
  }
  90% {
    opacity: 1;
  }
  to {
    opacity: 0;
    transform: translateY(100vh) scale(0.5) rotate(360deg);
  }
}

.fireworks .before::after,
.fireworks .after::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  filter: blur(4px);
  animation: glow 1s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 0 10px #fff,
                0 0 20px #fff,
                0 0 30px #ff3d3d,
                0 0 40px #ff3d3d;
  }
  to {
    box-shadow: 0 0 20px #fff,
                0 0 30px #00ff00,
                0 0 40px #00ff00,
                0 0 50px #00ff00;
  }
}

/* Game Over Dialog Animations */
.game-over-dialog {
  animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Mascot Animation */
.mascot-bounce {
  animation: mascotBounce 1s ease-in-out infinite;
}

@keyframes mascotBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Result Face Animation */
.result-face {
  animation: popIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) 0.2s both;
}

@keyframes popIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  70% {
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Stats Container */
.stats-container {
  background: linear-gradient(145deg, #f7f7f7, #ffffff);
  box-shadow:
    inset 2px 2px 5px rgba(255, 255, 255, 0.5),
    inset -2px -2px 5px rgba(0, 0, 0, 0.05);
}

.stat-item {
  animation: slideIn 0.5s ease-out both;
}

.stat-item:nth-child(2) {
  animation-delay: 0.1s;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Game Over Message */
.game-over-message h2 {
  animation: glowText 2s ease-in-out infinite;
  background: linear-gradient(90deg, #ff6b6b, #ffd93d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@keyframes glowText {
  0%, 100% {
    filter: drop-shadow(0 0 2px rgba(255, 107, 107, 0.7));
  }
  50% {
    filter: drop-shadow(0 0 5px rgba(255, 217, 61, 0.9));
  }
}