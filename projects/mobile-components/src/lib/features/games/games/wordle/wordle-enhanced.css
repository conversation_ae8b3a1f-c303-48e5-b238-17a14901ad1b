/* Wordle Enhanced Styles
 * These styles enhance the visual appeal of the Wordle game
 * without changing its core functionality.
 */

/* ===== CSS Variables for Theming ===== */
:root {
  --primary-color: #ff6b6b;
  --secondary-color: #ffd93d;
  --correct-color: #4ade80;
  --present-color: #facc15;
  --absent-color: #6b7280;
  --background-color: #ffffff;
  --text-color: #1f2937;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --highlight-color: rgba(255, 255, 255, 0.8);
  --tile-size: 14;
}

/* ===== Enhanced Background ===== */
.flex.overflow-hidden.flex-col.h-screen {
  position: relative;
  background: transparent;
  overflow: hidden;
}

/* Container for game content */
.flex.overflow-hidden.flex-col.flex-grow {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

/* Add backdrop blur to the game over overlay */
.flex.fixed.inset-0.z-50.justify-center.items-center.bg-black.bg-opacity-50 {
  background-color: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(8px) !important;
}

/* Game container */
.game-container {
  display: flex;
  flex-direction: column;
  max-height: 100vh;
  overflow: hidden;
  background-color: #f8f9fa;
}

.flex.overflow-hidden.flex-col.flex-grow {
  max-height: calc(100vh - 60px);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Word Grid Card */
.mb-4.w-full.max-w-md {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
  padding: 1rem;
  margin-bottom: 1rem;
  max-width: 350px;
}

/* Keyboard Card */
.mt-2.w-full.max-w-md {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
  padding: 1rem;
  max-width: 350px;
  margin-bottom: 1rem;
}

/* Make letter tiles smaller */
.letter-tile {
  width: 2.75rem !important;
  height: 2.75rem !important;
  font-size: 1rem !important;
}

/* Make keyboard buttons smaller and more compact */
.keyboard-button {
  height: 2.5rem !important;
  font-size: 0.85rem !important;
  padding: 0 !important;
}

/* Keyboard layout styling */
.grid.grid-cols-10, .grid.grid-cols-9 {
  margin-bottom: 0.25rem;
}

/* Adjust grid gap */
.grid.grid-cols-5.gap-2 {
  gap: 0.375rem !important;
}

.grid.grid-cols-9.gap-1 {
  gap: 0.25rem !important;
}

/* ===== Enhanced 3D Letter Tiles ===== */
.letter-tile {
  perspective: 1200px;
  transform-style: preserve-3d;
  position: relative;
  cursor: pointer;
  user-select: none;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow:
    0 4px 6px var(--shadow-color),
    inset 0 1px 0 var(--highlight-color);
  border-radius: 12px !important;
  overflow: hidden;
}

/* Improved hover effect */
.letter-tile:hover {
  transform: translateY(-2px);
  box-shadow:
    0 6px 10px var(--shadow-color),
    inset 0 1px 0 var(--highlight-color);
}

/* Enhanced flip animation */
.letter-tile.flip {
  animation: enhanced-flip 0.8s cubic-bezier(0.455, 0.03, 0.515, 0.955);
}

@keyframes enhanced-flip {
  0% {
    transform: rotateX(0deg);
    box-shadow: 0 4px 6px var(--shadow-color);
  }
  50% {
    transform: rotateX(90deg);
    box-shadow: 0 15px 15px var(--shadow-color);
  }
  100% {
    transform: rotateX(0deg);
    box-shadow: 0 4px 6px var(--shadow-color);
  }
}

/* Enhanced shake animation */
.letter-tile.shake {
  animation: enhanced-shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97);
}

@keyframes enhanced-shake {
  0%, 100% { transform: translateX(0) rotate(0deg); }
  20% { transform: translateX(-5px) rotate(-1deg); }
  40% { transform: translateX(5px) rotate(1deg); }
  60% { transform: translateX(-5px) rotate(-1deg); }
  80% { transform: translateX(5px) rotate(1deg); }
}

/* Enhanced tile colors with gradients */
.letter-tile.bg-green-500 {
  background: linear-gradient(135deg, #4ade80, #22c55e);
  border: none;
}

.letter-tile.bg-yellow-500 {
  background: linear-gradient(135deg, #facc15, #eab308);
  border: none;
}

.letter-tile.bg-gray-500 {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  border: none;
}

/* Current row highlight */
.current-row .letter-tile {
  animation: subtle-pulse 2s ease-in-out infinite;
}

@keyframes subtle-pulse {
  0%, 100% {
    box-shadow:
      0 4px 6px var(--shadow-color),
      inset 0 1px 0 var(--highlight-color),
      0 0 0 rgba(255, 107, 107, 0);
  }
  50% {
    box-shadow:
      0 4px 6px var(--shadow-color),
      inset 0 1px 0 var(--highlight-color),
      0 0 10px rgba(255, 107, 107, 0.3);
  }
}

/* ===== Enhanced Keyboard ===== */
.keyboard-button {
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease-in-out;
  border-radius: 8px;
  box-shadow:
    0 4px 6px var(--shadow-color),
    inset 0 1px 0 var(--highlight-color);
}

/* Improved hover state */
.keyboard-button:hover {
  transform: translateY(-2px);
  box-shadow:
    0 6px 10px var(--shadow-color),
    inset 0 1px 0 var(--highlight-color);
}

/* Enhanced active state */
.keyboard-button:active {
  transform: translateY(1px) scale(0.95);
  box-shadow:
    0 2px 3px var(--shadow-color),
    inset 0 1px 0 var(--highlight-color);
}

/* Enhanced spark effect */
.keyboard-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: var(--secondary-color);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.keyboard-button.spark::after {
  animation: enhanced-spark 0.6s ease-out;
}

@keyframes enhanced-spark {
  0% {
    opacity: 1;
    transform: scale(0, 0) translate(-50%, -50%);
    box-shadow: 0 0 0 0 rgba(253, 224, 71, 0.8);
  }
  50% {
    opacity: 0.9;
  }
  100% {
    opacity: 0;
    transform: scale(15, 15) translate(-50%, -50%);
    box-shadow: 0 0 50px 30px rgba(253, 224, 71, 0);
  }
}

/* Enhanced ripple effect */
.keyboard-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(253, 224, 71, 0.7) 0%, rgba(253, 224, 71, 0.3) 100%);
  border-radius: 50%;
  transform: scale(0) translate(-50%, -50%);
  transform-origin: 0% 0%;
}

.keyboard-button.ripple::before {
  animation: enhanced-ripple 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes enhanced-ripple {
  0% {
    transform: scale(0) translate(-50%, -50%);
    opacity: 0.8;
  }
  60% {
    transform: scale(2) translate(-50%, -50%);
    opacity: 0.6;
  }
  100% {
    opacity: 0;
    transform: scale(2.5) translate(-50%, -50%);
  }
}

/* ===== Enhanced Victory Animations ===== */
/* Improved win animation */
.win-animation {
  animation: enhanced-bounce 1.2s cubic-bezier(0.28, 0.84, 0.42, 1);
}

@keyframes enhanced-bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-25px); }
  60% { transform: translateY(-15px); }
}

/* Enhanced confetti */
.confetti {
  position: absolute;
  width: 12px;
  height: 12px;
  background-color: var(--primary-color);
  animation: enhanced-confetti-fall 4s cubic-bezier(0.445, 0.05, 0.55, 0.95) infinite;
  opacity: 0.9;
  border-radius: 2px;
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
}

/* Different confetti shapes */
.confetti:nth-child(3n) {
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%); /* Triangle */
}

.confetti:nth-child(3n+1) {
  clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%); /* Hexagon */
}

@keyframes enhanced-confetti-fall {
  0% {
    transform: translateY(-100vh) rotate(0deg) scale(1);
    opacity: 1;
  }
  25% {
    transform: translateY(-50vh) rotate(90deg) scale(1.2);
  }
  50% {
    transform: translateY(0vh) rotate(180deg) scale(1);
  }
  75% {
    transform: translateY(50vh) rotate(270deg) scale(0.8);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(360deg) scale(0.5);
    opacity: 0;
  }
}

/* Enhanced fireworks */
.fireworks {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 99;
  opacity: 1;
}

.fireworks .before,
.fireworks .after {
  position: absolute;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  box-shadow: 0 0 20px 5px var(--primary-color);
  animation: enhanced-fireworks 2s ease-in-out infinite;
}

.fireworks .before {
  top: 30%;
  left: 30%;
  animation-delay: 0s;
}

.fireworks .after {
  top: 70%;
  left: 70%;
  animation-delay: 0.5s;
}

@keyframes enhanced-fireworks {
  0% {
    opacity: 1;
    transform: scale(0);
    box-shadow: 0 0 20px 5px var(--primary-color);
  }
  50% {
    opacity: 1;
    transform: scale(1);
    box-shadow: 0 0 40px 20px var(--primary-color);
  }
  100% {
    opacity: 0;
    transform: scale(1.5);
    box-shadow: 0 0 60px 30px var(--primary-color);
  }
}

/* ===== Enhanced Game Over Dialog ===== */
.game-over-dialog {
  animation: enhanced-scale-in 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  background: linear-gradient(to bottom, #1e293b, #0f172a) !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3) !important;
  border: 1px solid #334155 !important;
  border-radius: 16px !important;
  overflow: hidden;
}

@keyframes enhanced-scale-in {
  from {
    opacity: 0;
    transform: scale(0.7);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Enhanced mascot animation */
.mascot-container {
  position: relative;
}

.mascot-container::before {
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(circle, rgba(34, 197, 94, 0.2), transparent);
  border-radius: 50%;
  filter: blur(10px);
  z-index: -1;
}

.mascot-bounce {
  animation: bounce 2s ease-in-out infinite;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.2));
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Enhanced result face */
.result-face {
  animation: enhanced-pop-in 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275) 0.3s both;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
}

@keyframes enhanced-pop-in {
  0% {
    opacity: 0;
    transform: scale(0.3) rotate(-10deg);
  }
  70% {
    transform: scale(1.2) rotate(5deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* Enhanced stats container */
.stats-container {
  background: rgba(51, 65, 85, 0.5) !important;
  border: 1px solid rgba(71, 85, 105, 0.5) !important;
  border-radius: 12px !important;
  box-shadow: none !important;
}

/* Enhanced game over message */
.game-over-message h2 {
  animation: textShine 3s linear infinite;
  background: linear-gradient(to right, #ef4444, #f97316, #ef4444);
  background-size: 200% auto;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 2.5rem !important;
  letter-spacing: 1px;
  text-shadow: 0 0 20px rgba(239, 68, 68, 0.7);
}

@keyframes textShine {
  0% {
    background-position: 0% center;
  }
  100% {
    background-position: 200% center;
  }
}

/* Add styling for game over dialog content */
.game-over-dialog p {
  color: #e2e8f0 !important;
}

.game-over-dialog .text-orange-500 {
  color: #f97316 !important;
}

/* Style the Play Again button */
.game-over-dialog button {
  background: linear-gradient(to right, #22c55e, #16a34a) !important;
  border-radius: 12px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.game-over-dialog button:hover {
  background: linear-gradient(to right, #16a34a, #15803d) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}
