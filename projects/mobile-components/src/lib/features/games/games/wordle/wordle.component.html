<div class="flex overflow-hidden flex-col h-screen rounded-lg game-container bg-gradient-to-b from-slate-950 to-slate-900" [class.win-animation]="showAnimation">
  <!-- Header - Removed score display as requested -->

  <!-- Game Title -->
  <div class="text-center mb-1">
    <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-amber-400 to-orange-500">Wordle</h1>
    <p class="text-xs text-slate-400 mb-1">Guess the 5-letter word!</p>
  </div>

  <!-- Game Board -->
  <div class="flex overflow-hidden flex-col flex-grow items-center p-2">
    <!-- Word Grid -->
    <div class="mb-4 w-full max-w-md overflow-auto" style="max-height: 50vh;">
      <div class="grid grid-rows-6 gap-2 bg-gradient-to-br from-slate-800 to-slate-900 p-4 rounded-xl shadow-xl border-2 border-slate-700">
        <!-- Evaluated Guesses -->
        <ng-container *ngFor="let evaluated of evaluatedGuesses; let guessIndex = index">
          <div [class]="'grid grid-cols-5 gap-2 row-' + guessIndex">
            <ng-container *ngFor="let letterEval of evaluated.evaluation; let letterIndex = index">
              <div class="flex overflow-hidden relative justify-center items-center rounded-lg shadow-md cursor-pointer letter-tile"
                   [ngClass]="letterEval.status === 'correct' ? 'bg-gradient-to-r from-green-500 to-green-600' : letterEval.status === 'present' ? 'bg-gradient-to-r from-yellow-500 to-yellow-600' : 'bg-gradient-to-r from-slate-600 to-slate-700'"
                   (click)="toggleLettersForRow(guessIndex)">
                <span class="absolute text-xl font-bold text-white"
                      [class.opacity-100]="showingLettersForRow === guessIndex"
                      [class.opacity-0]="showingLettersForRow !== guessIndex">
                  {{ letterEval.letter }}
                </span>
                <img [src]="getFaceImage(letterEval.status)"
                     [alt]="letterEval.status"
                     class="object-contain absolute top-0 left-0 w-full h-full transition-opacity duration-300 pointer-events-none face-image"
                     [class.opacity-0]="showingLettersForRow === guessIndex"
                     [class.opacity-100]="showingLettersForRow !== guessIndex">
              </div>
            </ng-container>
          </div>
        </ng-container>

        <!-- Current Guess Row -->
        <div class="grid grid-cols-5 gap-2 current-row" *ngIf="!gameOver">
          <ng-container *ngFor="let i of [0,1,2,3,4]">
            <div class="flex justify-center items-center bg-slate-800 rounded-lg border-2 border-slate-600 letter-tile">
              <span class="text-xl font-bold text-white">{{ i < currentGuess.length ? currentGuess[i] : '' }}</span>
            </div>
          </ng-container>
        </div>

        <!-- Empty Rows -->
        <ng-container *ngFor="let empty of getRemainingRows(); let i = index">
          <div class="grid grid-cols-5 gap-2">
            <ng-container *ngFor="let col of [0,1,2,3,4]">
              <div class="flex justify-center items-center bg-slate-800/50 rounded-lg border-2 border-slate-700/50 letter-tile"></div>
            </ng-container>
          </div>
        </ng-container>
      </div>
    </div>

    <!-- Keyboard -->
    <div class="mt-2 w-full max-w-md">
      <!-- First row: QWERTYUIOP -->
      <div class="grid grid-cols-10 gap-1 mb-1">
        <ng-container *ngFor="let letter of 'QWERTYUIOP'.split('')">
          <button
            #keyButton
            (click)="triggerButtonAnimation(keyButton, letter); onKeyPress(letter)"
            class="w-8 h-10 font-bold rounded-md shadow-md transition-all duration-200 keyboard-button hover:shadow-lg"
            [ngClass]="getKeyboardLetterClass(letter)"
          >
            {{ letter }}
          </button>
        </ng-container>
      </div>

      <!-- Second row: ASDFGHJKL -->
      <div class="grid grid-cols-9 gap-1 mb-1 px-2">
        <ng-container *ngFor="let letter of 'ASDFGHJKL'.split('')">
          <button
            #keyButton
            (click)="triggerButtonAnimation(keyButton, letter); onKeyPress(letter)"
            class="w-8 h-10 font-bold rounded-md shadow-md transition-all duration-200 keyboard-button hover:shadow-lg"
            [ngClass]="getKeyboardLetterClass(letter)"
          >
            {{ letter }}
          </button>
        </ng-container>
      </div>

      <!-- Third row: ZXCVBNM + Enter + Backspace -->
      <div class="grid grid-cols-9 gap-1">
        <button
          #enterButton
          (click)="triggerButtonAnimation(enterButton, 'Enter'); onKeyPress('Enter')"
          class="col-span-1 w-full h-10 font-bold text-white bg-gradient-to-r from-green-500 to-emerald-600 rounded-md shadow-md transition-all duration-200 keyboard-button hover:from-green-600 hover:to-emerald-700"
        >
          ↵
        </button>

        <ng-container *ngFor="let letter of 'ZXCVBNM'.split('')">
          <button
            #keyButton
            (click)="triggerButtonAnimation(keyButton, letter); onKeyPress(letter)"
            class="w-8 h-10 font-bold rounded-md shadow-md transition-all duration-200 keyboard-button hover:shadow-lg"
            [ngClass]="getKeyboardLetterClass(letter)"
          >
            {{ letter }}
          </button>
        </ng-container>

        <button
          #backspaceButton
          (click)="triggerButtonAnimation(backspaceButton, 'Backspace'); onKeyPress('Backspace')"
          class="col-span-1 w-full h-10 font-bold text-white bg-gradient-to-r from-slate-600 to-slate-700 rounded-md shadow-md transition-all duration-200 keyboard-button hover:from-slate-700 hover:to-slate-800"
        >
          ←
        </button>
      </div>
    </div>

    <!-- Game Instructions -->
    <div class="mt-4 p-3 bg-slate-800/50 rounded-lg border border-slate-700/50 text-center max-w-md w-full">
      <p class="text-xs text-slate-400">
        <span class="text-amber-400">Guess</span> <span class="text-slate-300">the 5-letter word in 6 tries</span> |
        <span class="text-green-400">Green</span> <span class="text-slate-300">= correct spot</span> |
        <span class="text-yellow-400">Yellow</span> <span class="text-slate-300">= wrong spot</span>
      </p>
    </div>
  </div>

  <!-- Game Over Dialog -->
  <div *ngIf="gameOver" class="game-popup-overlay">
    <div class="game-popup-dialog">
      <!-- Mascot -->
      <div class="mascot-container">
        <div class="absolute inset-0 bg-gradient-to-b from-amber-500/20 to-transparent rounded-full blur-xl"></div>
        <img src="assets/images/games/mascot.png" alt="Game Mascot" class="mascot-bounce">
      </div>

      <!-- Result Face -->
      <div class="mb-4 result-face">
        <img [src]="getGameOverMessage().face" alt="Result Face" class="mx-auto w-24 h-24">
      </div>

      <!-- Message -->
      <h2 class="game-over-title" [ngStyle]="{'background': gameResult === 'win' ? 'linear-gradient(to right, #10b981, #34d399, #10b981)' : 'linear-gradient(to right, #ef4444, #f97316, #ef4444)'}">
        {{ gameResult === 'win' ? 'VICTORY!' : 'GAME OVER' }}
      </h2>
      <p class="mb-6 text-lg text-slate-300">{{ getGameOverMessage().message }}</p>

      <!-- Stats -->
      <div class="stats-container">
        <div class="grid grid-cols-2 gap-4">
          <div class="stat-item">
            <span class="stat-label">Score</span>
            <span class="stat-value stat-value-score">{{ score }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Attempts</span>
            <span class="stat-value stat-value-attempts">{{ evaluatedGuesses.length }}/{{ maxTurns }}</span>
          </div>
        </div>
      </div>

      <!-- Play Again Button -->
      <button
        (click)="newGame()"
        class="game-action-button game-action-button-primary"
      >
        Play Again
      </button>
    </div>

    <!-- Fireworks for win -->
    <div *ngIf="gameResult === 'win'" class="fireworks">
      <div class="before"></div>
      <div class="after"></div>
    </div>
    <div *ngIf="gameResult === 'win'" class="confetti-container">
      <div class="confetti" *ngFor="let i of [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]"></div>
    </div>
  </div>

  <!-- Win/Lose Overlay -->
  <!-- <lib-win-lose-overlay
    [status]="gameResult"
    (restart)="newGame()"
    [message]="'The word was: ' + word"
  ></lib-win-lose-overlay> -->
</div>
