import { Component, OnInit, Input, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Game } from 'lp-client-api';

@Component({
  selector: 'games-wordle',
  templateUrl: './wordle.component.html',
  styleUrls: ['./wordle.component.css', './wordle-enhanced.css'],
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class WordleComponent implements OnInit {
  @Input() gameId: string = '';
  @Input() config: any = {};
  @Input() gameInstance: any = null;
  @Input() game?: Game;

  word: string = '';
  evaluatedGuesses: Array<{
    guess: string,
    evaluation: Array<{
      letter: string,
      status: 'correct' | 'present' | 'absent'
    }>
  }> = [];
  currentGuess: string = '';
  maxTurns: number = 6;
  gameOver: boolean = false;
  score: number = 0;
  streak: number = 0;
  isWinner: boolean = false;
  gameResult: 'win' | 'lose' | null = null;
  showAnimation: boolean = false;

  // Track which row is showing letters
  showingLettersForRow: number | null = null;

  // Timer reference to clear timeout if needed
  private letterShowTimeout: any;

  // Track button animations
  private animatingButtons: Set<string> = new Set();

  // Default words list in case config doesn't provide one
  private words = [
    'APPLE', 'BEACH', 'CHAIR', 'DANCE', 'EARTH',
    'FLAME', 'GRAPE', 'HOUSE', 'IGLOO', 'JUICE',
    'KNIFE', 'LEMON', 'MOUSE', 'NIGHT', 'OCEAN',
    'PIANO', 'QUEEN', 'RIVER', 'SNAKE', 'TABLE',
    'UNCLE', 'VOICE', 'WATER', 'XENON', 'YACHT',
    'ZEBRA', 'BREAD', 'CLOUD', 'DREAM', 'EAGLE'
  ];

  ngOnInit() {
    console.log('WORDLE CONFIG', this.config);
    console.log('WORDLE GAME INSTANCE', this.gameInstance);

    // Try to get words from config, but use default if not available
    if (this.config?.gameStyles?.words && Array.isArray(this.config.gameStyles.words) && this.config.gameStyles.words.length > 0) {
      this.words = this.config.gameStyles.words;
    } else {
      console.log('Using default words list for Wordle');
    }

    this.newGame();
  }

  newGame() {
    this.word = this.getRandomWord();
    this.evaluatedGuesses = [];
    this.currentGuess = '';
    this.gameOver = false;
    this.isWinner = false;
    this.gameResult = null;
    this.showAnimation = false;
  }

  onKeyPress(key: string): void {
    if (this.gameOver) return;

    if (key === 'Enter') {
      if (this.currentGuess.length === 5) {
        this.submitGuess();
      } else {
        this.shakeCurrentRow();
      }
    } else if (key === 'Backspace') {
      this.currentGuess = this.currentGuess.slice(0, -1);
    } else if (/^[A-Z]$/.test(key) && this.currentGuess.length < 5) {
      this.currentGuess += key;
    }
  }

  submitGuess(): void {
    if (this.isValidWord(this.currentGuess)) {
      const evaluation = this.evaluateGuess(this.currentGuess);
      this.evaluatedGuesses.push({ guess: this.currentGuess, evaluation });

      if (this.currentGuess === this.word) {
        setTimeout(() => {
          this.gameOver = true;
          this.isWinner = true;
          this.gameResult = 'win';
          this.score += 100;
          this.streak++;
          this.showAnimation = true;
        }, 1500);
      } else if (this.evaluatedGuesses.length >= this.maxTurns) {
        setTimeout(() => {
          this.gameOver = true;
          this.isWinner = false;
          this.gameResult = 'lose';
          this.streak = 0;
        }, 1500);
      }

      const currentRow = this.evaluatedGuesses.length - 1;
      this.currentGuess = '';

      // Flip animation with delay for each letter
      setTimeout(() => {
        const tiles = document.querySelectorAll(`.row-${currentRow} .letter-tile`);
        tiles.forEach((tile, i) => {
          setTimeout(() => {
            tile.classList.add('flip');
            setTimeout(() => {
              const faceImage = tile.querySelector('.face-image');
              if (faceImage) {
                faceImage.classList.add('show');
              }
            }, 300);
          }, i * 200);
        });
      }, 100);
    } else {
      this.shakeCurrentRow();
    }
  }

  evaluateGuess(guess: string): Array<{ letter: string, status: 'correct' | 'present' | 'absent' }> {
    const evaluation: Array<{ letter: string, status: 'correct' | 'present' | 'absent' }> = [];
    const wordLetters = this.word.split('');
    const guessLetters = guess.toUpperCase().split('');

    // First pass: Find correct letters
    for (let i = 0; i < guessLetters.length; i++) {
      if (guessLetters[i] === wordLetters[i]) {
        evaluation[i] = { letter: guessLetters[i], status: 'correct' };
        wordLetters[i] = '*'; // Mark as used
      }
    }

    // Second pass: Find present letters
    for (let i = 0; i < guessLetters.length; i++) {
      if (!evaluation[i]) {
        const index = wordLetters.indexOf(guessLetters[i]);
        if (index !== -1) {
          evaluation[i] = { letter: guessLetters[i], status: 'present' };
          wordLetters[index] = '*'; // Mark as used
        } else {
          evaluation[i] = { letter: guessLetters[i], status: 'absent' };
        }
      }
    }

    return evaluation;
  }

  getFaceImage(status: 'correct' | 'present' | 'absent'): string {
    // Default face images if not provided in config
    const defaultFaceImages = {
      correct: 'assets/images/games/wordle/correct.png',
      present: 'assets/images/games/wordle/present.png',
      absent: 'assets/images/games/wordle/absent.png'
    };

    if (!this.config?.gameStyles?.faceImages) {
      return defaultFaceImages[status];
    }

    switch (status) {
      case 'correct':
        return this.config.gameStyles.faceImages.correct || defaultFaceImages.correct;
      case 'present':
        return this.config.gameStyles.faceImages.present || defaultFaceImages.present;
      case 'absent':
        return this.config.gameStyles.faceImages.absent || defaultFaceImages.absent;
      default:
        return defaultFaceImages.absent;
    }
  }

  flipRow(index: number): void {
    const tiles = document.querySelectorAll(`.row-${index} .letter-tile`);
    tiles.forEach((tile, i) => {
      setTimeout(() => {
        tile.classList.add('flip');
        // Show face image halfway through the flip animation
        setTimeout(() => {
          const faceImage = tile.querySelector('.face-image');
          if (faceImage) {
            faceImage.classList.add('show');
          }
        }, 300);
      }, i * 200); // Stagger the animations
    });
  }

  shakeCurrentRow(): void {
    const currentRow = document.querySelector('.current-row');
    if (currentRow) {
      currentRow.classList.add('shake');
      setTimeout(() => {
        currentRow.classList.remove('shake');
      }, 500);
    }
  }

  getKeyboardLetterClass(letter: string): string {
    let isCorrect = false;
    let isPresent = false;

    for (const evaluated of this.evaluatedGuesses) {
      const evaluation = this.evaluateGuess(evaluated.guess);
      for (let i = 0; i < evaluation.length; i++) {
        if (evaluation[i].letter === letter) {
          if (evaluation[i].status === 'correct') {
            isCorrect = true;
            break;
          } else if (evaluation[i].status === 'present') {
            isPresent = true;
          }
        }
      }
      if (isCorrect) break;
    }

    if (isCorrect) {
      return 'bg-gradient-to-r from-green-500 to-green-600 text-white';
    } else if (isPresent) {
      return 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-white';
    } else if (this.evaluatedGuesses.some(e => e.guess.includes(letter))) {
      return 'bg-gradient-to-r from-slate-600 to-slate-700 text-white';
    }
    return 'bg-gradient-to-r from-slate-400 to-slate-500 text-white';
  }

  getRandomWord(): string {
    return this.words[Math.floor(Math.random() * this.words.length)];
  }

  isValidWord(word: string): boolean {
    // For now, accept any 5-letter word
    return word.length === 5;
  }

  // Method to toggle letters visibility for a row
  toggleLettersForRow(rowIndex: number): void {
    // If we're already showing letters for this row, do nothing
    if (this.showingLettersForRow === rowIndex) {
      return;
    }

    // If we're showing letters for a different row, clear that first
    if (this.letterShowTimeout) {
      clearTimeout(this.letterShowTimeout);
    }

    // Show letters for the clicked row
    this.showingLettersForRow = rowIndex;

    // Hide letters after 3 seconds
    this.letterShowTimeout = setTimeout(() => {
      this.showingLettersForRow = null;
    }, 3000);
  }

  // Method to trigger button animation
  triggerButtonAnimation(button: HTMLButtonElement, letter: string): void {
    if (this.animatingButtons.has(letter)) {
      return;
    }

    this.animatingButtons.add(letter);
    button.classList.add('spark', 'ripple');

    // Remove animation classes after animation completes
    setTimeout(() => {
      button.classList.remove('spark', 'ripple');
      this.animatingButtons.delete(letter);
    }, 600);
  }

  // Get remaining empty rows
  getRemainingRows(): number[] {
    const remaining = this.maxTurns - this.evaluatedGuesses.length - (this.gameOver ? 0 : 1);
    return remaining > 0 ? Array(remaining).fill(0) : [];
  }

  // Get game over message with face
  getGameOverMessage(): { message: string; face: string } {
    // Default messages and faces if not provided in config
    const defaultMessages = {
      win: {
        message: 'Congratulations! You guessed the word!',
        face: 'assets/images/games/wordle/win-face.png'
      },
      lose: {
        message: `Sorry! The word was: ${this.word}`,
        face: 'assets/images/games/wordle/lose-face.png'
      }
    };

    if (!this.config?.gameStyles) {
      return this.gameResult === 'win' ? defaultMessages.win : defaultMessages.lose;
    }

    if (this.gameResult === 'win') {
      if (this.config.gameStyles.win) {
        return {
          message: this.config.gameStyles.win.message || defaultMessages.win.message,
          face: this.config.gameStyles.win.face || defaultMessages.win.face
        };
      }
      return defaultMessages.win;
    } else {
      if (this.config.gameStyles.lose) {
        // Replace ${this.word} with actual word value
        let message = this.config.gameStyles.lose.message || defaultMessages.lose.message;
        message = message.replace('${this.word}', this.word);
        return {
          message,
          face: this.config.gameStyles.lose.face || defaultMessages.lose.face
        };
      }
      return defaultMessages.lose;
    }
  }
}
