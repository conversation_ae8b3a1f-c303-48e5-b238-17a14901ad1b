<div class="flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800">
  <!-- Demo Mode Indicator -->
  <div *ngIf="demoMode" class="demo-mode-indicator">
    Demo Mode
  </div>

  <!-- Game Title -->
  <div class="text-center mb-1">
    <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">Solitaire</h1>
    <p class="text-xs text-slate-400 mb-1">Classic card game of patience and strategy</p>
  </div>

  <!-- Game Score -->
  <div class="w-full max-w-md mx-auto px-4 py-2">
    <games-score [scores]="getScoreItems()"></games-score>
  </div>

  <!-- Game Board -->
  <div class="game-board flex-grow overflow-auto p-4">
    <div class="solitaire-table max-w-4xl mx-auto">
      <!-- Stock and Waste -->
      <div class="stock-waste-area">
        <!-- Stock Pile -->
        <div
          class="stock-pile"
          (click)="drawCard()"
        >
          <div
            *ngFor="let card of stock; let i = index"
            class="card card-back"
          ></div>
        </div>

        <!-- Waste Pile -->
        <div
          class="waste-pile-area"
          (dragover)="allowDrop($event)"
          (drop)="onDrop($event, 'waste')"
        >
          <div
            *ngFor="let card of waste; let i = index"
            class="card"
            [ngClass]="{
              'card-red': card.color === 'red',
              'card-black': card.color === 'black',
              'card-highlighted': card.highlighted
            }"
            draggable="true"
            (dragstart)="onDragStart($event, 'waste', 0, i)"
            (click)="onCardClick('waste', 0, i)"
          >
            {{ card.rank }}{{ card.suit }}
          </div>
        </div>
      </div>

      <!-- Foundation Piles -->
      <div class="foundation-area">
        <div
          *ngFor="let foundation of foundations; let i = index"
          class="foundation-pile"
          [ngClass]="{'foundation-highlighted': highlightedFoundation === i}"
          (dragover)="allowDrop($event)"
          (drop)="onDrop($event, 'foundation', i)"
        >
          <div
            *ngFor="let card of foundation; let j = index"
            class="card"
            [ngClass]="{
              'card-red': card.color === 'red',
              'card-black': card.color === 'black',
              'card-highlighted': card.highlighted
            }"
            draggable="true"
            (dragstart)="onDragStart($event, 'foundation', i, j)"
            (click)="onCardClick('foundation', i, j)"
          >
            {{ card.rank }}{{ card.suit }}
          </div>
        </div>
      </div>

      <!-- Tableau Piles -->
      <div class="tableau-area">
        <div
          *ngFor="let pile of tableauPiles; let i = index"
          class="tableau-pile-container"
          (dragover)="allowDrop($event)"
          (drop)="onDrop($event, 'tableau', i)"
        >
          <div
            *ngFor="let card of pile; let j = index"
            class="tableau-card"
            [style.top.px]="getCardOffset(j)"
            [ngClass]="{
              'card-back': !card.faceUp,
              'card-red': card.faceUp && card.color === 'red',
              'card-black': card.faceUp && card.color === 'black',
              'card-highlighted': card.highlighted
            }"
            [attr.draggable]="card.faceUp"
            (dragstart)="onDragStart($event, 'tableau', i, j)"
            (click)="card.faceUp && onCardClick('tableau', i, j)"
          >
            <ng-container *ngIf="card.faceUp">{{ card.rank }}{{ card.suit }}</ng-container>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Game Controls -->
  <div class="game-controls p-4">
    <lib-games-play-button
      [controls]="gameControls"
      (controlClicked)="handleControlClick($event)"
    ></lib-games-play-button>
  </div>

  <!-- Win/Lose Overlay -->
  <lib-win-lose-overlay
    *ngIf="gameResult"
    [status]="gameResult"
    [message]="gameResult === 'win' ? 'Congratulations!' : ''"
    [score]="score"
    [highScore]="highScore"
    (restart)="newGame()"
  ></lib-win-lose-overlay>
</div>
