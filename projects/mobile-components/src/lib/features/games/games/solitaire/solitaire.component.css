/* Import shared game styles */
@import '../styles/game-styles.css';

:host {
  @apply block w-full h-full;
}

/* Demo Mode Indicator */
.demo-mode-indicator {
  @apply absolute top-2 right-2 px-2 py-1 text-xs font-bold text-white rounded-full z-20;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

/* Game Board */
.game-board {
  @apply bg-slate-800/50 rounded-lg;
  min-height: 300px;
}

.solitaire-table {
  @apply relative;
}

/* Card Styling */
.card {
  @apply flex justify-center items-center w-14 h-20 rounded-lg shadow-md transition-all duration-200 ease-in-out;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 1px solid rgba(100, 116, 139, 0.5);
  font-weight: bold;
  font-size: 1.2rem;
  position: relative;
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

.card-red {
  @apply text-red-500;
  background: linear-gradient(135deg, #fee2e2, #fecaca);
}

.card-black {
  @apply text-slate-800;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

.card-back {
  background: linear-gradient(135deg, #1e40af, #3b82f6);
  @apply border-2 border-blue-300;
}

.card-highlighted {
  @apply ring-4 ring-yellow-400;
  animation: highlight-pulse 1.5s infinite;
}

@keyframes highlight-pulse {
  0% { box-shadow: 0 0 0 0 rgba(250, 204, 21, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(250, 204, 21, 0); }
  100% { box-shadow: 0 0 0 0 rgba(250, 204, 21, 0); }
}

/* Stock and Waste Area */
.stock-waste-area {
  @apply flex justify-center space-x-4 mb-5;
  min-height: 80px;
}

/* Stock Pile */
.stock-pile {
  @apply w-14 h-20 rounded-lg cursor-pointer relative;
  background: linear-gradient(135deg, #1e40af, #3b82f6);
  border: 1px dashed rgba(100, 116, 139, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.stock-pile .card {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.stock-pile:hover {
  @apply shadow-lg;
  transform: translateY(-2px);
}

.stock-pile:active {
  transform: translateY(0);
}

/* Waste Pile */
.waste-pile-area {
  @apply w-14 h-20 rounded-lg relative;
  border: 1px dashed rgba(100, 116, 139, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.waste-pile-area .card {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Foundation Piles */
.foundation-pile {
  @apply w-14 h-20 rounded-lg mr-2 mb-2 relative;
  border: 1px dashed rgba(100, 116, 139, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.foundation-pile .card {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.foundation-highlighted {
  @apply ring-4 ring-yellow-400;
  animation: highlight-pulse 1.5s infinite;
}

/* Foundation Area */
.foundation-area {
  @apply flex justify-center space-x-2 mb-5;
  min-height: 80px;
}

/* Tableau Piles */
.tableau-pile-container {
  @apply w-14 mr-2 mb-2 relative;
  min-height: 120px; /* Increased to accommodate more cards */
}

.tableau-area {
  @apply flex justify-center space-x-2;
}

.tableau-card {
  @apply flex justify-center items-center w-14 h-20 rounded-lg shadow-md transition-all duration-200 ease-in-out;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 1px solid rgba(100, 116, 139, 0.5);
  font-weight: bold;
  font-size: 1.2rem;
  position: absolute;
  left: 0;
  /* top is set dynamically with [style.top.px] in the template */
  transform-style: preserve-3d;
  backface-visibility: hidden;
  z-index: 1; /* Ensure proper stacking */
}

/* Increase z-index for each card to ensure proper stacking order */
.tableau-card:nth-child(2) { z-index: 2; }
.tableau-card:nth-child(3) { z-index: 3; }
.tableau-card:nth-child(4) { z-index: 4; }
.tableau-card:nth-child(5) { z-index: 5; }
.tableau-card:nth-child(6) { z-index: 6; }
.tableau-card:nth-child(7) { z-index: 7; }
.tableau-card:nth-child(8) { z-index: 8; }
.tableau-card:nth-child(9) { z-index: 9; }
.tableau-card:nth-child(10) { z-index: 10; }
.tableau-card:nth-child(11) { z-index: 11; }
.tableau-card:nth-child(12) { z-index: 12; }
.tableau-card:nth-child(13) { z-index: 13; }

.tableau-card.card-back {
  background: linear-gradient(135deg, #1e40af, #3b82f6);
  @apply border-2 border-blue-300;
}

.tableau-card.card-red {
  @apply text-red-500;
  background: linear-gradient(135deg, #fee2e2, #fecaca);
}

.tableau-card.card-black {
  @apply text-slate-800;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

.tableau-card.card-highlighted {
  @apply ring-4 ring-yellow-400;
  animation: highlight-pulse 1.5s infinite;
}

/* Game Controls */
.game-controls {
  @apply w-full max-w-md mx-auto;
}

/* Responsive Adjustments */
@media (max-width: 640px) {
  .card, .foundation-pile, .stock-pile, .waste-pile-area, .tableau-card {
    @apply w-12 h-16;
    font-size: 0.9rem;
  }

  .tableau-card {
    margin-top: 15px; /* Reduced spacing for smaller screens */
  }

  .tableau-pile-container {
    @apply w-12;
    min-height: 100px;
  }

  .foundation-area, .stock-waste-area {
    @apply space-x-2;
  }

  .foundation-pile, .tableau-pile-container {
    @apply mr-1 mb-1;
  }

  .game-board {
    @apply p-2; /* Reduce padding on small screens */
  }
}

/* Extra small screens */
@media (max-width: 375px) {
  .card, .foundation-pile, .stock-pile, .waste-pile-area, .tableau-card {
    @apply w-10 h-14;
    font-size: 0.8rem;
  }

  .tableau-card {
    margin-top: 12px; /* Even smaller spacing */
  }

  .tableau-pile-container {
    @apply w-10;
    min-height: 90px;
  }

  .foundation-area, .stock-waste-area, .tableau-area {
    @apply space-x-1;
  }
}
