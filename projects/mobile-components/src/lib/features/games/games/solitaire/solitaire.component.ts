import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, Input, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import { NgIf, NgFor, NgClass, CommonModule } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { WinLoseOverlayComponent } from '../components/win-lose-overlay/win-lose-overlay.component';
import { GameControl, GamesPlayButtonComponent } from '../components/games-play-button/games-play-button.component';
import { Game, GameEvent } from 'lp-client-api';
import { GameService } from 'lp-client-api';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

// Define the Solitaire game configuration interface
interface SolitaireConfig {
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  maxAttempts: number;
  variation: 'klondike' | 'spider' | 'freecell';
  drawCount: number;
  scoring: {
    wasteToTableau: number;
    wasteToFoundation: number;
    tableauToFoundation: number;
    turnOverTableau: number;
    foundationToTableau: number;
  };
}

interface Card {
  suit: string;
  rank: string;
  color: string;
  faceUp: boolean;
  highlighted?: boolean;
}

@Component({
  selector: 'lib-solitaire',
  templateUrl: './solitaire.component.html',
  styleUrls: ['./solitaire.component.css'],
  standalone: true,
  imports: [CommonModule, NgIf, NgFor, NgClass, GamesScoreComponent, WinLoseOverlayComponent, GamesPlayButtonComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class SolitaireComponent implements OnInit {
  private destroy$ = new Subject<void>();

  // Input properties
  @Input() gameId?: string;
  @Input() game?: Game;
  @Input() gameInstance?: any;
  @Input() config: any = {};

  // Output events
  @Output() gameEvent = new EventEmitter<GameEvent>();

  // Game state
  foundations: Card[][] = [[], [], [], []];
  tableauPiles: Card[][] = [[], [], [], [], [], [], []];
  stock: Card[] = [];
  waste: Card[] = [];
  moves: number = 0;
  time: string = '00:00';
  score: number = 0;
  highScore: number = 0;
  currentVariation: string = 'klondike';
  gameResult: 'win' | 'lose' | null = null;
  seconds: number = 0;
  timerInterval: any;

  // Game configuration
  solitaireGameId = 0; // Will be set from input or default
  solitaireConfig?: SolitaireConfig;
  canPlay: boolean = false;
  attemptsRemaining: number = 3; // Default attempts
  demoMode: boolean = false; // Demo mode flag

  // Game controls
  gameControls: GameControl[] = [
    { name: 'restart', label: 'New Game', icon: 'refresh-outline' },
    { name: 'hint', label: 'Hint', icon: 'bulb-outline' },
    { name: 'undo', label: 'Undo', icon: 'arrow-undo-outline' }
  ];

  private deck: Card[] = [];
  private moveHistory: any[] = [];

  constructor(
    private gameService: GameService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    console.log('Solitaire component initialized');

    // Check if we have a game ID from input
    if (this.game?.id) {
      this.solitaireGameId = this.game.id;
      console.log('Using game ID from input:', this.solitaireGameId);
    }

    // Try to load game config from API
    this.loadGameConfig();
  }

  /**
   * Load game configuration from API or use demo mode
   */
  private loadGameConfig(): void {
    // If we have a game from input, use its configuration
    if (this.game) {
      console.log('Using game configuration from input:', this.game);
      this.setupFromGameConfig();
      return;
    }

    // Otherwise, use demo mode
    console.log('No game configuration from input, using demo mode');
    this.setupDemoMode();
  }

  /**
   * Setup game from API configuration
   */
  private setupFromGameConfig(): void {
    // Extract solitaire configuration from game config
    const gameConfig = this.game?.gameConfig?.[0];
    if (gameConfig) {
      this.solitaireConfig = {
        difficulty: (gameConfig.difficulty as 'EASY' | 'MEDIUM' | 'HARD') || 'MEDIUM',
        maxAttempts: gameConfig.frequencyAttempts || 3,
        variation: 'klondike',
        drawCount: 1,
        scoring: {
          wasteToTableau: 5,
          wasteToFoundation: 10,
          tableauToFoundation: 10,
          turnOverTableau: 5,
          foundationToTableau: -15
        }
      };

      // Check if the game can be played
      if (this.solitaireGameId) {
        this.gameService.checkGameAvailability(this.solitaireGameId)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: (canPlay) => {
              this.canPlay = canPlay;
              if (!canPlay) {
                console.log('Game cannot be played, using demo mode');
                this.setupDemoMode();
              } else {
                console.log('Game can be played, initializing solitaire');
                this.newGame();
              }
            },
            error: (error) => {
              console.error('Error checking game availability:', error);
              this.setupDemoMode();
            }
          });
      } else {
        this.setupDemoMode();
      }
    } else {
      this.setupDemoMode();
    }
  }

  /**
   * Setup demo mode with default configuration
   */
  private setupDemoMode(): void {
    this.demoMode = true;
    this.canPlay = true;
    this.attemptsRemaining = 3;

    // Set default configuration for demo mode
    this.solitaireConfig = {
      difficulty: 'MEDIUM',
      maxAttempts: 3,
      variation: 'klondike',
      drawCount: 1,
      scoring: {
        wasteToTableau: 5,
        wasteToFoundation: 10,
        tableauToFoundation: 10,
        turnOverTableau: 5,
        foundationToTableau: -15
      }
    };

    // Initialize the game
    this.newGame();
  }

  newGame(): void {
    // Stop any existing timer
    this.stopTimer();

    // Reset game state
    this.gameResult = null;

    // Initialize the deck and deal cards
    this.initializeDeck();
    this.shuffleDeck();
    this.dealCards();
    this.resetGameState();
    this.resetTimer();

    // Start the timer
    this.startTimer();

    // Emit game start event
    this.emitGameEvent('start', 0);
  }

  private initializeDeck(): void {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = [
      'A',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '10',
      'J',
      'Q',
      'K',
    ];
    this.deck = [];

    for (const suit of suits) {
      for (const rank of ranks) {
        this.deck.push({
          suit,
          rank,
          color: suit === '♠' || suit === '♣' ? 'black' : 'red',
          faceUp: false,
        });
      }
    }
  }

  private shuffleDeck(): void {
    for (let i = this.deck.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [this.deck[i], this.deck[j]] = [this.deck[j], this.deck[i]];
    }
  }

  private dealCards(): void {
    this.tableauPiles = Array(7)
      .fill(null)
      .map(() => []);
    for (let i = 0; i < 7; i++) {
      for (let j = i; j < 7; j++) {
        const card = this.deck.pop()!;
        card.faceUp = i === j;
        this.tableauPiles[j].push(card);
      }
    }
    this.stock = this.deck;
  }

  private resetGameState(): void {
    this.foundations = [[], [], [], []];
    this.waste = [];
    this.moves = 0;
    this.time = '00:00';
    this.seconds = 0;
    this.score = 0;
    this.moveHistory = [];
    this.gameResult = null;
  }

  private resetTimer(): void {
    this.stopTimer();
    this.seconds = 0;
    this.time = '00:00';
  }

  private startTimer(): void {
    this.stopTimer();
    this.timerInterval = setInterval(() => {
      this.seconds++;
      const minutes = Math.floor(this.seconds / 60);
      const remainingSeconds = this.seconds % 60;
      this.time = `${minutes.toString().padStart(2, '0')}:${remainingSeconds
        .toString()
        .padStart(2, '0')}`;
      this.cdr.detectChanges();
    }, 1000);
  }

  private stopTimer(): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
  }

  /**
   * Get score items for the games-score component
   */
  getScoreItems() {
    return [
      { title: 'Score', number: this.score },
      { title: 'Moves', number: this.moves },
      { title: 'Time', number: this.time }
    ];
  }

  /**
   * Handle control clicks from the games-play-button component
   */
  handleControlClick(event: any) {
    // Convert the event to a string if it's not already
    const controlName = typeof event === 'string' ? event : event.toString();

    switch (controlName) {
      case 'restart':
        this.newGame();
        break;
      case 'hint':
        this.hint();
        break;
      case 'undo':
        this.undo();
        break;
      default:
        break;
    }
  }

  drawCard(): void {
    if (this.stock.length === 0) {
      this.stock = this.waste.reverse();
      this.waste = [];
    } else {
      const card = this.stock.pop()!;
      card.faceUp = true;
      this.waste.push(card);
    }
    this.moves++;
    this.updateScore();
  }

  // onDrop method is now implemented below with more comprehensive logic

  private isValidMove(
    card: Card,
    destination: string,
    index?: number
  ): boolean {
    if (destination === 'foundation') {
      const foundation = this.foundations[index!];
      if (foundation.length === 0) {
        return card.rank === 'A';
      }
      const topCard = foundation[foundation.length - 1];
      return (
        card.suit === topCard.suit &&
        this.getRankValue(card.rank) === this.getRankValue(topCard.rank) + 1
      );
    } else if (destination === 'tableau') {
      const tableau = this.tableauPiles[index!];
      if (tableau.length === 0) {
        return card.rank === 'K';
      }
      const topCard = tableau[tableau.length - 1];
      return (
        card.color !== topCard.color &&
        this.getRankValue(card.rank) === this.getRankValue(topCard.rank) - 1
      );
    }
    return false;
  }

  private moveCard(
    card: Card,
    sourceArray: Card[],
    destination: string,
    index?: number
  ): void {
    const cardIndex = sourceArray.indexOf(card);
    const movedCards = sourceArray.splice(cardIndex);

    if (destination === 'foundation') {
      // Only move one card to the foundation
      this.foundations[index!].push(movedCards[0]);
      // Put the rest back
      sourceArray.push(...movedCards.slice(1));
    } else if (destination === 'tableau') {
      this.tableauPiles[index!].push(...movedCards);
    }

    if (sourceArray.length > 0 && !sourceArray[sourceArray.length - 1].faceUp) {
      sourceArray[sourceArray.length - 1].faceUp = true;
    }

    this.moveHistory.push({ card, sourceArray, destination, index });
  }

  undo(): void {
    if (this.moveHistory.length === 0) return;

    const lastMove = this.moveHistory.pop()!;
    const { card, sourceArray, destination, index } = lastMove;

    const currentArray = this.getArrayByType(destination, index);
    const movedCards = currentArray.splice(currentArray.indexOf(card));
    sourceArray.push(...movedCards);

    if (sourceArray[sourceArray.length - 1] !== card) {
      sourceArray[sourceArray.length - 1].faceUp = false;
    }

    this.moves++;
    this.updateScore();
  }

  hint(): void {
    for (let i = 0; i < this.tableauPiles.length; i++) {
      const tableau = this.tableauPiles[i];
      if (tableau.length === 0) continue;

      const card = tableau[tableau.length - 1];
      for (let j = 0; j < this.foundations.length; j++) {
        if (this.isValidMove(card, 'foundation', j)) {
          this.highlightCard(card, 'tableau', i, tableau.length - 1);
          this.highlightFoundation(j);
          return;
        }
      }
    }

    // Check waste pile
    if (this.waste.length > 0) {
      const card = this.waste[this.waste.length - 1];
      for (let j = 0; j < this.foundations.length; j++) {
        if (this.isValidMove(card, 'foundation', j)) {
          this.highlightCard(card, 'waste', 0, this.waste.length - 1);
          this.highlightFoundation(j);
          return;
        }
      }
    }

    alert('No hints available');
  }

  private highlightCard(
    card: Card,
    sourceType: string,
    sourceIndex: number,
    cardIndex: number
  ): void {
    card.highlighted = true;
    setTimeout(() => {
      card.highlighted = false;
    }, 2000);
  }

  // Add a property to track highlighted foundations
  public highlightedFoundation: number | null = null;

  private highlightFoundation(index: number): void {
    this.highlightedFoundation = index;
    setTimeout(() => {
      this.highlightedFoundation = null;
    }, 2000);
  }

  private updateScore(): void {
    // Calculate score based on cards in foundation and moves
    const foundationScore = this.foundations.reduce(
      (sum, foundation) => sum + foundation.length * 10,
      0
    );

    // Apply scoring based on configuration
    const movesPenalty = this.moves * 0.5;
    this.score = Math.max(0, Math.floor(foundationScore - movesPenalty));

    // Force change detection
    this.cdr.detectChanges();
  }

  private checkForWin(): void {
    if (this.foundations.every((foundation) => foundation.length === 13)) {
      // Stop the timer
      this.stopTimer();

      // Set game result to win
      this.gameResult = 'win';

      // Update high score if needed
      if (this.score > this.highScore) {
        this.highScore = this.score;
      }

      // Emit game win event
      this.emitGameEvent('win', this.score);

      // Save score to API if not in demo mode
      if (!this.demoMode && this.solitaireGameId) {
        this.gameService.saveGameScore(this.solitaireGameId, this.score)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: (response) => {
              console.log('Score saved:', response);
            },
            error: (error) => {
              console.error('Error saving score:', error);
            }
          });
      }
    }
  }

  /**
   * Emit a game event to the parent component
   * @param state The state of the game (start, win, lose, etc.)
   * @param score The current score
   */
  private emitGameEvent(state: string, score: number): void {
    if (!this.demoMode && this.gameInstance?.id) {
      // Create a game event
      const event: GameEvent = {
        id: 0, // Will be assigned by the API
        level: 1,
        score: score,
        duration: this.seconds,
        state: state,
        payload: JSON.stringify({
          score: this.score,
          moves: this.moves,
          time: this.time,
          foundationCards: this.foundations.reduce((sum, foundation) => sum + foundation.length, 0)
        })
      };

      // Emit the event
      this.gameEvent.emit(event);

      // Save the event to the API
      if (this.solitaireGameId && this.gameInstance?.id) {
        this.gameService.createGameEvent(
          this.solitaireGameId,
          this.gameInstance.id,
          event
        ).pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            console.log('Game event saved:', response);
          },
          error: (error) => {
            console.error('Error saving game event:', error);
          }
        });
      }
    } else {
      // In demo mode, just emit the event without saving to API
      const demoEvent: GameEvent = {
        id: Math.floor(Math.random() * 1000),
        level: 1,
        score: score,
        duration: this.seconds,
        state: state,
        payload: JSON.stringify({
          score: this.score,
          moves: this.moves,
          time: this.time,
          foundationCards: this.foundations.reduce((sum, foundation) => sum + foundation.length, 0)
        })
      };

      this.gameEvent.emit(demoEvent);
    }
  }

  private getArrayByType(type: string, index?: number): Card[] {
    switch (type) {
      case 'tableau':
        return this.tableauPiles[index!];
      case 'foundation':
        return this.foundations[index!];
      case 'waste':
        return this.waste;
      default:
        return [];
    }
  }

  private getRankValue(rank: string): number {
    const ranks = [
      'A',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      '10',
      'J',
      'Q',
      'K',
    ];
    return ranks.indexOf(rank) + 1;
  }

  changeVariation(variation: string): void {
    // Switch between game variations
    this.currentVariation = variation;
    this.newGame();
  }

  // Add more methods for game logic, card movement, scoring, etc.

  ngOnDestroy(): void {
    // Stop the timer
    this.stopTimer();

    // Complete the destroy subject
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Add this method to handle card clicks
  onCardClick(
    sourceType: string,
    sourceIndex: number,
    cardIndex: number
  ): void {
    // Don't allow moves if game is over
    if (this.gameResult) return;

    const sourceArray = this.getArrayByType(sourceType, sourceIndex);
    const card = sourceArray[cardIndex];

    // Try to move to foundation first
    for (let i = 0; i < this.foundations.length; i++) {
      if (this.isValidMove(card, 'foundation', i)) {
        this.moveCard(card, sourceArray, 'foundation', i);
        this.moves++;
        this.updateScore();
        this.checkForWin();
        return;
      }
    }

    // If can't move to foundation, try to move to tableau
    for (let i = 0; i < this.tableauPiles.length; i++) {
      if (this.isValidMove(card, 'tableau', i)) {
        this.moveCard(card, sourceArray, 'tableau', i);
        this.moves++;
        this.updateScore();
        return;
      }
    }
  }

  // Add this method to handle card dragging
  onDragStart(
    event: DragEvent,
    sourceType: string,
    sourceIndex: number,
    cardIndex: number
  ): void {
    event.dataTransfer?.setData(
      'text/plain',
      `${sourceType},${sourceIndex},${cardIndex}`
    );
  }

  // Update allowDrop method
  allowDrop(event: DragEvent): void {
    event.preventDefault();
  }

  // Handle drop events
  onDrop(event: DragEvent, destinationType: string, destinationIndex?: number): void {
    event.preventDefault();

    // Don't allow moves if game is over
    if (this.gameResult) return;

    const data = event.dataTransfer?.getData('text/plain');
    if (!data) return;

    const [sourceType, sourceIndex, cardIndex] = data.split(',').map(val => parseInt(val) || val);
    const sourceArray = this.getArrayByType(sourceType as string, sourceIndex as number);

    if (!sourceArray || cardIndex === undefined) return;

    const card = sourceArray[cardIndex as number];

    if (this.isValidMove(card, destinationType, destinationIndex)) {
      this.moveCard(card, sourceArray, destinationType, destinationIndex);
      this.moves++;
      this.updateScore();

      if (destinationType === 'foundation') {
        this.checkForWin();
      }
    }
  }

  // Calculate card offset based on screen size
  getCardOffset(index: number): number {
    // Get window width
    const width = window.innerWidth;

    // Adjust offset based on screen size
    if (width <= 375) {
      return index * 12; // Smaller offset for very small screens
    } else if (width <= 640) {
      return index * 15; // Medium offset for small screens
    } else {
      return index * 20; // Default offset for larger screens
    }
  }
}
