import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { SolitaireComponent } from './solitaire.component';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    SolitaireComponent
  ],
  exports: [
    SolitaireComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class SolitaireModule { }
