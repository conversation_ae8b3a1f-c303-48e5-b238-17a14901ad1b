.games-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  height: 100vh;
  overflow-y: auto;
}

.games-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 20px;
}

.game-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
  }

  .game-image-container {
    position: relative;
    width: 100%;
    height: 180px;
    overflow: hidden;

    .game-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    .game-status {
      position: absolute;
      top: 12px;
      right: 12px;
      z-index: 1;

      .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        
        &.active {
          background-color: #4CAF50;
          color: white;
        }
        
        &.inactive {
          background-color: #f44336;
          color: white;
        }
      }
    }
  }

  .game-info {
    padding: 20px;

    .game-title {
      margin: 0 0 8px;
      font-size: 1.4rem;
      font-weight: 600;
      color: #2c3e50;
    }

    .game-description {
      margin: 0 0 16px;
      font-size: 0.95rem;
      color: #5d6d7e;
      line-height: 1.5;
    }

    .game-meta {
      margin-bottom: 16px;

      .genre-badge {
        display: inline-block;
        padding: 4px 12px;
        background-color: #e3f2fd;
        color: #1976d2;
        border-radius: 16px;
        font-size: 0.85rem;
        font-weight: 500;
        margin-bottom: 12px;
      }

      .game-stats {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        margin-top: 8px;

        .stat-item {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 0.9rem;
          color: #64748b;

          i {
            font-size: 1rem;
            color: #475569;
          }
        }
      }
    }

    .game-dates {
      display: flex;
      justify-content: space-between;
      font-size: 0.85rem;
      color: #64748b;
      border-top: 1px solid #e2e8f0;
      padding-top: 12px;
      margin-top: 12px;

      .date-item {
        strong {
          color: #475569;
          font-weight: 500;
        }
      }
    }
  }
}

.no-games {
  text-align: center;
  padding: 40px;
  color: #64748b;
  font-size: 1.1rem;
  background: white;
  border-radius: 12px;
  margin-top: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}