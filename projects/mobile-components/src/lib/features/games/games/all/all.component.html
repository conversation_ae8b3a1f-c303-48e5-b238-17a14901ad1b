<div class="flex overflow-hidden overflow-y-auto flex-col min-h-screen bg-blue-200">
  <div class="absolute inset-0 z-0 h-screen bg-blue-400 animated-bg"></div>
  <div class="overflow-y-auto absolute inset-0 z-10">
    <games-header bgColor="blue"></games-header>
    <div class="flex relative flex-col flex-grow p-4 pb-12 mx-auto space-y-4 max-w-4xl">
    <div
      *ngFor="let gameViewModel of games"
      class="game-card"
      (click)="playGame(gameViewModel)"
    >
      <div class="game-image-container">
        <div class="flex justify-center items-center p-2 w-full h-full">
          <img
          [src]="gameViewModel.display.image"
          [alt]="gameViewModel.game.name"
          class="object-fill"
        />
        </div>

        <div class="game-status">
          <span class="status-badge" [ngClass]="gameViewModel.game.status === 420 ? 'active' : 'inactive'">
            {{ gameViewModel.game.gameType.typeDescription.description }}
          </span>
        </div>
      </div>
 
      <div class="game-info">
        <h3 class="game-title">{{ gameViewModel.game.name }}</h3>
        <p class="game-description">{{ gameViewModel.display.description }}</p>

        <div class="game-meta">
          <span class="genre-badge">{{ gameViewModel.display.genre }}</span>
          <div class="game-stats">
            <span class="stat-item">
              <i class="fas fa-signal"></i>
              {{ gameViewModel.display.difficulty }}
            </span>
            <span class="stat-item">
              <i class="fas fa-redo"></i>
              {{ gameViewModel.display.frequency }}
            </span>
            <span class="stat-item">
              <i class="fas fa-gamepad"></i>
              {{ gameViewModel.display.attempts }} attempts
            </span>
          </div>
        </div>

        <div class="game-dates" *ngIf="gameViewModel.game.startDate && gameViewModel.game.endDate">
          <span class="date-item">
            <strong>Start:</strong> {{ gameViewModel.game.startDate | date:'shortDate' }}
          </span>
          <span class="date-item">
            <strong>End:</strong> {{ gameViewModel.game.endDate | date:'shortDate' }}
          </span>
        </div>
      </div>
    </div>
  </div>
  </div>

  <div *ngIf="games.length === 0" class="no-games">
    <p>No games available at the moment.</p>
  </div>
</div>

