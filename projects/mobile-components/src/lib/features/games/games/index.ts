export * from './snake/snake.component';
export * from './flappy-bird/flappy-bird.component';
export * from './quiz/quiz.component';
export * from './wordle/wordle.component';
export * from './hangman/hangman.component';
export * from './tic-tac-toe/tic-tac-toe.component';
export * from './memory/memory.component';
export * from './puzzle/puzzle.component';
export * from './racing/racing.component';
export * from './trivia/trivia.component';
export * from './2048/2048.component';
export * from './pac-man/pac-man.component';
export * from './breakout/breakout.component';
export * from './pong/pong.component';
export * from './simon-says/simon-says.component';
export * from './rock-paper-scissors/rock-paper-scissors.component';
export * from './platformer/platformer.component';
export * from './tower-defense/tower-defense.component';
export * from './tetris/tetris.component';
export * from './poker/poker.component';
export * from './sudoku/sudoku.component';
export * from './candy-crush/candy-crush.component';
export * from './chess/chess.component';
export * from './crossword/crossword.component';

export * from './categories/categories.component';
export { DashboardComponent as GameLoaderDashboardComponent } from './dashboard/dashboard.component';
export * from './all/all.component';
export { SingleComponent as FeatureGamesSingleWrapperComponent } from './single/single.component';
export * from './home/<USER>';
export * from './how-to-play/how-to-play.component';
export * from './favourites/favourites.component';
