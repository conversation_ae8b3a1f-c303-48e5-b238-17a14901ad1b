/* Import shared game styles */
@import '../styles/game-styles.css';

:host {
  @apply block w-full h-full;
}

/* Demo Mode Indicator */
.demo-mode-indicator {
  @apply absolute top-2 right-2 px-2 py-1 text-xs font-bold text-white rounded-full z-20;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

/* Game Area */
.game-area {
  @apply bg-slate-800 relative;
  min-height: 300px;
}

/* Road Markings */
.dashed-line {
  background: repeating-linear-gradient(
    to bottom,
    #f59e0b,
    #f59e0b 20px,
    transparent 20px,
    transparent 40px
  );
  animation: moveRoad 1s linear infinite;
}

@keyframes moveRoad {
  from { background-position: 0 0; }
  to { background-position: 0 40px; }
}

/* Car Styling */
.car {
  @apply relative;
  transform-origin: center center;
}

.car-body {
  @apply w-full h-full relative;
}

.car-chassis {
  @apply absolute inset-0 rounded-lg;
  background: linear-gradient(135deg, #ef4444, #b91c1c);
  box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
}

.car-windows {
  @apply absolute top-1 left-1 right-1 h-1/3 rounded-t-sm;
  background: linear-gradient(135deg, #93c5fd, #3b82f6);
}

.car-wheel {
  @apply absolute w-3 h-5 bg-gray-800 rounded-full;
  box-shadow: inset 0 0 2px rgba(255, 255, 255, 0.5);
}

.car-wheel-front-left {
  @apply -left-1 top-1/4;
}

.car-wheel-front-right {
  @apply -right-1 top-1/4;
}

.car-wheel-back-left {
  @apply -left-1 bottom-1;
}

.car-wheel-back-right {
  @apply -right-1 bottom-1;
}

.car-headlight {
  @apply absolute w-2 h-2 rounded-full;
  background: #fef3c7;
  box-shadow: 0 0 5px #fef3c7, 0 0 10px #fef3c7;
}

.car-headlight-left {
  @apply bottom-1 left-1;
}

.car-headlight-right {
  @apply bottom-1 right-1;
}

/* Obstacle Styling */
.obstacle {
  @apply rounded-lg;
  background: linear-gradient(135deg, #fbbf24, #d97706);
  box-shadow: 0 0 10px rgba(251, 191, 36, 0.5);
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Joystick Styling */
.joystick-area {
  @apply relative w-32 h-32 rounded-full touch-none;
  background: radial-gradient(circle, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.9) 100%);
  border: 2px solid rgba(100, 116, 139, 0.5);
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.5), 0 0 10px rgba(0, 0, 0, 0.3);
}

.joystick-handle {
  @apply absolute w-16 h-16 -translate-x-1/2 -translate-y-1/2 rounded-full left-1/2 top-1/2;
  background: radial-gradient(circle, rgba(71, 85, 105, 1) 0%, rgba(51, 65, 85, 1) 100%);
  border: 2px solid rgba(100, 116, 139, 0.8);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

/* Game Controls */
.game-controls {
  @apply w-full max-w-md px-4;
}

/* Responsive Adjustments */
@media (max-width: 640px) {
  .joystick-area {
    @apply w-24 h-24;
  }

  .joystick-handle {
    @apply w-12 h-12;
  }
}