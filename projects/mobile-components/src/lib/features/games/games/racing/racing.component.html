<div class="flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800">
  <!-- Demo Mode Indicator -->
  <div *ngIf="demoMode" class="demo-mode-indicator">
    Demo Mode
  </div>

  <!-- Game Title -->
  <div class="text-center mb-1">
    <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">Racing</h1>
    <p class="text-xs text-slate-400 mb-1">Avoid obstacles and survive as long as possible</p>
  </div>

  <!-- Game Score -->
  <div class="w-full max-w-md mx-auto px-4 py-2">
    <games-score [scores]="getScoreItems()"></games-score>
  </div>

  <!-- Game area -->
  <div
    class="game-area relative flex-grow w-full overflow-hidden"
    (click)="onScreenClick($event)"
    (touchstart)="onScreenTouch($event)"
    tabindex="0"
  >
    <!-- Game Background -->
    <div class="absolute inset-0 bg-gradient-to-b from-slate-800 to-slate-900 z-0">
      <!-- Road Markings -->
      <div class="absolute left-1/2 top-0 bottom-0 w-1 bg-yellow-400 z-0 transform -translate-x-1/2 dashed-line"></div>
    </div>

    <!-- Car -->
    <div
      [style.left.px]="car.x"
      [style.top.px]="car.y"
      class="car absolute z-10 transition-all duration-100 ease-in-out"
      [style.width.px]="car.width"
      [style.height.px]="car.height"
    >
      <!-- Car design with modern styling -->
      <div class="car-body">
        <!-- Car body -->
        <div class="car-chassis"></div>
        <!-- Windows -->
        <div class="car-windows"></div>
        <!-- Wheels -->
        <div class="car-wheel car-wheel-front-left"></div>
        <div class="car-wheel car-wheel-front-right"></div>
        <div class="car-wheel car-wheel-back-left"></div>
        <div class="car-wheel car-wheel-back-right"></div>
        <!-- Headlights -->
        <div class="car-headlight car-headlight-left"></div>
        <div class="car-headlight car-headlight-right"></div>
      </div>
    </div>

    <!-- Obstacles -->
    <div
      *ngFor="let obs of obstacles"
      [style.left.px]="obs.x"
      [style.top.px]="obs.y"
      class="obstacle absolute z-10 transition-all duration-100 ease-in-out"
      [style.width.px]="obs.width"
      [style.height.px]="obs.height"
    ></div>

    <!-- Joystick Control -->
    <div class="absolute bottom-32 left-1/2 -translate-x-1/2 z-20">
      <div
        #joystickArea
        class="joystick-area"
        (mousedown)="initJoystick($event)"
        (mousemove)="moveJoystick($event)"
        (mouseup)="stopJoystick()"
        (touchstart)="initJoystick($event)"
        (touchmove)="moveJoystick($event)"
        (touchend)="stopJoystick()"
      >
        <div
          #joystickHandle
          class="joystick-handle"
          [ngStyle]="joystickPosition"
        ></div>
      </div>
    </div>

    <!-- Game Controls -->
    <div class="game-controls absolute bottom-4 left-1/2 transform -translate-x-1/2 z-20">
      <lib-games-play-button
        [controls]="gameControls"
        (controlClicked)="handleControlClick($event)"
      ></lib-games-play-button>
    </div>

    <!-- Game Over Dialog -->
    <lib-win-lose-overlay
      [status]="gameResult"
      [message]="gameResult === 'lose' ? 'Game Over!' : ''"
      [score]="score"
      [highScore]="highScore"
      [level]="level"
      (restart)="restartGame()"
    ></lib-win-lose-overlay>
  </div>
</div>
