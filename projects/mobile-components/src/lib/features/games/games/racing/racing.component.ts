import {
  <PERSON>mpo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ElementRef,
  ViewChild,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
  Input,
  Output,
  EventEmitter,
  ChangeDetectorRef
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { WinLoseOverlayComponent } from '../components/win-lose-overlay/win-lose-overlay.component';
import { GameControl, GamesPlayButtonComponent } from '../components/games-play-button/games-play-button.component';
import { Game, GameEvent } from 'lp-client-api';
import { GameService } from 'lp-client-api';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

// Define the Racing game configuration interface
interface RacingConfig {
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  maxAttempts: number;
  carSpeed: number;
  obstacleSpeed: number;
  obstacleFrequency: number;
  levels: number;
  startLevel?: number;
}

// Define the car and obstacle interfaces
interface Car {
  x: number;
  y: number;
  width: number;
  height: number;
  speed: number;
}

interface Obstacle {
  x: number;
  y: number;
  width: number;
  height: number;
  speed: number;
}

@Component({
  selector: 'lib-racing',
  templateUrl: './racing.component.html',
  styleUrls: ['./racing.component.css'],
  standalone: true,
  imports: [CommonModule, GamesScoreComponent, WinLoseOverlayComponent, GamesPlayButtonComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class RacingComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Input properties
  @Input() gameId?: string;
  @Input() game?: Game;
  @Input() gameInstance?: any;
  @Input() config: any = {};

  // Output events
  @Output() gameEvent = new EventEmitter<GameEvent>();

  // Game state
  car: Car = { x: 0, y: 0, width: 48, height: 80, speed: 10 };
  obstacles: Obstacle[] = [];
  score = 0;
  highScore = 0;
  level = 1;
  gameResult: 'win' | 'lose' | null = null;
  gameAreaWidth!: number;
  gameAreaHeight!: number;
  private gameLoop: any;
  joystickPosition = { transform: 'translate(-50%, -50%)' };
  private isJoystickActive = false;
  private joystickCenter = { x: 0, y: 0 };
  private readonly maxJoystickDistance = 32; // Half of joystick area width/height

  // Game configuration
  racingGameId = 0; // Will be set from input or default
  racingConfig?: RacingConfig;
  canPlay: boolean = false;
  attemptsRemaining: number = 3; // Default attempts
  demoMode: boolean = false; // Demo mode flag

  // Game controls
  gameControls: GameControl[] = [
    { name: 'restart', label: 'Restart', icon: 'refresh-outline' },
  ];

  @ViewChild('joystickArea') joystickArea!: ElementRef;

  constructor(
    private elementRef: ElementRef,
    private gameService: GameService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit() {
    console.log('Racing component initialized');

    // Check if we have a game ID from input
    if (this.game?.id) {
      this.racingGameId = this.game.id;
      console.log('Using game ID from input:', this.racingGameId);
    }

    // Try to load game config from API
    this.loadGameConfig();
  }

  ngOnDestroy() {
    this.stopGameLoop();
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load game configuration from API or use demo mode
   */
  private loadGameConfig(): void {
    // If we have a game from input, use its configuration
    if (this.game) {
      console.log('Using game configuration from input:', this.game);
      this.setupFromGameConfig();
      return;
    }

    // Otherwise, use demo mode
    console.log('No game configuration from input, using demo mode');
    this.setupDemoMode();
  }

  /**
   * Setup game from API configuration
   */
  private setupFromGameConfig(): void {
    // Extract racing configuration from game config
    const gameConfig = this.game?.gameConfig?.[0];
    if (gameConfig) {
      this.racingConfig = {
        difficulty: (gameConfig.difficulty as 'EASY' | 'MEDIUM' | 'HARD') || 'MEDIUM',
        maxAttempts: gameConfig.frequencyAttempts || 3,
        carSpeed: 10,
        obstacleSpeed: 5,
        obstacleFrequency: 0.05,
        levels: 5
      };

      // Check if the game can be played
      if (this.racingGameId) {
        this.gameService.checkGameAvailability(this.racingGameId)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: (canPlay) => {
              this.canPlay = canPlay;
              if (!canPlay) {
                console.log('Game cannot be played, using demo mode');
                this.setupDemoMode();
              } else {
                console.log('Game can be played, initializing racing game');
                this.initializeGame();
              }
            },
            error: (error) => {
              console.error('Error checking game availability:', error);
              this.setupDemoMode();
            }
          });
      } else {
        this.setupDemoMode();
      }
    } else {
      this.setupDemoMode();
    }
  }

  /**
   * Setup demo mode with default configuration
   */
  private setupDemoMode(): void {
    this.demoMode = true;
    this.canPlay = true;
    this.attemptsRemaining = 3;

    // Set default configuration for demo mode
    this.racingConfig = {
      difficulty: 'MEDIUM',
      maxAttempts: 3,
      carSpeed: 10,
      obstacleSpeed: 5,
      obstacleFrequency: 0.05,
      levels: 5
    };

    // Initialize the game
    this.initializeGame();
  }

  /**
   * Initialize the game with current configuration
   */
  initializeGame() {
    // Wait for the view to be initialized
    setTimeout(() => {
      const gameArea = this.elementRef.nativeElement.querySelector('.game-area');
      if (gameArea) {
        this.gameAreaWidth = gameArea.clientWidth;
        this.gameAreaHeight = gameArea.clientHeight;

        // Set car position
        this.car.x = this.gameAreaWidth / 2 - this.car.width / 2;
        this.car.y = this.gameAreaHeight * 0.6 - this.car.height / 2;

        // Set car speed from config
        if (this.racingConfig) {
          this.car.speed = this.racingConfig.carSpeed;
        }

        // Reset game state
        this.score = 0;
        this.gameResult = null;
        this.obstacles = [];

        // Start the game loop
        this.startGameLoop();

        // Emit game start event
        this.emitGameEvent('start', 0);
      } else {
        console.error('Game area not found');
      }
    }, 100);
  }

  startGameLoop() {
    this.stopGameLoop();
    this.gameLoop = setInterval(() => {
      this.updateGame();
    }, 50); // Update every 50ms
  }

  stopGameLoop() {
    if (this.gameLoop) {
      clearInterval(this.gameLoop);
    }
  }

  updateGame() {
    // Don't update if game is over
    if (this.gameResult) return;

    // Increment score
    this.score++;

    // Increase difficulty based on score
    this.updateDifficulty();

    // Move and generate obstacles
    this.moveObstacles();
    this.generateObstacle();
    this.checkCollisions();
    this.removeOffscreenObstacles();

    // Force change detection
    this.cdr.detectChanges();
  }

  updateDifficulty() {
    // Increase level every 500 points
    const newLevel = Math.floor(this.score / 500) + 1;
    if (newLevel > this.level) {
      this.level = newLevel;

      // Emit level up event
      this.emitGameEvent('levelup', this.score);

      // Increase obstacle speed and frequency with level
      if (this.racingConfig) {
        this.racingConfig.obstacleSpeed = 5 + (this.level - 1);
        this.racingConfig.obstacleFrequency = 0.05 + (this.level - 1) * 0.01;
      }
    }
  }

  moveObstacles() {
    this.obstacles.forEach((obs) => {
      // Use obstacle speed from config if available
      const speed = this.racingConfig?.obstacleSpeed || 5;
      obs.y += speed; // Move obstacles down
    });
  }

  generateObstacle() {
    // Use obstacle frequency from config if available
    const frequency = this.racingConfig?.obstacleFrequency || 0.05;

    if (Math.random() < frequency) {
      // Generate an obstacle with random position
      this.obstacles.push({
        x: Math.random() * (this.gameAreaWidth - 32),
        y: -40,
        width: 32,
        height: 32,
        speed: this.racingConfig?.obstacleSpeed || 5
      });
    }
  }

  removeOffscreenObstacles() {
    this.obstacles = this.obstacles.filter(
      (obs) => obs.y < this.gameAreaHeight
    );
  }

  moveCar(dx: number, dy: number) {
    // Don't move car if game is over
    if (this.gameResult) return;

    // Use car speed from config if available
    const speed = this.car.speed;

    // Apply speed to movement
    const scaledDx = dx * (speed / 10);
    const scaledDy = dy * (speed / 10);

    // Keep car within game area bounds
    this.car.x = Math.max(
      0,
      Math.min(this.car.x + scaledDx, this.gameAreaWidth - this.car.width)
    );
    this.car.y = Math.max(
      0,
      Math.min(this.car.y + scaledDy, this.gameAreaHeight - this.car.height)
    );
  }

  @HostListener('window:keydown', ['$event'])
  onKeyDown(event: KeyboardEvent) {
    const speed = 10;
    switch (event.key) {
      case 'ArrowLeft':
        this.moveCar(-speed, 0);
        break;
      case 'ArrowRight':
        this.moveCar(speed, 0);
        break;
      case 'ArrowUp':
        this.moveCar(0, -speed);
        break;
      case 'ArrowDown':
        this.moveCar(0, speed);
        break;
    }
  }

  onScreenClick(event: MouseEvent) {
    const rect = (event.target as HTMLElement).getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    this.moveCarTowards(x, y);
  }

  onScreenTouch(event: TouchEvent) {
    const rect = (event.target as HTMLElement).getBoundingClientRect();
    const x = event.touches[0].clientX - rect.left;
    const y = event.touches[0].clientY - rect.top;
    this.moveCarTowards(x, y);
  }

  moveCarTowards(x: number, y: number) {
    const dx = x - (this.car.x + this.car.width / 2);
    const dy = y - (this.car.y + this.car.height / 2);
    const distance = Math.sqrt(dx * dx + dy * dy);
    const speed = 10;
    if (distance > speed) {
      this.moveCar((dx / distance) * speed, (dy / distance) * speed);
    } else {
      this.moveCar(dx, dy);
    }
  }

  checkCollisions() {
    for (const obs of this.obstacles) {
      if (this.isColliding(this.car, obs)) {
        this.handleCollision();
        break;
      }
    }
  }

  isColliding(
    rect1: { x: number; y: number; width: number; height: number },
    rect2: { x: number; y: number; width: number; height: number }
  ): boolean {
    return (
      rect1.x < rect2.x + rect2.width &&
      rect1.x + rect1.width > rect2.x &&
      rect1.y < rect2.y + rect2.height &&
      rect1.y + rect1.height > rect2.y
    );
  }

  handleCollision() {
    // Stop the game loop
    this.stopGameLoop();

    // Set game result to lose
    this.gameResult = 'lose';

    // Update high score if needed
    if (this.score > this.highScore) {
      this.highScore = this.score;
    }

    // Emit game over event
    this.emitGameEvent('lose', this.score);

    // Save score to API if not in demo mode
    if (!this.demoMode && this.racingGameId) {
      this.gameService.saveGameScore(this.racingGameId, this.score)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            console.log('Score saved:', response);
          },
          error: (error) => {
            console.error('Error saving score:', error);
          }
        });
    }

    // Decrease attempts remaining if not in demo mode
    if (!this.demoMode && this.attemptsRemaining > 0) {
      this.attemptsRemaining--;
    }
  }

  restartGame() {
    // Reset game state
    this.gameResult = null;

    // Initialize a new game
    this.initializeGame();
  }

  /**
   * Emit a game event to the parent component
   * @param state The state of the game (start, win, lose, etc.)
   * @param score The current score
   */
  private emitGameEvent(state: string, score: number): void {
    if (!this.demoMode && this.gameInstance?.id) {
      // Create a game event
      const event: GameEvent = {
        id: 0, // Will be assigned by the API
        level: this.level,
        score: score,
        duration: 0, // Not tracking duration for racing game
        state: state,
        payload: JSON.stringify({
          level: this.level,
          score: this.score,
          obstacles: this.obstacles.length
        })
      };

      // Emit the event
      this.gameEvent.emit(event);

      // Save the event to the API
      if (this.racingGameId && this.gameInstance?.id) {
        this.gameService.createGameEvent(
          this.racingGameId,
          this.gameInstance.id,
          event
        ).pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            console.log('Game event saved:', response);
          },
          error: (error) => {
            console.error('Error saving game event:', error);
          }
        });
      }
    } else {
      // In demo mode, just emit the event without saving to API
      const demoEvent: GameEvent = {
        id: Math.floor(Math.random() * 1000),
        level: this.level,
        score: score,
        duration: 0,
        state: state,
        payload: JSON.stringify({
          level: this.level,
          score: this.score,
          obstacles: this.obstacles.length
        })
      };

      this.gameEvent.emit(demoEvent);
    }
  }

  /**
   * Get score items for the games-score component
   */
  getScoreItems() {
    return [
      { title: 'Score', number: this.score },
      { title: 'Level', number: this.level },
      { title: 'High Score', number: this.highScore }
    ];
  }

  /**
   * Handle control clicks from the games-play-button component
   */
  handleControlClick(event: any) {
    // Convert the event to a string if it's not already
    const controlName = typeof event === 'string' ? event : event.toString();

    switch (controlName) {
      case 'restart':
        this.restartGame();
        break;
      default:
        break;
    }
  }

  initJoystick(event: MouseEvent | TouchEvent) {
    this.isJoystickActive = true;
    const rect = this.joystickArea.nativeElement.getBoundingClientRect();
    this.joystickCenter = {
      x: rect.left + rect.width / 2,
      y: rect.top + rect.height / 2,
    };
  }

  moveJoystick(event: MouseEvent | TouchEvent) {
    if (!this.isJoystickActive) return;

    event.preventDefault();

    const clientX =
      event instanceof MouseEvent ? event.clientX : event.touches[0].clientX;
    const clientY =
      event instanceof MouseEvent ? event.clientY : event.touches[0].clientY;

    let dx = clientX - this.joystickCenter.x;
    let dy = clientY - this.joystickCenter.y;

    // Limit the joystick movement to the maximum distance
    const distance = Math.sqrt(dx * dx + dy * dy);
    if (distance > this.maxJoystickDistance) {
      dx = (dx / distance) * this.maxJoystickDistance;
      dy = (dy / distance) * this.maxJoystickDistance;
    }

    // Update joystick visual position
    this.joystickPosition = {
      transform: `translate(calc(-50% + ${dx}px), calc(-50% + ${dy}px))`,
    };

    // Move the car based on joystick position
    const carSpeed = 5;
    this.moveCar(
      (dx / this.maxJoystickDistance) * carSpeed,
      (dy / this.maxJoystickDistance) * carSpeed
    );
  }

  stopJoystick() {
    this.isJoystickActive = false;
    this.joystickPosition = { transform: 'translate(-50%, -50%)' };
  }
}
