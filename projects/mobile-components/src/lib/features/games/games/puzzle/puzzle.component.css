/* Import shared game styles */
@import '../styles/game-styles.css';

:host {
  @apply block w-full h-full;
}

/* Demo Mode Indicator */
.demo-mode-indicator {
  @apply absolute top-2 right-2 px-2 py-1 text-xs font-bold text-white rounded-full z-20;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

/* Game Board */
.game-board {
  @apply p-4 rounded-xl bg-slate-700/50 border border-slate-600 shadow-xl;
  position: relative;
  overflow: hidden;
  max-width: 100%;
  margin: 0 auto;
}

.game-board::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10;
  filter: blur(20px);
  z-index: 0;
}

/* Puzzle Grid */
.puzzle-grid {
  @apply grid gap-2 relative z-10;
  max-width: 400px;
  margin: 0 auto;
}

/* Puzzle Tiles */
.puzzle-tile {
  @apply flex justify-center items-center font-bold text-white rounded-lg shadow-md transition-all duration-300 cursor-pointer;
  background: linear-gradient(135deg, #4f46e5, #6366f1);
  aspect-ratio: 1;
  position: relative;
  overflow: hidden;
  min-width: 3rem;
  min-height: 3rem;
}

.puzzle-tile::before {
  content: '';
  @apply absolute inset-0 opacity-30;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
}

.puzzle-tile:hover {
  @apply transform scale-105;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
}

/* Empty Tile */
.empty-tile {
  @apply rounded-lg shadow-inner;
  background: rgba(30, 41, 59, 0.5);
  aspect-ratio: 1;
  min-width: 3rem;
  min-height: 3rem;
}

/* Game Controls */
.game-controls {
  @apply w-full max-w-md px-4 mx-auto;
}

/* Shake animation */
@keyframes shake {
  0% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  50% { transform: translateX(5px); }
  75% { transform: translateX(-5px); }
  100% { transform: translateX(0); }
}

.invalid-move {
  animation: shake 0.3s;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .puzzle-tile {
    @apply text-base;
    min-width: 2.5rem;
    min-height: 2.5rem;
  }

  .empty-tile {
    min-width: 2.5rem;
    min-height: 2.5rem;
  }
}
