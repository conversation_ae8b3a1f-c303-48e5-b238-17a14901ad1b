import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, Input, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription, timer } from 'rxjs';
import { GameControl, GamesPlayButtonComponent } from '../components/games-play-button/games-play-button.component';
import { WinLoseOverlayComponent } from '../components/win-lose-overlay/win-lose-overlay.component';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { Game, GameEvent } from 'lp-client-api';
import { GameService } from 'lp-client-api';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';

interface PuzzleConfig {
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  maxAttempts: number;
  timeLimit: number;
  levels: number;
  startLevel?: number;
}

@Component({
  selector: 'lib-puzzle',
  templateUrl: './puzzle.component.html',
  styleUrls: ['./puzzle.component.css'],
  standalone: true,
  imports: [CommonModule, WinLoseOverlayComponent, GamesPlayButtonComponent, GamesScoreComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class PuzzleComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Input properties
  @Input() gameId?: string;
  @Input() game?: Game;
  @Input() gameInstance?: any;
  @Input() config: any = {};

  // Output events
  @Output() gameEvent = new EventEmitter<GameEvent>();

  // Game state
  tiles: number[] = [];
  moveCount: number = 0;
  currentLevel: number = 1;
  score: number = 0;
  puzzleSize: number = 3; // Starting with a 3x3 puzzle
  timeLimit: number = 100; // seconds
  timeLeft: number = this.timeLimit;
  timerSubscription: Subscription | null = null;
  isAnimating: boolean = false;
  gameResult: 'win' | 'lose' | null = null;
  highScore: number = 0;

  // Game configuration
  puzzleGameId = 0; // Will be set from input or default
  puzzleConfig: PuzzleConfig | undefined;
  canPlay: boolean = false;
  attemptsRemaining: number = 3; // Default attempts
  demoMode: boolean = false; // Demo mode flag

  gameControls: GameControl[] = [
    { name: 'start', label: 'Reset', icon: 'refresh-outline' },
  ];

  constructor(
    private gameService: GameService,
    private cdr: ChangeDetectorRef
  ) {}

  // Handle control clicks from the games-play-button component
  handleControlClick(event: any) {
    // Convert the event to a string if it's not already
    const controlName = typeof event === 'string' ? event : event.toString();

    switch (controlName) {
      case 'start':
      case 'restart':
        this.initializePuzzle();
        break;
      case 'newGame':
        this.gameResult = null;
        this.currentLevel = 1;
        this.score = 0;
        this.initializePuzzle();
        break;
      default:
        break;
    }
  }

  ngOnInit() {
    console.log('Puzzle component initialized');

    // Check if we have a game ID from input
    if (this.game?.id) {
      this.puzzleGameId = this.game.id;
      console.log('Using game ID from input:', this.puzzleGameId);
    }

    // Try to load game config from API
    this.loadGameConfig();
  }

  ngOnDestroy() {
    this.timerSubscription?.unsubscribe();
    this.destroy$.next();
    this.destroy$.complete();
  }

  restartGame() {
    this.gameResult = null;
    this.initializePuzzle();

    // Emit game restart event
    this.emitGameEvent('restart', 0);
  }

  /**
   * Load game configuration from API or use demo mode
   */
  private loadGameConfig(): void {
    // If we have a game from input, use its configuration
    if (this.game) {
      console.log('Using game configuration from input:', this.game);
      this.setupFromGameConfig();
      return;
    }

    // Otherwise, use demo mode
    console.log('No game configuration from input, using demo mode');
    this.setupDemoMode();
  }

  /**
   * Setup game from API configuration
   */
  private setupFromGameConfig(): void {
    // Extract puzzle configuration from game config
    const gameConfig = this.game?.gameConfig?.[0];
    if (gameConfig) {
      this.puzzleConfig = {
        difficulty: (gameConfig.difficulty as 'EASY' | 'MEDIUM' | 'HARD') || 'MEDIUM',
        maxAttempts: gameConfig.frequencyAttempts || 3,
        timeLimit: 100,
        levels: 10
      };

      // Check if the game can be played
      if (this.puzzleGameId) {
        this.gameService.checkGameAvailability(this.puzzleGameId)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: (canPlay) => {
              this.canPlay = canPlay;
              if (!canPlay) {
                console.log('Game cannot be played, using demo mode');
                this.setupDemoMode();
              } else {
                console.log('Game can be played, initializing puzzle');
                this.initializePuzzle();
              }
            },
            error: (error) => {
              console.error('Error checking game availability:', error);
              this.setupDemoMode();
            }
          });
      } else {
        this.setupDemoMode();
      }
    } else {
      this.setupDemoMode();
    }
  }

  /**
   * Setup demo mode with default configuration
   */
  private setupDemoMode(): void {
    this.demoMode = true;
    this.canPlay = true;
    this.attemptsRemaining = 3;

    // Set default configuration for demo mode
    this.puzzleConfig = {
      difficulty: 'MEDIUM',
      maxAttempts: 3,
      timeLimit: 100,
      levels: 10
    };

    // Initialize the puzzle
    this.initializePuzzle();
  }

  initializePuzzle() {
    // Initialize or reset the puzzle based on the current level
    this.puzzleSize = 3 + this.currentLevel - 1; // Increase puzzle size with level
    this.tiles = this.generateShuffledTiles(this.puzzleSize);
    this.moveCount = 0;

    // Use configuration from puzzleConfig if available
    if (this.puzzleConfig) {
      this.timeLimit = this.puzzleConfig.timeLimit;
    }

    this.timeLeft = this.timeLimit - (this.currentLevel - 1) * 5; // Decrease time limit per level
    this.gameResult = null;

    // Start the timer
    this.startTimer();

    // Emit game start event
    this.emitGameEvent('start', 0);

    // Force change detection
    this.cdr.detectChanges();
  }

  startTimer() {
    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe();
    }
    this.timerSubscription = timer(0, 1000).subscribe((seconds) => {
      this.timeLeft = this.timeLimit - seconds - (this.currentLevel - 1) * 5;
      if (this.timeLeft <= 0) {
        this.timerSubscription?.unsubscribe();
        // Handle time out (e.g., reset level or end game)
        this.gameResult = 'lose';

        // Emit game over event
        this.emitGameEvent('lose', this.score);

        // Reset the game
        this.currentLevel = 1;
        this.score = 0;

        // If not in demo mode, decrease attempts
        if (!this.demoMode && this.attemptsRemaining > 0) {
          this.attemptsRemaining--;
        }

        // Initialize a new puzzle
        this.initializePuzzle();
      }
    });
  }

  /**
   * Emit a game event to the parent component
   * @param state The state of the game (start, win, lose, etc.)
   * @param score The current score
   */
  private emitGameEvent(state: string, score: number): void {
    if (!this.demoMode && this.gameInstance?.id) {
      // Create a game event
      const event: GameEvent = {
        id: 0, // Will be assigned by the API
        level: this.currentLevel,
        score: score,
        duration: this.timeLimit - this.timeLeft,
        state: state,
        payload: JSON.stringify({
          puzzleSize: this.puzzleSize,
          moveCount: this.moveCount,
          timeLeft: this.timeLeft
        })
      };

      // Emit the event
      this.gameEvent.emit(event);

      // Save the event to the API
      if (this.puzzleGameId && this.gameInstance?.id) {
        this.gameService.createGameEvent(
          this.puzzleGameId,
          this.gameInstance.id,
          event
        ).pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            console.log('Game event saved:', response);
          },
          error: (error) => {
            console.error('Error saving game event:', error);
          }
        });
      }
    } else {
      // In demo mode, just emit the event without saving to API
      const demoEvent: GameEvent = {
        id: Math.floor(Math.random() * 1000),
        level: this.currentLevel,
        score: score,
        duration: this.timeLimit - this.timeLeft,
        state: state,
        payload: JSON.stringify({
          puzzleSize: this.puzzleSize,
          moveCount: this.moveCount,
          timeLeft: this.timeLeft
        })
      };

      this.gameEvent.emit(demoEvent);
    }
  }

  generateShuffledTiles(size: number): number[] {
    const totalTiles = size * size;
    let tiles = Array.from({ length: totalTiles }, (_, i) => i);
    // Shuffle tiles
    do {
      for (let i = tiles.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [tiles[i], tiles[j]] = [tiles[j], tiles[i]];
      }
    } while (!this.isSolvable(tiles, size) || this.isSolvedArray(tiles));
    return tiles;
  }

  // Check if the puzzle is solvable
  isSolvable(tiles: number[], size: number): boolean {
    let inversions = 0;
    for (let i = 0; i < tiles.length; i++) {
      for (let j = i + 1; j < tiles.length; j++) {
        if (tiles[i] !== 0 && tiles[j] !== 0 && tiles[i] > tiles[j]) {
          inversions++;
        }
      }
    }

    if (size % 2 !== 0) {
      return inversions % 2 === 0;
    } else {
      const emptyRow = Math.floor(tiles.indexOf(0) / size);
      if (
        (emptyRow % 2 === 0 && inversions % 2 !== 0) ||
        (emptyRow % 2 !== 0 && inversions % 2 === 0)
      ) {
        return true;
      }
      return false;
    }
  }

  // Check if the tiles array is already solved
  isSolvedArray(tiles: number[]): boolean {
    for (let i = 0; i < tiles.length - 1; i++) {
      if (tiles[i] !== i + 1) {
        return false;
      }
    }
    return tiles[tiles.length - 1] === 0;
  }

  moveTile(index: number) {
    // Don't allow moves if the game is over or animating
    if (this.isAnimating || this.gameResult) return;

    // Don't allow moves if not in demo mode and can't play
    if (!this.demoMode && (!this.canPlay || this.attemptsRemaining <= 0)) {
      console.log('Cannot play: demo mode =', this.demoMode, 'canPlay =', this.canPlay, 'attemptsRemaining =', this.attemptsRemaining);
      return;
    }

    const emptyIndex = this.tiles.indexOf(0);
    const rowClicked = Math.floor(index / this.puzzleSize);
    const colClicked = index % this.puzzleSize;
    const rowEmpty = Math.floor(emptyIndex / this.puzzleSize);
    const colEmpty = emptyIndex % this.puzzleSize;

    const isAdjacent =
      (Math.abs(rowClicked - rowEmpty) === 1 && colClicked === colEmpty) ||
      (Math.abs(colClicked - colEmpty) === 1 && rowClicked === rowEmpty);

    if (isAdjacent) {
      this.isAnimating = true;
      // Swap the clicked tile with the empty tile
      this.tiles[emptyIndex] = this.tiles[index];
      this.tiles[index] = 0;
      this.moveCount++;

      setTimeout(() => {
        if (this.isSolved()) {
          this.updateScore();
          this.gameResult = 'win';

          // Emit win event
          this.emitGameEvent('win', this.score);

          // Advance to the next level
          this.advanceLevel();
        }
        this.isAnimating = false;
      }, 300); // Duration should match the CSS transition duration
    } else {
      // Provide shake animation for invalid move
      const tileElement = document.getElementById(`tile-${index}`);
      if (tileElement) {
        tileElement.classList.add('invalid-move');
        setTimeout(() => {
          tileElement.classList.remove('invalid-move');
        }, 300);
      }
      console.log('Invalid move. Tile not adjacent to empty space.');
    }
  }

  isSolved(): boolean {
    for (let i = 0; i < this.tiles.length - 1; i++) {
      if (this.tiles[i] !== i + 1) {
        return false;
      }
    }
    return this.tiles[this.tiles.length - 1] === 0;
  }

  updateScore() {
    // Example scoring: more points for fewer moves and more time left
    const timeBonus = this.timeLeft > 0 ? this.timeLeft : 0;
    const points = Math.max(100 - this.moveCount, 10) + timeBonus;
    this.score += points;

    // Update high score if needed
    if (this.score > this.highScore) {
      this.highScore = this.score;
    }

    // Save score to API if not in demo mode
    if (!this.demoMode && this.puzzleGameId) {
      this.gameService.saveGameScore(this.puzzleGameId, this.score)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            console.log('Score saved:', response);
          },
          error: (error) => {
            console.error('Error saving score:', error);
          }
        });
    }
  }

  advanceLevel() {
    this.currentLevel++;

    // Get max levels from config or use default
    const maxLevels = this.puzzleConfig?.levels || 10;

    // Check if we've reached the maximum level
    if (this.currentLevel > maxLevels) {
      // Game completed
      this.gameResult = 'win';

      // Emit game complete event
      this.emitGameEvent('complete', this.score);

      // Reset game for a new round
      this.currentLevel = 1;

      // If not in demo mode, decrease attempts
      if (!this.demoMode && this.attemptsRemaining > 0) {
        this.attemptsRemaining--;
      }

      // Reset score only if we're starting a new game
      this.score = 0;

      // Initialize a new puzzle
      this.initializePuzzle();
    } else {
      // Continue to the next level
      this.initializePuzzle();

      // Emit level up event
      this.emitGameEvent('levelup', this.score);
    }
  }


}
