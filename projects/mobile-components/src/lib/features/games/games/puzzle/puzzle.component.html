<div class="flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800">
  <!-- Demo Mode Indicator -->
  <div *ngIf="demoMode" class="demo-mode-indicator">
    Demo Mode
  </div>

  <!-- Game Title -->
  <div class="text-center mb-1">
    <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500">Puzzle</h1>
    <p class="text-xs text-slate-400 mb-1">Slide the tiles to solve the puzzle</p>
  </div>

  <!-- Game Score -->
  <games-score
    [scores]="[
      { title: 'Score', number: score },
      { title: 'Level', number: currentLevel },
      { title: 'Moves', number: moveCount },
      { title: 'Time', number: timeLeft }
    ]"
  ></games-score>

  <!-- Game Board -->
  <div class="flex overflow-hidden flex-col flex-grow items-center p-2">
    <div class="game-board">
      <div
        class="puzzle-grid"
        [ngClass]="{
          'grid-cols-3': puzzleSize === 3,
          'grid-cols-4': puzzleSize === 4,
          'grid-cols-5': puzzleSize === 5
        }"
      >
        <ng-container *ngFor="let tile of tiles; let i = index">
          <div
            *ngIf="tile !== 0"
            (click)="moveTile(i)"
            id="tile-{{ i }}"
            [ngClass]="{ 'invalid-move': false }"
            class="puzzle-tile"
          >
            {{ tile }}
          </div>
          <div
            *ngIf="tile === 0"
            class="empty-tile"
          ></div>
        </ng-container>
      </div>
    </div>
  </div>

  <!-- Game Controls -->
  <div class="game-controls mt-2">
    <lib-games-play-button
      [controls]="gameControls"
      (controlClicked)="handleControlClick($event)"
    ></lib-games-play-button>
  </div>

  <!-- Game Over Dialog -->
  <lib-win-lose-overlay
    [status]="gameResult"
    [message]="
      gameResult
        ? gameResult === 'win'
          ? '🎉 Congratulations! You solved the puzzle! 🎉'
          : 'Better luck next time!'
        : ''
    "
    [score]="score"
    [highScore]="highScore"
    [level]="currentLevel"
    [moves]="moveCount"
    (restart)="restartGame()"
  ></lib-win-lose-overlay>
</div>
