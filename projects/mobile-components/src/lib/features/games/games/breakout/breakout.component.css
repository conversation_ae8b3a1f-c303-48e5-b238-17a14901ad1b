@import '../styles/game-styles.css';

.game-container {
  position: relative;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  overflow: hidden;
}

.responsive-canvas {
  width: 100%;
  height: 350px; /* Fixed height for better visibility */
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

/* Button animations */
button {
  transition: all 0.2s ease;
}

button:active {
  transform: scale(0.95);
}

/* Paddle and ball animations */
@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
}

@media (max-width: 600px) {
  .game-container {
    max-width: 100%;
  }
}
