<div [ngClass]="containerClasses">
  <!-- Game Title -->
  <h1 class="text-3xl font-bold text-center py-2 text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-600"
      [ngClass]="{ 'text-lg': largeText, 'text-3xl': !largeText }">
    Breakout
  </h1>

  <!-- Demo Mode Indicator -->
  <div *ngIf="isDemoMode" 
       class="absolute top-2 right-2 px-3 py-1 bg-yellow-600 text-white text-sm rounded-full"
       role="status"
       aria-label="Demo mode active">
    Demo Mode
  </div>

  <!-- Paused State Indicator -->
  <div *ngIf="gameState === 'paused'" 
       class="absolute top-2 left-2 px-3 py-1 bg-orange-600 text-white text-sm rounded-full"
       role="status"
       aria-label="Game paused">
    Paused
  </div>

  <!-- Score Display -->
  <div class="w-full px-4 mb-2">
    <games-score [scores]="getScoreItems()"></games-score>
  </div>

  <!-- Game Canvas -->
  <div class="w-full px-4" role="main" aria-label="Breakout game area">
    <canvas
      #gameCanvas
      class="responsive-canvas border-2 border-blue-500 rounded-lg shadow-lg bg-black/30 mx-auto"
      [width]="effectiveCanvasWidth"
      [height]="effectiveCanvasHeight"
      [attr.aria-label]="'Breakout game canvas, score: ' + score + ', level: ' + level"
      role="img"
    ></canvas>
  </div>

  <!-- Pause/Resume Button -->
  <div class="w-full px-4 mb-2 flex justify-center" *ngIf="allowPause && !gameOver && !gameWon">
    <button 
      (click)="togglePause()"
      class="px-4 py-2 rounded-lg font-medium text-sm transition-colors"
      [ngClass]="{
        'bg-yellow-600 hover:bg-yellow-700 text-white': gameState === 'playing',
        'bg-green-600 hover:bg-green-700 text-white': gameState === 'paused'
      }"
      [attr.aria-label]="gameState === 'playing' ? 'Pause game' : 'Resume game'">
      {{ gameState === 'playing' ? 'Pause' : 'Resume' }}
    </button>
  </div>

  <!-- Game Controls -->
  <div class="w-full px-4 mb-4">
    <lib-games-play-button
      [controls]="gameControls"
      (controlClick)="handleControlClick($event)"
    ></lib-games-play-button>
  </div>

  <!-- Control Instructions -->
  <div class="w-full px-4 mb-4 text-center" 
       role="region" 
       aria-label="Game controls">
    <p class="text-xs text-slate-400" *ngIf="!keyboardControls">
      <span class="text-blue-400">Touch</span> <span class="text-slate-300">the buttons to move paddle</span>
    </p>
    <p class="text-xs text-slate-400" *ngIf="keyboardControls">
      <span class="text-blue-400">← →</span> <span class="text-slate-300">Arrow keys or</span>
      <span class="text-blue-400">A D</span> <span class="text-slate-300">to move paddle</span> |
      <span class="text-blue-400">Space</span> <span class="text-slate-300">to pause</span>
    </p>
  </div>

  <!-- Control Buttons -->
  <div class="flex justify-center space-x-8 mb-6 z-10 relative" *ngIf="!keyboardControls">
    <button
      (touchstart)="movePaddle('left')"
      (touchend)="stopPaddle()"
      (mousedown)="movePaddle('left')"
      (mouseup)="stopPaddle()"
      class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-700 text-white rounded-full flex items-center justify-center text-2xl shadow-lg shadow-blue-500/30 transition-transform hover:scale-105 active:scale-95"
      [disabled]="gameState !== 'playing'"
      aria-label="Move paddle left"
      role="button"
    >
      ←
    </button>
    <button
      (touchstart)="movePaddle('right')"
      (touchend)="stopPaddle()"
      (mousedown)="movePaddle('right')"
      (mouseup)="stopPaddle()"
      class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-700 text-white rounded-full flex items-center justify-center text-2xl shadow-lg shadow-blue-500/30 transition-transform hover:scale-105 active:scale-95"
      [disabled]="gameState !== 'playing'"
      aria-label="Move paddle right"
      role="button"
    >
      →
    </button>
  </div>

  <!-- Game Over Overlay -->
  <lib-win-lose-overlay
    class="z-10"
    [status]="gameOver ? (gameWon ? 'win' : 'lose') : null"
    [message]="
      gameOver
        ? gameWon
          ? 'Congratulations!'
          : 'Better luck next time!'
        : ''
    "
    (restart)="restartGame()"
    (exit)="handleControlClick('newGame')"
  ></lib-win-lose-overlay>
</div>
