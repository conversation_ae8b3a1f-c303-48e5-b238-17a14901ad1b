import {
  <PERSON>mpo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ViewChild,
  ElementRef,
  HostListener,
  Input,
  Output,
  EventEmitter,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
  ChangeDetectorRef
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Game } from 'lp-client-api';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { GameControl, GamesPlayButtonComponent } from '../components/games-play-button/games-play-button.component';
import { WinLoseOverlayComponent } from '../components/win-lose-overlay/win-lose-overlay.component';

// Enhanced interfaces for game events
interface GameStartEvent {
  gameId: string;
  timestamp: Date;
  config: BreakoutConfig;
}

interface GameEndEvent {
  gameId: string;
  timestamp: Date;
  score: number;
  level: number;
  duration: number;
  result: 'win' | 'lose' | 'quit';
}

interface GameScoreEvent {
  gameId: string;
  timestamp: Date;
  score: number;
  previousScore: number;
  level: number;
}

interface GamePauseEvent {
  gameId: string;
  timestamp: Date;
  currentState: 'paused' | 'resumed';
}

interface GameErrorEvent {
  gameId: string;
  timestamp: Date;
  error: string;
  context?: any;
}

interface GameLevelEvent {
  gameId: string;
  timestamp: Date;
  level: number;
  previousLevel: number;
}

interface GameAchievementEvent {
  gameId: string;
  timestamp: Date;
  achievement: string;
  points: number;
}

interface Brick {
  x: number;
  y: number;
  width: number;
  height: number;
  status: boolean;
  color: string;
  points: number;
}

interface BreakoutConfig {
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  maxAttempts: number;
  paddle: {
    width: number;
    height: number;
    speed: number;
  };
  ball: {
    radius: number;
    initialSpeed: number;
    speedIncrement: number;
  };
  bricks: {
    rows: number;
    columns: number;
    padding: number;
    offsetTop: number;
    offsetLeft: number;
    width?: number;
    height?: number;
  };
  levels: number;
}

interface BreakoutGameEvent {
  type: string;
  data: any;
  timestamp: string;
  gameId: string;
}

@Component({
  selector: 'lib-breakout',
  templateUrl: './breakout.component.html',
  styleUrls: ['./breakout.component.css'],
  standalone: true,
  imports: [CommonModule, GamesScoreComponent, GamesPlayButtonComponent, WinLoseOverlayComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class BreakoutComponent implements OnInit, OnDestroy {
  
  // === STANDARD GAME INPUTS ===
  // Base styling and configuration
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'accent' = 'default';
  @Input() theme: 'light' | 'dark' | 'auto' = 'auto';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Game mechanics
  @Input() difficulty: 'easy' | 'medium' | 'hard' | 'expert' = 'medium';
  @Input() timeLimit: number = 0; // 0 = no limit
  @Input() lives: number = 3;
  @Input() maxLives: number = 5;

  // Scoring and rewards
  @Input() targetScore: number = 0;
  @Input() pointsMultiplier: number = 1;
  @Input() rewardPoints: number = 10;
  @Input() bonusPoints: number = 0;

  // Game state management
  @Input() gameState: 'idle' | 'playing' | 'paused' | 'completed' | 'failed' = 'idle';
  @Input() autoStart: boolean = false;
  @Input() autoReset: boolean = false;
  @Input() saveProgress: boolean = true;
  @Input() allowPause: boolean = true;

  // Sound and effects
  @Input() soundEnabled: boolean = true;
  @Input() effectsEnabled: boolean = true;
  @Input() musicEnabled: boolean = false;
  @Input() vibrationEnabled: boolean = true;

  // Performance settings
  @Input() frameRate: number = 60;
  @Input() enableOptimizations: boolean = true;
  @Input() reducedMotion: boolean = false;

  // Accessibility
  @Input() keyboardControls: boolean = true;
  @Input() screenReaderSupport: boolean = true;
  @Input() highContrast: boolean = false;
  @Input() largeText: boolean = false;

  // === GAME-SPECIFIC INPUTS ===
  @Input() paddleWidth: number = 75;
  @Input() paddleSpeed: number = 7;
  @Input() ballSpeed: number = 4;
  @Input() brickRows: number = 5;
  @Input() brickColumns: number = 9;
  @Input() ballRadius: number = 8;
  @Input() customCanvasWidth: number = 400;
  @Input() customCanvasHeight: number = 400;

  // === GAME EVENT OUTPUTS ===
  @Output() gameStart = new EventEmitter<GameStartEvent>();
  @Output() gameEnd = new EventEmitter<GameEndEvent>();
  @Output() gameScore = new EventEmitter<GameScoreEvent>();
  @Output() gamePause = new EventEmitter<GamePauseEvent>();
  @Output() levelChange = new EventEmitter<GameLevelEvent>();
  @Output() gameAchievement = new EventEmitter<GameAchievementEvent>();
  @Output() gameError = new EventEmitter<GameErrorEvent>();

  // Game-specific events
  @Output() brickDestroyed = new EventEmitter<{position: {x: number, y: number}, points: number}>();
  @Output() paddleHit = new EventEmitter<{position: number}>();
  @Output() ballLost = new EventEmitter<{livesRemaining: number}>();
  @Output() levelComplete = new EventEmitter<{level: number, score: number}>();

  // Legacy inputs for backward compatibility
  @Input() gameId: string = '';
  @Input() config: BreakoutConfig = {
    difficulty: 'MEDIUM',
    maxAttempts: 3,
    paddle: {
      width: 75,
      height: 10,
      speed: 7
    },
    ball: {
      radius: 8,
      initialSpeed: 4,
      speedIncrement: 0.5
    },
    bricks: {
      rows: 5,
      columns: 9,
      padding: 10,
      offsetTop: 60,
      offsetLeft: 30
    },
    levels: 5
  };
  @Input() gameInstance: any = null;
  @Input() game?: Game;

  @Output() gameEventEmitter = new EventEmitter<BreakoutGameEvent>();

  @ViewChild('gameCanvas', { static: true })
  gameCanvas!: ElementRef<HTMLCanvasElement>;
  ctx!: CanvasRenderingContext2D;
  canvasWidth: number = 400;
  canvasHeight: number = 400;
  router: Router = new Router();
  private startTime: number = 0;

  // Computed styling properties
  get containerClasses(): string {
    const baseClasses = 'flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800';
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm', 
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };
    const variantClasses = {
      default: 'from-slate-900 to-slate-800',
      primary: 'from-blue-900 to-blue-800',
      secondary: 'from-gray-900 to-gray-800', 
      accent: 'from-purple-900 to-purple-800'
    };
    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-lg',
      lg: 'rounded-xl',
      full: 'rounded-full'
    };
    
    return `${baseClasses} ${sizeClasses[this.size]} ${variantClasses[this.variant]} ${roundedClasses[this.rounded]} ${this.className}`.trim();
  }

  get effectivePaddleWidth(): number {
    return this.paddleWidth || this.config?.paddle?.width || 75;
  }

  get effectivePaddleSpeed(): number {
    return this.paddleSpeed || this.config?.paddle?.speed || 7;
  }

  get effectiveBallSpeed(): number {
    return this.ballSpeed || this.config?.ball?.initialSpeed || 4;
  }

  get effectiveBallRadius(): number {
    return this.ballRadius || this.config?.ball?.radius || 8;
  }

  get effectiveBrickRows(): number {
    return this.brickRows || this.config?.bricks?.rows || 5;
  }

  get effectiveBrickColumns(): number {
    return this.brickColumns || this.config?.bricks?.columns || 9;
  }

  get effectiveCanvasWidth(): number {
    return this.customCanvasWidth || 400;
  }

  get effectiveCanvasHeight(): number {
    return this.customCanvasHeight || 400;
  }

  // Game state
  paddle = {
    x: 175,
    y: 320, // Adjusted for better visibility
    width: 75,
    height: 10,
    speed: 7,
    dx: 0,
  };

  ball = {
    x: 200,
    y: 250, // Adjusted for better visibility
    radius: 8,
    speed: 4,
    dx: 4,
    dy: -4,
  };

  bricks: Brick[][] = [];
  brickColors = ['#FF5733', '#33FF57', '#3357FF', '#F3FF33', '#FF33F3'];
  brickPoints = [1, 2, 3, 5, 7];

  score: number = 0;
  level: number = 1;
  maxLevel: number = 5;
  gameOver: boolean = false;
  gameWon: boolean = false;
  attemptsRemaining: number = 3;
  isDemoMode: boolean = false;
  errorMessage: string = '';
  animationFrameId: number = 0;

  // Game controls configuration
  gameControls: GameControl[] = [
    { name: 'restart', icon: 'refresh-outline', label: 'Restart' },
    { name: 'newGame', icon: 'add-circle-outline', label: 'New Game' }
  ];

  constructor(private cdr: ChangeDetectorRef) {}

  @HostListener('window:keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    if (!this.keyboardControls || this.gameState !== 'playing') return;

    const key = event.key.toLowerCase();

    switch (key) {
      case 'arrowleft':
      case 'a':
        this.paddle.dx = -this.effectivePaddleSpeed;
        event.preventDefault();
        break;
      case 'arrowright': 
      case 'd':
        this.paddle.dx = this.effectivePaddleSpeed;
        event.preventDefault();
        break;
      case ' ':
        if (this.allowPause) {
          this.togglePause();
          event.preventDefault();
        }
        break;
      case 'r':
        if (event.ctrlKey || event.metaKey) return; // Don't interfere with browser refresh
        this.restartGame();
        event.preventDefault();
        break;
    }
  }

  @HostListener('window:keyup', ['$event'])
  onKeyUp(event: KeyboardEvent): void {
    if (!this.keyboardControls) return;

    const key = event.key.toLowerCase();

    switch (key) {
      case 'arrowleft':
      case 'a':
      case 'arrowright':
      case 'd':
        this.paddle.dx = 0;
        event.preventDefault();
        break;
    }
  }

  public togglePause(): void {
    if (!this.allowPause) return;
    
    if (this.gameState === 'playing') {
      this.gameState = 'paused';
      if (this.animationFrameId) {
        cancelAnimationFrame(this.animationFrameId);
        this.animationFrameId = 0;
      }
      this.gamePause.emit({
        gameId: this.gameId || 'game-breakout',
        timestamp: new Date(),
        currentState: 'paused'
      });
    } else if (this.gameState === 'paused') {
      this.gameState = 'playing';
      this.startGameLoop();
      this.gamePause.emit({
        gameId: this.gameId || 'game-breakout',
        timestamp: new Date(),
        currentState: 'resumed'
      });
    }
  }

  ngOnInit() {
    try {
      this.startTime = Date.now();
      
      // Check if we have a valid configuration from the input
      if (this.config && this.config.difficulty) {
        console.log('Initializing Breakout game with config:', this.config);

        // Apply configuration using effective properties
        this.paddle.width = this.effectivePaddleWidth;
        this.paddle.height = this.config.paddle.height;
        this.paddle.speed = this.effectivePaddleSpeed;

        this.ball.radius = this.effectiveBallRadius;
        this.ball.speed = this.effectiveBallSpeed;

        this.maxLevel = this.config.levels;
        this.attemptsRemaining = this.config.maxAttempts;
      } else {
        console.warn('No valid config provided, using default config');
        this.initializeDemoMode();
      }

      const canvas = this.gameCanvas.nativeElement;
      this.ctx = canvas.getContext('2d') as CanvasRenderingContext2D;

      // Use effective canvas dimensions
      this.canvasWidth = this.effectiveCanvasWidth;
      this.canvasHeight = this.effectiveCanvasHeight;

      this.initializeCanvasSize();
      this.initializeBricks();
      this.drawPaddle();
      this.drawBall();
      this.drawBricks();
      this.setRandomInitialDirection();

      // Emit game start event
      this.gameStart.emit({
        gameId: this.gameId || 'game-breakout',
        timestamp: new Date(),
        config: this.config
      });

      // Auto-start if enabled
      if (this.autoStart) {
        this.startGameLoop();
      }

      // Emit legacy game start event
      this.emitGameEvent('start', {
        difficulty: this.config.difficulty,
        level: this.level,
        isDemoMode: this.isDemoMode
      });
    } catch (error) {
      console.error('Error initializing Breakout game:', error);
      this.gameError.emit({
        gameId: this.gameId || 'game-breakout',
        timestamp: new Date(),
        error: error instanceof Error ? error.message : 'Unknown error',
        context: { method: 'ngOnInit' }
      });
      this.initializeDemoMode();
    }
  }

  startGameLoop(): void {
    if (this.gameState !== 'playing') {
      this.gameState = 'playing';
    }
    // Start game loop
    this.animationFrameId = requestAnimationFrame(() => this.gameLoop());
  }

  ngOnDestroy() {
    // Cancel animation frame to prevent memory leaks
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
    }
  }

  initializeDemoMode() {
    console.log('Initializing Breakout game in demo mode');
    this.isDemoMode = true;
    this.errorMessage = 'Playing in demo mode';

    // Set default configuration
    this.config = {
      difficulty: 'MEDIUM',
      maxAttempts: 3,
      paddle: {
        width: 75,
        height: 10,
        speed: 7
      },
      ball: {
        radius: 8,
        initialSpeed: 4,
        speedIncrement: 0.5
      },
      bricks: {
        rows: 5,
        columns: 9,
        padding: 10,
        offsetTop: 60,
        offsetLeft: 30
      },
      levels: 5
    };

    this.attemptsRemaining = this.config.maxAttempts;

    // Apply configuration to game objects
    this.paddle.width = this.config.paddle.width;
    this.paddle.height = this.config.paddle.height;
    this.paddle.speed = this.config.paddle.speed;

    this.ball.radius = this.config.ball.radius;
    this.ball.speed = this.config.ball.initialSpeed;

    this.maxLevel = this.config.levels;
  }

  initializeCanvasSize() {
    // Dynamically set canvas size based on container
    const container = this.gameCanvas.nativeElement.parentElement!;
    this.canvasWidth = container.clientWidth;
    this.canvasHeight = 350; // Fixed height for better visibility

    this.gameCanvas.nativeElement.width = this.canvasWidth;
    this.gameCanvas.nativeElement.height = this.canvasHeight;

    // Move paddle closer to bottom
    this.paddle.y = this.canvasHeight - this.paddle.height - 15;
    this.paddle.x = (this.canvasWidth - this.paddle.width) / 2;

    // Reset ball position
    this.ball.x = this.canvasWidth / 2;
    this.ball.y = this.canvasHeight / 2;
  }

  initializeBricks() {
    const brickWidth = (this.canvasWidth - this.config.bricks.offsetLeft * 2 -
                        this.config.bricks.padding * (this.config.bricks.columns - 1)) /
                        this.config.bricks.columns;
    const brickHeight = 20;

    this.bricks = [];

    for (let r = 0; r < this.config.bricks.rows; r++) {
      this.bricks[r] = [];
      for (let c = 0; c < this.config.bricks.columns; c++) {
        const brickX = c * (brickWidth + this.config.bricks.padding) + this.config.bricks.offsetLeft;
        const brickY = r * (brickHeight + this.config.bricks.padding) + this.config.bricks.offsetTop;

        this.bricks[r][c] = {
          x: brickX,
          y: brickY,
          width: brickWidth,
          height: brickHeight,
          status: true,
          color: this.brickColors[r % this.brickColors.length],
          points: this.brickPoints[r % this.brickPoints.length]
        };
      }
    }
  }

  movePaddle(direction: 'left' | 'right') {
    if (direction === 'left') {
      this.paddle.dx = -this.paddle.speed;
    } else {
      this.paddle.dx = this.paddle.speed;
    }
  }

  stopPaddle() {
    this.paddle.dx = 0;
  }

  updatePaddlePosition() {
    this.paddle.x += this.paddle.dx;

    // Prevent paddle from going out of bounds
    if (this.paddle.x < 0) {
      this.paddle.x = 0;
    }
    if (this.paddle.x + this.paddle.width > this.canvasWidth) {
      this.paddle.x = this.canvasWidth - this.paddle.width;
    }
  }

  drawPaddle() {
    this.ctx.beginPath();
    this.ctx.rect(
      this.paddle.x,
      this.paddle.y,
      this.paddle.width,
      this.paddle.height
    );

    // Create gradient for paddle
    const gradient = this.ctx.createLinearGradient(
      this.paddle.x,
      this.paddle.y,
      this.paddle.x + this.paddle.width,
      this.paddle.y + this.paddle.height
    );
    gradient.addColorStop(0, '#3B82F6');
    gradient.addColorStop(1, '#1E40AF');

    this.ctx.fillStyle = gradient;
    this.ctx.shadowColor = 'rgba(59, 130, 246, 0.5)';
    this.ctx.shadowBlur = 10;
    this.ctx.fill();
    this.ctx.shadowBlur = 0;
    this.ctx.closePath();
  }

  drawBall() {
    this.ctx.beginPath();
    this.ctx.arc(this.ball.x, this.ball.y, this.ball.radius, 0, Math.PI * 2);

    // Create gradient for ball
    const gradient = this.ctx.createRadialGradient(
      this.ball.x,
      this.ball.y,
      0,
      this.ball.x,
      this.ball.y,
      this.ball.radius
    );
    gradient.addColorStop(0, '#EF4444');
    gradient.addColorStop(1, '#B91C1C');

    this.ctx.fillStyle = gradient;
    this.ctx.shadowColor = 'rgba(239, 68, 68, 0.5)';
    this.ctx.shadowBlur = 10;
    this.ctx.fill();
    this.ctx.shadowBlur = 0;
    this.ctx.closePath();
  }

  drawBricks() {
    for (let r = 0; r < this.bricks.length; r++) {
      for (let c = 0; c < this.bricks[r].length; c++) {
        const brick = this.bricks[r][c];
        if (brick.status) {
          this.ctx.beginPath();
          this.ctx.rect(brick.x, brick.y, brick.width, brick.height);

          // Create gradient for brick
          const gradient = this.ctx.createLinearGradient(
            brick.x,
            brick.y,
            brick.x + brick.width,
            brick.y + brick.height
          );
          gradient.addColorStop(0, brick.color);
          gradient.addColorStop(1, this.darkenColor(brick.color, 30));

          this.ctx.fillStyle = gradient;
          this.ctx.fill();
          this.ctx.strokeStyle = '#FFFFFF';
          this.ctx.lineWidth = 1;
          this.ctx.stroke();
          this.ctx.closePath();
        }
      }
    }
  }

  darkenColor(color: string, percent: number): string {
    // Convert hex to RGB
    let r = parseInt(color.substring(1, 3), 16);
    let g = parseInt(color.substring(3, 5), 16);
    let b = parseInt(color.substring(5, 7), 16);

    // Darken
    r = Math.floor(r * (100 - percent) / 100);
    g = Math.floor(g * (100 - percent) / 100);
    b = Math.floor(b * (100 - percent) / 100);

    // Convert back to hex
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }

  clearCanvas() {
    this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
  }

  gameLoop() {
    this.clearCanvas();
    this.updatePaddlePosition();
    this.ballMovement();
    this.checkBrickCollisions();
    this.drawPaddle();
    this.drawBall();
    this.drawBricks();

    if (!this.gameOver) {
      this.animationFrameId = requestAnimationFrame(() => this.gameLoop());
    }
  }

  ballMovement() {
    this.ball.x += this.ball.dx;
    this.ball.y += this.ball.dy;

    // Wall collisions
    if (
      this.ball.x + this.ball.dx > this.canvasWidth - this.ball.radius ||
      this.ball.x + this.ball.dx < this.ball.radius
    ) {
      this.ball.dx = -this.ball.dx;
      this.emitGameEvent('wallCollision', { position: 'side' });
    }

    if (this.ball.y + this.ball.dy < this.ball.radius) {
      this.ball.dy = -this.ball.dy;
      this.emitGameEvent('wallCollision', { position: 'top' });
    } else if (
      this.ball.y + this.ball.dy >
      this.canvasHeight - this.ball.radius
    ) {
      // Paddle collision
      if (
        this.ball.x > this.paddle.x &&
        this.ball.x < this.paddle.x + this.paddle.width
      ) {
        // Calculate angle based on where the ball hits the paddle
        const hitPosition = (this.ball.x - this.paddle.x) / this.paddle.width;
        const angle = hitPosition * Math.PI - Math.PI / 2; // -90° to 90° angle

        this.ball.dx = Math.cos(angle) * this.ball.speed;
        this.ball.dy = -Math.sin(angle) * this.ball.speed;

        this.emitGameEvent('paddleHit', {
          position: hitPosition,
          angle: angle,
          ballSpeed: this.ball.speed
        });
      } else {
        // Ball missed the paddle
        this.attemptsRemaining--;

        if (this.attemptsRemaining <= 0) {
          this.gameOver = true;
          this.gameWon = false;
          this.emitGameEvent('gameOver', {
            score: this.score,
            level: this.level,
            result: 'lose'
          });
        } else {
          this.resetBall();
          this.emitGameEvent('ballLost', {
            attemptsRemaining: this.attemptsRemaining
          });
        }
      }
    }
  }

  checkBrickCollisions() {
    let allBricksDestroyed = true;

    for (let r = 0; r < this.bricks.length; r++) {
      for (let c = 0; c < this.bricks[r].length; c++) {
        const brick = this.bricks[r][c];

        if (brick.status) {
          allBricksDestroyed = false;

          // Check collision
          if (
            this.ball.x > brick.x &&
            this.ball.x < brick.x + brick.width &&
            this.ball.y > brick.y &&
            this.ball.y < brick.y + brick.height
          ) {
            this.ball.dy = -this.ball.dy;
            brick.status = false;
            this.score += brick.points;

            this.emitGameEvent('brickHit', {
              row: r,
              column: c,
              points: brick.points,
              totalScore: this.score
            });

            // Check if all bricks are destroyed
            let remainingBricks = 0;
            for (let r = 0; r < this.bricks.length; r++) {
              for (let c = 0; c < this.bricks[r].length; c++) {
                if (this.bricks[r][c].status) {
                  remainingBricks++;
                }
              }
            }

            if (remainingBricks === 0) {
              if (this.level < this.maxLevel) {
                this.level++;
                this.resetBall();
                this.initializeBricks();

                // Increase ball speed for next level
                this.ball.speed += this.config.ball.speedIncrement;

                this.emitGameEvent('levelUp', {
                  level: this.level,
                  score: this.score,
                  ballSpeed: this.ball.speed
                });
              } else {
                // Game won
                this.gameOver = true;
                this.gameWon = true;

                this.emitGameEvent('gameOver', {
                  score: this.score,
                  level: this.level,
                  result: 'win'
                });
              }
            }
          }
        }
      }
    }
  }

  resetBall() {
    this.ball.x = this.canvasWidth / 2;
    this.ball.y = this.canvasHeight / 2;
    this.setRandomInitialDirection();
  }

  setRandomInitialDirection() {
    const angle = Math.random() * (Math.PI / 3) + Math.PI / 6; // Random angle between 30° and 60°
    const direction = Math.random() < 0.5 ? -1 : 1; // Randomly left or right

    this.ball.dx = Math.cos(angle) * this.ball.speed * direction;
    this.ball.dy = -Math.sin(angle) * this.ball.speed;
  }

  restartGame() {
    if (this.attemptsRemaining <= 0) {
      this.attemptsRemaining = this.config.maxAttempts;
    }

    this.score = 0;
    this.level = 1;
    this.gameOver = false;
    this.gameWon = false;
    this.ball.speed = this.config.ball.initialSpeed;
    this.initializeBricks();
    this.resetBall();

    // Cancel existing animation frame and start a new one
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
    }
    this.animationFrameId = requestAnimationFrame(() => this.gameLoop());

    this.emitGameEvent('restart', {
      attemptsRemaining: this.attemptsRemaining
    });
  }

  handleControlClick(control: any) {
    // Convert the event to a string if it's not already
    const controlName = typeof control === 'string' ? control : control.toString();

    console.log('Control clicked:', controlName);

    switch (controlName) {
      case 'restart':
        this.restartGame();
        break;
      case 'newGame':
        this.attemptsRemaining = this.config.maxAttempts;
        this.score = 0;
        this.level = 1;
        this.gameOver = false;
        this.gameWon = false;
        this.ball.speed = this.config.ball.initialSpeed;
        this.initializeBricks();
        this.resetBall();

        // Cancel existing animation frame and start a new one
        if (this.animationFrameId) {
          cancelAnimationFrame(this.animationFrameId);
        }
        this.animationFrameId = requestAnimationFrame(() => this.gameLoop());

        this.emitGameEvent('newGame', {});
        break;
      default:
        console.warn('Unknown control:', controlName);
    }
  }

  emitGameEvent(type: string, data: any) {
    const event: BreakoutGameEvent = {
      type: type,
      data: data,
      timestamp: new Date().toISOString(),
      gameId: this.gameId
    };

    this.gameEventEmitter.emit(event);
    console.log('Breakout game event:', event);
  }

  getScoreItems() {
    return [
      { title: 'Score', number: this.score },
      { title: 'Level', number: `${this.level}/${this.maxLevel}` },
      { title: 'Attempts', number: this.attemptsRemaining }
    ];
  }

  // Event Listeners for Keyboard and Touch Controls...
  @HostListener('window:keydown', ['$event'])
  handleKeyboardInput(event: KeyboardEvent) {
    if (event.key === 'ArrowLeft') {
      this.movePaddle('left');
    } else if (event.key === 'ArrowRight') {
      this.movePaddle('right');
    }
  }

  @HostListener('window:keyup', ['$event'])
  handleKeyboardRelease(event: KeyboardEvent) {
    if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
      this.stopPaddle();
    }
  }
}
