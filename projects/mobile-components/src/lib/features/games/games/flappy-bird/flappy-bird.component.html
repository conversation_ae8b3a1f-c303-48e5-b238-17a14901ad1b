<div class="game-container">
  <!-- Score Display -->
  <games-score
    [scores]="[
      { title: 'Score', number: score },
      { title: 'High', number: highScore },
      { title: 'Level', number: level },
      { title: 'Time', number: timer + 's' }
    ]"
  ></games-score>

  <!-- Game Title -->
  <div class="game-title">
    <h1>Flappy Bird</h1>
    <p>Tap or press any key to jump</p>
  </div>

  <!-- Game Board -->
  <div class="game-board-container">
    <div class="game-board">
      <div
        class="bird"
        [style.left.px]="bird.x"
        [style.top.px]="bird.y"
        [style.width.px]="birdSize"
        [style.height.px]="birdSize"
      ></div>

      <div class="pipe" *ngFor="let pipe of pipes" [style.left.px]="pipe.x">
        <div class="pipe-top" [style.height.px]="pipe.gapY"></div>
        <div class="gap" [style.top.px]="pipe.gapY" [style.height.px]="pipe.gapSize"></div>
        <div class="pipe-bottom" [style.top.px]="pipe.gapY + pipe.gapSize" [style.height.px]="gameHeight - pipe.gapY - pipe.gapSize"></div>
      </div>
    </div>
  </div>

  <!-- Game Controls -->
  <div class="controls">
    <lib-games-play-button
      [controls]="gameControls"
      (controlClicked)="handleControlClick($event)"
    ></lib-games-play-button>
  </div>

  <!-- Win/Lose Overlay -->
  <lib-win-lose-overlay
    *ngIf="gameResult"
    [status]="gameResult"
    (restart)="restartGame()"
    [message]="'Score: ' + score + ' | High Score: ' + highScore"
  ></lib-win-lose-overlay>
</div>
