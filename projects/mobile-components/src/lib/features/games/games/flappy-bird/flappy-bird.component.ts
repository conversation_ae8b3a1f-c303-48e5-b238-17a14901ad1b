import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, Input, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import { Ng<PERSON><PERSON>, <PERSON>F<PERSON>, CommonModule } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { WinLoseOverlayComponent } from '../components/win-lose-overlay/win-lose-overlay.component';
import { GameControl, GamesPlayButtonComponent } from '../components/games-play-button/games-play-button.component';
import { Game, GameEvent } from 'lp-client-api';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

// Enhanced interfaces for game events
interface GameStartEvent {
  gameId: string;
  timestamp: Date;
  config: FlappyBirdConfig;
}

interface GameEndEvent {
  gameId: string;
  timestamp: Date;
  score: number;
  level: number;
  duration: number;
  result: 'win' | 'lose' | 'quit';
}

interface GameScoreEvent {
  gameId: string;
  timestamp: Date;
  score: number;
  previousScore: number;
  level: number;
}

interface GamePauseEvent {
  gameId: string;
  timestamp: Date;
  currentState: 'paused' | 'resumed';
}

interface GameErrorEvent {
  gameId: string;
  timestamp: Date;
  error: string;
  context?: any;
}

interface GameLevelEvent {
  gameId: string;
  timestamp: Date;
  level: number;
  previousLevel: number;
}

interface GameAchievementEvent {
  gameId: string;
  timestamp: Date;
  achievement: string;
  points: number;
}

interface FlappyBirdConfig {
  id: number;
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  frequencyAttempts: number;
  bird: {
    size: number;
    jumpPower: number;
    gravity: number;
  };
  pipes: {
    width: number;
    gapSize: number;
    speed: number;
    spacing: number;
  };
  gameHeight: number;
  gameWidth: number;
}

@Component({
  selector: 'lib-flappy-bird',
  templateUrl: './flappy-bird.component.html',
  styleUrls: ['./flappy-bird.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    NgIf,
    NgFor,
    GamesScoreComponent,
    WinLoseOverlayComponent,
    GamesPlayButtonComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class FlappyBirdComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  private defaultConfig: FlappyBirdConfig = {
    id: 0,
    difficulty: 'EASY',
    frequency: 'DAILY',
    frequencyAttempts: 3,
    bird: {
      size: 30,
      jumpPower: 6,
      gravity: 0.25
    },
    pipes: {
      width: 60,
      gapSize: 200,
      speed: 1.5,
      spacing: 300
    },
    gameHeight: 500,
    gameWidth: 400
  };

  // === STANDARD GAME INPUTS ===
  // Base styling and configuration
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'accent' = 'default';
  @Input() theme: 'light' | 'dark' | 'auto' = 'auto';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Game mechanics
  @Input() difficulty: 'easy' | 'medium' | 'hard' | 'expert' = 'medium';
  @Input() maxLevel: number = 10;
  @Input() timeLimit: number = 0; // 0 = no limit
  @Input() lives: number = 3;
  @Input() maxLives: number = 5;

  // Scoring and rewards
  @Input() targetScore: number = 0;
  @Input() pointsMultiplier: number = 1;
  @Input() rewardPoints: number = 10;
  @Input() bonusPoints: number = 0;

  // Game state management
  @Input() gameState: 'idle' | 'playing' | 'paused' | 'completed' | 'failed' = 'idle';
  @Input() autoStart: boolean = false;
  @Input() autoReset: boolean = false;
  @Input() saveProgress: boolean = true;
  @Input() allowPause: boolean = true;

  // Sound and effects
  @Input() soundEnabled: boolean = true;
  @Input() effectsEnabled: boolean = true;
  @Input() musicEnabled: boolean = false;
  @Input() vibrationEnabled: boolean = true;

  // Performance settings
  @Input() frameRate: number = 60;
  @Input() enableOptimizations: boolean = true;
  @Input() reducedMotion: boolean = false;

  // Accessibility
  @Input() keyboardControls: boolean = true;
  @Input() screenReaderSupport: boolean = true;
  @Input() highContrast: boolean = false;
  @Input() largeText: boolean = false;

  // === GAME-SPECIFIC INPUTS ===
  @Input() customBirdSize: number = 30;
  @Input() customJumpPower: number = 6;
  @Input() customGravity: number = 0.25;
  @Input() customPipeWidth: number = 60;
  @Input() customGapSize: number = 200;
  @Input() customPipeSpeed: number = 1.5;
  @Input() customGameHeight: number = 500;
  @Input() customGameWidth: number = 400;

  // === GAME EVENT OUTPUTS ===
  @Output() gameStart = new EventEmitter<GameStartEvent>();
  @Output() gameEnd = new EventEmitter<GameEndEvent>();
  @Output() gameScore = new EventEmitter<GameScoreEvent>();
  @Output() gamePause = new EventEmitter<GamePauseEvent>();
  @Output() levelChange = new EventEmitter<GameLevelEvent>();
  @Output() gameAchievement = new EventEmitter<GameAchievementEvent>();
  @Output() gameError = new EventEmitter<GameErrorEvent>();

  // Game-specific events
  @Output() birdJump = new EventEmitter<{position: {x: number, y: number}, velocity: number}>();
  @Output() pipePass = new EventEmitter<{pipeIndex: number, score: number}>();
  @Output() collision = new EventEmitter<{type: 'pipe' | 'ground' | 'ceiling', position: {x: number, y: number}}>();
  @Output() levelUp = new EventEmitter<{level: number, newSpeed: number}>();

  // Legacy inputs for backward compatibility
  @Input() gameId?: string;
  @Input() game: Game | undefined;
  @Input() gameInstance?: any;
  @Input()
  set config(value: FlappyBirdConfig) {
    console.log('Config input set with value:', value);
    this._config = {
      ...this.defaultConfig,
      ...value
    };
    console.log('Merged config:', this._config);
  }
  get config(): FlappyBirdConfig {
    return this._config;
  }
  private _config: FlappyBirdConfig;

  @Output() gameEvent = new EventEmitter<GameEvent>();

  // Computed styling properties
  get containerClasses(): string {
    const baseClasses = 'flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800';
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm', 
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };
    const variantClasses = {
      default: 'from-slate-900 to-slate-800',
      primary: 'from-blue-900 to-blue-800',
      secondary: 'from-gray-900 to-gray-800', 
      accent: 'from-purple-900 to-purple-800'
    };
    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-lg',
      lg: 'rounded-xl',
      full: 'rounded-full'
    };
    
    return `${baseClasses} ${sizeClasses[this.size]} ${variantClasses[this.variant]} ${roundedClasses[this.rounded]} ${this.className}`.trim();
  }

  get effectiveBirdSize(): number {
    return this.customBirdSize || this.config?.bird?.size || 30;
  }

  get effectiveJumpPower(): number {
    return this.customJumpPower || this.config?.bird?.jumpPower || 6;
  }

  get effectiveGravity(): number {
    return this.customGravity || this.config?.bird?.gravity || 0.25;
  }

  get effectivePipeWidth(): number {
    return this.customPipeWidth || this.config?.pipes?.width || 60;
  }

  get effectiveGapSize(): number {
    return this.customGapSize || this.config?.pipes?.gapSize || 200;
  }

  get effectivePipeSpeed(): number {
    return this.customPipeSpeed || this.config?.pipes?.speed || 1.5;
  }

  get effectiveGameHeight(): number {
    return this.customGameHeight || this.config?.gameHeight || 500;
  }

  get effectiveGameWidth(): number {
    return this.customGameWidth || this.config?.gameWidth || 400;
  }

  // Game state

  // Game state
  bird = { x: 50, y: 230, velocity: 0 };
  pipes: { x: number; gapY: number; gapSize: number; passed?: boolean }[] = [];
  gameLoop: any;
  score = 0;
  gameOver = false;
  gameStarted = false;
  gameWon = false;
  level = 1;
  duration = 0;
  startTime = 0;
  highScore = 0;
  moveCount = 0;

  // Animation and timing
  timer = 0;
  timerInterval: any;
  animationFrameId: number = 0;

  // Game physics
  minGapY = 150;
  maxGapY = 300;

  // Game controls configuration
  gameControls: GameControl[] = [
    { name: 'jump', label: 'Jump', icon: 'arrow-up-outline' },
  ];

  gameControls2: GameControl[] = [
    { name: 'restart', label: 'Restart', icon: 'refresh-outline' },
  ];

  constructor(private cdr: ChangeDetectorRef) {
    this._config = { ...this.defaultConfig };
  }

  @HostListener('window:keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    if (!this.keyboardControls || this.gameState !== 'playing') return;

    const key = event.key.toLowerCase();

    switch (key) {
      case ' ':
      case 'arrowup':
      case 'w':
        this.jump();
        event.preventDefault();
        break;
      case 'r':
        if (event.ctrlKey || event.metaKey) return; // Don't interfere with browser refresh
        this.restartGame();
        event.preventDefault();
        break;
      case 'p':
        if (this.allowPause) {
          this.togglePause();
          event.preventDefault();
        }
        break;
    }
  }

  @HostListener('window:touchstart', ['$event'])
  onTouchStart(event: TouchEvent): void {
    if (this.gameState === 'playing') {
      this.jump();
      event.preventDefault();
    }
  }

  public togglePause(): void {
    if (!this.allowPause) return;
    
    if (this.gameState === 'playing') {
      this.gameState = 'paused';
      this.stopGame();
      this.gamePause.emit({
        gameId: this.gameId || 'game-flappy-bird',
        timestamp: new Date(),
        currentState: 'paused'
      });
    } else if (this.gameState === 'paused') {
      this.gameState = 'playing';
      this.startGameLoop();
      this.gamePause.emit({
        gameId: this.gameId || 'game-flappy-bird',
        timestamp: new Date(),
        currentState: 'resumed'
      });
    }
  }

  ngOnInit() {
    console.log('Initializing Flappy Bird game with config:', this.config);

    if (this.game?.gameConfig) {
      const gameConfig = this.game.gameConfig.find(config =>
        config.hasOwnProperty('bird') || config.hasOwnProperty('pipes')
      ) as unknown as {
        id: number;
        difficulty: string;
        frequency: string;
        frequencyAttempts: number;
        bird: { size: number; jumpPower: number; gravity: number };
        pipes: { width: number; gapSize: number; speed: number; spacing: number };
        gameHeight: number;
        gameWidth: number;
      };

      if (gameConfig) {
        console.log('Found game config:', gameConfig);
        this.config = {
          ...this.config,
          id: gameConfig.id ?? this.config.id,
          difficulty: (gameConfig.difficulty as 'EASY' | 'MEDIUM' | 'HARD') ?? this.config.difficulty,
          frequency: (gameConfig.frequency as 'DAILY' | 'WEEKLY' | 'MONTHLY') ?? this.config.frequency,
          frequencyAttempts: gameConfig.frequencyAttempts ?? this.config.frequencyAttempts,
          bird: { ...this.config.bird, ...gameConfig.bird },
          pipes: { ...this.config.pipes, ...gameConfig.pipes },
          gameHeight: gameConfig.gameHeight ?? this.config.gameHeight,
          gameWidth: gameConfig.gameWidth ?? this.config.gameWidth
        };
      }
    }

    console.log('Final config before game init:', this.config);
    this.initializeGame();

    // Load high score from localStorage if available
    const savedHighScore = localStorage.getItem('flappyBirdHighScore');
    if (savedHighScore) {
      this.highScore = parseInt(savedHighScore, 10);
    }
    
    if (this.autoStart) {
      this.startGame();
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    this.stopGame();
  }

  initializeGame(): void {
    console.log('Initializing game with config:', this.config);

    this.bird = { x: 50, y: this.effectiveGameHeight / 2, velocity: 0 };
    this.pipes = [];
    this.score = 0;
    this.level = 1;
    this.gameOver = false;
    this.gameWon = false;
    this.gameStarted = false;
    this.duration = 0;
    this.startTime = Date.now();
    this.timer = 0;
    this.moveCount = 0;
    
    console.log('Game initialized');
  }

  startGame(): void {
    console.log('Starting game');
    if (this.gameStarted) return;
    
    this.gameStarted = true;
    this.gameState = 'playing';
    this.gameOver = false;
    this.startTime = Date.now();

    // Initialize first pipe
    this.addInitialPipe();

    // Emit game start event
    this.gameStart.emit({
      gameId: this.gameId || 'game-flappy-bird',
      timestamp: new Date(),
      config: this.config
    });

    this.startTimers();
    this.startGameLoop();
    
    console.log('Game started');
    this.cdr.detectChanges();
  }

  private addInitialPipe(): void {
    const gapY = Math.floor(Math.random() * (this.maxGapY - this.minGapY + 1)) + this.minGapY;
    this.pipes.push({
      x: this.effectiveGameWidth,
      gapY: gapY,
      gapSize: this.effectiveGapSize,
      passed: false
    });
  }

  private startTimers(): void {
    if (this.timerInterval) clearInterval(this.timerInterval);
    this.timerInterval = setInterval(() => {
      this.timer++;
    }, 1000);
  }

  private startGameLoop(): void {
    if (this.gameLoop) clearInterval(this.gameLoop);
    this.gameLoop = setInterval(() => {
      this.updateGameState();
    }, 1000 / this.frameRate);
  }

  // Handle control button clicks
  handleControlClick(controlName: string) {
    console.log('Control clicked:', controlName);
    switch (controlName) {
      case 'restart':
        this.restartGame();
        break;
      case 'jump':
        this.jump();
        break;
    }
  }

  stopGame() {
    console.log('Stopping game');
    clearInterval(this.gameLoop);
    clearInterval(this.timerInterval);
    this.gameLoop = null;
    this.timerInterval = null;
    console.log('Game stopped. Final score:', this.score);
  }

  jump() {
    if (this.gameState !== 'playing') return;
    
    this.bird.velocity = -this.effectiveJumpPower;
    this.moveCount++;

    // Emit jump event
    this.birdJump.emit({
      position: { x: this.bird.x, y: this.bird.y },
      velocity: this.bird.velocity
    });

    if (this.vibrationEnabled && navigator.vibrate) {
      navigator.vibrate(50);
    }
  }

  updateGameState() {
    if (this.gameState !== 'playing') return;

    this.bird.y += this.bird.velocity;
    this.bird.velocity += this.effectiveGravity;

    // Only add a new pipe when the last pipe is far enough away
    if (this.pipes.length === 0 || this.pipes[this.pipes.length - 1].x < this.effectiveGameWidth - 300) {
      const gapY = Math.floor(Math.random() * (this.maxGapY - this.minGapY + 1)) + this.minGapY;
      this.pipes.push({
        x: this.effectiveGameWidth,
        gapY: gapY,
        gapSize: this.effectiveGapSize,
        passed: false
      });
    }

    // Move pipes
    this.pipes.forEach((pipe) => {
      pipe.x -= this.effectivePipeSpeed;
    });

    // Remove pipes that are off screen
    this.pipes = this.pipes.filter((pipe) => pipe.x > -this.effectivePipeWidth);

    this.checkCollisions();

    // Check boundary collisions
    if (this.bird.y + this.effectiveBirdSize >= this.effectiveGameHeight || this.bird.y <= 0) {
      console.log('Game over: Bird hit the boundary');
      this.collision.emit({
        type: this.bird.y <= 0 ? 'ceiling' : 'ground',
        position: { x: this.bird.x, y: this.bird.y }
      });
      this.endGame('lose');
    }
  }

  checkCollisions() {
    for (let i = 0; i < this.pipes.length; i++) {
      const pipe = this.pipes[i];
      
      if (
        this.bird.x + this.effectiveBirdSize > pipe.x &&
        this.bird.x < pipe.x + this.effectivePipeWidth
      ) {
        if (this.bird.y < pipe.gapY || this.bird.y + this.effectiveBirdSize > pipe.gapY + pipe.gapSize) {
          console.log('Game over: Bird hit a pipe');
          this.collision.emit({
            type: 'pipe',
            position: { x: this.bird.x, y: this.bird.y }
          });
          this.endGame('lose');
          return;
        }
      }

      if (pipe.x + this.effectivePipeWidth < this.bird.x && !pipe.passed) {
        const previousScore = this.score;
        this.score++;
        pipe.passed = true;
        console.log('Score increased:', this.score);

        // Emit pipe pass event
        this.pipePass.emit({
          pipeIndex: i,
          score: this.score
        });

        // Emit score change event
        this.gameScore.emit({
          gameId: this.gameId || 'game-flappy-bird',
          timestamp: new Date(),
          score: this.score,
          previousScore: previousScore,
          level: this.level
        });

        // Update high score if needed
        if (this.score > this.highScore) {
          this.highScore = this.score;
          localStorage.setItem('flappyBirdHighScore', this.highScore.toString());
        }

        this.updateLevel();
        this.saveGameProgress();
      }
    }
  }

  updateLevel() {
    const previousLevel = this.level;
    const newLevel = Math.floor(this.score / 10) + 1;
    
    if (newLevel > this.level && newLevel <= this.maxLevel) {
      this.level = newLevel;
      
      // Emit level change event
      this.levelChange.emit({
        gameId: this.gameId || 'game-flappy-bird',
        timestamp: new Date(),
        level: this.level,
        previousLevel: previousLevel
      });

      // Emit level up event
      this.levelUp.emit({
        level: this.level,
        newSpeed: this.effectivePipeSpeed
      });

      console.log('Level up! Current level:', this.level);
    }
  }

  private saveGameProgress(): void {
    this.duration = Math.floor((Date.now() - this.startTime) / 1000);

    const gameEvent: GameEvent = {
      id: 0, // Will be set by backend
      score: this.score,
      level: this.level,
      duration: this.duration,
      state: this.gameWon ? 'win' : (this.gameOver ? 'lose' : 'in_progress'),
      payload: JSON.stringify({
        bird: this.bird,
        pipes: this.pipes.length,
        moveCount: this.moveCount,
        highScore: Math.max(this.score, this.highScore)
      })
    };

    this.gameEvent.emit(gameEvent);
  }

  private endGame(result: 'win' | 'lose'): void {
    this.gameOver = true;
    this.gameWon = result === 'win';
    this.gameState = result === 'win' ? 'completed' : 'failed';
    this.duration = Math.floor((Date.now() - this.startTime) / 1000);

    // Emit game end event
    this.gameEnd.emit({
      gameId: this.gameId || 'game-flappy-bird',
      timestamp: new Date(),
      score: this.score,
      level: this.level,
      duration: this.duration,
      result: result
    });

    this.saveGameProgress();
    this.stopGame();

    // Auto restart if enabled
    if (this.autoReset && this.autoStart) {
      setTimeout(() => this.restartGame(), 2000);
    }
  }

  restartGame() {
    console.log('Restarting game');
    
    // Emit game end event if game was in progress
    if (this.gameStarted && !this.gameOver) {
      this.gameEnd.emit({
        gameId: this.gameId || 'game-flappy-bird',
        timestamp: new Date(),
        score: this.score,
        level: this.level,
        duration: Math.floor((Date.now() - this.startTime) / 1000),
        result: 'quit'
      });
    }

    this.stopGame();
    this.initializeGame();

    // Auto restart if enabled
    if (this.autoReset && this.autoStart) {
      setTimeout(() => this.startGame(), 100);
    }
  }
}
