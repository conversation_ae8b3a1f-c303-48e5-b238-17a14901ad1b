/* Import shared game styles */
@import '../styles/game-styles.css';

:host {
  @apply block w-full h-full;
}

.game-container {
  @apply flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800;
}

/* Game Title */
.game-title {
  @apply text-center mb-1;
}

.game-title h1 {
  @apply text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-indigo-500;
}

.game-title p {
  @apply text-xs text-slate-400 mb-1;
}

/* Game Board Container */
.game-board-container {
  @apply flex-grow flex justify-center items-center p-2;
  position: relative;
  z-index: 1;
}

.game-board {
  position: relative;
  width: 100%;
  max-width: 600px;
  height: 500px;
  background: linear-gradient(to bottom, #87CEEB, #1E90FF);
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.bird {
  position: absolute;
  background: radial-gradient(circle at 30% 30%, #FFFF00, #FFA500);
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(255, 255, 0, 0.7);
  transition: top 0.05s linear;
  z-index: 10;
}

.pipe {
  position: absolute;
  width: 60px;
  height: 100%;
  z-index: 5;
}

.pipe-top {
  position: absolute;
  top: 0;
  width: 100%;
  background-color: green;
  border-bottom: 4px solid darkgreen;
}

.gap {
  position: absolute;
  width: 100%;
  background-color: transparent;
}

.pipe-bottom {
  position: absolute;
  width: 100%;
  background-color: green;
  border-top: 4px solid darkgreen;
}

/* Controls */
.controls {
  @apply w-full max-w-md mt-4 px-4 mx-auto;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .game-board {
    height: 400px;
  }
}
