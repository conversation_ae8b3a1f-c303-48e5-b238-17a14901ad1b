<div class="flex overflow-hidden flex-col h-screen rounded-lg" [class.win-animation]="showAnimation">
  <!-- Header with Scores -->
  <games-score
    [scores]="[
      { title: 'Score', number: score },
      { title: 'Time', number: timeLeft },
      { title: 'Matches', number: matchedPairs },
      { title: 'Moves', number: moveCount }
    ]"
  ></games-score>

  <!-- Game Board -->
  <div class="flex overflow-hidden flex-col flex-grow items-center p-2 mt-12">
    <div class="w-full max-w-4xl">
      <!-- Grid size based on current level -->
      <div class="grid gap-2" [ngStyle]="{
        'grid-template-columns': 'repeat(' + getGridSize().columns + ', 1fr)',
        'grid-template-rows': 'repeat(' + getGridSize().rows + ', 1fr)'
      }">
        <div *ngFor="let card of cards"
             class="memory-card"
             [class.flipped]="card.flipped"
             [class.matched]="card.matched"
             (click)="onCardClick(card)">
          <div class="card-inner">
            <!-- Front of card (hidden) -->
            <div class="card-front">
              <img [src]="card.image" [alt]="'Card ' + card.value" class="w-full h-full object-cover">
            </div>
            <!-- Back of card (shown) -->
            <div class="card-back">
              <img [src]="memoryConfig.design.cardBackImage" alt="Card back" class="w-full h-full object-cover">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Game Over Overlay -->
  <div *ngIf="gameResult" class="flex fixed inset-0 z-50 justify-center items-center bg-black bg-opacity-70 backdrop-blur-sm">
    <div class="p-8 text-center bg-gradient-to-b from-slate-800 to-slate-900 rounded-2xl shadow-2xl transform game-over-dialog border border-slate-700 max-w-md w-11/12">
      <!-- Mascot -->
      <div class="mb-4 mascot-container relative">
        <div class="absolute inset-0 bg-gradient-to-b from-green-500/20 to-transparent rounded-full blur-xl"></div>
        <img src="assets/images/games/mascot.png" alt="Game Mascot" class="mx-auto w-24 h-24 mascot-bounce relative z-10">
      </div>

      <!-- Result Message -->
      <h2 class="mb-4 text-4xl font-bold text-white game-over-title"
          [ngClass]="{'text-green-500': gameResult === 'win', 'text-red-500': gameResult === 'lose'}">
        {{ gameResult === 'win' ? 'Congratulations!' : 'Game Over!' }}
      </h2>

      <!-- Stats -->
      <div class="mb-6 p-4 bg-slate-700/50 rounded-xl border border-slate-600">
        <div class="grid grid-cols-2 gap-4">
          <div class="stat-item bg-slate-800/70 p-3 rounded-lg border border-slate-700">
            <span class="block text-slate-400">Score</span>
            <span class="block text-2xl font-bold text-green-400">{{ score }}</span>
          </div>
          <div class="stat-item bg-slate-800/70 p-3 rounded-lg border border-slate-700">
            <span class="block text-slate-400">Time</span>
            <span class="block text-2xl font-bold text-amber-400">{{ timeLeft }}s</span>
          </div>
          <div class="stat-item bg-slate-800/70 p-3 rounded-lg border border-slate-700">
            <span class="block text-slate-400">Matches</span>
            <span class="block text-2xl font-bold text-blue-400">{{ matchedPairs }}</span>
          </div>
          <div class="stat-item bg-slate-800/70 p-3 rounded-lg border border-slate-700">
            <span class="block text-slate-400">Moves</span>
            <span class="block text-2xl font-bold text-purple-400">{{ moveCount }}</span>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <button
        (click)="restartGame()"
        class="px-8 py-3 w-full text-xl font-bold text-white bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105 hover:from-green-600 hover:to-emerald-700 focus:outline-none focus:ring-4 focus:ring-green-400/50">
        Play Again
      </button>
    </div>
  </div>

  <!-- Start Game Button -->
  <div *ngIf="!gameStarted && !gameResult" class="flex fixed inset-0 z-50 justify-center items-center bg-black bg-opacity-70 backdrop-blur-sm">
    <div class="p-8 text-center bg-gradient-to-b from-slate-800 to-slate-900 rounded-2xl shadow-2xl transform game-over-dialog border border-slate-700 max-w-md w-11/12">
      <!-- Mascot -->
      <div class="mb-4 mascot-container relative">
        <div class="absolute inset-0 bg-gradient-to-b from-blue-500/20 to-transparent rounded-full blur-xl"></div>
        <img src="assets/images/games/mascot.png" alt="Game Mascot" class="mx-auto w-24 h-24 mascot-bounce relative z-10">
      </div>

      <!-- Welcome Message -->
      <h2 class="mb-6 text-3xl font-bold text-white">
        Memory Game
      </h2>

      <p class="mb-6 text-slate-300">Match pairs of cards to win. Try to remember the positions of each card!</p>

      <!-- Start Button -->
      <button (click)="startGame()"
              class="px-8 py-3 w-full text-xl font-bold text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105 hover:from-blue-600 hover:to-indigo-700 focus:outline-none focus:ring-4 focus:ring-blue-400/50">
        Start Game
      </button>
    </div>
  </div>

  <!-- Game messages and faces -->
  <div *ngIf="currentMessage" class="fixed inset-0 flex items-center justify-center z-50 pointer-events-none">
    <div class="bg-gradient-to-b from-slate-800 to-slate-900 rounded-xl p-6 shadow-2xl text-center transform transition-all duration-300 ease-in-out border border-slate-700 max-w-xs">
      <img [src]="currentMessage.face || ''" alt="Game Face" class="w-24 h-24 mx-auto mb-3 result-face">
      <p class="text-xl font-bold text-white">{{ currentMessage.message || '' }}</p>
    </div>
  </div>
</div>
