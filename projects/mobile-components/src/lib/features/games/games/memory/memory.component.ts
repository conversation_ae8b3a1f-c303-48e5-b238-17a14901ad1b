import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectorRef, Input, Output, EventEmitter, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { Game, MemoryConfig as GameMemoryConfig, GameEvent } from 'lp-client-api';

interface MemoryCard {
  id: number;
  value: string;
  image: string;
  flipped: boolean;
  matched: boolean;
}

interface GridSize {
  rows: number;
  columns: number;
}

interface MemoryLevel {
  timeLimit: number;
  maxAttempts: number;
  points: number;
  gridSize: GridSize;
}

interface MemoryConfig {
  levels: MemoryLevel[];
  cardImages: string[];
  design: {
    cardBackImage: string;
    cardStyle?: {
      backgroundColor?: string;
      borderColor?: string;
    };
    matchAnimation?: boolean;
  };
  matchTime: number;
}

@Component({
  selector: 'games-memory',
  templateUrl: './memory.component.html',
  styleUrls: ['./memory.component.css'],
  standalone: true,
  imports: [CommonModule, GamesScoreComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class MemoryComponent implements OnInit, OnDestroy {
  @Input() gameId?: string;
  @Input() game?: Game;
  @Input() gameInstance?: any;
  @Input() config: any = {};

  @Output() gameEvent = new EventEmitter<GameEvent>();

  private readonly POINTS_PER_MATCH = 10;
  private readonly MAX_MOVES = 9;
  private timerInterval?: number;

  memoryConfig: MemoryConfig = {
    levels: [
      {
        timeLimit: 300,
        maxAttempts: 3,
        points: 10,
        gridSize: { rows: 4, columns: 4 }
      }
    ],
    cardImages: [],
    design: {
      cardBackImage: 'assets/images/games/mascot.png',
      matchAnimation: true
    },
    matchTime: 1000
  };

  cards: MemoryCard[] = [];
  flippedCards: MemoryCard[] = [];
  score = 0;
  timeLeft = 0;
  matchedPairs = 0;
  moveCount = 0;
  currentLevel = 0;
  gameStarted = false;
  gameResult: 'win' | 'lose' | null = null;
  showAnimation = false;
  attemptsRemaining = Infinity;
  isLocked = false;
  currentMessage: { message: string; face: string } | null = null;

  constructor(private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    console.log('Memory Config:', this.config);
    if (this.game?.gameConfig) {
      const memoryConfig = this.game.gameConfig.find(config =>
        config.hasOwnProperty('design') || config.hasOwnProperty('levels')
      ) as GameMemoryConfig;

      if (memoryConfig) {
        // Create default level if none provided
        const defaultLevel = {
          level: 1,
          pairs: 8,
          timeLimit: 300
        };

        this.memoryConfig = {
          ...this.memoryConfig,
          design: {
            ...this.memoryConfig.design,
            cardBackImage: memoryConfig.design?.cardBackImage || this.memoryConfig.design.cardBackImage,
            cardStyle: memoryConfig.design?.cardStyle,
            matchAnimation: memoryConfig.design?.matchAnimation
          },
          matchTime: memoryConfig.design?.matchAnimation ? 1000 : 500,
          levels: memoryConfig.levels?.map(level => ({
            timeLimit: level.timeLimit || 300,
            maxAttempts: memoryConfig.attemptsPerLevel || 3,
            points: memoryConfig.baseScore || 10,
            gridSize: {
              rows: Math.floor(Math.sqrt(level.pairs * 2)),
              columns: Math.ceil(Math.sqrt(level.pairs * 2))
            }
          })) || [
            {
              timeLimit: defaultLevel.timeLimit,
              maxAttempts: memoryConfig.attemptsPerLevel || 3,
              points: memoryConfig.baseScore || 10,
              gridSize: {
                rows: Math.floor(Math.sqrt(defaultLevel.pairs * 2)),
                columns: Math.ceil(Math.sqrt(defaultLevel.pairs * 2))
              }
            }
          ]
        };
      }
    }

    this.initializeGame();
  }

  ngOnDestroy(): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }
  }

  public getCurrentLevelConfig(): MemoryLevel {
    return this.memoryConfig.levels[this.currentLevel] || this.memoryConfig.levels[0];
  }

  public getGridSize(): GridSize {
    return this.getCurrentLevelConfig().gridSize;
  }

  private initializeGame(): void {
    this.gameStarted = false;
    this.gameResult = null;
    this.showAnimation = false;
    this.score = 0;
    this.moveCount = 0;
    this.matchedPairs = 0;
    this.flippedCards = [];
    this.timeLeft = this.getCurrentLevelConfig().timeLimit;
    this.attemptsRemaining = this.getCurrentLevelConfig().maxAttempts;
    this.createCards();
  }

  private createCards(): void {
    const levelConfig = this.getCurrentLevelConfig();
    const totalPairs = (levelConfig.gridSize.rows * levelConfig.gridSize.columns) / 2;

    // Create default card images if none provided
    const defaultCardImages = Array.from({ length: totalPairs }, (_, i) =>
      `assets/images/games/memory/card-${i + 1}.png`
    );

    // Get random cards from the available images, cycling through them if we need more pairs
    const cardImages = defaultCardImages;

    const cardPairs = cardImages.map((image: string, index: number) => [
      { id: index * 2, value: `${index}`, image, flipped: false, matched: false },
      { id: index * 2 + 1, value: `${index}`, image, flipped: false, matched: false }
    ]);

    // Flatten and shuffle the cards
    this.cards = cardPairs.reduce((acc, pair) => [...acc, ...pair], [])
      .sort(() => Math.random() - 0.5);
  }

  public onCardClick(card: MemoryCard): void {
    if (
      !this.gameStarted ||
      this.isLocked ||
      card.flipped ||
      card.matched ||
      this.flippedCards.length >= 2
    ) {
      return;
    }

    card.flipped = true;
    this.flippedCards.push(card);

    if (this.flippedCards.length === 2) {
      this.moveCount++;
      this.checkMatch();
    }
  }

  private checkMatch(): void {
    const [card1, card2] = this.flippedCards;
    this.isLocked = true;

    if (card1.value === card2.value) {
      setTimeout(() => {
        card1.matched = true;
        card2.matched = true;
        this.matchedPairs++;
        this.updateScore();
        this.showMessage('match');
        this.checkWinCondition();
        this.flippedCards = [];
        this.isLocked = false;
      }, this.memoryConfig.matchTime);
    } else {
      setTimeout(() => {
        card1.flipped = false;
        card2.flipped = false;
        this.showMessage('mismatch');
        this.flippedCards = [];
        this.isLocked = false;

        // Check if we've exceeded the move limit
        if (this.moveCount >= this.MAX_MOVES) {
          this.handleGameOver();
        }
      }, this.memoryConfig.matchTime);
    }
  }

  private updateScore(): void {
    const levelConfig = this.getCurrentLevelConfig();
    const basePoints = levelConfig.points || this.POINTS_PER_MATCH;
    const timeBonus = Math.floor(this.timeLeft / 10);
    this.score += basePoints + timeBonus;
  }

  public startGame(): void {
    if (this.attemptsRemaining <= 0) return;

    this.gameStarted = true;
    this.createCards();
    this.startTimer();
  }

  private startTimer(): void {
    const levelConfig = this.getCurrentLevelConfig();
    this.timeLeft = levelConfig.timeLimit;

    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }

    this.timerInterval = window.setInterval(() => {
      if (this.timeLeft > 0) {
        this.timeLeft--;
      } else {
        this.handleGameOver();
      }
    }, 1000);
  }

  private checkWinCondition(): void {
    if (this.matchedPairs === this.cards.length / 2) {
      this.handleGameWin();
    }
  }

  private handleGameWin(): void {
    this.gameResult = 'win';
    this.showAnimation = true;
    this.gameStarted = false;
    this.showMessage('win');
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }
    this.saveGameProgress();
  }

  private handleGameOver(): void {
    this.gameResult = 'lose';
    this.showAnimation = true;
    this.gameStarted = false;
    this.showMessage('lose');
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }
    this.saveGameProgress();
  }

  private saveGameProgress(): void {
    const gameEvent: GameEvent = {
      id: 0, // This will be set by the backend
      score: this.score,
      level: this.currentLevel + 1,
      duration: this.getCurrentLevelConfig().timeLimit - this.timeLeft,
      state: this.gameResult || 'in_progress',
      payload: JSON.stringify({
        matchedPairs: this.matchedPairs,
        moveCount: this.moveCount,
        timeLeft: this.timeLeft
      })
    };

    this.gameEvent.emit(gameEvent);
  }

  public restartGame(): void {
    this.initializeGame();
  }

  private showMessage(type: 'match' | 'mismatch' | 'win' | 'lose'): void {
    if (this.config.gameStyles?.messages?.[type]) {
      this.currentMessage = {
        message: this.config.gameStyles.messages[type].message,
        face: this.config.gameStyles.messages[type].face
      };
      // Clear message after 2 seconds for match/mismatch, keep for win/lose
      if (type === 'match' || type === 'mismatch') {
        setTimeout(() => {
          this.currentMessage = null;
          this.cdr.detectChanges();
        }, 2000);
      }
    }
  }

  getFaceImage(type: 'match' | 'mismatch' | 'win' | 'lose'): string {
    return this.config.gameStyles?.faceImages?.[type] || '';
  }
}
