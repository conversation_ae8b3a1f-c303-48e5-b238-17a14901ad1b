.memory-game {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  padding: 1rem;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.game-header {
  width: 100%;
  max-width: 600px;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  margin-bottom: 1rem;
}

.stats-container {
  display: flex;
  justify-content: space-around;
  gap: 1rem;
}

.stat-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  min-width: 80px;
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
}

.stat-value {
  font-size: 1.25rem;
  font-weight: bold;
  color: white;
}

.game-board {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  max-width: 600px;
  width: 100%;
  perspective: 1000px;
}

.memory-card {
  aspect-ratio: 1;
  perspective: 1000px;
  cursor: pointer;
  position: relative;
  transition: transform 0.2s;
}

.memory-card:hover {
  transform: scale(1.02);
}

.card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  transition: transform 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.memory-card.flipped .card-inner {
  transform: rotateY(180deg);
}

.card-front,
.card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-front {
  transform: rotateY(180deg);
  background-color: white;
}

.card-back {
  background-color: #2563eb;
}

.memory-card.matched .card-inner {
  animation: matched 0.4s ease-in-out;
}

@keyframes matched {
  0%, 100% {
    transform: rotateY(180deg) scale(1);
  }
  50% {
    transform: rotateY(180deg) scale(1.1);
  }
}

.win-animation {
  animation: win 0.5s ease-in-out;
}

@keyframes win {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.game-over-dialog {
  animation: scaleIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes scaleIn {
  from {
    transform: scale(0.7);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.mascot-bounce {
  animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.game-over-title {
  animation: textShine 3s linear infinite;
  background: linear-gradient(to right, #ef4444, #f97316, #ef4444);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  background-size: 200% auto;
  text-shadow: 0 0 20px rgba(239, 68, 68, 0.7);
}

@keyframes textShine {
  0% {
    background-position: 0% center;
  }
  100% {
    background-position: 200% center;
  }
}

.result-face {
  animation: popIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) 0.2s both;
}

@keyframes popIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  70% {
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.stat-item {
  padding: 0.75rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  animation: slideIn 0.5s ease-out both;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.stat-item:nth-child(2) {
  animation-delay: 0.1s;
}

.stat-item:nth-child(3) {
  animation-delay: 0.2s;
}

.stat-item:nth-child(4) {
  animation-delay: 0.3s;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.game-result-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  animation: fadeIn 0.5s ease-out;
}

.result-content {
  text-align: center;
  color: white;
  padding: 2rem;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.mascot {
  width: 120px;
  height: 120px;
  margin-bottom: 1rem;
}

.bounce {
  animation: bounce 1s infinite;
}

.shake {
  animation: shake 0.5s infinite;
}

.new-game-btn {
  margin-top: 1rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  background: #4CAF50;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.3s;
}

.new-game-btn:hover {
  background: #45a049;
}

.confetti-container {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: -1;
}

.confetti {
  position: absolute;
  width: 10px;
  height: 10px;
  background: #ffd700;
  animation: confetti-fall 3s linear infinite;
}

.confetti:nth-child(2n) {
  background: #ff4081;
  animation-delay: -0.5s;
}

.confetti:nth-child(3n) {
  background: #00e676;
  animation-delay: -1s;
}

.confetti:nth-child(4n) {
  background: #2196f3;
  animation-delay: -1.5s;
}

@keyframes confetti-fall {
  0% {
    transform: translateY(-100%) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(360deg);
    opacity: 0;
  }
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-20px); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
