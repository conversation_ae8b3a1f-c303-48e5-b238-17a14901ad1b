/* Import shared game styles */
@import '../styles/game-styles.css';

:host {
  @apply block w-full h-full;
}

.game-container {
  @apply flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800;
}

.game-title {
  @apply text-center mb-1;
}

.game-title h1 {
  @apply text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500;
}

.game-title p {
  @apply text-xs text-slate-400 mb-1;
}

.game-board {
  position: relative;
  overflow: hidden;
  max-width: 100%;
  margin: 0 auto;
}

.game-board::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-yellow-500/10 to-orange-500/10;
  filter: blur(20px);
  z-index: 0;
}

.board-grid {
  @apply relative z-10 rounded-lg overflow-hidden;
  display: grid;
  grid-template-rows: repeat(9, 1fr);
  aspect-ratio: 10/9;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8), rgba(15, 23, 42, 0.9));
  border: 1px solid rgba(100, 116, 139, 0.3);
}

.board-row {
  display: flex;
  width: 100%;
}

.board-cell {
  position: relative;
  aspect-ratio: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s ease;
}

.wall {
  background: linear-gradient(135deg, #1e40af, #1e3a8a);
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.5);
}

.path {
  background-color: rgba(0, 0, 0, 0.7);
}

.character {
  font-size: 1.25rem;
  line-height: 1;
  z-index: 2;
  transition: transform 0.2s ease;
}

.pacman {
  filter: drop-shadow(0 0 5px rgba(255, 255, 0, 0.7));
}

.ghost {
  filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.5));
  transition: all 0.2s ease;
}

.ghost-vulnerable {
  filter: drop-shadow(0 0 8px rgba(0, 0, 255, 0.7));
  animation: shake 0.5s infinite;
}

.dot {
  width: 6px;
  height: 6px;
  background: rgb(254, 240, 138);
  border-radius: 50%;
  box-shadow: 0 0 5px rgba(254, 240, 138, 0.7);
}

.power-pellet {
  font-size: 1.25rem;
  line-height: 1;
  filter: drop-shadow(0 0 8px rgba(0, 255, 255, 0.8));
}

.controls-container {
  @apply fixed bottom-20 left-1/2 transform -translate-x-1/2 w-32 h-32 z-50;
}

.joystick-base {
  @apply relative w-full h-full rounded-full;
  background: radial-gradient(circle, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.5) 100%);
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
}

.joystick-handle {
  @apply absolute w-14 h-14 rounded-full;
  transform: translate(-50%, -50%);
  background: radial-gradient(circle, rgba(255, 255, 255, 0.9) 0%, rgba(200, 200, 200, 0.8) 100%);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  cursor: pointer;
}

.game-result {
  @apply fixed inset-0 flex items-center justify-center bg-black/70 z-50;
}

.game-result-content {
  @apply bg-slate-800 p-6 rounded-xl text-center text-white shadow-2xl;
  animation: fadeIn 0.5s ease-in-out;
}

.power-mode-message {
  @apply fixed top-1/4 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-yellow-600 to-orange-600 text-white px-4 py-2 rounded-lg text-center z-50 shadow-lg;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translate(-50%, -60%); }
  to { opacity: 1; transform: translate(-50%, -50%); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

.animate-pulse {
  animation: pulse 1.5s infinite;
}
