import {
  <PERSON>mponent,
  <PERSON><PERSON>nit,
  Host<PERSON>istener,
  NgZone,
  ViewChild,
  ElementRef,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
  Input,
  Output,
  EventEmitter,
  OnDestroy,
  ChangeDetectorRef
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { GameControl, GamesPlayButtonComponent } from '../components/games-play-button/games-play-button.component';
import { Game, GameEvent } from 'lp-client-api';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

// Enhanced interfaces for game events
interface GameStartEvent {
  gameId: string;
  timestamp: Date;
  config: PacManConfig;
}

interface GameEndEvent {
  gameId: string;
  timestamp: Date;
  score: number;
  level: number;
  duration: number;
  result: 'win' | 'lose' | 'quit';
}

interface GameScoreEvent {
  gameId: string;
  timestamp: Date;
  score: number;
  previousScore: number;
  level: number;
}

interface GamePauseEvent {
  gameId: string;
  timestamp: Date;
  currentState: 'paused' | 'resumed';
}

interface GameErrorEvent {
  gameId: string;
  timestamp: Date;
  error: string;
  context?: any;
}

interface GameLevelEvent {
  gameId: string;
  timestamp: Date;
  level: number;
  previousLevel: number;
}

interface GameAchievementEvent {
  gameId: string;
  timestamp: Date;
  achievement: string;
  points: number;
}

interface PacManConfig {
  id: number;
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  frequencyAttempts: number;
  ghostSpeed: number;
  powerModeDuration: number;
  levels: number;
  gridSize: { width: number; height: number };
  pacmanSpeed: number;
  pointValues: {
    dot: number;
    powerPellet: number;
    ghost: number;
    fruit: number;
  };
}

interface PacManGameEvent {
  type: string;
  data: any;
  timestamp: string;
  gameId: string;
}

@Component({
  selector: 'lib-pac-man',
  templateUrl: './pac-man.component.html',
  styleUrls: ['./pac-man.component.css'],
  standalone: true,
  imports: [CommonModule, GamesScoreComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class PacManComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  private defaultConfig: PacManConfig = {
    id: 0,
    difficulty: 'MEDIUM',
    frequency: 'DAILY',
    frequencyAttempts: 3,
    ghostSpeed: 500,
    powerModeDuration: 10000,
    levels: 5,
    gridSize: { width: 10, height: 9 },
    pacmanSpeed: 200,
    pointValues: {
      dot: 10,
      powerPellet: 50,
      ghost: 200,
      fruit: 100
    }
  };

  // === STANDARD GAME INPUTS ===
  // Base styling and configuration
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'accent' = 'default';
  @Input() theme: 'light' | 'dark' | 'auto' = 'auto';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Game mechanics
  @Input() difficulty: 'easy' | 'medium' | 'hard' | 'expert' = 'medium';
  @Input() maxLevel: number = 10;
  @Input() timeLimit: number = 0; // 0 = no limit
  @Input() lives: number = 3;
  @Input() maxLives: number = 5;

  // Scoring and rewards
  @Input() targetScore: number = 0;
  @Input() pointsMultiplier: number = 1;
  @Input() rewardPoints: number = 10;
  @Input() bonusPoints: number = 0;

  // Game state management
  @Input() gameState: 'idle' | 'playing' | 'paused' | 'completed' | 'failed' = 'idle';
  @Input() autoStart: boolean = false;
  @Input() autoReset: boolean = false;
  @Input() saveProgress: boolean = true;
  @Input() allowPause: boolean = true;

  // Sound and effects
  @Input() soundEnabled: boolean = true;
  @Input() effectsEnabled: boolean = true;
  @Input() musicEnabled: boolean = false;
  @Input() vibrationEnabled: boolean = true;

  // Performance settings
  @Input() frameRate: number = 60;
  @Input() enableOptimizations: boolean = true;
  @Input() reducedMotion: boolean = false;

  // Accessibility
  @Input() keyboardControls: boolean = true;
  @Input() screenReaderSupport: boolean = true;
  @Input() highContrast: boolean = false;
  @Input() largeText: boolean = false;

  // === GAME-SPECIFIC INPUTS ===
  @Input() customGhostSpeed: number = 500;
  @Input() customPowerModeDuration: number = 10000;
  @Input() customPacmanSpeed: number = 200;
  @Input() customGridWidth: number = 10;
  @Input() customGridHeight: number = 9;
  @Input() showGhostTargets: boolean = false;
  @Input() enablePowerPellets: boolean = true;

  // === GAME EVENT OUTPUTS ===
  @Output() gameStart = new EventEmitter<GameStartEvent>();
  @Output() gameEnd = new EventEmitter<GameEndEvent>();
  @Output() gameScore = new EventEmitter<GameScoreEvent>();
  @Output() gamePause = new EventEmitter<GamePauseEvent>();
  @Output() levelChange = new EventEmitter<GameLevelEvent>();
  @Output() gameAchievement = new EventEmitter<GameAchievementEvent>();
  @Output() gameError = new EventEmitter<GameErrorEvent>();

  // Game-specific events
  @Output() dotEaten = new EventEmitter<{position: {x: number, y: number}, points: number}>();
  @Output() powerPelletEaten = new EventEmitter<{position: {x: number, y: number}}>();
  @Output() ghostEaten = new EventEmitter<{ghostId: number, points: number}>();
  @Output() pacmanDeath = new EventEmitter<{position: {x: number, y: number}, livesRemaining: number}>();

  // Legacy inputs for backward compatibility
  @Input() gameId: string = '';
  @Input() game?: Game;
  @Input() gameInstance: any = null;
  @Input()
  set config(value: PacManConfig) {
    console.log('Config input set with value:', value);
    this._config = {
      ...this.defaultConfig,
      ...value
    };
    console.log('Merged config:', this._config);
  }
  get config(): PacManConfig {
    return this._config;
  }
  private _config: PacManConfig;

  @Output() gameEventEmitter = new EventEmitter<PacManGameEvent>();
  @Output() gameEvent = new EventEmitter<GameEvent>();

  // Computed styling properties
  get containerClasses(): string {
    const baseClasses = 'flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800';
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm', 
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };
    const variantClasses = {
      default: 'from-slate-900 to-slate-800',
      primary: 'from-blue-900 to-blue-800',
      secondary: 'from-gray-900 to-gray-800', 
      accent: 'from-purple-900 to-purple-800'
    };
    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-lg',
      lg: 'rounded-xl',
      full: 'rounded-full'
    };
    
    return `${baseClasses} ${sizeClasses[this.size]} ${variantClasses[this.variant]} ${roundedClasses[this.rounded]} ${this.className}`.trim();
  }

  get effectiveGhostSpeed(): number {
    return this.customGhostSpeed || this.config?.ghostSpeed || 500;
  }

  get effectivePowerModeDuration(): number {
    return this.customPowerModeDuration || this.config?.powerModeDuration || 10000;
  }

  get effectivePacmanSpeed(): number {
    return this.customPacmanSpeed || this.config?.pacmanSpeed || 200;
  }

  get effectiveGridWidth(): number {
    return this.customGridWidth || this.config?.gridSize?.width || 10;
  }

  get effectiveGridHeight(): number {
    return this.customGridHeight || this.config?.gridSize?.height || 9;
  }

  // Existing game state properties

  // Existing game state properties
  grid: number[][] = [];
  pacman = { x: 1, y: 1, direction: 'right' };
  score = 0;
  ghosts: {
    x: number;
    y: number;
    direction: string;
    startX: number;
    startY: number;
  }[] = [];
  powerMode = false;
  powerModeTimer: any;
  level = 1;
  gameLoop: any;
  gameResult: 'win' | 'lose' | null = null;
  private movementLoop: any;
  private gameActive = true;
  isDemoMode = false;
  errorMessage = '';
  startTime: Date | null = null;
  endTime: Date | null = null;
  duration = 0;
  gameOver = false;
  gameWon = false;
  gameStarted = false;

  @ViewChild('joystickArea') joystickArea!: ElementRef;
  @ViewChild('joystickHandle') joystickHandle!: ElementRef;

  joystickPosition = { left: '50%', top: '50%' };
  isJoystickActive = false;
  joystickCenter = { x: 0, y: 0 };

  // Add threshold for joystick movement
  private readonly JOYSTICK_THRESHOLD = 20;
  private readonly MOVEMENT_SPEED = 200; // ms between moves

  powerModeMessage = '';
  showPowerModeMessage = false;

  // Game controls configuration
  gameControls: GameControl[] = [
    { name: 'up', label: '', icon: 'chevron-up-outline' },
    { name: 'left', label: '', icon: 'chevron-back-outline' },
    { name: 'down', label: '', icon: 'chevron-down-outline' },
    { name: 'right', label: '', icon: 'chevron-forward-outline' },
  ];

  gameControls2: GameControl[] = [
    { name: 'restart', label: 'Restart', icon: 'refresh-outline' },
  ];

  constructor(private ngZone: NgZone, private cdr: ChangeDetectorRef) {
    this._config = { ...this.defaultConfig };
  }

  @HostListener('window:keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    if (!this.keyboardControls || this.gameState !== 'playing') return;

    const key = event.key.toLowerCase();
    let direction: string | null = null;

    switch (key) {
      case 'arrowleft':
      case 'a':
        direction = 'left';
        break;
      case 'arrowright': 
      case 'd':
        direction = 'right';
        break;
      case 'arrowup':
      case 'w':
        direction = 'up';
        break;
      case 'arrowdown':
      case 's':
        direction = 'down';
        break;
      case 'r':
        if (event.ctrlKey || event.metaKey) return; // Don't interfere with browser refresh
        this.restartGame();
        event.preventDefault();
        return;
      case 'p':
        if (this.allowPause) {
          this.togglePause();
          event.preventDefault();
        }
        return;
    }

    if (direction) {
      this.setPacmanDirection(direction);
      event.preventDefault();
    }
  }

  public togglePause(): void {
    if (!this.allowPause) return;
    
    if (this.gameState === 'playing') {
      this.gameState = 'paused';
      this.pauseGame();
      this.gamePause.emit({
        gameId: this.gameId || 'game-pacman',
        timestamp: new Date(),
        currentState: 'paused'
      });
    } else if (this.gameState === 'paused') {
      this.gameState = 'playing';
      this.resumeGame();
      this.gamePause.emit({
        gameId: this.gameId || 'game-pacman',
        timestamp: new Date(),
        currentState: 'resumed'
      });
    }
  }

  public handleControlClick(controlName: string): void {
    console.log('Control clicked:', controlName);
    if (controlName === 'restart') {
      this.restartGame();
      return;
    }

    this.setPacmanDirection(controlName);
  }

  private setPacmanDirection(direction: string): void {
    if (this.gameState === 'playing') {
      this.pacman.direction = direction;
    }
  }

  private pauseGame(): void {
    this.cleanupTimers();
    this.gameActive = false;
  }

  private resumeGame(): void {
    this.gameActive = true;
    this.startGameLoop();
    this.startContinuousMovement();
  }

  ngOnInit() {
    console.log('Initializing Pac-Man game with config:', this.config);

    if (this.game?.gameConfig) {
      const gameConfig = this.game.gameConfig.find(config =>
        config.hasOwnProperty('ghostSpeed') || config.hasOwnProperty('powerModeDuration')
      ) as unknown as {
        id: number;
        difficulty: string;
        frequency: string;
        frequencyAttempts: number;
        ghostSpeed: number;
        powerModeDuration: number;
        levels: number;
        gridSize: { width: number; height: number };
        pacmanSpeed: number;
        pointValues: { dot: number; powerPellet: number; ghost: number; fruit: number };
      };

      if (gameConfig) {
        console.log('Found game config:', gameConfig);
        this.config = {
          ...this.config,
          id: gameConfig.id ?? this.config.id,
          difficulty: (gameConfig.difficulty as 'EASY' | 'MEDIUM' | 'HARD') ?? this.config.difficulty,
          frequency: (gameConfig.frequency as 'DAILY' | 'WEEKLY' | 'MONTHLY') ?? this.config.frequency,
          frequencyAttempts: gameConfig.frequencyAttempts ?? this.config.frequencyAttempts,
          ghostSpeed: gameConfig.ghostSpeed ?? this.config.ghostSpeed,
          powerModeDuration: gameConfig.powerModeDuration ?? this.config.powerModeDuration,
          levels: gameConfig.levels ?? this.config.levels,
          gridSize: gameConfig.gridSize ?? this.config.gridSize,
          pacmanSpeed: gameConfig.pacmanSpeed ?? this.config.pacmanSpeed,
          pointValues: { ...this.config.pointValues, ...gameConfig.pointValues }
        };
      }
    }

    console.log('Final config before game init:', this.config);
    this.startTime = new Date();

    if (this.gameInstance) {
      try {
        // Try to load game state from gameInstance
        this.loadGameState();
      } catch (error) {
        console.error('Error loading game state:', error);
        this.initializeDemoMode();
      }
    } else {
      this.initializeDemoMode();
    }

    if (this.autoStart) {
      this.startGame();
    }
  }

  startGame(): void {
    console.log('Starting game');
    this.gameStarted = true;
    this.gameState = 'playing';
    this.startTime = new Date();

    // Emit game start event
    this.gameStart.emit({
      gameId: this.gameId || 'game-pacman',
      timestamp: new Date(),
      config: this.config
    });
  }

  private restartGame(): void {
    console.log('Restarting game');
    
    // Emit game end event if game was in progress
    if (this.gameStarted && !this.gameOver) {
      this.gameEnd.emit({
        gameId: this.gameId || 'game-pacman',
        timestamp: new Date(),
        score: this.score,
        level: this.level,
        duration: Math.floor((Date.now() - (this.startTime?.getTime() || 0)) / 1000),
        result: 'quit'
      });
    }

    this.cleanupTimers();
    this.initializeDemoMode();

    // Auto restart if enabled
    if (this.autoReset && this.autoStart) {
      setTimeout(() => this.startGame(), 100);
    }
  }

  ngOnDestroy() {
    // Clean up all timers and intervals
    this.cleanupTimers();
  }

  /**
   * Clean up all timers and intervals
   */
  cleanupTimers() {
    if (this.movementLoop) {
      clearInterval(this.movementLoop);
      this.movementLoop = null;
    }
    if (this.gameLoop) {
      clearInterval(this.gameLoop);
      this.gameLoop = null;
    }
    if (this.powerModeTimer) {
      clearTimeout(this.powerModeTimer);
      this.powerModeTimer = null;
    }
  }

  /**
   * Initialize the game in demo mode with default settings
   */
  initializeDemoMode() {
    console.log('Initializing Pac-Man game in demo mode');
    this.isDemoMode = true;
    this.errorMessage = 'Playing in demo mode';

    this.initializeGrid();
    this.initializeGhosts();
    this.startGameLoop();
    this.startContinuousMovement();

    this.emitGameEvent('game_start', {
      mode: 'demo',
      level: this.level,
      ghostSpeed: this.ghostSpeed
    });
  }

  /**
   * Load saved game state from gameInstance
   */
  loadGameState() {
    if (!this.gameInstance || !this.gameInstance.state) {
      throw new Error('No valid game state found');
    }

    const state = JSON.parse(this.gameInstance.state);
    this.level = state.level || 1;
    this.score = state.score || 0;
    this.ghostSpeed = state.ghostSpeed || this.config.ghostSpeed || 500;

    this.initializeGrid();

    // If there's a saved grid state, restore it
    if (state.grid) {
      this.restoreGridState(state.grid);
    }

    // If there's a saved pacman state, restore it
    if (state.pacman) {
      this.pacman = state.pacman;
    }

    // If there's a saved ghosts state, restore it
    if (state.ghosts) {
      this.ghosts = state.ghosts;
    }

    this.startGameLoop();
    this.startContinuousMovement();

    this.emitGameEvent('game_resume', {
      level: this.level,
      score: this.score,
      ghostSpeed: this.ghostSpeed
    });
  }

  /**
   * Restore the grid state from saved data
   */
  restoreGridState(savedGrid: number[][]) {
    if (!savedGrid || !Array.isArray(savedGrid) || savedGrid.length === 0) {
      return;
    }

    this.grid = savedGrid;
  }

  /**
   * Save the current game state
   */
  saveGameState() {
    if (this.isDemoMode) {
      console.log('Demo mode - game state not saved');
      return;
    }

    const state = {
      level: this.level,
      score: this.score,
      ghostSpeed: this.ghostSpeed,
      grid: this.grid,
      pacman: this.pacman,
      ghosts: this.ghosts
    };

    this.emitGameEvent('game_save', state);
  }

  /**
   * Emit a game event
   */
  emitGameEvent(type: string, data: any) {
    const event: PacManGameEvent = {
      type: type,
      data: data,
      timestamp: new Date().toISOString(),
      gameId: this.gameId
    };

    this.gameEventEmitter.emit(event);
    console.log('Pac-Man game event:', event);
  }

  resetGame() {
    this.cleanupTimers();

    this.score = 0;
    this.level = 1;
    this.ghostSpeed = this.config.ghostSpeed || 500;
    this.powerMode = false;
    this.pacman = { x: 1, y: 1, direction: 'right' };
    this.gameResult = null;
    this.startTime = new Date();
    this.endTime = null;

    this.gameActive = true;

    this.initializeGrid();
    this.initializeGhosts();
    this.startGameLoop();
    this.startContinuousMovement();

    this.emitGameEvent('game_reset', {
      level: this.level,
      score: this.score,
      ghostSpeed: this.ghostSpeed
    });
  }

  @HostListener('window:keydown', ['$event'])
  handleKeyDown(event: KeyboardEvent) {
    switch (event.key) {
      case 'ArrowUp':
        this.move('up');
        break;
      case 'ArrowDown':
        this.move('down');
        break;
      case 'ArrowLeft':
        this.move('left');
        break;
      case 'ArrowRight':
        this.move('right');
        break;
    }
  }

  @HostListener('touchstart', ['$event'])
  @HostListener('touchmove', ['$event'])
  handleTouch(event: TouchEvent) {
    if (!this.gameActive) return;

    event.preventDefault();
    const touch = event.touches[0];
    const gameArea = event.target as HTMLElement;
    const rect = gameArea.getBoundingClientRect();

    // Calculate touch position relative to center
    const touchX = touch.clientX - rect.left;
    const touchY = touch.clientY - rect.top;

    // Calculate angles between touch point and center
    const angle = Math.atan2(touchY - rect.height / 2, touchX - rect.width / 2);
    const degrees = angle * (180 / Math.PI);

    // Convert angle to direction
    let direction: string;
    if (degrees >= -45 && degrees < 45) {
      direction = 'right';
    } else if (degrees >= 45 && degrees < 135) {
      direction = 'down';
    } else if (degrees >= -135 && degrees < -45) {
      direction = 'up';
    } else {
      direction = 'left';
    }

    this.startContinuousMovement(direction);
  }

  @HostListener('touchend')
  handleTouchEnd() {
    this.stopContinuousMovement();
  }

  startContinuousMovement(direction?: string) {
    if (this.movementLoop) {
      clearInterval(this.movementLoop);
    }

    if (direction) {
      this.pacman.direction = direction;
    }

    this.movementLoop = setInterval(() => {
      this.ngZone.run(() => {
        this.move(this.pacman.direction);
      });
    }, this.MOVEMENT_SPEED);
  }

  stopContinuousMovement() {
    if (this.movementLoop) {
      clearInterval(this.movementLoop);
    }
  }

  initializeGrid() {
    this.gameResult = null;
    this.grid = [
      [1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
      [1, 0, 0, 3, 0, 0, 3, 0, 0, 1],
      [1, 0, 1, 1, 0, 0, 1, 1, 0, 1],
      [1, 3, 1, 0, 0, 0, 0, 1, 3, 1],
      [1, 0, 0, 0, 1, 1, 0, 0, 0, 1],
      [1, 0, 1, 0, 0, 0, 0, 1, 0, 1],
      [1, 0, 1, 1, 0, 0, 1, 1, 0, 1],
      [1, 0, 0, 3, 0, 0, 3, 0, 0, 1],
      [1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
    ];
  }

  initializeGhosts() {
    this.ghosts = [
      { x: 8, y: 1, direction: 'left', startX: 8, startY: 1 },
      { x: 1, y: 7, direction: 'right', startX: 1, startY: 7 },
    ];
  }

  startGameLoop() {
    this.gameLoop = setInterval(() => {
      this.moveGhosts();
    }, this.ghostSpeed);
  }

  move(direction: string) {
    if (!this.gameActive) return;

    let newX = this.pacman.x;
    let newY = this.pacman.y;

    // Fix the direction mapping
    switch (direction) {
      case 'right':
        newX++;
        break;
      case 'left':
        newX--;
        break;
      case 'down':
        newY++;
        break;
      case 'up':
        newY--;
        break;
    }

    // Check if new position is valid and within bounds
    if (
      this.grid[newY] &&
      this.grid[newY][newX] !== undefined &&
      this.grid[newY][newX] !== 1
    ) {
      const oldX = this.pacman.x;
      const oldY = this.pacman.y;

      this.pacman.x = newX;
      this.pacman.y = newY;

      // Emit move event
      this.emitGameEvent('pacman_move', {
        from: { x: oldX, y: oldY },
        to: { x: newX, y: newY },
        direction: direction
      });

      if (this.grid[newY][newX] === 0) {
        this.score += 10;
        this.grid[newY][newX] = 2; // Mark as eaten

        // Emit dot eaten event
        this.emitGameEvent('dot_eaten', {
          position: { x: newX, y: newY },
          score: this.score
        });
      } else if (this.grid[newY][newX] === 3) {
        this.score += 50;
        this.grid[newY][newX] = 2; // Mark as eaten

        // Emit power pellet eaten event
        this.emitGameEvent('power_pellet_eaten', {
          position: { x: newX, y: newY },
          score: this.score
        });

        this.activatePowerMode();
      }

      // Save game state after each move
      this.saveGameState();

      if (this.checkLevelComplete()) {
        this.nextLevel();
      }
    }
  }

  activatePowerMode() {
    this.powerMode = true;
    this.showPowerModeMessage = true;
    this.powerModeMessage = 'Power Mode Activated! Chase the ghosts!';

    // Emit power mode activated event
    this.emitGameEvent('power_mode_activated', {
      duration: this.powerModeDuration
    });

    // Hide message after 2 seconds
    setTimeout(() => {
      this.showPowerModeMessage = false;
    }, 2000);

    clearTimeout(this.powerModeTimer);
    this.powerModeTimer = setTimeout(() => {
      this.powerMode = false;
      this.showPowerModeMessage = true;
      this.powerModeMessage = 'Power Mode Ended! Watch out!';

      // Emit power mode ended event
      this.emitGameEvent('power_mode_ended', {
        score: this.score
      });

      // Hide message after 2 seconds
      setTimeout(() => {
        this.showPowerModeMessage = false;
      }, 2000);
    }, this.powerModeDuration);
  }

  moveGhosts() {
    this.ghosts.forEach((ghost, index) => {
      const directions = ['up', 'down', 'left', 'right'];
      const possibleDirections = directions.filter((dir) => {
        const { x, y } = this.getNewPosition(ghost.x, ghost.y, dir);
        return this.grid[y] && this.grid[y][x] !== 1;
      });

      if (possibleDirections.length > 0) {
        ghost.direction =
          possibleDirections[
            Math.floor(Math.random() * possibleDirections.length)
          ];
      }

      const oldX = ghost.x;
      const oldY = ghost.y;
      const { x, y } = this.getNewPosition(ghost.x, ghost.y, ghost.direction);

      if (this.grid[y] && this.grid[y][x] !== 1) {
        ghost.x = x;
        ghost.y = y;

        // Emit ghost move event
        this.emitGameEvent('ghost_move', {
          ghostIndex: index,
          from: { x: oldX, y: oldY },
          to: { x, y },
          direction: ghost.direction
        });
      }

      // Check for collision with Pacman
      if (ghost.x === this.pacman.x && ghost.y === this.pacman.y) {
        if (this.powerMode) {
          // Eat ghost
          this.score += 200;
          this.showPowerModeMessage = true;
          this.powerModeMessage = 'Ghost eaten! +200 points!';

          // Emit ghost eaten event
          this.emitGameEvent('ghost_eaten', {
            ghostIndex: index,
            position: { x: ghost.x, y: ghost.y },
            scoreBonus: 200,
            totalScore: this.score
          });

          // Hide message after 1 second
          setTimeout(() => {
            this.showPowerModeMessage = false;
          }, 1000);

          // Remove the ghost temporarily
          this.ghosts.splice(index, 1);

          // Respawn the ghost after a delay
          setTimeout(() => {
            this.respawnGhost(ghost);
          }, 5000);
        } else {
          // Game over if not in power mode
          this.endGame('lose');
        }
      }
    });

    // Save game state after ghost movement
    this.saveGameState();
  }

  respawnGhost(ghost: {
    x: number;
    y: number;
    direction: string;
    startX: number;
    startY: number;
  }) {
    // Reset ghost to its starting position
    ghost.x = ghost.startX;
    ghost.y = ghost.startY;
    this.ghosts.push(ghost);

    // Emit ghost respawn event
    this.emitGameEvent('ghost_respawn', {
      position: { x: ghost.startX, y: ghost.startY }
    });
  }

  getNewPosition(x: number, y: number, direction: string) {
    switch (direction) {
      case 'up':
        return { x, y: y - 1 };
      case 'down':
        return { x, y: y + 1 };
      case 'left':
        return { x: x - 1, y };
      case 'right':
        return { x: x + 1, y };
    }
    return { x, y };
  }

  private endGame(result: 'win' | 'lose'): void {
    this.gameOver = true;
    this.gameWon = result === 'win';
    this.gameState = result === 'win' ? 'completed' : 'failed';
    this.gameResult = result;
    this.gameActive = false;
    this.endTime = new Date();
    this.duration = Math.floor((this.endTime.getTime() - (this.startTime?.getTime() || 0)) / 1000);

    // Emit game end event
    this.gameEnd.emit({
      gameId: this.gameId || 'game-pacman',
      timestamp: new Date(),
      score: this.score,
      level: this.level,
      duration: this.duration,
      result: result
    });

    this.stopContinuousMovement();
    this.cleanupTimers();

    this.showPowerModeMessage = true;
    this.powerModeMessage = result === 'win' ? 'You Win!' : 'Game Over! Try again!';

    // Calculate game duration in seconds
    const duration = this.endTime.getTime() - (this.startTime?.getTime() || 0);

    this.emitGameEvent('game_end', {
      result: result,
      score: this.score,
      level: this.level,
      duration: Math.floor(duration / 1000),
      finalState: {
        pacman: this.pacman,
        ghosts: this.ghosts,
        grid: this.grid
      }
    });

    // Save high score if applicable
    this.saveGameProgress();

    // Auto restart if enabled
    if (this.autoReset && this.autoStart) {
      setTimeout(() => this.restartGame(), 2000);
    }
  }
    let duration = 0;
    if (this.startTime && this.endTime) {
      duration = Math.floor((this.endTime.getTime() - this.startTime.getTime()) / 1000);
    }

    // Emit game over event
    this.emitGameEvent('game_over', {
      score: this.score,
      level: this.level,
      duration: duration
    });
  }

  onScreenControlClick(direction: string) {
    this.startContinuousMovement(direction);
  }

  checkLevelComplete() {
    for (let row of this.grid) {
      for (let cell of row) {
        if (cell === 0 || cell === 3) {
          return false;
        }
      }
    }
    this.gameActive = false;
    this.stopContinuousMovement();

    // Emit level complete event
    this.emitGameEvent('level_complete', {
      level: this.level,
      score: this.score
    });

    return true;
  }

  nextLevel() {
    this.level++;
    this.gameResult = null;
    this.initializeGrid();
    this.initializeGhosts();
    this.increaseDifficulty();

    // Restart the game loop with updated difficulty
    if (this.gameLoop) {
      clearInterval(this.gameLoop);
    }
    this.startGameLoop();

    // Emit next level event
    this.emitGameEvent('next_level', {
      level: this.level,
      ghostSpeed: this.ghostSpeed,
      score: this.score
    });

    // Check if all levels are completed
    if (this.level > (this.config.levels || 5)) {
      this.gameComplete();
    }
  }

  gameComplete() {
    this.gameActive = false;
    this.endTime = new Date();
    this.gameResult = 'win';
    this.stopContinuousMovement();
    this.cleanupTimers();

    this.showPowerModeMessage = true;
    this.powerModeMessage = 'Congratulations! You completed all levels!';

    // Calculate game duration in seconds
    let duration = 0;
    if (this.startTime && this.endTime) {
      duration = Math.floor((this.endTime.getTime() - this.startTime.getTime()) / 1000);
    }

    // Emit game complete event
    this.emitGameEvent('game_complete', {
      score: this.score,
      levels: this.level,
      duration: duration
    });
  }

  increaseDifficulty() {
    if (this.ghostSpeed > 100) {
      this.ghostSpeed -= 50; // Increase ghost speed
    } else {
      this.ghostSpeed = 100; // Minimum speed limit
    }

    // Emit difficulty increased event
    this.emitGameEvent('difficulty_increased', {
      level: this.level,
      ghostSpeed: this.ghostSpeed
    });
  }

  initJoystick(event: MouseEvent | TouchEvent) {
    event.preventDefault();
    this.isJoystickActive = true;
    const rect = this.joystickArea.nativeElement.getBoundingClientRect();
    this.joystickCenter = {
      x: rect.left + rect.width / 2,
      y: rect.top + rect.height / 2,
    };
  }

  moveJoystick(event: MouseEvent | TouchEvent) {
    if (!this.isJoystickActive) return;
    event.preventDefault();

    const clientX =
      event instanceof MouseEvent ? event.clientX : event.touches[0].clientX;
    const clientY =
      event instanceof MouseEvent ? event.clientY : event.touches[0].clientY;

    const deltaX = clientX - this.joystickCenter.x;
    const deltaY = clientY - this.joystickCenter.y;

    // Only change direction if movement is greater than threshold
    if (
      Math.abs(deltaX) < this.JOYSTICK_THRESHOLD &&
      Math.abs(deltaY) < this.JOYSTICK_THRESHOLD
    ) {
      return;
    }

    // Calculate joystick position
    const maxDistance = 50;
    const distance = Math.min(
      Math.sqrt(deltaX * deltaX + deltaY * deltaY),
      maxDistance
    );
    const angle = Math.atan2(deltaY, deltaX);

    // Update visual joystick position
    const newX = 50 + (Math.cos(angle) * distance * 100) / maxDistance;
    const newY = 50 + (Math.sin(angle) * distance * 100) / maxDistance;

    this.joystickPosition = {
      left: `${newX}%`,
      top: `${newY}%`,
    };

    // Determine direction based on angle
    const degrees = angle * (180 / Math.PI);
    let newDirection: string;

    // Fix direction mapping
    if (degrees > -45 && degrees <= 45) {
      newDirection = 'right';
    } else if (degrees > 45 && degrees <= 135) {
      newDirection = 'down';
    } else if (degrees > 135 || degrees <= -135) {
      newDirection = 'left';
    } else {
      newDirection = 'up';
    }

    // Update pacman direction and ensure continuous movement
    if (this.pacman.direction !== newDirection) {
      this.pacman.direction = newDirection;
      if (!this.movementLoop) {
        this.startContinuousMovement();
      }
    }
  }

  stopJoystick() {
    this.isJoystickActive = false;
    this.joystickPosition = { left: '50%', top: '50%' };
    // Don't stop movement, just reset joystick position
  }

  // Add touch event handlers to the joystick area
  @HostListener('touchstart', ['$event'])
  onTouchStart(event: TouchEvent) {
    event.preventDefault();
    this.isJoystickActive = true;
    const rect = this.joystickArea.nativeElement.getBoundingClientRect();
    this.joystickCenter = {
      x: rect.left + rect.width / 2,
      y: rect.top + rect.height / 2,
    };
  }

  @HostListener('touchmove', ['$event'])
  onTouchMove(event: TouchEvent) {
    if (!this.isJoystickActive) return;
    event.preventDefault();

    const touch = event.touches[0];
    const deltaX = touch.clientX - this.joystickCenter.x;
    const deltaY = touch.clientY - this.joystickCenter.y;

    // Calculate joystick position
    const maxDistance = 50;
    const distance = Math.min(
      Math.sqrt(deltaX * deltaX + deltaY * deltaY),
      maxDistance
    );
    const angle = Math.atan2(deltaY, deltaX);

    // Update visual joystick position
    const newX = 50 + (Math.cos(angle) * distance * 100) / maxDistance;
    const newY = 50 + (Math.sin(angle) * distance * 100) / maxDistance;

    this.joystickPosition = {
      left: `${newX}%`,
      top: `${newY}%`,
    };

    // Determine direction based on angle
    if (
      Math.abs(deltaX) < this.JOYSTICK_THRESHOLD &&
      Math.abs(deltaY) < this.JOYSTICK_THRESHOLD
    ) {
      return;
    }

    // Convert angle to direction
    const degrees = angle * (180 / Math.PI);
    let newDirection: string;

    if (degrees > -45 && degrees <= 45) {
      newDirection = 'right';
    } else if (degrees > 45 && degrees <= 135) {
      newDirection = 'down';
    } else if (degrees > 135 || degrees <= -135) {
      newDirection = 'left';
    } else {
      newDirection = 'up';
    }

    // Only update direction if it changed
    if (this.pacman.direction !== newDirection) {
      this.pacman.direction = newDirection;
    }
  }

  @HostListener('touchend')
  onTouchEnd() {
    this.isJoystickActive = false;
    this.joystickPosition = { left: '50%', top: '50%' };
  }
}
