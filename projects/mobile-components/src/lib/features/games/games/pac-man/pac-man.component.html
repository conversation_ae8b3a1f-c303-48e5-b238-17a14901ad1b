<div class="game-container flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800">
  <!-- Game Header -->
  <div class="game-title text-center p-4">
    <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500">Pac-Man</h1>
    <p class="text-xs text-slate-400 mb-1" *ngIf="isDemoMode">Playing in demo mode</p>
  </div>

  <!-- Score section -->
  <div class="score-container px-4">
    <games-score
      [scores]="[
        { title: 'Score', number: score },
        { title: 'Level', number: level },
        { title: powerMode ? 'Power ⚡' : 'Power', number: powerMode ? 1 : 0 }
      ]"
    ></games-score>
  </div>

  <!-- Game board -->
  <div class="flex-1 overflow-auto p-4">
    <div class="game-board p-4 rounded-xl bg-slate-700/50 border border-slate-600 shadow-xl mx-auto max-w-md">
      <div class="board-grid">
        <div class="board-row" *ngFor="let row of grid; let y = index">
          <div
            class="board-cell"
            *ngFor="let cell of row; let x = index"
            [ngClass]="{
              'wall': cell === 1,
              'path': cell !== 1,
              'animate-pulse': cell === 3
            }"
          >
            <div
              *ngIf="x === pacman.x && y === pacman.y"
              class="character pacman"
              [ngClass]="{'animate-pulse': powerMode}"
            >
              🟡
            </div>
            <div *ngIf="cell === 0" class="dot"></div>
            <div *ngIf="cell === 3" class="power-pellet">👾</div>
            <ng-container *ngFor="let ghost of ghosts">
              <div
                *ngIf="x === ghost.x && y === ghost.y"
                class="character ghost"
                [ngClass]="{'ghost-vulnerable': powerMode}"
              >
                {{ powerMode ? '😱' : '👻' }}
              </div>
            </ng-container>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Game Controls -->
  <div class="controls-container">
    <div
      class="joystick-base"
      #joystickArea
      (touchstart)="initJoystick($event)"
      (touchmove)="moveJoystick($event)"
      (touchend)="stopJoystick()"
      (mousedown)="initJoystick($event)"
      (mousemove)="moveJoystick($event)"
      (mouseup)="stopJoystick()"
      (mouseleave)="stopJoystick()"
    >
      <div
        class="joystick-handle"
        #joystickHandle
        [style.left]="joystickPosition.left"
        [style.top]="joystickPosition.top"
      ></div>
    </div>
  </div>

  <!-- Game Result -->
  <div *ngIf="gameResult" class="game-result">
    <div class="game-result-content">
      <h2 class="text-2xl font-bold mb-2">
        {{ gameResult === 'win' ? 'You Win!' : 'Game Over!' }}
      </h2>
      <p class="mb-4">Score: {{ score }}</p>
      <button
        class="px-4 py-2 bg-gradient-to-br from-yellow-500 to-orange-600 text-white rounded-lg hover:from-yellow-600 hover:to-orange-700 shadow-md transition-all duration-200 transform hover:scale-105"
        (click)="resetGame()"
      >
        Play Again
      </button>
    </div>
  </div>

  <!-- Power Mode Message -->
  <div
    *ngIf="showPowerModeMessage"
    class="power-mode-message"
  >
    {{ powerModeMessage }}
  </div>
</div>
