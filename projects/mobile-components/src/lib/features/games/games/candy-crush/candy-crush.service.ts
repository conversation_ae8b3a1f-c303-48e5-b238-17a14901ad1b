import { Injectable } from '@angular/core';
import { Candy } from './candy.model';

@Injectable({
  providedIn: 'root',
})
export class CandyCrushService {
  private candyTypes = ['red', 'blue', 'green', 'yellow', 'purple', 'orange'];
  private difficulty = 1;

  createBoard(rows: number = 8, cols: number = 8): Candy[][] {
    const board: Candy[][] = [];
    for (let i = 0; i < rows; i++) {
      board[i] = [];
      for (let j = 0; j < cols; j++) {
        board[i][j] = this.generateCandy();
      }
    }
    return board;
  }

  generateCandy(): Candy {
    const randomType =
      this.candyTypes[Math.floor(Math.random() * this.candyTypes.length)];
    return new Candy(randomType);
  }

  increaseDifficulty() {
    this.difficulty++;
    // Add logic to increase difficulty (e.g., reduce available moves, add obstacles, etc.)
  }

  // Add more methods for game logic as needed
}
