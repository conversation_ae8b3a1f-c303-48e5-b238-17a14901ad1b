/* Import shared game styles */
@import '../styles/game-styles.css';

:host {
  @apply block w-full h-full;
}

.game-container {
  @apply flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800;
  position: relative;
}

.game-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.05),
    rgba(255, 255, 255, 0.05) 20px,
    transparent 20px,
    transparent 40px
  );
  animation: move 20s linear infinite;
  z-index: 0;
  pointer-events: none;
}

@keyframes move {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 282px 0;
  }
}

/* Game Title */
.game-title {
  @apply text-center mb-1;
}

.game-title h1 {
  @apply text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-purple-500;
}

.game-title p {
  @apply text-xs text-slate-400 mb-1;
}

/* Game Board Container */
.game-board-container {
  @apply flex-grow flex justify-center items-center p-4;
  position: relative;
  z-index: 1;
  max-height: 70vh;
  overflow-y: auto;
}

.game-board {
  @apply p-4 rounded-xl bg-slate-700/50 border border-slate-600 shadow-xl;
  min-width: min-content;
  max-width: 100%;
  overflow: auto;
  position: relative;
  display: grid;
  grid-template-columns: repeat(8, minmax(40px, 50px));
  grid-template-rows: repeat(8, minmax(40px, 50px));
  grid-gap: 4px;
  backdrop-filter: blur(5px);
  z-index: 1;
}

.game-board::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-purple-500/10;
  filter: blur(20px);
  z-index: 0;
}

.candy {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  will-change: transform, opacity;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
}

.candy::before {
  content: '';
  position: absolute;
  inset: 0;
  opacity: 0.7;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
  z-index: 1;
}

.candy::after {
  content: '';
  position: absolute;
  inset: 0;
  opacity: 0;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
  z-index: 2;
  transition: opacity 0.3s ease;
}

.candy:hover {
  transform: scale(1.05);
}

.candy:hover::after {
  opacity: 0.5;
}

/* Animation states */
.candy.selected {
  transform: scale(1.1);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.candy.matched {
  transform: scale(0);
  opacity: 0;
  transition: transform 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55), opacity 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.candy.new {
  animation: popIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

@keyframes popIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  70% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.red {
  background: radial-gradient(circle at center, #ff6b63, #e60000);
  border: 3px solid #ff4136;
  box-shadow: 0 0 15px rgba(255, 65, 54, 0.5);
}

.blue {
  background: radial-gradient(circle at center, #3498db, #004b8c);
  border: 3px solid #0074d9;
  box-shadow: 0 0 15px rgba(0, 116, 217, 0.5);
}

.green {
  background: radial-gradient(circle at center, #5aed6c, #1b7926);
  border: 3px solid #2ecc40;
  box-shadow: 0 0 15px rgba(46, 204, 64, 0.5);
}

.yellow {
  background: radial-gradient(circle at center, #ffe433, #e6c700);
  border: 3px solid #ffdc00;
  box-shadow: 0 0 15px rgba(255, 220, 0, 0.5);
}

.purple {
  background: radial-gradient(circle at center, #d43cef, #7b0a8c);
  border: 3px solid #b10dc9;
  box-shadow: 0 0 15px rgba(177, 13, 201, 0.5);
}

.orange {
  background: radial-gradient(circle at center, #ffa54e, #e67300);
  border: 3px solid #ff851b;
  box-shadow: 0 0 15px rgba(255, 133, 27, 0.5);
}

/* Controls */
.controls {
  @apply flex justify-center p-4;
  position: relative;
  z-index: 1;
}

/* Confetti Animation for Win */
@keyframes confetti-fall {
  0% {
    transform: translateY(-100%) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(360deg);
    opacity: 0;
  }
}

/* Win Animation */
@keyframes winAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.win-animation {
  background: linear-gradient(
    -45deg,
    #ff7676,
    #ff7676,
    #ffb56b,
    #ffff76,
    #76ff76,
    #76ffff,
    #7676ff,
    #ff76ff,
    #ff7676
  );
  background-size: 400% 400%;
  animation: winAnimation 15s ease infinite;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .game-board {
    grid-template-columns: repeat(8, minmax(35px, 45px));
  }

  .candy {
    height: 40px;
  }
}
