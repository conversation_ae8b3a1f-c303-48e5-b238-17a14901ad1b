import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, Input, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import { CandyCrushService } from './candy-crush.service';
import { Candy } from './candy.model';
import { CommonModule, NgFor, NgIf } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { WinLoseOverlayComponent } from '../components/win-lose-overlay/win-lose-overlay.component';
import { GameControl, GamesPlayButtonComponent } from '../components/games-play-button/games-play-button.component';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { Game, GameEvent } from 'lp-client-api';

@Component({
  selector: 'app-candy-crush',
  templateUrl: './candy-crush.component.html',
  styleUrls: ['./candy-crush.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    NgFor,
    NgIf,
    GamesScoreComponent,
    WinLoseOverlayComponent,
    GamesPlayButtonComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class CandyCrushComponent implements OnInit, OnDestroy {
  @Input() gameId: string = '';
  @Input() config: any = null;
  @Input() gameInstance: any = null;
  @Input() game: Game | null = null;
  @Output() gameEvent = new EventEmitter<GameEvent>();

  private destroy$ = new Subject<void>();
  private animationInProgress = false;

  // Game state
  board: Candy[][] = [];
  score: number = 0;
  highScore: number = 0;
  moves: number = 0;
  level: number = 1;
  selectedCandy: { row: number; col: number } | null = null;
  gameResult: 'win' | 'lose' | null = null;
  targetScore: number = 1000;
  movesLimit: number = 30;
  showWinAnimation: boolean = false;
  newCandies: { row: number; col: number }[] = [];

  // Game controls
  gameControls: GameControl[] = [
    { name: 'reset', label: 'Reset', icon: 'refresh-outline', color: 'blue' }
  ];

  constructor(
    private candyCrushService: CandyCrushService,
    private cdr: ChangeDetectorRef
  ) {
    // Load high score from localStorage
    const savedHighScore = localStorage.getItem('candyCrushHighScore');
    if (savedHighScore) {
      this.highScore = parseInt(savedHighScore, 10);
    }
  }

  ngOnInit() {
    // Initialize the game
    this.initializeGame();

    // Apply configuration if available
    if (this.config) {
      if (this.config.boardSize) {
        // Use board size from config
        this.board = this.candyCrushService.createBoard(
          this.config.boardSize.width || 8,
          this.config.boardSize.height || 8
        ).map(row => row.map(candy => ({ ...candy, state: 'normal' })));
      }

      if (this.config.targetScore) {
        this.targetScore = this.config.targetScore;
      }

      if (this.config.movesLimit) {
        this.movesLimit = this.config.movesLimit;
      }
    }

    // Log inputs for debugging
    console.log('CandyCrush component initialized with:', {
      gameId: this.gameId,
      config: this.config,
      gameInstance: this.gameInstance,
      game: this.game
    });

    // Emit game started event
    this.emitGameEvent('start');
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  initializeGame() {
    this.board = this.candyCrushService
      .createBoard()
      .map((row) => row.map((candy) => ({ ...candy, state: 'normal' })));
    this.score = 0;
    this.moves = 0;
    this.level = 1;
    this.selectedCandy = null;
    this.gameResult = null;
    this.targetScore = this.level * 1000;
    this.movesLimit = 30 + (this.level - 1) * 5;
    this.showWinAnimation = false;
    this.newCandies = [];
    this.animationInProgress = false;
  }

  // Handle control clicks from the games-play-button component
  handleControlClick(controlName: string) {
    switch (controlName) {
      case 'reset':
        this.restartGame();
        break;
      default:
        break;
    }
  }

  restartGame() {
    this.initializeGame();
    this.emitGameEvent('restart');
  }

  selectCandy(row: number, col: number) {
    // Prevent interactions during animations or when game is over
    if (this.animationInProgress || this.gameResult) {
      return;
    }

    if (this.selectedCandy) {
      if (this.isAdjacent(this.selectedCandy, { row, col })) {
        this.swapCandies(this.selectedCandy, { row, col });
        this.selectedCandy = null;
      } else {
        this.board[this.selectedCandy.row][this.selectedCandy.col].state = 'normal';
        this.selectedCandy = { row, col };
        this.board[row][col].state = 'selected';
        this.cdr.detectChanges();
      }
    } else {
      this.selectedCandy = { row, col };
      this.board[row][col].state = 'selected';
      this.cdr.detectChanges();
    }
  }

  // Handle animation transitions using CSS classes
  handleAnimationTransition(row: number, col: number, state: string) {
    // Handle matched candies
    if (state === 'matched') {
      // Set a timeout to simulate the animation completion
      setTimeout(() => {
        if (row >= 0 && row < this.board.length && col >= 0 && col < this.board[row].length) {
          this.board[row][col].state = 'normal';
        }
      }, 400); // Match the CSS transition duration
    }

    // Handle new candies
    if (state === 'new') {
      // Set a timeout to simulate the animation completion
      setTimeout(() => {
        if (this.newCandies.some(candy => candy.row === row && candy.col === col)) {
          this.newCandies = this.newCandies.filter(candy => !(candy.row === row && candy.col === col));

          if (this.newCandies.length === 0) {
            this.animationInProgress = false;
            this.checkMatches(); // Check for new matches after animations complete
          }
        }
      }, 400); // Match the CSS animation duration
    }
  }

  isAdjacent(
    candy1: { row: number; col: number },
    candy2: { row: number; col: number }
  ): boolean {
    const rowDiff = Math.abs(candy1.row - candy2.row);
    const colDiff = Math.abs(candy1.col - candy2.col);
    return (rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1);
  }

  swapCandies(
    candy1: { row: number; col: number },
    candy2: { row: number; col: number }
  ) {
    this.animationInProgress = true;

    // Swap the candies in the board array
    const temp = this.board[candy1.row][candy1.col];
    this.board[candy1.row][candy1.col] = this.board[candy2.row][candy2.col];
    this.board[candy2.row][candy2.col] = temp;

    this.moves++;
    this.cdr.detectChanges();

    // Check for matches after a short delay to allow the swap animation to complete
    setTimeout(() => {
      this.checkMatches();
    }, 300);
  }

  checkMatches() {
    if (this.gameResult) return; // Don't check matches if game is over

    let matches: { row: number; col: number }[] = [];

    // Check horizontal matches
    for (let row = 0; row < this.board.length; row++) {
      for (let col = 0; col < this.board[row].length - 2; col++) {
        if (
          this.board[row][col].type === this.board[row][col + 1].type &&
          this.board[row][col].type === this.board[row][col + 2].type &&
          this.board[row][col].type !== ''
        ) {
          matches.push(
            { row, col },
            { row, col: col + 1 },
            { row, col: col + 2 }
          );
        }
      }
    }

    // Check vertical matches
    for (let row = 0; row < this.board.length - 2; row++) {
      for (let col = 0; col < this.board[row].length; col++) {
        if (
          this.board[row][col].type === this.board[row + 1][col].type &&
          this.board[row][col].type === this.board[row + 2][col].type &&
          this.board[row][col].type !== ''
        ) {
          matches.push(
            { row, col },
            { row: row + 1, col },
            { row: row + 2, col }
          );
        }
      }
    }

    // Remove duplicates from matches
    matches = matches.filter((match, index, self) =>
      index === self.findIndex((m) => m.row === match.row && m.col === match.col)
    );

    if (matches.length > 0) {
      this.animationInProgress = true;
      this.markMatchedCandies(matches);

      // Wait for match animation to complete before filling empty spaces
      setTimeout(() => {
        this.removeMatches(matches);
        this.fillEmptySpaces();

        const pointsEarned = matches.length * 10;
        this.score += pointsEarned;
        this.emitGameEvent('score', { score: this.score });

        // Update high score if needed
        if (this.score > this.highScore) {
          this.highScore = this.score;
          localStorage.setItem('candyCrushHighScore', this.highScore.toString());
        }

        // Check for win/lose conditions
        this.checkGameStatus();
      }, 500);
    } else {
      this.animationInProgress = false;

      if (this.moves >= this.movesLimit) {
        // Out of moves
        this.endGame('lose');
      }
    }
  }

  markMatchedCandies(matches: { row: number; col: number }[]) {
    matches.forEach(match => {
      if (this.board[match.row][match.col].type !== '') {
        this.board[match.row][match.col].state = 'matched';
        this.handleAnimationTransition(match.row, match.col, 'matched');
      }
    });
    this.cdr.detectChanges();
  }

  checkGameStatus() {
    // Win condition: reached target score
    if (this.score >= this.targetScore) {
      this.endGame('win');
    }
    // Lose condition: out of moves
    else if (this.moves >= this.movesLimit) {
      this.endGame('lose');
    }
  }

  endGame(result: 'win' | 'lose') {
    this.gameResult = result;
    this.showWinAnimation = result === 'win';

    // Add win animation class to container if win
    if (result === 'win') {
      const container = document.querySelector('.game-container');
      if (container) {
        container.classList.add('win-animation');
      }
    }

    // Emit game event
    this.emitGameEvent(result === 'win' ? 'win' : 'lose', {
      score: this.score,
      level: this.level
    });

    if (result === 'win') {
      // Prepare for next level if won
      setTimeout(() => {
        if (this.gameResult === 'win') {
          // Remove win animation class
          const container = document.querySelector('.game-container');
          if (container) {
            container.classList.remove('win-animation');
          }

          this.level++;
          this.initializeGame();
        }
      }, 3000);
    }
  }

  removeMatches(matches: { row: number; col: number }[]) {
    matches.forEach((match) => {
      this.board[match.row][match.col] = { type: '', state: 'normal' };
    });
    this.cdr.detectChanges();
  }

  fillEmptySpaces() {
    this.newCandies = []; // Clear previous new candies

    for (let col = 0; col < this.board[0].length; col++) {
      let emptySpaces = 0;
      for (let row = this.board.length - 1; row >= 0; row--) {
        if (this.board[row][col].type === '') {
          emptySpaces++;
        } else if (emptySpaces > 0) {
          this.board[row + emptySpaces][col] = this.board[row][col];
          this.board[row][col] = new Candy('');
        }
      }

      // Add new candies at the top with 'new' state for animation
      for (let row = 0; row < emptySpaces; row++) {
        const newCandy = this.candyCrushService.generateCandy();
        newCandy.state = 'new';
        this.board[row][col] = newCandy;
        this.newCandies.push({ row, col });
        this.handleAnimationTransition(row, col, 'new');
      }
    }

    // If no new candies were added, make sure we're not stuck in animation state
    if (this.newCandies.length === 0) {
      this.animationInProgress = false;
    }

    this.cdr.detectChanges();
  }

  // Emit game events to parent component
  emitGameEvent(eventType: string, data: any = {}) {
    // Create a GameEvent object according to the interface
    const gameEvent: GameEvent = {
      id: 0, // This will be set by the backend
      score: this.score,
      level: this.level,
      duration: 0, // Calculate duration if needed
      state: eventType, // Use the event type as state
      payload: JSON.stringify({
        ...data,
        highScore: this.highScore,
        moves: this.moves,
        gameId: this.gameId
      })
    };

    this.gameEvent.emit(gameEvent);
  }
}
