<div class="game-container">
  <!-- Score Display -->
  <games-score
    [scores]="[
      { title: 'Score', number: score },
      { title: 'High Score', number: highScore },
      { title: 'Level', number: level },
      { title: 'Moves', number: moves + '/' + movesLimit }
    ]"
  ></games-score>

  <!-- Game Title -->
  <div class="game-title">
    <h1>Candy Crush</h1>
    <p>Match 3 or more candies in a row!</p>
  </div>

  <!-- Game Board Container -->
  <div class="game-board-container">
    <div class="game-board">
      <ng-container *ngFor="let row of board; let i = index">
        <div
          *ngFor="let candy of row; let j = index"
          class="candy {{ candy.type }} {{ candy.state || 'normal' }}"
          (click)="selectCandy(i, j)"
          [style.grid-row]="i + 1"
          [style.grid-column]="j + 1"
        ></div>
      </ng-container>
    </div>
  </div>

  <!-- Game Controls -->
  <div class="controls">
    <lib-games-play-button
      [controls]="gameControls"
      (controlClicked)="handleControlClick($event)"
    ></lib-games-play-button>
  </div>

  <!-- Win/Lose Overlay -->
  <lib-win-lose-overlay
    *ngIf="gameResult"
    [status]="gameResult"
    (restart)="restartGame()"
    [message]="gameResult === 'win' ? 'Level ' + level + ' completed! Score: ' + score : 'Game Over! Your score: ' + score"
  ></lib-win-lose-overlay>
</div>
