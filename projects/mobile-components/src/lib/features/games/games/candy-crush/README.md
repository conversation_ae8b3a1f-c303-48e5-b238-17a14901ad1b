# Candy Crush Game

## Overview

Candy Crush is a match-three puzzle game where players swap adjacent candies to create matches of three or more of the same color. The game features colorful candies, level progression, and score tracking.

## Game Rules

1. **Objective**: Match 3 or more candies of the same color in a row or column
2. **Controls**: Click on a candy and then click on an adjacent candy to swap them
3. **Scoring**: Each match earns points based on the number of candies matched
4. **Win Condition**: Reach the target score for the current level
5. **Lose Condition**: Run out of moves before reaching the target score

## Features

- Modern UI with gradient backgrounds and candy styling
- Level progression with increasing difficulty
- Score tracking with high score persistence
- Responsive design for different screen sizes
- Win/lose overlay with game results

## UI Components

The Candy Crush game uses several shared components:

1. **GamesScoreComponent**: Displays score, high score, level, and moves information
2. **GamesPlayButtonComponent**: Provides game control buttons
3. **WinLoseOverlayComponent**: Shows game over screen with win/lose message

## Technical Implementation

### Component Structure

The Candy Crush game is implemented as a standalone Angular component with the following files:

- `candy-crush.component.ts`: Game logic and state management
- `candy-crush.component.html`: Game UI template
- `candy-crush.component.css`: Game-specific styling
- `candy.model.ts`: Candy model definition
- `candy-crush.service.ts`: Service for board generation and game logic

### Key Features

1. **Modern UI Design**:
   - Gradient backgrounds and borders
   - Rounded corners and shadows
   - Consistent color scheme with other games
   - Responsive layout for different screen sizes

2. **Game Board**:
   - 8x8 grid of colorful candies
   - Animated candy swapping
   - Match detection for horizontal and vertical matches
   - Gravity effect for filling empty spaces

3. **Game State Management**:
   - Score tracking with high score persistence
   - Level progression with increasing difficulty
   - Move limit with remaining moves display
   - Win/lose conditions

4. **Animations**:
   - Candy selection animation
   - Match and clear animations
   - Board refill animations

## Integration

The Candy Crush game is registered in the game system with the code `candy-crush` and can be accessed through:

1. The All Games View
2. Direct URL: `/games/single?game=candy-crush`

## Future Enhancements

Potential improvements for future versions:

1. Add special candy types (striped, wrapped, color bomb)
2. Implement combo effects for special candy matches
3. Add sound effects for matches and game events
4. Create different board layouts for variety
5. Add a tutorial mode for new players

## Troubleshooting

If the game doesn't load properly, check:

1. Console for any JavaScript errors
2. Network tab for failed API requests
3. Component imports in the module
4. CSS/styling issues in the browser inspector

## Credits

- Original concept based on the popular Candy Crush Saga game
- Implemented for the LP Angular project using modern web technologies
- Styled to match the visual design of other games in the application
