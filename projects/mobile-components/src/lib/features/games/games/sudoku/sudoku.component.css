/* Import shared game styles */
@import '../styles/game-styles.css';

:host {
  @apply block w-full h-full;
}

.game-container {
  @apply flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800;
}

.game-title {
  @apply text-center mb-1;
}

.game-title h1 {
  @apply text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-purple-500;
}

.game-title p {
  @apply text-xs text-slate-400 mb-1;
}

.game-board {
  @apply p-4 rounded-xl bg-slate-700/50 border border-slate-600 shadow-xl;
  position: relative;
  overflow: hidden;
  max-width: 100%;
  margin: 0 auto;
}

.game-board::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-purple-500/10;
  filter: blur(20px);
  z-index: 0;
}

.sudoku-grid {
  @apply grid grid-cols-9 gap-0 p-1 rounded-lg shadow-inner relative z-10;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8), rgba(15, 23, 42, 0.9));
  border: 1px solid rgba(100, 116, 139, 0.3);
}

.cell {
  @apply flex justify-center items-center
         text-lg font-bold select-none
         transition-all duration-200 relative;
  aspect-ratio: 1;
  min-width: 2rem;
  min-height: 2rem;
  position: relative;
  overflow: hidden;
}

.cell::before {
  content: '';
  @apply absolute inset-0 opacity-30;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
}

/* Initial numbers */
.cell.initial {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  @apply text-white shadow-md;
}

/* User entered numbers */
.cell:not(.initial) {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(241, 245, 249, 0.9));
  @apply text-slate-800 hover:bg-slate-200;
}

/* Selected cell */
.cell.selected {
  background: linear-gradient(135deg, #93c5fd, #60a5fa);
  @apply text-white shadow-lg;
  transform: scale(1.05);
  z-index: 5;
}

/* Incorrect numbers */
.cell.incorrect {
  background: linear-gradient(135deg, #f87171, #ef4444);
  @apply text-white shadow-md;
  animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

@keyframes shake {
  10%, 90% { transform: translate3d(-1px, 0, 0); }
  20%, 80% { transform: translate3d(2px, 0, 0); }
  30%, 50%, 70% { transform: translate3d(-2px, 0, 0); }
  40%, 60% { transform: translate3d(2px, 0, 0); }
}

/* Border styling */
.cell {
  @apply border border-slate-600/30;
}

.cell[data-box-border-top="true"] {
  @apply border-t-2 border-t-indigo-500;
}

.cell[data-box-border-bottom="true"] {
  @apply border-b-2 border-b-indigo-500;
}

.cell[data-box-border-left="true"] {
  @apply border-l-2 border-l-indigo-500;
}

.cell[data-box-border-right="true"] {
  @apply border-r-2 border-r-indigo-500;
}

/* Number buttons */
.number-pad {
  @apply grid grid-cols-9 gap-2 mt-4 relative z-10;
}

.number-button {
  @apply flex justify-center items-center font-bold
         transition-all duration-200
         hover:scale-105 active:scale-95 shadow-lg;
  background: linear-gradient(135deg, #4f46e5, #6366f1);
  border-radius: 50%;
  aspect-ratio: 1;
  color: white;
  position: relative;
  overflow: hidden;
}

.number-button::before {
  content: '';
  @apply absolute inset-0 opacity-30;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
}

.number-button:hover {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
}

.demo-mode-indicator {
  @apply absolute top-2 right-2 px-2 py-1 text-xs font-bold text-white rounded-full z-20;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

@media (max-width: 640px) {
  .cell {
    @apply text-base;
    min-width: 1.75rem;
    min-height: 1.75rem;
  }

  .number-button {
    @apply text-sm;
    min-width: 2rem;
    min-height: 2rem;
  }
}
