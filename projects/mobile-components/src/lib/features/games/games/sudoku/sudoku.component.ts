import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, Input } from '@angular/core';
import { NgI<PERSON>, <PERSON><PERSON><PERSON>, NgFor, CommonModule } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { WinLoseOverlayComponent } from '../components/win-lose-overlay/win-lose-overlay.component';
import { GameService } from 'lp-client-api';
import { Game, SudokuConfig, GameProgress, GameEvent } from 'lp-client-api';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { GamesPlayButtonComponent, GameControl } from '../components/games-play-button/games-play-button.component';
import { EventEmitter, Output } from '@angular/core';

// Define GameBonus interface if it's not imported from lp-client-api
interface GameBonus {
  type: 'PERFECT_GAME' | 'SPEED_MASTER' | 'PUZZLE_MASTER';
  points: number;
}

interface SudokuParticipation {
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY';
  frequencyAttempts: number;
  lastParticipation: Date;
}

interface GameBonus {
  type: 'PERFECT_GAME' | 'SPEED_MASTER' | 'PUZZLE_MASTER';
  points: number;
}

@Component({
  selector: 'lib-sudoku',
  templateUrl: './sudoku.component.html',
  styleUrls: ['./sudoku.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    NgIf,
    NgFor,
    GamesPlayButtonComponent,
    GamesScoreComponent,
    WinLoseOverlayComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class SudokuComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Input properties
  @Input() gameId?: string;
  @Input() game?: Game;
  @Input() gameInstance?: any;
  @Input() config: any = {};

  // Output events
  @Output() gameEvent = new EventEmitter<GameEvent>();

  // Game state
  sudokuGameId = 396; // Default Sudoku game ID
  sudokuConfig: SudokuConfig | undefined;
  gameProgress: GameProgress | undefined;
  canPlay: boolean = false;
  attemptsRemaining: number = 3; // Default attempts
  demoMode: boolean = false; // Demo mode flag

  // Game metrics
  board: number[][] = [];
  solution: number[][] = [];
  initialBoard: number[][] = [];
  selectedCell: { row: number; col: number } | null = null;
  incorrectCells: Set<string> = new Set();
  score: number = 0;
  highScore: number = 0;
  level: number = 1;
  moveCount: number = 0;
  gameStarted: boolean = false;
  gameOver: boolean = false;
  startTime: number = 0;
  elapsedTime: string = '00:00';
  gameResult: 'win' | 'lose' | null = null;

  // Difficulty settings
  difficultySettings = {
    EASY: {
      emptyCells: 40,
      hints: 3,
      timeLimit: 1800,
      pointMultiplier: 1,
    },
    MEDIUM: {
      emptyCells: 50,
      hints: 2,
      timeLimit: 2700,
      pointMultiplier: 2,
    },
    HARD: {
      emptyCells: 60,
      hints: 1,
      timeLimit: 3600,
      pointMultiplier: 3,
    },
  };

  // Default configuration
  private readonly DEFAULT_CONFIG: SudokuConfig = {
    id: 0,
    difficulty: 'EASY',
    frequency: 'DAILY',
    frequencyAttempts: 3,
    hints: 3,
    showMistakes: true,
  };

  gameControls: GameControl[] = [
    { name: 'reset', label: 'Reset', icon: 'refresh-outline' },
    { name: 'hint', label: 'Hint', icon: 'bulb-outline' },
  ];

  private timer: ReturnType<typeof setTimeout> | null = null;

  constructor(
    private gameService: GameService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    console.log('Sudoku component initialized');

    // Check if we have a game ID from input
    if (this.game?.id) {
      this.sudokuGameId = this.game.id;
      console.log('Using game ID from input:', this.sudokuGameId);
    }

    // Try to load game config from API
    this.loadGameConfig();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    if (this.timer) {
      clearTimeout(this.timer);
    }
  }

  private loadGameConfig(): void {
    console.log('Loading game config for ID:', this.sudokuGameId);

    this.gameService
      .getGameById(this.sudokuGameId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (game) => {
          console.log('Game config loaded:', game);
          if (game?.gameConfig[0]) {
            this.sudokuConfig = game.gameConfig[0] as SudokuConfig;
            this.applyDifficultySettings(this.sudokuConfig.difficulty);
            this.loadGameProgress();
          } else {
            console.warn('No game config found in API response, using default');
            this.enableDemoMode();
          }
        },
        error: (error) => {
          console.error('Error loading game config:', error);
          this.enableDemoMode();
        },
      });
  }

  private enableDemoMode(): void {
    console.log('Enabling demo mode');
    this.demoMode = true;
    this.canPlay = true;
    this.attemptsRemaining = 3;
    this.sudokuConfig = this.DEFAULT_CONFIG;
    this.applyDifficultySettings('MEDIUM');
  }

  private loadGameProgress(): void {
    if (this.demoMode) {
      // In demo mode, we don't need to check availability
      this.canPlay = true;
      this.attemptsRemaining = 3;
      this.highScore = 0;
      this.initializeGame();
      this.cdr.detectChanges();
      return;
    }

    this.gameService
      .checkGameAvailability(this.sudokuGameId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (canPlay) => {
          console.log('Game availability checked:', canPlay);
          this.canPlay = canPlay;
          if (canPlay) {
            this.loadProgress();
          } else {
            console.warn('Game not available, enabling demo mode');
            this.enableDemoMode();
          }
        },
        error: (error) => {
          console.error('Error checking game availability:', error);
          this.enableDemoMode();
        },
      });
  }

  private loadProgress(): void {
    if (this.demoMode) {
      this.initializeGame();
      return;
    }

    this.gameService
      .getGameProgress(this.sudokuGameId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (progress) => {
          console.log('Game progress loaded:', progress);
          if (progress) {
            this.gameProgress = progress;
            this.highScore = progress.highScore;
            this.attemptsRemaining = progress.attemptsRemaining;
            this.level = progress.currentLevel;
          }
          this.initializeGame();
        },
        error: (error) => {
          console.error('Error loading game progress:', error);
          this.enableDemoMode();
        },
      });
  }

  private applyDifficultySettings(difficulty: string): void {
    const settings =
      this.difficultySettings[
        difficulty as keyof typeof this.difficultySettings
      ];
    if (settings) {
      this.sudokuConfig = {
        ...this.sudokuConfig,
        hints: settings.hints,
        showMistakes: true,
        difficulty: difficulty
      } as SudokuConfig;
    }
  }

  private calculateBonuses(): GameBonus[] {
    const bonuses: GameBonus[] = [];
    const timeElapsed = (Date.now() - this.startTime) / 1000;
    const difficulty = this.sudokuConfig
      ?.difficulty as keyof typeof this.difficultySettings;
    const settings = this.difficultySettings[difficulty];

    // Perfect game bonus (no mistakes)
    if (this.incorrectCells.size === 0) {
      bonuses.push({
        type: 'PERFECT_GAME',
        points: Math.floor(this.score * 0.2),
      });
    }

    // Speed bonus
    if (timeElapsed < settings.timeLimit * 0.5) {
      bonuses.push({
        type: 'SPEED_MASTER',
        points: Math.floor(this.score * 0.15),
      });
    }

    // Efficiency bonus (minimal moves)
    if (this.moveCount < settings.emptyCells * 1.2) {
      bonuses.push({
        type: 'PUZZLE_MASTER',
        points: Math.floor(this.score * 0.25),
      });
    }

    return bonuses;
  }

  // ... existing game logic methods ...

  private updateProgress(): void {
    const finalScore = this.calculateFinalScore();

    // In demo mode, just update the high score locally
    if (this.demoMode) {
      this.highScore = Math.max(this.highScore, finalScore);
      this.endGame(true);
      return;
    }

    const progress: Partial<GameProgress> = {
      gameId: this.sudokuGameId,
      userId: this.gameProgress?.userId || 'demo-user',
      lastPlayed: new Date(),
      attemptsRemaining: this.attemptsRemaining - 1,
      highScore: Math.max(finalScore, this.gameProgress?.highScore || 0),
      currentLevel: this.level,
      gameSpecificProgress: {
        puzzlesCompleted:
          (this.gameProgress?.gameSpecificProgress?.puzzlesCompleted || 0) + 1,
        bestTimes: {
          ...(this.gameProgress?.gameSpecificProgress?.bestTimes || {}),
          [this.sudokuConfig?.difficulty || 'EASY']: Math.min(
            (Date.now() - this.startTime) / 1000,
            this.gameProgress?.gameSpecificProgress?.bestTimes?.[
              this.sudokuConfig?.difficulty || 'EASY'
            ] || Infinity
          ),
        },
        hintsUsed: this.gameProgress?.gameSpecificProgress?.hintsUsed || 0,
        averageTime: this.calculateAverageTime(),
        difficulty: this.sudokuConfig?.difficulty || 'EASY',
        pointsEarned: finalScore,
        bonusesAwarded: this.calculateBonuses(),
      },
    };

    this.gameService
      .updateGameProgress(this.sudokuGameId, progress)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (updatedProgress) => {
          this.gameProgress = updatedProgress;
          this.highScore = updatedProgress.highScore;
          this.attemptsRemaining = updatedProgress.attemptsRemaining;
          this.endGame(true);
        },
        error: (error) => {
          console.error('Error updating progress:', error);
          // Continue in demo mode if API fails
          this.enableDemoMode();
          this.endGame(true);
        },
      });
  }

  private calculateFinalScore(): number {
    const difficulty = this.sudokuConfig
      ?.difficulty as keyof typeof this.difficultySettings;
    const multiplier =
      this.difficultySettings[difficulty]?.pointMultiplier || 1;
    const baseScore = this.score * multiplier;

    const bonuses = this.calculateBonuses();
    const bonusPoints = bonuses.reduce(
      (total, bonus) => total + bonus.points,
      0
    );

    return baseScore + bonusPoints;
  }

  private calculateAverageTime(): number {
    const currentTime = (Date.now() - this.startTime) / 1000;
    const totalGames =
      this.gameProgress?.gameSpecificProgress?.puzzlesCompleted || 0;
    const currentAvg =
      this.gameProgress?.gameSpecificProgress?.averageTime || 0;

    return (currentAvg * totalGames + currentTime) / (totalGames + 1);
  }

  private endGame(isWin: boolean): void {
    this.gameOver = true;
    this.gameResult = isWin ? 'win' : 'lose';

    if (this.timer) {
      clearTimeout(this.timer);
    }

    if (isWin) {
      // Calculate final score
      const timeBonus = Math.max(
        0,
        1000 - Math.floor((Date.now() - this.startTime) / 1000)
      );
      const difficultyMultiplier =
        this.difficultySettings[
          this.sudokuConfig?.difficulty as keyof typeof this.difficultySettings
        ].pointMultiplier;
      this.score =
        this.score + timeBonus * difficultyMultiplier + 1000 * difficultyMultiplier;

      // In demo mode, just update the high score locally
      if (this.demoMode) {
        this.highScore = Math.max(this.highScore, this.score);
        this.cdr.detectChanges();
        return;
      }

      // Save score
      this.gameService
        .saveGameScore(this.sudokuGameId, this.score)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (response) => {
            console.log('Score saved:', response);
            this.highScore = Math.max(this.highScore, this.score);
            this.cdr.detectChanges();
          },
          error: (error) => {
            console.error('Error saving score:', error);
            // Continue in demo mode if API fails
            this.enableDemoMode();
            this.highScore = Math.max(this.highScore, this.score);
            this.cdr.detectChanges();
          },
        });
    } else {
      // Decrement attempts
      if (!this.demoMode) {
        this.attemptsRemaining = Math.max(0, this.attemptsRemaining - 1);
      }
    }

    this.cdr.detectChanges();
  }

  // Public methods for template
  public getDifficultyMultiplier(difficulty: string): number {
    const validDifficulty = difficulty as keyof typeof this.difficultySettings;
    return this.difficultySettings[validDifficulty]?.pointMultiplier || 1;
  }

  public getBonuses(): GameBonus[] {
    return (
      (this.gameProgress?.gameSpecificProgress as any)?.bonusesAwarded || []
    );
  }

  private initializeGame(): void {
    if (!this.sudokuConfig) return;

    // Reset game state
    this.gameStarted = false;
    this.gameOver = false;
    this.score = 0;
    this.moveCount = 0;
    this.incorrectCells.clear();
    this.selectedCell = null;
    this.gameResult = null;

    // Initialize board based on difficulty
    const difficulty = this.sudokuConfig
      .difficulty as keyof typeof this.difficultySettings;
    const settings = this.difficultySettings[difficulty];

    // Generate new puzzle
    this.solution = this.generateSolution();
    this.board = this.solution.map((row) => [...row]);

    // Remove cells based on difficulty
    let cellsToRemove = settings.emptyCells;
    while (cellsToRemove > 0) {
      const row = Math.floor(Math.random() * 9);
      const col = Math.floor(Math.random() * 9);
      if (this.board[row][col] !== 0) {
        this.board[row][col] = 0;
        cellsToRemove--;
      }
    }

    // Save initial board state
    this.initialBoard = this.board.map((row) => [...row]);

    // Reset timer
    if (this.timer) {
      clearTimeout(this.timer);
    }
    this.startTime = Date.now();
    this.elapsedTime = '00:00';
    this.updateTimer();

    // Log initialization
    console.log('Game initialized with difficulty:', difficulty);
    console.log('Initial board:', this.board);

    this.cdr.detectChanges();
  }

  private updateTimer(): void {
    if (this.gameOver) return;

    const now = Date.now();
    const diff = now - this.startTime;
    const minutes = Math.floor(diff / 60000)
      .toString()
      .padStart(2, '0');
    const seconds = Math.floor((diff % 60000) / 1000)
      .toString()
      .padStart(2, '0');
    this.elapsedTime = `${minutes}:${seconds}`;

    this.timer = setTimeout(() => this.updateTimer(), 1000);
  }

  public selectCell(row: number, col: number): void {
    if (!this.gameStarted || this.gameOver) return;

    // Don't allow selecting initial cells
    if (this.initialBoard[row][col] !== 0) return;

    this.selectedCell = { row, col };
  }

  public enterNumber(num: number): void {
    if (!this.selectedCell || !this.gameStarted || this.gameOver) return;

    const { row, col } = this.selectedCell;

    // Only allow changing non-initial cells
    if (this.initialBoard[row][col] !== 0) return;

    this.board[row][col] = num;
    this.moveCount++;

    // Check if the move was valid
    if (!this.isValidMove(row, col, num)) {
      this.incorrectCells.add(`${row}-${col}`);
    } else {
      this.incorrectCells.delete(`${row}-${col}`);
      this.score += 10;
    }

    // Check if puzzle is complete
    if (this.isPuzzleComplete()) {
      this.gameOver = true;
      this.gameResult = 'win';
      this.updateProgress();
    }
  }

  public handleControlClick(controlName: string): void {
    switch (controlName) {
      case 'reset':
        this.resetGame();
        break;
      case 'hint':
        this.useHint();
        break;
      default:
        console.warn('Unknown control:', controlName);
    }
  }

  public newGame(): void {
    if ((!this.canPlay && !this.demoMode) || this.attemptsRemaining <= 0) return;

    // Reset game state
    this.gameStarted = true;
    this.gameOver = false;
    this.score = 0;
    this.moveCount = 0;
    this.incorrectCells.clear();
    this.selectedCell = null;
    this.gameResult = null;
    this.startTime = Date.now();

    // Initialize board
    const difficulty = this.sudokuConfig
      ?.difficulty as keyof typeof this.difficultySettings;
    const settings = this.difficultySettings[difficulty];

    // Generate new puzzle
    this.solution = this.generateSolution();
    this.board = this.solution.map((row) => [...row]);

    // Remove cells based on difficulty
    let cellsToRemove = settings.emptyCells;
    while (cellsToRemove > 0) {
      const row = Math.floor(Math.random() * 9);
      const col = Math.floor(Math.random() * 9);
      if (this.board[row][col] !== 0) {
        this.board[row][col] = 0;
        cellsToRemove--;
      }
    }

    // Save initial board state
    this.initialBoard = this.board.map((row) => [...row]);

    // Start timer
    this.updateTimer();

    // In demo mode, we don't need to save game state
    if (this.demoMode) {
      this.cdr.detectChanges();
      console.log('Game started in demo mode with board:', this.board);
      return;
    }

    // Save initial game state
    this.gameService
      .saveGameScore(this.sudokuGameId, 0)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.cdr.detectChanges();
          console.log('Game started with board:', this.board);
        },
        error: (error) => {
          console.error('Error starting game:', error);
          // Continue in demo mode if API fails
          this.enableDemoMode();
        },
      });
  }

  private generateSolution(): number[][] {
    // For now, returning a valid Sudoku solution
    // In production, you'd want a proper Sudoku generator
    return [
      [5, 3, 4, 6, 7, 8, 9, 1, 2],
      [6, 7, 2, 1, 9, 5, 3, 4, 8],
      [1, 9, 8, 3, 4, 2, 5, 6, 7],
      [8, 5, 9, 7, 6, 1, 4, 2, 3],
      [4, 2, 6, 8, 5, 3, 7, 9, 1],
      [7, 1, 3, 9, 2, 4, 8, 5, 6],
      [9, 6, 1, 5, 3, 7, 2, 8, 4],
      [2, 8, 7, 4, 1, 9, 6, 3, 5],
      [3, 4, 5, 2, 8, 6, 1, 7, 9],
    ];
  }

  private isValidMove(row: number, col: number, num: number): boolean {
    // Check row
    for (let x = 0; x < 9; x++) {
      if (x !== col && this.board[row][x] === num) return false;
    }

    // Check column
    for (let x = 0; x < 9; x++) {
      if (x !== row && this.board[x][col] === num) return false;
    }

    // Check 3x3 box
    const boxRow = Math.floor(row / 3) * 3;
    const boxCol = Math.floor(col / 3) * 3;
    for (let i = 0; i < 3; i++) {
      for (let j = 0; j < 3; j++) {
        if (
          boxRow + i !== row &&
          boxCol + j !== col &&
          this.board[boxRow + i][boxCol + j] === num
        ) {
          return false;
        }
      }
    }

    return true;
  }

  private isPuzzleComplete(): boolean {
    // Check if all cells are filled correctly
    for (let i = 0; i < 9; i++) {
      for (let j = 0; j < 9; j++) {
        if (this.board[i][j] !== this.solution[i][j]) {
          return false;
        }
      }
    }
    return true;
  }

  private useHint(): void {
    if (!this.selectedCell || !this.sudokuConfig?.hints) return;

    const { row, col } = this.selectedCell;
    this.board[row][col] = this.solution[row][col];
    this.incorrectCells.delete(`${row}-${col}`);

    // Update hints used in progress
    if (this.gameProgress?.gameSpecificProgress && !this.demoMode) {
      if (typeof this.gameProgress.gameSpecificProgress === 'object') {
        // Check if hintsUsed property exists, if not create it
        if (!('hintsUsed' in this.gameProgress.gameSpecificProgress)) {
          (this.gameProgress.gameSpecificProgress as any).hintsUsed = 0;
        }
        (this.gameProgress.gameSpecificProgress as any).hintsUsed++;
      }
    }
  }

  public resetGame(): void {
    if (this.attemptsRemaining <= 0 && !this.demoMode) {
      console.log('No attempts remaining');
      return;
    }

    // In demo mode, just reset the game locally
    if (this.demoMode) {
      this.level = 1;
      this.score = 0;
      this.initializeGame();
      this.newGame();
      return;
    }

    this.gameService
      .resetGameProgress(this.sudokuGameId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (resetProgress) => {
          console.log('Game progress reset:', resetProgress);
          this.gameProgress = resetProgress;
          this.attemptsRemaining = resetProgress.attemptsRemaining;
          this.level = 1;
          this.score = 0;
          this.initializeGame();
          this.newGame();
        },
        error: (error) => {
          console.error('Error resetting game:', error);
          // Continue in demo mode if API fails
          this.enableDemoMode();
          this.level = 1;
          this.score = 0;
          this.initializeGame();
          this.newGame();
        },
      });
  }
}
