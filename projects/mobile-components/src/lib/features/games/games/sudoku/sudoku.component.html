<div class="game-container">
  <!-- Demo Mode Indicator -->
  <div *ngIf="demoMode" class="demo-mode-indicator">
    Demo Mode
  </div>

  <!-- Header with Scores -->
  <games-score
    [scores]="[
      { title: 'Score', number: score },
      { title: 'High Score', number: highScore },
      { title: 'Level', number: level },
      { title: 'Time', number: elapsedTime }
    ]"
  ></games-score>

  <!-- Game Title -->
  <div class="game-title">
    <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-purple-500">Sudoku</h1>
    <p class="text-xs text-slate-400 mb-1">Fill the grid with numbers 1-9</p>
  </div>

  <!-- Game Board -->
  <div class="flex overflow-hidden flex-col flex-grow items-center p-2">
    <div class="game-board w-full max-w-md">
      <!-- Start Game Button -->
      <button
        *ngIf="!gameStarted && (canPlay || demoMode) && attemptsRemaining > 0"
        (click)="newGame()"
        class="game-action-button game-action-button-primary mb-4"
      >
        Start Game ({{ attemptsRemaining }} attempts left)
      </button>

      <!-- Sudoku Grid -->
      <div *ngIf="gameStarted" class="sudoku-grid">
        <ng-container *ngFor="let row of board; let i = index">
          <ng-container *ngFor="let cell of row; let j = index">
            <div
              (click)="selectCell(i, j)"
              class="cell"
              [class.initial]="initialBoard[i][j] !== 0"
              [class.selected]="
                selectedCell?.row === i && selectedCell?.col === j
              "
              [class.incorrect]="incorrectCells.has(i + '-' + j)"
              [attr.data-box-border-top]="i % 3 === 0"
              [attr.data-box-border-bottom]="(i + 1) % 3 === 0"
              [attr.data-box-border-left]="j % 3 === 0"
              [attr.data-box-border-right]="(j + 1) % 3 === 0"
            >
              {{ cell !== 0 ? cell : "" }}
            </div>
          </ng-container>
        </ng-container>
      </div>

      <!-- Number Pad -->
      <div *ngIf="gameStarted" class="number-pad">
        <ng-container *ngFor="let num of [1, 2, 3, 4, 5, 6, 7, 8, 9]">
          <button (click)="enterNumber(num)" class="number-button">
            {{ num }}
          </button>
        </ng-container>
      </div>

      <!-- Game Controls -->
      <div *ngIf="gameStarted" class="mt-4">
        <lib-games-play-button
          [controls]="gameControls"
          (controlClicked)="handleControlClick($event)"
        ></lib-games-play-button>
      </div>
    </div>
  </div>

  <!-- Win/Lose Overlay -->
  <lib-win-lose-overlay
    [status]="gameResult"
    (restart)="newGame()"
    message="Congratulations! You've completed the puzzle in {{
      elapsedTime
    }}!"
  ></lib-win-lose-overlay>
</div>
