<div class="flex overflow-hidden overflow-y-auto flex-col min-h-screen bg-slate-900">
  <!-- <div class="absolute inset-0 z-0 h-screen animated-bg-gradient"></div> -->
  <div class="overflow-y-auto absolute inset-0 z-10">
    <games-header bgColor="gray"></games-header>

    <!-- Loading State -->
    <div *ngIf="loading" class="flex justify-center items-center min-h-[200px]">
      <div class="flex items-center text-lg text-white">
        <ion-icon name="hourglass-outline" class="mr-2 animate-spin"></ion-icon>
        Loading game...
      </div>
    </div>

    <!-- Error State -->
    <div *ngIf="error" class="p-4 text-center text-red-500 font-bold">
      {{ error }}
    </div>

    <!-- Game Dashboard -->
    <div *ngIf="!loading && !error" class="flex flex-col flex-grow p-4 mx-auto max-w-4xl relative">
      <!-- Animated Logo -->
      <div *ngIf="showLogoAnimation" class="logo-container">
        <div class="absolute inset-0 bg-gradient-to-b from-amber-500/20 to-transparent rounded-full blur-xl"></div>
        <img src="assets/images/games/mascot.png"
             class="game-logo logo-animation relative z-10"
             alt="Game mascot">
      </div>

      <!-- Game Header -->
      <div class="flex relative justify-between items-center p-5 mb-6 rounded-xl shadow-lg bg-gradient-to-br from-slate-800/90 to-slate-900/90 border border-slate-700/50">
        <!-- Game background image with overlay -->
        <div class="absolute inset-0 overflow-hidden rounded-xl">
          <img *ngIf="activeGame?.backgroundImage"
               [src]="activeGame?.backgroundImage"
               class="object-cover absolute inset-0 z-0 w-full h-full opacity-10"
               alt="Game background">
          <div class="absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 z-1"></div>
        </div>

        <div class="flex items-center z-10">
          <div class="relative mr-4 w-16 h-16 rounded-lg overflow-hidden shadow-lg border border-slate-600/50">
            <img *ngIf="activeGame?.backgroundImage"
                 [src]="activeGame?.backgroundImage"
                 class="object-cover w-full h-full"
                 alt="Game logo">
            <div class="absolute inset-0 bg-gradient-to-br from-transparent to-slate-900/30"></div>
          </div>
          <div>
            <h1 class="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-amber-400 to-orange-500">{{ gameTitle }}</h1>
            <p class="text-slate-300">{{ activeGame?.gameType?.typeDescription?.description }}</p>
          </div>
        </div>
        <div class="flex gap-2 z-10">
          <button
          class="px-5 py-3 text-white font-bold rounded-full shadow-lg transition-all bg-gradient-to-br from-green-500 to-green-600 hover:from-green-400 hover:to-green-500 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-400/50"
          (click)="startGame()">
            <ion-icon name="play-outline" class="mr-1"></ion-icon>
            Start Game
          </button>
        </div>
      </div>

      <!-- Game Stats and Info Grid -->
      <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
        <!-- Stats Card -->
        <div class="p-6 rounded-xl shadow-lg bg-gradient-to-br from-slate-800/90 to-slate-900/90 border border-slate-700/50 transform transition-all hover:scale-[1.01]">
          <h2 class="mb-4 text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-amber-400 to-orange-500">Your Stats</h2>
          <div class="grid grid-cols-2 gap-4">
            <div class="p-4 text-center rounded-lg bg-slate-800/70 border border-slate-700/50 shadow-inner">
              <p class="text-2xl font-bold text-green-400 stat-value">{{ bestScore }}</p>
              <p class="text-slate-400">Best Score</p>
            </div>
            <div class="p-4 text-center rounded-lg bg-slate-800/70 border border-slate-700/50 shadow-inner">
              <p class="text-2xl font-bold text-amber-400 stat-value">{{ gamesPlayed }}</p>
              <p class="text-slate-400">Games Played</p>
            </div>
            <div class="p-4 text-center rounded-lg bg-slate-800/70 border border-slate-700/50 shadow-inner">
              <p class="text-2xl font-bold text-purple-400 stat-value">{{ currentStreak }}</p>
              <p class="text-slate-400">Current Streak</p>
            </div>
            <div class="p-4 text-center rounded-lg bg-slate-800/70 border border-slate-700/50 shadow-inner">
              <p class="text-2xl font-bold text-blue-400 stat-value">{{ gameInstance?.gameEvents?.length || 0 }}</p>
              <p class="text-slate-400">Recent Games</p>
            </div>
          </div>
        </div>

        <!-- Game Rules -->
        <div class="p-6 rounded-xl shadow-lg bg-gradient-to-br from-slate-800/90 to-slate-900/90 border border-slate-700/50 transform transition-all hover:scale-[1.01]">
          <h2 class="mb-4 text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-amber-400 to-orange-500">How to Play</h2>
          <div class="space-y-3 text-slate-300" [ngSwitch]="selectedGame">
            <div *ngSwitchCase="'wordle'" class="space-y-2">
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">🎯 Guess the word in 6 tries</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">🟩 Green tiles: Letter is correct and in right spot</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">🟨 Yellow tiles: Letter is in word but wrong spot</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">⬜️ Gray tiles: Letter is not in word</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">⌨️ Use keyboard or type to enter letters</p>
            </div>
            <div *ngSwitchCase="'memory'" class="space-y-2">
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">🎴 Find matching pairs of cards</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">🕒 Complete levels within time limit</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">🎯 Remember card positions</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">⭐️ Score bonus points for speed</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">🔄 Progress through harder levels</p>
            </div>
            <div *ngSwitchCase="'2048'" class="space-y-2">
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">🔢 Combine matching numbers</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">⬆️ Swipe in any direction to move tiles</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">🎯 Reach the 2048 tile to win</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">📊 Plan your moves carefully</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">💫 Get bonus points for large combinations</p>
            </div>
            <div *ngSwitchCase="'snake'" class="space-y-2">
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">🐍 Control the snake using arrow keys</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">🍎 Collect food to grow longer</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">⚡️ Speed increases as you grow</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">❌ Avoid hitting walls and yourself</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">🏆 Aim for the highest score</p>
            </div>
            <div *ngSwitchCase="'minesweeper'" class="space-y-2">
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">💣 Find all mines without triggering them</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">🔢 Numbers show adjacent mines</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">🚩 Right-click to flag potential mines</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">🤔 Use logic to deduce mine locations</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">⏱️ Complete faster for better scores</p>
            </div>
            <div *ngSwitchCase="'tetris'" class="space-y-2">
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">🟦 Arrange falling blocks to create lines</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">🔄 Rotate pieces to fit perfectly</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">⚡️ Speed increases with level</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">🌟 Clear multiple lines for bonus points</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">📊 Plan ahead using next piece preview</p>
            </div>
            <div *ngSwitchCase="'sudoku'" class="space-y-2">
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">🔢 Fill grid with numbers 1-9</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">✨ Each row, column, and box must contain 1-9</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">💡 Use hints when stuck</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">⏱️ Solve faster for better scores</p>
              <p class="p-2 rounded-lg bg-slate-800/50 border border-slate-700/30">🎯 Multiple difficulty levels available</p>
            </div>
            <div *ngSwitchDefault class="space-y-2">
              <p class="p-3 rounded-lg bg-slate-800/50 border border-slate-700/30 text-center">Select a game to see its rules and instructions</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
