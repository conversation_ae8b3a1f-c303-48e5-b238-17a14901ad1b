import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { ActivatedRoute, Router } from '@angular/router';
import { GameService } from 'lp-client-api';
import { Game, GameConfig, GameConfigs, GameInstance } from 'lp-client-api';
import { GamesHeaderComponent } from '../components/games-header/games-header.component';

interface Prize {
  name: string;
  points: number;
}

@Component({
  selector: 'app-game-loader-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule, GamesHeaderComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class DashboardComponent implements OnInit {
  private readonly MEMKEY = 517193;
  public game: Game | null = null;
  public gameConfig: GameConfigs | null = null;
  public activeGameInstance: any | null = null;
  public loading = true;
  public error: string | null = null;

  // Add missing properties
  public gameTitle: string = '';
  public selectedGame: string | null = null;
  public gameInstance: GameInstance | null = null;
  public activeGame: Game | null = null;
  public showLogoAnimation = false;

  // Stats (to be implemented with real data)
  public bestScore: number = 0;
  public gamesPlayed: number = 0;
  public currentStreak: number = 0;

  // Sample prizes (to be replaced with real data)
  public prizes: Prize[] = [
    { name: 'Bronze Trophy', points: 100 },
    { name: 'Silver Trophy', points: 250 },
    { name: 'Gold Trophy', points: 500 },
  ];

  // Add missing properties
  public gameProgress: any = null;

  constructor(private route: ActivatedRoute, private router: Router, private gameService: GameService) {
    const gameData = localStorage.getItem('activeGame');
    if (gameData) {
      this.game = JSON.parse(gameData);
      this.activeGame = this.game;
    }
  }

  ngOnInit() {
    // First check if there's a game parameter in the URL
    this.route.queryParams.subscribe(params => {
      const gameParam = params['game'];
      console.log('Game parameter from URL:', gameParam);

      if (gameParam) {
        // If we have a game parameter, find the corresponding game
        this.loadGameFromParameter(gameParam);
      } else {
        // If no game parameter, fall back to the active game from service
        this.loadActiveGameFromService();
      }
    });
  }

  private loadGameFromParameter(gameParam: string) {
    // Normalize the game parameter
    const normalizedGameParam = gameParam.toLowerCase();
    console.log('Normalized game parameter:', normalizedGameParam);

    // Get all games and find the one matching the parameter
    this.gameService.getAllGameConfigs().subscribe({
      next: (games) => {
        console.log('All games:', games);

        // Find the game with matching type code
        const matchingGame = games.find(
          (g) => g.gameType.typeDescription.code.toLowerCase() === normalizedGameParam
        );

        if (matchingGame) {
          console.log('Found matching game:', matchingGame);

          // Set as active game
          this.activeGame = matchingGame;
          this.gameService.setActiveGame(matchingGame);
          localStorage.setItem('activeGame', JSON.stringify(matchingGame));

          // Set selected game and title
          this.selectedGame = normalizedGameParam;
          this.gameTitle = matchingGame.name.replace('%20', ' ');

          // Trigger logo animation
          this.triggerLogoAnimation();

          this.loading = false;
        } else {
          console.error(`No game found with type code: ${normalizedGameParam}`);
          this.error = `Game "${gameParam}" not found`;
          this.loading = false;

          // Fall back to active game from service
          this.loadActiveGameFromService();
        }
      },
      error: (error) => {
        console.error('Error loading games:', error);
        this.error = 'Failed to load games';
        this.loading = false;

        // Fall back to active game from service
        this.loadActiveGameFromService();
      }
    });
  }

  private loadActiveGameFromService() {
    // Get active game from service
    this.activeGame = this.gameService.getActiveGame();
    console.log('Active game from service:', this.activeGame);

    if (this.activeGame) {
      this.selectedGame = this.activeGame.gameType.typeDescription.code.toLowerCase();
      this.gameTitle = this.activeGame.name.replace('%20', ' ');

      // Trigger logo animation
      this.triggerLogoAnimation();

      this.loading = false;
    } else {
      console.error('No active game found');
      this.error = 'No active game found';
      this.loading = false;
    }
  }

  private triggerLogoAnimation() {
    // Trigger logo animation after a short delay
    setTimeout(() => {
      this.showLogoAnimation = true;
      // Hide animation after it completes
      setTimeout(() => {
        this.showLogoAnimation = false;
      }, 3000);
    }, 500);
  }

  // Add missing methods
  public startGame() {
    try {
      this.loading = true;
      this.error = null;

      // Get active game and validate
      if (!this.activeGame) {
        this.activeGame = this.gameService.getActiveGame();
      }

      if (!this.activeGame?.id) {
        console.error('Initial check: Active game or game ID is missing.', this.activeGame);
        throw new Error('No active game found or game ID is missing');
      }

      // Navigate to game component
      this.navigateToGame();

    } catch (error) {
      console.error('Error starting game:', error);
      this.error = error instanceof Error ? error.message : 'Unknown error starting game';
    } finally {
      this.loading = false;
    }
  }

  public navigateToGame() {
    if (!this.activeGame) {
      this.activeGame = this.gameService.getActiveGame();
    }

    if (!this.activeGame) {
      this.error = 'No active game found';
      return;
    }

    const typeCode = this.activeGame.gameType.typeDescription.code.toLowerCase();

    this.router.navigate(['/public/games/single'], {
      queryParams: {
        game: typeCode,
        instance: this.gameInstance?.id
      }
    });
  }

  // Add missing utility methods
  public getDuration(duration: number): string {
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${seconds}s`;
  }

  public formatTime(seconds: number): string {
    if (!seconds) return '0';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    if (minutes > 0) {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return remainingSeconds.toString();
  }

  // private async initializeGame() {
  //   try {
  //     if (!this.game) {
  //       throw new Error('No game data found');
  //     }

  //     // Get game config for the current game
  //     const gameConfig = this.game.gameConfig[0];
  //     if (!gameConfig) {
  //       throw new Error('No game configuration found');
  //     }

  //     this.gameConfig = gameConfig;

  //     // First try to get existing instances
  //     try {
  //       const accountConfigs = await firstValueFrom(
  //         this.gameService.getAccountGameConfigs(this.MEMKEY)
  //       );

  //       // Find matching config and its active instance
  //       const matchingConfig = accountConfigs.find(
  //         config => config.gameConfig === gameConfig.id
  //       );

  //       if (matchingConfig?.gameInstance) {
  //         // Get the full instance details
  //         this.activeGameInstance = await firstValueFrom(
  //           this.gameService.getGameInstance(gameConfig.id, matchingConfig.gameInstance)
  //         );
  //         console.log('Found existing game instance:', this.activeGameInstance);
  //       } else {
  //         // If no instance exists, create a new one
  //         this.activeGameInstance = await firstValueFrom(
  //           this.gameService.createGameInstance(gameConfig.id)
  //         );
  //         console.log('Created new game instance:', this.activeGameInstance);
  //       }
  //     } catch (instanceError) {
  //       console.error('Error getting/creating game instance:', instanceError);
  //       throw new Error('Failed to initialize game instance');
  //     }

  //     this.loading = false;
  //   } catch (error: any) {
  //     this.error = error?.message || 'Failed to initialize game';
  //     this.loading = false;
  //     console.error('Game initialization error:', error);
  //   }
  // }

  // public async sendGameEvent(eventType: string, eventData: any) {
  //   if (!this.gameConfig || !this.activeGameInstance) return;

  //   try {
  //     await firstValueFrom(
  //       this.gameService.createGameEvent(
  //         this.gameConfig.id,
  //         this.activeGameInstance.id,
  //         {
  //           level: 1,
  //           score: eventData.score || 0,
  //           duration: eventData.duration || 0,
  //           state: eventType,
  //           payload: JSON.stringify(eventData)
  //         }
  //       )
  //     );
  //   } catch (error: any) {
  //     console.error('Failed to send game event:', error);
  //   }
  // }
}
