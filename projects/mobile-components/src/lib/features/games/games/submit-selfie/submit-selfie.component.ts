import {
  Compo<PERSON>,
  OnInit,
  OnDestroy,
  ViewChild,
  NgZone,
  Input,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Geolocation, Position } from '@capacitor/geolocation';
import {
  Camera,
  CameraResultType,
  CameraSource,
  CameraDirection,
} from '@capacitor/camera';
import { GoogleMap, MapMarker, MapCircle, GoogleMapsModule } from '@angular/google-maps';
import { GameService } from 'lp-client-api';
import {
  Game,
  GeoSpaceConfig,
  Location,
  GeoSpaceProgress,
  GameProgressType,
} from 'lp-client-api';

@Component({
  selector: 'lib-submit-selfie',
  templateUrl: './submit-selfie.component.html',
  styleUrls: ['./submit-selfie.component.css'],
  standalone: true,
  imports: [CommonModule, GoogleMapsModule]
})
export class SubmitSelfieComponent implements OnInit, <PERSON><PERSON><PERSON>roy {
  @ViewChild(GoogleMap, { static: false }) map!: GoogleMap;
  isGoogleMapsLoaded = false;
  loading = true;

  remainingAttempts = 0;

  private watchId: string | null = null;
  private validLocations: Location[] = [];

  // Google Maps options
  center: google.maps.LatLngLiteral = { lat: -25.8494938, lng: 28.1896346 };
  zoom = 15;
  markerOptions: google.maps.MarkerOptions = { draggable: false };
  circleOptions: google.maps.CircleOptions = {
    fillColor: '#4285F4',
    fillOpacity: 0.2,
    strokeColor: '#4285F4',
    strokeOpacity: 1,
    strokeWeight: 1,
  };

  // Component state
  currentPosition: Position | null = null;
  userMarkerPosition: google.maps.LatLngLiteral | null = null;
  isInValidLocation = false;
  capturedImage: string | null = null;
  errorMessage: string | null = null;
  locationCircles: google.maps.CircleOptions[] = [];

  // Updated Google Maps options
  options: google.maps.MapOptions = {
    disableDefaultUI: true,
    fullscreenControl: true,
    zoomControl: true,
    mapTypeControl: false,
    streetViewControl: false,
    maxZoom: 17,
    minZoom: 3,
    styles: [
      {
        featureType: 'poi',
        elementType: 'labels',
        stylers: [{ visibility: 'off' }],
      },
    ],
  };

  markerClustererImagePath =
    'https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m';

  // Update markers array type
  validLocationMarkers: any[] = [];

  // Add marker options for user location
  userMarkerOptions: google.maps.MarkerOptions = {
    icon: {
      path: google.maps.SymbolPath.CIRCLE,
      scale: 7,
      fillColor: '#4285F4',
      fillOpacity: 1,
      strokeColor: '#ffffff',
      strokeWeight: 2,
    },
  };

  // Component state
  @Input() config: GeoSpaceConfig | undefined;
  @Input() globalConfig: any;

  // Configuration with proper typing
  gameId = 391; // Location Based Photo game ID
  private gameProgress: GameProgressType<GeoSpaceProgress> | undefined;

  // Default values that will be overridden by config
  private readonly DEFAULT_CONFIG = {
    maxPhotoSize: 5242880, // 5MB
    allowedFileTypes: ['jpg', 'jpeg', 'png'],
    maxDistanceMeters: 200,
    photoQualityMin: 0.7,
  };

  constructor(private ngZone: NgZone, private gameService: GameService) {}

  async ngOnInit() {
    console.log(
      'Initializing SubmitSelfie component with gameId:',
      this.gameId
    );

    // Load game configuration and progress
    this.gameService.getGameById(this.gameId).subscribe((game) => {
      console.log('Game config loaded:', game);
      if (game && game.gameConfig[0]) {
        this.config = game.gameConfig[0] as GeoSpaceConfig;
        console.log('Location based config:', this.config);

        // Set background if provided
        if (game.backgroundImage) {
          console.log('Setting background image:', game.backgroundImage);
          document.documentElement.style.setProperty(
            '--geo-game-bg',
            `url(${game.backgroundImage})`
          );
        }

        // Initialize valid locations from config
        if (this.config.locations) {
          this.validLocations = this.config.locations;
          console.log('Valid locations initialized:', this.validLocations);
          this.initializeValidLocationCircles();
        }

        // Initialize circles with configured values
        this.circleOptions = {
          ...this.circleOptions,
          radius:
            this.config.maxDistanceMeters ||
            this.DEFAULT_CONFIG.maxDistanceMeters,
          fillColor: this.config.design?.circleColor || '#4285F4',
          fillOpacity: this.config.design?.circleOpacity || 0.2,
        };
      }
    });

    // Load game progress
    this.loadProgress();

    // Initialize map after a short delay
    setTimeout(() => {
      this.ngZone.run(() => {
        this.isGoogleMapsLoaded = !!window.google?.maps;
        this.loading = false;
        console.log('Google Maps loaded state:', this.isGoogleMapsLoaded);

        if (this.isGoogleMapsLoaded) {
          this.checkPermissions();
          this.startLocationWatch();
        } else {
          console.error('Google Maps failed to load');
        }
      });
    }, 1000);
  }

  ngOnDestroy() {
    if (this.watchId) {
      Geolocation.clearWatch({ id: this.watchId });
    }
  }

  private async checkPermissions() {
    try {
      const status = await Geolocation.checkPermissions();
      console.log('Geolocation permissions status:', status);

      if (status.location !== 'granted') {
        console.log('Requesting location permissions...');
        const requestStatus = await Geolocation.requestPermissions();
        console.log('Location permissions request result:', requestStatus);

        if (requestStatus.location !== 'granted') {
          this.errorMessage =
            'Location permissions are required for this feature';
          return;
        }
      }

      // Get initial position
      const position = await Geolocation.getCurrentPosition({
        enableHighAccuracy: true,
        timeout: 10000, // Increase timeout to 10 seconds
      });
      this.updatePosition(position);
    } catch (error) {
      this.errorMessage =
        'Unable to get your location. Please enable location services.';
      console.error('Geolocation error:', error);
    }
  }

  private initializeValidLocationCircles() {
    this.locationCircles = this.validLocations.map((location) => ({
      ...this.circleOptions,
      center: { lat: location.latitude, lng: location.longitude },
      radius: location.radius,
    }));
  }

  private startLocationWatch() {
    if ('geolocation' in navigator) {
      const watch = navigator.geolocation.watchPosition(
        (position) => {
          this.ngZone.run(() => {
            // Update user marker position
            this.userMarkerPosition = {
              lat: position.coords.latitude,
              lng: position.coords.longitude,
            };

            // Center map on user's location if it's the first position update
            if (!this.currentPosition) {
              this.center = this.userMarkerPosition;
              this.zoom = 15;
            }

            this.currentPosition = position;
            this.checkLocationValidity();
          });
        },
        (error) => {
          console.error('Error getting location:', error);
          this.errorMessage =
            'Unable to get your location. Please enable location services.';
        },
        {
          enableHighAccuracy: true,
          timeout: 5000,
          maximumAge: 0,
        }
      );
      this.watchId = String(watch);
    } else {
      this.errorMessage = 'Geolocation is not supported by your browser.';
    }
  }

  private updatePosition(position: Position) {
    this.currentPosition = position;
    this.userMarkerPosition = {
      lat: position.coords.latitude,
      lng: position.coords.longitude,
    };

    console.log('User position updated:', {
      position: this.userMarkerPosition,
      markerOptions: this.userMarkerOptions,
    });

    // Update map center to follow user
    this.center = this.userMarkerPosition;

    // Check if user is in valid location
    this.checkLocationValidity();
  }

  private checkLocationValidity() {
    if (!this.currentPosition?.coords) return;

    this.isInValidLocation = this.validLocations.some((location) => {
      const distance = this.calculateDistance(
        this.currentPosition!.coords.latitude,
        this.currentPosition!.coords.longitude,
        location.latitude,
        location.longitude
      );
      return distance <= location.radius;
    });
  }

  private calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = (lat1 * Math.PI) / 180;
    const φ2 = (lat2 * Math.PI) / 180;
    const Δφ = ((lat2 - lat1) * Math.PI) / 180;
    const Δλ = ((lon2 - lon1) * Math.PI) / 180;

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  }

  async takeSelfie() {
    if (!this.isInValidLocation || !this.config) {
      this.errorMessage = 'You must be in a valid location to take a selfie';
      return;
    }

    try {
      const image = await Camera.getPhoto({
        quality: Math.round(
          (this.config.photoQualityMin || this.DEFAULT_CONFIG.photoQualityMin) *
            100
        ),
        allowEditing: true,
        resultType: CameraResultType.DataUrl,
        source: CameraSource.Camera,
        direction: CameraDirection.Front,
      });

      this.capturedImage = image.dataUrl || null;

      if (this.capturedImage && this.currentPosition) {
        // Update game progress
        this.updateProgress();
      }
    } catch (error) {
      this.errorMessage = 'Error capturing photo';
      console.error('Camera error:', error);
    }
  }

  private calculateBase64Size(base64String: string): number {
    const padding = base64String.endsWith('==')
      ? 2
      : base64String.endsWith('=')
      ? 1
      : 0;
    return (base64String.length * 3) / 4 - padding;
  }

  private async submitSelfie() {
    if (!this.capturedImage || !this.currentPosition) return;

    try {
      // TODO: Implement your backend submission logic here
      const submission = {
        image: this.capturedImage,
        location: {
          latitude: this.currentPosition.coords.latitude,
          longitude: this.currentPosition.coords.longitude,
          accuracy: this.currentPosition.coords.accuracy,
        },
        timestamp: new Date().toISOString(),
      };

      // Reset after successful submission
      this.capturedImage = null;
      this.errorMessage = null;
    } catch (error) {
      this.errorMessage = 'Error submitting selfie';
      console.error('Submission error:', error);
    }
  }

  loadProgress() {
    this.gameService
      .getGameProgress<GeoSpaceProgress>(this.gameId)
      .subscribe((progress) => {
        this.gameProgress = progress;
        this.remainingAttempts = progress.attemptsRemaining;
      });
  }

  updateProgress() {
    const progress: Partial<GameProgressType<GeoSpaceProgress>> = {
      lastPlayed: new Date(),
      attemptsRemaining: this.remainingAttempts,
      gameSpecificProgress: {
        totalPhotos:
          (this.gameProgress?.gameSpecificProgress.totalPhotos || 0) + 1,
        lastPhotoTime: new Date().toISOString(),
        photosToday:
          (this.gameProgress?.gameSpecificProgress.photosToday || 0) + 1,
        totalPointsWon:
          this.gameProgress?.gameSpecificProgress.totalPointsWon || 0,
        visitedLocations:
          this.gameProgress?.gameSpecificProgress.visitedLocations || [],
      },
    };

    this.gameService
      .updateGameProgress<GeoSpaceProgress>(this.gameId, progress)
      .subscribe((updatedProgress) => {
        this.gameProgress = updatedProgress;
        this.remainingAttempts = updatedProgress.attemptsRemaining;
      });
  }
}
