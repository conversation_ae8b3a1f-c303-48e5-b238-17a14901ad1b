<div class="flex flex-col h-full p-4 space-y-4">
  <!-- Error Message -->
  <div
    *ngIf="errorMessage"
    class="
      bg-red-100
      border border-red-400
      text-red-700
      px-4
      py-3
      rounded
      relative
    "
  >
    {{ errorMessage }}
  </div>

  <!-- Location Status -->
  <div
    [ngClass]="
      isInValidLocation
        ? 'bg-green-100 text-green-700'
        : 'bg-yellow-100 text-yellow-700'
    "
    class="px-4 py-3 rounded"
  >
    <p *ngIf="isInValidLocation">
      You're in a valid location! You can take a selfie now.
    </p>
    <p *ngIf="!isInValidLocation">
      You're not in a valid location to take a selfie. Please move to one of the
      marked locations on the map.
    </p>
  </div>

  <!-- Updated Map -->
  <div class="flex-1 min-h-[300px] rounded overflow-hidden">
    <google-map
      *ngIf="isGoogleMapsLoaded"
      #map
      [height]="'300px'"
      [width]="'100%'"
      [options]="options"
      [center]="center"
      [zoom]="zoom"
    >
      <map-advanced-marker
        *ngFor="let marker of validLocationMarkers"
        [position]="marker.position"
        [title]="marker.title"
      ></map-advanced-marker>

      <map-advanced-marker
        *ngIf="userMarkerPosition"
        [position]="userMarkerPosition"
        [title]="'Your location'"
      ></map-advanced-marker>

      <map-circle
        *ngFor="let circle of locationCircles"
        [center]="circle.center!"
        [radius]="circle.radius!"
        [options]="circle"
      >
      </map-circle>

      <map-marker
        *ngIf="userMarkerPosition"
        [position]="userMarkerPosition"
        [options]="userMarkerOptions"
      >
      </map-marker>
    </google-map>

    <!-- Loading state -->
    <div
      *ngIf="!isGoogleMapsLoaded"
      class="w-full h-[300px] bg-gray-100 flex items-center justify-center"
    >
      <span>Loading map...</span>
    </div>
  </div>

  <!-- Camera Button -->
  <button
    (click)="takeSelfie()"
    [disabled]="!isInValidLocation"
    class="w-full py-3 px-4 rounded-lg text-white font-medium"
    [ngClass]="
      isInValidLocation ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-400'
    "
  >
    Take Selfie
  </button>

  <!-- Preview -->
  <div *ngIf="capturedImage" class="mt-4">
    <img
      [src]="capturedImage"
      class="w-full h-64 object-cover rounded-lg"
      alt="Selfie preview"
    />
  </div>
</div>
