import { Component, OnInit, OnDestroy, Input, Output, EventEmitter, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule, NgIf, NgF<PERSON>, Ng<PERSON><PERSON>, NgStyle } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { WinLoseOverlayComponent } from '../components/win-lose-overlay/win-lose-overlay.component';
import { GameControl, GamesPlayButtonComponent } from '../components/games-play-button/games-play-button.component';
import { Game, GameEvent } from 'lp-client-api';
import { GameService } from 'lp-client-api';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

// Define the Treasure Hunt game configuration interface
interface TreasureHuntConfig {
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  maxAttempts: number;
  gridSize: number;
  treasureCount: number;
  trapCount: number;
  timeLimit: number;
  hintCount: number;
}

// Define the cell interface
interface Cell {
  row: number;
  col: number;
  revealed: boolean;
  hasTreasure: boolean;
  hasTrap: boolean;
  hasHint: boolean;
  value: number; // Distance to nearest treasure
  flagged: boolean;
}

@Component({
  selector: 'lib-treasure-hunt',
  templateUrl: './treasure-hunt.component.html',
  styleUrls: ['./treasure-hunt.component.css'],
  standalone: true,
  imports: [CommonModule, NgIf, NgFor, NgClass, NgStyle, GamesScoreComponent, WinLoseOverlayComponent, GamesPlayButtonComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class TreasureHuntComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Input properties
  @Input() gameId?: string;
  @Input() game?: Game;
  @Input() gameInstance?: any;
  @Input() config: any = {};

  // Output events
  @Output() gameEvent = new EventEmitter<GameEvent>();

  // Game state
  grid: Cell[][] = [];
  treasuresFound: number = 0;
  trapsTriggered: number = 0;
  hintsRemaining: number = 0;
  timeRemaining: number = 0;
  score: number = 0;
  highScore: number = 0;
  gameResult: 'win' | 'lose' | null = null;
  timerInterval: any;
  flagMode: boolean = false;

  // Game configuration
  treasureHuntGameId = 0; // Will be set from input or default
  treasureHuntConfig?: TreasureHuntConfig;
  canPlay: boolean = false;
  attemptsRemaining: number = 3; // Default attempts
  demoMode: boolean = false; // Demo mode flag

  // Game controls
  gameControls: GameControl[] = [
    { name: 'restart', label: 'New Game', icon: 'refresh-outline' },
    { name: 'hint', label: 'Use Hint', icon: 'bulb-outline' },
    { name: 'flag', label: 'Flag Mode', icon: 'flag-outline' }
  ];

  constructor(
    private gameService: GameService
  ) {}

  ngOnInit(): void {
    console.log('Treasure Hunt component initialized');

    // Check if we have a game ID from input
    if (this.game?.id) {
      this.treasureHuntGameId = this.game.id;
      console.log('Using game ID from input:', this.treasureHuntGameId);
    }

    // Try to load game config from API
    this.loadGameConfig();
  }

  /**
   * Load game configuration from API or use demo mode
   */
  private loadGameConfig(): void {
    // If we have a game from input, use its configuration
    if (this.game) {
      console.log('Using game configuration from input:', this.game);
      this.setupFromGameConfig();
      return;
    }

    // Otherwise, use demo mode
    console.log('No game configuration from input, using demo mode');
    this.setupDemoMode();
  }

  /**
   * Setup game from API configuration
   */
  private setupFromGameConfig(): void {
    // Extract treasure hunt configuration from game config
    const gameConfig = this.game?.gameConfig?.[0];
    if (gameConfig) {
      this.treasureHuntConfig = {
        difficulty: (gameConfig.difficulty as 'EASY' | 'MEDIUM' | 'HARD') || 'MEDIUM',
        maxAttempts: gameConfig.frequencyAttempts || 3,
        gridSize: this.config.gridSize || 8,
        treasureCount: this.config.treasureCount || 5,
        trapCount: this.config.trapCount || 3,
        timeLimit: this.config.timeLimit || 180,
        hintCount: this.config.hintCount || 3
      };

      // Check if the game can be played
      if (this.treasureHuntGameId) {
        this.gameService.checkGameAvailability(this.treasureHuntGameId)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: (canPlay) => {
              this.canPlay = canPlay;
              if (!canPlay) {
                console.log('Game cannot be played, using demo mode');
                this.setupDemoMode();
              } else {
                console.log('Game can be played, initializing treasure hunt');
                this.newGame();
              }
            },
            error: (error) => {
              console.error('Error checking game availability:', error);
              this.setupDemoMode();
            }
          });
      } else {
        this.setupDemoMode();
      }
    } else {
      this.setupDemoMode();
    }
  }

  /**
   * Setup demo mode with default configuration
   */
  private setupDemoMode(): void {
    this.demoMode = true;
    this.canPlay = true;
    this.attemptsRemaining = 3;

    // Set default configuration for demo mode
    this.treasureHuntConfig = {
      difficulty: 'MEDIUM',
      maxAttempts: 3,
      gridSize: 8,
      treasureCount: 5,
      trapCount: 3,
      timeLimit: 180,
      hintCount: 3
    };

    // Initialize the game
    this.newGame();
  }

  /**
   * Start a new game
   */
  newGame(): void {
    // Reset game state
    this.treasuresFound = 0;
    this.trapsTriggered = 0;
    this.score = 0;
    this.gameResult = null;
    this.flagMode = false;

    // Set hints based on difficulty
    if (this.treasureHuntConfig) {
      this.hintsRemaining = this.treasureHuntConfig.hintCount;
      this.timeRemaining = this.treasureHuntConfig.timeLimit;
    } else {
      this.hintsRemaining = 3;
      this.timeRemaining = 180;
    }

    // Initialize the grid
    this.initializeGrid();

    // Start the timer
    this.startTimer();

    // Emit game start event
    this.emitGameEvent('start', 0);
  }

  /**
   * Initialize the game grid
   */
  private initializeGrid(): void {
    const gridSize = this.treasureHuntConfig?.gridSize || 8;
    const treasureCount = this.treasureHuntConfig?.treasureCount || 5;
    const trapCount = this.treasureHuntConfig?.trapCount || 3;

    // Create empty grid
    this.grid = [];
    for (let row = 0; row < gridSize; row++) {
      this.grid[row] = [];
      for (let col = 0; col < gridSize; col++) {
        this.grid[row][col] = {
          row,
          col,
          revealed: false,
          hasTreasure: false,
          hasTrap: false,
          hasHint: false,
          value: 0,
          flagged: false
        };
      }
    }

    // Place treasures randomly
    let treasuresPlaced = 0;
    while (treasuresPlaced < treasureCount) {
      const row = Math.floor(Math.random() * gridSize);
      const col = Math.floor(Math.random() * gridSize);

      if (!this.grid[row][col].hasTreasure) {
        this.grid[row][col].hasTreasure = true;
        treasuresPlaced++;
      }
    }

    // Place traps randomly
    let trapsPlaced = 0;
    while (trapsPlaced < trapCount) {
      const row = Math.floor(Math.random() * gridSize);
      const col = Math.floor(Math.random() * gridSize);

      if (!this.grid[row][col].hasTreasure && !this.grid[row][col].hasTrap) {
        this.grid[row][col].hasTrap = true;
        trapsPlaced++;
      }
    }

    // Calculate distance values for each cell
    this.calculateDistanceValues();
  }

  /**
   * Calculate distance values for each cell (distance to nearest treasure)
   */
  private calculateDistanceValues(): void {
    const gridSize = this.treasureHuntConfig?.gridSize || 8;

    for (let row = 0; row < gridSize; row++) {
      for (let col = 0; col < gridSize; col++) {
        if (this.grid[row][col].hasTreasure) {
          this.grid[row][col].value = 0;
        } else {
          // Find distance to nearest treasure
          let minDistance = Number.MAX_VALUE;

          for (let r = 0; r < gridSize; r++) {
            for (let c = 0; c < gridSize; c++) {
              if (this.grid[r][c].hasTreasure) {
                const distance = Math.max(
                  Math.abs(row - r),
                  Math.abs(col - c)
                );
                minDistance = Math.min(minDistance, distance);
              }
            }
          }

          this.grid[row][col].value = minDistance;
        }
      }
    }
  }

  /**
   * Handle cell click
   */
  onCellClick(cell: Cell): void {
    // Don't allow clicks if game is over
    if (this.gameResult) return;

    // If cell is already revealed, do nothing
    if (cell.revealed) return;

    // Handle flag mode
    if (this.flagMode) {
      cell.flagged = !cell.flagged;
      return;
    }

    // If cell is flagged, don't reveal it
    if (cell.flagged) return;

    // Reveal the cell
    cell.revealed = true;

    // Check if it's a treasure
    if (cell.hasTreasure) {
      this.treasuresFound++;
      this.score += 100;

      // Check if all treasures are found
      const treasureCount = this.treasureHuntConfig?.treasureCount || 5;
      if (this.treasuresFound === treasureCount) {
        this.handleWin();
      }
    }

    // Check if it's a trap
    if (cell.hasTrap) {
      this.trapsTriggered++;
      this.score -= 50;

      // Check if too many traps are triggered
      const trapCount = this.treasureHuntConfig?.trapCount || 3;
      if (this.trapsTriggered >= trapCount) {
        this.handleLoss();
      }
    }

    // Update score based on distance value
    if (!cell.hasTreasure && !cell.hasTrap) {
      // Give more points for cells closer to treasures
      this.score += Math.max(10 - cell.value * 2, 1);
    }
  }

  /**
   * Toggle flag mode
   */
  toggleFlagMode(): void {
    this.flagMode = !this.flagMode;
  }

  /**
   * Use a hint to reveal a cell with valuable information
   */
  useHint(): void {
    // Don't allow hints if game is over or no hints remaining
    if (this.gameResult || this.hintsRemaining <= 0) return;

    // Find a good cell to reveal as a hint
    const gridSize = this.treasureHuntConfig?.gridSize || 8;
    let bestCell: Cell | null = null;
    let bestValue = -1;

    for (let row = 0; row < gridSize; row++) {
      for (let col = 0; col < gridSize; col++) {
        const cell = this.grid[row][col];

        // Skip revealed cells
        if (cell.revealed) continue;

        // Skip treasure and trap cells
        if (cell.hasTreasure || cell.hasTrap) continue;

        // Prefer cells with low distance values (close to treasures)
        if (cell.value === 1) {
          bestCell = cell;
          break;
        } else if (cell.value < 3 && (bestCell === null || cell.value < bestValue)) {
          bestCell = cell;
          bestValue = cell.value;
        }
      }
    }

    // If we found a good hint cell, reveal it
    if (bestCell) {
      bestCell.revealed = true;
      bestCell.hasHint = true;
      this.hintsRemaining--;
    }
  }

  /**
   * Start the game timer
   */
  private startTimer(): void {
    // Clear any existing timer
    this.stopTimer();

    // Start a new timer
    this.timerInterval = setInterval(() => {
      this.timeRemaining--;

      if (this.timeRemaining <= 0) {
        this.handleLoss();
      }
    }, 1000);
  }

  /**
   * Stop the game timer
   */
  private stopTimer(): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
  }

  /**
   * Handle game win
   */
  private handleWin(): void {
    this.stopTimer();
    this.gameResult = 'win';

    // Add time bonus
    const timeBonus = Math.floor(this.timeRemaining / 10);
    this.score += timeBonus;

    // Update high score
    if (this.score > this.highScore) {
      this.highScore = this.score;
    }

    // Emit game win event
    this.emitGameEvent('win', this.score);
  }

  /**
   * Handle game loss
   */
  private handleLoss(): void {
    this.stopTimer();
    this.gameResult = 'lose';

    // Reveal all treasures and traps
    this.revealAll();

    // Emit game lose event
    this.emitGameEvent('lose', this.score);
  }

  /**
   * Reveal all cells
   */
  private revealAll(): void {
    const gridSize = this.treasureHuntConfig?.gridSize || 8;

    for (let row = 0; row < gridSize; row++) {
      for (let col = 0; col < gridSize; col++) {
        this.grid[row][col].revealed = true;
      }
    }
  }

  /**
   * Handle control clicks from the games-play-button component
   */
  handleControlClick(event: any): void {
    // Convert the event to a string if it's not already
    const controlName = typeof event === 'string' ? event : event.toString();

    switch (controlName) {
      case 'restart':
        this.newGame();
        break;
      case 'hint':
        this.useHint();
        break;
      case 'flag':
        this.toggleFlagMode();
        break;
      default:
        break;
    }
  }

  /**
   * Get score items for the games-score component
   */
  getScoreItems(): any[] {
    return [
      { title: 'Score', value: this.score },
      { title: 'Treasures', value: `${this.treasuresFound}/${this.treasureHuntConfig?.treasureCount || 5}` },
      { title: 'Traps', value: `${this.trapsTriggered}/${this.treasureHuntConfig?.trapCount || 3}` },
      { title: 'Time', value: this.formatTime(this.timeRemaining) }
    ];
  }

  /**
   * Format time in seconds to MM:SS format
   */
  formatTime(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  /**
   * Get cell background color based on its state and value
   */
  getCellColor(cell: Cell): string {
    if (!cell.revealed) {
      return cell.flagged ? '#f59e0b' : '#1e293b';
    }

    if (cell.hasTreasure) {
      return '#10b981'; // Green for treasure
    }

    if (cell.hasTrap) {
      return '#ef4444'; // Red for trap
    }

    if (cell.hasHint) {
      return '#8b5cf6'; // Purple for hint
    }

    // Color based on distance to nearest treasure
    switch (cell.value) {
      case 1: return '#3b82f6'; // Blue for close
      case 2: return '#60a5fa';
      case 3: return '#93c5fd';
      case 4: return '#bfdbfe';
      default: return '#dbeafe'; // Light blue for far
    }
  }

  /**
   * Get cell text based on its state and value
   */
  getCellText(cell: Cell): string {
    if (!cell.revealed) {
      return cell.flagged ? '🚩' : '';
    }

    if (cell.hasTreasure) {
      return '💎';
    }

    if (cell.hasTrap) {
      return '💣';
    }

    return cell.value.toString();
  }

  /**
   * Emit game event
   */
  private emitGameEvent(state: string, score: number): void {
    // Create event object
    const event: GameEvent = {
      id: 0, // Will be set by the API
      level: 1,
      score: score,
      duration: this.treasureHuntConfig?.timeLimit! - this.timeRemaining,
      state: state,
      payload: JSON.stringify({
        treasuresFound: this.treasuresFound,
        trapsTriggered: this.trapsTriggered,
        hintsUsed: this.treasureHuntConfig?.hintCount! - this.hintsRemaining,
        timeRemaining: this.timeRemaining
      })
    };

    // If we have a game ID and instance, save the event to the API
    if (!this.demoMode && this.treasureHuntGameId && this.gameInstance?.id) {
      this.gameService.createGameEvent(
        this.treasureHuntGameId,
        this.gameInstance.id,
        event
      ).pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          console.log('Game event saved:', response);
        },
        error: (error) => {
          console.error('Error saving game event:', error);
        }
      });
    }

    // Emit the event
    this.gameEvent.emit(event);
  }

  /**
   * Clean up on component destruction
   */
  ngOnDestroy(): void {
    this.stopTimer();
    this.destroy$.next();
    this.destroy$.complete();
  }
}
