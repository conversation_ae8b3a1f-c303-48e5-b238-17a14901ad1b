/* Import shared game styles */
@import '../styles/game-styles.css';

:host {
  @apply block w-full h-full;
}

/* Demo Mode Indicator */
.demo-mode-indicator {
  @apply absolute top-2 right-2 px-2 py-1 text-xs font-bold text-white rounded-full z-20;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

/* Flag Mode Indicator */
.flag-mode-indicator {
  @apply flex items-center justify-center px-3 py-1 mx-auto mb-2 text-sm font-bold text-white rounded-full w-fit;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  animation: pulse 2s infinite;
}

.flag-icon {
  @apply mr-1;
}

/* Game Board */
.game-board {
  @apply bg-slate-800/50 rounded-lg;
  min-height: 300px;
}

.treasure-grid {
  @apply p-2 rounded-lg;
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(100, 116, 139, 0.5);
}

.grid-container {
  display: grid;
  gap: 4px;
}

.grid-row {
  display: contents;
}

.grid-cell {
  @apply flex justify-center items-center rounded-md cursor-pointer transition-all duration-200 ease-in-out;
  aspect-ratio: 1;
  min-width: 32px;
  min-height: 32px;
  font-weight: bold;
  border: 1px solid rgba(100, 116, 139, 0.5);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.grid-cell:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

.grid-cell.revealed {
  cursor: default;
}

.grid-cell.flagged {
  border: 2px solid #f59e0b;
}

.cell-content {
  @apply text-center text-white;
  font-size: 1rem;
}

/* Game Controls */
.game-controls {
  @apply w-full max-w-md mx-auto;
}

/* Responsive Adjustments */
@media (max-width: 640px) {
  .grid-cell {
    min-width: 28px;
    min-height: 28px;
    font-size: 0.9rem;
  }
}

@media (max-width: 375px) {
  .grid-cell {
    min-width: 24px;
    min-height: 24px;
    font-size: 0.8rem;
  }
}