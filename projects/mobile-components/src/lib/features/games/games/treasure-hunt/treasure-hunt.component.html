<div class="flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800">
  <!-- Demo Mode Indicator -->
  <div *ngIf="demoMode" class="demo-mode-indicator">
    Demo Mode
  </div>

  <!-- Game Title -->
  <div class="text-center mb-1">
    <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-amber-400 to-yellow-600">Treasure Hunt</h1>
    <p class="text-xs text-slate-400 mb-1">Find treasures, avoid traps, follow the clues</p>
  </div>

  <!-- Game Score -->
  <div class="w-full max-w-md mx-auto px-4 py-2">
    <games-score [scores]="getScoreItems()"></games-score>
  </div>

  <!-- Flag Mode Indicator -->
  <div *ngIf="flagMode" class="flag-mode-indicator">
    <span class="flag-icon">🚩</span> Flag Mode Active
  </div>

  <!-- Game Board -->
  <div class="game-board flex-grow overflow-auto p-4">
    <div class="treasure-grid max-w-md mx-auto">
      <div class="grid-container" [ngStyle]="{'grid-template-columns': 'repeat(' + (treasureHuntConfig?.gridSize || 8) + ', 1fr)'}">
        <div
          *ngFor="let row of grid; let i = index"
          class="grid-row"
        >
          <div
            *ngFor="let cell of row; let j = index"
            class="grid-cell"
            [ngClass]="{'revealed': cell.revealed, 'flagged': cell.flagged && !cell.revealed}"
            [ngStyle]="{'background-color': getCellColor(cell)}"
            (click)="onCellClick(cell)"
          >
            <span class="cell-content">{{ getCellText(cell) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Game Controls -->
  <div class="game-controls mt-2">
    <lib-games-play-button
      [controls]="gameControls"
      (controlClicked)="handleControlClick($event)"
    ></lib-games-play-button>
  </div>

  <!-- Win/Lose Overlay -->
  <lib-win-lose-overlay
    *ngIf="gameResult"
    [status]="gameResult"
    [score]="score"
    [highScore]="highScore"
    (restart)="newGame()"
  ></lib-win-lose-overlay>
</div>
