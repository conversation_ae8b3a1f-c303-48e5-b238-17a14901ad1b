/* Import shared game styles */
@import '../styles/game-styles.css';

:host {
  @apply block w-full h-full;
}

/* Demo Mode Indicator */
.demo-mode-indicator {
  @apply flex items-center justify-center p-1 mb-1 text-xs text-amber-300 bg-amber-900/50 rounded-md;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 0.5;
  }
}

/* <PERSON>vas Styling */
canvas {
  max-width: 100%;
  max-height: 70vh;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

canvas:hover {
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
}

/* Game Controls */
.game-controls {
  @apply flex justify-center gap-2 mt-3;
}

.game-action-button {
  @apply px-3 py-1 rounded-full text-white font-medium shadow-md transition-all duration-200 hover:scale-105;
}

.game-action-button-primary {
  @apply bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600;
}

.game-action-button-secondary {
  @apply bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600;
}

.game-action-button-disabled {
  @apply opacity-50 cursor-not-allowed;
  @apply bg-gradient-to-r from-gray-500 to-gray-600;
  transform: none !important;
}
