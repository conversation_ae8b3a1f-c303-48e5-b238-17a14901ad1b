import {
  <PERSON>mpo<PERSON>,
  <PERSON><PERSON><PERSON>t,
  <PERSON><PERSON><PERSON><PERSON>,
  HostListener,
  ViewChild,
  ElementRef,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
  Input,
  Output,
  EventEmitter
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { WinLoseOverlayComponent } from '../components/win-lose-overlay/win-lose-overlay.component';
import { GameControl, GamesPlayButtonComponent } from '../components/games-play-button/games-play-button.component';
import { Game } from 'lp-client-api';

interface PongConfig {
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  maxAttempts: number;
  winScore: number;
  paddleSize?: number;
  ballSpeed?: number;
  aiDifficulty?: number;
}

interface GameEvent {
  type: string;
  data: any;
  timestamp: string;
  gameId: string;
}

@Component({
  selector: 'lib-pong',
  templateUrl: './pong.component.html',
  styleUrls: ['./pong.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    GamesScoreComponent,
    WinLoseOverlayComponent,
    GamesPlayButtonComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class PongComponent implements OnInit, OnDestroy {
  // Required inputs for all games
  @Input() gameId: string = '';
  @Input() config: any = {};
  @Input() gameInstance: any = null;
  @Input() game?: Game;

  // Output for game events
  @Output() gameEvent = new EventEmitter<GameEvent>();

  @ViewChild('gameCanvas', { static: true })
  gameCanvas!: ElementRef<HTMLCanvasElement>;

  // Game dimensions
  canvasWidth: number = 320;
  canvasHeight: number = 240;
  paddleHeight = 80;
  paddleWidth = 10;
  ballSize = 10;

  // Game state
  playerY = this.canvasHeight / 2 - this.paddleHeight / 2;
  aiY = this.canvasHeight / 2 - this.paddleHeight / 2;
  ballX = this.canvasWidth / 2;
  ballY = this.canvasHeight / 2;
  ballSpeedX = 2;
  ballSpeedY = 2;
  speedIncrement = 0.005;
  level = 1;
  maxBallSpeed = 8;
  playerScore = 0;
  aiScore = 0;
  winScore = 5;
  gameOver = false;
  gameResult: 'win' | 'lose' | null = null;
  demoMode: boolean = false;
  errorMessage: string = '';
  aiDifficulty = 0.8; // AI difficulty factor (0-1)

  // Default configuration
  private readonly DEFAULT_CONFIG: PongConfig = {
    difficulty: 'MEDIUM',
    maxAttempts: 3,
    winScore: 5,
    paddleSize: 80,
    ballSpeed: 2,
    aiDifficulty: 0.8
  };

  // Game controls
  gameControls: GameControl[] = [
    { name: 'restart', label: 'New Game', icon: 'refresh-outline', color: 'green' }
  ];

  // Canvas context
  private ctx!: CanvasRenderingContext2D;

  // Variables for touch/mouse controls
  private isTouching = false;
  private animationFrameId: number | null = null;
  setCanvasSize(): void {
    const screenWidth = window.innerWidth;
    const screenHeight = window.innerHeight;
    const padding = 20; // Padding on all sides
    const maxAspectRatio = 4 / 3; // Maximum aspect ratio (width / height)

    let width = screenWidth - padding * 2;
    let height = screenHeight - padding * 2;

    // Adjust dimensions to maintain aspect ratio
    if (width / height > maxAspectRatio) {
      width = height * maxAspectRatio;
    } else {
      height = width / maxAspectRatio;
    }

    this.canvasWidth = Math.floor(width);
    this.canvasHeight = Math.floor(height);

    const canvas = this.gameCanvas.nativeElement;
    canvas.width = this.canvasWidth;
    canvas.height = this.canvasHeight;

    // Recalculate game elements based on new canvas size
    this.recalculateGameElements();
  }

  @HostListener('window:resize')
  onResize(): void {
    this.setCanvasSize();
  }

  ngOnInit() {
    console.log('Pong component initialized');
    console.log('Game ID:', this.gameId);
    console.log('Config:', this.config);
    console.log('Game Instance:', this.gameInstance);

    try {
      this.initializeGame();
    } catch (error) {
      console.error('Error initializing game:', error);
      this.initializeDemoMode();
    }
  }

  /**
   * Initialize the game with configuration
   */
  private initializeGame(): void {
    // Set up configuration
    if (this.config) {
      // Set difficulty
      if (this.config.difficulty) {
        switch (this.config.difficulty) {
          case 'EASY':
            this.aiDifficulty = 0.6;
            this.paddleHeight = 100;
            this.speedIncrement = 0.003;
            break;
          case 'MEDIUM':
            this.aiDifficulty = 0.8;
            this.paddleHeight = 80;
            this.speedIncrement = 0.005;
            break;
          case 'HARD':
            this.aiDifficulty = 0.95;
            this.paddleHeight = 60;
            this.speedIncrement = 0.008;
            break;
          default:
            this.aiDifficulty = this.DEFAULT_CONFIG.aiDifficulty || 0.8;
            this.paddleHeight = this.DEFAULT_CONFIG.paddleSize || 80;
        }
      }

      // Set win score
      if (this.config.winScore) {
        this.winScore = this.config.winScore;
      } else {
        this.winScore = this.DEFAULT_CONFIG.winScore;
      }

      // Set ball speed
      if (this.config.ballSpeed) {
        this.ballSpeedX = this.config.ballSpeed;
        this.ballSpeedY = this.config.ballSpeed;
      }
    } else {
      // Use default configuration
      this.aiDifficulty = this.DEFAULT_CONFIG.aiDifficulty || 0.8;
      this.paddleHeight = this.DEFAULT_CONFIG.paddleSize || 80;
      this.winScore = this.DEFAULT_CONFIG.winScore;
    }

    this.setupGame();
  }

  /**
   * Initialize demo mode when API fails
   */
  private initializeDemoMode(): void {
    console.log('Initializing demo mode for Pong game');
    this.demoMode = true;
    this.aiDifficulty = this.DEFAULT_CONFIG.aiDifficulty || 0.8;
    this.paddleHeight = this.DEFAULT_CONFIG.paddleSize || 80;
    this.winScore = this.DEFAULT_CONFIG.winScore;
    this.setupGame();
  }

  /**
   * Set up the game canvas and start the game loop
   */
  private setupGame(): void {
    this.setCanvasSize();
    window.addEventListener('resize', this.setCanvasSize.bind(this));
    const canvas = this.gameCanvas.nativeElement;
    this.ctx = canvas.getContext('2d') as CanvasRenderingContext2D;

    // Add touch event listeners
    canvas.addEventListener('touchstart', this.onTouchStart.bind(this));
    canvas.addEventListener('touchmove', this.onTouchMove.bind(this));
    canvas.addEventListener('touchend', this.onTouchEnd.bind(this));

    // Add mouse event listeners for desktop (optional)
    canvas.addEventListener('mousedown', this.onMouseDown.bind(this));
    canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
    canvas.addEventListener('mouseup', this.onMouseUp.bind(this));

    // Reset game state
    this.resetGame();

    // Start the game loop
    this.startGameLoop();

    // Emit game start event
    this.emitGameEvent('game_start', {
      difficulty: this.config?.difficulty || 'MEDIUM',
      winScore: this.winScore,
      demoMode: this.demoMode
    });
  }

  /**
   * Reset the game state
   */
  resetGame(): void {
    this.playerScore = 0;
    this.aiScore = 0;
    this.gameOver = false;
    this.gameResult = null;
    this.level = 1;
    this.maxBallSpeed = 8;
    this.resetBall(true);
  }

  /**
   * Handle control button clicks
   */
  handleControlClick(controlName: string): void {
    if (controlName === 'restart') {
      this.resetGame();
      this.emitGameEvent('game_restart', {
        difficulty: this.config?.difficulty || 'MEDIUM',
        winScore: this.winScore
      });
    }
  }

  ngOnDestroy() {
    this.stopGameLoop();
    this.removeEventListeners();
    this.clearCanvas();
  }

  private startGameLoop() {
    this.gameLoop();
  }

  private stopGameLoop() {
    if (this.animationFrameId !== null) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  private removeEventListeners() {
    const canvas = this.gameCanvas.nativeElement;
    canvas.removeEventListener('touchstart', this.onTouchStart);
    canvas.removeEventListener('touchmove', this.onTouchMove);
    canvas.removeEventListener('touchend', this.onTouchEnd);
    canvas.removeEventListener('mousedown', this.onMouseDown);
    canvas.removeEventListener('mousemove', this.onMouseMove);
    canvas.removeEventListener('mouseup', this.onMouseUp);
  }

  private clearCanvas() {
    if (this.ctx) {
      this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
    }
  }

  // Touch event handlers
  onTouchStart(event: TouchEvent) {
    event.preventDefault();
    this.isTouching = true;
    this.updatePlayerPosition(event.touches[0].clientY);
  }

  onTouchMove(event: TouchEvent) {
    event.preventDefault();
    if (this.isTouching) {
      this.updatePlayerPosition(event.touches[0].clientY);
    }
  }

  onTouchEnd(event: TouchEvent) {
    event.preventDefault();
    this.isTouching = false;
  }

  // Mouse event handlers (optional for desktop)
  onMouseDown(event: MouseEvent) {
    event.preventDefault();
    this.isTouching = true;
    this.updatePlayerPosition(event.clientY);
  }

  onMouseMove(event: MouseEvent) {
    event.preventDefault();
    if (this.isTouching) {
      this.updatePlayerPosition(event.clientY);
    }
  }

  onMouseUp(event: MouseEvent) {
    event.preventDefault();
    this.isTouching = false;
  }

  updatePlayerPosition(cursorY: number) {
    const rect = this.gameCanvas.nativeElement.getBoundingClientRect();
    const y = cursorY - rect.top;
    this.playerY = y - this.paddleHeight / 2;

    // Ensure paddle stays within canvas
    this.playerY = Math.max(
      0,
      Math.min(this.playerY, this.canvasHeight - this.paddleHeight)
    );
  }

  recalculateGameElements(): void {
    // Adjust paddle sizes, ball size, and positions based on new canvas dimensions
    this.paddleHeight = Math.floor(this.canvasHeight * 0.15); // 15% of canvas height
    this.paddleWidth = Math.floor(this.canvasWidth * 0.02); // 2% of canvas width
    this.ballSize = Math.floor(
      Math.min(this.canvasWidth, this.canvasHeight) * 0.02
    ); // 2% of smaller dimension

    // Reset positions
    this.playerY = this.canvasHeight / 2 - this.paddleHeight / 2;
    this.aiY = this.canvasHeight / 2 - this.paddleHeight / 2;
    this.ballX = this.canvasWidth / 2;
    this.ballY = this.canvasHeight / 2;

    // Adjust ball speed based on canvas size
    const baseSpeed = Math.min(this.canvasWidth, this.canvasHeight) * 0.004;
    this.ballSpeedX = baseSpeed;
    this.ballSpeedY = baseSpeed;
    this.maxBallSpeed = baseSpeed * 4;

    // Recalculate other game-specific variables as needed
  }

  gameLoop() {
    if (!this.gameOver) {
      this.moveBall();
      this.moveAI();
      this.checkCollision();
    }
    this.render();
    this.animationFrameId = requestAnimationFrame(() => this.gameLoop());
  }

  render() {
    if (!this.ctx) return;

    // Clear the canvas
    this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);

    // Draw center line
    this.ctx.setLineDash([5, 5]);
    this.ctx.beginPath();
    this.ctx.moveTo(this.canvasWidth / 2, 0);
    this.ctx.lineTo(this.canvasWidth / 2, this.canvasHeight);
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
    this.ctx.stroke();
    this.ctx.setLineDash([]);

    // Draw player paddle
    this.ctx.fillStyle = '#3b82f6'; // Blue
    this.ctx.fillRect(0, this.playerY, this.paddleWidth, this.paddleHeight);

    // Add gradient to player paddle
    const playerGradient = this.ctx.createLinearGradient(0, this.playerY, this.paddleWidth, this.playerY);
    playerGradient.addColorStop(0, '#3b82f6');
    playerGradient.addColorStop(1, '#2563eb');
    this.ctx.fillStyle = playerGradient;
    this.ctx.fillRect(0, this.playerY, this.paddleWidth, this.paddleHeight);

    // Draw AI paddle
    this.ctx.fillStyle = '#ef4444'; // Red
    this.ctx.fillRect(
      this.canvasWidth - this.paddleWidth,
      this.aiY,
      this.paddleWidth,
      this.paddleHeight
    );

    // Add gradient to AI paddle
    const aiGradient = this.ctx.createLinearGradient(
      this.canvasWidth - this.paddleWidth, this.aiY,
      this.canvasWidth, this.aiY
    );
    aiGradient.addColorStop(0, '#ef4444');
    aiGradient.addColorStop(1, '#dc2626');
    this.ctx.fillStyle = aiGradient;
    this.ctx.fillRect(
      this.canvasWidth - this.paddleWidth,
      this.aiY,
      this.paddleWidth,
      this.paddleHeight
    );

    // Draw the ball with a gradient
    const ballGradient = this.ctx.createRadialGradient(
      this.ballX + this.ballSize / 2, this.ballY + this.ballSize / 2, 0,
      this.ballX + this.ballSize / 2, this.ballY + this.ballSize / 2, this.ballSize
    );
    ballGradient.addColorStop(0, '#22c55e'); // Light green
    ballGradient.addColorStop(1, '#16a34a'); // Dark green
    this.ctx.fillStyle = ballGradient;
    this.ctx.beginPath();
    this.ctx.arc(
      this.ballX + this.ballSize / 2,
      this.ballY + this.ballSize / 2,
      this.ballSize / 2,
      0,
      Math.PI * 2
    );
    this.ctx.fill();

    // Draw scores
    this.ctx.font = 'bold 24px Arial';
    this.ctx.fillStyle = 'white';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(
      this.playerScore.toString(),
      this.canvasWidth / 4,
      30
    );
    this.ctx.fillText(
      this.aiScore.toString(),
      (this.canvasWidth / 4) * 3,
      30
    );
  }

  moveBall() {
    this.ballX += this.ballSpeedX;
    this.ballY += this.ballSpeedY;

    // Gradually increase ball speed over time
    if (Math.abs(this.ballSpeedX) < this.maxBallSpeed) {
      this.ballSpeedX +=
        this.ballSpeedX > 0 ? this.speedIncrement : -this.speedIncrement;
    }
    if (Math.abs(this.ballSpeedY) < this.maxBallSpeed) {
      this.ballSpeedY +=
        this.ballSpeedY > 0 ? this.speedIncrement : -this.speedIncrement;
    }
  }

  moveAI() {
    // AI difficulty affects how perfectly it tracks the ball
    const aiCenter = this.aiY + this.paddleHeight / 2;
    const targetY = this.ballY + this.ballSize / 2;
    const distanceFromBall = Math.abs(aiCenter - targetY);

    // Only move if the ball is moving toward the AI
    if (this.ballSpeedX > 0) {
      // Calculate AI speed based on difficulty and level
      const speedAdjustment = (2 + this.level * 0.3) * this.aiDifficulty;

      // Add some randomness to make AI less perfect based on difficulty
      const randomFactor = Math.random() * (1 - this.aiDifficulty);
      const shouldMove = randomFactor < 0.9; // Small chance AI doesn't react

      if (shouldMove) {
        // Move toward the ball with some prediction
        if (aiCenter < targetY - 5) {
          this.aiY = Math.min(
            this.canvasHeight - this.paddleHeight,
            this.aiY + speedAdjustment
          );
        } else if (aiCenter > targetY + 5) {
          this.aiY = Math.max(0, this.aiY - speedAdjustment);
        }
      }
    }
  }

  checkCollision() {
    // Ball collision with top and bottom walls
    if (this.ballY <= 0 || this.ballY + this.ballSize >= this.canvasHeight) {
      this.ballSpeedY = -this.ballSpeedY;

      // Emit wall collision event
      this.emitGameEvent('ball_wall_collision', {
        position: { x: this.ballX, y: this.ballY },
        velocity: { x: this.ballSpeedX, y: this.ballSpeedY }
      });
    }

    // Ball collision with player paddle
    if (
      this.ballX <= this.paddleWidth &&
      this.ballY + this.ballSize >= this.playerY &&
      this.ballY <= this.playerY + this.paddleHeight
    ) {
      // Calculate collision point relative to paddle center (0 = center, -1 = top edge, 1 = bottom edge)
      const collisionPoint =
        (this.ballY + this.ballSize / 2 - (this.playerY + this.paddleHeight / 2)) /
        (this.paddleHeight / 2);

      // Calculate new angle based on collision point
      const angle = collisionPoint * (Math.PI / 4); // Max 45 degree angle

      // Set new velocity with increased speed
      const speed = Math.sqrt(this.ballSpeedX * this.ballSpeedX + this.ballSpeedY * this.ballSpeedY);
      const newSpeed = Math.min(speed + 0.2, this.maxBallSpeed);

      this.ballSpeedX = Math.cos(angle) * newSpeed;
      this.ballSpeedY = Math.sin(angle) * newSpeed;

      // Ensure ball is moving away from player
      if (this.ballSpeedX < 0) {
        this.ballSpeedX = -this.ballSpeedX;
      }

      // Emit paddle hit event
      this.emitGameEvent('player_paddle_hit', {
        position: { x: this.ballX, y: this.ballY },
        velocity: { x: this.ballSpeedX, y: this.ballSpeedY },
        collisionPoint: collisionPoint
      });
    }

    // Ball collision with AI paddle
    if (
      this.ballX + this.ballSize >= this.canvasWidth - this.paddleWidth &&
      this.ballY + this.ballSize >= this.aiY &&
      this.ballY <= this.aiY + this.paddleHeight
    ) {
      // Calculate collision point relative to paddle center
      const collisionPoint =
        (this.ballY + this.ballSize / 2 - (this.aiY + this.paddleHeight / 2)) /
        (this.paddleHeight / 2);

      // Calculate new angle based on collision point
      const angle = collisionPoint * (Math.PI / 4); // Max 45 degree angle

      // Set new velocity with increased speed
      const speed = Math.sqrt(this.ballSpeedX * this.ballSpeedX + this.ballSpeedY * this.ballSpeedY);
      const newSpeed = Math.min(speed + 0.2, this.maxBallSpeed);

      this.ballSpeedX = -Math.cos(angle) * newSpeed; // Negative to move toward player
      this.ballSpeedY = Math.sin(angle) * newSpeed;

      // Emit paddle hit event
      this.emitGameEvent('ai_paddle_hit', {
        position: { x: this.ballX, y: this.ballY },
        velocity: { x: this.ballSpeedX, y: this.ballSpeedY },
        collisionPoint: collisionPoint
      });
    }

    // Ball out of bounds - AI scores
    if (this.ballX < 0) {
      this.aiScore++;
      this.emitGameEvent('ai_score', {
        playerScore: this.playerScore,
        aiScore: this.aiScore
      });

      // Check for game over
      if (this.aiScore >= this.winScore) {
        this.endGame('lose');
      } else {
        this.resetBall(false);
      }
    }
    // Ball out of bounds - Player scores
    else if (this.ballX > this.canvasWidth) {
      this.playerScore++;
      this.emitGameEvent('player_score', {
        playerScore: this.playerScore,
        aiScore: this.aiScore
      });

      // Level up after every 3 points
      if (this.playerScore % 3 === 0) {
        this.level = Math.min(this.level + 1, 10);
        this.maxBallSpeed = 8 + this.level;

        this.emitGameEvent('level_up', {
          level: this.level,
          maxBallSpeed: this.maxBallSpeed
        });
      }

      // Check for game over
      if (this.playerScore >= this.winScore) {
        this.endGame('win');
      } else {
        this.resetBall(true);
      }
    }
  }

  resetBall(isPlayerScore: boolean) {
    // Reset ball position
    this.ballX = this.canvasWidth / 2;
    this.ballY = this.canvasHeight / 2;

    // Reset ball speed to initial slower speed
    const initialSpeed = 2 + (this.level * 0.5);
    this.ballSpeedX = isPlayerScore ? initialSpeed : -initialSpeed;

    // Add some randomness to Y direction
    this.ballSpeedY = (Math.random() * 2 - 1) * initialSpeed / 2;

    // Emit ball reset event
    this.emitGameEvent('ball_reset', {
      isPlayerScore: isPlayerScore,
      velocity: { x: this.ballSpeedX, y: this.ballSpeedY }
    });
  }

  /**
   * End the game with a win or loss
   */
  endGame(result: 'win' | 'lose') {
    this.gameOver = true;
    this.gameResult = result;

    // Emit game end event
    this.emitGameEvent('game_end', {
      result: result,
      playerScore: this.playerScore,
      aiScore: this.aiScore,
      level: this.level
    });
  }

  /**
   * Emit a game event
   */
  private emitGameEvent(eventType: string, data: any): void {
    const gameEvent: GameEvent = {
      type: eventType,
      data: data,
      timestamp: new Date().toISOString(),
      gameId: this.gameId
    };

    this.gameEvent.emit(gameEvent);
    console.log('Game event emitted:', gameEvent);
  }
}
