<div class="flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800">
  <!-- Demo Mode Indicator -->
  <div *ngIf="demoMode" class="demo-mode-indicator">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
    <span>DEMO MODE</span>
  </div>

  <!-- Score Display -->
  <games-score
    [scores]="[
      { title: 'Player', number: playerScore },
      { title: 'AI', number: aiScore },
      { title: 'Level', number: level }
    ]"
  ></games-score>

  <!-- Game Title -->
  <div class="text-center mb-1">
    <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-indigo-500">Pong</h1>
    <p class="text-xs text-slate-400 mb-1">Score {{winScore}} points to win!</p>
  </div>

  <!-- Game Canvas -->
  <div class="flex-grow flex justify-center items-center p-2">
    <canvas
      #gameCanvas
      class="border border-slate-700 rounded-lg shadow-lg bg-slate-800"
      [width]="canvasWidth"
      [height]="canvasHeight"
    ></canvas>
  </div>

  <!-- Game Controls -->
  <div class="mt-auto">
    <lib-games-play-button
      [controls]="gameControls"
      (controlClicked)="handleControlClick($event)"
    ></lib-games-play-button>
  </div>

  <!-- Win/Lose Overlay -->
  <lib-win-lose-overlay
    [status]="gameResult"
    (restart)="resetGame()"
    [message]="gameResult === 'win' ? 'You Won!' : 'You Lost!'"
  ></lib-win-lose-overlay>
</div>
