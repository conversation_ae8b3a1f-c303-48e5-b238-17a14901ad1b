import { Component, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule, TitleCasePipe } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { GameControl, GamesPlayButtonComponent } from '../components/games-play-button/games-play-button.component';
import { WinLoseOverlayComponent } from '../components/win-lose-overlay/win-lose-overlay.component';
import { Game } from 'lp-client-api';

interface RockPaperScissorsConfig {
  rounds: number;
  timeLimit: number;
  difficulty?: 'EASY' | 'MEDIUM' | 'HARD';
  maxAttempts?: number;
}

interface RockPaperScissorsGameEvent {
  type: string;
  data: any;
  timestamp: string;
  gameId: string;
}

@Component({
  selector: 'lib-rock-paper-scissors',
  templateUrl: './rock-paper-scissors.component.html',
  styleUrls: ['./rock-paper-scissors.component.css'],
  standalone: true,
  imports: [CommonModule, TitleCasePipe, GamesScoreComponent, GamesPlayButtonComponent, WinLoseOverlayComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class RockPaperScissorsComponent {
  @Input() gameId: string = '';
  @Input() config: RockPaperScissorsConfig = {
    rounds: 3,
    timeLimit: 10,
    difficulty: 'MEDIUM',
    maxAttempts: 3
  };
  @Input() gameInstance: any = null;
  @Input() game?: Game;

  @Output() gameEventEmitter = new EventEmitter<RockPaperScissorsGameEvent>();

  choices: string[] = ['rock', 'paper', 'scissors'];
  playerChoice: string | null = null;
  computerChoice: string | null = null;
  result: string | null = null;

  // Game state
  playerScore: number = 0;
  computerScore: number = 0;
  roundsPlayed: number = 0;
  gameOver: boolean = false;
  gameResult: 'win' | 'lose' | null = null;
  timeRemaining: number = 0;
  timerInterval: any = null;
  attemptsRemaining: number = 3;
  isDemoMode: boolean = false;
  errorMessage: string = '';

  // Game controls configuration
  gameControls: GameControl[] = [
    { name: 'restart', icon: 'refresh-outline', label: 'Restart' },
    { name: 'newGame', icon: 'add-circle-outline', label: 'New Game' }
  ];

  ngOnInit() {
    this.initializeGame();
  }

  ngOnDestroy() {
    this.clearTimer();
  }

  initializeGame() {
    try {
      // Check if we have a valid configuration from the input
      if (this.config && this.config.rounds) {
        console.log('Initializing Rock Paper Scissors with config:', this.config);
      } else {
        console.warn('No valid config provided, using default config');
        this.initializeDemoMode();
      }

      // Set attempts remaining from config
      this.attemptsRemaining = this.config.maxAttempts || 3;

      // Reset game state
      this.playerScore = 0;
      this.computerScore = 0;
      this.roundsPlayed = 0;
      this.gameOver = false;
      this.gameResult = null;
      this.playerChoice = null;
      this.computerChoice = null;
      this.result = null;

      // Start timer if time limit is set
      this.startTimer();

      // Emit game start event
      this.emitGameEvent('start', {
        difficulty: this.config.difficulty,
        rounds: this.config.rounds,
        isDemoMode: this.isDemoMode
      });
    } catch (error) {
      console.error('Error initializing Rock Paper Scissors game:', error);
      this.initializeDemoMode();
    }
  }

  initializeDemoMode() {
    console.log('Initializing Rock Paper Scissors in demo mode');
    this.isDemoMode = true;
    this.errorMessage = 'Playing in demo mode';

    // Set default configuration
    this.config = {
      rounds: 3,
      timeLimit: 10,
      difficulty: 'MEDIUM',
      maxAttempts: 3
    };

    this.attemptsRemaining = this.config.maxAttempts || 3;
  }

  startTimer() {
    this.clearTimer();

    if (this.config.timeLimit > 0) {
      this.timeRemaining = this.config.timeLimit;
      this.timerInterval = setInterval(() => {
        this.timeRemaining--;
        if (this.timeRemaining <= 0) {
          this.clearTimer();
          this.handleTimeUp();
        }
      }, 1000);
    }
  }

  clearTimer() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
  }

  handleTimeUp() {
    if (!this.gameOver && this.playerChoice === null) {
      // If player hasn't made a choice, count as a loss
      this.computerScore++;
      this.roundsPlayed++;
      this.result = 'Time up! Computer wins!';
      this.emitGameEvent('timeUp', {
        playerScore: this.playerScore,
        computerScore: this.computerScore,
        roundsPlayed: this.roundsPlayed
      });
      this.checkGameOver();
    }
  }

  play(playerChoice: string): void {
    if (this.gameOver || this.playerChoice !== null) return;

    this.playerChoice = playerChoice;

    // Add a slight delay for computer's choice to simulate thinking
    setTimeout(() => {
      // Computer choice is random but can be adjusted based on difficulty
      let computerChoiceIndex = Math.floor(Math.random() * 3);

      // For HARD difficulty, computer has a slight advantage
      if (this.config.difficulty === 'HARD' && Math.random() < 0.3) {
        // Find the choice that beats the player's choice
        if (playerChoice === 'rock') computerChoiceIndex = 1; // paper
        else if (playerChoice === 'paper') computerChoiceIndex = 2; // scissors
        else computerChoiceIndex = 0; // rock
      }

      this.computerChoice = this.choices[computerChoiceIndex];
      this.result = this.getResult();

      // Update scores
      if (this.result === 'You win!') {
        this.playerScore++;
      } else if (this.result === 'Computer wins!') {
        this.computerScore++;
      }

      this.roundsPlayed++;

      // Emit game event
      this.emitGameEvent('round', {
        playerChoice: this.playerChoice,
        computerChoice: this.computerChoice,
        result: this.result,
        playerScore: this.playerScore,
        computerScore: this.computerScore,
        roundsPlayed: this.roundsPlayed
      });

      // Check if game is over
      this.checkGameOver();

      // Reset timer for next round
      this.clearTimer();
      if (!this.gameOver) {
        setTimeout(() => {
          this.resetRound();
        }, 2000); // Wait 2 seconds before starting next round
      }
    }, 500); // 500ms delay for computer's choice
  }

  resetRound(): void {
    this.playerChoice = null;
    this.computerChoice = null;
    this.result = null;
    this.startTimer();
  }

  checkGameOver(): void {
    const roundsToWin = Math.ceil(this.config.rounds / 2);

    if (this.playerScore >= roundsToWin) {
      this.gameOver = true;
      this.gameResult = 'win';
      this.clearTimer();
      this.emitGameEvent('end', {
        playerScore: this.playerScore,
        computerScore: this.computerScore,
        roundsPlayed: this.roundsPlayed,
        result: 'win'
      });
    } else if (this.computerScore >= roundsToWin) {
      this.gameOver = true;
      this.gameResult = 'lose';
      this.clearTimer();
      this.emitGameEvent('end', {
        playerScore: this.playerScore,
        computerScore: this.computerScore,
        roundsPlayed: this.roundsPlayed,
        result: 'lose'
      });
      this.attemptsRemaining--;
    } else if (this.roundsPlayed >= this.config.rounds) {
      // If all rounds are played but no clear winner (should be rare with odd number of rounds)
      this.gameOver = true;
      this.gameResult = this.playerScore > this.computerScore ? 'win' : 'lose';
      this.clearTimer();
      this.emitGameEvent('end', {
        playerScore: this.playerScore,
        computerScore: this.computerScore,
        roundsPlayed: this.roundsPlayed,
        result: this.gameResult
      });
      if (this.gameResult === 'lose') {
        this.attemptsRemaining--;
      }
    }
  }

  getChoiceIcon(choice: string | null): string {
    if (!choice) return '';
    const icons: Record<string, string> = {
      rock: '✊',
      paper: '✋',
      scissors: '✌️',
    };
    return icons[choice.toLowerCase()] || '';
  }

  resetGame(): void {
    if (this.attemptsRemaining <= 0) {
      this.emitGameEvent('outOfAttempts', {});
      return;
    }

    this.playerScore = 0;
    this.computerScore = 0;
    this.roundsPlayed = 0;
    this.gameOver = false;
    this.gameResult = null;
    this.playerChoice = null;
    this.computerChoice = null;
    this.result = null;
    this.startTimer();

    this.emitGameEvent('restart', {
      attemptsRemaining: this.attemptsRemaining
    });
  }

  handleControlClick(control: any): void {
    // Convert the event to a string if it's not already
    const controlName = typeof control === 'string' ? control : control.toString();

    console.log('Control clicked:', controlName);

    switch (controlName) {
      case 'restart':
        this.resetGame();
        break;
      case 'newGame':
        this.attemptsRemaining = this.config.maxAttempts || 3;
        this.initializeGame();
        break;
      default:
        console.warn('Unknown control:', controlName);
    }
  }

  emitGameEvent(type: string, data: any): void {
    const event: RockPaperScissorsGameEvent = {
      type: type,
      data: data,
      timestamp: new Date().toISOString(),
      gameId: this.gameId
    };

    this.gameEventEmitter.emit(event);
    console.log('Rock Paper Scissors game event:', event);
  }

  getScoreItems() {
    return [
      { title: 'Score', number: `${this.playerScore} - ${this.computerScore}` },
      { title: 'Round', number: `${this.roundsPlayed}/${this.config.rounds}` },
      { title: 'Time', number: `${this.timeRemaining}s` },
      { title: 'Attempts', number: this.attemptsRemaining }
    ];
  }

  private getResult(): string {
    if (this.playerChoice === this.computerChoice) {
      return "It's a tie!";
    }
    if (
      (this.playerChoice === 'rock' && this.computerChoice === 'scissors') ||
      (this.playerChoice === 'paper' && this.computerChoice === 'rock') ||
      (this.playerChoice === 'scissors' && this.computerChoice === 'paper')
    ) {
      return 'You win!';
    }
    return 'Computer wins!';
  }
}
