<div class="flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800">
  <!-- Score Display -->
  <div class="w-full max-w-md mx-auto px-4 py-2">
    <games-score [scores]="getScoreItems()"></games-score>
  </div>

  <!-- Game Title -->
  <div class="text-center mb-1">
    <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-indigo-500">Rock Paper Scissors</h1>
    <p *ngIf="isDemoMode" class="text-xs text-amber-400 mb-1">Demo Mode</p>
    <p class="text-xs text-slate-400 mb-1">Best of {{config.rounds}} rounds</p>
  </div>

  <!-- Game Board -->
  <div class="flex overflow-hidden flex-col flex-grow items-center p-4">
    <!-- Game choices with icons -->
    <div *ngIf="!playerChoice && !gameOver" class="flex space-x-6 mb-4 mt-4">
      <button
        *ngFor="let choice of choices"
        (click)="play(choice)"
        class="group flex flex-col items-center transition-all duration-200 hover:scale-110 active:scale-95"
      >
        <div
          class="w-20 h-20 mb-2 rounded-full bg-gradient-to-br from-blue-500/80 to-indigo-700/80 flex items-center justify-center shadow-lg border border-blue-400/30 choice-button"
        >
          <span class="text-3xl text-white">{{ getChoiceIcon(choice) }}</span>
        </div>
        <span class="text-sm font-medium text-slate-300">{{ choice | titlecase }}</span>
      </button>
    </div>

    <!-- Timer display when waiting for choice -->
    <div *ngIf="!playerChoice && !gameOver && timeRemaining > 0" class="mt-4 text-center">
      <div class="timer-container">
        <div class="timer-bar" [style.width.%]="(timeRemaining / config.timeLimit) * 100"></div>
      </div>
      <p class="text-sm text-slate-400 mt-1">Time remaining: {{timeRemaining}}s</p>
    </div>

    <!-- Game results with animations -->
    <div
      *ngIf="playerChoice && computerChoice && !gameOver"
      class="text-center animate-fadeIn mt-4"
    >
      <div class="flex items-center justify-center space-x-12 mb-6">
        <!-- Player choice -->
        <div class="text-center">
          <div
            class="w-24 h-24 mb-2 rounded-full bg-gradient-to-br from-green-500/80 to-green-700/80 flex items-center justify-center shadow-lg border border-green-400/30 animate-bounceIn"
          >
            <span class="text-4xl text-white">{{ getChoiceIcon(playerChoice) }}</span>
          </div>
          <p class="font-medium text-slate-300">You</p>
        </div>

        <!-- VS text -->
        <div class="text-2xl font-bold text-slate-400">VS</div>

        <!-- Computer choice -->
        <div class="text-center">
          <div
            class="w-24 h-24 mb-2 rounded-full bg-gradient-to-br from-red-500/80 to-red-700/80 flex items-center justify-center shadow-lg border border-red-400/30 animate-bounceIn"
          >
            <span class="text-4xl text-white">{{ getChoiceIcon(computerChoice) }}</span>
          </div>
          <p class="font-medium text-slate-300">Computer</p>
        </div>
      </div>

      <!-- Result text -->
      <p
        class="text-2xl font-bold animate-fadeInUp mb-6"
        [ngClass]="{
          'text-green-400': result === 'You win!',
          'text-red-400': result === 'Computer wins!' || result === 'Time up! Computer wins!',
          'text-yellow-400': result === 'It\'s a tie!'
        }"
      >
        {{ result }}
      </p>
    </div>
  </div>

  <!-- Game Controls -->
  <div class="p-2 my-2 space-y-2 border-t border-slate-700">
    <lib-games-play-button
      [controls]="gameControls"
      (controlClicked)="handleControlClick($event)"
    ></lib-games-play-button>
  </div>

  <!-- Win/Lose Overlay -->
  <lib-win-lose-overlay
    *ngIf="gameResult"
    [status]="gameResult"
    [message]="
      gameResult === 'win'
        ? 'Congratulations! You won the match!'
        : 'Better luck next time! Computer won the match!'
    "
    (restart)="resetGame()"
  ></lib-win-lose-overlay>
</div>
