import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GamesScoreComponent } from '../components/games-score/games-score.component';
import { WinLoseOverlayComponent } from '../components/win-lose-overlay/win-lose-overlay.component';
import { Game } from 'lp-client-api';

interface HangmanConfig {
  difficulty: 'EASY' | 'MEDIUM' | 'HARD';
  maxAttempts: number;
  wordLists?: string[][][];
}

interface GameEvent {
  type: string;
  data: any;
  timestamp: string;
  gameId: string;
}

@Component({
  selector: 'lib-hangman',
  templateUrl: './hangman.component.html',
  styleUrls: ['./hangman.component.css'],
  standalone: true,
  imports: [CommonModule, GamesScoreComponent, WinLoseOverlayComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class HangmanComponent implements OnInit {
  @Input() gameId: string = '';
  @Input() config: any = {};
  @Input() gameInstance: any = null;
  @Input() game?: Game;

  @Output() gameEvent = new EventEmitter<GameEvent>();

  // Default word lists
  private defaultWordLists: string[][][] = [
    [
      ['CAT', 'DOG', 'RAT', 'BAT', 'HAT'],
      ['BIRD', 'FISH', 'LION', 'BEAR', 'WOLF'],
      ['TIGER', 'MONKEY', 'GIRAFFE', 'ELEPHANT', 'PENGUIN'],
      ['CROCODILE', 'HIPPOPOTAMUS', 'RHINOCEROS', 'CHIMPANZEE', 'KANGAROO'],
      ['TYRANNOSAURUS', 'BRACHIOSAURUS', 'VELOCIRAPTOR', 'STEGOSAURUS', 'TRICERATOPS'],
    ],
    [
      ['APPLE', 'GRAPE', 'PEACH', 'LEMON', 'MELON'],
      ['BANANA', 'ORANGE', 'CHERRY', 'MANGO', 'KIWI'],
      ['AVOCADO', 'COCONUT', 'PAPAYA', 'APRICOT', 'GUAVA'],
      ['PINEAPPLE', 'BLUEBERRY', 'RASPBERRY', 'STRAWBERRY', 'BLACKBERRY'],
      ['WATERMELON', 'POMEGRANATE', 'DRAGONFRUIT', 'PASSIONFRUIT', 'CANTALOUPE'],
    ],
    [
      ['RED', 'BLUE', 'PINK', 'GOLD', 'GRAY'],
      ['GREEN', 'PURPLE', 'YELLOW', 'BLACK', 'WHITE'],
      ['ORANGE', 'VIOLET', 'INDIGO', 'SILVER', 'BRONZE'],
      ['CRIMSON', 'MAGENTA', 'LAVENDER', 'TURQUOISE', 'MAROON'],
      ['CERULEAN', 'VERMILION', 'CHARTREUSE', 'PERIWINKLE', 'BURGUNDY'],
    ]
  ];

  // Game state
  wordLists: string[][][] = [];
  currentCategory: number = 0;
  currentLevel: number = 0;
  word: string = '';
  guessedLetters: Set<string> = new Set();
  remainingAttempts: number = 6;
  gameStatus: 'playing' | 'won' | 'lost' = 'playing';
  gameResult: 'win' | 'lose' | null = null;
  score: number = 0;
  demoMode: boolean = false;
  errorMessage: string = '';

  ngOnInit() {
    console.log('Hangman component initialized');
    console.log('Game ID:', this.gameId);
    console.log('Config:', this.config);
    console.log('Game Instance:', this.gameInstance);

    try {
      this.initializeGame();
    } catch (error) {
      console.error('Error initializing game:', error);
      this.initializeDemoMode();
    }
  }

  /**
   * Initialize the game with configuration
   */
  private initializeGame(): void {
    // Set up word lists
    if (this.config?.wordLists && Array.isArray(this.config.wordLists)) {
      this.wordLists = this.config.wordLists;
    } else {
      this.wordLists = this.defaultWordLists;
    }

    // Set up difficulty
    if (this.config?.difficulty) {
      switch (this.config.difficulty) {
        case 'EASY':
          this.remainingAttempts = 8;
          break;
        case 'MEDIUM':
          this.remainingAttempts = 6;
          break;
        case 'HARD':
          this.remainingAttempts = 4;
          break;
        default:
          this.remainingAttempts = 6;
      }
    } else {
      this.remainingAttempts = 6;
    }

    // Start the game
    this.startNewGame();
  }

  /**
   * Initialize demo mode when API fails
   */
  private initializeDemoMode(): void {
    console.log('Initializing demo mode for Hangman game');
    this.demoMode = true;
    this.wordLists = this.defaultWordLists;
    this.remainingAttempts = 6;
    this.startNewGame();
  }

  /**
   * Start a new game
   */
  startNewGame() {
    this.word = this.getRandomWord();
    this.guessedLetters.clear();
    this.remainingAttempts = this.config?.maxAttempts || 6;
    this.gameStatus = 'playing';
    this.gameResult = null;

    // Emit game start event
    this.emitGameEvent('game_start', {
      level: this.currentLevel + 1,
      category: this.currentCategory,
      wordLength: this.word.length
    });
  }

  /**
   * Get a random word from the current level
   */
  getRandomWord(): string {
    const categoryWords = this.wordLists[this.currentCategory];
    const levelWords = categoryWords[this.currentLevel];
    return levelWords[Math.floor(Math.random() * levelWords.length)];
  }

  /**
   * Handle letter guess
   */
  guess(letter: string) {
    if (this.gameStatus !== 'playing') return;

    letter = letter.toUpperCase();
    if (!this.guessedLetters.has(letter)) {
      this.guessedLetters.add(letter);

      const isCorrect = this.word.includes(letter);

      if (!isCorrect) {
        this.remainingAttempts--;
      } else {
        // Add points for correct guess
        this.score += 10;
      }

      // Emit guess event
      this.emitGameEvent('letter_guess', {
        letter: letter,
        correct: isCorrect,
        remainingAttempts: this.remainingAttempts
      });

      this.checkGameStatus();
    }
  }

  /**
   * Check if the game is won or lost
   */
  checkGameStatus() {
    if (this.word.split('').every((letter) => this.guessedLetters.has(letter))) {
      this.gameStatus = 'won';
      this.gameResult = 'win';

      // Add bonus points for winning
      const bonusPoints = this.remainingAttempts * 20 + this.currentLevel * 50;
      this.score += bonusPoints;

      // Emit win event
      this.emitGameEvent('game_win', {
        word: this.word,
        level: this.currentLevel + 1,
        score: this.score,
        bonusPoints: bonusPoints
      });

      this.levelUp();
    } else if (this.remainingAttempts === 0) {
      this.gameStatus = 'lost';
      this.gameResult = 'lose';

      // Emit lose event
      this.emitGameEvent('game_lose', {
        word: this.word,
        level: this.currentLevel + 1,
        score: this.score
      });
    }
  }

  /**
   * Level up after winning
   */
  levelUp() {
    if (this.currentLevel < this.wordLists[this.currentCategory].length - 1) {
      this.currentLevel++;
    } else if (this.currentCategory < this.wordLists.length - 1) {
      this.currentCategory++;
      this.currentLevel = 0;
    }
  }

  /**
   * Get the display word with underscores for unguessed letters
   */
  getDisplayWord(): string {
    return this.word
      .split('')
      .map((letter) => (this.guessedLetters.has(letter) ? letter : '_'))
      .join(' ');
  }

  /**
   * Emit a game event
   */
  private emitGameEvent(eventType: string, data: any): void {
    const gameEvent: GameEvent = {
      type: eventType,
      data: data,
      timestamp: new Date().toISOString(),
      gameId: this.gameId
    };

    this.gameEvent.emit(gameEvent);
    console.log('Game event emitted:', gameEvent);
  }

  hangmanStages: string[] = [
    `
   +---+
   |   |
       |
       |
       |
       |
=========`,
    `
   +---+
   |   |
   O   |
       |
       |
       |
=========`,
    `
   +---+
   |   |
   O   |
   |   |
       |
       |
=========`,
    `
   +---+
   |   |
   O   |
  /|   |
       |
       |
=========`,
    `
   +---+
   |   |
   O   |
  /|\\  |
       |
       |
=========`,
    `
   +---+
   |   |
   O   |
  /|\\  |
  /    |
       |
=========`,
    `
   +---+
   |   |
   O   |
  /|\\  |
  / \\  |
       |
=========`,
  ];
}
