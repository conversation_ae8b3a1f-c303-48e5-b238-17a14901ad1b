<div class="flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800">
  <!-- Demo Mode Indicator -->
  <div *ngIf="demoMode" class="demo-mode-indicator">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
    <span>DEMO MODE</span>
  </div>

  <!-- Score Display -->
  <games-score
    [scores]="[
      { title: 'Level', number: currentLevel + 1 },
      { title: 'Attempts', number: remainingAttempts },
      { title: 'Score', number: score }
    ]"
  ></games-score>

  <!-- Game Title -->
  <div class="game-title">
    <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-amber-400 to-orange-500">Hangman</h1>
    <p class="text-xs text-slate-400 mb-1">Guess the word before the hangman is complete!</p>
  </div>

  <!-- Game Board Container -->
  <div class="game-board-container">
    <!-- Word Display -->
    <div class="word-display w-full max-w-md mx-auto">
      <p>{{ getDisplayWord() }}</p>
    </div>

    <!-- Hangman Display -->
    <div class="hangman-display w-full max-w-md mx-auto">
      <pre>{{ hangmanStages[6 - remainingAttempts] }}</pre>
    </div>

    <!-- Keyboard -->
    <div *ngIf="gameStatus === 'playing'" class="keyboard-container w-full max-w-md mx-auto">
      <div class="keyboard">
        <button
          *ngFor="let letter of 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('')"
          (click)="guess(letter)"
          [disabled]="guessedLetters.has(letter)"
          class="keyboard-button"
        >
          {{ letter }}
        </button>
      </div>
    </div>
  </div>

  <!-- Win/Lose Overlay -->
  <lib-win-lose-overlay
    [status]="gameResult"
    (restart)="startNewGame()"
    [message]="gameResult === 'lose' ? 'The word was: ' + word : ''"
  ></lib-win-lose-overlay>
</div>
