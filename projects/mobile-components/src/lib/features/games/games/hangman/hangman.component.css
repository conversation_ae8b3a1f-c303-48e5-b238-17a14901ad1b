/* Import shared game styles */
@import '../styles/game-styles.css';

:host {
  @apply block w-full h-full;
}

.game-container {
  @apply flex overflow-hidden flex-col h-screen rounded-lg bg-gradient-to-b from-slate-900 to-slate-800;
  position: relative;
}

/* Game Title */
.game-title {
  @apply text-center mb-1;
}

.game-title h1 {
  @apply text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-amber-400 to-orange-500;
}

.game-title p {
  @apply text-xs text-slate-400 mb-1;
}

/* Word Display */
.word-display {
  @apply p-4 mt-2 mb-4 rounded-xl bg-slate-700/50 border border-slate-600 shadow-xl;
  position: relative;
  overflow: hidden;
}

.word-display::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-purple-500/10;
  filter: blur(20px);
  z-index: 0;
}

.word-display p {
  @apply font-mono text-3xl text-center text-white relative z-10;
  letter-spacing: 0.25rem;
}

/* Hangman Display */
.hangman-display {
  @apply mb-4 p-4 rounded-xl bg-slate-700/50 border border-slate-600 shadow-xl text-center;
  position: relative;
  overflow: hidden;
}

.hangman-display::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-purple-500/10;
  filter: blur(20px);
  z-index: 0;
}

.hangman-display pre {
  @apply font-mono text-sm text-white relative z-10;
  white-space: pre;
}

/* Keyboard */
.keyboard-container {
  @apply mb-4 p-2 rounded-xl bg-slate-700/50 border border-slate-600 shadow-xl;
  position: relative;
  overflow: hidden;
}

.keyboard-container::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-purple-500/10;
  filter: blur(20px);
  z-index: 0;
}

.keyboard {
  @apply grid gap-2 relative z-10;
  grid-template-columns: repeat(auto-fit, minmax(2.5rem, 1fr));
}

.keyboard-button {
  @apply px-2 py-3 text-lg font-bold text-white rounded-lg shadow-md transition-all duration-200;
  @apply bg-gradient-to-r from-blue-500 to-blue-600;
  @apply hover:from-blue-600 hover:to-blue-700;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-400;
  @apply disabled:from-slate-400 disabled:to-slate-500 disabled:cursor-not-allowed;
  position: relative;
  overflow: hidden;
}

.keyboard-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgb(253, 224, 71); /* Yellow-300 */
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.keyboard-button:active::after {
  animation: rippleAnimation 0.6s ease-out;
}

@keyframes rippleAnimation {
  0% {
    opacity: 1;
    transform: scale(0, 0) translate(-50%, -50%);
    box-shadow: 0 0 0 0 rgba(253, 224, 71, 0.8);
  }
  50% {
    opacity: 0.9;
  }
  100% {
    opacity: 0;
    transform: scale(10, 10) translate(-50%, -50%);
    box-shadow: 0 0 40px 25px rgba(253, 224, 71, 0);
  }
}

/* Game Board Container */
.game-board-container {
  @apply flex-grow flex flex-col items-center justify-center p-2;
  position: relative;
  z-index: 1;
}

/* Demo Mode Indicator */
.demo-mode-indicator {
  @apply bg-gradient-to-r from-amber-400 to-yellow-500 text-black text-center py-1 px-2 text-sm font-bold rounded-b-md shadow-md flex items-center justify-center space-x-1;
}

/* Animations */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pulse-animation {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .word-display p {
    @apply text-2xl;
  }

  .keyboard-button {
    @apply px-1 py-2 text-base;
  }
}