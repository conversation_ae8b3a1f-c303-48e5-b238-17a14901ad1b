import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { IonicModule } from '@ionic/angular';
import { GoogleMapsModule } from '@angular/google-maps';
import { SubmitSelfieComponent } from './submit-selfie/submit-selfie.component';
import { WheelSpinComponent } from './wheel-spin/wheel-spin.component';
import { TetrisComponent } from './tetris/tetris.component';
import { CrosswordComponent } from './crossword/crossword.component';
import { SudokuComponent } from './sudoku/sudoku.component';

// Import game modules
import { BlackjackComponent } from './blackjack/blackjack.component';
import { MinesweeperComponent } from './minesweeper/minesweeper.component';
import { SolitaireComponent } from './solitaire/solitaire.component';
import { CandyCrushComponent } from './candy-crush/candy-crush.component';
import { TreasureHuntComponent } from './treasure-hunt/treasure-hunt.component';
import { WinLoseOverlayComponent } from './components/win-lose-overlay/win-lose-overlay.component';
import { GamesScoreComponent } from './components/games-score/games-score.component';

@NgModule({
  declarations: [
  ],
  imports: [
    CommonModule,
    FormsModule,
    GoogleMapsModule,
    BrowserAnimationsModule,
    IonicModule,
    // Import standalone components
    SubmitSelfieComponent,
    TetrisComponent,
    WheelSpinComponent,
    // Import standalone game components
    BlackjackComponent,
    MinesweeperComponent,
    SolitaireComponent,
    CrosswordComponent,
    SudokuComponent,
    CandyCrushComponent,
    TreasureHuntComponent,
    WinLoseOverlayComponent,
    GamesScoreComponent
  ],
  exports: [
    WheelSpinComponent,
    // Export standalone components
    SubmitSelfieComponent,
    TetrisComponent,
    // Export standalone game components
    BlackjackComponent,
    MinesweeperComponent,
    SolitaireComponent,
    CrosswordComponent,
    SudokuComponent,
    CandyCrushComponent,
    TreasureHuntComponent,
    WinLoseOverlayComponent,
    GamesScoreComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class GamesModule {}
