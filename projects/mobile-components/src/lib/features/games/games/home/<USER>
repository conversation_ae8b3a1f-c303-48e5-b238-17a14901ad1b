import { Component, Injector, ChangeDetectorRef, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { AbstractComponent } from '../../../../shared/abstract.component';

import {
  KeyCloakService,
  MemberProfile,
  MemberService,
  LssConfig,
} from 'lp-client-api';
import { Router } from '@angular/router';

@Component({
  selector: 'pages-games-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class GamesHomeComponent extends AbstractComponent {
  constructor(injector: Injector) {
    super(injector);
  }
}
