import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import Geolocation components
// Standalone (0)
// None

// Non-Standalone (2)
import { GeoComponent } from './geo/geo.component';
import { GeoLocationComponent } from './geo-location/geo-location.component';


@NgModule({
  declarations: [
    // Non-Standalone Components (2)
 
  ],
  imports: [
    CommonModule,
    IonicModule,
    GeoComponent,
    GeoLocationComponent,
    // Standalone Components (0)
    // None
  ],
  exports: [
    // Non-Standalone Components (2)
    GeoComponent,
    GeoLocationComponent,
  ]
})
export class GeolocationModule { }

