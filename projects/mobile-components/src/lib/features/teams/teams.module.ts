import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import Teams components
// Standalone (2)
import { TeamListCompactComponent } from './team-list-compact/team-list-compact.component';
import { TeamSearchCompactComponent } from './team-search-compact/team-search-compact.component';

// Non-Standalone (0)
// None

@NgModule({
  declarations: [
    // Non-Standalone Components (0)
    // None
  ],
  imports: [
    CommonModule,
    IonicModule,
    // Standalone Components (2)
    TeamListCompactComponent,
    TeamSearchCompactComponent,
  ],
  exports: [
    // Standalone Components (2)
    TeamListCompactComponent,
    TeamSearchCompactComponent,
  ]
})
export class TeamsModule { }
