import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import Invites components
// Standalone (0)
// None

// Non-Standalone (1)
import { FilterInviteComponent } from './filter-invite/filter-invite.component';


@NgModule({
  declarations: [
    // Non-Standalone Components (1)
  ],
  imports: [
    CommonModule,
    IonicModule,
    FilterInviteComponent,

    // Standalone Components (0)
    // None
  ],
  exports: [
    // Non-Standalone Components (1)
    FilterInviteComponent,
  ]
})
export class InvitesModule { }

