<!-- Security-Enhanced Password Component Template -->
<div [class]="computedClasses()">
  <!-- Reset Password Mode -->
  <div *ngIf="mode === 'reset'" class="password-reset-container">
    <ion-header [translucent]="true">
      <ion-toolbar class="header-backgrounds">
        <ion-buttons slot="start">
          <ion-button class="" [routerLink]="['/']" [disabled]="disabled">
            <ion-icon slot="icon-only" name="arrow-back-outline"></ion-icon>
          </ion-button>
        </ion-buttons>
        <ion-title>{{ label || 'Forgot Password' }}</ion-title>
        <ion-buttons slot="end"></ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content class="app-background">
      <ion-grid>
        <ion-row class="center">
          <ion-col>
            <div class="logo-col">
              <img
                class="logo"
                [src]="lssConfig.icon"
                alt="Application Logo"
                loading="lazy"
              />
            </div>
          </ion-col>
        </ion-row>
      </ion-grid>

      <div class="ion-padding">
        <!-- Account Lockout Warning -->
        <ion-card *ngIf="isLocked()" color="danger" class="security-warning">
          <ion-card-content>
            <ion-icon name="lock-closed" class="warning-icon"></ion-icon>
            <h3>Account Temporarily Locked</h3>
            <p>Too many failed attempts. Please wait before trying again.</p>
          </ion-card-content>
        </ion-card>

        <form class="form" [formGroup]="_form" (ngSubmit)="resetPassword()">
          <ion-item lines="none" fill="outline" [class.item-disabled]="disabled || isLocked()">
            <ion-label position="floating">{{ accountNumberLabel }}</ion-label>
            <ion-icon slot="start" name="person" aria-hidden="true"></ion-icon>
            <ion-input 
              type="text" 
              formControlName="accountNumber"
              [placeholder]="accountNumberPlaceholder"
              [disabled]="disabled || isLocked()"
              autocomplete="username"
              inputmode="text"
              maxlength="50"
              (ionInput)="onPasswordInput($event)"
            ></ion-input>
          </ion-item>
          
          <ion-item lines="none" *ngIf="isFormComponentInvalid('accountNumber')">
            <div
              *ngFor="let error of getComponentErrors('accountNumber')"
              class="validator-error"
              role="alert"
            >
              <ion-note color="danger">{{ error }}</ion-note>
            </div>
          </ion-item>

          <div class="form-spacer"></div>
          
          <ion-button
            expand="block"
            class="save"
            type="submit"
            [disabled]="!isFormValid() || disabled || isLocked()"
            color="primary"
          >
            Reset Password
          </ion-button>
        </form>
      </div>
    </ion-content>
  </div>

  <!-- Password Change/Create Mode -->
  <ion-content 
    class="ion-padding password-form-container" 
    *ngIf="mode !== 'reset'"
  >
    <!-- Account Lockout Warning -->
    <ion-card *ngIf="isLocked()" color="danger" class="security-warning">
      <ion-card-content>
        <ion-icon name="lock-closed" class="warning-icon"></ion-icon>
        <h3>Too Many Attempts</h3>
        <p>Please wait before trying again for security reasons.</p>
      </ion-card-content>
    </ion-card>

    <form class="form" [formGroup]="_form" (ngSubmit)="resetPassword()">
      <!-- Password Field -->
      <ion-item lines="none" [class.item-disabled]="disabled || isLocked()">
        <ion-label position="floating">{{ label }}</ion-label>
        <ion-icon slot="start" name="lock-closed" aria-hidden="true"></ion-icon>
        <ion-input 
          [type]="passwordVisible() ? 'text' : 'password'"
          formControlName="password"
          [placeholder]="placeholder"
          [disabled]="disabled || isLocked()"
          autocomplete="new-password"
          [maxlength]="config.maxLength || null"
          (ionInput)="onPasswordInput($event)"
        ></ion-input>
        <ion-button 
          *ngIf="config.showToggleVisibility"
          slot="end" 
          fill="clear" 
          (click)="togglePasswordVisibility()"
          [disabled]="disabled || isLocked()"
          type="button"
          [attr.aria-label]="passwordVisible() ? 'Hide password' : 'Show password'"
        >
          <ion-icon 
            [name]="passwordVisible() ? 'eye-off' : 'eye'"
            aria-hidden="true"
          ></ion-icon>
        </ion-button>
      </ion-item>

      <!-- Password Validation Errors -->
      <ion-item lines="none" *ngIf="isFormComponentInvalid('password')">
        <div
          *ngFor="let error of getComponentErrors('password')"
          class="validator-error"
          role="alert"
        >
          <ion-note color="danger">{{ error }}</ion-note>
        </div>
      </ion-item>

      <!-- Password Strength Indicator -->
      <div *ngIf="config.showStrengthIndicator && _form.get('password')?.value" class="strength-indicator">
        <ion-progress-bar 
          [value]="strength() === 'weak' ? 0.25 : strength() === 'fair' ? 0.5 : strength() === 'good' ? 0.75 : 1"
          [color]="getStrengthColor()"
        ></ion-progress-bar>
        <ion-text [color]="getStrengthColor()" class="strength-text">
          <small>Password strength: {{ getStrengthText() }}</small>
        </ion-text>
      </div>

      <!-- Confirm Password Field -->
      <ion-item 
        *ngIf="config.requireConfirmation"
        lines="none" 
        [class.item-disabled]="disabled || isLocked()"
      >
        <ion-label position="floating">{{ confirmLabel }}</ion-label>
        <ion-icon slot="start" name="lock-closed" aria-hidden="true"></ion-icon>
        <ion-input 
          [type]="confirmVisible() ? 'text' : 'password'"
          formControlName="passwordConfirm"
          [placeholder]="confirmPlaceholder"
          [disabled]="disabled || isLocked()"
          autocomplete="new-password"
          [maxlength]="config.maxLength || null"
        ></ion-input>
        <ion-button 
          *ngIf="config.showToggleVisibility"
          slot="end" 
          fill="clear" 
          (click)="toggleConfirmVisibility()"
          [disabled]="disabled || isLocked()"
          type="button"
          [attr.aria-label]="confirmVisible() ? 'Hide password confirmation' : 'Show password confirmation'"
        >
          <ion-icon 
            [name]="confirmVisible() ? 'eye-off' : 'eye'"
            aria-hidden="true"
          ></ion-icon>
        </ion-button>
      </ion-item>

      <!-- Confirm Password Validation Errors -->
      <ion-item 
        *ngIf="config.requireConfirmation && isFormComponentInvalid('passwordConfirm')"
        lines="none"
      >
        <div
          *ngFor="let error of getComponentErrors('passwordConfirm')"
          class="validator-error"
          role="alert"
        >
          <ion-note color="danger">{{ error }}</ion-note>
        </div>
      </ion-item>

      <div class="form-spacer"></div>
      
      <ion-button
        expand="block"
        class="save"
        type="submit"
        [disabled]="!isFormValid() || disabled || isLocked()"
        [color]="variant === 'default' ? 'primary' : variant"
      >
        {{ mode === 'create' ? 'Create Password' : 'Update Password' }}
      </ion-button>
    </form>
  </ion-content>
</div>
