import { Component, Injector, OnInit, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, Input, Output, EventEmitter, computed, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AbstractControlOptions, Validators, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { CustomValidators, LssConfig } from 'lp-client-api';
import { AbstractFormComponent } from '../../../shared/abstract.form.component';

export type PasswordSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
export type PasswordVariant = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
export type PasswordRounded = 'none' | 'sm' | 'md' | 'lg' | 'full';
export type PasswordStrength = 'weak' | 'fair' | 'good' | 'strong';

export interface PasswordConfig {
  showStrengthIndicator?: boolean;
  showToggleVisibility?: boolean;
  requireConfirmation?: boolean;
  minLength?: number;
  maxLength?: number;
  requireSpecialChars?: boolean;
  requireNumbers?: boolean;
  requireMixedCase?: boolean;
}

@Component({
  selector: 'app-password',
  templateUrl: 'password.component.html',
  styleUrls: ['password.component.scss'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, IonicModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class PasswordComponent
  extends AbstractFormComponent<any>
  implements OnInit
{
  // Standard UI inputs
  @Input() className: string = '';
  @Input() size: PasswordSize = 'md';
  @Input() variant: PasswordVariant = 'default';
  @Input() rounded: PasswordRounded = 'md';
  @Input() disabled: boolean = false;

  // Authentication-specific inputs
  @Input() placeholder: string = 'Enter password';
  @Input() confirmPlaceholder: string = 'Confirm password';
  @Input() label: string = 'Password';
  @Input() confirmLabel: string = 'Confirm Password';
  @Input() config: PasswordConfig = {
    showStrengthIndicator: true,
    showToggleVisibility: true,
    requireConfirmation: true,
    minLength: 8,
    maxLength: 128,
    requireSpecialChars: true,
    requireNumbers: true,
    requireMixedCase: true
  };
  @Input() mode: 'reset' | 'change' | 'create' = 'reset';
  @Input() accountNumberLabel: string = 'Account Number';
  @Input() accountNumberPlaceholder: string = 'Enter account number';
  
  // Security inputs
  @Input() sessionTimeout: number = 900000; // 15 minutes
  @Input() maxAttempts: number = 3;
  @Input() lockoutDuration: number = 300000; // 5 minutes

  // Event outputs
  @Output() passwordChange = new EventEmitter<string>();
  @Output() passwordStrengthChange = new EventEmitter<PasswordStrength>();
  @Output() resetRequest = new EventEmitter<string>();
  @Output() securityEvent = new EventEmitter<{type: string, data: any}>();

  // Internal state
  private _passwordVisible = signal(false);
  private _confirmVisible = signal(false);
  private _strength = signal<PasswordStrength>('weak');
  private _attempts = signal(0);
  private _lastAttempt = signal<number | null>(null);

  // Computed properties
  public passwordVisible = computed(() => this._passwordVisible());
  public confirmVisible = computed(() => this._confirmVisible());
  public strength = computed(() => this._strength());
  public isLocked = computed(() => {
    const lastAttempt = this._lastAttempt();
    if (!lastAttempt || this._attempts() < this.maxAttempts) return false;
    return (Date.now() - lastAttempt) < this.lockoutDuration;
  });

  // Computed classes
  public computedClasses = computed(() => {
    const baseClasses = ['password-component'];
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };
    const variantClasses = {
      default: 'border-gray-300',
      primary: 'border-blue-500',
      secondary: 'border-gray-500',
      success: 'border-green-500',
      warning: 'border-yellow-500',
      danger: 'border-red-500'
    };
    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    return [
      ...baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      this.disabled ? 'opacity-50 cursor-not-allowed' : '',
      this.className
    ].filter(Boolean).join(' ');
  });

  public lssConfig: LssConfig;
  @Input() environment: any;
  constructor(injector: Injector) {
    super(injector);
    this.lssConfig = {} as LssConfig; // Initialize lssConfig
    this.generateForm();
    this.initializeSecurityFeatures();
  }

  ngOnInit(): void {
    this.startSessionTimeout();
  }

  private initializeSecurityFeatures(): void {
    // Initialize security event monitoring
    this.securityEvent.emit({
      type: 'component_initialized',
      data: { timestamp: Date.now(), mode: this.mode }
    });
  }

  private startSessionTimeout(): void {
    if (this.sessionTimeout > 0) {
      setTimeout(() => {
        this.securityEvent.emit({
          type: 'session_timeout',
          data: { timestamp: Date.now() }
        });
        this.clearSensitiveData();
      }, this.sessionTimeout);
    }
  }

  private clearSensitiveData(): void {
    if (this._form) {
      this._form.reset();
      this._form.markAsUntouched();
      this._form.markAsPristine();
    }
    this._passwordVisible.set(false);
    this._confirmVisible.set(false);
  }

  private sanitizeInput(input: string): string {
    // Basic input sanitization - remove potential XSS patterns
    return input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                .replace(/javascript:/gi, '')
                .replace(/on\w+\s*=/gi, '');
  }

  private calculatePasswordStrength(password: string): PasswordStrength {
    if (!password) return 'weak';
    
    let score = 0;
    
    // Length check
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;
    
    // Character variety checks
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;
    
    // Common patterns penalty
    if (/(.)\1{2,}/.test(password)) score--; // Repeated characters
    if (/123|abc|qwe/i.test(password)) score--; // Sequential characters
    
    if (score < 2) return 'weak';
    if (score < 4) return 'fair';
    if (score < 6) return 'good';
    return 'strong';
  }

  public onPasswordInput(event: any): void {
    const password = this.sanitizeInput(event.target.value);
    event.target.value = password; // Update the input with sanitized value
    
    if (this.config.showStrengthIndicator) {
      const strength = this.calculatePasswordStrength(password);
      this._strength.set(strength);
      this.passwordStrengthChange.emit(strength);
    }
    
    this.passwordChange.emit(password);
  }

  public togglePasswordVisibility(): void {
    this._passwordVisible.set(!this._passwordVisible());
    this.securityEvent.emit({
      type: 'password_visibility_toggled',
      data: { visible: this._passwordVisible(), timestamp: Date.now() }
    });
  }

  public toggleConfirmVisibility(): void {
    this._confirmVisible.set(!this._confirmVisible());
  }

  generateForm(): void {
    const passwordValidators = [
      Validators.required,
      Validators.minLength(this.config.minLength || 8),
      Validators.maxLength(this.config.maxLength || 128)
    ];

    if (this.config.requireSpecialChars) {
      passwordValidators.push(CustomValidators.passwordValidate());
    }

    if (this.mode === 'reset') {
      this.setFormState(this._formStateType.reset);
      this._form = this._formBuilder.group({
        accountNumber: [
          '', 
          [
            Validators.required,
            Validators.pattern(/^[a-zA-Z0-9]+$/), // Only alphanumeric
            Validators.minLength(3),
            Validators.maxLength(50)
          ]
        ],
      });
    } else {
      this.setFormState(this._formStateType.update);
      const formConfig: any = {
        password: ['', passwordValidators],
      };

      if (this.config.requireConfirmation) {
        formConfig.passwordConfirm = ['', [Validators.required]];
      }

      const formOptions: AbstractControlOptions = {};
      
      if (this.config.requireConfirmation) {
        formOptions.validators = Validators.compose([
          CustomValidators.matchValue(
            'password',
            'Password',
            'passwordConfirm',
            'Confirm Password'
          ),
        ]);
      }

      this._form = this._formBuilder.group(formConfig, formOptions);
    }
  }

  resetPassword(): void {
    if (this.isLocked()) {
      this.securityEvent.emit({
        type: 'attempt_while_locked',
        data: { timestamp: Date.now(), attempts: this._attempts() }
      });
      return;
    }

    if (!this.isFormValid()) {
      this._attempts.set(this._attempts() + 1);
      this._lastAttempt.set(Date.now());
      
      this.securityEvent.emit({
        type: 'invalid_attempt',
        data: { attempts: this._attempts(), timestamp: Date.now() }
      });
      
      if (this._attempts() >= this.maxAttempts) {
        this.securityEvent.emit({
          type: 'account_locked',
          data: { 
            attempts: this._attempts(), 
            lockoutDuration: this.lockoutDuration,
            timestamp: Date.now() 
          }
        });
      }
      return;
    }

    // Reset attempts on successful validation
    this._attempts.set(0);
    this._lastAttempt.set(null);

    const formValue = this._form.value;
    
    if (this.mode === 'reset') {
      // Sanitize account number before emitting
      const sanitizedAccountNumber = this.sanitizeInput(formValue.accountNumber);
      this.resetRequest.emit(sanitizedAccountNumber);
    } else {
      // For password change/create modes
      this.passwordChange.emit(formValue.password);
    }

    this.securityEvent.emit({
      type: 'form_submitted',
      data: { mode: this.mode, timestamp: Date.now() }
    });
  }

  // Helper methods for template
  public getStrengthColor(): string {
    const colors = {
      weak: 'danger',
      fair: 'warning', 
      good: 'primary',
      strong: 'success'
    };
    return colors[this.strength()];
  }

  public getStrengthText(): string {
    const texts = {
      weak: 'Weak',
      fair: 'Fair',
      good: 'Good', 
      strong: 'Strong'
    };
    return texts[this.strength()];
  }

  public override isFormComponentInvalid(fieldName: string): boolean {
    return super.isFormComponentInvalid(fieldName) && !this.isLocked();
  }
}
