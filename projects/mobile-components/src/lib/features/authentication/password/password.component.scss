.app-background {
  --background: var(--ion-color-base);
}
.action-card {
  text-align: center;

  background-color: var(--ion-color-primary-shade);
  border-radius: 1rem;
  box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.6);
}
.card-background {
  background-color: #fff;
  width: 100vw;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.form {
  background-color: #fff;
  padding: 6px;
}
.center {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-icon {
  position: relative;
  top: -25px;
  right: -23px;
  border-radius: 3rem;
  width: 4rem;
  height: 4rem;
  box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.4);
  text-align: center;
  padding: 25px 0;
  background-color: var(--ion-color-tertiary);
}
.points {
  --background: var(--ion-color-secondary);
  width: 100%;
  margin-left: 20px;
  height: 50px;
}

.save {
  --background: var(--ion-color-secondary);
}
.logo {
  height: 100px;
}
.title {
  margin-left: 20px;
  margin-bottom: 14px;
}
.action-name {
  position: relative;

  top: -20px;
}

ion-textarea {
  min-height: 140px;
}

.logo {
  height: 150px;
}
.logo-col {
  align-items: flex-end;
  --background: #ffff;
}
.contact-card {
  justify-content: center;
}
.contact {
  --background: var(--ion-color-primary);
  position: relative;
  height: 100vh;
}

.formbackground {
  margin: 10px;
}

.app-background {
  --background: var(--ion-color-base);
}
.action-card {
  text-align: center;

  background-color: var(--ion-color-primary-shade);
  border-radius: 1rem;
  box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.6);
}
.card-background {
  background-color: #fff;
  width: 100vw;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.center {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-icon {
  position: relative;
  top: -25px;
  right: -23px;
  border-radius: 3rem;
  width: 4rem;
  height: 4rem;
  box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.4);
  text-align: center;
  padding: 25px 0;
  background-color: var(--ion-color-tertiary);
}
.points {
  --background: var(--ion-color-secondary);
  width: 100%;
  margin-left: 20px;
  height: 50px;
}

.icon-call {
  color: var(--ion-color-tertiary);
  margin-right: 5px;
  margin-top: 5px;
}

.submit {
  --background: var(--ion-color-primary);
}
.phone-call {
  color: var(--ion-color-primary-contrast);
  text-decoration: none;
  margin-left: 35px;
}

.flex {
  width: 100%;
  justify-content: space-between;
}

.pg-title {
  margin-top: 20px;
}

.text-black {
  color: black;
  padding: 25px;
}

.text-red {
  color: red;
  padding: 25px;
}

.signup {
  .state-before {
  }

  .state-success {
    // background-image: url("../../../assets/images/signup.png"); // Temporarily commented out - asset path issue
    background-size: cover;
    background-position: center;
    height: 100%;
    display: flex;
    font-weight: 300;
  }
}
ion-modal {
  --width: 290px;
  --height: 392px;
  --border-radius: 8px;
}

ion-modal ion-datetime {
  height: 392px;
}
::-webkit-calendar-picker-indicator {
  filter: invert(1);
}
.center {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin: auto 0;
}
.formbackground {
  --background: var(--ion-color-base-shade);
  color: var(--ion-color-base-contrast);
  height: 70vh;
}
.success-text {
  text-align: center;
  flex-grow: 1;
  align-self: flex-end;
  margin-bottom: 20%;
  color: var(--ion-color-primary);
  ion-button {
    --background: var(--ion-color-primary);
    --color: var(--ion-color-primary-contrast);
    --border-radius: 64px;
  }
}

.title {
  background: var(--ion-color-primary);
  color: var(--ion-color-primary-contrast);
  height: 100px;
}

.app-background {
  --background: var(--ion-color-base);
}

.header-backgrounds {
  background: var(--ion-color-base-shade);
  display: flex;
  justify-content: space-between;
}

.logo {
  height: 80px;
  margin: auto 0;
}

.w-full {
  width: 100%;
}

.logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.app {
  --background: var(--ion-color-primary);
}
.p-0 {
  padding: 0 !important;
  --padding-start: 0px !important;
  width: 100%;
}

.save {
  --background: var(--ion-color-primary);
}
.header-button {
}

#time-button {
  display: none !important;
}

ion-popover {
  --width: 288px;
  --offset-y: -42px;
  color: var(--ion-color-primary);
}

ion-title {
  position: absolute;
  top: 0;
  left: 0;
  padding: 0 90px 1px;
  width: 100%;
  height: 100%;
  text-align: center;
}

.terms-link {
  display: flex;
  justify-content: center;
  padding-block-start: 4px;
  padding-block-end: 24px;
  font-size: 1.1rem;
}

/* Security Enhancement Styles */
.password-component {
  width: 100%;
}

.security-warning {
  margin-bottom: 1rem;

  .warning-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  h3 {
    margin: 0.5rem 0;
    font-weight: 600;
  }

  p {
    margin: 0;
    opacity: 0.9;
  }
}

.password-form-container {
  .form {
    background-color: transparent;
  }
}

.password-reset-container {
  .form {
    background-color: #fff;
    padding: 6px;
  }
}

/* Strength Indicator Styles */
.strength-indicator {
  margin: 0.5rem 0 1rem 0;

  ion-progress-bar {
    margin-bottom: 0.25rem;
    height: 4px;
    border-radius: 2px;
  }

  .strength-text {
    display: block;
    font-size: 0.75rem;
    font-weight: 500;
  }
}

/* Enhanced Form Styles */
.item-disabled {
  opacity: 0.6;
  pointer-events: none;
}

.validator-error {
  padding: 0.25rem 0;
  font-size: 0.75rem;

  ion-note {
    font-weight: 500;
  }
}

/* Size Variations */
.password-component {
  &.text-xs ion-input {
    font-size: 0.75rem;
  }

  &.text-sm ion-input {
    font-size: 0.875rem;
  }

  &.text-base ion-input {
    font-size: 1rem;
  }

  &.text-lg ion-input {
    font-size: 1.125rem;
  }

  &.text-xl ion-input {
    font-size: 1.25rem;
  }
}

/* Enhanced Security Visual Feedback */
.form-spacer {
  height: 1rem;
}

.save {
  margin-top: 1rem;
  font-weight: 600;

  &:disabled {
    opacity: 0.5;
  }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
  .strength-indicator ion-progress-bar {
    transition: none;
  }
}

/* Focus States for Better Accessibility */
ion-input:focus-within {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

ion-button:focus {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}
