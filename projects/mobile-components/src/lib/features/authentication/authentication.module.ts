import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router'; // Authentication flows might involve routing

// Import Authentication components
// Standalone (4)
import { PasswordComponent } from './password/password.component';
import { PinComponent } from './pin/pin.component';
import { SecurityComponent } from './security/security.component';
import { SecureComponent } from './secure/secure.component';

@NgModule({
  declarations: [
    // Non-Standalone Components - NOW EMPTY
  ],
  imports: [
    CommonModule,
    IonicModule,
    RouterModule,
    // Standalone Components (4)
    PasswordComponent,
    PinComponent,
    SecurityComponent,
    SecureComponent, // Added: Now treated as standalone
  ],
  exports: [
    // Non-Standalone Components - NOW EMPTY
    // Standalone Components (4)
    PasswordComponent,
    PinComponent,
    SecurityComponent,
    SecureComponent, // Kept: Exports are the same for standalone
  ]
})
export class AuthenticationModule { }
