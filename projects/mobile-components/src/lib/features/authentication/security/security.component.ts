import { Component, Injector, OnInit, ChangeDetectorRef, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, Input, Output, EventEmitter, computed, signal, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import {
  FormBuilder,
  FormGroup,
  Validators,
  AbstractControlOptions,
  ReactiveFormsModule
} from '@angular/forms';
import {
  CustomValidators,
  LPMemberEntityTools,
  MemberProfile,
  MemberService,
  ValidationService,
  KeyCloakService,
  Address,
  LssConfig,
} from 'lp-client-api';
import { Router } from '@angular/router';
import { AbstractFormComponent } from '../../../shared/abstract.form.component';

export type SecuritySize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
export type SecurityVariant = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
export type SecurityRounded = 'none' | 'sm' | 'md' | 'lg' | 'full';
export type SecurityLevel = 'basic' | 'standard' | 'enhanced' | 'maximum';

export interface SecurityConfig {
  enablePinSecurity?: boolean;
  enablePasswordSecurity?: boolean;
  enableBiometrics?: boolean;
  enableTwoFactor?: boolean;
  enableDeviceRegistration?: boolean;
  enableSessionManagement?: boolean;
  enableAuditLogging?: boolean;
  autoLockTimeout?: number;
  maxFailedAttempts?: number;
  passwordComplexity?: 'basic' | 'standard' | 'strong';
}

export interface SecuritySettings {
  pinEnabled: boolean;
  pinLength: number;
  biometricsEnabled: boolean;
  twoFactorEnabled: boolean;
  deviceRegistrationRequired: boolean;
  sessionTimeout: number;
  autoLockEnabled: boolean;
  auditLoggingEnabled: boolean;
  securityLevel: SecurityLevel;
}

export interface SecurityEvent {
  type: 'setting_change' | 'security_update' | 'pin_change' | 'password_change' | 'biometric_toggle' | 'audit_event';
  timestamp: number;
  setting?: string;
  previousValue?: any;
  newValue?: any;
  userId?: string;
  deviceId?: string;
}

@Component({
  selector: 'app-security',
  templateUrl: './security.component.html',
  styleUrls: ['./security.component.scss'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, IonicModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class SecurityComponent extends AbstractFormComponent<MemberProfile> implements OnDestroy {
  // Standard UI inputs
  @Input() className: string = '';
  @Input() size: SecuritySize = 'md';
  @Input() variant: SecurityVariant = 'default';
  @Input() rounded: SecurityRounded = 'md';
  @Input() disabled: boolean = false;

  // Security-specific inputs
  @Input() title: string = 'Security Settings';
  @Input() subtitle: string = 'Manage your account security preferences';
  @Input() config: SecurityConfig = {
    enablePinSecurity: true,
    enablePasswordSecurity: true,
    enableBiometrics: true,
    enableTwoFactor: true,
    enableDeviceRegistration: true,
    enableSessionManagement: true,
    enableAuditLogging: true,
    autoLockTimeout: 300000, // 5 minutes
    maxFailedAttempts: 3,
    passwordComplexity: 'strong'
  };

  @Input() currentSettings: SecuritySettings = {
    pinEnabled: false,
    pinLength: 5,
    biometricsEnabled: false,
    twoFactorEnabled: false,
    deviceRegistrationRequired: false,
    sessionTimeout: 900000, // 15 minutes
    autoLockEnabled: true,
    auditLoggingEnabled: true,
    securityLevel: 'standard'
  };

  @Input() userId?: string;
  @Input() deviceId?: string;

  // Event outputs
  @Output() settingsChange = new EventEmitter<SecuritySettings>();
  @Output() securityEvent = new EventEmitter<SecurityEvent>();
  @Output() pinUpdateRequest = new EventEmitter<string>();
  @Output() passwordUpdateRequest = new EventEmitter<void>();
  @Output() biometricToggle = new EventEmitter<boolean>();
  @Output() twoFactorToggle = new EventEmitter<boolean>();
  @Output() deviceRegistrationToggle = new EventEmitter<boolean>();

  // Internal state using signals
  private _settings = signal<SecuritySettings>(this.currentSettings);
  private _isUpdating = signal(false);
  private _lastUpdate = signal<number | null>(null);

  // Computed properties
  public settings = computed(() => this._settings());
  public isUpdating = computed(() => this._isUpdating());
  public lastUpdate = computed(() => this._lastUpdate());

  // Computed classes
  public computedClasses = computed(() => {
    const baseClasses = ['security-component'];
    const sizeClasses = {
      xs: 'security-xs',
      sm: 'security-sm',
      md: 'security-md',
      lg: 'security-lg',
      xl: 'security-xl'
    };
    const variantClasses = {
      default: 'security-default',
      primary: 'security-primary',
      secondary: 'security-secondary',
      success: 'security-success',
      warning: 'security-warning',
      danger: 'security-danger'
    };
    const roundedClasses = {
      none: 'security-rounded-none',
      sm: 'security-rounded-sm',
      md: 'security-rounded-md',
      lg: 'security-rounded-lg',
      full: 'security-rounded-full'
    };
    const levelClasses = {
      basic: 'security-level-basic',
      standard: 'security-level-standard',
      enhanced: 'security-level-enhanced',
      maximum: 'security-level-maximum'
    };

    return [
      ...baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      levelClasses[this._settings().securityLevel],
      this.disabled ? 'security-disabled' : '',
      this.className
    ].filter(Boolean).join(' ');
  });

  // Forms
  profile?: MemberProfile;
  profileForm!: FormGroup;
  profileFormPassword!: FormGroup;
  securitySettingsForm!: FormGroup;
  validated: boolean = false;
  validatedPassword: boolean = false;
  status_loading: boolean = true;

  constructor(
    injector: Injector,
    private memberService: MemberService,
    public override _formValidations: ValidationService,
    public override _formBuilder: FormBuilder,
    private kc: KeyCloakService,
    protected readonly router: Router,
    public lssConfig: LssConfig
  ) {
    super(injector);
    
    this.initializeForms();
    this.initializeSecuritySettings();
  }

  override ngOnDestroy(): void {
    this.clearSensitiveData();
  }

  private initializeForms(): void {
    // PIN form
    this.profileForm = this._formBuilder.group({
      pin: [
        '',
        [
          Validators.required, 
          Validators.minLength(this.currentSettings.pinLength),
          Validators.maxLength(this.currentSettings.pinLength),
          Validators.pattern(/^\d+$/) // Only digits
        ],
      ],
    });

    // Password form
    this.profileFormPassword = this._formBuilder.group(
      {
        currentPassword: [
          '',
          [Validators.required]
        ],
        password: [
          '',
          Validators.compose([
            Validators.required,
            CustomValidators.passwordValidate(),
          ]),
        ],
        passwordConfirm: ['', [Validators.required]],
      },
      {
        validators: Validators.compose([
          CustomValidators.matchValue(
            'password',
            'Password',
            'passwordConfirm',
            'Confirm Password'
          ),
        ]),
      } as AbstractControlOptions
    );

    // Security settings form
    this.securitySettingsForm = this._formBuilder.group({
      pinEnabled: [this.currentSettings.pinEnabled],
      pinLength: [this.currentSettings.pinLength, [Validators.min(4), Validators.max(8)]],
      biometricsEnabled: [this.currentSettings.biometricsEnabled],
      twoFactorEnabled: [this.currentSettings.twoFactorEnabled],
      deviceRegistrationRequired: [this.currentSettings.deviceRegistrationRequired],
      sessionTimeout: [this.currentSettings.sessionTimeout, [Validators.min(60000)]], // Min 1 minute
      autoLockEnabled: [this.currentSettings.autoLockEnabled],
      auditLoggingEnabled: [this.currentSettings.auditLoggingEnabled],
      securityLevel: [this.currentSettings.securityLevel]
    });

    // Watch for form changes
    this.securitySettingsForm.valueChanges.subscribe(value => {
      this.onSettingsChange(value);
    });
  }

  private initializeSecuritySettings(): void {
    this._settings.set(this.currentSettings);
    this.emitSecurityEvent('audit_event', { action: 'settings_viewed' });
  }

  private clearSensitiveData(): void {
    this.profileForm.reset();
    this.profileFormPassword.reset();
  }

  private emitSecurityEvent(type: SecurityEvent['type'], data?: any): void {
    const event: SecurityEvent = {
      type,
      timestamp: Date.now(),
      userId: this.userId,
      deviceId: this.deviceId,
      ...data
    };
    this.securityEvent.emit(event);
  }

  private onSettingsChange(newSettings: Partial<SecuritySettings>): void {
    const currentSettings = this._settings();
    const updatedSettings = { ...currentSettings, ...newSettings };
    
    // Validate security level requirements
    if (newSettings.securityLevel) {
      this.enforceSecurityLevelRequirements(updatedSettings);
    }

    this._settings.set(updatedSettings);
    this.settingsChange.emit(updatedSettings);
    
    // Log the change
    this.emitSecurityEvent('setting_change', {
      previousSettings: currentSettings,
      newSettings: updatedSettings,
      changedFields: Object.keys(newSettings)
    });
  }

  private enforceSecurityLevelRequirements(settings: SecuritySettings): void {
    switch (settings.securityLevel) {
      case 'maximum':
        settings.pinEnabled = true;
        settings.biometricsEnabled = true;
        settings.twoFactorEnabled = true;
        settings.deviceRegistrationRequired = true;
        settings.auditLoggingEnabled = true;
        settings.autoLockEnabled = true;
        settings.sessionTimeout = Math.min(settings.sessionTimeout, 300000); // Max 5 min
        break;
      case 'enhanced':
        settings.pinEnabled = true;
        settings.twoFactorEnabled = true;
        settings.auditLoggingEnabled = true;
        settings.autoLockEnabled = true;
        break;
      case 'standard':
        settings.pinEnabled = true;
        settings.auditLoggingEnabled = true;
        break;
      case 'basic':
        // No specific requirements
        break;
    }
  }

  // Form getters
  override get form(): any {
    return this.profileForm.controls;
  }

  get passwordForm(): any {
    return this.profileFormPassword.controls;
  }

  get settingsForm(): any {
    return this.securitySettingsForm.controls;
  }

  // Public methods for template
  public updatePin(): void {
    if (!this.profileForm.valid) {
      this.profileForm.markAllAsTouched();
      return;
    }

    this._isUpdating.set(true);
    const pinValue = this.profileForm.get('pin')?.value;

    // Emit pin update request
    this.pinUpdateRequest.emit(pinValue);
    
    this.emitSecurityEvent('pin_change', { 
      action: 'pin_updated',
      pinLength: pinValue.length 
    });

    // Simulate update completion
    setTimeout(() => {
      this._isUpdating.set(false);
      this._lastUpdate.set(Date.now());
      this.profileForm.reset();
    }, 1000);
  }

  public updatePassword(): void {
    if (!this.profileFormPassword.valid) {
      this.profileFormPassword.markAllAsTouched();
      return;
    }

    this._isUpdating.set(true);
    
    // Emit password update request
    this.passwordUpdateRequest.emit();
    
    this.emitSecurityEvent('password_change', { 
      action: 'password_updated'
    });

    // Simulate update completion
    setTimeout(() => {
      this._isUpdating.set(false);
      this._lastUpdate.set(Date.now());
      this.profileFormPassword.reset();
    }, 1000);
  }

  public toggleBiometrics(enabled: boolean): void {
    const currentSettings = this._settings();
    const updatedSettings = { ...currentSettings, biometricsEnabled: enabled };
    this._settings.set(updatedSettings);
    
    this.biometricToggle.emit(enabled);
    this.settingsChange.emit(updatedSettings);
    
    this.emitSecurityEvent('biometric_toggle', { 
      enabled,
      action: enabled ? 'biometrics_enabled' : 'biometrics_disabled'
    });
  }

  public toggleTwoFactor(enabled: boolean): void {
    const currentSettings = this._settings();
    const updatedSettings = { ...currentSettings, twoFactorEnabled: enabled };
    this._settings.set(updatedSettings);
    
    this.twoFactorToggle.emit(enabled);
    this.settingsChange.emit(updatedSettings);
    
    this.emitSecurityEvent('setting_change', { 
      setting: 'twoFactorEnabled',
      previousValue: currentSettings.twoFactorEnabled,
      newValue: enabled
    });
  }

  public toggleDeviceRegistration(enabled: boolean): void {
    const currentSettings = this._settings();
    const updatedSettings = { ...currentSettings, deviceRegistrationRequired: enabled };
    this._settings.set(updatedSettings);
    
    this.deviceRegistrationToggle.emit(enabled);
    this.settingsChange.emit(updatedSettings);
    
    this.emitSecurityEvent('setting_change', { 
      setting: 'deviceRegistrationRequired',
      previousValue: currentSettings.deviceRegistrationRequired,
      newValue: enabled
    });
  }

  public changeSecurityLevel(level: SecurityLevel): void {
    const currentSettings = this._settings();
    const updatedSettings = { ...currentSettings, securityLevel: level };
    
    this.enforceSecurityLevelRequirements(updatedSettings);
    this._settings.set(updatedSettings);
    
    // Update form to reflect enforced changes
    this.securitySettingsForm.patchValue(updatedSettings, { emitEvent: false });
    
    this.settingsChange.emit(updatedSettings);
    
    this.emitSecurityEvent('setting_change', { 
      setting: 'securityLevel',
      previousValue: currentSettings.securityLevel,
      newValue: level,
      enforcedChanges: true
    });
  }

  // Helper methods for template
  public getSecurityLevelLabel(level: SecurityLevel): string {
    const labels = {
      basic: 'Basic Security',
      standard: 'Standard Security',
      enhanced: 'Enhanced Security',
      maximum: 'Maximum Security'
    };
    return labels[level];
  }

  public getSecurityLevelDescription(level: SecurityLevel): string {
    const descriptions = {
      basic: 'Minimal security features enabled',
      standard: 'PIN protection and audit logging',
      enhanced: 'PIN, 2FA, and comprehensive logging',
      maximum: 'All security features enabled with strict timeouts'
    };
    return descriptions[level];
  }

  public getSecurityLevelColor(level: SecurityLevel): string {
    const colors = {
      basic: 'warning',
      standard: 'primary',
      enhanced: 'success',
      maximum: 'success'
    };
    return colors[level];
  }

  public getSessionTimeoutDisplay(): string {
    const timeout = this._settings().sessionTimeout;
    const minutes = Math.floor(timeout / 60000);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      const remainingMinutes = minutes % 60;
      return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
    }
    return `${minutes}m`;
  }

  public isFeatureAvailable(feature: keyof SecurityConfig): boolean {
    return this.config[feature] === true;
  }

  public getSecurityScore(): number {
    const settings = this._settings();
    let score = 0;
    
    if (settings.pinEnabled) score += 15;
    if (settings.biometricsEnabled) score += 20;
    if (settings.twoFactorEnabled) score += 25;
    if (settings.deviceRegistrationRequired) score += 15;
    if (settings.autoLockEnabled) score += 10;
    if (settings.auditLoggingEnabled) score += 10;
    if (settings.sessionTimeout <= 300000) score += 5; // Short session timeout
    
    return Math.min(score, 100);
  }

  public getSecurityScoreColor(): string {
    const score = this.getSecurityScore();
    if (score >= 80) return 'success';
    if (score >= 60) return 'warning';
    return 'danger';
  }

  // Legacy methods (kept for compatibility)
  ionViewWillEnter() {
    // Load current security settings if needed
  }

  doLoad() {
    this.updatePin();
  }
}
