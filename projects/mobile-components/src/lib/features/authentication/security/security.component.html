<ion-content class="app-background">
  <div class="absolute inset-0 z-0 h-screen bg-blue-200 animated-bg"></div>
  <div class="overflow-y-auto absolute inset-0 z-10 pb-24 space-y-2">
  <lib-head-logo
    [balance]="profile?.currentBalance"
    [src]="lssConfig.icon"
  />

  <ion-card class="">
    <!-- <form [formGroup]="profileForm" (ngSubmit)="doLoad()">
      <ion-item>
        <ion-icon slot="start" name="keypad-outline"></ion-icon>
        <ion-input
          labelPlacement="floating"
          label="* Pin"
          type="text"
          formControlName="pin"
        ></ion-input>
      </ion-item>
      <ion-item *ngIf="isFormComponentInvalid('pin')">
        <div
          *ngFor="let error of getComponentErrors('pin')"
          class="validator-error"
        >
          5 Digits needed when you spend points.
        </div>
      </ion-item>

      <div class="form-spacer"></div>
      <ion-button expand="block" class="save" type="submit"
        >Update Pin</ion-button
      >
    </form> -->
  </ion-card>

  <ion-card class="">
    <!-- <form [formGroup]="profileFormPassword" (ngSubmit)="doPassword()">
      <ion-button expand="block" class="save" type="submit"
        >Update Password</ion-button
      >
    </form> -->
  </ion-card>
  </div>
</ion-content>
