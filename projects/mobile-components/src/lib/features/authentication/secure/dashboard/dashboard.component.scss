// MCFC-005: Enhanced Secure Dashboard Component Styles
// Security-focused styling with responsive design

.secure-dashboard {
  width: 100%;
  height: 100%;
  position: relative;

  // Size variants
  &.dashboard-xs {
    font-size: 0.75rem;
    
    .dashboard-header h1 { 
      font-size: 1.125rem; 
    }
    
    .action-card { 
      padding: 0.75rem; 
    }
  }

  &.dashboard-sm {
    font-size: 0.875rem;
    
    .dashboard-header h1 { 
      font-size: 1.25rem; 
    }
    
    .action-card { 
      padding: 1rem; 
    }
  }

  &.dashboard-md {
    font-size: 1rem;
    
    .dashboard-header h1 { 
      font-size: 1.5rem; 
    }
    
    .action-card { 
      padding: 1.5rem; 
    }
  }

  &.dashboard-lg {
    font-size: 1.125rem;
    
    .dashboard-header h1 { 
      font-size: 1.875rem; 
    }
    
    .action-card { 
      padding: 2rem; 
    }
  }

  &.dashboard-xl {
    font-size: 1.25rem;
    
    .dashboard-header h1 { 
      font-size: 2.25rem; 
    }
    
    .action-card { 
      padding: 2.5rem; 
    }
  }

  // Variant colors
  &.dashboard-default {
    --dashboard-primary: #3b82f6;
    --dashboard-bg: #ffffff;
  }

  &.dashboard-primary {
    --dashboard-primary: #2563eb;
    --dashboard-bg: #eff6ff;
  }

  &.dashboard-secondary {
    --dashboard-primary: #4b5563;
    --dashboard-bg: #f9fafb;
  }

  &.dashboard-success {
    --dashboard-primary: #059669;
    --dashboard-bg: #ecfdf5;
  }

  &.dashboard-warning {
    --dashboard-primary: #d97706;
    --dashboard-bg: #fffbeb;
  }

  &.dashboard-danger {
    --dashboard-primary: #dc2626;
    --dashboard-bg: #fef2f2;
  }

  &.dashboard-info {
    --dashboard-primary: #0891b2;
    --dashboard-bg: #ecfeff;
  }

  // Rounded variants
  &.dashboard-rounded-none { border-radius: 0; }
  &.dashboard-rounded-sm { border-radius: 0.125rem; }
  &.dashboard-rounded-md { border-radius: 0.375rem; }
  &.dashboard-rounded-lg { border-radius: 0.5rem; }
  &.dashboard-rounded-full { border-radius: 9999px; }

  // Security level styling
  &.dashboard-auth-basic {
    .security-level-badge {
      border: 1px solid #fef3c7;
      background-color: #fffbeb;
    }
  }

  &.dashboard-auth-standard {
    .security-level-badge {
      border: 1px solid #dbeafe;
      background-color: #eff6ff;
    }
  }

  &.dashboard-auth-enhanced {
    .security-level-badge {
      border: 1px solid #d1fae5;
      background-color: #ecfdf5;
    }
  }

  // Disabled state
  &.dashboard-disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  // Loading state
  .dashboard-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 16rem;
    gap: 1rem;
    
    ion-spinner {
      width: 2rem;
      height: 2rem;
      color: #3b82f6;
    }

    p {
      color: #6b7280;
      font-size: 0.875rem;
    }
  }

  // Error state
  .dashboard-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 16rem;
    gap: 1rem;
    padding: 1.5rem;
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 0.5rem;

    .error-icon {
      width: 3rem;
      height: 3rem;
      color: #ef4444;
    }

    p {
      color: #b91c1c;
      text-align: center;
    }

    ion-button {
      margin-top: 1rem;
    }
  }

  // Dashboard content
  .dashboard-content {
    width: 100%;
    height: 100%;
  }

  // Animated background
  .animated-bg {
    background: linear-gradient(-45deg, #e3f2fd, #bbdefb, #90caf9, #64b5f6);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;

    &.disabled {
      animation-play-state: paused;
      opacity: 0.5;
    }
  }

  // Header section
  .dashboard-header {
    margin-bottom: 1.5rem;

    h1 {
      font-weight: 700;
      color: #111827;
    }

    p {
      color: #6b7280;
      margin-top: 0.25rem;
    }

    .security-level-badge {
      margin-top: 0.5rem;

      span {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
        
        ion-icon {
          margin-right: 0.25rem;
          width: 0.75rem;
          height: 0.75rem;
        }
      }
    }
  }

  // Action cards
  .action-card {
    transition: all 0.2s ease-in-out;

    &:hover:not(.disabled) {
      transform: scale(1.05);
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    div {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
      border: 1px solid #f3f4f6;
      transition: all 0.2s;

      &:hover:not(.disabled) {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border-color: #e5e7eb;
      }
    }

    ion-icon, span {
      transition: color 0.2s;
    }
  }

  // Account summary
  .account-summary {
    transition: all 0.2s;

    h3 {
      color: #111827;
      font-weight: 500;
    }

    .balance-grid {
      div {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #f3f4f6;

        &:last-child {
          border-bottom: none;
        }

        span:first-child {
          color: #6b7280;
          font-weight: 500;
        }

        span:last-child {
          font-weight: 600;
        }
      }
    }

    ion-button {
      transition: transform 0.2s;

      &:not([disabled]):hover {
        transform: scale(1.1);
      }

      ion-icon.spin {
        animation: spin 1s linear infinite;
      }
    }
  }

  // Session info
  .session-info {
    background-color: #f9fafb;
    border-radius: 0.5rem;
    padding: 1rem;
    border: 1px solid #e5e7eb;

    ion-icon {
      width: 1rem;
      height: 1rem;
    }

    span:last-child {
      font-weight: 500;
    }
  }

  // Profile removal
  .profile-removal {
    div {
      transition: all 0.2s;
      cursor: pointer;
      background: linear-gradient(135deg, #ef4444, #dc2626);

      &:hover:not(.disabled) {
        transform: scale(1.02);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        background: linear-gradient(135deg, #dc2626, #b91c1c);
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        
        &:hover {
          transform: none;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
      }

      ion-icon {
        transition: transform 0.2s;
      }

      &:hover:not(.disabled) ion-icon {
        transform: scale(1.1);
      }
    }
  }

  // Animations
  .animate-fade-in-down {
    animation: fadeInDown 0.6s ease-out;
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }
}

// Keyframe animations
@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .secure-dashboard {
    --dashboard-bg: #1f2937;
    
    .dashboard-header h1 {
      color: white;
    }

    .dashboard-header p {
      color: #d1d5db;
    }

    .action-card div {
      background-color: #1f2937;
      border-color: #374151;
      color: white;
      
      &:hover:not(.disabled) {
        background-color: #374151;
        border-color: #4b5563;
      }
    }

    .account-summary {
      background-color: #1f2937;
      border-color: #374151;

      h3 {
        color: white;
      }

      .balance-grid div {
        border-color: #374151;

        span:first-child {
          color: #d1d5db;
        }

        span:last-child {
          color: white;
        }
      }
    }

    .session-info {
      background-color: #374151;
      border-color: #4b5563;
      color: #e5e7eb;
    }
  }
}

// Legacy compatibility styles
.app-background {
  --background: var(--ion-color-base);
}

.center {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.account-name {
  font-size: 26px;
}

.name-text {
  margin-left: 20px;
  font-size: 26px;
}

.w-full {
  width: 100%;
}

.card-background {
  background-color: #fff;
  width: 100vw;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.transaction-summary {
  ion-row {
    h3 {
      margin-bottom: 1rem;
      margin-left: 16px;
      font-size: 1.4rem;
      font-weight: 400;
    }
  }
  
  ion-col {
    ion-row {
      h3 {
        font-size: 0.8rem;
        font-weight: 400;
        width: 100%;
        margin-right: 16px;

        span {
          font-size: 1rem;
          padding-bottom: 0;
        }
      }
    }
  }
  
  h3 {
    margin-bottom: 1rem;
    margin-left: 16px;

    span {
      font-size: 1rem;
      display: block;
      padding-bottom: 12px;
    }
  }
}

.pcu-earned {
  color: var(--ion-color-success) !important;
}

.pcu-spent {
  color: var(--ion-color-danger) !important;
}