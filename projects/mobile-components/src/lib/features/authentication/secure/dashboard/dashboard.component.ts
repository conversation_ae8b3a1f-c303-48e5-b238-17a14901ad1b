import { Component, Injector, OnInit, ChangeDetectorRef, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, Input, Output, EventEmitter, computed, signal, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import {
  MemberProfile,
  MemberService,
  KeyCloakService,
  LssConfig,
} from 'lp-client-api';
import { AbstractComponent } from '../../../../shared/abstract.component';

export type DashboardSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
export type DashboardVariant = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info';
export type DashboardRounded = 'none' | 'sm' | 'md' | 'lg' | 'full';

export interface DashboardConfig {
  enableQuickActions: boolean;
  enableAccountSummary: boolean;
  enableProfileRemoval: boolean;
  enableBalanceDisplay: boolean;
  enableSecurityAccess: boolean;
  showMembershipNumber: boolean;
  showAnimations: boolean;
  enableRefresh: boolean;
  autoRefreshInterval?: number;
  maxRetries: number;
  sessionTimeout: number;
}

export interface DashboardData {
  profile?: MemberProfile;
  balance?: number;
  availUnits?: number;
  availRands?: number;
  earnedPoints?: number;
  usedPoints?: number;
  membershipNumber?: string;
  displayName?: string;
}

export interface DashboardSecurity {
  sessionActive: boolean;
  lastActivity: number;
  authLevel: 'basic' | 'standard' | 'enhanced';
  deviceVerified: boolean;
  twoFactorEnabled: boolean;
}

export interface DashboardEvent {
  type: 'navigation' | 'balance_refresh' | 'profile_access' | 'security_access' | 'logout' | 'session_timeout';
  timestamp: number;
  userId?: string;
  deviceId?: string;
  data?: any;
}

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  standalone: true,
  imports: [CommonModule, RouterModule, IonicModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class DashboardComponent extends AbstractComponent implements OnInit, OnDestroy {
  // Standard UI inputs
  @Input() className: string = '';
  @Input() size: DashboardSize = 'md';
  @Input() variant: DashboardVariant = 'default';
  @Input() rounded: DashboardRounded = 'md';
  @Input() disabled: boolean = false;

  // Dashboard-specific inputs
  @Input() title: string = 'Dashboard';
  @Input() subtitle: string = 'Welcome to your secure account';
  @Input() config: DashboardConfig = {
    enableQuickActions: true,
    enableAccountSummary: true,
    enableProfileRemoval: true,
    enableBalanceDisplay: true,
    enableSecurityAccess: true,
    showMembershipNumber: true,
    showAnimations: true,
    enableRefresh: true,
    autoRefreshInterval: 300000, // 5 minutes
    maxRetries: 3,
    sessionTimeout: 1800000 // 30 minutes
  };

  @Input() dashboardData: DashboardData = {};
  @Input() securityState: DashboardSecurity = {
    sessionActive: true,
    lastActivity: Date.now(),
    authLevel: 'standard',
    deviceVerified: false,
    twoFactorEnabled: false
  };

  @Input() userId?: string;
  @Input() deviceId?: string;

  // Event outputs
  @Output() navigationClick = new EventEmitter<string>();
  @Output() balanceRefresh = new EventEmitter<void>();
  @Output() profileAccess = new EventEmitter<void>();
  @Output() securityAccess = new EventEmitter<void>();
  @Output() logoutRequest = new EventEmitter<void>();
  @Output() dashboardEvent = new EventEmitter<DashboardEvent>();

  // Internal state using signals
  private _data = signal<DashboardData>(this.dashboardData);
  private _security = signal<DashboardSecurity>(this.securityState);
  private _isLoading = signal(false);
  private _error = signal<string | null>(null);
  private _lastRefresh = signal<number | null>(null);

  // Computed properties
  public data = computed(() => this._data());
  public security = computed(() => this._security());
  public isLoading = computed(() => this._isLoading());
  public error = computed(() => this._error());
  public lastRefresh = computed(() => this._lastRefresh());

  // Computed classes
  public computedClasses = computed(() => {
    const baseClasses = ['dashboard-component'];
    const sizeClasses = {
      xs: 'dashboard-xs',
      sm: 'dashboard-sm',
      md: 'dashboard-md',
      lg: 'dashboard-lg',
      xl: 'dashboard-xl'
    };
    const variantClasses = {
      default: 'dashboard-default',
      primary: 'dashboard-primary',
      secondary: 'dashboard-secondary',
      success: 'dashboard-success',
      warning: 'dashboard-warning',
      danger: 'dashboard-danger',
      info: 'dashboard-info'
    };
    const roundedClasses = {
      none: 'dashboard-rounded-none',
      sm: 'dashboard-rounded-sm',
      md: 'dashboard-rounded-md',
      lg: 'dashboard-rounded-lg',
      full: 'dashboard-rounded-full'
    };
    const authLevelClasses = {
      basic: 'dashboard-auth-basic',
      standard: 'dashboard-auth-standard',
      enhanced: 'dashboard-auth-enhanced'
    };

    return [
      ...baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      authLevelClasses[this._security().authLevel],
      this.disabled ? 'dashboard-disabled' : '',
      this.className
    ].filter(Boolean).join(' ');
  });

  // Legacy properties for compatibility
  profile?: MemberProfile;
  private autoRefreshTimer?: any;
  private sessionTimer?: any;

  constructor(
    injector: Injector,
    private memberService: MemberService,
    private kc: KeyCloakService,
    private cd: ChangeDetectorRef,
    public lssConfig: LssConfig,
    private sanitizer: DomSanitizer
  ) {
    super(injector);
    
    this.initializeDashboard();
    this.startSessionMonitoring();
  }

  ngOnInit(): void {
    this.loadDashboardData();
    this.setupAutoRefresh();
    
    // Use direct subscription instead of addGlobalSubscription
    this.memberService.profileSubject.subscribe((data) => {
      if (data) {
        this.updateProfileData(data);
        this.emitDashboardEvent('profile_access', { profileLoaded: true });
      }
      this.cd.detectChanges();
    });

    this.emitDashboardEvent('navigation', { page: 'dashboard_loaded' });
  }

  override ngOnDestroy(): void {
    this.clearTimers();
    this.clearSensitiveData();
  }

  // Public methods for template
  public refreshBalance(): void {
    if (this._isLoading() || this.disabled) return;

    this._isLoading.set(true);
    this._error.set(null);

    this.getBalance()
      .then(() => {
        this._lastRefresh.set(Date.now());
        this.balanceRefresh.emit();
        this.emitDashboardEvent('balance_refresh', { 
          timestamp: Date.now(),
          success: true 
        });
      })
      .catch((error) => {
        this._error.set('Failed to refresh balance');
        this.emitDashboardEvent('balance_refresh', { 
          timestamp: Date.now(),
          success: false,
          error: error.message 
        });
      })
      .finally(() => {
        this._isLoading.set(false);
      });
  }

  public navigateToProfile(): void {
    this.emitDashboardEvent('navigation', { target: 'profile' });
    this.profileAccess.emit();
    this.navigationClick.emit('/secure/profile');
  }

  public navigateToSecurity(): void {
    this.emitDashboardEvent('navigation', { target: 'security' });
    this.securityAccess.emit();
    this.navigationClick.emit('/secure/security');
  }

  public navigateToProfileRemoval(): void {
    this.emitDashboardEvent('navigation', { target: 'profile_removal' });
    this.navigationClick.emit('/secure/profileremove');
  }

  public handleLogout(): void {
    this.emitDashboardEvent('logout', { timestamp: Date.now() });
    this.logoutRequest.emit();
    this.clearSensitiveData();
  }

  // Helper methods
  public sanitizeHtml(html: string): SafeHtml {
    return this.sanitizer.sanitize(1, html) || '';
  }

  public getDisplayName(): string {
    const data = this._data();
    return data.displayName || 
           (data.profile ? `${data.profile.givenNames} ${data.profile.surname}` : '') ||
           'User';
  }

  public getMembershipNumber(): string {
    const data = this._data();
    return data.membershipNumber || 
           data.profile?.newMembershipNumber || 
           'N/A';
  }

  public getFormattedBalance(): string {
    const data = this._data();
    return data.balance ? data.balance.toFixed(2) : '0.00';
  }

  public getFormattedRands(): string {
    const data = this._data();
    return data.availRands ? `R ${data.availRands.toFixed(2)}` : 'R 0.00';
  }

  public getAvailableUnits(): string {
    const data = this._data();
    return data.availUnits?.toString() || '0';
  }

  public getEarnedPoints(): string {
    const data = this._data();
    return data.earnedPoints?.toString() || '0';
  }

  public getUsedPoints(): string {
    const data = this._data();
    return data.usedPoints?.toString() || '0';
  }

  public isFeatureEnabled(feature: keyof DashboardConfig): boolean {
    return this.config[feature] === true;
  }

  public getLastRefreshDisplay(): string {
    const lastRefresh = this._lastRefresh();
    if (!lastRefresh) return 'Never';
    
    const now = Date.now();
    const diff = now - lastRefresh;
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes === 1) return '1 minute ago';
    return `${minutes} minutes ago`;
  }

  public getSecurityLevel(): string {
    const security = this._security();
    return security.authLevel.charAt(0).toUpperCase() + security.authLevel.slice(1);
  }

  public isSessionActive(): boolean {
    const security = this._security();
    const now = Date.now();
    return security.sessionActive && 
           (now - security.lastActivity) < this.config.sessionTimeout;
  }

  // Private methods
  private initializeDashboard(): void {
    this._data.set(this.dashboardData);
    this._security.set(this.securityState);
    this.emitDashboardEvent('navigation', { action: 'dashboard_initialized' });
  }

  private loadDashboardData(): void {
    this._isLoading.set(true);
    
    // Load profile data if available
    if (this.kc.authSuccess) {
      this.getBalance()
        .then(() => {
          this._isLoading.set(false);
        })
        .catch(() => {
          this._isLoading.set(false);
          this._error.set('Failed to load dashboard data');
        });
    } else {
      this._isLoading.set(false);
    }
  }

  private updateProfileData(profile: MemberProfile): void {
    this.profile = profile;
    
    const currentData = this._data();
    this._data.set({
      ...currentData,
      profile,
      displayName: `${profile.givenNames} ${profile.surname}`,
      membershipNumber: profile.newMembershipNumber,
      balance: profile.currentBalance,
      availUnits: profile.availUnits,
      availRands: profile.availRands
    });
  }

  private async getBalance(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.memberService
        .memberBalance()
        .subscribe({
          next: (data: any) => {
            const currentData = this._data();
            this._data.set({
              ...currentData,
              ...data
            });
            
            this.profile = { ...this.profile, ...data };
            resolve();
          },
          error: (error) => {
            reject(error);
          }
        });
    });
  }

  private setupAutoRefresh(): void {
    if (this.config.enableRefresh && this.config.autoRefreshInterval) {
      this.autoRefreshTimer = setInterval(() => {
        if (this.isSessionActive()) {
          this.refreshBalance();
        }
      }, this.config.autoRefreshInterval);
    }
  }

  private startSessionMonitoring(): void {
    this.sessionTimer = setInterval(() => {
      const security = this._security();
      const now = Date.now();
      
      if ((now - security.lastActivity) > this.config.sessionTimeout) {
        this.handleSessionTimeout();
      }
    }, 60000); // Check every minute
  }

  private handleSessionTimeout(): void {
    const updatedSecurity = { 
      ...this._security(), 
      sessionActive: false 
    };
    this._security.set(updatedSecurity);
    
    this.emitDashboardEvent('session_timeout', { 
      timestamp: Date.now(),
      lastActivity: this._security().lastActivity 
    });
    
    this.handleLogout();
  }

  private clearTimers(): void {
    if (this.autoRefreshTimer) {
      clearInterval(this.autoRefreshTimer);
      this.autoRefreshTimer = null;
    }
    if (this.sessionTimer) {
      clearInterval(this.sessionTimer);
      this.sessionTimer = null;
    }
  }

  private clearSensitiveData(): void {
    // Clear any sensitive data from memory
    this._data.set({});
    this.profile = undefined;
  }

  private emitDashboardEvent(type: DashboardEvent['type'], data?: any): void {
    const event: DashboardEvent = {
      type,
      timestamp: Date.now(),
      userId: this.userId,
      deviceId: this.deviceId,
      ...data
    };
    this.dashboardEvent.emit(event);
  }

  // Legacy methods (kept for compatibility)
  override detectChanges(): void {
    this.cd.detectChanges();
  }
}
