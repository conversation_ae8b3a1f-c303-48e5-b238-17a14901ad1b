<div class="secure-dashboard" [ngClass]="computedClasses()">
  <!-- Loading State -->
  <div *ngIf="isLoading()" class="dashboard-loading">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Loading your dashboard...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error()" class="dashboard-error">
    <ion-icon name="alert-circle-outline" class="error-icon"></ion-icon>
    <p>{{ error() }}</p>
    <ion-button (click)="refreshBalance()" fill="outline" size="small">
      <ion-icon name="refresh-outline" slot="start"></ion-icon>
      Try Again
    </ion-button>
  </div>

  <!-- Main Dashboard Content -->
  <div *ngIf="!isLoading() && !error()" class="dashboard-content">
    <!-- Animated Background -->
    <div class="absolute inset-0 z-0 h-screen bg-blue-200 animated-bg" 
         [class.disabled]="disabled"></div>
    
    <div class="overflow-y-auto absolute inset-0 z-10 pb-24 space-y-2">
      <!-- Header with Logo -->
      <div class="header-section" *ngIf="isFeatureEnabled('showMembershipNumber')">
        <lib-head-logo
          [names]="getDisplayName()"
          [membership]="getMembershipNumber()"
          type="membership"
          [balance]="getFormattedBalance()"
          [src]="lssConfig.icon"
          class="mb-8"
        />
      </div>

      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- Dashboard Title -->
        <div class="dashboard-header mb-6" *ngIf="title">
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            {{ title }}
          </h1>
          <p *ngIf="subtitle" class="text-gray-600 dark:text-gray-300 mt-1">
            {{ subtitle }}
          </p>
          
          <!-- Security Level Indicator -->
          <div class="security-level-badge mt-2">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  [ngClass]="{
                    'bg-green-100 text-green-800': security().authLevel === 'enhanced',
                    'bg-blue-100 text-blue-800': security().authLevel === 'standard',
                    'bg-yellow-100 text-yellow-800': security().authLevel === 'basic'
                  }">
              <ion-icon name="shield-checkmark-outline" class="mr-1"></ion-icon>
              {{ getSecurityLevel() }} Security
            </span>
          </div>
        </div>

        <!-- Quick Actions -->
        <div *ngIf="isFeatureEnabled('enableQuickActions')" 
             class="grid grid-cols-2 gap-2 mb-8"
             [class.animate-fade-in-down]="isFeatureEnabled('showAnimations')">
          
          <!-- Profile Action -->
          <div class="action-card" 
               [class.disabled]="disabled"
               (click)="!disabled && navigateToProfile()">
            <div class="flex items-center space-x-4 p-6 bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
              <ion-icon name="person-add-outline" class="text-3xl text-secondary-500"></ion-icon>
              <span class="text-lg font-medium text-gray-900 dark:text-white">Profile</span>
            </div>
          </div>
          
          <!-- Security Action -->
          <div *ngIf="isFeatureEnabled('enableSecurityAccess')" 
               class="action-card"
               [class.disabled]="disabled"
               (click)="!disabled && navigateToSecurity()">
            <div class="flex items-center space-x-4 p-6 bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
              <ion-icon name="shield-checkmark-outline" class="text-3xl text-secondary-500"></ion-icon>
              <span class="text-lg font-medium text-gray-900 dark:text-white">Security</span>
            </div>
          </div>
        </div>

        <!-- Account Summary -->
        <div *ngIf="isFeatureEnabled('enableAccountSummary')" 
             class="account-summary bg-white rounded-lg shadow overflow-hidden mb-8">
          <div class="px-4 py-5 sm:p-6 space-y-4">
            <!-- Header with Refresh Button -->
            <div class="flex justify-between items-center">
              <h3 class="text-lg font-medium text-gray-900">Account Summary</h3>
              <div class="flex items-center space-x-2">
                <span *ngIf="lastRefresh()" class="text-xs text-gray-500">
                  {{ getLastRefreshDisplay() }}
                </span>
                <ion-button 
                  *ngIf="isFeatureEnabled('enableRefresh')"
                  (click)="refreshBalance()" 
                  fill="clear" 
                  size="small"
                  [disabled]="isLoading() || disabled">
                  <ion-icon name="refresh-outline" 
                           [class.spin]="isLoading()"></ion-icon>
                </ion-button>
              </div>
            </div>

            <!-- Balance Information -->
            <div *ngIf="isFeatureEnabled('enableBalanceDisplay')" class="balance-grid space-y-4">
              <div class="flex justify-between items-center">
                <span class="text-sm font-medium text-gray-500">Rand Value</span>
                <span class="text-lg font-semibold text-gray-900">
                  {{ getFormattedRands() }}
                </span>
              </div>
              
              <div class="flex justify-between items-center">
                <span class="text-sm font-medium text-gray-500">Current Available Balance</span>
                <span class="text-lg font-semibold text-gray-900">{{ getAvailableUnits() }}</span>
              </div>
              
              <div class="flex justify-between items-center">
                <span class="text-sm font-medium text-gray-500">{{ lssConfig.pointsTitle }}</span>
                <span class="text-lg font-semibold text-gray-900">{{ getFormattedBalance() }}</span>
              </div>
              
              <div class="flex justify-between items-center">
                <span class="text-sm font-medium text-gray-500">Earned</span>
                <span class="text-lg font-semibold text-green-600">{{ getEarnedPoints() }}</span>
              </div>
              
              <div class="flex justify-between items-center">
                <span class="text-sm font-medium text-gray-500">Used</span>
                <span class="text-lg font-semibold text-red-600">{{ getUsedPoints() }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Session Information -->
        <div class="session-info bg-gray-50 rounded-lg p-4 mb-8">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <ion-icon name="time-outline" class="text-gray-500"></ion-icon>
              <span class="text-sm text-gray-600">Session Status</span>
            </div>
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                  [ngClass]="{
                    'bg-green-100 text-green-800': isSessionActive(),
                    'bg-red-100 text-red-800': !isSessionActive()
                  }">
              {{ isSessionActive() ? 'Active' : 'Expired' }}
            </span>
          </div>
        </div>

        <!-- Remove Profile Action -->
        <div *ngIf="isFeatureEnabled('enableProfileRemoval')" 
             class="profile-removal"
             [class.animate-fade-in-up]="isFeatureEnabled('showAnimations')">
          <div class="block bg-danger-500 rounded-lg shadow p-6 cursor-pointer hover:bg-red-600 transition-colors"
               [class.disabled]="disabled"
               (click)="!disabled && navigateToProfileRemoval()">
            <div class="flex items-center justify-center space-x-4">
              <ion-icon name="trash-outline" class="text-3xl text-white"></ion-icon>
              <span class="text-lg font-medium text-white">Remove Profile</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
