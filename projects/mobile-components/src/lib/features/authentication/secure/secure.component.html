<!-- Multi-Factor Authentication Secure Component -->
<div [class]="computedClasses()" [attr.aria-label]="title">
  <!-- Lockout Warning -->
  <div *ngIf="isLocked()" class="security-warning lockout-warning" role="alert">
    <ion-icon name="lock-closed" class="warning-icon" aria-hidden="true"></ion-icon>
    <h3>Account Temporarily Locked</h3>
    <p>Too many authentication attempts</p>
    <p class="countdown">Time remaining: {{ getRemainingLockoutTime() }}</p>
  </div>

  <!-- Session Expired Warning -->
  <div *ngIf="isSessionExpired()" class="security-warning session-expired" role="alert">
    <ion-icon name="time" class="warning-icon" aria-hidden="true"></ion-icon>
    <h3>Session Expired</h3>
    <p>Please restart authentication for security.</p>
  </div>

  <!-- Main Authentication Interface -->
  <div class="auth-container" [class.disabled]="disabled || isLocked() || isSessionExpired()">
    <!-- Header -->
    <div class="auth-header">
      <h1>{{ title }}</h1>
      <p class="subtitle">{{ subtitle }}</p>
    </div>

    <!-- Authentication Progress -->
    <div class="auth-progress" *ngIf="config.enableMFA">
      <div class="progress-header">
        <span>Authentication Progress</span>
        <span class="progress-text">{{ completedMethods().length }} / {{ (config.requiredMethods || []).length }}</span>
      </div>
      <ion-progress-bar 
        [value]="getAuthProgress() / 100"
        [color]="variant === 'default' ? 'primary' : variant"
        class="auth-progress-bar"
      ></ion-progress-bar>
    </div>

    <!-- Completed Methods Display -->
    <div class="completed-methods" *ngIf="completedMethods().length > 0">
      <h3>Verified Methods</h3>
      <div class="method-chips">
        <ion-chip 
          *ngFor="let method of completedMethods()" 
          color="success"
          class="completed-chip"
        >
          <ion-icon [name]="getMethodIcon(method)" slot="start"></ion-icon>
          <ion-label>{{ getMethodLabel(method) }}</ion-label>
          <ion-icon name="checkmark-circle" slot="end"></ion-icon>
        </ion-chip>
      </div>
    </div>

    <!-- Current Authentication Method -->
    <div class="current-auth-method" *ngIf="!isAuthenticationComplete()">
      <h3>{{ getMethodLabel(currentMethod()) }} Authentication</h3>
      
      <!-- Password Authentication -->
      <div *ngIf="currentMethod() === 'password'" class="auth-method password-auth">
        <form [formGroup]="authForm" (ngSubmit)="authenticateWithPassword(authForm.get('password')?.value)">
          <ion-item lines="none" fill="outline">
            <ion-label position="floating">Password</ion-label>
            <ion-icon slot="start" name="key"></ion-icon>
            <ion-input 
              type="password" 
              formControlName="password"
              [disabled]="disabled || authState() === 'authenticating'"
              placeholder="Enter your password"
            ></ion-input>
          </ion-item>
          
          <ion-button 
            expand="block" 
            type="submit"
            [disabled]="authForm.get('password')?.invalid || authState() === 'authenticating'"
            [color]="variant === 'default' ? 'primary' : variant"
            class="auth-button"
          >
            <ion-spinner *ngIf="authState() === 'authenticating'" slot="start"></ion-spinner>
            {{ authState() === 'authenticating' ? 'Verifying...' : 'Verify Password' }}
          </ion-button>
        </form>
      </div>

      <!-- PIN Authentication -->
      <div *ngIf="currentMethod() === 'pin'" class="auth-method pin-auth">
        <form [formGroup]="authForm" (ngSubmit)="authenticateWithPIN(authForm.get('pin')?.value)">
          <ion-item lines="none" fill="outline">
            <ion-label position="floating">PIN</ion-label>
            <ion-icon slot="start" name="keypad"></ion-icon>
            <ion-input 
              type="password" 
              formControlName="pin"
              [disabled]="disabled || authState() === 'authenticating'"
              placeholder="Enter your PIN"
              maxlength="6"
            ></ion-input>
          </ion-item>
          
          <ion-button 
            expand="block" 
            type="submit"
            [disabled]="authForm.get('pin')?.invalid || authState() === 'authenticating'"
            [color]="variant === 'default' ? 'primary' : variant"
            class="auth-button"
          >
            <ion-spinner *ngIf="authState() === 'authenticating'" slot="start"></ion-spinner>
            {{ authState() === 'authenticating' ? 'Verifying...' : 'Verify PIN' }}
          </ion-button>
        </form>
      </div>

      <!-- Biometric Authentication -->
      <div *ngIf="currentMethod() === 'biometric'" class="auth-method biometric-auth">
        <div class="biometric-prompt">
          <ion-icon name="finger-print" class="biometric-icon"></ion-icon>
          <h4>Touch the fingerprint sensor</h4>
          <p>Use your registered fingerprint to authenticate</p>
          
          <ion-button 
            expand="block"
            (click)="authenticateWithBiometric()"
            [disabled]="disabled || authState() === 'authenticating'"
            color="success"
            class="auth-button"
          >
            <ion-spinner *ngIf="authState() === 'authenticating'" slot="start"></ion-spinner>
            {{ authState() === 'authenticating' ? 'Scanning...' : 'Start Biometric Scan' }}
          </ion-button>
        </div>
      </div>

      <!-- SMS Authentication -->
      <div *ngIf="currentMethod() === 'sms'" class="auth-method sms-auth">
        <form [formGroup]="authForm">
          <ion-item lines="none" fill="outline">
            <ion-label position="floating">Phone Number</ion-label>
            <ion-icon slot="start" name="phone-portrait"></ion-icon>
            <ion-input 
              type="tel" 
              formControlName="phoneNumber"
              [disabled]="disabled || authState() === 'authenticating'"
              placeholder="+1234567890"
            ></ion-input>
          </ion-item>
          
          <ion-button 
            expand="block"
            (click)="authenticateWithSMS(authForm.get('phoneNumber')?.value)"
            [disabled]="authForm.get('phoneNumber')?.invalid || authState() === 'authenticating'"
            [color]="variant === 'default' ? 'primary' : variant"
            class="auth-button"
          >
            <ion-spinner *ngIf="authState() === 'authenticating'" slot="start"></ion-spinner>
            {{ authState() === 'authenticating' ? 'Sending...' : 'Send SMS Code' }}
          </ion-button>

          <!-- SMS Code Input (shown after SMS sent) -->
          <div *ngIf="authState() === 'authenticating'" class="verification-code">
            <ion-item lines="none" fill="outline">
              <ion-label position="floating">SMS Code</ion-label>
              <ion-icon slot="start" name="chatbox"></ion-icon>
              <ion-input 
                type="number" 
                formControlName="smsCode"
                placeholder="Enter 6-digit code"
                maxlength="6"
              ></ion-input>
            </ion-item>
            
            <ion-button 
              expand="block"
              (click)="verifyTOTP(authForm.get('smsCode')?.value)"
              [disabled]="authForm.get('smsCode')?.invalid"
              color="success"
              class="auth-button"
            >
              Verify SMS Code
            </ion-button>
          </div>
        </form>
      </div>

      <!-- Email Authentication -->
      <div *ngIf="currentMethod() === 'email'" class="auth-method email-auth">
        <form [formGroup]="authForm">
          <ion-item lines="none" fill="outline">
            <ion-label position="floating">Email Address</ion-label>
            <ion-icon slot="start" name="mail"></ion-icon>
            <ion-input 
              type="email" 
              formControlName="email"
              [disabled]="disabled || authState() === 'authenticating'"
              placeholder="<EMAIL>"
            ></ion-input>
          </ion-item>
          
          <ion-button 
            expand="block"
            (click)="authenticateWithEmail(authForm.get('email')?.value)"
            [disabled]="authForm.get('email')?.invalid || authState() === 'authenticating'"
            [color]="variant === 'default' ? 'primary' : variant"
            class="auth-button"
          >
            <ion-spinner *ngIf="authState() === 'authenticating'" slot="start"></ion-spinner>
            {{ authState() === 'authenticating' ? 'Sending...' : 'Send Email Code' }}
          </ion-button>

          <!-- Email Code Input -->
          <div *ngIf="authState() === 'authenticating'" class="verification-code">
            <ion-item lines="none" fill="outline">
              <ion-label position="floating">Email Code</ion-label>
              <ion-icon slot="start" name="chatbox"></ion-icon>
              <ion-input 
                type="number" 
                formControlName="emailCode"
                placeholder="Enter 6-digit code"
                maxlength="6"
              ></ion-input>
            </ion-item>
            
            <ion-button 
              expand="block"
              (click)="verifyTOTP(authForm.get('emailCode')?.value)"
              [disabled]="authForm.get('emailCode')?.invalid"
              color="success"
              class="auth-button"
            >
              Verify Email Code
            </ion-button>
          </div>
        </form>
      </div>

      <!-- TOTP Authentication -->
      <div *ngIf="currentMethod() === 'totp'" class="auth-method totp-auth">
        <form [formGroup]="authForm" (ngSubmit)="verifyTOTP(authForm.get('totpCode')?.value)">
          <div class="totp-info">
            <ion-icon name="time" class="totp-icon"></ion-icon>
            <h4>Authenticator App</h4>
            <p>Enter the 6-digit code from your authenticator app</p>
          </div>
          
          <ion-item lines="none" fill="outline">
            <ion-label position="floating">Authenticator Code</ion-label>
            <ion-icon slot="start" name="time"></ion-icon>
            <ion-input 
              type="number" 
              formControlName="totpCode"
              [disabled]="disabled || authState() === 'authenticating'"
              placeholder="000000"
              maxlength="6"
            ></ion-input>
          </ion-item>
          
          <ion-button 
            expand="block" 
            type="submit"
            [disabled]="authForm.get('totpCode')?.invalid || authState() === 'authenticating'"
            [color]="variant === 'default' ? 'primary' : variant"
            class="auth-button"
          >
            <ion-spinner *ngIf="authState() === 'authenticating'" slot="start"></ion-spinner>
            {{ authState() === 'authenticating' ? 'Verifying...' : 'Verify Code' }}
          </ion-button>
        </form>
      </div>
    </div>

    <!-- Alternative Methods -->
    <div class="alternative-methods" *ngIf="!isAuthenticationComplete() && !isLocked()">
      <h4>Alternative Methods</h4>
      <div class="method-buttons">
        <ion-button 
          *ngFor="let method of config.optionalMethods" 
          (click)="switchAuthMethod(method)"
          [disabled]="!isMethodAvailable(method) || authState() === 'authenticating'"
          fill="outline"
          class="method-button"
        >
          <ion-icon [name]="getMethodIcon(method)" slot="start"></ion-icon>
          {{ getMethodLabel(method) }}
        </ion-button>
      </div>
    </div>

    <!-- Authentication Complete -->
    <div class="auth-complete" *ngIf="isAuthenticationComplete()">
      <ion-icon name="shield-checkmark" class="success-icon" color="success"></ion-icon>
      <h3>Authentication Successful</h3>
      <p>All required security checks have been completed.</p>
      
      <div class="session-info">
        <small>
          <ion-icon name="time" slot="start"></ion-icon>
          Session started: {{ getSessionStartTime() }}
        </small>
      </div>
    </div>

    <!-- Error State -->
    <div class="auth-error" *ngIf="authState() === 'failed'" role="alert">
      <ion-icon name="warning" class="error-icon" color="danger"></ion-icon>
      <h4>Authentication Failed</h4>
      <p>Please try again with the correct credentials.</p>
      <small>Attempts: {{ attempts() }} / {{ config.maxAttempts }}</small>
    </div>

    <!-- Security Footer -->
    <div class="security-footer">
      <small class="security-notice">
        <ion-icon name="shield-checkmark" slot="start"></ion-icon>
        Your authentication is secured with multiple layers of protection
      </small>
    </div>
  </div>
</div>
