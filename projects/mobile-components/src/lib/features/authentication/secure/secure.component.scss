/* Multi-Factor Authentication Secure Component Styles */

.secure-component {
  width: 100%;
  min-height: 100vh;
  padding: 1rem;
  background: var(--ion-color-light);
  
  &.secure-disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}

/* Security Warning Styles */
.security-warning {
  background: var(--ion-color-danger-tint);
  border: 2px solid var(--ion-color-danger);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  text-align: center;
  animation: pulse-warning 2s infinite;
  
  .warning-icon {
    font-size: 4rem;
    color: var(--ion-color-danger);
    margin-bottom: 1rem;
    animation: shake 0.5s ease-in-out infinite alternate;
  }
  
  h3 {
    color: var(--ion-color-danger-shade);
    margin: 0.5rem 0;
    font-weight: 700;
    font-size: 1.2rem;
  }
  
  p {
    margin: 0.5rem 0;
    color: var(--ion-color-danger-shade);
    font-weight: 500;
  }
  
  .countdown {
    font-family: 'Courier New', monospace;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--ion-color-danger);
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    margin-top: 1rem;
    display: inline-block;
  }
  
  &.session-expired {
    background: var(--ion-color-warning-tint);
    border-color: var(--ion-color-warning);
    
    .warning-icon,
    h3,
    p {
      color: var(--ion-color-warning-shade);
    }
    
    .countdown {
      color: var(--ion-color-warning);
    }
  }
}

@keyframes pulse-warning {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

@keyframes shake {
  0% { transform: translateX(0); }
  100% { transform: translateX(4px); }
}

/* Main Authentication Container */
.auth-container {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  
  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}

/* Header Styles */
.auth-header {
  text-align: center;
  margin-bottom: 2rem;
  
  h1 {
    color: var(--ion-color-primary);
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
  }
  
  .subtitle {
    color: var(--ion-color-medium);
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
  }
}

/* Authentication Progress */
.auth-progress {
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--ion-color-light-tint);
  border-radius: 12px;
  border: 1px solid var(--ion-color-light-shade);
  
  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    
    span {
      font-weight: 600;
      color: var(--ion-color-dark);
    }
    
    .progress-text {
      font-family: 'Courier New', monospace;
      color: var(--ion-color-primary);
      background: var(--ion-color-primary-tint);
      padding: 0.25rem 0.5rem;
      border-radius: 6px;
      font-size: 0.85rem;
    }
  }
  
  .auth-progress-bar {
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
    background: var(--ion-color-light);
  }
}

/* Completed Methods Display */
.completed-methods {
  margin-bottom: 2rem;
  
  h3 {
    color: var(--ion-color-success);
    font-size: 1.1rem;
    margin: 0 0 1rem 0;
    display: flex;
    align-items: center;
    
    &::before {
      content: '✓';
      margin-right: 0.5rem;
      font-weight: bold;
    }
  }
  
  .method-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .completed-chip {
    animation: slideInFromLeft 0.3s ease-out;
    
    ion-icon[slot="end"] {
      color: var(--ion-color-success);
    }
  }
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Current Authentication Method */
.current-auth-method {
  margin-bottom: 2rem;
  
  h3 {
    color: var(--ion-color-primary);
    font-size: 1.3rem;
    margin: 0 0 1.5rem 0;
    text-align: center;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -0.5rem;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 3px;
      background: var(--ion-color-primary);
      border-radius: 2px;
    }
  }
}

/* Individual Authentication Methods */
.auth-method {
  padding: 1.5rem;
  background: var(--ion-color-light-tint);
  border-radius: 12px;
  border: 1px solid var(--ion-color-light-shade);
  
  ion-item {
    margin-bottom: 1rem;
    border-radius: 8px;
    
    ion-icon[slot="start"] {
      color: var(--ion-color-primary);
      margin-right: 0.5rem;
    }
  }
  
  .auth-button {
    margin-top: 1rem;
    height: 48px;
    font-weight: 600;
    border-radius: 12px;
    
    ion-spinner {
      --color: white;
    }
  }
}

/* Authentication Complete */
.auth-complete {
  text-align: center;
  padding: 2rem;
  background: var(--ion-color-success-tint);
  border-radius: 16px;
  border: 2px solid var(--ion-color-success);
  animation: slideInFromBottom 0.5s ease-out;
  
  .success-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: bounce 1s ease-in-out;
  }
  
  h3 {
    color: var(--ion-color-success-shade);
    margin: 0 0 0.5rem 0;
    font-size: 1.4rem;
  }
  
  p {
    color: var(--ion-color-success-shade);
    margin: 0 0 1.5rem 0;
    font-size: 1rem;
  }
  
  .session-info {
    background: rgba(255, 255, 255, 0.3);
    padding: 0.75rem;
    border-radius: 8px;
    
    small {
      color: var(--ion-color-success-shade);
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      
      ion-icon {
        margin-right: 0.5rem;
      }
    }
  }
}

@keyframes slideInFromBottom {
  0% {
    transform: translateY(30px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}
