import { Component, OnInit, Input, Output, EventEmitter, computed, signal, OnD<PERSON>roy, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';

export type SecureSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
export type SecureVariant = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
export type SecureRounded = 'none' | 'sm' | 'md' | 'lg' | 'full';
export type SecureAuthMethod = 'password' | 'pin' | 'biometric' | 'sms' | 'email' | 'totp';
export type SecureAuthState = 'initial' | 'authenticating' | 'authenticated' | 'failed' | 'locked' | 'expired';

export interface SecureConfig {
  enableMFA?: boolean;
  requiredMethods?: SecureAuthMethod[];
  optionalMethods?: SecureAuthMethod[];
  maxAttempts?: number;
  lockoutDuration?: number; // in milliseconds
  sessionTimeout?: number; // in milliseconds
  enableBiometrics?: boolean;
  enableTOTP?: boolean;
  enableSMS?: boolean;
  enableEmail?: boolean;
  requireDeviceRegistration?: boolean;
}

export interface AuthenticationResult {
  success: boolean;
  method: SecureAuthMethod;
  timestamp: number;
  deviceId?: string;
  sessionId?: string;
  error?: string;
}

export interface SecurityEvent {
  type: 'attempt' | 'success' | 'failure' | 'lockout' | 'timeout' | 'method_change';
  method?: SecureAuthMethod;
  timestamp: number;
  data?: any;
}

@Component({
  selector: 'app-secure',
  templateUrl: './secure.component.html',
  styleUrls: ['./secure.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule, ReactiveFormsModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class SecureComponent implements OnInit, OnDestroy {
  // Standard UI inputs
  @Input() className: string = '';
  @Input() size: SecureSize = 'md';
  @Input() variant: SecureVariant = 'default';
  @Input() rounded: SecureRounded = 'md';
  @Input() disabled: boolean = false;

  // Secure authentication inputs
  @Input() title: string = 'Secure Authentication';
  @Input() subtitle: string = 'Multiple layers of security to protect your account';
  @Input() config: SecureConfig = {
    enableMFA: true,
    requiredMethods: ['password'],
    optionalMethods: ['biometric', 'sms'],
    maxAttempts: 3,
    lockoutDuration: 300000, // 5 minutes
    sessionTimeout: 900000, // 15 minutes
    enableBiometrics: true,
    enableTOTP: false,
    enableSMS: true,
    enableEmail: true,
    requireDeviceRegistration: false
  };

  @Input() primaryMethod: SecureAuthMethod = 'password';
  @Input() fallbackMethods: SecureAuthMethod[] = ['sms', 'email'];
  @Input() deviceId?: string;
  @Input() userId?: string;

  // Event outputs
  @Output() authenticationComplete = new EventEmitter<AuthenticationResult>();
  @Output() authenticationFailed = new EventEmitter<AuthenticationResult>();
  @Output() securityEvent = new EventEmitter<SecurityEvent>();
  @Output() methodChange = new EventEmitter<SecureAuthMethod>();
  @Output() lockoutTriggered = new EventEmitter<{ duration: number, attempts: number }>();
  @Output() biometricRequest = new EventEmitter<void>();
  @Output() smsRequest = new EventEmitter<string>(); // phone number
  @Output() emailRequest = new EventEmitter<string>(); // email address

  // Internal state using signals
  private _currentMethod = signal<SecureAuthMethod>(this.primaryMethod);
  private _authState = signal<SecureAuthState>('initial');
  private _attempts = signal(0);
  private _completedMethods = signal<SecureAuthMethod[]>([]);
  private _sessionStart = signal(Date.now());
  private _lockoutEnd = signal<number | null>(null);
  private _sessionId = signal<string>('');

  // Computed properties
  public currentMethod = computed(() => this._currentMethod());
  public authState = computed(() => this._authState());
  public attempts = computed(() => this._attempts());
  public completedMethods = computed(() => this._completedMethods());
  public isLocked = computed(() => {
    const lockoutEnd = this._lockoutEnd();
    return lockoutEnd ? Date.now() < lockoutEnd : false;
  });
  public isSessionExpired = computed(() => {
    const sessionDuration = Date.now() - this._sessionStart();
    return sessionDuration > (this.config.sessionTimeout || 900000);
  });
  public remainingMethods = computed(() => {
    const completed = this._completedMethods();
    const required = this.config.requiredMethods || [];
    return required.filter(method => !completed.includes(method));
  });
  public isAuthenticationComplete = computed(() => {
    if (!this.config.enableMFA) {
      return this._completedMethods().length > 0;
    }
    const required = this.config.requiredMethods || [];
    const completed = this._completedMethods();
    return required.every(method => completed.includes(method));
  });

  // Computed classes
  public computedClasses = computed(() => {
    const baseClasses = ['secure-component'];
    const sizeClasses = {
      xs: 'secure-xs',
      sm: 'secure-sm',
      md: 'secure-md',
      lg: 'secure-lg',
      xl: 'secure-xl'
    };
    const variantClasses = {
      default: 'secure-default',
      primary: 'secure-primary',
      secondary: 'secure-secondary',
      success: 'secure-success',
      warning: 'secure-warning',
      danger: 'secure-danger'
    };
    const roundedClasses = {
      none: 'secure-rounded-none',
      sm: 'secure-rounded-sm',
      md: 'secure-rounded-md',
      lg: 'secure-rounded-lg',
      full: 'secure-rounded-full'
    };
    const stateClasses = {
      initial: 'secure-initial',
      authenticating: 'secure-authenticating',
      authenticated: 'secure-authenticated',
      failed: 'secure-failed',
      locked: 'secure-locked',
      expired: 'secure-expired'
    };

    return [
      ...baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      stateClasses[this.authState()],
      this.disabled || this.isLocked() ? 'secure-disabled' : '',
      this.className
    ].filter(Boolean).join(' ');
  });

  // Form for additional data
  public authForm: FormGroup;

  constructor(private formBuilder: FormBuilder) {
    this.authForm = this.createAuthForm();
    this.initializeSession();
  }

  ngOnInit(): void {
    this.startSessionMonitoring();
    this.emitSecurityEvent('attempt', { method: this._currentMethod() });
  }

  ngOnDestroy(): void {
    this.clearSensitiveData();
  }

  private initializeSession(): void {
    const sessionId = this.generateSessionId();
    this._sessionId.set(sessionId);
    this._sessionStart.set(Date.now());
    this._currentMethod.set(this.primaryMethod);
  }

  private generateSessionId(): string {
    return `secure_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private createAuthForm(): FormGroup {
    return this.formBuilder.group({
      username: ['', [Validators.required]],
      password: ['', [Validators.required, Validators.minLength(8)]],
      pin: ['', [Validators.required, Validators.pattern(/^\d{4,6}$/)]],
      phoneNumber: ['', [Validators.pattern(/^\+?[1-9]\d{1,14}$/)]],
      email: ['', [Validators.email]],
      totpCode: ['', [Validators.pattern(/^\d{6}$/)]],
      smsCode: ['', [Validators.pattern(/^\d{6}$/)]],
      emailCode: ['', [Validators.pattern(/^\d{6}$/)]],
      backupCode: ['', [Validators.minLength(8)]]
    });
  }

  private startSessionMonitoring(): void {
    // Monitor session timeout
    const timeout = this.config.sessionTimeout || 900000;
    setTimeout(() => {
      if (!this.isAuthenticationComplete()) {
        this._authState.set('expired');
        this.emitSecurityEvent('timeout');
      }
    }, timeout);
  }

  private clearSensitiveData(): void {
    this.authForm.reset();
    this._completedMethods.set([]);
    // Clear any cached authentication data
  }

  private emitSecurityEvent(type: SecurityEvent['type'], data?: any): void {
    const event: SecurityEvent = {
      type,
      method: this._currentMethod(),
      timestamp: Date.now(),
      data: {
        sessionId: this._sessionId(),
        attempts: this._attempts(),
        completedMethods: this._completedMethods(),
        ...data
      }
    };
    this.securityEvent.emit(event);
  }

  private checkSecurityConstraints(): boolean {
    if (this.isLocked()) {
      this.emitSecurityEvent('attempt', { blocked: true, reason: 'locked' });
      return false;
    }

    if (this.isSessionExpired()) {
      this._authState.set('expired');
      this.emitSecurityEvent('timeout');
      return false;
    }

    if (this._attempts() >= (this.config.maxAttempts || 3)) {
      this.triggerLockout();
      return false;
    }

    return true;
  }

  private triggerLockout(): void {
    const duration = this.config.lockoutDuration || 300000;
    this._lockoutEnd.set(Date.now() + duration);
    this._authState.set('locked');
    
    this.lockoutTriggered.emit({
      duration,
      attempts: this._attempts()
    });
    
    this.emitSecurityEvent('lockout', { duration, attempts: this._attempts() });
  }

  public switchAuthMethod(method: SecureAuthMethod): void {
    if (!this.checkSecurityConstraints()) return;

    if (this.isMethodAvailable(method)) {
      this._currentMethod.set(method);
      this.methodChange.emit(method);
      this.emitSecurityEvent('method_change', { newMethod: method, previousMethod: this._currentMethod() });
    }
  }

  public isMethodAvailable(method: SecureAuthMethod): boolean {
    const required = this.config.requiredMethods || [];
    const optional = this.config.optionalMethods || [];
    const completed = this._completedMethods();
    
    return (required.includes(method) || optional.includes(method)) && !completed.includes(method);
  }

  public isMethodCompleted(method: SecureAuthMethod): boolean {
    return this._completedMethods().includes(method);
  }

  public isMethodRequired(method: SecureAuthMethod): boolean {
    return (this.config.requiredMethods || []).includes(method);
  }

  public authenticateWithPassword(password: string): void {
    if (!this.checkSecurityConstraints()) return;

    this._authState.set('authenticating');
    this._attempts.set(this._attempts() + 1);

    // Simulate authentication (replace with actual authentication logic)
    setTimeout(() => {
      const success = this.validatePassword(password);
      this.handleAuthenticationResult('password', success, { passwordLength: password.length });
    }, 1000);
  }

  public authenticateWithPIN(pin: string): void {
    if (!this.checkSecurityConstraints()) return;

    this._authState.set('authenticating');
    this._attempts.set(this._attempts() + 1);

    // Simulate PIN validation
    setTimeout(() => {
      const success = this.validatePIN(pin);
      this.handleAuthenticationResult('pin', success, { pinLength: pin.length });
    }, 500);
  }

  public authenticateWithBiometric(): void {
    if (!this.checkSecurityConstraints()) return;
    if (!this.config.enableBiometrics) return;

    this._authState.set('authenticating');
    this.biometricRequest.emit();
  }

  public authenticateWithSMS(phoneNumber: string): void {
    if (!this.checkSecurityConstraints()) return;
    if (!this.config.enableSMS) return;

    this._authState.set('authenticating');
    this.smsRequest.emit(phoneNumber);
  }

  public authenticateWithEmail(email: string): void {
    if (!this.checkSecurityConstraints()) return;
    if (!this.config.enableEmail) return;

    this._authState.set('authenticating');
    this.emailRequest.emit(email);
  }

  public verifyTOTP(code: string): void {
    if (!this.checkSecurityConstraints()) return;
    if (!this.config.enableTOTP) return;

    this._authState.set('authenticating');
    this._attempts.set(this._attempts() + 1);

    // Simulate TOTP validation
    setTimeout(() => {
      const success = this.validateTOTP(code);
      this.handleAuthenticationResult('totp', success, { code: code.replace(/./g, '*') });
    }, 500);
  }

  public handleExternalAuthResult(method: SecureAuthMethod, success: boolean, data?: any): void {
    this.handleAuthenticationResult(method, success, data);
  }

  private handleAuthenticationResult(method: SecureAuthMethod, success: boolean, additionalData?: any): void {
    const result: AuthenticationResult = {
      success,
      method,
      timestamp: Date.now(),
      deviceId: this.deviceId,
      sessionId: this._sessionId(),
      error: success ? undefined : 'Authentication failed'
    };

    if (success) {
      // Add method to completed list
      const completed = [...this._completedMethods(), method];
      this._completedMethods.set(completed);
      
      // Reset attempts for this method
      this._attempts.set(0);
      
      // Check if all required methods are completed
      if (this.isAuthenticationComplete()) {
        this._authState.set('authenticated');
        this.authenticationComplete.emit(result);
        this.emitSecurityEvent('success', { completedMethods: completed, ...additionalData });
      } else {
        // Move to next required method
        const remaining = this.remainingMethods();
        if (remaining.length > 0) {
          this._currentMethod.set(remaining[0]);
        }
        this.emitSecurityEvent('success', { method, nextMethod: this._currentMethod(), ...additionalData });
      }
    } else {
      this._authState.set('failed');
      this.authenticationFailed.emit(result);
      this.emitSecurityEvent('failure', { method, attempts: this._attempts(), ...additionalData });
      
      // Check if max attempts reached
      if (this._attempts() >= (this.config.maxAttempts || 3)) {
        this.triggerLockout();
      }
    }
  }

  // Validation methods (implement actual validation logic)
  private validatePassword(password: string): boolean {
    // Implement actual password validation
    return password.length >= 8;
  }

  private validatePIN(pin: string): boolean {
    // Implement actual PIN validation
    return pin.length >= 4 && /^\d+$/.test(pin);
  }

  private validateTOTP(code: string): boolean {
    // Implement actual TOTP validation
    return code.length === 6 && /^\d+$/.test(code);
  }

  // Helper methods for template
  public getMethodLabel(method: SecureAuthMethod): string {
    const labels = {
      password: 'Password',
      pin: 'PIN',
      biometric: 'Biometric',
      sms: 'SMS Code',
      email: 'Email Code',
      totp: 'Authenticator App'
    };
    return labels[method] || method;
  }

  public getMethodIcon(method: SecureAuthMethod): string {
    const icons = {
      password: 'key',
      pin: 'keypad',
      biometric: 'finger-print',
      sms: 'phone-portrait',
      email: 'mail',
      totp: 'time'
    };
    return icons[method] || 'shield-checkmark';
  }

  public getAuthProgress(): number {
    if (!this.config.enableMFA) {
      return this._completedMethods().length > 0 ? 100 : 0;
    }
    const required = this.config.requiredMethods || [];
    const completed = this._completedMethods();
    return (completed.filter(m => required.includes(m)).length / required.length) * 100;
  }

  public getRemainingLockoutTime(): string {
    const lockoutEnd = this._lockoutEnd();
    if (!lockoutEnd) return '';

    const remaining = Math.max(0, lockoutEnd - Date.now());
    const minutes = Math.floor(remaining / 60000);
    const seconds = Math.floor((remaining % 60000) / 1000);

    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  public getSessionStartTime(): string {
    return new Date(this._sessionStart()).toLocaleTimeString();
  }
}
