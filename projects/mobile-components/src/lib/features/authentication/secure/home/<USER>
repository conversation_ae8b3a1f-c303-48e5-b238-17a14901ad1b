// Secure Home Component Styles
// Mobile-first responsive design with security-focused UI patterns
// Supports dark mode and accessibility features

.secure-home {
  min-height: 100vh;
  background-color: #f9fafb;
  
  &__container {
    max-width: 28rem;
    margin: 0 auto;
    padding: 1.5rem 1rem;
  }

  // Loading state overlay
  &__loading {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.75);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
    
    .spinner {
      animation: spin 1s linear infinite;
      border-radius: 50%;
      height: 2rem;
      width: 2rem;
      border-bottom: 2px solid #2563eb;
    }
  }

  // Error state banner
  &__error {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
    
    &-icon {
      width: 1.25rem;
      height: 1.25rem;
      color: #f87171;
    }
    
    &-content {
      margin-left: 0.75rem;
      
      h3 {
        font-size: 0.875rem;
        font-weight: 500;
        color: #991b1b;
      }
      
      p {
        margin-top: 0.25rem;
        font-size: 0.875rem;
        color: #b91c1c;
      }
    }
    
    &-actions {
      margin-top: 1rem;
      display: flex;
      
      button {
        background-color: #fee2e2;
        color: #991b1b;
        padding: 0.5rem 0.75rem;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        font-weight: 500;
        border: none;
        cursor: pointer;
        
        &:hover {
          background-color: #fecaca;
        }
        
        &:focus {
          outline: none;
          box-shadow: 0 0 0 2px #ef4444, 0 0 0 4px rgba(239, 68, 68, 0.2);
        }
      }
    }
  }

  // Header section
  &__header {
    margin-bottom: 2rem;
    
    &-greeting {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1rem;
      
      h1 {
        font-size: 1.5rem;
        font-weight: 700;
        color: #111827;
      }
      
      .time {
        font-size: 0.875rem;
        color: #6b7280;
      }
    }
    
    &-user {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 1rem;
      background-color: #ffffff;
      border-radius: 0.5rem;
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      border: 1px solid #e5e7eb;
      
      .avatar {
        width: 3rem;
        height: 3rem;
        background-color: #3b82f6;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        
        span {
          color: #ffffff;
          font-weight: 600;
          font-size: 1.125rem;
        }
      }
      
      .info {
        flex: 1;
        
        .name {
          font-weight: 500;
          color: #111827;
        }
        
        .email {
          font-size: 0.875rem;
          color: #6b7280;
        }
      }
      
      .status {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 9999px;
        font-weight: 500;
        
        &--verified {
          background-color: #dcfce7;
          color: #166534;
        }
        
        &--pending {
          background-color: #fef3c7;
          color: #92400e;
        }
        
        &--warning {
          background-color: #fee2e2;
          color: #991b1b;
        }
      }
    }
  }

  // Security status section
  &__security {
    margin-bottom: 2rem;
    
    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1rem;
      
      h2 {
        font-size: 1.125rem;
        font-weight: 600;
        color: #111827;
      }
      
      .score {
        font-size: 0.875rem;
        font-weight: 500;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        
        &--high {
          background-color: #dcfce7;
          color: #166534;
        }
        
        &--medium {
          background-color: #fef3c7;
          color: #92400e;
        }
        
        &--low {
          background-color: #fee2e2;
          color: #991b1b;
        }
      }
    }
    
    &-badges {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 0.75rem;
      
      .badge {
        padding: 0.75rem;
        background-color: #ffffff;
        border-radius: 0.5rem;
        border: 1px solid #e5e7eb;
        text-align: center;
        
        .icon {
          width: 1.5rem;
          height: 1.5rem;
          margin: 0 auto 0.5rem auto;
          
          &--active {
            color: #10b981;
          }
          
          &--inactive {
            color: #9ca3af;
          }
        }
        
        .label {
          font-size: 0.75rem;
          font-weight: 500;
          color: #111827;
        }
        
        .status {
          font-size: 0.75rem;
          color: #6b7280;
          margin-top: 0.25rem;
        }
      }
    }
  }

  // Quick actions section
  &__actions {
    margin-bottom: 2rem;
    
    h2 {
      font-size: 1.125rem;
      font-weight: 600;
      color: #111827;
      margin-bottom: 1rem;
    }
    
    &-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 1rem;
      
      .action {
        padding: 1rem;
        background-color: #ffffff;
        border-radius: 0.5rem;
        border: 1px solid #e5e7eb;
        text-align: center;
        cursor: pointer;
        transition: box-shadow 0.15s ease-in-out;
        
        &:hover {
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
          border-color: #93c5fd;
        }
        
        &:focus {
          outline: none;
          box-shadow: 0 0 0 2px #3b82f6, 0 0 0 4px rgba(59, 130, 246, 0.2);
        }
        
        &--disabled {
          opacity: 0.5;
          cursor: not-allowed;
          
          &:hover {
            border-color: #e5e7eb;
            box-shadow: none;
          }
        }
        
        .icon {
          width: 2rem;
          height: 2rem;
          margin: 0 auto 0.75rem auto;
          color: #3b82f6;
        }
        
        .label {
          font-size: 0.875rem;
          font-weight: 500;
          color: #111827;
        }
        
        .description {
          font-size: 0.75rem;
          color: #6b7280;
          margin-top: 0.25rem;
        }
      }
    }
  }

  // Notifications section
  &__notifications {
    margin-bottom: 2rem;
    
    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1rem;
      
      h2 {
        font-size: 1.125rem;
        font-weight: 600;
        color: #111827;
      }
      
      .badge {
        background-color: #fee2e2;
        color: #991b1b;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 9999px;
        font-weight: 500;
      }
    }
    
    &-list {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      
      .notification {
        padding: 1rem;
        background-color: #ffffff;
        border-radius: 0.5rem;
        border: 1px solid #e5e7eb;
        
        &--unread {
          border-left: 4px solid #3b82f6;
        }
        
        &--urgent {
          border-left: 4px solid #ef4444;
          background-color: #fef2f2;
        }
        
        .header {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          margin-bottom: 0.5rem;
          
          .title {
            font-weight: 500;
            color: #111827;
            font-size: 0.875rem;
          }
          
          .time {
            font-size: 0.75rem;
            color: #6b7280;
          }
        }
        
        .content {
          font-size: 0.875rem;
          color: #374151;
        }
        
        .actions {
          margin-top: 0.75rem;
          display: flex;
          gap: 0.5rem;
          
          button {
            font-size: 0.75rem;
            padding: 0.25rem 0.75rem;
            border-radius: 0.375rem;
            font-weight: 500;
            border: 1px solid #d1d5db;
            background-color: #ffffff;
            color: #374151;
            cursor: pointer;
            
            &:hover {
              background-color: #f9fafb;
            }
            
            &:focus {
              outline: none;
              box-shadow: 0 0 0 2px #3b82f6;
            }
            
            &.primary {
              background-color: #3b82f6;
              color: #ffffff;
              border-color: #3b82f6;
              
              &:hover {
                background-color: #2563eb;
              }
            }
          }
        }
      }
    }
    
    &-empty {
      text-align: center;
      padding: 2rem 0;
      
      .icon {
        width: 3rem;
        height: 3rem;
        margin: 0 auto 1rem auto;
        color: #9ca3af;
      }
      
      .message {
        color: #6b7280;
      }
    }
  }

  // Recent activity section
  &__activity {
    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1rem;
      
      h2 {
        font-size: 1.125rem;
        font-weight: 600;
        color: #111827;
      }
      
      .view-all {
        font-size: 0.875rem;
        color: #3b82f6;
        text-decoration: none;
        
        &:hover {
          color: #2563eb;
        }
      }
    }
    
    &-list {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
      
      .activity-item {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        padding: 0.75rem;
        background-color: #ffffff;
        border-radius: 0.5rem;
        border: 1px solid #e5e7eb;
        
        .icon {
          width: 2rem;
          height: 2rem;
          padding: 0.375rem;
          border-radius: 50%;
          flex-shrink: 0;
          
          &--success {
            background-color: #dcfce7;
            color: #059669;
          }
          
          &--warning {
            background-color: #fef3c7;
            color: #d97706;
          }
          
          &--error {
            background-color: #fee2e2;
            color: #dc2626;
          }
          
          &--info {
            background-color: #dbeafe;
            color: #2563eb;
          }
        }
        
        .content {
          flex: 1;
          min-width: 0;
          
          .title {
            font-size: 0.875rem;
            font-weight: 500;
            color: #111827;
          }
          
          .description {
            font-size: 0.875rem;
            color: #374151;
            margin-top: 0.25rem;
          }
          
          .metadata {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-top: 0.5rem;
            font-size: 0.75rem;
            color: #6b7280;
            
            .time {
              display: flex;
              align-items: center;
              gap: 0.25rem;
            }
            
            .location {
              display: flex;
              align-items: center;
              gap: 0.25rem;
            }
          }
        }
      }
    }
    
    &-empty {
      text-align: center;
      padding: 2rem 0;
      
      .icon {
        width: 3rem;
        height: 3rem;
        margin: 0 auto 1rem auto;
        color: #9ca3af;
      }
      
      .message {
        color: #6b7280;
      }
    }
  }

  // Session info section
  &__session {
    margin-top: 2rem;
    padding: 1rem;
    background-color: #f3f4f6;
    border-radius: 0.5rem;
    
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 0.75rem;
      
      h3 {
        font-size: 0.875rem;
        font-weight: 500;
        color: #111827;
      }
      
      .refresh {
        font-size: 0.75rem;
        color: #3b82f6;
        cursor: pointer;
        
        &:hover {
          color: #2563eb;
        }
      }
    }
    
    .info {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 1rem;
      font-size: 0.75rem;
      
      .item {
        .label {
          color: #6b7280;
        }
        
        .value {
          color: #111827;
          font-weight: 500;
          margin-top: 0.25rem;
        }
      }
    }
    
    .actions {
      margin-top: 1rem;
      display: flex;
      gap: 0.5rem;
      
      button {
        font-size: 0.75rem;
        padding: 0.5rem 0.75rem;
        border-radius: 0.375rem;
        font-weight: 500;
        border: 1px solid #d1d5db;
        color: #374151;
        background-color: #ffffff;
        cursor: pointer;
        
        &:hover {
          background-color: #f9fafb;
        }
        
        &:focus {
          outline: none;
          box-shadow: 0 0 0 2px #3b82f6;
        }
        
        &.danger {
          border-color: #fca5a5;
          color: #b91c1c;
          
          &:hover {
            background-color: #fef2f2;
          }
        }
      }
    }
  }
}

// Dark mode styles
@media (prefers-color-scheme: dark) {
  .secure-home {
    background-color: #111827;
    
    &__container {
      color: #f9fafb;
    }
    
    &__loading {
      background-color: rgba(17, 24, 39, 0.75);
    }
    
    &__error {
      background-color: #7f1d1d;
      border-color: #b91c1c;
      
      &-content {
        h3 {
          color: #fca5a5;
        }
        
        p {
          color: #f87171;
        }
      }
      
      &-actions button {
        background-color: #7f1d1d;
        color: #fca5a5;
        
        &:hover {
          background-color: #991b1b;
        }
      }
    }
    
    &__header {
      &-greeting h1 {
        color: #ffffff;
      }
      
      &-user {
        background-color: #1f2937;
        border-color: #374151;
        
        .info .name {
          color: #ffffff;
        }
        
        .status {
          &--verified {
            background-color: #064e3b;
            color: #6ee7b7;
          }
          
          &--pending {
            background-color: #78350f;
            color: #fcd34d;
          }
          
          &--warning {
            background-color: #7f1d1d;
            color: #fca5a5;
          }
        }
      }
    }
    
    &__security {
      &-header h2 {
        color: #ffffff;
      }
      
      &-badges .badge {
        background-color: #1f2937;
        border-color: #374151;
        
        .label {
          color: #ffffff;
        }
      }
    }
    
    &__actions {
      h2 {
        color: #ffffff;
      }
      
      &-grid .action {
        background-color: #1f2937;
        border-color: #374151;
        
        .label {
          color: #ffffff;
        }
        
        &:hover {
          border-color: #3b82f6;
        }
      }
    }
    
    &__notifications {
      &-header h2 {
        color: #ffffff;
      }
      
      &-list .notification {
        background-color: #1f2937;
        border-color: #374151;
        
        &--urgent {
          background-color: rgba(127, 29, 29, 0.2);
        }
        
        .header .title {
          color: #ffffff;
        }
        
        .content {
          color: #d1d5db;
        }
        
        .actions button {
          background-color: #374151;
          border-color: #4b5563;
          color: #d1d5db;
          
          &:hover {
            background-color: #4b5563;
          }
          
          &.primary {
            background-color: #1d4ed8;
            
            &:hover {
              background-color: #1e40af;
            }
          }
        }
      }
    }
    
    &__activity {
      &-header h2 {
        color: #ffffff;
      }
      
      &-list .activity-item {
        background-color: #1f2937;
        border-color: #374151;
        
        .content {
          .title {
            color: #ffffff;
          }
          
          .description {
            color: #d1d5db;
          }
        }
      }
    }
    
    &__session {
      background-color: #1f2937;
      
      .header h3 {
        color: #ffffff;
      }
      
      .info .item .value {
        color: #ffffff;
      }
      
      .actions button {
        background-color: #374151;
        border-color: #4b5563;
        color: #d1d5db;
        
        &:hover {
          background-color: #4b5563;
        }
        
        &.danger {
          border-color: #b91c1c;
          color: #fca5a5;
          
          &:hover {
            background-color: rgba(127, 29, 29, 0.2);
          }
        }
      }
    }
  }
}

// Animations
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-10px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

// Animation classes
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

// Responsive design
@media (min-width: 640px) {
  .secure-home {
    &__container {
      max-width: 42rem;
      padding: 1.5rem;
    }
    
    &__actions {
      &-grid {
        grid-template-columns: repeat(3, 1fr);
      }
    }
  }
}

@media (min-width: 768px) {
  .secure-home {
    &__container {
      max-width: 56rem;
      padding: 2rem;
    }
    
    &__content {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 2rem;
    }
    
    &__actions {
      &-grid {
        grid-template-columns: repeat(4, 1fr);
      }
    }
  }
}

// Print styles
@media print {
  .secure-home {
    &__loading,
    &__actions,
    &__session {
      display: none;
    }
    
    * {
      color: #000000 !important;
      background: #ffffff !important;
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .secure-home {
    &__header,
    &__security,
    &__actions,
    &__notifications,
    &__activity {
      .badge,
      .action,
      .notification,
      .activity-item {
        border-width: 2px;
      }
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .secure-home {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

// Focus visible enhancements
.secure-home {
  button:focus-visible,
  [tabindex]:focus-visible {
    outline: none;
    box-shadow: 0 0 0 2px #3b82f6, 0 0 0 4px rgba(59, 130, 246, 0.2);
  }
}