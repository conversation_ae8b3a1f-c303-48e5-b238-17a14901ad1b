import { Component, Injector, OnInit, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, Input, Output, EventEmitter, computed, signal, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { Dom<PERSON>anitizer, SafeHtml } from '@angular/platform-browser';
import {
  KeyCloakService,
  LogService,
  MemberProfile,
  MemberService,
  LssConfig,
} from 'lp-client-api';
import { AbstractComponent } from '../../../../shared/abstract.component';

export type HomeSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
export type HomeVariant = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info';
export type HomeRounded = 'none' | 'sm' | 'md' | 'lg' | 'full';

export interface HomeConfig {
  enableWelcomeMessage: boolean;
  enableQuickActions: boolean;
  enableNotifications: boolean;
  enableSecurityStatus: boolean;
  enableRecentActivity: boolean;
  enableNavigation: boolean;
  showUserInfo: boolean;
  enableRefresh: boolean;
  autoRefreshInterval?: number;
  maxRetries: number;
  sessionTimeout: number;
  showAnimations: boolean;
}

export interface HomeData {
  profile?: MemberProfile;
  welcomeMessage?: string;
  notificationCount?: number;
  lastLoginTime?: string;
  securityLevel?: string;
  recentActivities?: HomeActivity[];
  quickActions?: HomeAction[];
}

export interface HomeActivity {
  id: string;
  type: 'login' | 'transaction' | 'profile_update' | 'security_change';
  description: string;
  timestamp: number;
  status: 'completed' | 'pending' | 'failed';
}

export interface HomeAction {
  id: string;
  title: string;
  description: string;
  icon: string;
  route?: string;
  action?: string;
  enabled: boolean;
  requiresAuth: boolean;
}

export interface HomeSecurity {
  sessionActive: boolean;
  lastActivity: number;
  authLevel: 'basic' | 'standard' | 'enhanced';
  deviceVerified: boolean;
  twoFactorEnabled: boolean;
  riskScore: number;
}

export interface HomeEvent {
  type: 'navigation' | 'action_click' | 'refresh' | 'notification_view' | 'security_alert' | 'logout';
  timestamp: number;
  userId?: string;
  deviceId?: string;
  data?: any;
}

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  standalone: true,
  imports: [CommonModule, RouterModule, IonicModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class HomeComponent extends AbstractComponent implements OnInit, OnDestroy {
  // Standard UI inputs
  @Input() className: string = '';
  @Input() size: HomeSize = 'md';
  @Input() variant: HomeVariant = 'default';
  @Input() rounded: HomeRounded = 'md';
  @Input() disabled: boolean = false;

  // Home-specific inputs
  @Input() title: string = 'Welcome Home';
  @Input() subtitle: string = 'Your secure digital space';
  @Input() config: HomeConfig = {
    enableWelcomeMessage: true,
    enableQuickActions: true,
    enableNotifications: true,
    enableSecurityStatus: true,
    enableRecentActivity: true,
    enableNavigation: true,
    showUserInfo: true,
    enableRefresh: true,
    autoRefreshInterval: 300000, // 5 minutes
    maxRetries: 3,
    sessionTimeout: 1800000, // 30 minutes
    showAnimations: true
  };

  @Input() homeData: HomeData = {
    welcomeMessage: 'Welcome back! We hope you have a great day.',
    notificationCount: 0,
    quickActions: [
      {
        id: 'profile',
        title: 'My Profile',
        description: 'Manage your personal information',
        icon: 'person-outline',
        route: '/secure/profile',
        enabled: true,
        requiresAuth: true
      },
      {
        id: 'security',
        title: 'Security Settings',
        description: 'Manage your security preferences',
        icon: 'shield-checkmark-outline',
        route: '/secure/security',
        enabled: true,
        requiresAuth: true
      },
      {
        id: 'dashboard',
        title: 'Dashboard',
        description: 'View your account overview',
        icon: 'grid-outline',
        route: '/secure/dashboard',
        enabled: true,
        requiresAuth: true
      },
      {
        id: 'notifications',
        title: 'Notifications',
        description: 'View recent notifications',
        icon: 'notifications-outline',
        route: '/secure/notifications',
        enabled: true,
        requiresAuth: true
      }
    ],
    recentActivities: []
  };

  @Input() securityState: HomeSecurity = {
    sessionActive: true,
    lastActivity: Date.now(),
    authLevel: 'standard',
    deviceVerified: false,
    twoFactorEnabled: false,
    riskScore: 0
  };

  @Input() userId?: string;
  @Input() deviceId?: string;

  // Event outputs
  @Output() actionClick = new EventEmitter<HomeAction>();
  @Output() navigationClick = new EventEmitter<string>();
  @Output() notificationClick = new EventEmitter<void>();
  @Output() refreshRequest = new EventEmitter<void>();
  @Output() securityAlert = new EventEmitter<any>();
  @Output() homeEvent = new EventEmitter<HomeEvent>();

  // Internal state using signals
  private _data = signal<HomeData>(this.homeData);
  private _security = signal<HomeSecurity>(this.securityState);
  private _isLoading = signal(false);
  private _error = signal<string | null>(null);
  private _lastRefresh = signal<number | null>(null);

  // Computed properties
  public data = computed(() => this._data());
  public security = computed(() => this._security());
  public isLoading = computed(() => this._isLoading());
  public error = computed(() => this._error());
  public lastRefresh = computed(() => this._lastRefresh());

  // Computed classes
  public computedClasses = computed(() => {
    const baseClasses = ['home-component'];
    const sizeClasses = {
      xs: 'home-xs',
      sm: 'home-sm',
      md: 'home-md',
      lg: 'home-lg',
      xl: 'home-xl'
    };
    const variantClasses = {
      default: 'home-default',
      primary: 'home-primary',
      secondary: 'home-secondary',
      success: 'home-success',
      warning: 'home-warning',
      danger: 'home-danger',
      info: 'home-info'
    };
    const roundedClasses = {
      none: 'home-rounded-none',
      sm: 'home-rounded-sm',
      md: 'home-rounded-md',
      lg: 'home-rounded-lg',
      full: 'home-rounded-full'
    };
    const authLevelClasses = {
      basic: 'home-auth-basic',
      standard: 'home-auth-standard',
      enhanced: 'home-auth-enhanced'
    };

    return [
      ...baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      authLevelClasses[this._security().authLevel],
      this.disabled ? 'home-disabled' : '',
      this.className
    ].filter(Boolean).join(' ');
  });

  // Legacy properties for compatibility
  private autoRefreshTimer?: any;
  private sessionTimer?: any;

  constructor(
    injector: Injector,
    private kc: KeyCloakService,
    private logService: LogService,
    private memberService: MemberService,
    public lssConfig: LssConfig,
    private sanitizer: DomSanitizer
  ) {
    super(injector);
    
    this.initializeHome();
    this.startSessionMonitoring();
  }

  ngOnInit(): void {
    this.loadHomeData();
    this.setupAutoRefresh();
    this.emitHomeEvent('navigation', { page: 'home_loaded' });
  }

  override ngOnDestroy(): void {
    this.clearTimers();
    this.clearSensitiveData();
  }

  // Public methods for template
  public refreshData(): void {
    if (this._isLoading() || this.disabled) return;

    this._isLoading.set(true);
    this._error.set(null);

    this.loadHomeData()
      .then(() => {
        this._lastRefresh.set(Date.now());
        this.refreshRequest.emit();
        this.emitHomeEvent('refresh', { 
          timestamp: Date.now(),
          success: true 
        });
      })
      .catch((error) => {
        this._error.set('Failed to refresh data');
        this.emitHomeEvent('refresh', { 
          timestamp: Date.now(),
          success: false,
          error: error.message 
        });
      })
      .finally(() => {
        this._isLoading.set(false);
      });
  }

  public handleActionClick(action: HomeAction): void {
    if (this.disabled || !action.enabled) return;

    // Check authentication requirements
    if (action.requiresAuth && !this.isAuthenticated()) {
      this.emitHomeEvent('security_alert', { 
        type: 'unauthorized_access',
        action: action.id 
      });
      return;
    }

    this.emitHomeEvent('action_click', { 
      action: action.id,
      title: action.title 
    });

    this.actionClick.emit(action);

    if (action.route) {
      this.navigationClick.emit(action.route);
    }
  }

  public handleNotificationClick(): void {
    if (this.disabled) return;

    this.emitHomeEvent('notification_view', { timestamp: Date.now() });
    this.notificationClick.emit();
  }

  public getWelcomeMessage(): string {
    const data = this._data();
    const profile = data.profile;
    
    if (profile?.givenNames) {
      return `Welcome back, ${profile.givenNames}!`;
    }
    
    return data.welcomeMessage || 'Welcome back!';
  }

  public getLastLoginDisplay(): string {
    const data = this._data();
    if (!data.lastLoginTime) return 'First time login';
    
    const lastLogin = new Date(data.lastLoginTime);
    const now = new Date();
    const diffMs = now.getTime() - lastLogin.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    
    if (diffHours < 1) return 'Just now';
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  }

  public getNotificationCount(): number {
    return this._data().notificationCount || 0;
  }

  public getSecurityLevel(): string {
    const security = this._security();
    return security.authLevel.charAt(0).toUpperCase() + security.authLevel.slice(1);
  }

  public getSecurityScore(): number {
    return this._security().riskScore;
  }

  public getSecurityScoreColor(): string {
    const score = this.getSecurityScore();
    if (score < 30) return 'success';
    if (score < 70) return 'warning';
    return 'danger';
  }

  public getEnabledActions(): HomeAction[] {
    return this._data().quickActions?.filter(action => action.enabled) || [];
  }

  public getRecentActivities(): HomeActivity[] {
    return this._data().recentActivities?.slice(0, 5) || [];
  }

  public isAuthenticated(): boolean {
    return this.kc.authSuccess === true;
  }

  public isFeatureEnabled(feature: keyof HomeConfig): boolean {
    return this.config[feature] === true;
  }

  public isSessionActive(): boolean {
    const security = this._security();
    const now = Date.now();
    return security.sessionActive && 
           (now - security.lastActivity) < this.config.sessionTimeout;
  }

  public sanitizeHtml(html: string): SafeHtml {
    return this.sanitizer.sanitize(1, html) || '';
  }

  public getLastRefreshDisplay(): string {
    const lastRefresh = this._lastRefresh();
    if (!lastRefresh) return 'Never';
    
    const now = Date.now();
    const diff = now - lastRefresh;
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes === 1) return '1 minute ago';
    return `${minutes} minutes ago`;
  }

  // Template utility methods
  public trackByActionId(index: number, action: HomeAction): string {
    return action.id;
  }

  public trackByActivityId(index: number, activity: HomeActivity): string {
    return activity.id;
  }

  public getActivityIcon(type: HomeActivity['type']): string {
    const icons = {
      login: 'log-in-outline',
      transaction: 'card-outline',
      profile_update: 'person-outline',
      security_change: 'shield-outline'
    };
    return icons[type] || 'ellipse-outline';
  }

  public getStatusIcon(status: HomeActivity['status']): string {
    const icons = {
      completed: 'checkmark-circle-outline',
      pending: 'time-outline',
      failed: 'close-circle-outline'
    };
    return icons[status] || 'help-circle-outline';
  }

  public getActivityTime(timestamp: number): string {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  }

  // Private methods
  private initializeHome(): void {
    this._data.set(this.homeData);
    this._security.set(this.securityState);
    this.emitHomeEvent('navigation', { action: 'home_initialized' });
  }

  private async loadHomeData(): Promise<void> {
    this._isLoading.set(true);
    
    try {
      // Simulate loading user data
      if (this.isAuthenticated()) {
        // Load user profile and activities
        const currentData = this._data();
        
        // Update with fresh data
        this._data.set({
          ...currentData,
          lastLoginTime: new Date().toISOString(),
          recentActivities: this.generateRecentActivities()
        });
      }
    } catch (error) {
      this._error.set('Failed to load home data');
      throw error;
    } finally {
      this._isLoading.set(false);
    }
  }

  private generateRecentActivities(): HomeActivity[] {
    return [
      {
        id: '1',
        type: 'login',
        description: 'Successful login from mobile device',
        timestamp: Date.now() - 300000, // 5 minutes ago
        status: 'completed'
      },
      {
        id: '2',
        type: 'profile_update',
        description: 'Profile information updated',
        timestamp: Date.now() - 3600000, // 1 hour ago
        status: 'completed'
      }
    ];
  }

  private setupAutoRefresh(): void {
    if (this.config.enableRefresh && this.config.autoRefreshInterval) {
      this.autoRefreshTimer = setInterval(() => {
        if (this.isSessionActive()) {
          this.refreshData();
        }
      }, this.config.autoRefreshInterval);
    }
  }

  private startSessionMonitoring(): void {
    this.sessionTimer = setInterval(() => {
      const security = this._security();
      const now = Date.now();
      
      if ((now - security.lastActivity) > this.config.sessionTimeout) {
        this.handleSessionTimeout();
      }
    }, 60000); // Check every minute
  }

  private handleSessionTimeout(): void {
    const updatedSecurity = { 
      ...this._security(), 
      sessionActive: false 
    };
    this._security.set(updatedSecurity);
    
    this.emitHomeEvent('security_alert', { 
      type: 'session_timeout',
      timestamp: Date.now(),
      lastActivity: this._security().lastActivity 
    });
  }

  private clearTimers(): void {
    if (this.autoRefreshTimer) {
      clearInterval(this.autoRefreshTimer);
      this.autoRefreshTimer = null;
    }
    if (this.sessionTimer) {
      clearInterval(this.sessionTimer);
      this.sessionTimer = null;
    }
  }

  private clearSensitiveData(): void {
    // Clear any sensitive data from memory
    this._data.set({});
  }

  private emitHomeEvent(type: HomeEvent['type'], data?: any): void {
    const event: HomeEvent = {
      type,
      timestamp: Date.now(),
      userId: this.userId,
      deviceId: this.deviceId,
      ...data
    };
    this.homeEvent.emit(event);
  }

  // Legacy method for compatibility
  extend(): void {
    // Legacy functionality - now handled by refreshData()
    this.refreshData();
  }
}
