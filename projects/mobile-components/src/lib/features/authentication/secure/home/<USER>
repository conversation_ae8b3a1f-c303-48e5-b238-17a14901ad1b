<div class="secure-home" [ngClass]="computedClasses()">
  <!-- Loading State -->
  <div *ngIf="isLoading()" class="home-loading">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Loading your home...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error()" class="home-error">
    <ion-icon name="alert-circle-outline" class="error-icon"></ion-icon>
    <p>{{ error() }}</p>
    <ion-button (click)="refreshData()" fill="outline" size="small">
      <ion-icon name="refresh-outline" slot="start"></ion-icon>
      Try Again
    </ion-button>
  </div>

  <!-- Main Home Content -->
  <div *ngIf="!isLoading() && !error()" class="home-content">
    <div class="home-container">
      <!-- Header Section -->
      <div class="home-header" *ngIf="title">
        <div class="header-content">
          <h1 class="home-title">{{ title }}</h1>
          <p *ngIf="subtitle" class="home-subtitle">{{ subtitle }}</p>
          
          <!-- Security Badge -->
          <div class="security-badge">
            <span class="security-level"
                  [ngClass]="{
                    'level-enhanced': security().authLevel === 'enhanced',
                    'level-standard': security().authLevel === 'standard',
                    'level-basic': security().authLevel === 'basic'
                  }">
              <ion-icon name="shield-checkmark-outline"></ion-icon>
              {{ getSecurityLevel() }} Security
            </span>
          </div>
        </div>

        <!-- Refresh Button -->
        <div class="header-actions" *ngIf="isFeatureEnabled('enableRefresh')">
          <ion-button 
            (click)="refreshData()" 
            fill="clear" 
            size="small"
            [disabled]="isLoading() || disabled">
            <ion-icon name="refresh-outline" 
                     [class.spin]="isLoading()"></ion-icon>
          </ion-button>
          <span *ngIf="lastRefresh()" class="refresh-time">
            {{ getLastRefreshDisplay() }}
          </span>
        </div>
      </div>

      <!-- Welcome Message -->
      <div *ngIf="isFeatureEnabled('enableWelcomeMessage')" 
           class="welcome-section"
           [class.animate-fade-in]="isFeatureEnabled('showAnimations')">
        <div class="welcome-card">
          <div class="welcome-content">
            <h2 class="welcome-message">{{ getWelcomeMessage() }}</h2>
            <p *ngIf="isAuthenticated()" class="last-login">
              Last login: {{ getLastLoginDisplay() }}
            </p>
          </div>
          <div class="welcome-icon">
            <ion-icon name="home-outline"></ion-icon>
          </div>
        </div>
      </div>

      <!-- User Info Section -->
      <div *ngIf="isFeatureEnabled('showUserInfo') && data().profile" 
           class="user-info-section">
        <div class="user-info-card">
          <div class="user-avatar">
            <ion-icon name="person-circle-outline"></ion-icon>
          </div>
          <div class="user-details">
            <h3>{{ data().profile?.givenNames }} {{ data().profile?.surname }}</h3>
            <p>{{ data().profile?.emailAddress }}</p>
            <div class="user-badges">
              <span class="badge verified" *ngIf="security().deviceVerified">
                <ion-icon name="checkmark-circle-outline"></ion-icon>
                Device Verified
              </span>
              <span class="badge two-factor" *ngIf="security().twoFactorEnabled">
                <ion-icon name="shield-outline"></ion-icon>
                2FA Enabled
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions Grid -->
      <div *ngIf="isFeatureEnabled('enableQuickActions')" 
           class="quick-actions-section">
        <h3 class="section-title">Quick Actions</h3>
        <div class="actions-grid"
             [class.animate-fade-in-up]="isFeatureEnabled('showAnimations')">
          <div *ngFor="let action of getEnabledActions(); trackBy: trackByActionId" 
               class="action-card"
               [class.disabled]="disabled || !action.enabled"
               (click)="handleActionClick(action)">
            <div class="action-content">
              <div class="action-icon">
                <ion-icon [name]="action.icon"></ion-icon>
              </div>
              <div class="action-details">
                <h4>{{ action.title }}</h4>
                <p>{{ action.description }}</p>
              </div>
              <div class="action-arrow">
                <ion-icon name="chevron-forward-outline"></ion-icon>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Notifications Section -->
      <div *ngIf="isFeatureEnabled('enableNotifications')" 
           class="notifications-section">
        <div class="notifications-header">
          <h3 class="section-title">Notifications</h3>
          <div class="notification-badge" 
               *ngIf="getNotificationCount() > 0"
               (click)="handleNotificationClick()">
            <ion-badge color="primary">{{ getNotificationCount() }}</ion-badge>
          </div>
        </div>
        <div class="notifications-content">
          <div *ngIf="getNotificationCount() === 0" class="no-notifications">
            <ion-icon name="notifications-off-outline"></ion-icon>
            <p>No new notifications</p>
          </div>
          <div *ngIf="getNotificationCount() > 0" class="notification-summary">
            <p>You have {{ getNotificationCount() }} new notification{{ getNotificationCount() > 1 ? 's' : '' }}</p>
            <ion-button fill="outline" size="small" (click)="handleNotificationClick()">
              View All
            </ion-button>
          </div>
        </div>
      </div>

      <!-- Security Status -->
      <div *ngIf="isFeatureEnabled('enableSecurityStatus')" 
           class="security-section">
        <h3 class="section-title">Security Status</h3>
        <div class="security-content">
          <div class="security-score">
            <div class="score-circle" 
                 [ngClass]="'score-' + getSecurityScoreColor()">
              <span class="score-value">{{ getSecurityScore() }}</span>
            </div>
            <div class="score-details">
              <h4>Security Score</h4>
              <p>{{ getSecurityScoreColor() === 'success' ? 'Excellent' : 
                     getSecurityScoreColor() === 'warning' ? 'Good' : 'Needs Attention' }}</p>
            </div>
          </div>
          
          <div class="security-features">
            <div class="security-item" 
                 [class.enabled]="security().deviceVerified">
              <ion-icon name="phone-portrait-outline"></ion-icon>
              <span>Device Verification</span>
              <ion-icon [name]="security().deviceVerified ? 'checkmark-circle' : 'close-circle'"></ion-icon>
            </div>
            
            <div class="security-item" 
                 [class.enabled]="security().twoFactorEnabled">
              <ion-icon name="shield-outline"></ion-icon>
              <span>Two-Factor Authentication</span>
              <ion-icon [name]="security().twoFactorEnabled ? 'checkmark-circle' : 'close-circle'"></ion-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Activity -->
      <div *ngIf="isFeatureEnabled('enableRecentActivity')" 
           class="activity-section">
        <h3 class="section-title">Recent Activity</h3>
        <div class="activity-content">
          <div *ngIf="getRecentActivities().length === 0" class="no-activity">
            <ion-icon name="time-outline"></ion-icon>
            <p>No recent activity</p>
          </div>
          
          <div *ngFor="let activity of getRecentActivities(); trackBy: trackByActivityId" 
               class="activity-item">
            <div class="activity-icon" 
                 [ngClass]="'activity-' + activity.type">
              <ion-icon 
                [name]="getActivityIcon(activity.type)">
              </ion-icon>
            </div>
            <div class="activity-details">
              <p class="activity-description">{{ activity.description }}</p>
              <span class="activity-time">{{ getActivityTime(activity.timestamp) }}</span>
            </div>
            <div class="activity-status" 
                 [ngClass]="'status-' + activity.status">
              <ion-icon 
                [name]="getStatusIcon(activity.status)">
              </ion-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- Session Information -->
      <div class="session-info">
        <div class="session-status">
          <ion-icon name="time-outline"></ion-icon>
          <span>Session Status: </span>
          <span class="status-badge"
                [ngClass]="{
                  'active': isSessionActive(),
                  'expired': !isSessionActive()
                }">
            {{ isSessionActive() ? 'Active' : 'Expired' }}
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
