<!-- Security-Enhanced PIN Component Template -->
<div [class]="computedClasses()" [attr.aria-label]="header">
  <!-- Security Warning for Locked State -->
  <div *ngIf="isLocked()" class="security-warning" role="alert">
    <ion-icon name="lock-closed" class="warning-icon" aria-hidden="true"></ion-icon>
    <h3>Account Temporarily Locked</h3>
    <p>{{ lockedMessage }}</p>
    <p class="countdown">Time remaining: {{ getRemainingLockoutTime() }}</p>
  </div>

  <!-- Session Expired Warning -->
  <div *ngIf="isSessionExpired()" class="security-warning session-expired" role="alert">
    <ion-icon name="time" class="warning-icon" aria-hidden="true"></ion-icon>
    <h3>Session Expired</h3>
    <p>Please restart your session for security.</p>
  </div>

  <!-- PIN Entry Interface -->
  <div class="pin-lock" [class.disabled]="disabled || isLocked() || isSessionExpired()">
    <!-- Header Section -->
    <div class="lock-label-row" role="heading" aria-level="1">
      {{ header }}
    </div>
    
    <div class="lock-label-row small" role="heading" aria-level="2">
      {{ subheader }}
    </div>
    
    <!-- Invalid Attempt Warning -->
    <div class="lock-label-row invalid" *ngIf="invalid" role="alert">
      {{ invalidLabel }}
    </div>

    <!-- Attempt Warning -->
    <div class="lock-label-row warning" *ngIf="getAttemptWarning()" role="alert">
      <ion-icon name="warning" aria-hidden="true"></ion-icon>
      {{ getAttemptWarning() }}
    </div>
    
    <!-- PIN Progress Circles -->
    <div 
      class="lock-circles-row" 
      [ngClass]="invalid ? 'lock-shake' : ''"
      role="progressbar"
      [attr.aria-valuenow]="pin().length"
      [attr.aria-valuemin]="0"
      [attr.aria-valuemax]="pinLength"
      [attr.aria-label]="'PIN entry progress: ' + pin().length + ' of ' + pinLength + ' digits entered'"
    >
      <div 
        class="lock-circle" 
        *ngFor="let col of [].constructor(pinLength); let i = index" 
        [ngClass]="pin().length > i ? 'lock-full' : ''"
        [attr.aria-hidden]="true"
      ></div>  
    </div>

    <!-- Progress Bar (if enabled) -->
    <div *ngIf="config.showProgress" class="progress-container">
      <ion-progress-bar 
        [value]="getPinProgress() / 100"
        [color]="variant === 'default' ? 'primary' : variant"
        class="pin-progress"
      ></ion-progress-bar>
    </div>

    <!-- Number Pad -->
    <div class="number-pad" [class.shuffled]="config.shuffleNumbers">
      <!-- Row 1: 1,2,3 or shuffled equivalent -->
      <div class="lock-numbers-row">
        <button 
          (click)="digit(getNumberForPosition(1))" 
          class="lock-digit"
          [disabled]="disabled || isLocked() || isSessionExpired()"
          [attr.aria-label]="'Digit ' + getNumberForPosition(1)"
          type="button"
        >
          {{ getNumberForPosition(1) }}
        </button>
        <button 
          (click)="digit(getNumberForPosition(2))" 
          class="lock-digit"
          [disabled]="disabled || isLocked() || isSessionExpired()"
          [attr.aria-label]="'Digit ' + getNumberForPosition(2)"
          type="button"
        >
          {{ getNumberForPosition(2) }}
        </button>
        <button 
          (click)="digit(getNumberForPosition(3))" 
          class="lock-digit"
          [disabled]="disabled || isLocked() || isSessionExpired()"
          [attr.aria-label]="'Digit ' + getNumberForPosition(3)"
          type="button"
        >
          {{ getNumberForPosition(3) }}
        </button>
      </div>

      <!-- Row 2: 4,5,6 or shuffled equivalent -->
      <div class="lock-numbers-row">
        <button 
          (click)="digit(getNumberForPosition(4))" 
          class="lock-digit"
          [disabled]="disabled || isLocked() || isSessionExpired()"
          [attr.aria-label]="'Digit ' + getNumberForPosition(4)"
          type="button"
        >
          {{ getNumberForPosition(4) }}
        </button>
        <button 
          (click)="digit(getNumberForPosition(5))" 
          class="lock-digit"
          [disabled]="disabled || isLocked() || isSessionExpired()"
          [attr.aria-label]="'Digit ' + getNumberForPosition(5)"
          type="button"
        >
          {{ getNumberForPosition(5) }}
        </button>
        <button 
          (click)="digit(getNumberForPosition(6))" 
          class="lock-digit"
          [disabled]="disabled || isLocked() || isSessionExpired()"
          [attr.aria-label]="'Digit ' + getNumberForPosition(6)"
          type="button"
        >
          {{ getNumberForPosition(6) }}
        </button>
      </div>

      <!-- Row 3: 7,8,9 or shuffled equivalent -->
      <div class="lock-numbers-row">
        <button 
          (click)="digit(getNumberForPosition(7))" 
          class="lock-digit"
          [disabled]="disabled || isLocked() || isSessionExpired()"
          [attr.aria-label]="'Digit ' + getNumberForPosition(7)"
          type="button"
        >
          {{ getNumberForPosition(7) }}
        </button>
        <button 
          (click)="digit(getNumberForPosition(8))" 
          class="lock-digit"
          [disabled]="disabled || isLocked() || isSessionExpired()"
          [attr.aria-label]="'Digit ' + getNumberForPosition(8)"
          type="button"
        >
          {{ getNumberForPosition(8) }}
        </button>
        <button 
          (click)="digit(getNumberForPosition(9))" 
          class="lock-digit"
          [disabled]="disabled || isLocked() || isSessionExpired()"
          [attr.aria-label]="'Digit ' + getNumberForPosition(9)"
          type="button"
        >
          {{ getNumberForPosition(9) }}
        </button>
      </div>

      <!-- Row 4: Clear, 0, Delete -->
      <div class="lock-numbers-row">
        <button 
          (click)="clear()" 
          class="lock-digit lock-ac"
          [disabled]="disabled || isLocked() || isSessionExpired()"
          aria-label="Clear all digits"
          type="button"
        >
          <ion-icon name="refresh-outline" aria-hidden="true"></ion-icon>
        </button>
        <button 
          (click)="digit(getNumberForPosition(0))" 
          class="lock-digit"
          [disabled]="disabled || isLocked() || isSessionExpired()"
          [attr.aria-label]="'Digit ' + getNumberForPosition(0)"
          type="button"
        >
          {{ getNumberForPosition(0) }}
        </button>
        <button 
          (click)="remove()" 
          class="lock-digit lock-del"
          [disabled]="disabled || isLocked() || isSessionExpired()"
          aria-label="Remove last digit"
          type="button"
        >
          <ion-icon name="backspace-outline" aria-hidden="true"></ion-icon>
        </button>
      </div>

      <!-- Row 5: Biometric (if enabled) and Cancel -->
      <div class="lock-numbers-row">
        <!-- Biometric Button -->
        <button 
          *ngIf="config.enableBiometrics" 
          (click)="requestBiometric()" 
          class="lock-digit lock-biometric"
          [disabled]="disabled || isLocked() || isSessionExpired()"
          aria-label="Use biometric authentication"
          type="button"
        >
          <ion-icon name="finger-print" aria-hidden="true"></ion-icon>
        </button>
        
        <!-- Spacer if no biometric -->
        <div *ngIf="!config.enableBiometrics" class="lock-digit lock-spacer"></div>
        
        <!-- Cancel Button -->
        <button 
          (click)="cancelPin()" 
          class="lock-digit lock-cancel"
          [disabled]="disabled"
          aria-label="Cancel PIN entry"
          type="button"
        >
          Cancel
        </button>
        
        <!-- Spacer -->
        <div class="lock-digit lock-spacer"></div>
      </div>
    </div>

    <!-- Security Status -->
    <div class="security-status" *ngIf="securityConfig.auditLogging">
      <small class="security-info">
        <ion-icon name="shield-checkmark" aria-hidden="true"></ion-icon>
        Secure PIN entry enabled
      </small>
    </div>
  </div>
</div>