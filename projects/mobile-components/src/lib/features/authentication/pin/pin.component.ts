import {
  Component,
  EventEmitter,
  Injector,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
  computed,
  signal,
  OnDestroy
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { IdleService } from 'lp-client-api';
import { AbstractComponent } from '../../../shared/abstract.component';
import { Haptics } from '@capacitor/haptics';
import { Subscription } from 'rxjs';

export type PinSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
export type PinVariant = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
export type PinRounded = 'none' | 'sm' | 'md' | 'lg' | 'full';
export type PinLayout = 'standard' | 'compact' | 'extended';

export interface PinConfig {
  enableHaptics?: boolean;
  enableBiometrics?: boolean;
  autoSubmit?: boolean;
  showProgress?: boolean;
  enableShake?: boolean;
  requireConfirmation?: boolean;
  shuffleNumbers?: boolean;
}

export interface SecurityConfig {
  maxAttempts?: number;
  lockoutDuration?: number; // in milliseconds
  sessionTimeout?: number; // in milliseconds
  auditLogging?: boolean;
  encryptPin?: boolean;
}

@Component({
  selector: 'lp-pin',
  templateUrl: './pin.component.html',
  styleUrls: ['./pin.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    IonicModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class PinComponent extends AbstractComponent implements OnChanges, OnDestroy {
  // Standard UI inputs
  @Input() className: string = '';
  @Input() size: PinSize = 'md';
  @Input() variant: PinVariant = 'default';
  @Input() rounded: PinRounded = 'md';
  @Input() disabled: boolean = false;
  @Input() layout: PinLayout = 'standard';

  // Pin-specific inputs
  @Input() header: string = 'PIN Authorization';
  @Input() subheader: string = 'Please enter your pin code';
  @Input() invalidLabel: string = 'Invalid pin';
  @Input() pinLength: number = 5;
  @Input() timeout: number = 0; // Timeout in seconds
  @Input() invalid: boolean = false;
  @Input() eventState: string = 'start';
  @Input() placeholder: string = 'Enter PIN';
  @Input() successMessage: string = 'PIN accepted';
  @Input() lockedMessage: string = 'Too many attempts. Please wait.';

  // Configuration inputs
  @Input() config: PinConfig = {
    enableHaptics: true,
    enableBiometrics: false,
    autoSubmit: true,
    showProgress: true,
    enableShake: true,
    requireConfirmation: false,
    shuffleNumbers: false
  };

  @Input() securityConfig: SecurityConfig = {
    maxAttempts: 3,
    lockoutDuration: 300000, // 5 minutes
    sessionTimeout: 900000, // 15 minutes
    auditLogging: true,
    encryptPin: true
  };

  // Event outputs
  @Output() submit = new EventEmitter<string>();
  @Output() timedOut = new EventEmitter<boolean>();
  @Output() cancel = new EventEmitter<boolean>();
  @Output() pinChange = new EventEmitter<string>();
  @Output() securityEvent = new EventEmitter<{type: string, data: any}>();
  @Output() biometricRequest = new EventEmitter<void>();
  @Output() maxAttemptsReached = new EventEmitter<void>();

  // Internal state using signals
  private _pin = signal('');
  private _attempts = signal(0);
  private _lastAttempt = signal<number | null>(null);
  private _isLocked = signal(false);
  private _sessionStart = signal(Date.now());
  private _numbers = signal([1, 2, 3, 4, 5, 6, 7, 8, 9, 0]);

  // Computed properties
  public pin = computed(() => this._pin());
  public attempts = computed(() => this._attempts());
  public isLocked = computed(() => {
    if (!this._lastAttempt() || this._attempts() < (this.securityConfig.maxAttempts || 3)) {
      return false;
    }
    const timeSinceLastAttempt = Date.now() - this._lastAttempt()!;
    return timeSinceLastAttempt < (this.securityConfig.lockoutDuration || 300000);
  });
  public isSessionExpired = computed(() => {
    const sessionDuration = Date.now() - this._sessionStart();
    return sessionDuration > (this.securityConfig.sessionTimeout || 900000);
  });
  public numbers = computed(() => this._numbers());

  // Computed classes
  public computedClasses = computed(() => {
    const baseClasses = ['pin-component'];
    const sizeClasses = {
      xs: 'pin-xs',
      sm: 'pin-sm', 
      md: 'pin-md',
      lg: 'pin-lg',
      xl: 'pin-xl'
    };
    const variantClasses = {
      default: 'pin-default',
      primary: 'pin-primary',
      secondary: 'pin-secondary',
      success: 'pin-success',
      warning: 'pin-warning',
      danger: 'pin-danger'
    };
    const roundedClasses = {
      none: 'pin-rounded-none',
      sm: 'pin-rounded-sm',
      md: 'pin-rounded-md',
      lg: 'pin-rounded-lg',
      full: 'pin-rounded-full'
    };

    return [
      ...baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      this.layout === 'compact' ? 'pin-compact' : '',
      this.disabled || this.isLocked() ? 'pin-disabled' : '',
      this.className
    ].filter(Boolean).join(' ');
  });

  protected pinSub?: Subscription;
  constructor(injector: Injector, private idleService: IdleService) {
    super(injector);
    this.initializeSecurity();
    if (this.config.shuffleNumbers) {
      this.shuffleNumberPad();
    }
  }

  override ngOnDestroy(): void {
    this.clearSensitiveData();
  }

  private initializeSecurity(): void {
    this._sessionStart.set(Date.now());
    if (this.securityConfig.auditLogging) {
      this.securityEvent.emit({
        type: 'pin_component_initialized',
        data: { 
          timestamp: Date.now(),
          config: this.config,
          sessionId: this.generateSessionId()
        }
      });
    }
  }

  private generateSessionId(): string {
    return `pin_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private shuffleNumberPad(): void {
    const numbers = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
    for (let i = numbers.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [numbers[i], numbers[j]] = [numbers[j], numbers[i]];
    }
    this._numbers.set(numbers);
  }

  private clearSensitiveData(): void {
    this._pin.set('');
    // Clear any remaining PIN data from memory
    setTimeout(() => {
      this._pin.set('');
    }, 100);
  }

  private sanitizeInput(input: any): string {
    // Ensure only numeric input
    const sanitized = String(input).replace(/[^0-9]/g, '');
    return sanitized.slice(0, 1); // Only allow single digit
  }

  private encryptPin(pin: string): string {
    if (!this.securityConfig.encryptPin) return pin;
    // Basic encryption for demo - in production use proper encryption
    return btoa(pin + '_' + Date.now());
  }

  private checkSecurityConstraints(): boolean {
    if (this.isLocked()) {
      this.securityEvent.emit({
        type: 'attempt_while_locked',
        data: { timestamp: Date.now(), attempts: this._attempts() }
      });
      return false;
    }

    if (this.isSessionExpired()) {
      this.securityEvent.emit({
        type: 'session_expired',
        data: { timestamp: Date.now(), sessionDuration: Date.now() - this._sessionStart() }
      });
      this.timedOut.emit(true);
      return false;
    }

    return true;
  }

  ionViewWillEnter(): void {
    // Subscribe to the timeout
    if (this.timeout > 0) {
      this.addViewSubscription(
        this.idleService.timeout.subscribe((timeoutCheck) => {
          if (timeoutCheck >= this.timeout) {
            this.timedOut.emit(true);
          }
        })
      );
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.invalid) {
      this.invalidAttempt();
    }

    if (changes['eventState']) {
      let previousEvent: string = changes['eventState'].previousValue;
      let currentEvent: string = changes['eventState'].currentValue;

      if (
        previousEvent !== '' &&
        currentEvent !== '' &&
        previousEvent !== currentEvent
      ) {
        console.log('Pin old eventState: ', previousEvent);
        console.log('Pin new eventState: ', currentEvent);
        this._pin.set('');
        this.invalid = false;
      }
    }

    // Check if component should be locked due to security constraints
    if (this.isLocked()) {
      this._isLocked.set(true);
    }
  }

  protected digit(digit: any): void {
    if (!this.checkSecurityConstraints() || this.disabled) {
      return;
    }

    const sanitizedDigit = this.sanitizeInput(digit);
    if (!sanitizedDigit) return;

    // Haptic feedback
    if (this.config.enableHaptics) {
      Haptics.vibrate();
    }

    const currentPin = this._pin();
    const newPin = currentPin + sanitizedDigit;
    this._pin.set(newPin);

    // Emit pin change event (without revealing the actual PIN)
    this.pinChange.emit('*'.repeat(newPin.length));

    // Security logging
    if (this.securityConfig.auditLogging) {
      this.securityEvent.emit({
        type: 'digit_entered',
        data: { 
          timestamp: Date.now(),
          pinLength: newPin.length,
          target: this.pinLength
        }
      });
    }

    // Auto-submit when PIN is complete
    if (newPin.length === this.pinLength && this.config.autoSubmit) {
      this.submitPin();
    }
  }

  private submitPin(): void {
    const currentPin = this._pin();
    
    if (currentPin.length !== this.pinLength) {
      return;
    }

    // Security event logging
    if (this.securityConfig.auditLogging) {
      this.securityEvent.emit({
        type: 'pin_submitted',
        data: { 
          timestamp: Date.now(),
          pinLength: currentPin.length,
          attempts: this._attempts() + 1
        }
      });
    }

    // Encrypt PIN if required
    const pinToSubmit = this.securityConfig.encryptPin ? 
      this.encryptPin(currentPin) : currentPin;

    // Emit the PIN
    this.submit.emit(pinToSubmit);
    
    // Clear PIN from memory immediately after submission
    this._pin.set('');
  }

  protected invalidAttempt(): void {
    this._attempts.set(this._attempts() + 1);
    this._lastAttempt.set(Date.now());

    // Security event logging
    if (this.securityConfig.auditLogging) {
      this.securityEvent.emit({
        type: 'invalid_attempt',
        data: { 
          timestamp: Date.now(),
          attempts: this._attempts(),
          maxAttempts: this.securityConfig.maxAttempts || 3
        }
      });
    }

    // Check if max attempts reached
    if (this._attempts() >= (this.securityConfig.maxAttempts || 3)) {
      this._isLocked.set(true);
      this.maxAttemptsReached.emit();
      
      if (this.securityConfig.auditLogging) {
        this.securityEvent.emit({
          type: 'account_locked',
          data: { 
            timestamp: Date.now(),
            attempts: this._attempts(),
            lockoutDuration: this.securityConfig.lockoutDuration || 300000
          }
        });
      }
    }

    // Shake animation if enabled
    if (this.config.enableShake) {
      setTimeout(() => {
        this._pin.set('');
        this.invalid = false;
      }, 1000);
    }
  }

  protected clear(): void {
    if (!this.checkSecurityConstraints() || this.disabled) {
      return;
    }

    this._pin.set('');
    this.invalid = false;
    
    if (this.securityConfig.auditLogging) {
      this.securityEvent.emit({
        type: 'pin_cleared',
        data: { timestamp: Date.now() }
      });
    }
  }

  protected remove(): void {
    if (!this.checkSecurityConstraints() || this.disabled) {
      return;
    }

    const currentPin = this._pin();
    this._pin.set(currentPin.slice(0, -1));
    
    if (this.securityConfig.auditLogging) {
      this.securityEvent.emit({
        type: 'digit_removed',
        data: { 
          timestamp: Date.now(),
          newLength: this._pin().length
        }
      });
    }
  }

  protected cancelPin(): void {
    this.clearSensitiveData();
    this.cancel.emit(true);
    
    if (this.securityConfig.auditLogging) {
      this.securityEvent.emit({
        type: 'pin_cancelled',
        data: { timestamp: Date.now() }
      });
    }
  }

  protected requestBiometric(): void {
    if (this.config.enableBiometrics && !this.disabled) {
      this.biometricRequest.emit();
      
      if (this.securityConfig.auditLogging) {
        this.securityEvent.emit({
          type: 'biometric_requested',
          data: { timestamp: Date.now() }
        });
      }
    }
  }

  // Helper methods for template
  public getPinProgress(): number {
    return (this._pin().length / this.pinLength) * 100;
  }

  public getAttemptWarning(): string {
    const maxAttempts = this.securityConfig.maxAttempts || 3;
    const remaining = maxAttempts - this._attempts();
    if (remaining <= 1 && remaining > 0) {
      return `${remaining} attempt remaining`;
    }
    return '';
  }

  public getRemainingLockoutTime(): string {
    if (!this.isLocked()) return '';
    
    const lastAttempt = this._lastAttempt();
    if (!lastAttempt) return '';
    
    const lockoutDuration = this.securityConfig.lockoutDuration || 300000;
    const elapsed = Date.now() - lastAttempt;
    const remaining = Math.max(0, lockoutDuration - elapsed);
    
    const minutes = Math.floor(remaining / 60000);
    const seconds = Math.floor((remaining % 60000) / 1000);
    
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  public getNumberForPosition(position: number): number {
    const nums = this.numbers();
    return nums[position] || position;
  }
}
