/* Animations*/
@keyframes lock-shake {
    from, to {
      transform: translate3d(0, 0, 0);
    }
    10%, 30%, 50%, 70%, 90% {
      transform: translate3d(-10px, 0, 0);
    }
    20%, 40%, 60%, 80% {
      transform: translate3d(10px, 0, 0);
    }
  }
@keyframes lock-buttonPress {
    0% {
        background-color: #E0E0E0;
    }
    100% {
        background-color: var(--ion-color-primary);
    }
}
/* Lock Screen Layout*/
.pin-lock {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;
    height: 100%;
    z-index: 999;

    .lock-label-row {
        height: 50px;
        width: 100%;
        text-align: center;
        font-size: 26px;
        
        color: var(--ion-text-color);
    }
    .small {
        font-size: 18px;
    }
    .invalid {
        color: var(--ion-color-danger);
        font-size: 24px;
    }
    .lock-circles-row {
        display: flex;
        flex-direction: row;
        justify-content: center;
        width: 100%;
        height: 45px;
    }
    .lock-circle {
        border-radius: 50%;
        width: 14px;
        height: 14px;
        border:solid 2px var(--ion-text-color);
        margin: 0 15px;
    }
    .lock-numbers-row {
        display: flex;
        flex-direction: row;
        justify-content: center;
        width: 100%;
        height: 100px;
    }
    .lock-digit {
        margin: 0 8px;
        width: 75px;
        border-radius: 10%;
        height: 75px;
        text-align: center;
        font-size: 26px;
        color: #fff;
        background-color: var(--ion-color-primary);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
    .lock-digit:active {
        transform: scale(0.95);
        -webkit-box-shadow: 0px 0px 10px 5px rgba(0,0,0,0.4);
        -moz-box-shadow: 0px 0px 10px 5px rgba(0,0,0,0.4);
        box-shadow: 0px 0px 10px 5px rgba(0,0,0,0.4);
    }
    .lock-cancel {
        height: 50px;
        width: 180px;
        font-size: 20px;
    }
    .lock-ac, .lock-del {
        color: var(--ion-text-color);
        background-color: transparent;
        font-size: 28px;
    }
    .lock-full {
        background-color:var(--ion-text-color)!important;
    }
    .lock-shake {
        -webkit-animation-name: lock-shake;
        animation-name: lock-shake;
        -webkit-animation-duration: 0.6;
        animation-duration: 0.6s;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
    }
}

/* Security Enhancement Styles */
.pin-component {
  width: 100%;
  min-height: 100vh;
  
  &.pin-disabled {
    opacity: 0.6;
    pointer-events: none;
  }
  
  &.pin-compact {
    min-height: auto;
    padding: 1rem;
  }
}

/* Security Warning Styles */
.security-warning {
  background: var(--ion-color-danger-tint);
  border: 2px solid var(--ion-color-danger);
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem;
  text-align: center;
  
  .warning-icon {
    font-size: 3rem;
    color: var(--ion-color-danger);
    margin-bottom: 0.5rem;
  }
  
  h3 {
    color: var(--ion-color-danger-shade);
    margin: 0.5rem 0;
    font-weight: 600;
  }
  
  p {
    margin: 0.25rem 0;
    color: var(--ion-color-danger-shade);
  }
  
  .countdown {
    font-family: 'Courier New', monospace;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--ion-color-danger);
  }
  
  &.session-expired {
    background: var(--ion-color-warning-tint);
    border-color: var(--ion-color-warning);
    
    .warning-icon,
    h3,
    p {
      color: var(--ion-color-warning-shade);
    }
  }
}

/* Progress Bar Styles */
.progress-container {
  padding: 0 2rem;
  margin: 1rem 0;
  
  .pin-progress {
    height: 6px;
    border-radius: 3px;
  }
}

/* Number Pad Enhancements */
.number-pad {
  &.shuffled {
    .lock-numbers-row {
      animation: subtle-shuffle 0.3s ease-in-out;
    }
  }
}

@keyframes subtle-shuffle {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

/* Enhanced Lock Digit Styles */
.lock-digit {
  position: relative;
  overflow: hidden;
  border: none;
  background: var(--ion-color-light);
  transition: all 0.2s ease;
  
  &:hover:not(:disabled) {
    background: var(--ion-color-light-shade);
    transform: scale(1.05);
  }
  
  &:active:not(:disabled) {
    transform: scale(0.95);
    background: var(--ion-color-primary);
    color: var(--ion-color-primary-contrast);
  }
  
  &:disabled {
    background: var(--ion-color-medium-tint);
    color: var(--ion-color-medium);
    cursor: not-allowed;
  }
  
  &.lock-biometric {
    background: var(--ion-color-success-tint);
    color: var(--ion-color-success);
    
    &:hover:not(:disabled) {
      background: var(--ion-color-success);
      color: var(--ion-color-success-contrast);
    }
  }
  
  &.lock-cancel {
    background: var(--ion-color-danger-tint);
    color: var(--ion-color-danger);
    
    &:hover:not(:disabled) {
      background: var(--ion-color-danger);
      color: var(--ion-color-danger-contrast);
    }
  }
  
  &.lock-spacer {
    background: transparent;
    border: none;
    pointer-events: none;
  }
}

/* Attempt Warning Styles */
.lock-label-row.warning {
  color: var(--ion-color-warning);
  font-weight: 600;
  
  ion-icon {
    margin-right: 0.5rem;
    vertical-align: middle;
  }
}

/* Security Status */
.security-status {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  
  .security-info {
    display: flex;
    align-items: center;
    color: var(--ion-color-success);
    font-size: 0.75rem;
    
    ion-icon {
      margin-right: 0.25rem;
      font-size: 1rem;
    }
  }
}

/* Size Variations */
.pin-xs {
  .lock-digit {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .lock-circle {
    width: 12px;
    height: 12px;
  }
}

.pin-sm {
  .lock-digit {
    width: 50px;
    height: 50px;
    font-size: 1.1rem;
  }
  
  .lock-circle {
    width: 14px;
    height: 14px;
  }
}

.pin-md {
  // Default medium size - uses existing styles
  .lock-digit {
    width: 60px;
    height: 60px;
    font-size: 1.2rem;
  }
  
  .lock-circle {
    width: 16px;
    height: 16px;
  }
}

.pin-lg {
  .lock-digit {
    width: 70px;
    height: 70px;
    font-size: 1.4rem;
  }
  
  .lock-circle {
    width: 18px;
    height: 18px;
  }
}

.pin-xl {
  .lock-digit {
    width: 80px;
    height: 80px;
    font-size: 1.6rem;
  }
  
  .lock-circle {
    width: 20px;
    height: 20px;
  }
}

/* Variant Color Schemes */
.pin-primary {
  .lock-digit:not(.lock-cancel):not(.lock-biometric) {
    &:active:not(:disabled) {
      background: var(--ion-color-primary);
      color: var(--ion-color-primary-contrast);
    }
  }
  
  .lock-circle.lock-full {
    background: var(--ion-color-primary);
  }
}

.pin-secondary {
  .lock-digit:not(.lock-cancel):not(.lock-biometric) {
    &:active:not(:disabled) {
      background: var(--ion-color-secondary);
      color: var(--ion-color-secondary-contrast);
    }
  }
  
  .lock-circle.lock-full {
    background: var(--ion-color-secondary);
  }
}

.pin-success {
  .lock-digit:not(.lock-cancel):not(.lock-biometric) {
    &:active:not(:disabled) {
      background: var(--ion-color-success);
      color: var(--ion-color-success-contrast);
    }
  }
  
  .lock-circle.lock-full {
    background: var(--ion-color-success);
  }
}

.pin-warning {
  .lock-digit:not(.lock-cancel):not(.lock-biometric) {
    &:active:not(:disabled) {
      background: var(--ion-color-warning);
      color: var(--ion-color-warning-contrast);
    }
  }
  
  .lock-circle.lock-full {
    background: var(--ion-color-warning);
  }
}

.pin-danger {
  .lock-digit:not(.lock-cancel):not(.lock-biometric) {
    &:active:not(:disabled) {
      background: var(--ion-color-danger);
      color: var(--ion-color-danger-contrast);
    }
  }
  
  .lock-circle.lock-full {
    background: var(--ion-color-danger);
  }
}

/* Rounded Variations */
.pin-rounded-none .lock-digit {
  border-radius: 0;
}

.pin-rounded-sm .lock-digit {
  border-radius: 4px;
}

.pin-rounded-md .lock-digit {
  border-radius: 8px;
}

.pin-rounded-lg .lock-digit {
  border-radius: 12px;
}

.pin-rounded-full .lock-digit {
  border-radius: 50%;
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
  .lock-digit {
    transition: none;
  }
  
  .lock-shake {
    animation: none;
  }
  
  .number-pad.shuffled .lock-numbers-row {
    animation: none;
  }
}

/* Focus States for Better Accessibility */
.lock-digit:focus {
  outline: 3px solid var(--ion-color-primary);
  outline-offset: 2px;
  z-index: 1;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .lock-digit {
    border: 2px solid currentColor;
  }
  
  .security-warning {
    border-width: 3px;
  }
}