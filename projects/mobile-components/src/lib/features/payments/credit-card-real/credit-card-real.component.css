/* Credit Card Real Component - PCI-DSS Compliant Styling */

/* Container Styles */
.credit-card-real-container {
  --primary-color: #2563eb;
  --success-color: #059669;
  --warning-color: #d97706;
  --danger-color: #dc2626;
  --neutral-color: #6b7280;
  --background-color: #ffffff;
  --border-color: #d1d5db;
  --text-color: #1f2937;
  --shadow-light: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-medium: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-large: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  --border-radius: 0.5rem;
  --transition: all 0.2s ease-in-out;

  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  max-width: 480px;
  margin: 0 auto;
  padding: 1.5rem;
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  position: relative;
  overflow: hidden;
}

/* Dark Mode Support */
.credit-card-real-container.dark {
  --background-color: #1f2937;
  --border-color: #374151;
  --text-color: #f9fafb;
  --neutral-color: #9ca3af;
}

/* Size Variations */
.credit-card-real-container.size-xs {
  padding: 0.75rem;
  font-size: 0.875rem;
}

.credit-card-real-container.size-sm {
  padding: 1rem;
  font-size: 0.875rem;
}

.credit-card-real-container.size-md {
  padding: 1.5rem;
  font-size: 1rem;
}

.credit-card-real-container.size-lg {
  padding: 2rem;
  font-size: 1.125rem;
}

.credit-card-real-container.size-xl {
  padding: 2.5rem;
  font-size: 1.25rem;
}

/* Variant Styles */
.credit-card-real-container.variant-primary {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 1px var(--primary-color);
}

.credit-card-real-container.variant-success {
  border-color: var(--success-color);
  box-shadow: 0 0 0 1px var(--success-color);
}

.credit-card-real-container.variant-warning {
  border-color: var(--warning-color);
  box-shadow: 0 0 0 1px var(--warning-color);
}

.credit-card-real-container.variant-danger {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 1px var(--danger-color);
}

/* Rounded Variations */
.credit-card-real-container.rounded-none {
  border-radius: 0;
}

.credit-card-real-container.rounded-sm {
  border-radius: 0.25rem;
}

.credit-card-real-container.rounded-lg {
  border-radius: 1rem;
}

.credit-card-real-container.rounded-full {
  border-radius: 2rem;
}

/* Disabled State */
.credit-card-real-container.disabled {
  opacity: 0.6;
  pointer-events: none;
  background-color: #f9fafb;
}

/* Security Header */
.security-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.security-badges {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.security-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-color);
}

.security-badge.pci-compliant {
  background: #dcfdf7;
  border-color: #6ee7b7;
  color: #065f46;
}

.security-badge.ssl-encrypted {
  background: #dbeafe;
  border-color: #93c5fd;
  color: #1e40af;
}

.security-badge.real-time {
  background: #fef3c7;
  border-color: #fcd34d;
  color: #92400e;
}

.security-status {
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.security-status.status-secure {
  background: #dcfdf7;
  color: #065f46;
}

.security-status.status-warning {
  background: #fef3c7;
  color: #92400e;
}

.security-status.status-danger {
  background: #fee2e2;
  color: #991b1b;
}

/* Session Timer */
.session-timer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.5rem;
  background: #f3f4f6;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-color);
}

.session-timer.warning {
  background: #fef3c7;
  color: #92400e;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Form Styles */
.credit-card-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.required {
  color: var(--danger-color);
}

/* Input Wrapper and Input Styles */
.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  font-size: 1rem;
  color: var(--text-color);
  background: var(--background-color);
  transition: var(--transition);
  outline: none;
}

.form-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.form-input:invalid {
  border-color: var(--danger-color);
}

.form-input:disabled {
  background-color: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

.form-input::placeholder {
  color: #9ca3af;
}

/* Card Number Specific Styles */
.card-number-wrapper {
  position: relative;
}

.card-number-input {
  padding-right: 8rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  letter-spacing: 0.05em;
}

.card-type-indicator {
  position: absolute;
  right: 3rem;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: rgba(255, 255, 255, 0.9);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  z-index: 2;
}

/* Validation Indicators */
.validation-indicator {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 3;
}

.luhn-check,
.expiry-check,
.cvc-check {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: 600;
  transition: var(--transition);
}

.luhn-check.valid,
.expiry-check.valid,
.cvc-check.valid {
  background: var(--success-color);
  color: white;
}

.luhn-check.invalid,
.expiry-check.invalid,
.cvc-check.invalid {
  background: var(--danger-color);
  color: white;
}

/* Error Messages */
.form-errors {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.error-message {
  color: var(--danger-color);
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.error-message::before {
  content: '⚠️';
  font-size: 0.875rem;
}

/* Validation Summary */
.validation-summary {
  padding: 1rem;
  background: #f9fafb;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  margin-top: 1rem;
}

.validation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
}

.validation-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: var(--transition);
}

.validation-item.valid {
  background: #dcfdf7;
  color: #065f46;
}

.validation-item.invalid {
  background: #fee2e2;
  color: #991b1b;
}

.validation-icon {
  font-weight: 600;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 3rem;
  outline: none;
}

.btn:focus {
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.2);
}

.btn-submit {
  flex: 1;
  background: var(--primary-color);
  color: white;
}

.btn-submit:hover:not(:disabled) {
  background: #1d4ed8;
  transform: translateY(-1px);
  box-shadow: var(--shadow-large);
}

.btn-submit:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-submit.loading {
  pointer-events: none;
}

.btn-reset {
  background: #f3f4f6;
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.btn-reset:hover:not(:disabled) {
  background: #e5e7eb;
  transform: translateY(-1px);
}

/* Loading Spinner */
.spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Security Footer */
.security-footer {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.security-notice {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--neutral-color);
  text-align: center;
}

.compliance-badges {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.pci-badge {
  background: #dcfdf7;
  color: #065f46;
  border: 1px solid #6ee7b7;
}

.ssl-badge {
  background: #dbeafe;
  color: #1e40af;
  border: 1px solid #93c5fd;
}

.gdpr-badge {
  background: #f3e8ff;
  color: #6b21a8;
  border: 1px solid #c4b5fd;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  border-radius: inherit;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  text-align: center;
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid #e5e7eb;
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 0.875rem;
  color: var(--neutral-color);
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 640px) {
  .credit-card-real-container {
    padding: 1rem;
    margin: 0;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .security-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .security-badges {
    justify-content: center;
  }

  .form-actions {
    flex-direction: column;
  }

  .validation-grid {
    grid-template-columns: 1fr;
  }

  .security-footer {
    text-align: center;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .credit-card-real-container {
    border-width: 2px;
    --shadow-medium: 0 4px 6px -1px rgb(0 0 0 / 0.3);
  }

  .form-input:focus {
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.4);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .credit-card-real-container * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print Styles */
@media print {
  .credit-card-real-container {
    box-shadow: none;
    border: 1px solid #000;
  }

  .btn,
  .loading-overlay,
  .security-badges,
  .validation-indicator {
    display: none !important;
  }
}