<!-- Real-time Credit Card Validation Component Template -->
<div [class]="containerClasses()" [attr.aria-label]="'Real-time credit card validation form'" role="region">
  <!-- Security Status Header -->
  <div class="security-header" *ngIf="mergedConfig().validation.showSecurityBadges">
    <div class="security-badges">
      <span class="security-badge pci-compliant" title="PCI-DSS Compliant">
        🔒 PCI Secure
      </span>
      <span class="security-badge ssl-encrypted" title="SSL Encrypted">
        🛡️ SSL
      </span>
      <span class="security-badge real-time" title="Real-time Validation">
        ⚡ Real-time
      </span>
      <span [class]="'security-status status-' + securityStatus()" 
            [title]="'Security Status: ' + securityStatus()">
        {{ getSecurityIcon() }} {{ securityStatus() | titlecase }}
      </span>
    </div>
    
    <!-- Session Timer -->
    <div class="session-timer" *ngIf="sessionTimeLeft() > 0" 
         [class.warning]="sessionTimeLeft() < 60">
      <span class="timer-icon">⏱️</span>
      <span class="timer-text">{{ Math.floor(sessionTimeLeft() / 60) }}:{{ (sessionTimeLeft() % 60).toString().padStart(2, '0') }}</span>
    </div>
  </div>

  <!-- Credit Card Form -->
  <form [formGroup]="creditCardForm" 
        (ngSubmit)="submitForm()" 
        class="credit-card-form"
        [class.submitting]="isSubmitting()"
        novalidate>
    
    <!-- Card Number Field -->
    <div class="form-group card-number-group">
      <label for="cardNumber" 
             class="form-label"
             *ngIf="showLabels">
        Card Number
        <span class="required" *ngIf="mergedConfig().requireCVC">*</span>
      </label>
      
      <div class="input-wrapper card-number-wrapper">
        <input 
          id="cardNumber"
          type="text"
          formControlName="cardNumber"
          class="form-input card-number-input"
          [placeholder]="mergedConfig().placeholder.cardNumber"
          [attr.aria-label]="getAriaLabel('cardNumber')"
          [attr.aria-invalid]="creditCardForm.get('cardNumber')?.invalid && creditCardForm.get('cardNumber')?.touched"
          [attr.aria-describedby]="'cardNumber-errors'"
          autocomplete="cc-number"
          inputmode="numeric"
          maxlength="23"
          [value]="formatCardNumber(creditCardForm.get('cardNumber')?.value || '')"
          (input)="creditCardForm.get('cardNumber')?.setValue(sanitizeCardNumber($any($event).target.value))"
          [disabled]="disabled">
        
        <!-- Card Type Indicator -->
        <div class="card-type-indicator" 
             *ngIf="mergedConfig().validation.showCardType && detectedCardType()">
          <span class="card-icon">{{ getCardTypeIcon() }}</span>
          <span class="card-type-text">{{ detectedCardType() | titlecase }}</span>
        </div>
        
        <!-- Real-time Validation Indicator -->
        <div class="validation-indicator">
          <span class="luhn-check" 
                [class.valid]="cardValidation().luhnValid"
                [class.invalid]="!cardValidation().luhnValid && creditCardForm.get('cardNumber')?.value"
                title="Luhn Algorithm Check">
            {{ cardValidation().luhnValid ? '✓' : '✗' }}
          </span>
        </div>
      </div>
      
      <!-- Real-time Card Number Errors -->
      <div class="form-errors" 
           id="cardNumber-errors"
           *ngIf="creditCardForm.get('cardNumber')?.touched && creditCardForm.get('cardNumber')?.errors">
        <div class="error-message" *ngIf="creditCardForm.get('cardNumber')?.errors?.['required']">
          Card number is required
        </div>
        <div class="error-message" *ngIf="creditCardForm.get('cardNumber')?.errors?.['luhn']">
          Invalid card number (Luhn check failed)
        </div>
        <div class="error-message" *ngIf="creditCardForm.get('cardNumber')?.errors?.['length']">
          Invalid card number length
        </div>
        <div class="error-message" *ngIf="creditCardForm.get('cardNumber')?.errors?.['cardType']">
          Card type not accepted
        </div>
      </div>
    </div>

    <!-- Expiry Date and CVC Row -->
    <div class="form-row">
      <!-- Expiry Date Field -->
      <div class="form-group expiry-group">
        <label for="expiryDate" 
               class="form-label"
               *ngIf="showLabels">
          Expiry Date
          <span class="required">*</span>
        </label>
        
        <div class="input-wrapper">
          <input 
            id="expiryDate"
            type="text"
            formControlName="expiryDate"
            class="form-input expiry-input"
            [placeholder]="mergedConfig().placeholder.expiryDate"
            [attr.aria-label]="getAriaLabel('expiryDate')"
            [attr.aria-invalid]="creditCardForm.get('expiryDate')?.invalid && creditCardForm.get('expiryDate')?.touched"
            [attr.aria-describedby]="'expiryDate-errors'"
            autocomplete="cc-exp"
            inputmode="numeric"
            maxlength="5"
            [value]="formatExpiryDate(creditCardForm.get('expiryDate')?.value || '')"
            (input)="creditCardForm.get('expiryDate')?.setValue(formatExpiryDate($any($event).target.value))"
            [disabled]="disabled">
          
          <div class="validation-indicator">
            <span class="expiry-check" 
                  [class.valid]="cardValidation().expiryValid"
                  [class.invalid]="!cardValidation().expiryValid && creditCardForm.get('expiryDate')?.value"
                  title="Expiry Date Validation">
              {{ cardValidation().expiryValid ? '✓' : '✗' }}
            </span>
          </div>
        </div>
        
        <div class="form-errors" 
             id="expiryDate-errors"
             *ngIf="creditCardForm.get('expiryDate')?.touched && creditCardForm.get('expiryDate')?.errors">
          <div class="error-message" *ngIf="creditCardForm.get('expiryDate')?.errors?.['required']">
            Expiry date is required
          </div>
          <div class="error-message" *ngIf="creditCardForm.get('expiryDate')?.errors?.['format']">
            Invalid format (MM/YY)
          </div>
          <div class="error-message" *ngIf="creditCardForm.get('expiryDate')?.errors?.['month']">
            Invalid month
          </div>
          <div class="error-message" *ngIf="creditCardForm.get('expiryDate')?.errors?.['expired']">
            Card has expired
          </div>
        </div>
      </div>

      <!-- CVC Field -->
      <div class="form-group cvc-group">
        <label for="cvc" 
               class="form-label"
               *ngIf="showLabels">
          CVC
          <span class="required" *ngIf="mergedConfig().requireCVC">*</span>
        </label>
        
        <div class="input-wrapper">
          <input 
            id="cvc"
            type="text"
            formControlName="cvc"
            class="form-input cvc-input"
            [placeholder]="mergedConfig().placeholder.cvc"
            [attr.aria-label]="getAriaLabel('cvc')"
            [attr.aria-invalid]="creditCardForm.get('cvc')?.invalid && creditCardForm.get('cvc')?.touched"
            [attr.aria-describedby]="'cvc-errors'"
            autocomplete="cc-csc"
            inputmode="numeric"
            [maxlength]="detectedCardType() === 'amex' ? 4 : 3"
            [disabled]="disabled">
          
          <div class="validation-indicator">
            <span class="cvc-check" 
                  [class.valid]="cardValidation().cvcValid"
                  [class.invalid]="!cardValidation().cvcValid && creditCardForm.get('cvc')?.value"
                  title="CVC Validation">
              {{ cardValidation().cvcValid ? '✓' : '✗' }}
            </span>
          </div>
        </div>
        
        <div class="form-errors" 
             id="cvc-errors"
             *ngIf="creditCardForm.get('cvc')?.touched && creditCardForm.get('cvc')?.errors">
          <div class="error-message" *ngIf="creditCardForm.get('cvc')?.errors?.['required']">
            CVC is required
          </div>
          <div class="error-message" *ngIf="creditCardForm.get('cvc')?.errors?.['format']">
            CVC must be numeric
          </div>
          <div class="error-message" *ngIf="creditCardForm.get('cvc')?.errors?.['length']">
            CVC must be {{ detectedCardType() === 'amex' ? '4' : '3' }} digits
          </div>
        </div>
      </div>
    </div>

    <!-- Cardholder Name Field -->
    <div class="form-group name-group" *ngIf="mergedConfig().requireName">
      <label for="cardName" 
             class="form-label"
             *ngIf="showLabels">
        Cardholder Name
        <span class="required">*</span>
      </label>
      
      <div class="input-wrapper">
        <input 
          id="cardName"
          type="text"
          formControlName="cardName"
          class="form-input name-input"
          [placeholder]="mergedConfig().placeholder.cardName"
          [attr.aria-label]="getAriaLabel('cardName')"
          [attr.aria-invalid]="creditCardForm.get('cardName')?.invalid && creditCardForm.get('cardName')?.touched"
          [attr.aria-describedby]="'cardName-errors'"
          autocomplete="cc-name"
          [disabled]="disabled">
      </div>
      
      <div class="form-errors" 
           id="cardName-errors"
           *ngIf="creditCardForm.get('cardName')?.touched && creditCardForm.get('cardName')?.errors">
        <div class="error-message" *ngIf="creditCardForm.get('cardName')?.errors?.['required']">
          Cardholder name is required
        </div>
        <div class="error-message" *ngIf="creditCardForm.get('cardName')?.errors?.['minLength']">
          Name must be at least 2 characters
        </div>
        <div class="error-message" *ngIf="creditCardForm.get('cardName')?.errors?.['format']">
          Invalid name format
        </div>
      </div>
    </div>

    <!-- ZIP Code Field -->
    <div class="form-group zip-group" *ngIf="mergedConfig().requireZip">
      <label for="zipCode" 
             class="form-label"
             *ngIf="showLabels">
        ZIP Code
        <span class="required">*</span>
      </label>
      
      <div class="input-wrapper">
        <input 
          id="zipCode"
          type="text"
          formControlName="zipCode"
          class="form-input zip-input"
          [placeholder]="mergedConfig().placeholder.zipCode"
          [attr.aria-label]="getAriaLabel('zipCode')"
          [attr.aria-invalid]="creditCardForm.get('zipCode')?.invalid && creditCardForm.get('zipCode')?.touched"
          [attr.aria-describedby]="'zipCode-errors'"
          autocomplete="postal-code"
          inputmode="numeric"
          maxlength="10"
          [disabled]="disabled">
      </div>
      
      <div class="form-errors" 
           id="zipCode-errors"
           *ngIf="creditCardForm.get('zipCode')?.touched && creditCardForm.get('zipCode')?.errors">
        <div class="error-message" *ngIf="creditCardForm.get('zipCode')?.errors?.['required']">
          ZIP code is required
        </div>
        <div class="error-message" *ngIf="creditCardForm.get('zipCode')?.errors?.['format']">
          Invalid ZIP code format
        </div>
      </div>
    </div>

    <!-- Real-time Validation Summary -->
    <div class="validation-summary" 
         *ngIf="mergedConfig().validation.strengthIndicator && cardValidation().cardType">
      <div class="validation-grid">
        <div class="validation-item" 
             [class.valid]="cardValidation().luhnValid"
             [class.invalid]="!cardValidation().luhnValid">
          <span class="validation-icon">{{ cardValidation().luhnValid ? '✓' : '✗' }}</span>
          <span class="validation-text">Luhn Check</span>
        </div>
        <div class="validation-item" 
             [class.valid]="cardValidation().lengthValid"
             [class.invalid]="!cardValidation().lengthValid">
          <span class="validation-icon">{{ cardValidation().lengthValid ? '✓' : '✗' }}</span>
          <span class="validation-text">Length Valid</span>
        </div>
        <div class="validation-item" 
             [class.valid]="cardValidation().cvcValid"
             [class.invalid]="!cardValidation().cvcValid">
          <span class="validation-icon">{{ cardValidation().cvcValid ? '✓' : '✗' }}</span>
          <span class="validation-text">CVC Valid</span>
        </div>
        <div class="validation-item" 
             [class.valid]="cardValidation().expiryValid"
             [class.invalid]="!cardValidation().expiryValid">
          <span class="validation-icon">{{ cardValidation().expiryValid ? '✓' : '✗' }}</span>
          <span class="validation-text">Expiry Valid</span>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="form-actions">
      <button 
        type="submit"
        class="btn btn-submit"
        [disabled]="!creditCardForm.valid || disabled || isSubmitting()"
        [attr.aria-label]="'Submit credit card information'"
        [class.loading]="isSubmitting()">
        <span class="btn-text" *ngIf="!isSubmitting()">
          {{ autoSubmit ? 'Validate Card' : 'Submit Payment' }}
        </span>
        <span class="btn-loading" *ngIf="isSubmitting()">
          <span class="spinner"></span>
          Validating...
        </span>
      </button>
      
      <button 
        type="button"
        class="btn btn-reset"
        (click)="resetForm()"
        [disabled]="disabled"
        [attr.aria-label]="'Reset form'">
        Reset
      </button>
    </div>

    <!-- Security Footer -->
    <div class="security-footer">
      <div class="security-notice">
        <span class="security-icon">🔒</span>
        <span class="security-text">
          Your payment information is encrypted and secure. We never store your credit card details.
        </span>
      </div>
      
      <div class="compliance-badges">
        <span class="badge pci-badge" title="PCI DSS Level 1 Compliant">PCI DSS</span>
        <span class="badge ssl-badge" title="256-bit SSL Encryption">SSL 256-bit</span>
        <span class="badge gdpr-badge" title="GDPR Compliant">GDPR</span>
      </div>
    </div>
  </form>

  <!-- Loading State Overlay -->
  <div class="loading-overlay" *ngIf="isSubmitting()">
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <div class="loading-text">Validating your payment information...</div>
    </div>
  </div>
</div>
