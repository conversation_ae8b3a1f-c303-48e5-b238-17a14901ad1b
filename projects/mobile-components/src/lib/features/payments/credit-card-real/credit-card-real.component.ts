import { Component, Input, Output, EventEmitter, computed, signal, OnInit, OnDestroy, PLATFORM_ID, Inject } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';
import { debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';
import { Subject, timer } from 'rxjs';

// PCI-DSS compliant interfaces
interface CreditCardValidation {
  isValid: boolean;
  cardType: string;
  errors: string[];
  luhnValid: boolean;
  lengthValid: boolean;
  cvcValid: boolean;
  expiryValid: boolean;
}

interface SecurityConfig {
  maxAttempts: number;
  sessionTimeout: number;
  fraudDetection: boolean;
  auditLogging: boolean;
  deviceFingerprinting: boolean;
}

interface CreditCardConfig {
  allowedCardTypes: string[];
  requireCVC: boolean;
  requireName: boolean;
  requireZip: boolean;
  enableRealTimeValidation: boolean;
  enableSecurityFeatures: boolean;
  placeholder: {
    cardNumber: string;
    expiryDate: string;
    cvc: string;
    cardName: string;
    zipCode: string;
  };
  validation: {
    showCardType: boolean;
    showSecurityBadges: boolean;
    realTimeErrors: boolean;
    strengthIndicator: boolean;
  };
}

interface PaymentSecurityEvent {
  type: 'validation' | 'attempt' | 'fraud' | 'session' | 'device';
  timestamp: string;
  details: any;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
}

// Card type patterns for real-time detection
const CARD_PATTERNS = {
  visa: /^4[0-9]{12}(?:[0-9]{3})?$/,
  mastercard: /^5[1-5][0-9]{14}$/,
  amex: /^3[47][0-9]{13}$/,
  discover: /^6(?:011|5[0-9]{2})[0-9]{12}$/,
  dinersclub: /^3[0689][0-9]{11}$/,
  jcb: /^(?:2131|1800|35\d{3})\d{11}$/
};

@Component({
  selector: 'lib-credit-card-real',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './credit-card-real.component.html',
  styleUrl: './credit-card-real.component.css'
})
export class CreditCardRealComponent implements OnInit, OnDestroy {
  // Standard UI inputs for LP-GO builder integration
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() disabled: boolean = false;

  // Credit card specific inputs
  @Input() config: Partial<CreditCardConfig> = {};
  @Input() securityConfig: Partial<SecurityConfig> = {};
  @Input() autoSubmit: boolean = false;
  @Input() showLabels: boolean = true;
  @Input() enableAnimations: boolean = true;
  @Input() darkMode: boolean = false;

  // Security and validation outputs
  @Output() cardValidated = new EventEmitter<CreditCardValidation>();
  @Output() securityEvent = new EventEmitter<PaymentSecurityEvent>();
  @Output() paymentReady = new EventEmitter<boolean>();
  @Output() cardTypeDetected = new EventEmitter<string>();
  @Output() fraudAlert = new EventEmitter<any>();

  // Component state using Angular signals
  private destroy$ = new Subject<void>();
  
  creditCardForm!: FormGroup;
  isSubmitting = signal(false);
  sessionTimeLeft = signal(0);
  validationErrors = signal<string[]>([]);
  detectedCardType = signal<string>('');
  securityStatus = signal<'secure' | 'warning' | 'danger'>('secure');
  
  // Real-time validation state
  cardValidation = signal<CreditCardValidation>({
    isValid: false,
    cardType: '',
    errors: [],
    luhnValid: false,
    lengthValid: false,
    cvcValid: false,
    expiryValid: false
  });

  // Security tracking
  private validationAttempts = 0;
  private sessionStartTime = Date.now();
  private fraudScore = 0;

  // Default configurations
  private defaultConfig: CreditCardConfig = {
    allowedCardTypes: ['visa', 'mastercard', 'amex', 'discover'],
    requireCVC: true,
    requireName: true,
    requireZip: false,
    enableRealTimeValidation: true,
    enableSecurityFeatures: true,
    placeholder: {
      cardNumber: '1234 5678 9012 3456',
      expiryDate: 'MM/YY',
      cvc: '123',
      cardName: 'John Doe',
      zipCode: '12345'
    },
    validation: {
      showCardType: true,
      showSecurityBadges: true,
      realTimeErrors: true,
      strengthIndicator: true
    }
  };

  private defaultSecurityConfig: SecurityConfig = {
    maxAttempts: 5,
    sessionTimeout: 900000, // 15 minutes
    fraudDetection: true,
    auditLogging: true,
    deviceFingerprinting: true
  };

  // Computed properties for dynamic styling
  containerClasses = computed(() => {
    const base = 'credit-card-real-container';
    const sizeClass = `size-${this.size}`;
    const variantClass = `variant-${this.variant}`;
    const roundedClass = `rounded-${this.rounded}`;
    const disabledClass = this.disabled ? 'disabled' : '';
    const darkModeClass = this.darkMode ? 'dark' : '';
    const securityClass = `security-${this.securityStatus()}`;

    return [
      base,
      sizeClass,
      variantClass,
      roundedClass,
      disabledClass,
      darkModeClass,
      securityClass,
      this.className
    ].filter(Boolean).join(' ');
  });

  // Merged configurations
  mergedConfig = computed(() => ({
    ...this.defaultConfig,
    ...this.config
  }));

  mergedSecurityConfig = computed(() => ({
    ...this.defaultSecurityConfig,
    ...this.securityConfig
  }));

  constructor(
    private fb: FormBuilder,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.setupRealTimeValidation();
    this.startSessionTimer();
    this.emitSecurityEvent('session', { action: 'started' }, 'low');
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.emitSecurityEvent('session', { action: 'ended', duration: Date.now() - this.sessionStartTime }, 'low');
  }

  private initializeForm(): void {
    this.creditCardForm = this.fb.group({
      cardNumber: ['', [Validators.required, this.cardNumberValidator.bind(this)]],
      expiryDate: ['', [Validators.required, this.expiryDateValidator.bind(this)]],
      cvc: ['', [Validators.required, this.cvcValidator.bind(this)]],
      cardName: ['', this.mergedConfig().requireName ? [Validators.required, this.nameValidator.bind(this)] : []],
      zipCode: ['', this.mergedConfig().requireZip ? [Validators.required, this.zipValidator.bind(this)] : []]
    });
  }

  private setupRealTimeValidation(): void {
    if (!this.mergedConfig().enableRealTimeValidation) return;

    // Real-time card number validation with debouncing
    this.creditCardForm.get('cardNumber')?.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(value => {
        this.validateCardNumberRealTime(value);
      });

    // Form-wide validation
    this.creditCardForm.valueChanges
      .pipe(
        debounceTime(500),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        this.performRealTimeValidation();
      });
  }

  private validateCardNumberRealTime(cardNumber: string): void {
    if (!cardNumber) {
      this.detectedCardType.set('');
      this.cardTypeDetected.emit('');
      return;
    }

    const sanitized = this.sanitizeCardNumber(cardNumber);
    const cardType = this.detectCardType(sanitized);
    
    this.detectedCardType.set(cardType);
    this.cardTypeDetected.emit(cardType);

    // Fraud detection
    this.updateFraudScore(cardNumber);
    
    this.emitSecurityEvent('validation', { 
      cardType, 
      partial: sanitized.substring(0, 4) + '****',
      fraudScore: this.fraudScore 
    }, this.fraudScore > 50 ? 'high' : 'low');
  }

  private performRealTimeValidation(): void {
    this.validationAttempts++;
    
    const validation: CreditCardValidation = {
      isValid: this.creditCardForm.valid,
      cardType: this.detectedCardType(),
      errors: this.getFormErrors(),
      luhnValid: this.isLuhnValid(this.creditCardForm.get('cardNumber')?.value || ''),
      lengthValid: this.isCardLengthValid(this.creditCardForm.get('cardNumber')?.value || ''),
      cvcValid: this.creditCardForm.get('cvc')?.valid || false,
      expiryValid: this.creditCardForm.get('expiryDate')?.valid || false
    };

    this.cardValidation.set(validation);
    this.cardValidated.emit(validation);
    this.paymentReady.emit(validation.isValid);

    // Update security status based on validation
    this.updateSecurityStatus(validation);
  }

  // Luhn algorithm for card validation
  private isLuhnValid(cardNumber: string): boolean {
    const sanitized = this.sanitizeCardNumber(cardNumber);
    if (sanitized.length < 13 || sanitized.length > 19) return false;

    let sum = 0;
    let alternate = false;

    for (let i = sanitized.length - 1; i >= 0; i--) {
      let digit = parseInt(sanitized.charAt(i), 10);

      if (alternate) {
        digit *= 2;
        if (digit > 9) {
          digit = (digit % 10) + 1;
        }
      }

      sum += digit;
      alternate = !alternate;
    }

    return (sum % 10) === 0;
  }

  private detectCardType(cardNumber: string): string {
    for (const [type, pattern] of Object.entries(CARD_PATTERNS)) {
      if (pattern.test(cardNumber)) {
        return type;
      }
    }
    return '';
  }

  private isCardLengthValid(cardNumber: string): boolean {
    const sanitized = this.sanitizeCardNumber(cardNumber);
    const cardType = this.detectCardType(sanitized);
    
    switch (cardType) {
      case 'amex': return sanitized.length === 15;
      case 'dinersclub': return sanitized.length === 14;
      default: return sanitized.length === 16;
    }
  }

  // Custom validators
  private cardNumberValidator(control: AbstractControl): ValidationErrors | null {
    const value = control.value;
    if (!value) return null;

    const sanitized = this.sanitizeCardNumber(value);
    const errors: ValidationErrors = {};

    if (!this.isLuhnValid(sanitized)) {
      errors['luhn'] = true;
    }

    if (!this.isCardLengthValid(sanitized)) {
      errors['length'] = true;
    }

    const cardType = this.detectCardType(sanitized);
    if (cardType && !this.mergedConfig().allowedCardTypes.includes(cardType)) {
      errors['cardType'] = true;
    }

    return Object.keys(errors).length > 0 ? errors : null;
  }

  private expiryDateValidator(control: AbstractControl): ValidationErrors | null {
    const value = control.value;
    if (!value) return null;

    const match = value.match(/^(\d{2})\/(\d{2})$/);
    if (!match) return { 'format': true };

    const month = parseInt(match[1], 10);
    const year = parseInt('20' + match[2], 10);
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;

    if (month < 1 || month > 12) return { 'month': true };
    if (year < currentYear || (year === currentYear && month < currentMonth)) {
      return { 'expired': true };
    }

    return null;
  }

  private cvcValidator(control: AbstractControl): ValidationErrors | null {
    const value = control.value;
    if (!value) return null;

    const cardType = this.detectedCardType();
    const expectedLength = cardType === 'amex' ? 4 : 3;
    
    if (!/^\d+$/.test(value)) return { 'format': true };
    if (value.length !== expectedLength) return { 'length': true };

    return null;
  }

  private nameValidator(control: AbstractControl): ValidationErrors | null {
    const value = control.value;
    if (!value) return null;

    if (value.length < 2) return { 'minLength': true };
    if (!/^[a-zA-Z\s\-\.\']+$/.test(value)) return { 'format': true };

    return null;
  }

  private zipValidator(control: AbstractControl): ValidationErrors | null {
    const value = control.value;
    if (!value) return null;

    if (!/^\d{5}(-\d{4})?$/.test(value)) return { 'format': true };

    return null;
  }

  private getFormErrors(): string[] {
    const errors: string[] = [];
    
    Object.keys(this.creditCardForm.controls).forEach(key => {
      const control = this.creditCardForm.get(key);
      if (control?.errors) {
        Object.keys(control.errors).forEach(errorKey => {
          errors.push(`${key}.${errorKey}`);
        });
      }
    });

    return errors;
  }

  // Security and fraud detection
  private updateFraudScore(cardNumber: string): void {
    // Basic fraud detection patterns
    const sanitized = this.sanitizeCardNumber(cardNumber);
    
    // Check for sequential numbers
    if (/123456|654321|111111|000000/.test(sanitized)) {
      this.fraudScore += 30;
    }

    // Check validation attempt frequency
    if (this.validationAttempts > this.mergedSecurityConfig().maxAttempts) {
      this.fraudScore += 50;
    }

    // Emit fraud alert if score is high
    if (this.fraudScore > 70) {
      this.fraudAlert.emit({
        score: this.fraudScore,
        triggers: ['high_score', 'multiple_attempts'],
        timestamp: new Date().toISOString()
      });
    }
  }

  private updateSecurityStatus(validation: CreditCardValidation): void {
    if (this.fraudScore > 70) {
      this.securityStatus.set('danger');
    } else if (this.fraudScore > 30 || validation.errors.length > 0) {
      this.securityStatus.set('warning');
    } else {
      this.securityStatus.set('secure');
    }
  }

  private startSessionTimer(): void {
    if (!isPlatformBrowser(this.platformId)) return;

    const timeout = this.mergedSecurityConfig().sessionTimeout;
    
    timer(0, 1000)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        const elapsed = Date.now() - this.sessionStartTime;
        const remaining = Math.max(0, timeout - elapsed);
        this.sessionTimeLeft.set(Math.floor(remaining / 1000));

        if (remaining <= 0) {
          this.handleSessionTimeout();
        }
      });
  }

  private handleSessionTimeout(): void {
    this.disabled = true;
    this.creditCardForm.disable();
    this.emitSecurityEvent('session', { action: 'timeout' }, 'high');
  }

  private emitSecurityEvent(type: PaymentSecurityEvent['type'], details: any, riskLevel: PaymentSecurityEvent['riskLevel']): void {
    if (!this.mergedSecurityConfig().auditLogging) return;

    const event: PaymentSecurityEvent = {
      type,
      timestamp: new Date().toISOString(),
      details,
      riskLevel
    };

    this.securityEvent.emit(event);
  }

  // Public Math reference for template
  Math = Math;

  // Template helper methods (made public for template access)
  sanitizeCardNumber(cardNumber: string): string {
    return cardNumber.replace(/\D/g, '');
  }

  formatCardNumber(value: string): string {
    const sanitized = this.sanitizeCardNumber(value);
    const cardType = this.detectCardType(sanitized);
    
    if (cardType === 'amex') {
      return sanitized.replace(/(\d{4})(\d{6})(\d{5})/, '$1 $2 $3');
    } else {
      return sanitized.replace(/(\d{4})(?=\d)/g, '$1 ');
    }
  }

  formatExpiryDate(value: string): string {
    const sanitized = value.replace(/\D/g, '');
    if (sanitized.length >= 2) {
      return sanitized.substring(0, 2) + '/' + sanitized.substring(2, 4);
    }
    return sanitized;
  }

  getCardTypeIcon(): string {
    switch (this.detectedCardType()) {
      case 'visa': return '💳';
      case 'mastercard': return '💳';
      case 'amex': return '💳';
      case 'discover': return '💳';
      default: return '';
    }
  }

  getSecurityIcon(): string {
    switch (this.securityStatus()) {
      case 'secure': return '🔒';
      case 'warning': return '⚠️';
      case 'danger': return '🚨';
      default: return '';
    }
  }

  // Public methods for form interaction
  submitForm(): void {
    if (this.creditCardForm.valid && !this.disabled) {
      this.isSubmitting.set(true);
      this.performRealTimeValidation();
      
      // Note: In real implementation, never log or store actual card data
      this.emitSecurityEvent('attempt', { 
        action: 'submit',
        cardType: this.detectedCardType(),
        valid: this.creditCardForm.valid 
      }, 'medium');
    }
  }

  resetForm(): void {
    this.creditCardForm.reset();
    this.fraudScore = 0;
    this.validationAttempts = 0;
    this.isSubmitting.set(false);
    this.detectedCardType.set('');
    this.securityStatus.set('secure');
  }

  // Accessibility helpers
  getAriaLabel(field: string): string {
    const validation = this.cardValidation();
    const hasError = validation.errors.some(error => error.startsWith(field));
    return hasError ? `${field} field has validation errors` : `${field} field`;
  }
}
