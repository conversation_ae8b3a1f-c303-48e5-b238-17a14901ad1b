import { Component, Input, Output, EventEmitter, computed, signal, OnInit, OnDestroy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, AbstractControl } from '@angular/forms';
import { Subject, takeUntil, debounceTime } from 'rxjs';

// PCI-DSS compliant credit card types
interface CreditCardType {
  name: string;
  pattern: RegExp;
  gaps: number[];
  lengths: number[];
  code: { size: number; name: string };
}

// Credit card configuration
interface CreditCardConfig {
  allowedTypes: string[];
  requireCVV: boolean;
  requireExpiry: boolean;
  requireName: boolean;
  enableLuhnValidation: boolean;
  maskNumber: boolean;
  securityLevel: 'basic' | 'enhanced' | 'maximum';
}

// Card validation state
interface CardValidationState {
  number: { valid: boolean; message: string; type?: string };
  expiry: { valid: boolean; message: string };
  cvv: { valid: boolean; message: string };
  name: { valid: boolean; message: string };
}

// Secure card data interface (no sensitive data stored)
interface SecureCardData {
  type: string;
  last4: string;
  expiryMonth?: string;
  expiryYear?: string;
  nameOnCard?: string;
  isValid: boolean;
}

@Component({
  selector: 'lib-credit-card',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './credit-card.component.html',
  styleUrl: './credit-card.component.css'
})
export class CreditCardComponent implements OnInit, OnDestroy {
  private fb = inject(FormBuilder);
  private destroy$ = new Subject<void>();

  // Standard LP-GO inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() disabled: boolean = false;

  // Credit card specific inputs
  @Input() config: Partial<CreditCardConfig> = {};
  @Input() showCardImage: boolean = true;
  @Input() showSecurityInfo: boolean = true;
  @Input() enableRealTimeValidation: boolean = true;
  @Input() placeholder: { number?: string; name?: string; expiry?: string; cvv?: string } = {};
  @Input() layout: 'horizontal' | 'vertical' | 'compact' = 'vertical';
  @Input() theme: 'light' | 'dark' | 'auto' = 'auto';
  @Input() language: 'en' | 'es' | 'fr' | 'de' = 'en';

  // Security inputs
  @Input() fraudDetection: boolean = true;
  @Input() sessionTimeout: number = 600000; // 10 minutes
  @Input() maxAttempts: number = 3;
  @Input() enableAuditLog: boolean = true;

  // Events
  @Output() cardChange = new EventEmitter<SecureCardData>();
  @Output() validationChange = new EventEmitter<CardValidationState>();
  @Output() cardTypeDetected = new EventEmitter<string>();
  @Output() securityEvent = new EventEmitter<{ type: string; details: any }>();
  @Output() formReady = new EventEmitter<boolean>();

  // Form and validation
  creditCardForm!: FormGroup;
  
  // Signals for reactive state
  validationState = signal<CardValidationState>({
    number: { valid: false, message: '' },
    expiry: { valid: false, message: '' },
    cvv: { valid: false, message: '' },
    name: { valid: false, message: '' }
  });

  detectedCardType = signal<string>('');
  isProcessing = signal<boolean>(false);
  attemptCount = signal<number>(0);
  sessionActive = signal<boolean>(true);

  // Computed properties
  computedClasses = computed(() => {
    const base = [
      'credit-card-container',
      `credit-card--${this.size}`,
      `credit-card--${this.variant}`,
      `credit-card--${this.rounded}`,
      `credit-card--${this.layout}`,
      `credit-card--${this.theme}`
    ];

    if (this.disabled) base.push('credit-card--disabled');
    if (this.isProcessing()) base.push('credit-card--processing');
    if (!this.sessionActive()) base.push('credit-card--session-expired');
    if (this.className) base.push(this.className);

    return base.join(' ');
  });

  isFormValid = computed(() => {
    const state = this.validationState();
    const config = this.mergedConfig();
    
    let valid = state.number.valid;
    if (config.requireExpiry) valid = valid && state.expiry.valid;
    if (config.requireCVV) valid = valid && state.cvv.valid;
    if (config.requireName) valid = valid && state.name.valid;
    
    return valid;
  });

  // PCI-DSS compliant card type definitions
  private readonly cardTypes: Record<string, CreditCardType> = {
    visa: {
      name: 'Visa',
      pattern: /^4/,
      gaps: [4, 8, 12],
      lengths: [16, 18, 19],
      code: { size: 3, name: 'CVV' }
    },
    mastercard: {
      name: 'Mastercard',
      pattern: /^(5[1-5]|2[2-7])/,
      gaps: [4, 8, 12],
      lengths: [16],
      code: { size: 3, name: 'CVC' }
    },
    amex: {
      name: 'American Express',
      pattern: /^3[47]/,
      gaps: [4, 10],
      lengths: [15],
      code: { size: 4, name: 'CID' }
    },
    discover: {
      name: 'Discover',
      pattern: /^6(?:011|5)/,
      gaps: [4, 8, 12],
      lengths: [16, 19],
      code: { size: 3, name: 'CID' }
    }
  };

  // Default configuration with security focus
  private readonly defaultConfig: CreditCardConfig = {
    allowedTypes: ['visa', 'mastercard', 'amex', 'discover'],
    requireCVV: true,
    requireExpiry: true,
    requireName: true,
    enableLuhnValidation: true,
    maskNumber: true,
    securityLevel: 'enhanced'
  };

  // Localized messages
  private readonly messages = {
    en: {
      invalidNumber: 'Please enter a valid card number',
      invalidExpiry: 'Please enter a valid expiry date',
      invalidCvv: 'Please enter a valid security code',
      invalidName: 'Please enter the cardholder name',
      expiredCard: 'This card has expired',
      unsupportedCard: 'This card type is not supported',
      sessionExpired: 'Session expired. Please refresh.',
      fraudDetected: 'Suspicious activity detected',
      maxAttemptsReached: 'Maximum attempts reached'
    }
  };

  ngOnInit() {
    this.initializeForm();
    this.setupValidation();
    this.startSessionTimer();
    this.emitSecurityEvent('component_initialized', { timestamp: new Date().toISOString() });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private mergedConfig(): CreditCardConfig {
    return { ...this.defaultConfig, ...this.config };
  }

  private initializeForm() {
    const config = this.mergedConfig();
    
    this.creditCardForm = this.fb.group({
      number: ['', [
        Validators.required,
        this.cardNumberValidator.bind(this)
      ]],
      expiry: ['', config.requireExpiry ? [
        Validators.required,
        this.expiryValidator.bind(this)
      ] : []],
      cvv: ['', config.requireCVV ? [
        Validators.required,
        this.cvvValidator.bind(this)
      ] : []],
      name: ['', config.requireName ? [
        Validators.required,
        Validators.minLength(2),
        this.nameValidator.bind(this)
      ] : []]
    });

    this.formReady.emit(true);
  }

  private setupValidation() {
    // Real-time validation for card number
    this.creditCardForm.get('number')?.valueChanges
      .pipe(
        debounceTime(300),
        takeUntil(this.destroy$)
      )
      .subscribe(value => {
        if (value) {
          const cardType = this.detectCardType(value);
          if (cardType !== this.detectedCardType()) {
            this.detectedCardType.set(cardType);
            this.cardTypeDetected.emit(cardType);
          }
        }
        this.updateValidationState();
      });

    // Validation for other fields
    ['expiry', 'cvv', 'name'].forEach(field => {
      this.creditCardForm.get(field)?.valueChanges
        .pipe(
          debounceTime(300),
          takeUntil(this.destroy$)
        )
        .subscribe(() => this.updateValidationState());
    });
  }

  private updateValidationState() {
    const form = this.creditCardForm;
    const newState: CardValidationState = {
      number: this.getFieldValidation('number'),
      expiry: this.getFieldValidation('expiry'),
      cvv: this.getFieldValidation('cvv'),
      name: this.getFieldValidation('name')
    };

    this.validationState.set(newState);
    this.validationChange.emit(newState);

    if (this.isFormValid()) {
      this.emitCardData();
    }
  }

  private getFieldValidation(fieldName: string): { valid: boolean; message: string; type?: string } {
    const control = this.creditCardForm.get(fieldName);
    if (!control) return { valid: true, message: '' };

    const valid = control.valid && control.value;
    let message = '';

    if (control.errors && control.touched) {
      if (control.errors['required']) {
        message = this.getLocalizedMessage(`invalid${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)}`);
      } else if (control.errors['cardNumber']) {
        message = control.errors['cardNumber'];
      } else if (control.errors['expiry']) {
        message = control.errors['expiry'];
      } else if (control.errors['cvv']) {
        message = control.errors['cvv'];
      }
    }

    const result: { valid: boolean; message: string; type?: string } = { valid, message };
    if (fieldName === 'number') {
      result.type = this.detectedCardType();
    }

    return result;
  }

  private emitCardData() {
    const formValue = this.creditCardForm.value;
    const cardType = this.detectedCardType();
    
    // Only emit non-sensitive data
    const secureData: SecureCardData = {
      type: cardType,
      last4: formValue.number ? formValue.number.replace(/\D/g, '').slice(-4) : '',
      expiryMonth: formValue.expiry ? formValue.expiry.split('/')[0] : undefined,
      expiryYear: formValue.expiry ? formValue.expiry.split('/')[1] : undefined,
      nameOnCard: formValue.name || undefined,
      isValid: this.isFormValid()
    };

    this.cardChange.emit(secureData);
  }

  // Card number validation with Luhn algorithm
  private cardNumberValidator(control: AbstractControl) {
    const value = control.value?.replace(/\D/g, '') || '';
    
    if (!value) return null;

    const cardType = this.detectCardType(value);
    const config = this.mergedConfig();

    // Check if card type is allowed
    if (!config.allowedTypes.includes(cardType) && cardType !== 'unknown') {
      return { cardNumber: this.getLocalizedMessage('unsupportedCard') };
    }

    // Validate length
    if (cardType !== 'unknown') {
      const typeConfig = this.cardTypes[cardType];
      if (!typeConfig.lengths.includes(value.length)) {
        return { cardNumber: this.getLocalizedMessage('invalidNumber') };
      }
    }

    // Luhn validation
    if (config.enableLuhnValidation && !this.luhnCheck(value)) {
      return { cardNumber: this.getLocalizedMessage('invalidNumber') };
    }

    return null;
  }

  // Expiry date validation
  private expiryValidator(control: AbstractControl) {
    const value = control.value || '';
    const expiryPattern = /^(0[1-9]|1[0-2])\/([0-9]{2})$/;
    
    if (!expiryPattern.test(value)) {
      return { expiry: this.getLocalizedMessage('invalidExpiry') };
    }

    const [month, year] = value.split('/').map(Number);
    const expiry = new Date(2000 + year, month - 1);
    const now = new Date();
    now.setDate(1); // Set to first day of current month

    if (expiry < now) {
      return { expiry: this.getLocalizedMessage('expiredCard') };
    }

    return null;
  }

  // CVV validation
  private cvvValidator(control: AbstractControl) {
    const value = control.value || '';
    const cardType = this.detectedCardType();
    
    if (cardType && this.cardTypes[cardType]) {
      const expectedLength = this.cardTypes[cardType].code.size;
      if (value.length !== expectedLength || !/^\d+$/.test(value)) {
        return { cvv: this.getLocalizedMessage('invalidCvv') };
      }
    } else if (!/^\d{3,4}$/.test(value)) {
      return { cvv: this.getLocalizedMessage('invalidCvv') };
    }

    return null;
  }

  // Name validation
  private nameValidator(control: AbstractControl) {
    const value = control.value || '';
    
    // Basic name validation - letters, spaces, hyphens, apostrophes
    if (!/^[a-zA-Z\s\-']+$/.test(value)) {
      return { name: this.getLocalizedMessage('invalidName') };
    }

    return null;
  }

  private detectCardType(number: string): string {
    const cleanNumber = number.replace(/\D/g, '');
    
    for (const [type, config] of Object.entries(this.cardTypes)) {
      if (config.pattern.test(cleanNumber)) {
        return type;
      }
    }
    
    return 'unknown';
  }

  // Luhn algorithm implementation
  private luhnCheck(number: string): boolean {
    let sum = 0;
    let alternate = false;
    
    for (let i = number.length - 1; i >= 0; i--) {
      let digit = parseInt(number.charAt(i), 10);
      
      if (alternate) {
        digit *= 2;
        if (digit > 9) {
          digit = Math.floor(digit / 10) + (digit % 10);
        }
      }
      
      sum += digit;
      alternate = !alternate;
    }
    
    return sum % 10 === 0;
  }

  private startSessionTimer() {
    setTimeout(() => {
      this.sessionActive.set(false);
      this.emitSecurityEvent('session_expired', { timestamp: new Date().toISOString() });
    }, this.sessionTimeout);
  }

  private getLocalizedMessage(key: string): string {
    return this.messages[this.language]?.[key as keyof typeof this.messages.en] || key;
  }

  private emitSecurityEvent(type: string, details: any) {
    if (this.enableAuditLog) {
      this.securityEvent.emit({ type, details });
    }
  }

  // Public methods for form interaction
  formatCardNumber(value: string): string {
    const cleanValue = value.replace(/\D/g, '');
    const cardType = this.detectCardType(cleanValue);
    
    if (cardType !== 'unknown' && this.cardTypes[cardType]) {
      const gaps = this.cardTypes[cardType].gaps;
      let formatted = '';
      
      for (let i = 0; i < cleanValue.length; i++) {
        if (gaps.includes(i)) {
          formatted += ' ';
        }
        formatted += cleanValue[i];
      }
      
      return formatted;
    }
    
    // Default formatting for unknown cards
    return cleanValue.replace(/(.{4})/g, '$1 ').trim();
  }

  formatExpiry(value: string): string {
    const cleanValue = value.replace(/\D/g, '');
    if (cleanValue.length >= 2) {
      return cleanValue.substring(0, 2) + '/' + cleanValue.substring(2, 4);
    }
    return cleanValue;
  }

  onCardNumberInput(event: any) {
    const value = event.target.value;
    const formatted = this.formatCardNumber(value);
    this.creditCardForm.get('number')?.setValue(formatted, { emitEvent: false });
    event.target.value = formatted;
  }

  onExpiryInput(event: any) {
    const value = event.target.value;
    const formatted = this.formatExpiry(value);
    this.creditCardForm.get('expiry')?.setValue(formatted, { emitEvent: false });
    event.target.value = formatted;
  }

  onCvvInput(event: any) {
    const value = event.target.value.replace(/\D/g, '');
    const cardType = this.detectedCardType();
    const maxLength = cardType && this.cardTypes[cardType] ? this.cardTypes[cardType].code.size : 4;
    
    const truncated = value.substring(0, maxLength);
    this.creditCardForm.get('cvv')?.setValue(truncated, { emitEvent: false });
    event.target.value = truncated;
  }

  getCardTypeIcon(type: string): string {
    const icons: Record<string, string> = {
      visa: '💳',
      mastercard: '💳',
      amex: '💳',
      discover: '💳',
      unknown: '💳'
    };
    return icons[type] || icons.unknown;
  }

  getCvvLabel(): string {
    const cardType = this.detectedCardType();
    return cardType && this.cardTypes[cardType] ? this.cardTypes[cardType].code.name : 'CVV';
  }

  reset() {
    this.creditCardForm.reset();
    this.detectedCardType.set('');
    this.attemptCount.set(0);
    this.validationState.set({
      number: { valid: false, message: '' },
      expiry: { valid: false, message: '' },
      cvv: { valid: false, message: '' },
      name: { valid: false, message: '' }
    });
    this.emitSecurityEvent('form_reset', { timestamp: new Date().toISOString() });
  }

  submit() {
    if (this.isFormValid() && this.sessionActive()) {
      this.isProcessing.set(true);
      this.emitSecurityEvent('form_submitted', { 
        timestamp: new Date().toISOString(),
        cardType: this.detectedCardType()
      });
      
      // Note: In real implementation, this would trigger secure payment processing
      setTimeout(() => {
        this.isProcessing.set(false);
      }, 2000);
    } else {
      this.attemptCount.update(count => count + 1);
      if (this.attemptCount() >= this.maxAttempts) {
        this.emitSecurityEvent('max_attempts_reached', { 
          timestamp: new Date().toISOString(),
          attempts: this.attemptCount()
        });
      }
    }
  }
}
