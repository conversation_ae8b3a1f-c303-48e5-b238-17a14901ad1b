/* Credit Card Component Styles */
/* PCI-DSS compliant styling with security-focused UX */

.credit-card-container {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

/* Size Variants */
.credit-card--xs { max-width: 300px; font-size: 0.875rem; }
.credit-card--sm { max-width: 400px; font-size: 0.9375rem; }
.credit-card--md { max-width: 500px; font-size: 1rem; }
.credit-card--lg { max-width: 600px; font-size: 1.125rem; }
.credit-card--xl { max-width: 700px; font-size: 1.25rem; }

/* Color Variants */
.credit-card--default { border: 1px solid #e5e7eb; }
.credit-card--primary { border: 2px solid #3b82f6; }
.credit-card--secondary { border: 2px solid #6b7280; }
.credit-card--success { border: 2px solid #10b981; }
.credit-card--warning { border: 2px solid #f59e0b; }
.credit-card--danger { border: 2px solid #ef4444; }

/* Rounded Variants */
.credit-card--rounded-none { border-radius: 0; }
.credit-card--rounded-sm { border-radius: 4px; }
.credit-card--rounded-md { border-radius: 8px; }
.credit-card--rounded-lg { border-radius: 12px; }
.credit-card--rounded-full { border-radius: 24px; }

/* Layout Variants */
.credit-card--horizontal .credit-card__row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.credit-card--compact {
  max-width: 350px;
}

.credit-card--compact .credit-card__field-group {
  margin-bottom: 0.75rem;
}

/* Theme Variants */
.credit-card--dark {
  background: #1f2937;
  color: #f9fafb;
  border-color: #374151;
}

.credit-card--dark .credit-card__input {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.credit-card--dark .credit-card__input::placeholder {
  color: #9ca3af;
}

/* Session Warning */
.credit-card__session-warning {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border: 2px solid #fca5a5;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  animation: pulse-warning 2s infinite;
}

.warning-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.warning-content h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #991b1b;
}

.warning-content p {
  margin: 0;
  font-size: 0.875rem;
  color: #b91c1c;
}

/* Security Info Panel */
.credit-card__security-info {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

.security-badges {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.security-badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.security-badge--pci {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.security-badge--ssl {
  background: #dbeafe;
  color: #1e40af;
  border: 1px solid #93c5fd;
}

.security-badge--fraud {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fcd34d;
}

.security-message {
  margin: 0;
  font-size: 0.875rem;
  color: #0369a1;
}

/* Form Styles */
.credit-card__form {
  padding: 1.5rem;
  transition: opacity 0.3s ease;
}

.credit-card__form--disabled {
  opacity: 0.6;
  pointer-events: none;
}

.credit-card__field-group {
  margin-bottom: 1.25rem;
  position: relative;
}

.credit-card__field-group--half {
  flex: 1;
}

.credit-card__row {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.credit-card__label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.credit-card__label--required {
  font-weight: 600;
}

.required-indicator {
  color: #ef4444;
  margin-left: 0.25rem;
}

/* Input Styles */
.credit-card__input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.credit-card__input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  transition: all 0.2s ease;
  background: #ffffff;
  color: #111827;
}

.credit-card__input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.credit-card__input--error {
  border-color: #ef4444;
  background: #fef2f2;
}

.credit-card__input--error:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.credit-card__input--valid {
  border-color: #10b981;
  background: #f0fdf4;
}

.credit-card__input--valid:focus {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.credit-card__input::placeholder {
  color: #9ca3af;
  font-style: italic;
}

/* Specific Input Types */
.credit-card__input--number {
  font-size: 1.125rem;
  letter-spacing: 0.05em;
  padding-right: 3rem;
}

.credit-card__input--expiry {
  text-align: center;
  font-size: 1.125rem;
  letter-spacing: 0.1em;
}

.credit-card__input--cvv {
  text-align: center;
  font-size: 1.125rem;
  letter-spacing: 0.2em;
  padding-right: 2.5rem;
}

.credit-card__input--name {
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Card Type Icon */
.credit-card__card-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.9);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.card-icon {
  font-size: 1.25rem;
}

.card-type-name {
  font-size: 0.75rem;
  font-weight: 600;
  color: #6b7280;
}

/* Help Icon */
.credit-card__help-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.credit-card__help-icon:hover {
  background: #d1d5db;
  transform: translateY(-50%) scale(1.1);
}

.help-icon {
  font-size: 0.75rem;
  font-weight: 600;
  color: #6b7280;
}

.credit-card__help-text {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
  font-style: italic;
}

/* Error Messages */
.credit-card__error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 4px;
  color: #991b1b;
  font-size: 0.875rem;
  animation: shake 0.3s ease-in-out;
}

.error-icon {
  flex-shrink: 0;
  font-size: 1rem;
}

/* Action Buttons */
.credit-card__actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  justify-content: flex-end;
}

.credit-card__button {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.credit-card__button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.credit-card__button--primary {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: #ffffff;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.credit-card__button--primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.credit-card__button--secondary {
  background: #ffffff;
  color: #6b7280;
  border-color: #d1d5db;
}

.credit-card__button--secondary:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

/* Loading State */
.loading-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Attempt Warning */
.credit-card__attempt-warning {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  padding: 0.75rem;
  background: #fef3c7;
  border: 1px solid #fcd34d;
  border-radius: 6px;
  color: #92400e;
  font-size: 0.875rem;
  font-weight: 500;
}

.credit-card__max-attempts {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  padding: 0.75rem;
  background: #fef2f2;
  border: 2px solid #fca5a5;
  border-radius: 6px;
  color: #991b1b;
  font-size: 0.875rem;
  font-weight: 600;
}

/* Status Indicator */
.credit-card__status {
  margin-top: 1rem;
  padding: 0.75rem;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  background: #f9fafb;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-indicator--valid {
  color: #059669;
}

.status-indicator--invalid {
  color: #dc2626;
}

.status-icon {
  font-size: 1.125rem;
}

.status-text {
  font-size: 0.875rem;
  font-weight: 500;
}

/* Security Footer */
.credit-card__security-footer {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-top: 1px solid #e2e8f0;
  padding: 1.5rem;
  text-align: center;
}

.security-features {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.security-feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #475569;
}

.feature-icon {
  font-size: 1rem;
}

.security-disclaimer {
  margin: 0;
  font-size: 0.75rem;
  color: #64748b;
  line-height: 1.5;
  max-width: 400px;
  margin: 0 auto;
}

/* State Modifiers */
.credit-card--disabled {
  opacity: 0.7;
  pointer-events: none;
}

.credit-card--processing {
  position: relative;
}

.credit-card--processing::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  animation: processing 2s infinite;
  z-index: 1;
}

.credit-card--session-expired {
  filter: grayscale(1);
}

/* Animations */
@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

@keyframes pulse-warning {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

@keyframes processing {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Responsive Design */
@media (max-width: 640px) {
  .credit-card-container {
    border-radius: 0;
    box-shadow: none;
    border-left: none;
    border-right: none;
  }

  .credit-card__form {
    padding: 1rem;
  }

  .credit-card__row {
    flex-direction: column;
    gap: 0.75rem;
  }

  .credit-card__actions {
    flex-direction: column;
  }

  .credit-card__button {
    width: 100%;
    padding: 1rem;
  }

  .security-features {
    flex-direction: column;
    gap: 0.75rem;
  }
}

@media (max-width: 480px) {
  .credit-card__input--number {
    font-size: 1rem;
  }

  .credit-card__card-icon {
    right: 0.5rem;
  }

  .card-type-name {
    display: none;
  }
}

/* Focus Management */
.credit-card__input:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.credit-card__button:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .credit-card__input {
    border-width: 3px;
  }

  .credit-card__button {
    border-width: 3px;
  }

  .security-badge {
    border-width: 2px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .credit-card__input,
  .credit-card__button,
  .credit-card__help-icon {
    transition: none;
  }

  .spinner {
    animation: none;
  }

  .credit-card__error-message {
    animation: none;
  }
}

/* Print Styles */
@media print {
  .credit-card-container {
    box-shadow: none;
    border: 2px solid #000000;
  }

  .credit-card__input {
    border-color: #000000;
    background: #ffffff;
  }

  .credit-card__actions,
  .credit-card__security-info {
    display: none;
  }
}