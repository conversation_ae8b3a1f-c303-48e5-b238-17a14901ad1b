<div [class]="computedClasses()" [attr.data-testid]="'credit-card-container'">
  <!-- Security Warning for Session Expiry -->
  <div *ngIf="!sessionActive()" 
       class="credit-card__session-warning"
       role="alert"
       aria-live="assertive">
    <div class="warning-icon">⚠️</div>
    <div class="warning-content">
      <h3>{{ getLocalizedMessage('sessionExpired') }}</h3>
      <p>Please refresh the page to continue securely.</p>
    </div>
  </div>

  <!-- Security Info Panel -->
  <div *ngIf="showSecurityInfo && sessionActive()" 
       class="credit-card__security-info">
    <div class="security-badges">
      <span class="security-badge security-badge--pci">PCI DSS</span>
      <span class="security-badge security-badge--ssl">🔒 SSL</span>
      <span *ngIf="fraudDetection" class="security-badge security-badge--fraud">🛡️ Fraud Protection</span>
    </div>
    <p class="security-message">Your payment information is encrypted and secure</p>
  </div>

  <!-- Main Credit Card Form -->
  <form [formGroup]="creditCardForm" 
        class="credit-card__form"
        [class.credit-card__form--disabled]="disabled || !sessionActive()"
        novalidate
        autocomplete="off"
        (ngSubmit)="submit()">

    <!-- Card Number Field -->
    <div class="credit-card__field-group">
      <label for="card-number" 
             class="credit-card__label"
             [class.credit-card__label--required]="true">
        Card Number
        <span class="required-indicator" aria-label="required">*</span>
      </label>
      
      <div class="credit-card__input-wrapper">
        <input
          id="card-number"
          formControlName="number"
          type="text"
          class="credit-card__input credit-card__input--number"
          [class.credit-card__input--error]="validationState().number.message"
          [class.credit-card__input--valid]="validationState().number.valid"
          [placeholder]="placeholder.number || '1234 5678 9012 3456'"
          [disabled]="disabled || !sessionActive()"
          autocomplete="cc-number"
          inputmode="numeric"
          maxlength="23"
          (input)="onCardNumberInput($event)"
          [attr.aria-describedby]="validationState().number.message ? 'card-number-error' : null"
          [attr.aria-invalid]="validationState().number.message ? 'true' : 'false'"
        />
        
        <!-- Card Type Icon -->
        <div class="credit-card__card-icon" 
             *ngIf="showCardImage && detectedCardType()"
             [attr.aria-label]="detectedCardType() + ' card'">
          <span class="card-icon">{{ getCardTypeIcon(detectedCardType()) }}</span>
          <span class="card-type-name">{{ cardTypes[detectedCardType()]?.name }}</span>
        </div>
      </div>
      
      <!-- Card Number Validation Message -->
      <div *ngIf="validationState().number.message" 
           id="card-number-error"
           class="credit-card__error-message"
           role="alert"
           aria-live="polite">
        <span class="error-icon">⚠️</span>
        {{ validationState().number.message }}
      </div>
    </div>

    <!-- Expiry and CVV Row -->
    <div class="credit-card__row" 
         *ngIf="mergedConfig().requireExpiry || mergedConfig().requireCVV">
      
      <!-- Expiry Date Field -->
      <div class="credit-card__field-group credit-card__field-group--half"
           *ngIf="mergedConfig().requireExpiry">
        <label for="card-expiry" 
               class="credit-card__label"
               [class.credit-card__label--required]="mergedConfig().requireExpiry">
          Expiry Date
          <span *ngIf="mergedConfig().requireExpiry" class="required-indicator" aria-label="required">*</span>
        </label>
        
        <input
          id="card-expiry"
          formControlName="expiry"
          type="text"
          class="credit-card__input credit-card__input--expiry"
          [class.credit-card__input--error]="validationState().expiry.message"
          [class.credit-card__input--valid]="validationState().expiry.valid"
          [placeholder]="placeholder.expiry || 'MM/YY'"
          [disabled]="disabled || !sessionActive()"
          autocomplete="cc-exp"
          inputmode="numeric"
          maxlength="5"
          (input)="onExpiryInput($event)"
          [attr.aria-describedby]="validationState().expiry.message ? 'card-expiry-error' : null"
          [attr.aria-invalid]="validationState().expiry.message ? 'true' : 'false'"
        />
        
        <div *ngIf="validationState().expiry.message" 
             id="card-expiry-error"
             class="credit-card__error-message"
             role="alert"
             aria-live="polite">
          <span class="error-icon">⚠️</span>
          {{ validationState().expiry.message }}
        </div>
      </div>

      <!-- CVV Field -->
      <div class="credit-card__field-group credit-card__field-group--half"
           *ngIf="mergedConfig().requireCVV">
        <label for="card-cvv" 
               class="credit-card__label"
               [class.credit-card__label--required]="mergedConfig().requireCVV">
          {{ getCvvLabel() }}
          <span *ngIf="mergedConfig().requireCVV" class="required-indicator" aria-label="required">*</span>
        </label>
        
        <div class="credit-card__input-wrapper">
          <input
            id="card-cvv"
            formControlName="cvv"
            type="password"
            class="credit-card__input credit-card__input--cvv"
            [class.credit-card__input--error]="validationState().cvv.message"
            [class.credit-card__input--valid]="validationState().cvv.valid"
            [placeholder]="placeholder.cvv || '123'"
            [disabled]="disabled || !sessionActive()"
            autocomplete="cc-csc"
            inputmode="numeric"
            [maxlength]="detectedCardType() === 'amex' ? '4' : '3'"
            (input)="onCvvInput($event)"
            [attr.aria-describedby]="validationState().cvv.message ? 'card-cvv-error cvv-help' : 'cvv-help'"
            [attr.aria-invalid]="validationState().cvv.message ? 'true' : 'false'"
          />
          
          <!-- CVV Help Icon -->
          <div class="credit-card__help-icon" 
               tabindex="0"
               role="button"
               aria-label="Security code help"
               title="3 digits on the back of your card, or 4 digits on the front for American Express">
            <span class="help-icon">?</span>
          </div>
        </div>
        
        <div id="cvv-help" class="credit-card__help-text">
          {{ detectedCardType() === 'amex' ? '4 digits on front' : '3 digits on back' }}
        </div>
        
        <div *ngIf="validationState().cvv.message" 
             id="card-cvv-error"
             class="credit-card__error-message"
             role="alert"
             aria-live="polite">
          <span class="error-icon">⚠️</span>
          {{ validationState().cvv.message }}
        </div>
      </div>
    </div>

    <!-- Cardholder Name Field -->
    <div class="credit-card__field-group"
         *ngIf="mergedConfig().requireName">
      <label for="card-name" 
             class="credit-card__label"
             [class.credit-card__label--required]="mergedConfig().requireName">
        Cardholder Name
        <span *ngIf="mergedConfig().requireName" class="required-indicator" aria-label="required">*</span>
      </label>
      
      <input
        id="card-name"
        formControlName="name"
        type="text"
        class="credit-card__input credit-card__input--name"
        [class.credit-card__input--error]="validationState().name.message"
        [class.credit-card__input--valid]="validationState().name.valid"
        [placeholder]="placeholder.name || 'John Doe'"
        [disabled]="disabled || !sessionActive()"
        autocomplete="cc-name"
        [attr.aria-describedby]="validationState().name.message ? 'card-name-error' : null"
        [attr.aria-invalid]="validationState().name.message ? 'true' : 'false'"
      />
      
      <div *ngIf="validationState().name.message" 
           id="card-name-error"
           class="credit-card__error-message"
           role="alert"
           aria-live="polite">
        <span class="error-icon">⚠️</span>
        {{ validationState().name.message }}
      </div>
    </div>

    <!-- Form Actions -->
    <div class="credit-card__actions">
      <button type="button"
              class="credit-card__button credit-card__button--secondary"
              [disabled]="disabled || !sessionActive() || isProcessing()"
              (click)="reset()">
        Reset
      </button>
      
      <button type="submit"
              class="credit-card__button credit-card__button--primary"
              [disabled]="!isFormValid() || disabled || !sessionActive() || isProcessing() || attemptCount() >= maxAttempts">
        <span *ngIf="!isProcessing()">Submit Payment</span>
        <span *ngIf="isProcessing()" class="loading-content">
          <span class="spinner" aria-hidden="true"></span>
          Processing...
        </span>
      </button>
    </div>

    <!-- Attempt Counter Warning -->
    <div *ngIf="attemptCount() > 0 && attemptCount() < maxAttempts" 
         class="credit-card__attempt-warning"
         role="alert"
         aria-live="polite">
      <span class="warning-icon">⚠️</span>
      {{ maxAttempts - attemptCount() }} attempts remaining
    </div>

    <!-- Max Attempts Reached -->
    <div *ngIf="attemptCount() >= maxAttempts" 
         class="credit-card__max-attempts"
         role="alert"
         aria-live="assertive">
      <span class="error-icon">🚫</span>
      {{ getLocalizedMessage('maxAttemptsReached') }}. Please contact support.
    </div>
  </form>

  <!-- Form Status Indicator -->
  <div class="credit-card__status" 
       [attr.aria-live]="'polite'"
       [attr.aria-label]="isFormValid() ? 'Form is valid' : 'Form has validation errors'">
    <div class="status-indicator" 
         [class.status-indicator--valid]="isFormValid()"
         [class.status-indicator--invalid]="!isFormValid()">
      <span class="status-icon">{{ isFormValid() ? '✓' : '⚠️' }}</span>
      <span class="status-text">
        {{ isFormValid() ? 'Form is complete' : 'Please complete all required fields' }}
      </span>
    </div>
  </div>

  <!-- Security Footer -->
  <div class="credit-card__security-footer" *ngIf="showSecurityInfo">
    <div class="security-features">
      <div class="security-feature">
        <span class="feature-icon">🔒</span>
        <span class="feature-text">256-bit SSL encryption</span>
      </div>
      <div class="security-feature">
        <span class="feature-icon">🛡️</span>
        <span class="feature-text">PCI DSS compliant</span>
      </div>
      <div class="security-feature" *ngIf="fraudDetection">
        <span class="feature-icon">🔍</span>
        <span class="feature-text">Fraud detection</span>
      </div>
    </div>
    <p class="security-disclaimer">
      We never store your full card number or security code. All payment data is encrypted and processed securely.
    </p>
  </div>
</div>
