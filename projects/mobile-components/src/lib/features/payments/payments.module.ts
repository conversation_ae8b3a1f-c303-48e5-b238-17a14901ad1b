import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import Payments components
// Standalone (3)
import { CreditCardComponent } from './credit-card/credit-card.component';
import { CreditCardRealComponent } from './credit-card-real/credit-card-real.component';
import { CreditCardSmallComponent } from './credit-card-small/credit-card-small.component';

// Non-Standalone (0)
// None

@NgModule({
  declarations: [
    // Non-Standalone Components (0)
    // None
  ],
  imports: [
    CommonModule,
    IonicModule,
    // Standalone Components (3)
    CreditCardComponent,
    CreditCardRealComponent,
    CreditCardSmallComponent,
  ],
  exports: [
    // Standalone Components (3)
    CreditCardComponent,
    CreditCardRealComponent,
    CreditCardSmallComponent,
  ]
})
export class PaymentsModule { }
