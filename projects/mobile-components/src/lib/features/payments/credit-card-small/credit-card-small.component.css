/* Credit Card Small Component - PCI-DSS Compliant Compact Display */

/* CSS Custom Properties */
.credit-card-small {
  --primary-color: #2563eb;
  --success-color: #059669;
  --warning-color: #d97706;
  --danger-color: #dc2626;
  --neutral-color: #6b7280;
  --background-color: #ffffff;
  --border-color: #d1d5db;
  --text-color: #1f2937;
  --text-muted: #6b7280;
  --shadow-light: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-medium: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-large: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  --border-radius: 0.5rem;
  --transition: all 0.2s ease-in-out;

  /* Component Structure */
  position: relative;
  display: flex;
  align-items: center;
  padding: 1rem;
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Robot<PERSON>, sans-serif;
  transition: var(--transition);
  overflow: hidden;
  min-height: 4rem;
  user-select: none;
}

/* Dark Mode Support */
.credit-card-small.dark {
  --background-color: #1f2937;
  --border-color: #374151;
  --text-color: #f9fafb;
  --text-muted: #9ca3af;
}

/* Size Variations */
.credit-card-small.size-xs {
  padding: 0.5rem;
  min-height: 2.5rem;
  font-size: 0.75rem;
}

.credit-card-small.size-sm {
  padding: 0.75rem;
  min-height: 3rem;
  font-size: 0.875rem;
}

.credit-card-small.size-md {
  padding: 1rem;
  min-height: 4rem;
  font-size: 1rem;
}

.credit-card-small.size-lg {
  padding: 1.25rem;
  min-height: 5rem;
  font-size: 1.125rem;
}

.credit-card-small.size-xl {
  padding: 1.5rem;
  min-height: 6rem;
  font-size: 1.25rem;
}

/* Variant Styles */
.credit-card-small.variant-primary {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 1px var(--primary-color);
}

.credit-card-small.variant-success {
  border-color: var(--success-color);
  box-shadow: 0 0 0 1px var(--success-color);
}

.credit-card-small.variant-warning {
  border-color: var(--warning-color);
  box-shadow: 0 0 0 1px var(--warning-color);
}

.credit-card-small.variant-danger {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 1px var(--danger-color);
}

/* Rounded Variations */
.credit-card-small.rounded-none {
  border-radius: 0;
}

.credit-card-small.rounded-sm {
  border-radius: 0.25rem;
}

.credit-card-small.rounded-lg {
  border-radius: 1rem;
}

.credit-card-small.rounded-full {
  border-radius: 2rem;
}

/* Interactive States */
.credit-card-small.interactive {
  cursor: pointer;
}

.credit-card-small.interactive:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-1px);
}

.credit-card-small.interactive:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.2);
}

.credit-card-small.hovered {
  box-shadow: var(--shadow-large);
  transform: translateY(-2px);
}

.credit-card-small.pressed {
  transform: translateY(0);
  box-shadow: var(--shadow-light);
}

.credit-card-small.selected {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-color);
  background: rgb(37 99 235 / 0.05);
}

/* Disabled State */
.credit-card-small.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
  background: #f9fafb;
}

/* Expired State */
.credit-card-small.expired {
  border-color: var(--danger-color);
  background: rgb(220 38 38 / 0.05);
}

/* Default Card State */
.credit-card-small.default-card {
  border-color: var(--warning-color);
  background: rgb(217 119 6 / 0.05);
}

/* Compact Mode */
.credit-card-small.compact {
  min-height: 3rem;
  padding: 0.75rem;
}

.credit-card-small.compact .card-content {
  gap: 0.5rem;
}

/* Card Content Layout */
.card-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  flex: 1;
  min-width: 0;
}

/* Header Section */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
}

.card-type-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 0;
}

.card-icon {
  font-size: 1.25em;
  flex-shrink: 0;
}

.card-type-name {
  font-size: 0.875em;
  font-weight: 600;
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-badges {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.security-badge {
  font-size: 0.875em;
  color: var(--success-color);
}

.status-badge {
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-badge.status-expired {
  background: #fee2e2;
  color: #991b1b;
}

.status-badge.status-default {
  background: #fef3c7;
  color: #92400e;
}

/* Card Number Section */
.card-number-section {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.card-number-label {
  font-size: 0.75rem;
  color: var(--text-muted);
  font-weight: 500;
}

.card-number {
  display: flex;
  align-items: center;
}

.masked-number {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 1em;
  font-weight: 600;
  color: var(--text-color);
  letter-spacing: 0.1em;
  white-space: nowrap;
}

.masked-number.compact {
  font-size: 0.875em;
}

/* Card Details Section */
.card-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.expiry-section,
.cardholder-section {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  min-width: 0;
}

.detail-label {
  font-size: 0.75rem;
  color: var(--text-muted);
  font-weight: 500;
}

.expiry-value,
.cardholder-value {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.expiry-text,
.cardholder-text {
  font-size: 0.875em;
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.expiry-value.expired .expiry-text {
  color: var(--danger-color);
  text-decoration: line-through;
}

.expired-indicator {
  font-size: 0.875em;
  color: var(--danger-color);
}

/* Compact Details */
.compact-details {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: var(--text-muted);
}

.compact-expiry.expired {
  color: var(--danger-color);
  text-decoration: line-through;
}

.compact-divider {
  color: var(--text-muted);
}

.compact-cardholder {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

/* Selection Indicator */
.selection-indicator {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  z-index: 10;
}

.selection-checkmark {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: 600;
}

/* Default Card Indicator */
.default-indicator {
  position: absolute;
  bottom: 0.5rem;
  right: 0.5rem;
  z-index: 10;
}

.default-badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: var(--warning-color);
  color: white;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.default-icon {
  font-size: 0.875em;
}

/* Hover Overlay */
.hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgb(37 99 235 / 0.05);
  opacity: 0;
  transition: var(--transition);
  pointer-events: none;
}

.credit-card-small.hovered .hover-overlay {
  opacity: 1;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.loading-spinner {
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid #e5e7eb;
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 640px) {
  .credit-card-small {
    padding: 0.75rem;
    min-height: 3.5rem;
  }

  .card-details {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .card-type-name {
    display: none;
  }

  .card-badges {
    gap: 0.25rem;
  }

  .compact-details {
    font-size: 0.6875rem;
    gap: 0.375rem;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .credit-card-small {
    border-width: 2px;
    --shadow-medium: 0 4px 6px -1px rgb(0 0 0 / 0.3);
  }

  .credit-card-small.interactive:focus {
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.4);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .credit-card-small,
  .credit-card-small * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print Styles */
@media print {
  .credit-card-small {
    box-shadow: none;
    border: 1px solid #000;
    break-inside: avoid;
  }

  .hover-overlay,
  .loading-overlay,
  .selection-indicator {
    display: none !important;
  }

  .masked-number {
    /* Ensure masked numbers are not selectable in print */
    user-select: none;
  }
}

/* Focus Management */
.credit-card-small:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Security Enhancement: Prevent text selection of sensitive data */
.masked-number,
.expiry-text,
.cardholder-text {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Accessibility: Screen reader only text */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}