<!-- Compact Credit Card Display Component Template -->
<div 
  [class]="containerClasses()"
  [attr.aria-label]="getAriaLabel()"
  [attr.aria-pressed]="getAriaPressed()"
  [attr.tabindex]="interactive && !disabled ? '0' : null"
  [attr.role]="interactive ? 'button' : 'img'"
  (click)="onCardClick($event)"
  (mouseenter)="onMouseEnter()"
  (mouseleave)="onMouseLeave()"
  (mousedown)="onMouseDown()"
  (mouseup)="onMouseUp()"
  (keydown)="onKeyDown($event)"
  (focus)="onFocus()"
  (blur)="onBlur()">

  <!-- Card Content Container -->
  <div class="card-content">
    
    <!-- Header Section -->
    <div class="card-header" *ngIf="shouldShowCardType() || shouldShowSecurityBadge()">
      <!-- Card Type Information -->
      <div class="card-type-info" *ngIf="shouldShowCardType()">
        <span class="card-icon" 
              [style.color]="getCardTypeColor()"
              [title]="cardTypeInfo().name">
          {{ getCardTypeIcon() }}
        </span>
        <span class="card-type-name" *ngIf="!compact">
          {{ cardTypeInfo().name }}
        </span>
      </div>

      <!-- Security and Status Badges -->
      <div class="card-badges">
        <!-- Security Badge -->
        <span class="security-badge" 
              *ngIf="shouldShowSecurityBadge()"
              title="PCI-DSS Secure">
          {{ getSecurityBadge() }}
        </span>

        <!-- Status Badge -->
        <span class="status-badge" 
              *ngIf="shouldShowStatus()"
              [class]="'status-' + getStatusText().toLowerCase()"
              [title]="getStatusText()">
          {{ getStatusText() }}
        </span>
      </div>
    </div>

    <!-- Card Number Section -->
    <div class="card-number-section">
      <!-- Card Number Label -->
      <div class="card-number-label" *ngIf="showLabels">
        <span class="label-text">Card Number</span>
      </div>

      <!-- Masked Card Number -->
      <div class="card-number">
        <span class="masked-number" 
              [class.compact]="compact"
              [attr.aria-label]="'Card number ending in ' + mergedCardData().lastFourDigits">
          {{ compact ? '•••• ' + mergedCardData().lastFourDigits : getFormattedMaskedNumber() }}
        </span>
      </div>
    </div>

    <!-- Card Details Section -->
    <div class="card-details" *ngIf="!compact && (shouldShowExpiry() || shouldShowCardholderName())">
      
      <!-- Expiry Date -->
      <div class="expiry-section" *ngIf="shouldShowExpiry()">
        <div class="detail-label" *ngIf="showLabels">
          <span class="label-text">Expires</span>
        </div>
        <div class="expiry-value" 
             [class.expired]="isExpired()"
             [attr.aria-label]="'Expires ' + getExpiryDisplay()">
          <span class="expiry-text">{{ getExpiryDisplay() }}</span>
          <span class="expired-indicator" *ngIf="isExpired()" title="Card Expired">⚠️</span>
        </div>
      </div>

      <!-- Cardholder Name -->
      <div class="cardholder-section" *ngIf="shouldShowCardholderName()">
        <div class="detail-label" *ngIf="showLabels">
          <span class="label-text">Cardholder</span>
        </div>
        <div class="cardholder-value" 
             [attr.aria-label]="'Cardholder ' + getCardholderDisplay()">
          <span class="cardholder-text">{{ getCardholderDisplay() }}</span>
        </div>
      </div>
    </div>

    <!-- Compact Details Row -->
    <div class="compact-details" *ngIf="compact && (shouldShowExpiry() || shouldShowCardholderName())">
      <span class="compact-expiry" *ngIf="shouldShowExpiry()" [class.expired]="isExpired()">
        {{ getExpiryDisplay() }}
        <span class="expired-indicator" *ngIf="isExpired()">⚠️</span>
      </span>
      <span class="compact-divider" *ngIf="shouldShowExpiry() && shouldShowCardholderName()">•</span>
      <span class="compact-cardholder" *ngIf="shouldShowCardholderName()">
        {{ getCardholderDisplay() }}
      </span>
    </div>

  </div>

  <!-- Selection Indicator -->
  <div class="selection-indicator" *ngIf="selected && interactive">
    <span class="selection-checkmark" aria-hidden="true">✓</span>
  </div>

  <!-- Default Card Indicator -->
  <div class="default-indicator" *ngIf="mergedCardData().isDefault">
    <span class="default-badge" title="Default Payment Method">
      <span class="default-icon">⭐</span>
      <span class="default-text" *ngIf="!compact">Default</span>
    </span>
  </div>

  <!-- Interactive Hover Overlay -->
  <div class="hover-overlay" *ngIf="interactive && !disabled" aria-hidden="true"></div>

  <!-- Loading State -->
  <div class="loading-overlay" *ngIf="disabled" aria-hidden="true">
    <div class="loading-spinner"></div>
  </div>

</div>
