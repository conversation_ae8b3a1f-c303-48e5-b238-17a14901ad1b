import { Component, Input, Output, EventEmitter, computed, signal, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';

// PCI-DSS compliant interfaces for secure card display
interface CreditCardData {
  lastFourDigits: string;
  cardType: string;
  expiryMonth?: string;
  expiryYear?: string;
  cardholderName?: string;
  isExpired?: boolean;
  isDefault?: boolean;
}

interface CompactDisplayConfig {
  showCardType: boolean;
  showExpiry: boolean;
  showCardholderName: boolean;
  showSecurityBadge: boolean;
  showStatus: boolean;
  maskingPattern: 'dots' | 'asterisks' | 'x' | 'custom';
  customMaskChar?: string;
  enableInteraction: boolean;
  enableSelection: boolean;
}

interface SecurityEventData {
  type: 'view' | 'select' | 'interaction' | 'security';
  timestamp: string;
  cardId?: string;
  details: any;
}

// Card type definitions with security-focused metadata
const CARD_TYPE_INFO = {
  visa: {
    name: 'Visa',
    icon: '💳',
    color: '#1A1F71',
    pattern: /^4/
  },
  mastercard: {
    name: 'Mastercard', 
    icon: '💳',
    color: '#EB001B',
    pattern: /^5[1-5]/
  },
  amex: {
    name: 'American Express',
    icon: '💳', 
    color: '#006FCF',
    pattern: /^3[47]/
  },
  discover: {
    name: 'Discover',
    icon: '💳',
    color: '#FF6000', 
    pattern: /^6(?:011|5)/
  },
  unknown: {
    name: 'Card',
    icon: '💳',
    color: '#6B7280',
    pattern: /.*/
  }
};

@Component({
  selector: 'lib-credit-card-small',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './credit-card-small.component.html',
  styleUrl: './credit-card-small.component.css'
})
export class CreditCardSmallComponent implements OnInit, OnDestroy {
  // Standard UI inputs for LP-GO builder integration
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() disabled: boolean = false;

  // Credit card specific inputs - PCI-DSS compliant (only last 4 digits and metadata)
  @Input() cardData: Partial<CreditCardData> = {};
  @Input() config: Partial<CompactDisplayConfig> = {};
  @Input() showLabels: boolean = false;
  @Input() interactive: boolean = true;
  @Input() selected: boolean = false;
  @Input() darkMode: boolean = false;
  @Input() compact: boolean = false;

  // Security and interaction outputs
  @Output() cardSelected = new EventEmitter<CreditCardData>();
  @Output() cardClicked = new EventEmitter<CreditCardData>();
  @Output() securityEvent = new EventEmitter<SecurityEventData>();

  // Component state using Angular signals
  private isHovered = signal(false);
  private isPressed = signal(false);
  private lastInteraction = signal<number>(0);

  // Default configurations
  private defaultCardData: CreditCardData = {
    lastFourDigits: '4567',
    cardType: 'visa',
    expiryMonth: '12',
    expiryYear: '25',
    cardholderName: 'John Doe',
    isExpired: false,
    isDefault: false
  };

  private defaultConfig: CompactDisplayConfig = {
    showCardType: true,
    showExpiry: true,
    showCardholderName: false,
    showSecurityBadge: true,
    showStatus: true,
    maskingPattern: 'dots',
    customMaskChar: '•',
    enableInteraction: true,
    enableSelection: true
  };

  // Computed properties for reactive state management
  mergedCardData = computed(() => ({
    ...this.defaultCardData,
    ...this.cardData
  }));

  mergedConfig = computed(() => ({
    ...this.defaultConfig,
    ...this.config
  }));

  // Card type information computed from card data
  cardTypeInfo = computed(() => {
    const cardType = this.mergedCardData().cardType || 'unknown';
    return CARD_TYPE_INFO[cardType as keyof typeof CARD_TYPE_INFO] || CARD_TYPE_INFO.unknown;
  });

  // Expiry status computation
  isExpired = computed(() => {
    if (this.mergedCardData().isExpired !== undefined) {
      return this.mergedCardData().isExpired;
    }

    const cardData = this.mergedCardData();
    if (!cardData.expiryMonth || !cardData.expiryYear) return false;

    const expiryDate = new Date(
      parseInt('20' + cardData.expiryYear!),
      parseInt(cardData.expiryMonth!) - 1,
      1
    );
    
    const now = new Date();
    const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    
    return expiryDate < currentMonth;
  });

  // Dynamic classes computation
  containerClasses = computed(() => {
    const base = 'credit-card-small';
    const sizeClass = `size-${this.size}`;
    const variantClass = `variant-${this.variant}`;
    const roundedClass = `rounded-${this.rounded}`;
    const stateClasses = [
      this.disabled ? 'disabled' : '',
      this.selected ? 'selected' : '',
      this.isHovered() ? 'hovered' : '',
      this.isPressed() ? 'pressed' : '',
      this.darkMode ? 'dark' : '',
      this.compact ? 'compact' : '',
      this.isExpired() ? 'expired' : '',
      this.mergedCardData().isDefault ? 'default-card' : '',
      this.interactive ? 'interactive' : 'static'
    ].filter(Boolean);

    return [
      base,
      sizeClass,
      variantClass,
      roundedClass,
      ...stateClasses,
      this.className
    ].filter(Boolean).join(' ');
  });

  constructor() {}

  ngOnInit(): void {
    this.emitSecurityEvent('view', { 
      cardType: this.mergedCardData().cardType,
      lastFour: this.mergedCardData().lastFourDigits 
    });
  }

  ngOnDestroy(): void {
    // Clean up any sensitive data from component state
    this.lastInteraction.set(0);
  }

  // Secure card number masking
  getMaskedCardNumber(): string {
    const config = this.mergedConfig();
    const lastFour = this.mergedCardData().lastFourDigits || '0000';
    
    let maskChar = '•';
    switch (config.maskingPattern) {
      case 'asterisks':
        maskChar = '*';
        break;
      case 'x':
        maskChar = 'X';
        break;
      case 'custom':
        maskChar = config.customMaskChar || '•';
        break;
      default:
        maskChar = '•';
    }

    // Standard PCI-DSS compliant masking: show only last 4 digits
    const maskedSection = maskChar.repeat(12);
    return `${maskedSection}${lastFour}`;
  }

  // Formatted display of masked number with spaces
  getFormattedMaskedNumber(): string {
    const masked = this.getMaskedCardNumber();
    return masked.replace(/(.{4})/g, '$1 ').trim();
  }

  // Expiry display formatting
  getExpiryDisplay(): string {
    const cardData = this.mergedCardData();
    if (!cardData.expiryMonth || !cardData.expiryYear) return '';
    
    return `${cardData.expiryMonth}/${cardData.expiryYear}`;
  }

  // Cardholder name display with privacy protection
  getCardholderDisplay(): string {
    const name = this.mergedCardData().cardholderName;
    if (!name || !this.mergedConfig().showCardholderName) return '';
    
    // For privacy, only show first name and last initial
    const parts = name.trim().split(' ');
    if (parts.length === 1) return parts[0];
    
    const firstName = parts[0];
    const lastInitial = parts[parts.length - 1].charAt(0).toUpperCase();
    
    return `${firstName} ${lastInitial}.`;
  }

  // Card status text
  getStatusText(): string {
    if (this.isExpired()) return 'Expired';
    if (this.mergedCardData().isDefault) return 'Default';
    return '';
  }

  // Security badge display
  getSecurityBadge(): string {
    if (!this.mergedConfig().showSecurityBadge) return '';
    return '🔒';
  }

  // Interaction handlers
  onCardClick(event: Event): void {
    if (this.disabled || !this.interactive) return;

    event.preventDefault();
    event.stopPropagation();

    this.lastInteraction.set(Date.now());
    
    const cardData = this.mergedCardData();
    this.cardClicked.emit(cardData);
    
    if (this.mergedConfig().enableSelection) {
      this.cardSelected.emit(cardData);
    }

    this.emitSecurityEvent('interaction', {
      action: 'click',
      cardType: cardData.cardType,
      lastFour: cardData.lastFourDigits
    });
  }

  onMouseEnter(): void {
    if (this.disabled || !this.interactive) return;
    this.isHovered.set(true);
  }

  onMouseLeave(): void {
    this.isHovered.set(false);
    this.isPressed.set(false);
  }

  onMouseDown(): void {
    if (this.disabled || !this.interactive) return;
    this.isPressed.set(true);
  }

  onMouseUp(): void {
    this.isPressed.set(false);
  }

  // Keyboard interaction support
  onKeyDown(event: KeyboardEvent): void {
    if (this.disabled || !this.interactive) return;

    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.onCardClick(event);
    }
  }

  // Focus management
  onFocus(): void {
    if (!this.disabled && this.interactive) {
      this.emitSecurityEvent('interaction', { action: 'focus' });
    }
  }

  onBlur(): void {
    this.isHovered.set(false);
    this.isPressed.set(false);
  }

  // Security event emission
  private emitSecurityEvent(type: SecurityEventData['type'], details: any): void {
    const event: SecurityEventData = {
      type,
      timestamp: new Date().toISOString(),
      cardId: this.generateSecureCardId(),
      details
    };

    this.securityEvent.emit(event);
  }

  // Generate a secure, non-sensitive card identifier for logging
  private generateSecureCardId(): string {
    const cardData = this.mergedCardData();
    const cardType = cardData.cardType || 'unknown';
    const lastFour = cardData.lastFourDigits || '0000';
    
    // Create a hash-like identifier that doesn't expose sensitive data
    return `${cardType}_${lastFour}_${Date.now().toString(36)}`;
  }

  // Accessibility helpers
  getAriaLabel(): string {
    const cardData = this.mergedCardData();
    const cardType = this.cardTypeInfo().name;
    const lastFour = cardData.lastFourDigits;
    const status = this.getStatusText();
    
    let label = `${cardType} ending in ${lastFour}`;
    
    if (status) {
      label += `, ${status}`;
    }
    
    if (this.interactive) {
      label += ', button';
    }
    
    return label;
  }

  getAriaPressed(): string | null {
    if (!this.interactive || !this.mergedConfig().enableSelection) return null;
    return this.selected.toString();
  }

  // Template helper methods
  shouldShowCardType(): boolean {
    return this.mergedConfig().showCardType;
  }

  shouldShowExpiry(): boolean {
    return this.mergedConfig().showExpiry && !!this.getExpiryDisplay();
  }

  shouldShowCardholderName(): boolean {
    return this.mergedConfig().showCardholderName && !!this.getCardholderDisplay();
  }

  shouldShowStatus(): boolean {
    return this.mergedConfig().showStatus && !!this.getStatusText();
  }

  shouldShowSecurityBadge(): boolean {
    return this.mergedConfig().showSecurityBadge;
  }

  // Card type styling helpers
  getCardTypeColor(): string {
    return this.cardTypeInfo().color;
  }

  getCardTypeIcon(): string {
    return this.cardTypeInfo().icon;
  }
}
