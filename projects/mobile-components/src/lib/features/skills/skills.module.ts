import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import Skills components
// Standalone (1)
import { TrendingSkillsComponent } from './trending-skills/trending-skills.component';

// Non-Standalone (0)
// None

@NgModule({
  declarations: [
    // Non-Standalone Components (0)
    // None
  ],
  imports: [
    CommonModule,
    IonicModule,
    // Standalone Components (1)
    TrendingSkillsComponent,
  ],
  exports: [
    // Standalone Components (1)
    TrendingSkillsComponent,
  ]
})
export class SkillsModule { }
