import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { CustomizerComponent } from './customizer.component';
import { ColoursModule } from '../colours/colours.module';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ColoursModule,
    CustomizerComponent
  ],
  exports: [
    CustomizerComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class CustomizerModule { }
