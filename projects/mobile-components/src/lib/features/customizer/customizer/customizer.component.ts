import { Component, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { ColoursComponent } from '../../../shared/colours/colours.component';
import { SocialsCustomizerComponent } from '../../socials/socials/customizer/socials-customizer.component';
import { PagesCustomizerComponent } from '../../../pages/customizer/pages-customizer.component';
import { CommonModule } from '@angular/common';
import { LssConfig } from 'lp-client-api';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'base-customizer',
  templateUrl: './customizer.component.html',
  styleUrls: ['./customizer.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    IonicModule,
    ColoursComponent,
    SocialsCustomizerComponent,
    PagesCustomizerComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class CustomizerComponent {
  isModalOpen = false;

  constructor(public lssConfig: LssConfig) {}

  setOpen(isOpen: boolean) {
    this.isModalOpen = isOpen;
  }
}
