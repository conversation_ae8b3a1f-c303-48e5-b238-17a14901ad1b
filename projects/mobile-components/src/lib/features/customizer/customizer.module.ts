import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import Customizer components
// Standalone (1)
import { CustomizerComponent } from './customizer/customizer.component';

// Non-Standalone (0)
// None

@NgModule({
  declarations: [
    // Non-Standalone Components (0)
    // None
  ],
  imports: [
    CommonModule,
    IonicModule,
    // Standalone Components (1)
    CustomizerComponent,
  ],
  exports: [
    // Standalone Components (1)
    CustomizerComponent,
  ]
})
export class CustomizerModule { }
