import { NgModule, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { StoresComponent } from './stores.component';
// Import IonicSelectableWrapperModule
import { IonicSelectableWrapperModule } from '../shared/ionic-selectable/ionic-selectable.module';
import { BaseComponentsModule } from '../base/base-components.module';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    IonicModule,
    IonicSelectableWrapperModule,
    BaseComponentsModule,
    StoresComponent
  ],
  exports: [
    StoresComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class StoresModule { }
