<ion-content class="app-background">
  <lib-head-logo
    [balance]="profile?.currentBalance"
    [src]="lssConfig.icon"
  />

  <ion-content class="card-background ion-padding">
    <ion-card-content class="app-background">
      <google-map
        class="google-map"
        width="auto"
        height="50vh"
        [options]="options"
        [center]="center"
        [zoom]="zoom"
        (boundsChanged)="boundsChanged()"
      >
        <map-marker-clusterer [imagePath]="markerClustererImagePath">
          <map-marker
            #marker="mapMarker"
            *ngFor="let marker of storeMarkers"
            [position]="marker.position"
            [label]="marker.label"
            [title]="marker.title"
            [options]="marker.options"
            (mapClick)="markerClick(marker)"
          ></map-marker>
        </map-marker-clusterer>
      </google-map>
      <ion-accordion-group class="store-filter" expand="compact">
        <ion-accordion value="first">
          <ion-item slot="header" color="light">
            <ion-label>Filter</ion-label>
          </ion-item>
          <div slot="content" class="ion-padding accordion-content">
            <form class="form">
              <ion-item lines="none">
                <!-- <ion-label position="floating">Search..</ion-label>? -->
                <ion-icon slot="start" name="search-outline"></ion-icon>
                <ion-input
                  labelPlacement="floating"
                  label="Search"
                  type="text"
                  name="searchFilter"
                  [(ngModel)]="searchFilter"
                  type="text"
                ></ion-input>
              </ion-item>
              <ion-item>
                <!-- <ion-label position="floating">Province</ion-label> -->
                <ion-select
                  labelPlacement="floating"
                  label="Province"
                  type="text"
                  name="provinceFilter"
                  [(ngModel)]="provinceFilter"
                  (ionChange)="
                    setProvinceFilter(provinceFilter ? provinceFilter : '')
                  "
                >
                  <ion-select-option
                    *ngFor="let province of provinces | async"
                    [value]="province.value"
                    >{{ province.label }}</ion-select-option
                  >
                </ion-select>
              </ion-item>
              <ion-item>
                <!-- <ion-label position="floating">City</ion-label> -->
                <ion-select
                  labelPlacement="floating"
                  label="City"
                  type="text"
                  name="cityFilter"
                  [(ngModel)]="cityFilter"
                >
                  <ion-select-option
                    *ngFor="let city of cities | async"
                    [value]="city.value"
                    >{{ city.label }}</ion-select-option
                  >
                </ion-select>
              </ion-item>
              <div class="form-spacer"></div>
              <ion-button expand="block" (click)="filter(false)"
                >Filter</ion-button
              >
              <ion-button expand="block" fill="outline" (click)="resetFilter()"
                >Reset</ion-button
              >
            </form>
          </div>
        </ion-accordion>
      </ion-accordion-group>
      <ion-list class="stores">
        <ion-item
          class="store"
          lines="none"
          *ngFor="let store of storeList | async"
          (click)="setSelectedStore(store)"
        >
          <ion-icon slot="start" name="location-outline"></ion-icon>
          <ion-label>
            <h2>{{ store.partnerName }}</h2>
            <p *ngIf="store.getAddressByType('PADR')">
              {{ store.getAddressByType("PADR")?.line1 }},
              {{ store.getAddressByType("PADR")?.suburbDesc }},
              {{ store.getAddressByType("PADR")?.provinceDesc }}
            </p>
          </ion-label>
          <ion-icon
            class="store-link"
            slot="end"
            name="chevron-forward-outline"
          ></ion-icon>
        </ion-item>
        <ion-item
          class="no-store"
          lines="none"
          *ngIf="this.filteredStores.length === 0"
        >
          <p *ngIf="!provinceFilter && !cityFilter">
            No stores in your immediate location
          </p>
          <p *ngIf="provinceFilter || cityFilter">
            No stores for your selected filter criteria
          </p>
        </ion-item>
      </ion-list>
    </ion-card-content>
  </ion-content>
</ion-content>
