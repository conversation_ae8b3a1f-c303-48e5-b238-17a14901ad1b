.app-background {
  --background: var(--ion-color-base)
}
.action-card {
  text-align: center;

background-color: var(--ion-color-primary-shade);
border-radius: 1rem;
box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.6);
}
.card-background {
  background-color: #fff;
  width: 100vw;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.center {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-icon {
position: relative;
top: -25px;
  right: -23px;
border-radius: 3rem;
width: 4rem;
height: 4rem;
box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.4);
  text-align: center;
  padding: 25px 0;
background-color: var(--ion-color-tertiary);

}
.points {
--background: var(--ion-color-secondary);
  width: 100%;
  margin-left: 20px;
  height: 50px;

}

.header-image {
  background: url("/assets/images/store_header.jpg") center/cover no-repeat !important;
}


.stores {
  .store {
    border-bottom: 2px solid #e5e5e5;

    ion-icon {
      font-size: 1.8em;
    }

    ion-icon[slot='end'] {
      color: var(--ion-color-primary);
    }

    h2 {
      text-transform: lowercase;

        &:first-line {
          text-transform: capitalize;
        }
    }
  }

  .no-store {
    p {
      margin: 0 auto;
    }
  }

  ion-item {
    margin: 0 15px;

    &:last-child {
      border-bottom: none;
    }
  } 
}

.accordion-expanded {
  .accordion-content {
    border-bottom: 2px solid #e5e5e5;
    margin: 0 15px;
  }
  
}

.card-background {
  background-color: #fff;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
}