.app-background {
  --background: var(--ion-color-base)
}

.store-header {
  color: black !important;
}
.text-wrap {
  white-space: normal !important;
}
.store-detail {
    padding-bottom: 20px;

    ion-list-header {
      ion-label {
        text-transform: lowercase;
  
          &:first-line {
            text-transform: capitalize;
          }
      }
    }

    ion-icon {
      font-size: 1.8em;
    }

    ion-item {
      border-bottom: 2px solid #e5e5e5;
      margin: 0 15px;

      h2 {
        padding-bottom: 4px;
      }

      .directions {
        font-size: 20px;
        text-align: center;
        font-weight: 600;
        padding: 4px 0 4px 0;
      }

      &:last-child {
        border-bottom: none;

        a {
          margin: 0 auto;
        }

        ion-button {
          font-size: 14px;
        }
      }
    }
}