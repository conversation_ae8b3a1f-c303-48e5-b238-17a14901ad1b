<ion-content class="ion-padding app-background">
  <ion-card class="" *ngIf="store">
    <div class="blur"></div>
    <ion-card-header>
      <ion-card-title class="store-header">{{
        store.partnerName
      }}</ion-card-title>
    </ion-card-header>
    <ion-list class="store-detail">
      <ion-item lines="none" *ngIf="store.getAddressByType('PADR')">
        <ion-icon slot="start" name="location-outline"></ion-icon>
        <ion-label>
          <h2>Address</h2>
          <p>{{ store.getAddressByType("PADR")?.line1 }}</p>
          <p>{{ store.getAddressByType("PADR")?.line2 }}</p>
          <p>{{ store.getAddressByType("PADR")?.cityDesc }}</p>
          <p>{{ store.getAddressByType("PADR")?.provinceDesc }}</p>
          <p>{{ store.getAddressByType("PADR")?.postCode }}</p>
        </ion-label>
      </ion-item>
      <ion-item lines="none" *ngIf="store.getTelephoneByType('WORK')">
        <ion-icon slot="start" name="call-outline"></ion-icon>
        <ion-label>
          <h2>Contact</h2>
          <p>
            <a href="tel:store.telephoneObject.telephoneNumber">{{
              store.getTelephoneByType("WORK")?.telephoneNumber
            }}</a>
          </p>
        </ion-label>
      </ion-item>
      <ion-item lines="none" *ngIf="store.operatingHours">
        <ion-icon slot="start" name="time-outline"></ion-icon>
        <ion-label>
          <h2>Trading Hours</h2>
          <p
            *ngFor="let operatingHours of store.operatingHours"
            class="text-wrap"
          >
            {{ operatingHours.dayOfWeek }}: {{ operatingHours.description }}
          </p>
        </ion-label>
      </ion-item>
      <ion-item lines="none">
        <a
          href="https://www.google.com/maps/dir/?api=1&destination={{
            store.getPartnerMoreByType('LOCATION')?.value
          }}"
          ><ion-button>Get directions</ion-button></a
        >
      </ion-item>
    </ion-list>
  </ion-card>

  <ion-card class="">
    <google-map
      class="clay"
      width="auto"
      height="50vh"
      [options]="options"
      [center]="center"
      [zoom]="zoom"
    >
      <map-marker
        #marker="mapMarker"
        *ngFor="let marker of storeMarkers"
        [position]="marker.position"
        [label]="marker.label"
        [title]="marker.title"
        [options]="marker.options"
      ></map-marker>
    </google-map>
  </ion-card>
</ion-content>
