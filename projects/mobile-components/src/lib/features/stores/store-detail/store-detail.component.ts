import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Child, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { GoogleMapsModule } from '@angular/google-maps';
import { Router } from '@angular/router';
import { Partner } from 'lp-client-api';

@Component({
  selector: 'app-store-detail',
  templateUrl: 'store-detail.component.html',
  styleUrls: ['store-detail.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule, GoogleMapsModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class StoreDetailComponent {
  store!: Partner;
  storeMarkers: any[] = [];
  center!: google.maps.LatLngLiteral;
  options: google.maps.MapOptions = {
    zoomControl: false,
    maxZoom: 18,
    minZoom: 4,
    streetViewControl: false,
  };
  zoom!: number;

  constructor(private router: Router) {}

  ionViewWillEnter(): void {
    const data = history.state.data;
    if (data === undefined) {
      this.router.navigate(['/public/stores']); // Navigate back to the store listing in the case of no store selected
    } else {
      this.store = new Partner({
        partnerId: data.partnerId,
        partnerName: data.partnerName,
      });
      console.log('store', this.store);
      this.store.address = data.address;
      this.store.telephone = data.telephone;
      this.store.operatingHours = data.operatingHours;
      this.store.partnerMore = data.partnerMore;
      console.log(
        'LOCATION : ' + this.store.getPartnerMoreByType('LOCATION')?.value
      );
    }
  }

  ionViewDidEnter(): void {
    this.showStoreLocation();
  }

  ionViewDidLeave(): void {
    this.storeMarkers = [];
  }

  private async showStoreLocation() {
    this.center = {
      lat: Number(
        this.store?.getPartnerMoreByType('LOCATION')?.value.split(',')[0]
      ),
      lng: Number(
        this.store?.getPartnerMoreByType('LOCATION')?.value.split(',')[1]
      ),
    };

    this.storeMarkers.push({
      position: {
        lat: this.center.lat,
        lng: this.center.lng,
      },
    });
  }
}
