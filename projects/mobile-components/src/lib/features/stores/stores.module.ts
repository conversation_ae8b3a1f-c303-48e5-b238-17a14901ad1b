import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import Stores components
// Standalone (2)
import { StoreDetailComponent } from './store-detail/store-detail.component';
import { StoresComponent } from './stores/stores.component';

// Non-Standalone (0)
// None

@NgModule({
  declarations: [
    // Non-Standalone Components (0)
    // None
  ],
  imports: [
    CommonModule,
    IonicModule,
    // Standalone Components (2)
    StoreDetailComponent,
    StoresComponent,
  ],
  exports: [
    // Standalone Components (2)
    StoreDetailComponent,
    StoresComponent,
  ]
})
export class StoresModule { }
