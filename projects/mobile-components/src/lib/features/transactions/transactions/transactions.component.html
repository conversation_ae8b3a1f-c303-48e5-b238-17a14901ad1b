<div [ngClass]="containerClasses()" [attr.aria-label]="'Transactions list with ' + filteredTransactions().length + ' items'">
  <div class="min-h-screen">
    <div class="absolute inset-0 z-0 h-screen bg-blue-200 animated-bg"></div>
    <div class="overflow-y-auto absolute inset-0 z-10 pb-24 space-y-2">
      
      <!-- Enhanced Header with Balance -->
      <lib-head-logo
        [balance]="profile?.currentBalance"
        [src]="lssConfig.icon"
      />
      
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        
        <!-- Enhanced Header Section -->
        <div class="transactions-header" *ngIf="!disabled">
          <div class="header-content">
            <h2 class="transactions-title">Recent Transactions</h2>
            <div class="transactions-subtitle" *ngIf="showLabels">
              {{ filteredTransactions().length }} of {{ statements.length }} transactions
            </div>
          </div>
          
          <div class="header-actions">
            <button
              type="button"
              class="btn btn-secondary refresh-btn"
              (click)="refreshTransactions()"
              [disabled]="disabled || isRefreshing()"
              [attr.aria-busy]="isRefreshing()"
              [attr.aria-label]="'Refresh transactions'"
            >
              <span class="btn-icon" [class.spinning]="isRefreshing()">🔄</span>
              <span class="btn-text" *ngIf="!mergedConfig().compactView">Refresh</span>
            </button>
          </div>
        </div>

        <!-- Enhanced Search and Filters -->
        <div class="transactions-filters" *ngIf="mergedConfig().showFilters && !disabled">
          <!-- Search Input -->
          <div class="filter-group" *ngIf="mergedConfig().enableSearch">
            <label for="search" class="filter-label" *ngIf="showLabels">Search</label>
            <div class="search-wrapper">
              <input
                id="search"
                type="text"
                class="filter-input search-input"
                placeholder="Search transactions..."
                [disabled]="disabled"
                [value]="searchTerm()"
                (input)="updateSearchTerm($any($event.target).value)"
                autocomplete="off"
                maxlength="100"
                aria-describedby="search-help"
              />
              <span class="search-icon">🔍</span>
            </div>
            <div id="search-help" class="input-help" *ngIf="showLabels">
              Search by type, description, or amount
            </div>
          </div>

          <!-- Transaction Type Filter -->
          <div class="filter-group">
            <label for="filter" class="filter-label" *ngIf="showLabels">Filter</label>
            <select
              id="filter"
              class="filter-select"
              [disabled]="disabled"
              [value]="selectedFilter()"
              (change)="updateFilter($any($event.target).value)"
              aria-label="Transaction type filter"
            >
              <option value="all">All Types</option>
              <option value="credit">Credit</option>
              <option value="debit">Debit</option>
              <option value="transfer">Transfer</option>
              <option value="payment">Payment</option>
            </select>
          </div>
        </div>

        <!-- Enhanced Transactions Card -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
          <div class="px-4 py-5 sm:p-6">
            
            <!-- Loading State -->
            <div *ngIf="loading" class="loading-state" role="status" aria-live="polite">
              <div class="loading-content">
                <div class="loading-spinner" aria-hidden="true"></div>
                <div class="loading-text">Loading transactions...</div>
              </div>
            </div>

            <!-- Empty State -->
            <div *ngIf="!loading && filteredTransactions().length === 0" class="empty-state" role="status">
              <div class="empty-icon">📄</div>
              <div class="empty-title">No transactions found</div>
              <div class="empty-message">
                <span *ngIf="searchTerm() || selectedFilter() !== 'all'">
                  Try adjusting your search or filter criteria.
                </span>
                <span *ngIf="!searchTerm() && selectedFilter() === 'all'">
                  No transactions recorded yet.
                </span>
              </div>
              <button
                type="button"
                class="btn btn-primary retry-btn"
                (click)="refreshTransactions()"
                [disabled]="disabled || isRefreshing()"
                *ngIf="!searchTerm() && selectedFilter() === 'all'"
              >
                Try Again
              </button>
            </div>

            <!-- Enhanced Transactions List -->
            <ul *ngIf="!loading && paginatedTransactions().length > 0" 
                class="divide-y divide-gray-200 transactions-list" 
                role="list"
                [attr.aria-label]="'List of ' + paginatedTransactions().length + ' transactions'">
              
              <li *ngFor="let statement of paginatedTransactions(); let i = index; trackBy: trackByTransactionId" 
                  class="transaction-item"
                  [ngClass]="{
                    'striped': i % 2 !== 0,
                    'interactive': interactive
                  }"
                  role="listitem"
                  [attr.aria-label]="'Transaction: ' + statement.transactionType + ' for ' + formatAmount(statement.actValue)"
                  [tabindex]="interactive ? '0' : null"
                  (click)="selectTransaction(statement)"
                  (keydown.enter)="selectTransaction(statement)"
                  (keydown.space)="selectTransaction(statement)">
                
                <!-- Transaction Icon -->
                <div class="transaction-icon">
                  <span class="icon-emoji" [title]="statement.transactionType">
                    {{ getTransactionIcon(statement.transactionType || '') }}
                  </span>
                </div>
                
                <!-- Transaction Content -->
                <div class="transaction-content">
                  <div class="transaction-primary">
                    <div class="transaction-info">
                      <p class="transaction-type" *ngIf="mergedConfig().showDescription">
                        {{ statement.transactionType }}
                      </p>
                      <p class="transaction-description">
                        {{ isValidDate(statement.label) ? (statement.label | date:'medium') : statement.label }}
                      </p>
                    </div>
                    
                    <!-- Transaction Amount -->
                    <div class="transaction-amount" *ngIf="mergedConfig().showAmount">
                      <p class="amount-points">{{ statement.transactionPoints }}</p>
                      <p class="amount-value" *ngIf="statement.actValue">
                        {{ formatAmount(statement.actValue) }}
                      </p>
                    </div>
                  </div>
                  
                  <!-- Transaction Metadata -->
                  <div class="transaction-meta" *ngIf="mergedConfig().showDate || mergedConfig().showTransactionDetails">
                    <span class="transaction-date" *ngIf="statement.loadDate && mergedConfig().showDate">
                      {{ isValidDate(statement.loadDate) ? (statement.loadDate.split("T")[0] | date:'mediumDate') : statement.loadDate }}
                    </span>
                    
                    <!-- Transaction Status - Removed as Statement interface doesn't have status property -->
                  </div>
                </div>

                <!-- Transaction Actions -->
                <div class="transaction-actions" *ngIf="interactive && !disabled">
                  <button
                    type="button"
                    class="action-btn details-btn"
                    (click)="onTransactionAction('details', statement); $event.stopPropagation()"
                    [attr.aria-label]="'View details for ' + statement.transactionType"
                    title="View Details"
                  >
                    <span class="action-icon">👁️</span>
                  </button>

                  <button
                    type="button"
                    class="action-btn export-btn"
                    (click)="onTransactionAction('export', statement); $event.stopPropagation()"
                    [attr.aria-label]="'Export ' + statement.transactionType"
                    title="Export Transaction"
                    *ngIf="mergedConfig().enableExport"
                  >
                    <span class="action-icon">📤</span>
                  </button>
                </div>
              </li>
            </ul>

            <!-- Enhanced Pagination -->
            <div class="pagination-wrapper" *ngIf="mergedConfig().enablePagination && totalPages() > 1">
              <div class="pagination-info">
                Showing {{ (currentPage() - 1) * mergedConfig().itemsPerPage + 1 }} to 
                {{ Math.min(currentPage() * mergedConfig().itemsPerPage, filteredTransactions().length) }} 
                of {{ filteredTransactions().length }} transactions
              </div>
              
              <div class="pagination-controls">
                <button
                  type="button"
                  class="btn btn-secondary page-btn"
                  (click)="changePage(currentPage() - 1)"
                  [disabled]="currentPage() === 1 || disabled"
                  [attr.aria-label]="'Go to previous page'"
                >
                  ← Previous
                </button>
                
                <span class="page-info">
                  Page {{ currentPage() }} of {{ totalPages() }}
                </span>
                
                <button
                  type="button"
                  class="btn btn-secondary page-btn"
                  (click)="changePage(currentPage() + 1)"
                  [disabled]="currentPage() === totalPages() || disabled"
                  [attr.aria-label]="'Go to next page'"
                >
                  Next →
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Enhanced Loading Overlay -->
  <div class="loading-overlay" *ngIf="isRefreshing()" role="status" aria-live="polite">
    <div class="loading-content">
      <div class="loading-spinner" aria-hidden="true"></div>
      <div class="loading-text">Refreshing transactions...</div>
    </div>
  </div>
</div>
