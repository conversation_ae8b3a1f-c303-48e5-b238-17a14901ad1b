import { Component, Injector, OnInit, Input, Output, EventEmitter, computed, signal, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import {
  MemberService,
  Statement,
  KeyCloakService,
  MemberProfile,
  LssConfig,
} from 'lp-client-api';
import { AbstractFormComponent } from '../../../shared/abstract.form.component';
import { CommonModule, DatePipe } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { HeadLogoComponent } from '../../../layout/head-logo/head-logo.component';

// Enhanced Transaction interfaces for security and functionality
interface TransactionConfig {
  showBalance: boolean;
  showFilters: boolean;
  enableSearch: boolean;
  enablePagination: boolean;
  itemsPerPage: number;
  autoRefresh: boolean;
  refreshInterval: number;
  showTransactionDetails: boolean;
  enableExport: boolean;
  showDate: boolean;
  showAmount: boolean;
  showDescription: boolean;
  groupByDate: boolean;
  sortOrder: 'asc' | 'desc';
  maxRetries: number;
  requestTimeout: number;
  compactView: boolean;
}

interface TransactionDisplayData {
  id: string;
  type: string;
  description: string;
  amount: number;
  currency: string;
  date: string;
  status: 'completed' | 'pending' | 'failed' | 'cancelled';
  reference?: string;
  category?: string;
  balance?: number;
  metadata?: Record<string, any>;
}

@Component({
  selector: 'lib-transactions',
  templateUrl: './transactions.component.html',
  styleUrls: ['./transactions.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    IonicModule,
    DatePipe,
    HeadLogoComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class TransactionsComponent extends AbstractFormComponent<Statement> {
  // Make Math available to template
  public Math = Math;

  // Standard UI inputs for LP-GO builder integration
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() disabled: boolean = false;

  // Enhanced Transaction specific inputs
  @Input() config: Partial<TransactionConfig> = {};
  @Input() initialTransactions: Statement[] = [];
  @Input() showLabels: boolean = true;
  @Input() darkMode: boolean = false;
  @Input() interactive: boolean = true;

  // Event outputs for security and functionality
  @Output() transactionSelected = new EventEmitter<Statement>();
  @Output() transactionAction = new EventEmitter<{ action: string; transaction: Statement }>();
  @Output() refreshRequested = new EventEmitter<void>();
  @Output() filterChanged = new EventEmitter<any>();
  @Output() pageChanged = new EventEmitter<number>();

  // Enhanced state management with signals
  isRefreshing = signal(false);
  currentPage = signal(1);
  searchTerm = signal('');
  selectedFilter = signal<string>('all');
  retryCount = signal(0);

  // Existing properties with enhancements
  searchRender = false;
  statements: Statement[] = [];
  beginDate?: any;
  endDate?: Date;
  profile?: MemberProfile;

  // Default configuration with security focus
  private defaultConfig: TransactionConfig = {
    showBalance: true,
    showFilters: true,
    enableSearch: true,
    enablePagination: true,
    itemsPerPage: 10,
    autoRefresh: false,
    refreshInterval: 300000, // 5 minutes
    showTransactionDetails: true,
    enableExport: false,
    showDate: true,
    showAmount: true,
    showDescription: true,
    groupByDate: false,
    sortOrder: 'desc',
    maxRetries: 3,
    requestTimeout: 30000,
    compactView: false
  };

  // Computed properties for enhanced functionality
  mergedConfig = computed(() => ({
    ...this.defaultConfig,
    ...this.config
  }));

  containerClasses = computed(() => {
    const base = 'transactions-container';
    const sizeClass = `size-${this.size}`;
    const variantClass = `variant-${this.variant}`;
    const roundedClass = `rounded-${this.rounded}`;
    const stateClasses = [
      this.disabled ? 'disabled' : '',
      this.darkMode ? 'dark' : '',
      this.isRefreshing() ? 'refreshing' : '',
      this.loading ? 'loading' : ''
    ].filter(Boolean);

    return [base, sizeClass, variantClass, roundedClass, ...stateClasses, this.className].filter(Boolean).join(' ');
  });

  filteredTransactions = computed(() => {
    let filtered = this.statements;
    
    // Apply search filter with security validation
    if (this.searchTerm()) {
      const term = this.sanitizeSearchTerm(this.searchTerm());
      filtered = filtered.filter(stmt => 
        this.matchesSearchTerm(stmt, term)
      );
    }

    // Apply type filter
    if (this.selectedFilter() !== 'all') {
      filtered = filtered.filter(stmt => 
        stmt.transactionType?.toLowerCase() === this.selectedFilter().toLowerCase()
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const dateA = new Date(a.loadDate || 0).getTime();
      const dateB = new Date(b.loadDate || 0).getTime();
      return this.mergedConfig().sortOrder === 'desc' ? dateB - dateA : dateA - dateB;
    });

    return filtered;
  });

  paginatedTransactions = computed(() => {
    const config = this.mergedConfig();
    if (!config.enablePagination) return this.filteredTransactions();
    
    const startIndex = (this.currentPage() - 1) * config.itemsPerPage;
    const endIndex = startIndex + config.itemsPerPage;
    return this.filteredTransactions().slice(startIndex, endIndex);
  });

  totalPages = computed(() => {
    const config = this.mergedConfig();
    if (!config.enablePagination) return 1;
    return Math.ceil(this.filteredTransactions().length / config.itemsPerPage);
  });

  constructor(
    injector: Injector,
    private memberService: MemberService,
    private kc: KeyCloakService,
    public lssConfig: LssConfig
  ) {
    super(injector);
    this.addGlobalSubscription(
      this.memberService.profileSubject.subscribe((data) => {
        this.profile = data;
        if (this.profile) {
          this._formState = this._formStateType.read;
        }
      })
    );

    // Initialize with provided transactions if available
    if (this.initialTransactions.length > 0) {
      this.statements = this.initialTransactions;
    }

    // Setup auto-refresh if enabled
    this.setupAutoRefresh();
  }

  override ionViewDidEnter(): void {
    super.ionViewDidEnter();
    this.loading = true;
    this.search();
  }

  // Enhanced search with security validation and retry logic
  search(retry?: boolean): void {
    if (!this.profile && !retry) {
      setTimeout(() => this.search(true), 500);
      return;
    }

    // Validate retry attempts for security
    if (this.retryCount() >= this.mergedConfig().maxRetries) {
      this.handleMaxRetriesReached();
      return;
    }

    let limit: number;

    this.showLoadingModal('Fetching your latest transactions').then(() => {
      var d = new Date();
      limit = this.mergedConfig().itemsPerPage;
      
      // Create timeout promise for security
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout')), this.mergedConfig().requestTimeout);
      });

      Promise.race([
        this.memberService.getTransactionHistory(
          undefined,
          undefined,
          0,
          limit
        ).toPromise(),
        timeoutPromise
      ]).then((body: any) => {
        this.handleTransactionSuccess(body);
      }).catch((error) => {
        this.handleTransactionError(error, retry);
      });
    });
  }

  // Enhanced transaction selection with security validation
  selectTransaction(statement: Statement): void {
    if (!this.interactive || this.disabled) return;
    
    // Validate transaction data before emitting
    if (this.isValidTransactionData(statement)) {
      this.transactionSelected.emit(statement);
    }
  }

  // Enhanced transaction actions with security validation
  onTransactionAction(action: string, statement: Statement): void {
    if (!this.interactive || this.disabled) return;
    
    // Validate action and transaction
    if (this.isValidAction(action) && this.isValidTransactionData(statement)) {
      this.transactionAction.emit({ action, transaction: statement });
    }
  }

  // Enhanced refresh functionality
  refreshTransactions(): void {
    if (this.disabled || this.isRefreshing()) return;
    
    this.isRefreshing.set(true);
    this.retryCount.set(0);
    this.refreshRequested.emit();
    
    this.search();
    
    // Reset refreshing state after delay
    setTimeout(() => {
      this.isRefreshing.set(false);
    }, 1000);
  }

  // Enhanced filtering with security validation
  updateSearchTerm(term: string): void {
    const sanitizedTerm = this.sanitizeSearchTerm(term);
    this.searchTerm.set(sanitizedTerm);
    this.currentPage.set(1); // Reset to first page
    this.filterChanged.emit({ search: sanitizedTerm, filter: this.selectedFilter() });
  }

  updateFilter(filter: string): void {
    if (this.isValidFilter(filter)) {
      this.selectedFilter.set(filter);
      this.currentPage.set(1); // Reset to first page
      this.filterChanged.emit({ search: this.searchTerm(), filter });
    }
  }

  // Enhanced pagination
  changePage(page: number): void {
    if (page >= 1 && page <= this.totalPages()) {
      this.currentPage.set(page);
      this.pageChanged.emit(page);
    }
  }

  // Security validation methods
  private sanitizeSearchTerm(term: string): string {
    // Remove potentially dangerous characters
    return term.replace(/[<>\"']/g, '').trim().substring(0, 100);
  }

  private isValidTransactionData(statement: Statement): boolean {
    return statement && 
           typeof statement === 'object' && 
           statement.hasOwnProperty('transactionType');
  }

  private isValidAction(action: string): boolean {
    const allowedActions = ['view', 'details', 'export', 'share', 'dispute'];
    return allowedActions.includes(action);
  }

  private isValidFilter(filter: string): boolean {
    const allowedFilters = ['all', 'debit', 'credit', 'transfer', 'payment'];
    return allowedFilters.includes(filter.toLowerCase());
  }

  private matchesSearchTerm(statement: Statement, term: string): boolean {
    const searchFields = [
      statement.transactionType,
      statement.label,
      statement.transactionPoints?.toString()
    ].filter(Boolean);

    return searchFields.some(field => 
      typeof field === 'string' && field.toLowerCase().includes(term.toLowerCase())
    );
  }

  // Enhanced error handling
  private handleTransactionSuccess(body: any): void {
    console.log('Transaction data received', body);
    if (body !== undefined && Array.isArray(body)) {
      this.statements = body;
      this.retryCount.set(0); // Reset retry count on success
    }
    this.dismissLoadingModal();
    this.isRefreshing.set(false);
  }

  private handleTransactionError(error: any, retry?: boolean): void {
    console.error('Transaction error:', error);
    this.retryCount.set(this.retryCount() + 1);
    
    this.dismissLoadingModal();
    this.isRefreshing.set(false);
    
    const errorMessage = this.getSecureErrorMessage(error);
    this.presentToast({
      message: errorMessage,
      color: 'danger',
      position: 'middle',
    });

    // Implement exponential backoff for retries
    if (this.retryCount() < this.mergedConfig().maxRetries) {
      const delay = Math.pow(2, this.retryCount()) * 1000; // Exponential backoff
      setTimeout(() => this.search(true), delay);
    }
  }

  private handleMaxRetriesReached(): void {
    this.presentToast({
      message: 'Unable to load transactions. Please try again later.',
      color: 'danger',
      position: 'middle',
    });
  }

  private getSecureErrorMessage(error: any): string {
    // Don't expose sensitive error details to users
    const secureMessages: Record<string, string> = {
      'timeout': 'Request timed out. Please try again.',
      'network': 'Network error. Please check your connection.',
      'unauthorized': 'Session expired. Please log in again.',
      'default': 'Oops, something went wrong!'
    };

    if (error?.message?.includes('timeout')) return secureMessages['timeout'];
    if (error?.message?.includes('network')) return secureMessages['network'];
    if (error?.status === 401) return secureMessages['unauthorized'];
    
    return secureMessages['default'];
  }

  private setupAutoRefresh(): void {
    if (this.mergedConfig().autoRefresh) {
      setInterval(() => {
        if (!this.disabled && !this.isRefreshing()) {
          this.refreshTransactions();
        }
      }, this.mergedConfig().refreshInterval);
    }
  }

  /**
   * Enhanced date validation with security considerations
   * @param value The value to check
   * @returns boolean indicating whether the value is a valid date
   */
  isValidDate(value: any): boolean {
    if (!value) return false;

    // Check if it's already a Date object
    if (value instanceof Date) return !isNaN(value.getTime());

    // Try to parse ISO format or timestamp with validation
    if (typeof value === 'string' || typeof value === 'number') {
      // Validate string length to prevent DoS attacks
      if (typeof value === 'string' && value.length > 50) return false;
      
      const date = new Date(value);
      const isValid = !isNaN(date.getTime());
      
      // Additional validation: ensure reasonable date range
      const year = date.getFullYear();
      return isValid && year >= 1900 && year <= 2100;
    }

    return false;
  }

  // Enhanced transaction type detection
  getTransactionIcon(transactionType: string): string {
    const icons: Record<string, string> = {
      'credit': '💰',
      'debit': '💳',
      'transfer': '🔄',
      'payment': '💸',
      'refund': '↩️',
      'fee': '📋',
      'default': '📄'
    };
    
    const normalizedType = transactionType?.toLowerCase() || '';
    return icons[normalizedType] || icons['default'];
  }

  // Enhanced amount formatting with security
  formatAmount(amount: any, currency: string = 'USD'): string {
    if (typeof amount !== 'number' || isNaN(amount)) return 'N/A';
    
    try {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
      }).format(amount);
    } catch (error) {
      // Fallback for invalid currency codes
      return `${amount.toFixed(2)}`;
    }
  }

  // Track by function for performance optimization
  trackByTransactionId(index: number, statement: Statement): string {
    // Create unique identifier from available Statement properties
    const transactionType = statement.transactionType || 'unknown';
    const loadDate = statement.loadDate || new Date().toISOString();
    const points = statement.transactionPoints || 0;
    return `${transactionType}_${loadDate}_${points}_${index}`;
  }
}
