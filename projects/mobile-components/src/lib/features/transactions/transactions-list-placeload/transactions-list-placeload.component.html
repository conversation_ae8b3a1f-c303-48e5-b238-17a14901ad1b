<div [class]="computedClasses()" [attr.data-testid]="'transactions-placeload-' + componentId()">
  
  <!-- Header Section -->
  @if (hasHeader) {
    <div [class]="headerClasses()">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
        {{ headerText }}
      </h3>
      @if (hasLoadingText) {
        <div class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
          <div class="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
          <span>{{ loadingText }}</span>
        </div>
      }
    </div>
  }

  <!-- Progress Bar -->
  @if (hasProgress) {
    <div [class]="progressClasses()">
      <div 
        class="h-full bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 ease-out"
        [style]="progressStyle"
        [attr.aria-valuenow]="currentProgress"
        [attr.aria-valuemin]="0"
        [attr.aria-valuemax]="100"
        role="progressbar"
        [attr.aria-label]="'Loading progress: ' + progressPercentage">
      </div>
    </div>
  }

  <!-- Loading Text (without header) -->
  @if (hasLoadingText && !hasHeader) {
    <div class="flex items-center justify-center space-x-2 mb-4 text-sm text-gray-500 dark:text-gray-400">
      <div class="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
      <span>{{ loadingText }}</span>
    </div>
  }

  <!-- Placeload Items List -->
  <div class="placeload-list space-y-2" role="list" aria-label="Loading transaction items">
    @for (item of items; track item.id) {
      <div 
        [class]="placeloadItemClasses()"
        role="listitem"
        [attr.aria-label]="'Loading transaction item ' + ($index + 1)">
        
        <!-- Transaction Icon Placeholder -->
        @if (shouldShowIcon) {
          <div class="flex-shrink-0">
            <div 
              [class]="shimmerClasses()"
              class="h-10 w-10 rounded-full"
              [style.width]="item.iconWidth"
              [style.height]="item.iconWidth"
              [attr.aria-hidden]="true">
            </div>
          </div>
        }

        <!-- Transaction Details Placeholder -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center justify-between mb-2">
            
            <!-- Merchant Name Placeholder -->
            @if (shouldShowMerchant) {
              <div class="flex-1">
                <div 
                  [class]="shimmerClasses()"
                  class="h-4 mb-1"
                  [style.width]="item.merchantWidth"
                  [attr.aria-hidden]="true">
                </div>
                <!-- Category/Description Placeholder -->
                @if (shouldShowCategory) {
                  <div 
                    [class]="shimmerClasses()"
                    class="h-3"
                    [style.width]="item.categoryWidth"
                    [attr.aria-hidden]="true">
                  </div>
                }
              </div>
            }

            <!-- Amount Placeholder -->
            @if (shouldShowAmount) {
              <div class="flex-shrink-0 ml-4">
                <div 
                  [class]="shimmerClasses()"
                  class="h-4 mb-1"
                  [style.width]="item.amountWidth"
                  [attr.aria-hidden]="true">
                </div>
                <!-- Status Placeholder -->
                @if (shouldShowStatus) {
                  <div 
                    [class]="shimmerClasses()"
                    class="h-3"
                    [style.width]="item.statusWidth"
                    [attr.aria-hidden]="true">
                  </div>
                }
              </div>
            }
          </div>

          <!-- Date/Time Placeholder -->
          @if (shouldShowDate) {
            <div class="flex justify-between items-center">
              <div 
                [class]="shimmerClasses()"
                class="h-3"
                [style.width]="item.dateWidth"
                [attr.aria-hidden]="true">
              </div>
              <!-- Additional details placeholder -->
              @if (variant === 'detailed') {
                <div 
                  [class]="shimmerClasses()"
                  class="h-3"
                  style="width: 40px"
                  [attr.aria-hidden]="true">
                </div>
              }
            </div>
          }
        </div>

        <!-- Action Button Placeholder (for detailed variant) -->
        @if (variant === 'detailed') {
          <div class="flex-shrink-0 ml-3">
            <div 
              [class]="shimmerClasses()"
              class="h-8 w-8 rounded-md"
              [attr.aria-hidden]="true">
            </div>
          </div>
        }
      </div>
    }
  </div>

  <!-- Compact Loading Indicators -->
  @if (variant === 'compact') {
    <div class="flex justify-center items-center mt-4 space-x-2">
      @for (dot of [1,2,3]; track dot) {
        <div 
          class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"
          [style.animation-delay]="(dot * 200) + 'ms'"
          [attr.aria-hidden]="true">
        </div>
      }
    </div>
  }

  <!-- Minimal Loading Text -->
  @if (variant === 'minimal' && hasLoadingText) {
    <div class="text-center mt-2">
      <span class="text-xs text-gray-400 dark:text-gray-500">{{ loadingText }}</span>
    </div>
  }

  <!-- Loading Summary (for accessibility) -->
  <div class="sr-only" aria-live="polite">
    @if (loadingState) {
      Loading {{ itemCount }} transaction items
      @if (hasProgress) {
        , {{ progressPercentage }} complete
      }
    } @else {
      Transaction loading complete
    }
  </div>

  <!-- Debug Info (development only) -->
  @if (false) {
    <div class="mt-4 p-2 bg-gray-100 dark:bg-gray-900 rounded text-xs">
      <div>Items: {{ itemCount }}</div>
      <div>Progress: {{ progressPercentage }}</div>
      <div>Elapsed: {{ getElapsedTime() }}ms</div>
      <div>State: {{ loadingState ? 'Loading' : 'Complete' }}</div>
    </div>
  }

</div>
