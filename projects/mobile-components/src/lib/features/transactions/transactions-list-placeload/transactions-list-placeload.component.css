
/* Base Component Styles */
.transactions-list-placeload {
  position: relative;
  font-family: inherit;
  line-height: 1.5;
}

/* Shimmer Animation */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-shimmer {
  background-size: 200px 100%;
  animation: shimmer 1.5s ease-in-out infinite;
}

/* Placeload Item Styles */
.placeload-item {
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease-in-out;
}

.placeload-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark .placeload-item:hover {
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
}

/* Shimmer Elements */
.shimmer {
  position: relative;
  overflow: hidden;
  background: linear-gradient(
    90deg,
    theme('colors.gray.200') 25%,
    theme('colors.gray.300') 50%,
    theme('colors.gray.200') 75%
  );
}

.dark .shimmer {
  background: linear-gradient(
    90deg,
    theme('colors.gray.700') 25%,
    theme('colors.gray.600') 50%,
    theme('colors.gray.700') 75%
  );
}

/* Enhanced shimmer with moving highlight */
.shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer-highlight 2s ease-in-out infinite;
}

.dark .shimmer::before {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
}

@keyframes shimmer-highlight {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Header Styles */
.placeload-header {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Progress Bar Styles */
.progress-bar {
  position: relative;
  overflow: hidden;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: progress-shine 2s ease-in-out infinite;
}

@keyframes progress-shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Loading Dots Animation */
@keyframes pulse-dot {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-pulse-dot {
  animation: pulse-dot 1.5s ease-in-out infinite;
}

/* Staggered Animation for List Items */
.placeload-list .placeload-item {
  animation: slideInUp 0.4s ease-out;
  animation-fill-mode: both;
}

.placeload-list .placeload-item:nth-child(1) { animation-delay: 0ms; }
.placeload-list .placeload-item:nth-child(2) { animation-delay: 100ms; }
.placeload-list .placeload-item:nth-child(3) { animation-delay: 200ms; }
.placeload-list .placeload-item:nth-child(4) { animation-delay: 300ms; }
.placeload-list .placeload-item:nth-child(5) { animation-delay: 400ms; }
.placeload-list .placeload-item:nth-child(n+6) { animation-delay: 500ms; }

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Variant Specific Styles */
.transactions-list-placeload.variant-compact .placeload-item {
  padding: 0.5rem;
  margin-bottom: 0.25rem;
}

.transactions-list-placeload.variant-compact .shimmer {
  height: 0.75rem;
}

.transactions-list-placeload.variant-detailed .placeload-item {
  padding: 1rem;
  border-radius: theme('borderRadius.lg');
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.transactions-list-placeload.variant-minimal {
  background: transparent;
}

.transactions-list-placeload.variant-minimal .placeload-item {
  background: transparent;
  border: none;
  padding: 0.25rem 0;
}

/* Size Variants */
.transactions-list-placeload.size-sm .shimmer {
  height: 0.75rem;
}

.transactions-list-placeload.size-sm .placeload-item {
  padding: 0.5rem;
}

.transactions-list-placeload.size-lg .shimmer {
  height: 1.25rem;
}

.transactions-list-placeload.size-lg .placeload-item {
  padding: 1.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .placeload-item {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
  }
  
  .placeload-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .shimmer {
    height: 0.875rem;
  }
}

@media (max-width: 480px) {
  .transactions-list-placeload {
    padding: 1rem;
  }
  
  .placeload-item {
    padding: 0.5rem;
    border-radius: theme('borderRadius.md');
  }
  
  .shimmer {
    height: 0.75rem;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .shimmer {
    background: linear-gradient(
      90deg,
      theme('colors.gray.400') 25%,
      theme('colors.gray.500') 50%,
      theme('colors.gray.400') 75%
    );
  }
  
  .dark .shimmer {
    background: linear-gradient(
      90deg,
      theme('colors.gray.500') 25%,
      theme('colors.gray.400') 50%,
      theme('colors.gray.500') 75%
    );
  }
  
  .placeload-item {
    border-width: 2px;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .animate-shimmer,
  .shimmer::before,
  .progress-bar::after,
  .animate-spin,
  .animate-pulse-dot,
  .placeload-item {
    animation: none !important;
  }
  
  .placeload-item:hover {
    transform: none;
  }
}

/* Print Styles */
@media print {
  .transactions-list-placeload {
    background: white !important;
    color: black !important;
    box-shadow: none;
  }
  
  .shimmer,
  .progress-bar,
  .animate-spin {
    display: none;
  }
  
  .placeload-item {
    border: 1px solid black;
    background: white !important;
  }
  
  .placeload-header::after {
    content: ' (Loading State)';
    font-style: italic;
  }
}

/* Focus and Accessibility */
.placeload-item:focus-within {
  outline: 2px solid theme('colors.blue.500');
  outline-offset: 2px;
}

/* Custom Loading States */
.transactions-list-placeload.loading-fast .animate-shimmer {
  animation-duration: 0.8s;
}

.transactions-list-placeload.loading-slow .animate-shimmer {
  animation-duration: 2.5s;
}

/* Error State (for future enhancement) */
.transactions-list-placeload.error .shimmer {
  background: linear-gradient(
    90deg,
    theme('colors.red.200') 25%,
    theme('colors.red.300') 50%,
    theme('colors.red.200') 75%
  );
}

.dark .transactions-list-placeload.error .shimmer {
  background: linear-gradient(
    90deg,
    theme('colors.red.800') 25%,
    theme('colors.red.700') 50%,
    theme('colors.red.800') 75%
  );
}

/* Success State (for completion) */
.transactions-list-placeload.success .shimmer {
  background: linear-gradient(
    90deg,
    theme('colors.green.200') 25%,
    theme('colors.green.300') 50%,
    theme('colors.green.200') 75%
  );
}

.dark .transactions-list-placeload.success .shimmer {
  background: linear-gradient(
    90deg,
    theme('colors.green.800') 25%,
    theme('colors.green.700') 50%,
    theme('colors.green.800') 75%
  );
}

/* Performance Optimization */
.placeload-item {
  contain: layout style paint;
  will-change: transform;
}

.shimmer {
  contain: layout style paint;
  will-change: background-position;
}

/* Custom Scrollbar */
.placeload-list::-webkit-scrollbar {
  width: 6px;
}

.placeload-list::-webkit-scrollbar-track {
  background: theme('colors.gray.100');
  border-radius: 3px;
}

.placeload-list::-webkit-scrollbar-thumb {
  background: theme('colors.gray.300');
  border-radius: 3px;
}

.placeload-list::-webkit-scrollbar-thumb:hover {
  background: theme('colors.gray.400');
}

.dark .placeload-list::-webkit-scrollbar-track {
  background: theme('colors.gray.800');
}

.dark .placeload-list::-webkit-scrollbar-thumb {
  background: theme('colors.gray.600');
}

.dark .placeload-list::-webkit-scrollbar-thumb:hover {
  background: theme('colors.gray.500');
}