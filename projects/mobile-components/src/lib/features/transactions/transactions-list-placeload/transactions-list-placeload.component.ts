import { Component, Input, Output, EventEmitter, computed, signal, inject, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

// Security and utility imports
import { Subject, interval, takeUntil, map } from 'rxjs';

// Types for loading placeholders
export interface PlaceloadConfig {
  rows: number;
  showIcon: boolean;
  showAmount: boolean;
  showDate: boolean;
  showMerchant: boolean;
  showCategory: boolean;
  showStatus: boolean;
  animationDuration: number;
  pulseDelay: number;
}

export interface PlaceloadItem {
  id: string;
  iconWidth: string;
  merchantWidth: string;
  categoryWidth: string;
  amountWidth: string;
  dateWidth: string;
  statusWidth: string;
}

@Component({
  selector: 'lib-transactions-list-placeload',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './transactions-list-placeload.component.html',
  styleUrl: './transactions-list-placeload.component.css'
})
export class TransactionsListPlaceloadComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();
  private readonly sanitizer = inject(DomSanitizer);

  // Input properties for LP-GO builder integration
  @Input() className: string = '';
  @Input() size: 'sm' | 'md' | 'lg' = 'md';
  @Input() variant: 'default' | 'compact' | 'detailed' | 'minimal' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() disabled: boolean = false;

  // Placeload configuration
  @Input() config: Partial<PlaceloadConfig> = {};
  @Input() rows: number = 5;
  @Input() animated: boolean = true;
  @Input() showShimmer: boolean = true;
  @Input() randomWidths: boolean = true;

  // Layout options
  @Input() showHeader: boolean = false;
  @Input() headerText: string = 'Loading Transactions...';
  @Input() showLoadingText: boolean = true;
  @Input() loadingText: string = 'Loading transactions';
  @Input() showProgress: boolean = false;

  // Timing controls
  @Input() minLoadTime: number = 1000; // Minimum loading time in ms
  @Input() maxLoadTime: number = 5000; // Maximum loading time in ms
  @Input() pulseInterval: number = 1500; // Pulse animation interval

  // Event outputs
  @Output() loadingStart = new EventEmitter<void>();
  @Output() loadingProgress = new EventEmitter<number>();
  @Output() loadingComplete = new EventEmitter<void>();
  @Output() loadingTimeout = new EventEmitter<void>();

  // Component state
  private readonly componentId = signal<string>(`placeload-${Math.random().toString(36).substr(2, 9)}`);
  private readonly isLoading = signal<boolean>(true);
  private readonly progress = signal<number>(0);
  private readonly loadingStartTime = signal<number>(0);

  // Default configuration
  private readonly defaultConfig: PlaceloadConfig = {
    rows: 5,
    showIcon: true,
    showAmount: true,
    showDate: true,
    showMerchant: true,
    showCategory: true,
    showStatus: true,
    animationDuration: 1500,
    pulseDelay: 200
  };

  // Generated placeload items
  private readonly placeloadItems = signal<PlaceloadItem[]>([]);

  // Computed properties
  public readonly resolvedConfig = computed(() => ({
    ...this.defaultConfig,
    ...this.config,
    rows: this.rows || this.defaultConfig.rows
  }));

  public readonly computedClasses = computed(() => {
    const baseClasses = [
      'transactions-list-placeload',
      'w-full',
      'space-y-2',
      'transition-all duration-200'
    ];

    // Size classes
    const sizeClasses = {
      sm: ['text-sm'],
      md: ['text-base'],
      lg: ['text-lg']
    };

    // Variant classes
    const variantClasses = {
      default: ['bg-white dark:bg-gray-800', 'rounded-lg', 'p-4'],
      compact: ['bg-gray-50 dark:bg-gray-900', 'rounded-md', 'p-3'],
      detailed: ['bg-white dark:bg-gray-800', 'rounded-xl', 'p-6', 'shadow-sm'],
      minimal: ['bg-transparent', 'p-2']
    };

    // Rounded classes
    const roundedClasses = {
      none: ['rounded-none'],
      sm: ['rounded-sm'],
      md: ['rounded-md'],
      lg: ['rounded-lg'],
      full: ['rounded-full']
    };

    // State classes
    const stateClasses = this.disabled ? ['opacity-50', 'pointer-events-none'] : [];

    return [
      ...baseClasses,
      ...sizeClasses[this.size],
      ...variantClasses[this.variant],
      ...roundedClasses[this.rounded],
      ...stateClasses,
      this.className
    ].filter(Boolean).join(' ');
  });

  public readonly placeloadItemClasses = computed(() => {
    const baseClasses = [
      'placeload-item',
      'flex items-center',
      'space-x-3',
      'p-3',
      'border border-gray-100 dark:border-gray-700',
      'bg-white dark:bg-gray-800',
      'transition-all duration-200'
    ];

    const sizeClasses = {
      sm: ['p-2', 'space-x-2'],
      md: ['p-3', 'space-x-3'],
      lg: ['p-4', 'space-x-4']
    };

    const roundedClasses = {
      none: ['rounded-none'],
      sm: ['rounded-sm'],
      md: ['rounded-md'],
      lg: ['rounded-lg'],
      full: ['rounded-full']
    };

    return [
      ...baseClasses,
      ...sizeClasses[this.size],
      ...roundedClasses[this.rounded]
    ].filter(Boolean).join(' ');
  });

  public readonly shimmerClasses = computed(() => [
    'shimmer',
    'bg-gradient-to-r',
    'from-gray-200 via-gray-300 to-gray-200',
    'dark:from-gray-700 dark:via-gray-600 dark:to-gray-700',
    this.animated && this.showShimmer ? 'animate-shimmer' : '',
    this.rounded === 'full' ? 'rounded-full' : `rounded-${this.rounded}`
  ].filter(Boolean).join(' '));

  public readonly headerClasses = computed(() => [
    'placeload-header',
    'flex items-center justify-between',
    'mb-4 pb-2',
    'border-b border-gray-200 dark:border-gray-700'
  ].join(' '));

  public readonly progressClasses = computed(() => [
    'progress-bar',
    'w-full h-1',
    'bg-gray-200 dark:bg-gray-700',
    'rounded-full overflow-hidden',
    'mb-4'
  ].join(' '));

  ngOnInit(): void {
    this.generatePlaceloadItems();
    this.startLoadingProgress();
    this.loadingStart.emit();
    this.loadingStartTime.set(Date.now());
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private generatePlaceloadItems(): void {
    const config = this.resolvedConfig();
    const items: PlaceloadItem[] = [];

    for (let i = 0; i < config.rows; i++) {
      items.push({
        id: `item-${i}`,
        iconWidth: this.getRandomWidth(20, 40),
        merchantWidth: this.getRandomWidth(80, 140),
        categoryWidth: this.getRandomWidth(60, 100),
        amountWidth: this.getRandomWidth(60, 80),
        dateWidth: this.getRandomWidth(70, 90),
        statusWidth: this.getRandomWidth(50, 70)
      });
    }

    this.placeloadItems.set(items);
  }

  private getRandomWidth(min: number, max: number): string {
    if (!this.randomWidths) {
      return `${(min + max) / 2}px`;
    }
    const width = Math.floor(Math.random() * (max - min + 1)) + min;
    return `${width}px`;
  }

  private startLoadingProgress(): void {
    if (!this.showProgress) return;

    const duration = Math.random() * (this.maxLoadTime - this.minLoadTime) + this.minLoadTime;
    const steps = 100;
    const stepDuration = duration / steps;

    interval(stepDuration).pipe(
      takeUntil(this.destroy$),
      map((step) => Math.min((step + 1) / steps * 100, 100))
    ).subscribe({
      next: (progress) => {
        this.progress.set(progress);
        this.loadingProgress.emit(progress);
        
        if (progress >= 100) {
          this.completeLoading();
        }
      }
    });

    // Timeout handling
    setTimeout(() => {
      if (this.isLoading()) {
        this.loadingTimeout.emit();
      }
    }, this.maxLoadTime + 1000);
  }

  private completeLoading(): void {
    this.isLoading.set(false);
    this.loadingComplete.emit();
  }

  // Public methods
  public restartLoading(): void {
    this.isLoading.set(true);
    this.progress.set(0);
    this.generatePlaceloadItems();
    this.startLoadingProgress();
    this.loadingStart.emit();
    this.loadingStartTime.set(Date.now());
  }

  public stopLoading(): void {
    this.isLoading.set(false);
    this.progress.set(100);
    this.loadingComplete.emit();
  }

  public getElapsedTime(): number {
    return Date.now() - this.loadingStartTime();
  }

  public getPlaceloadItem(index: number): PlaceloadItem | undefined {
    return this.placeloadItems()[index];
  }

  // Security methods
  public sanitizeHtml(html: string): SafeHtml {
    return this.sanitizer.sanitize(1, html) || '';
  }

  // Getters for template
  get loadingState(): boolean {
    return this.isLoading();
  }

  get currentProgress(): number {
    return this.progress();
  }

  get items(): PlaceloadItem[] {
    return this.placeloadItems();
  }

  get itemCount(): number {
    return this.resolvedConfig().rows;
  }

  get hasHeader(): boolean {
    return this.showHeader && !!this.headerText;
  }

  get hasProgress(): boolean {
    return this.showProgress && this.loadingState;
  }

  get hasLoadingText(): boolean {
    return this.showLoadingText && !!this.loadingText;
  }

  get shouldShowIcon(): boolean {
    return this.resolvedConfig().showIcon;
  }

  get shouldShowMerchant(): boolean {
    return this.resolvedConfig().showMerchant;
  }

  get shouldShowCategory(): boolean {
    return this.resolvedConfig().showCategory;
  }

  get shouldShowAmount(): boolean {
    return this.resolvedConfig().showAmount;
  }

  get shouldShowDate(): boolean {
    return this.resolvedConfig().showDate;
  }

  get shouldShowStatus(): boolean {
    return this.resolvedConfig().showStatus;
  }

  get progressPercentage(): string {
    return `${this.currentProgress}%`;
  }

  get progressStyle(): { width: string } {
    return { width: this.progressPercentage };
  }
}
