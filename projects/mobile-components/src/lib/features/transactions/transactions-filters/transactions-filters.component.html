<div [class]="computedClasses()" [attr.data-testid]="'transactions-filters-' + filterId()">
  <!-- Header Section -->
  <div class="filter-header flex items-center justify-between mb-4">
    <div class="flex items-center space-x-2">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
        Filters
      </h3>
      @if (hasActiveFilters) {
        <span [class]="activeFilterClasses()">
          {{ filterCount }} active
        </span>
      }
    </div>
    
    <div class="flex items-center space-x-2">
      @if (hasActiveFilters && showClearAll) {
        <button
          type="button"
          (click)="clearAllFilters()"
          class="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 transition-colors"
          [disabled]="disabled"
          aria-label="Clear all filters">
          Clear All
        </button>
      }
      
      @if (variant !== 'minimal') {
        <button
          type="button"
          (click)="toggleExpanded()"
          class="p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          [disabled]="disabled"
          [attr.aria-expanded]="expandedState"
          aria-label="Toggle filter options">
          <svg class="w-5 h-5 text-gray-500 transition-transform" 
               [class.rotate-180]="expandedState"
               fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </button>
      }
    </div>
  </div>

  <!-- Quick Filters -->
  @if (resolvedConfig().enableQuickFilters && quickFilterOptions.length > 0) {
    <div class="quick-filters mb-4">
      <div class="flex flex-wrap gap-2">
        @for (filter of quickFilterOptions; track filter.value) {
          <button
            type="button"
            (click)="selectQuickFilter(filter.value)"
            [class]="filterButtonClasses()"
            [disabled]="disabled"
            [attr.aria-label]="'Apply ' + filter.label + ' filter'">
            @if (filter.icon) {
              <span class="inline-block w-4 h-4 mr-1" [attr.data-icon]="filter.icon"></span>
            }
            {{ filter.label }}
          </button>
        }
      </div>
    </div>
  }

  <!-- Search Filter -->
  @if (resolvedConfig().showSearch) {
    <div class="search-filter mb-4">
      <label for="search-input" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        Search Transactions
      </label>
      <input
        id="search-input"
        type="text"
        [formControl]="$any(filterForm.get('searchTerm'))"
        [class]="searchInputClasses()"
        placeholder="Search by description, merchant, or reference..."
        [disabled]="disabled"
        autocomplete="off"
        [attr.aria-describedby]="'search-help'">
      <p id="search-help" class="text-xs text-gray-500 dark:text-gray-400 mt-1">
        Search across transaction descriptions, merchant names, and reference numbers
      </p>
    </div>
  }

  <!-- Expandable Filters -->
  <div class="filter-sections" [class.hidden]="!expandedState && variant !== 'expanded' && variant !== 'minimal'">
    <form [formGroup]="filterForm" class="space-y-4">
      
      <!-- Date Range Filter -->
      @if (resolvedConfig().showDateRange) {
        <div class="date-range-filter">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Date Range</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <label for="start-date" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">From</label>
              <input
                id="start-date"
                type="date"
                formControlName="startDate"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                [disabled]="disabled">
            </div>
            <div>
              <label for="end-date" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">To</label>
              <input
                id="end-date"
                type="date"
                formControlName="endDate"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                [disabled]="disabled">
            </div>
          </div>
        </div>
      }

      <!-- Amount Range Filter -->
      @if (resolvedConfig().showAmountRange) {
        <div class="amount-range-filter">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Amount Range</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <label for="min-amount" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">Minimum</label>
              <input
                id="min-amount"
                type="number"
                formControlName="minAmount"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                [placeholder]="formatCurrency(0)"
                min="0"
                step="0.01"
                [disabled]="disabled">
            </div>
            <div>
              <label for="max-amount" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">Maximum</label>
              <input
                id="max-amount"
                type="number"
                formControlName="maxAmount"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                [placeholder]="formatCurrency(maxAmount)"
                min="0"
                step="0.01"
                [max]="maxAmount"
                [disabled]="disabled">
            </div>
          </div>
        </div>
      }

      <!-- Transaction Types Filter -->
      @if (resolvedConfig().showTransactionTypes && transactionTypes.length > 0) {
        <div class="transaction-types-filter">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Transaction Types</h4>
          <div class="flex flex-wrap gap-2">
            @for (type of transactionTypes; track type.value) {
              <button
                type="button"
                (click)="toggleFilterValue('transactionTypes', type.value)"
                [class]="isFilterActive('transactionTypes', type.value) ? activeFilterClasses() : filterButtonClasses()"
                [disabled]="disabled"
                [attr.aria-pressed]="isFilterActive('transactionTypes', type.value)"
                [attr.aria-label]="'Toggle ' + type.label + ' transaction type filter'">
                {{ type.label }}
                @if (type.count !== undefined) {
                  <span class="ml-1 text-xs opacity-75">({{ type.count }})</span>
                }
              </button>
            }
          </div>
        </div>
      }

      <!-- Categories Filter -->
      @if (resolvedConfig().showCategories && categories.length > 0) {
        <div class="categories-filter">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Categories</h4>
          <div class="flex flex-wrap gap-2">
            @for (category of categories; track category.value) {
              <button
                type="button"
                (click)="toggleFilterValue('categories', category.value)"
                [class]="isFilterActive('categories', category.value) ? activeFilterClasses() : filterButtonClasses()"
                [disabled]="disabled"
                [attr.aria-pressed]="isFilterActive('categories', category.value)"
                [attr.aria-label]="'Toggle ' + category.label + ' category filter'">
                {{ category.label }}
                @if (category.count !== undefined) {
                  <span class="ml-1 text-xs opacity-75">({{ category.count }})</span>
                }
              </button>
            }
          </div>
        </div>
      }

      <!-- Merchants Filter -->
      @if (resolvedConfig().showMerchants && merchants.length > 0) {
        <div class="merchants-filter">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Merchants</h4>
          <div class="flex flex-wrap gap-2">
            @for (merchant of merchants; track merchant.value) {
              <button
                type="button"
                (click)="toggleFilterValue('merchants', merchant.value)"
                [class]="isFilterActive('merchants', merchant.value) ? activeFilterClasses() : filterButtonClasses()"
                [disabled]="disabled"
                [attr.aria-pressed]="isFilterActive('merchants', merchant.value)"
                [attr.aria-label]="'Toggle ' + merchant.label + ' merchant filter'">
                {{ merchant.label }}
                @if (merchant.count !== undefined) {
                  <span class="ml-1 text-xs opacity-75">({{ merchant.count }})</span>
                }
              </button>
            }
          </div>
        </div>
      }

      <!-- Status Filter -->
      @if (resolvedConfig().showStatus && statusOptions.length > 0) {
        <div class="status-filter">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</h4>
          <div class="flex flex-wrap gap-2">
            @for (status of statusOptions; track status.value) {
              <button
                type="button"
                (click)="toggleFilterValue('status', status.value)"
                [class]="isFilterActive('status', status.value) ? activeFilterClasses() : filterButtonClasses()"
                [disabled]="disabled"
                [attr.aria-pressed]="isFilterActive('status', status.value)"
                [attr.aria-label]="'Toggle ' + status.label + ' status filter'">
                {{ status.label }}
                @if (status.count !== undefined) {
                  <span class="ml-1 text-xs opacity-75">({{ status.count }})</span>
                }
              </button>
            }
          </div>
        </div>
      }

      <!-- Sort Options -->
      @if (resolvedConfig().showSort) {
        <div class="sort-options">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Sort By</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <label for="sort-by" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">Field</label>
              <select
                id="sort-by"
                formControlName="sortBy"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                [disabled]="disabled">
                <option value="date">Date</option>
                <option value="amount">Amount</option>
                <option value="merchant">Merchant</option>
                <option value="category">Category</option>
                <option value="description">Description</option>
                @for (option of sortOptions; track option.value) {
                  <option [value]="option.value">{{ option.label }}</option>
                }
              </select>
            </div>
            <div>
              <label for="sort-order" class="block text-xs text-gray-600 dark:text-gray-400 mb-1">Order</label>
              <select
                id="sort-order"
                formControlName="sortOrder"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                [disabled]="disabled">
                <option value="asc">Ascending</option>
                <option value="desc">Descending</option>
              </select>
            </div>
          </div>
        </div>
      }

    </form>

    <!-- Apply Button (if enabled) -->
    @if (showApplyButton) {
      <div class="filter-actions mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <button
          type="button"
          (click)="applyFilters(buildFilterObject())"
          class="w-full md:w-auto px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
          [disabled]="disabled || !isFormValid"
          [class.opacity-50]="!isFormValid">
          Apply Filters
        </button>
      </div>
    }
  </div>

  <!-- Validation Errors -->
  @if (hasValidationErrors) {
    <div class="validation-errors mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
      <h4 class="text-sm font-medium text-red-800 dark:text-red-200 mb-1">Validation Errors:</h4>
      <ul class="text-sm text-red-700 dark:text-red-300 list-disc list-inside">
        @for (error of errors; track error) {
          <li>{{ error }}</li>
        }
      </ul>
    </div>
  }

  <!-- Active Filters Summary (Mobile) -->
  @if (hasActiveFilters && variant === 'compact') {
    <div class="active-filters-summary mt-4 md:hidden">
      <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Active Filters:</h4>
      <div class="flex flex-wrap gap-1">
        @if (filterForm.get('searchTerm')?.value) {
          <span [class]="activeFilterClasses()">
            Search: "{{ filterForm.get('searchTerm')?.value }}"
          </span>
        }
        @if (filterForm.get('startDate')?.value || filterForm.get('endDate')?.value) {
          <span [class]="activeFilterClasses()">
            Date Range
          </span>
        }
        @if (filterForm.get('minAmount')?.value || filterForm.get('maxAmount')?.value) {
          <span [class]="activeFilterClasses()">
            Amount Range
          </span>
        }
        <!-- Add more active filter summaries as needed -->
      </div>
    </div>
  }

</div>
