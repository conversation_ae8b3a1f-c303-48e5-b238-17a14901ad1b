
/* Base Component Styles */
.transactions-filters {
  position: relative;
  font-family: inherit;
  line-height: 1.5;
}

/* Filter Header Styles */
.filter-header {
  border-bottom: 1px solid theme('colors.gray.200');
  padding-bottom: 0.75rem;
  margin-bottom: 1rem;
}

.dark .filter-header {
  border-bottom-color: theme('colors.gray.700');
}

/* Quick Filters Styles */
.quick-filters {
  background: theme('colors.gray.50');
  border-radius: theme('borderRadius.md');
  padding: 0.75rem;
  margin-bottom: 1rem;
}

.dark .quick-filters {
  background: theme('colors.gray.900');
}

/* Search Filter Styles */
.search-filter input {
  transition: all 0.2s ease-in-out;
}

.search-filter input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.dark .search-filter input:focus {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
}

/* Filter Section Styles */
.filter-sections {
  transition: all 0.3s ease-in-out;
}

.filter-sections.hidden {
  opacity: 0;
  max-height: 0;
  overflow: hidden;
}

/* Date Range Filter Styles */
.date-range-filter input[type="date"] {
  transition: all 0.2s ease-in-out;
}

.date-range-filter input[type="date"]:focus {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

/* Amount Range Filter Styles */
.amount-range-filter input[type="number"] {
  transition: all 0.2s ease-in-out;
}

.amount-range-filter input[type="number"]:focus {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

/* Remove number input arrows for cleaner look */
.amount-range-filter input[type="number"]::-webkit-outer-spin-button,
.amount-range-filter input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.amount-range-filter input[type="number"] {
  -moz-appearance: textfield;
}

/* Filter Button Styles */
.filter-sections button {
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.filter-sections button:hover {
  transform: translateY(-1px);
}

.filter-sections button:active {
  transform: translateY(0);
}

/* Active Filter Button Ripple Effect */
.filter-sections button.active::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(59, 130, 246, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  to {
    width: 100px;
    height: 100px;
    opacity: 0;
  }
}

/* Sort Options Styles */
.sort-options select {
  transition: all 0.2s ease-in-out;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

.sort-options select:focus {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

/* Dark mode select arrow */
.dark .sort-options select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%39ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

/* Filter Actions Styles */
.filter-actions button {
  transition: all 0.2s ease-in-out;
  font-weight: 500;
}

.filter-actions button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.filter-actions button:active:not(:disabled) {
  transform: translateY(0);
}

/* Validation Errors Styles */
.validation-errors {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Active Filters Summary Styles */
.active-filters-summary {
  background: linear-gradient(135deg, theme('colors.blue.50'), theme('colors.indigo.50'));
  border: 1px solid theme('colors.blue.200');
  border-radius: theme('borderRadius.lg');
  padding: 0.75rem;
}

.dark .active-filters-summary {
  background: linear-gradient(135deg, theme('colors.blue.900/20'), theme('colors.indigo.900/20'));
  border-color: theme('colors.blue.800');
}

/* Responsive Design */
@media (max-width: 768px) {
  .transactions-filters {
    padding: 1rem;
  }
  
  .filter-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .quick-filters .flex {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .quick-filters button {
    width: 100%;
    justify-content: center;
  }
  
  .filter-sections .grid {
    grid-template-columns: 1fr;
  }
  
  .filter-sections .flex {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .filter-sections button {
    width: 100%;
    justify-content: center;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .transactions-filters {
    border-width: 2px;
  }
  
  .filter-sections button {
    border-width: 2px;
    font-weight: 600;
  }
  
  .filter-sections input,
  .filter-sections select {
    border-width: 2px;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .transactions-filters *,
  .transactions-filters *::before,
  .transactions-filters *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print Styles */
@media print {
  .transactions-filters {
    background: white !important;
    color: black !important;
    border: 1px solid black;
    box-shadow: none;
  }
  
  .filter-sections button,
  .filter-actions button {
    display: none;
  }
  
  .active-filters-summary {
    background: white !important;
    border: 1px solid black;
  }
}

/* Focus Visible Support */
.transactions-filters button:focus-visible,
.transactions-filters input:focus-visible,
.transactions-filters select:focus-visible {
  outline: 2px solid theme('colors.blue.500');
  outline-offset: 2px;
}

/* Custom Scrollbar for Filter Sections */
.filter-sections::-webkit-scrollbar {
  width: 6px;
}

.filter-sections::-webkit-scrollbar-track {
  background: theme('colors.gray.100');
  border-radius: 3px;
}

.filter-sections::-webkit-scrollbar-thumb {
  background: theme('colors.gray.300');
  border-radius: 3px;
}

.filter-sections::-webkit-scrollbar-thumb:hover {
  background: theme('colors.gray.400');
}

.dark .filter-sections::-webkit-scrollbar-track {
  background: theme('colors.gray.800');
}

.dark .filter-sections::-webkit-scrollbar-thumb {
  background: theme('colors.gray.600');
}

.dark .filter-sections::-webkit-scrollbar-thumb:hover {
  background: theme('colors.gray.500');
}

/* Loading State (for future enhancement) */
.transactions-filters.loading {
  pointer-events: none;
  opacity: 0.7;
}

.transactions-filters.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.dark .transactions-filters.loading::after {
  background: rgba(0, 0, 0, 0.8);
}

/* Size Variants */
.transactions-filters.size-sm {
  font-size: 0.875rem;
}

.transactions-filters.size-sm .filter-header h3 {
  font-size: 1rem;
}

.transactions-filters.size-lg {
  font-size: 1.125rem;
}

.transactions-filters.size-lg .filter-header h3 {
  font-size: 1.25rem;
}

/* Variant Specific Styles */
.transactions-filters.variant-compact {
  padding: 0.75rem;
}

.transactions-filters.variant-compact .filter-header {
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
}

.transactions-filters.variant-compact .quick-filters,
.transactions-filters.variant-compact .search-filter {
  margin-bottom: 0.5rem;
}

.transactions-filters.variant-minimal {
  background: transparent;
  border: none;
  padding: 0.5rem;
}

.transactions-filters.variant-minimal .filter-header {
  border-bottom: none;
  margin-bottom: 0.5rem;
  padding-bottom: 0;
}

.transactions-filters.variant-expanded .filter-sections {
  opacity: 1;
  max-height: none;
  overflow: visible;
}