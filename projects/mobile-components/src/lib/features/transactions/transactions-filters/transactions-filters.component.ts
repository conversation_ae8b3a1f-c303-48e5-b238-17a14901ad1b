import { Component, Input, Output, EventEmitter, computed, signal, inject, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, FormControl } from '@angular/forms';
import { Dom<PERSON>anitizer, SafeHtml } from '@angular/platform-browser';

// Security and validation imports
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';

// Types for transactions filters
export interface TransactionFilter {
  id: string;
  dateRange?: {
    startDate: Date | null;
    endDate: Date | null;
  };
  amountRange?: {
    minAmount: number | null;
    maxAmount: number | null;
  };
  transactionTypes?: string[];
  categories?: string[];
  merchants?: string[];
  status?: string[];
  searchTerm?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface FilterOption {
  value: string;
  label: string;
  count?: number;
  icon?: string;
}

export interface FilterConfig {
  showDateRange: boolean;
  showAmountRange: boolean;
  showTransactionTypes: boolean;
  showCategories: boolean;
  showMerchants: boolean;
  showStatus: boolean;
  showSearch: boolean;
  showSort: boolean;
  enableQuickFilters: boolean;
  maxFilterDisplay: number;
}

@Component({
  selector: 'lib-transactions-filters',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './transactions-filters.component.html',
  styleUrl: './transactions-filters.component.css'
})
export class TransactionsFiltersComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();
  private readonly formBuilder = inject(FormBuilder);
  private readonly sanitizer = inject(DomSanitizer);

  // Input properties for LP-GO builder integration
  @Input() className: string = '';
  @Input() size: 'sm' | 'md' | 'lg' = 'md';
  @Input() variant: 'default' | 'compact' | 'expanded' | 'minimal' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() disabled: boolean = false;

  // Filter configuration
  @Input() config: Partial<FilterConfig> = {};
  @Input() transactionTypes: FilterOption[] = [];
  @Input() categories: FilterOption[] = [];
  @Input() merchants: FilterOption[] = [];
  @Input() statusOptions: FilterOption[] = [];
  @Input() sortOptions: FilterOption[] = [];
  @Input() maxAmount: number = 1000000;
  @Input() currency: string = 'USD';

  // Initial values
  @Input() initialFilters: Partial<TransactionFilter> = {};
  @Input() showClearAll: boolean = true;
  @Input() showApplyButton: boolean = false;
  @Input() debounceMs: number = 300;

  // Event outputs
  @Output() filtersChange = new EventEmitter<TransactionFilter>();
  @Output() filterApplied = new EventEmitter<TransactionFilter>();
  @Output() filterCleared = new EventEmitter<void>();
  @Output() quickFilterSelected = new EventEmitter<string>();

  // Component state
  private readonly filterId = signal<string>(`filter-${Math.random().toString(36).substr(2, 9)}`);
  private readonly isExpanded = signal<boolean>(false);
  private readonly activeFiltersCount = signal<number>(0);

  // Form and validation
  public readonly filterForm: FormGroup;
  private readonly validationErrors = signal<string[]>([]);

  // Default configuration
  private readonly defaultConfig: FilterConfig = {
    showDateRange: true,
    showAmountRange: true,
    showTransactionTypes: true,
    showCategories: true,
    showMerchants: true,
    showStatus: true,
    showSearch: true,
    showSort: true,
    enableQuickFilters: true,
    maxFilterDisplay: 5
  };

  // Quick filter presets
  private readonly quickFilters = signal<FilterOption[]>([
    { value: 'today', label: 'Today', icon: 'calendar' },
    { value: 'week', label: 'This Week', icon: 'calendar-week' },
    { value: 'month', label: 'This Month', icon: 'calendar-month' },
    { value: 'income', label: 'Income Only', icon: 'arrow-down' },
    { value: 'expenses', label: 'Expenses Only', icon: 'arrow-up' },
    { value: 'pending', label: 'Pending', icon: 'clock' }
  ]);

  // Computed properties
  public readonly resolvedConfig = computed(() => ({
    ...this.defaultConfig,
    ...this.config
  }));

  public readonly computedClasses = computed(() => {
    const baseClasses = [
      'transactions-filters',
      'bg-white dark:bg-gray-800',
      'border border-gray-200 dark:border-gray-700',
      'transition-all duration-200'
    ];

    // Size classes
    const sizeClasses = {
      sm: ['p-3', 'text-sm'],
      md: ['p-4', 'text-base'],
      lg: ['p-6', 'text-lg']
    };

    // Variant classes
    const variantClasses = {
      default: ['shadow-sm'],
      compact: ['shadow-none', 'border-0', 'bg-gray-50 dark:bg-gray-900'],
      expanded: ['shadow-lg', 'min-h-64'],
      minimal: ['border-0', 'bg-transparent', 'p-2']
    };

    // Rounded classes
    const roundedClasses = {
      none: ['rounded-none'],
      sm: ['rounded-sm'],
      md: ['rounded-md'],
      lg: ['rounded-lg'],
      full: ['rounded-full']
    };

    // State classes
    const stateClasses = this.disabled ? ['opacity-50', 'pointer-events-none'] : [];

    return [
      ...baseClasses,
      ...sizeClasses[this.size],
      ...variantClasses[this.variant],
      ...roundedClasses[this.rounded],
      ...stateClasses,
      this.className
    ].filter(Boolean).join(' ');
  });

  public readonly searchInputClasses = computed(() => [
    'w-full px-3 py-2',
    'border border-gray-300 dark:border-gray-600',
    'rounded-md',
    'bg-white dark:bg-gray-700',
    'text-gray-900 dark:text-gray-100',
    'placeholder-gray-500 dark:placeholder-gray-400',
    'focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
    'transition-colors duration-200',
    this.size === 'sm' ? 'text-sm py-1' : '',
    this.size === 'lg' ? 'text-lg py-3' : ''
  ].filter(Boolean).join(' '));

  public readonly filterButtonClasses = computed(() => [
    'px-3 py-1 text-sm rounded-full',
    'border transition-all duration-200',
    'hover:shadow-sm',
    'focus:outline-none focus:ring-2 focus:ring-blue-500',
    'bg-gray-100 dark:bg-gray-700',
    'text-gray-700 dark:text-gray-300',
    'border-gray-300 dark:border-gray-600',
    'hover:bg-gray-200 dark:hover:bg-gray-600'
  ].join(' '));

  public readonly activeFilterClasses = computed(() => [
    'px-3 py-1 text-sm rounded-full',
    'bg-blue-100 dark:bg-blue-900',
    'text-blue-800 dark:text-blue-200',
    'border border-blue-300 dark:border-blue-700'
  ].join(' '));

  constructor() {
    // Initialize reactive form with validation
    this.filterForm = this.formBuilder.group({
      searchTerm: [''],
      startDate: [null],
      endDate: [null],
      minAmount: [null],
      maxAmount: [null],
      transactionTypes: [[]],
      categories: [[]],
      merchants: [[]],
      status: [[]],
      sortBy: ['date'],
      sortOrder: ['desc']
    });
  }

  ngOnInit(): void {
    this.initializeFilters();
    this.setupFormSubscription();
    this.validateFilterConfiguration();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeFilters(): void {
    if (this.initialFilters) {
      this.applyFilters(this.initialFilters);
    }
    this.updateActiveFiltersCount();
  }

  private setupFormSubscription(): void {
    this.filterForm.valueChanges.pipe(
      debounceTime(this.debounceMs),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(() => {
      this.handleFiltersChange();
    });
  }

  private validateFilterConfiguration(): void {
    const errors: string[] = [];
    
    if (this.maxAmount <= 0) {
      errors.push('Maximum amount must be positive');
    }
    
    if (this.debounceMs < 0) {
      errors.push('Debounce time must be non-negative');
    }

    this.validationErrors.set(errors);
  }

  private handleFiltersChange(): void {
    const filters = this.buildFilterObject();
    this.updateActiveFiltersCount();
    this.filtersChange.emit(filters);
  }

  private buildFilterObject(): TransactionFilter {
    const formValue = this.filterForm.value;
    
    return {
      id: this.filterId(),
      searchTerm: this.sanitizeInput(formValue.searchTerm),
      dateRange: {
        startDate: formValue.startDate,
        endDate: formValue.endDate
      },
      amountRange: {
        minAmount: formValue.minAmount,
        maxAmount: formValue.maxAmount
      },
      transactionTypes: formValue.transactionTypes || [],
      categories: formValue.categories || [],
      merchants: formValue.merchants || [],
      status: formValue.status || [],
      sortBy: formValue.sortBy,
      sortOrder: formValue.sortOrder
    };
  }

  private updateActiveFiltersCount(): void {
    const formValue = this.filterForm.value;
    let count = 0;

    if (formValue.searchTerm?.trim()) count++;
    if (formValue.startDate || formValue.endDate) count++;
    if (formValue.minAmount !== null || formValue.maxAmount !== null) count++;
    if (formValue.transactionTypes?.length) count++;
    if (formValue.categories?.length) count++;
    if (formValue.merchants?.length) count++;
    if (formValue.status?.length) count++;

    this.activeFiltersCount.set(count);
  }

  // Public methods
  public applyFilters(filters: Partial<TransactionFilter>): void {
    this.filterForm.patchValue({
      searchTerm: filters.searchTerm || '',
      startDate: filters.dateRange?.startDate || null,
      endDate: filters.dateRange?.endDate || null,
      minAmount: filters.amountRange?.minAmount || null,
      maxAmount: filters.amountRange?.maxAmount || null,
      transactionTypes: filters.transactionTypes || [],
      categories: filters.categories || [],
      merchants: filters.merchants || [],
      status: filters.status || [],
      sortBy: filters.sortBy || 'date',
      sortOrder: filters.sortOrder || 'desc'
    });

    const filterObject = this.buildFilterObject();
    this.filterApplied.emit(filterObject);
  }

  public clearAllFilters(): void {
    this.filterForm.reset({
      searchTerm: '',
      startDate: null,
      endDate: null,
      minAmount: null,
      maxAmount: null,
      transactionTypes: [],
      categories: [],
      merchants: [],
      status: [],
      sortBy: 'date',
      sortOrder: 'desc'
    });
    
    this.filterCleared.emit();
  }

  public toggleExpanded(): void {
    this.isExpanded.update(expanded => !expanded);
  }

  public selectQuickFilter(filter: string): void {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    
    switch (filter) {
      case 'today':
        this.filterForm.patchValue({
          startDate: startOfDay,
          endDate: new Date(startOfDay.getTime() + 86400000 - 1)
        });
        break;
      case 'week':
        const startOfWeek = new Date(startOfDay);
        startOfWeek.setDate(startOfDay.getDate() - startOfDay.getDay());
        this.filterForm.patchValue({
          startDate: startOfWeek,
          endDate: today
        });
        break;
      case 'month':
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        this.filterForm.patchValue({
          startDate: startOfMonth,
          endDate: today
        });
        break;
      case 'income':
        this.filterForm.patchValue({
          transactionTypes: ['credit', 'deposit', 'income']
        });
        break;
      case 'expenses':
        this.filterForm.patchValue({
          transactionTypes: ['debit', 'withdrawal', 'expense']
        });
        break;
      case 'pending':
        this.filterForm.patchValue({
          status: ['pending', 'processing']
        });
        break;
    }

    this.quickFilterSelected.emit(filter);
  }

  public isFilterActive(filterType: string, value: string): boolean {
    const formValue = this.filterForm.value;
    const arrayField = formValue[filterType];
    return Array.isArray(arrayField) && arrayField.includes(value);
  }

  public toggleFilterValue(filterType: string, value: string): void {
    const currentValues = this.filterForm.get(filterType)?.value || [];
    const newValues = this.isFilterActive(filterType, value)
      ? currentValues.filter((v: string) => v !== value)
      : [...currentValues, value];
    
    this.filterForm.patchValue({ [filterType]: newValues });
  }

  public formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: this.currency
    }).format(amount);
  }

  // Security methods
  private sanitizeInput(input: string): string {
    if (!input) return '';
    return input.trim().replace(/[<>'"]/g, '');
  }

  public sanitizeHtml(html: string): SafeHtml {
    return this.sanitizer.sanitize(1, html) || '';
  }

  // Getters for template
  get hasActiveFilters(): boolean {
    return this.activeFiltersCount() > 0;
  }

  get isFormValid(): boolean {
    return this.filterForm.valid && this.validationErrors().length === 0;
  }

  get expandedState(): boolean {
    return this.isExpanded();
  }

  get filterCount(): number {
    return this.activeFiltersCount();
  }

  get quickFilterOptions(): FilterOption[] {
    return this.quickFilters();
  }

  get hasValidationErrors(): boolean {
    return this.validationErrors().length > 0;
  }

  get errors(): string[] {
    return this.validationErrors();
  }
}
