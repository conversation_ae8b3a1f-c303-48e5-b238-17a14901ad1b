import { Component, Input, Output, EventEmitter, computed, signal } from '@angular/core';
import { CommonModule } from '@angular/common';

// Transaction interfaces
interface Transaction {
  id: string;
  type: 'credit' | 'debit' | 'transfer' | 'payment' | 'refund' | 'fee' | 'interest';
  amount: number;
  currency: string;
  description: string;
  merchant?: string;
  category: string;
  date: string;
  status: 'completed' | 'pending' | 'failed' | 'cancelled' | 'processing';
  balance?: number;
  reference?: string;
  location?: string;
  icon?: string;
  metadata?: Record<string, any>;
}

interface TransactionCompactConfig {
  showAmount: boolean;
  showDate: boolean;
  showStatus: boolean;
  showCategory: boolean;
  showBalance: boolean;
  showIcon: boolean;
  showMerchant: boolean;
  enableHover: boolean;
  enableClick: boolean;
  dateFormat: 'short' | 'medium' | 'long' | 'relative';
  amountFormat: 'full' | 'compact' | 'abbreviated';
  maxDescriptionLength: number;
  highlightAmount: boolean;
}

const TRANSACTION_TYPES = {
  credit: { name: 'Credit', icon: '💰', color: 'green' },
  debit: { name: 'Debit', icon: '💳', color: 'red' },
  transfer: { name: 'Transfer', icon: '🔄', color: 'blue' },
  payment: { name: 'Payment', icon: '💸', color: 'orange' },
  refund: { name: 'Refund', icon: '↩️', color: 'green' },
  fee: { name: 'Fee', icon: '📋', color: 'gray' },
  interest: { name: 'Interest', icon: '📈', color: 'green' }
};

const TRANSACTION_CATEGORIES = {
  groceries: { name: 'Groceries', icon: '🛒' },
  restaurants: { name: 'Restaurants', icon: '🍽️' },
  transport: { name: 'Transport', icon: '🚗' },
  entertainment: { name: 'Entertainment', icon: '🎬' },
  utilities: { name: 'Utilities', icon: '⚡' },
  healthcare: { name: 'Healthcare', icon: '🏥' },
  shopping: { name: 'Shopping', icon: '🛍️' },
  education: { name: 'Education', icon: '📚' },
  travel: { name: 'Travel', icon: '✈️' },
  other: { name: 'Other', icon: '📁' }
};

@Component({
  selector: 'lib-transaction-compact',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './transaction-compact.component.html',
  styleUrl: './transaction-compact.component.css'
})
export class TransactionCompactComponent {
  // Standard UI inputs for LP-GO builder integration
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() disabled: boolean = false;

  // Transaction Compact specific inputs
  @Input() transaction: Transaction | null = null;
  @Input() config: Partial<TransactionCompactConfig> = {};
  @Input() showLabels: boolean = false;
  @Input() darkMode: boolean = false;
  @Input() interactive: boolean = true;

  // Event outputs
  @Output() transactionClicked = new EventEmitter<Transaction>();
  @Output() actionRequested = new EventEmitter<{ action: string; transaction: Transaction }>();

  isHovered = signal(false);

  // Default configuration
  private defaultConfig: TransactionCompactConfig = {
    showAmount: true,
    showDate: true,
    showStatus: true,
    showCategory: true,
    showBalance: false,
    showIcon: true,
    showMerchant: true,
    enableHover: true,
    enableClick: true,
    dateFormat: 'short',
    amountFormat: 'full',
    maxDescriptionLength: 40,
    highlightAmount: true
  };

  // Default transaction data
  private defaultTransaction: Transaction = {
    id: 'txn_001',
    type: 'debit',
    amount: -45.67,
    currency: 'USD',
    description: 'Coffee Shop Purchase',
    merchant: 'Starbucks Downtown',
    category: 'restaurants',
    date: new Date().toISOString(),
    status: 'completed',
    balance: 2543.89,
    reference: 'REF_123456',
    location: 'New York, NY'
  };

  // Computed properties
  mergedConfig = computed(() => ({
    ...this.defaultConfig,
    ...this.config
  }));

  displayTransaction = computed(() => 
    this.transaction || this.defaultTransaction
  );

  containerClasses = computed(() => {
    const base = 'transaction-compact-container';
    const sizeClass = `size-${this.size}`;
    const variantClass = `variant-${this.variant}`;
    const roundedClass = `rounded-${this.rounded}`;
    const stateClasses = [
      this.disabled ? 'disabled' : '',
      this.darkMode ? 'dark' : '',
      this.interactive && this.mergedConfig().enableClick ? 'interactive' : '',
      this.isHovered() && this.mergedConfig().enableHover ? 'hovered' : '',
      this.getTransactionTypeClass()
    ].filter(Boolean);

    return [base, sizeClass, variantClass, roundedClass, ...stateClasses, this.className].filter(Boolean).join(' ');
  });

  transactionIcon = computed(() => {
    const txn = this.displayTransaction();
    if (txn.icon) return txn.icon;
    
    // First try category icon
    const categoryInfo = TRANSACTION_CATEGORIES[txn.category as keyof typeof TRANSACTION_CATEGORIES];
    if (categoryInfo) return categoryInfo.icon;
    
    // Fall back to type icon
    const typeInfo = TRANSACTION_TYPES[txn.type as keyof typeof TRANSACTION_TYPES];
    return typeInfo?.icon || '💳';
  });

  formattedAmount = computed(() => {
    const txn = this.displayTransaction();
    const config = this.mergedConfig();
    
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: txn.currency
    });

    switch (config.amountFormat) {
      case 'compact':
        return this.formatCompactAmount(txn.amount, txn.currency);
      case 'abbreviated':
        return this.formatAbbreviatedAmount(txn.amount, txn.currency);
      default:
        return formatter.format(Math.abs(txn.amount));
    }
  });

  formattedDate = computed(() => {
    const txn = this.displayTransaction();
    const config = this.mergedConfig();
    const date = new Date(txn.date);

    switch (config.dateFormat) {
      case 'relative':
        return this.getRelativeDate(date);
      case 'medium':
        return date.toLocaleDateString('en-US', { 
          month: 'short', 
          day: 'numeric', 
          year: 'numeric' 
        });
      case 'long':
        return date.toLocaleDateString('en-US', { 
          weekday: 'short',
          month: 'short', 
          day: 'numeric', 
          year: 'numeric' 
        });
      default: // short
        return date.toLocaleDateString('en-US', { 
          month: 'numeric', 
          day: 'numeric' 
        });
    }
  });

  truncatedDescription = computed(() => {
    const txn = this.displayTransaction();
    const config = this.mergedConfig();
    
    if (txn.description.length <= config.maxDescriptionLength) {
      return txn.description;
    }
    
    return txn.description.substring(0, config.maxDescriptionLength - 3) + '...';
  });

  onTransactionClick(): void {
    if (!this.interactive || this.disabled || !this.mergedConfig().enableClick) return;
    
    const txn = this.displayTransaction();
    this.transactionClicked.emit(txn);
  }

  onActionClick(action: string, event: Event): void {
    event.stopPropagation();
    
    if (this.disabled) return;
    
    const txn = this.displayTransaction();
    this.actionRequested.emit({ action, transaction: txn });
  }

  onMouseEnter(): void {
    if (this.mergedConfig().enableHover) {
      this.isHovered.set(true);
    }
  }

  onMouseLeave(): void {
    this.isHovered.set(false);
  }

  private getTransactionTypeClass(): string {
    const txn = this.displayTransaction();
    const typeInfo = TRANSACTION_TYPES[txn.type as keyof typeof TRANSACTION_TYPES];
    return `type-${typeInfo?.color || 'gray'}`;
  }

  private formatCompactAmount(amount: number, currency: string): string {
    const absAmount = Math.abs(amount);
    
    if (absAmount >= 1000000) {
      return `${currency} ${(absAmount / 1000000).toFixed(1)}M`;
    } else if (absAmount >= 1000) {
      return `${currency} ${(absAmount / 1000).toFixed(1)}K`;
    } else {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
      }).format(absAmount);
    }
  }

  private formatAbbreviatedAmount(amount: number, currency: string): string {
    const absAmount = Math.abs(amount);
    const currencySymbol = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).formatToParts(1).find(part => part.type === 'currency')?.value || currency;
    
    if (absAmount >= 1000000) {
      return `${currencySymbol}${(absAmount / 1000000).toFixed(1)}M`;
    } else if (absAmount >= 1000) {
      return `${currencySymbol}${(absAmount / 1000).toFixed(1)}K`;
    } else {
      return `${currencySymbol}${absAmount.toFixed(0)}`;
    }
  }

  private getRelativeDate(date: Date): string {
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));

    if (diffInDays > 7) {
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    } else if (diffInDays > 0) {
      return `${diffInDays}d ago`;
    } else if (diffInHours > 0) {
      return `${diffInHours}h ago`;
    } else if (diffInMinutes > 0) {
      return `${diffInMinutes}m ago`;
    } else {
      return 'Just now';
    }
  }

  getStatusText(status: string): string {
    const statusMap = {
      completed: 'Completed',
      pending: 'Pending',
      failed: 'Failed',
      cancelled: 'Cancelled',
      processing: 'Processing'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  }

  getStatusIcon(status: string): string {
    const statusIcons = {
      completed: '✅',
      pending: '⏳',
      failed: '❌',
      cancelled: '🚫',
      processing: '🔄'
    };
    return statusIcons[status as keyof typeof statusIcons] || '❓';
  }

  isNegativeAmount(): boolean {
    return this.displayTransaction().amount < 0;
  }

  isPositiveAmount(): boolean {
    return this.displayTransaction().amount > 0;
  }

  getCategoryIcon(category: string): string {
    const categoryInfo = TRANSACTION_CATEGORIES[category as keyof typeof TRANSACTION_CATEGORIES];
    return categoryInfo?.icon || '📁';
  }

  getCategoryName(category: string): string {
    const categoryInfo = TRANSACTION_CATEGORIES[category as keyof typeof TRANSACTION_CATEGORIES];
    return categoryInfo?.name || category;
  }
}
