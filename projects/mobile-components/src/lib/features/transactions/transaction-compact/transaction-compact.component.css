
/* Base Container Styles */
.transaction-compact-container {
  @apply flex items-center space-x-3 p-3 bg-white border border-gray-200 rounded-lg transition-all duration-200 relative;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Size Variants */
.transaction-compact-container.size-xs {
  @apply p-2 space-x-2 text-xs;
}

.transaction-compact-container.size-sm {
  @apply p-2 space-x-2 text-sm;
}

.transaction-compact-container.size-md {
  @apply p-3 space-x-3 text-base;
}

.transaction-compact-container.size-lg {
  @apply p-4 space-x-4 text-lg;
}

.transaction-compact-container.size-xl {
  @apply p-4 space-x-4 text-xl;
}

/* Variant Styles */
.transaction-compact-container.variant-primary {
  @apply border-blue-300 bg-blue-50;
}

.transaction-compact-container.variant-secondary {
  @apply border-gray-300 bg-gray-50;
}

.transaction-compact-container.variant-success {
  @apply border-green-300 bg-green-50;
}

.transaction-compact-container.variant-warning {
  @apply border-yellow-300 bg-yellow-50;
}

.transaction-compact-container.variant-danger {
  @apply border-red-300 bg-red-50;
}

/* Rounded Variants */
.transaction-compact-container.rounded-none {
  @apply rounded-none;
}

.transaction-compact-container.rounded-sm {
  @apply rounded-sm;
}

.transaction-compact-container.rounded-md {
  @apply rounded-md;
}

.transaction-compact-container.rounded-lg {
  @apply rounded-lg;
}

.transaction-compact-container.rounded-full {
  @apply rounded-full;
}

/* State Styles */
.transaction-compact-container.disabled {
  @apply opacity-60 pointer-events-none;
}

.transaction-compact-container.interactive {
  @apply cursor-pointer hover:shadow-md hover:border-blue-300;
}

.transaction-compact-container.hovered {
  @apply shadow-md border-blue-300 bg-blue-50;
}

/* Transaction Type Colors */
.transaction-compact-container.type-green {
  @apply border-l-4 border-l-green-500;
}

.transaction-compact-container.type-red {
  @apply border-l-4 border-l-red-500;
}

.transaction-compact-container.type-blue {
  @apply border-l-4 border-l-blue-500;
}

.transaction-compact-container.type-orange {
  @apply border-l-4 border-l-orange-500;
}

.transaction-compact-container.type-gray {
  @apply border-l-4 border-l-gray-500;
}

/* Dark Mode */
.transaction-compact-container.dark {
  @apply bg-gray-800 border-gray-600 text-white;
}

.transaction-compact-container.dark .description-text {
  @apply text-white;
}

.transaction-compact-container.dark .merchant-text {
  @apply text-gray-300;
}

.transaction-compact-container.dark .transaction-date,
.transaction-compact-container.dark .category-name {
  @apply text-gray-400;
}

.transaction-compact-container.dark .action-btn {
  @apply bg-gray-700 hover:bg-gray-600;
}

.transaction-compact-container.dark.hovered {
  @apply bg-gray-700 border-blue-400;
}

/* Transaction Icon */
.transaction-icon {
  @apply flex-shrink-0 w-10 h-10 flex items-center justify-center bg-gray-100 rounded-full;
}

.icon-emoji {
  @apply text-xl;
}

/* Transaction Content */
.transaction-content {
  @apply flex-1 min-w-0 space-y-1;
}

.transaction-primary {
  @apply flex items-center justify-between;
}

.transaction-description {
  @apply flex-1 min-w-0;
}

.description-text {
  @apply block font-medium text-gray-900 truncate;
}

.merchant-text {
  @apply block text-sm text-gray-600 truncate;
}

/* Amount Styling */
.transaction-amount {
  @apply flex-shrink-0 ml-3;
}

.amount-text {
  @apply font-semibold;
}

.amount-text.negative {
  @apply text-red-600;
}

.amount-text.positive {
  @apply text-green-600;
}

.amount-text.highlighted {
  @apply font-bold;
}

.amount-sign {
  @apply font-normal;
}

/* Secondary Info */
.transaction-secondary {
  @apply flex items-center justify-between;
}

.transaction-meta {
  @apply flex items-center space-x-3 text-sm text-gray-600;
}

.transaction-date {
  @apply flex-shrink-0;
}

.transaction-category {
  @apply flex items-center space-x-1;
}

.category-icon {
  @apply text-sm;
}

.category-name {
  @apply text-xs;
}

.transaction-balance {
  @apply text-xs font-medium;
}

/* Status Styling */
.transaction-status {
  @apply flex-shrink-0;
}

.status-indicator {
  @apply flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium;
}

.status-indicator.status-completed {
  @apply bg-green-100 text-green-800;
}

.status-indicator.status-pending {
  @apply bg-yellow-100 text-yellow-800;
}

.status-indicator.status-failed {
  @apply bg-red-100 text-red-800;
}

.status-indicator.status-cancelled {
  @apply bg-gray-100 text-gray-800;
}

.status-indicator.status-processing {
  @apply bg-blue-100 text-blue-800;
}

.status-icon {
  @apply text-xs;
}

.status-text {
  @apply text-xs;
}

/* Action Buttons */
.transaction-actions {
  @apply flex items-center space-x-1 opacity-0 transition-opacity duration-200;
}

.transaction-compact-container:hover .transaction-actions,
.transaction-compact-container.hovered .transaction-actions {
  @apply opacity-100;
}

.action-btn {
  @apply w-8 h-8 flex items-center justify-center bg-gray-100 hover:bg-gray-200 rounded-full transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1;
}

.action-icon {
  @apply text-sm;
}

.details-btn:hover {
  @apply bg-blue-100;
}

.share-btn:hover {
  @apply bg-green-100;
}

.dispute-btn:hover {
  @apply bg-orange-100;
}

/* Processing Indicator */
.processing-indicator {
  @apply absolute top-1 right-1;
}

.processing-spinner {
  @apply w-4 h-4 border-2 border-blue-200 border-t-blue-600 rounded-full animate-spin;
}

/* Hover Overlay */
.hover-overlay {
  @apply absolute inset-0 bg-blue-100 bg-opacity-20 rounded-lg pointer-events-none;
}

/* Mobile Responsiveness */
@media (max-width: 640px) {
  .transaction-compact-container {
    @apply space-x-2 p-2;
  }
  
  .transaction-icon {
    @apply w-8 h-8;
  }
  
  .icon-emoji {
    @apply text-lg;
  }
  
  .transaction-meta {
    @apply space-x-2;
  }
  
  .category-name {
    @apply hidden;
  }
  
  .transaction-balance {
    @apply hidden;
  }
  
  .transaction-actions {
    @apply opacity-100;
  }
  
  .action-btn {
    @apply w-6 h-6;
  }
  
  .action-icon {
    @apply text-xs;
  }
}

/* Focus Styles */
.transaction-compact-container:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

.action-btn:focus {
  @apply ring-2 ring-blue-500 ring-offset-1;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .transaction-compact-container {
    @apply border-2 border-black;
  }
  
  .action-btn {
    @apply border border-gray-800;
  }
  
  .status-indicator {
    @apply border border-gray-800;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .transaction-compact-container,
  .action-btn,
  .transaction-actions,
  .hover-overlay {
    @apply transition-none;
  }
  
  .processing-spinner {
    @apply animate-none;
  }
}

/* Print Styles */
@media print {
  .transaction-compact-container {
    @apply shadow-none border border-black;
  }
  
  .transaction-actions {
    @apply hidden;
  }
  
  .processing-indicator,
  .hover-overlay {
    @apply hidden;
  }
  
  .amount-text.negative {
    @apply text-black;
  }
  
  .amount-text.positive {
    @apply text-black;
  }
}

/* Animation Keyframes */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.transaction-compact-container {
  animation: fadeIn 0.3s ease-out;
}

/* Special States */
.transaction-compact-container.size-xs .transaction-secondary {
  @apply hidden;
}

.transaction-compact-container.size-sm .transaction-balance {
  @apply hidden;
}

/* Loading State */
.transaction-compact-container.loading {
  @apply animate-pulse;
}

.transaction-compact-container.loading .description-text,
.transaction-compact-container.loading .amount-text {
  @apply bg-gray-200 text-transparent rounded;
}

/* Error State */
.transaction-compact-container.error {
  @apply border-red-300 bg-red-50;
}

.transaction-compact-container.error .amount-text {
  @apply text-red-600;
}