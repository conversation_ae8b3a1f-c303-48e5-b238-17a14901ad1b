<div 
  [ngClass]="containerClasses()" 
  [attr.aria-label]="'Transaction: ' + truncatedDescription()"
  [attr.tabindex]="interactive && mergedConfig().enableClick ? '0' : null"
  (click)="onTransactionClick()"
  (mouseenter)="onMouseEnter()"
  (mouseleave)="onMouseLeave()"
  (keydown.enter)="onTransactionClick()"
  (keydown.space)="onTransactionClick()"
  role="listitem"
>
  <!-- Transaction Icon -->
  <div class="transaction-icon" *ngIf="mergedConfig().showIcon">
    <span class="icon-emoji" [title]="displayTransaction().type">
      {{ transactionIcon() }}
    </span>
  </div>

  <!-- Main Content -->
  <div class="transaction-content">
    <!-- Primary Info -->
    <div class="transaction-primary">
      <div class="transaction-description">
        <span class="description-text" [title]="displayTransaction().description">
          {{ truncatedDescription() }}
        </span>
        <span class="merchant-text" *ngIf="mergedConfig().showMerchant && displayTransaction().merchant">
          {{ displayTransaction().merchant }}
        </span>
      </div>
      
      <div class="transaction-amount" *ngIf="mergedConfig().showAmount">
        <span 
          class="amount-text"
          [class.negative]="isNegativeAmount()"
          [class.positive]="isPositiveAmount()"
          [class.highlighted]="mergedConfig().highlightAmount"
        >
          <span class="amount-sign" *ngIf="isNegativeAmount()">-</span>
          <span class="amount-sign" *ngIf="isPositiveAmount()">+</span>
          {{ formattedAmount() }}
        </span>
      </div>
    </div>

    <!-- Secondary Info -->
    <div class="transaction-secondary">
      <div class="transaction-meta">
        <!-- Date -->
        <span class="transaction-date" *ngIf="mergedConfig().showDate">
          {{ formattedDate() }}
        </span>

        <!-- Category -->
        <span class="transaction-category" *ngIf="mergedConfig().showCategory">
          <span class="category-icon">{{ getCategoryIcon(displayTransaction().category) }}</span>
          <span class="category-name">{{ getCategoryName(displayTransaction().category) }}</span>
        </span>

        <!-- Balance -->
        <span class="transaction-balance" *ngIf="mergedConfig().showBalance && displayTransaction().balance !== undefined">
          Balance: {{ displayTransaction().balance | currency:displayTransaction().currency:'symbol':'1.2-2' }}
        </span>
      </div>

      <!-- Status -->
      <div class="transaction-status" *ngIf="mergedConfig().showStatus">
        <span class="status-indicator" [class]="'status-' + displayTransaction().status">
          <span class="status-icon">{{ getStatusIcon(displayTransaction().status) }}</span>
          <span class="status-text" *ngIf="showLabels">{{ getStatusText(displayTransaction().status) }}</span>
        </span>
      </div>
    </div>
  </div>

  <!-- Action Buttons -->
  <div class="transaction-actions" *ngIf="interactive && !disabled">
    <button
      type="button"
      class="action-btn details-btn"
      (click)="onActionClick('details', $event)"
      [attr.aria-label]="'View details for ' + truncatedDescription()"
      title="View Details"
    >
      <span class="action-icon">👁️</span>
    </button>

    <button
      type="button"
      class="action-btn share-btn"
      (click)="onActionClick('share', $event)"
      [attr.aria-label]="'Share ' + truncatedDescription()"
      title="Share Transaction"
      *ngIf="displayTransaction().status === 'completed'"
    >
      <span class="action-icon">📤</span>
    </button>

    <button
      type="button"
      class="action-btn dispute-btn"
      (click)="onActionClick('dispute', $event)"
      [attr.aria-label]="'Dispute ' + truncatedDescription()"
      title="Dispute Transaction"
      *ngIf="displayTransaction().status === 'completed' && isNegativeAmount()"
    >
      <span class="action-icon">⚠️</span>
    </button>
  </div>

  <!-- Loading Indicator for Processing -->
  <div class="processing-indicator" *ngIf="displayTransaction().status === 'processing'">
    <div class="processing-spinner" aria-hidden="true"></div>
  </div>

  <!-- Hover Overlay -->
  <div class="hover-overlay" *ngIf="isHovered() && mergedConfig().enableHover" aria-hidden="true"></div>
</div>
