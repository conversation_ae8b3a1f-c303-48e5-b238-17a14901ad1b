/* Money In Transaction Component - Financial Security Focused */

/* CSS Custom Properties */
.money-in-container {
  --primary-color: #2563eb;
  --success-color: #059669;
  --warning-color: #d97706;
  --danger-color: #dc2626;
  --neutral-color: #6b7280;
  --background-color: #ffffff;
  --border-color: #d1d5db;
  --text-color: #1f2937;
  --text-muted: #6b7280;
  --shadow-light: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-medium: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-large: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  --border-radius: 0.5rem;
  --transition: all 0.2s ease-in-out;

  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  position: relative;
  overflow: hidden;
}

/* Dark Mode Support */
.money-in-container.dark {
  --background-color: #1f2937;
  --border-color: #374151;
  --text-color: #f9fafb;
  --text-muted: #9ca3af;
}

/* Size Variations */
.money-in-container.size-xs {
  padding: 1rem;
  font-size: 0.875rem;
}

.money-in-container.size-sm {
  padding: 1.5rem;
  font-size: 0.875rem;
}

.money-in-container.size-md {
  padding: 2rem;
  font-size: 1rem;
}

.money-in-container.size-lg {
  padding: 2.5rem;
  font-size: 1.125rem;
}

.money-in-container.size-xl {
  padding: 3rem;
  font-size: 1.25rem;
}

/* Variant Styles */
.money-in-container.variant-primary {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 1px var(--primary-color);
}

.money-in-container.variant-success {
  border-color: var(--success-color);
  box-shadow: 0 0 0 1px var(--success-color);
}

.money-in-container.variant-warning {
  border-color: var(--warning-color);
  box-shadow: 0 0 0 1px var(--warning-color);
}

.money-in-container.variant-danger {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 1px var(--danger-color);
}

/* Rounded Variations */
.money-in-container.rounded-none {
  border-radius: 0;
}

.money-in-container.rounded-sm {
  border-radius: 0.25rem;
}

.money-in-container.rounded-lg {
  border-radius: 1rem;
}

.money-in-container.rounded-full {
  border-radius: 2rem;
}

/* State Styles */
.money-in-container.disabled {
  opacity: 0.6;
  pointer-events: none;
  background-color: #f9fafb;
}

.money-in-container.processing {
  pointer-events: none;
}

.money-in-container.valid {
  border-color: var(--success-color);
}

.money-in-container.invalid {
  border-color: var(--danger-color);
}

/* Form Header */
.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.form-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0;
}

.form-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.status-indicator {
  font-size: 1rem;
}

.status-indicator.valid {
  color: var(--success-color);
}

.status-indicator.invalid {
  color: var(--warning-color);
}

.status-text {
  color: var(--text-muted);
  font-weight: 500;
}

/* Form Layout */
.money-in-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  gap: 1rem;
}

.amount-currency-row {
  grid-template-columns: 2fr 1fr;
}

.source-row {
  grid-template-columns: 1fr 1fr;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.required {
  color: var(--danger-color);
  font-weight: 700;
}

/* Input Styles */
.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  font-size: 1rem;
  color: var(--text-color);
  background: var(--background-color);
  transition: var(--transition);
  outline: none;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.form-input:invalid,
.form-select:invalid,
.form-textarea:invalid {
  border-color: var(--danger-color);
}

.form-input:disabled,
.form-select:disabled,
.form-textarea:disabled {
  background-color: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: #9ca3af;
}

/* Amount Input Specific */
.amount-wrapper {
  position: relative;
}

.amount-input {
  padding-left: 2.5rem;
  padding-right: 3rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 600;
  font-size: 1.125rem;
}

.currency-prefix {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-weight: 600;
  color: var(--text-color);
  z-index: 2;
  pointer-events: none;
}

.validation-indicator {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 3;
}

.validation-check {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: 600;
  transition: var(--transition);
}

.validation-check.valid {
  background: var(--success-color);
  color: white;
}

.validation-check.invalid {
  background: var(--danger-color);
  color: white;
}

/* Formatted Amount Display */
.formatted-display {
  margin-top: 0.25rem;
}

.formatted-text {
  font-size: 0.875rem;
  color: var(--text-muted);
  font-weight: 500;
  background: #f3f4f6;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  display: inline-block;
}

/* Error Messages */
.form-errors {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.error-message {
  color: var(--danger-color);
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.error-message::before {
  content: '⚠️';
  font-size: 0.875rem;
}

/* Optional Fields */
.optional-fields {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  background: #f9fafb;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
}

.description-textarea {
  resize: vertical;
  min-height: 4rem;
}

/* Fee Calculation */
.fee-calculation {
  padding: 1rem;
  background: #f0f9ff;
  border: 1px solid #e0f2fe;
  border-radius: 0.5rem;
}

.fee-breakdown {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.fee-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
}

.fee-label {
  color: var(--text-color);
}

.fee-value {
  font-weight: 600;
  color: var(--text-color);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.total-row {
  border-top: 1px solid #e0f2fe;
  padding-top: 0.5rem;
  margin-top: 0.5rem;
  font-size: 1rem;
}

.total-value {
  font-size: 1.125rem;
  color: var(--primary-color);
}

/* Validation Summary */
.validation-summary {
  padding: 1rem;
  background: #f9fafb;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
}

.validation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.75rem;
}

.validation-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: var(--transition);
}

.validation-item.valid {
  background: #dcfdf7;
  color: #065f46;
}

.validation-item.invalid {
  background: #fee2e2;
  color: #991b1b;
}

.validation-icon {
  font-weight: 600;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 3rem;
  outline: none;
}

.btn:focus {
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.2);
}

.btn-submit {
  flex: 1;
  background: var(--primary-color);
  color: white;
}

.btn-submit:hover:not(:disabled) {
  background: #1d4ed8;
  transform: translateY(-1px);
  box-shadow: var(--shadow-large);
}

.btn-submit:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-submit.loading {
  pointer-events: none;
}

.btn-reset {
  background: #f3f4f6;
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.btn-reset:hover:not(:disabled) {
  background: #e5e7eb;
  transform: translateY(-1px);
}

/* Loading Spinner */
.spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  border-radius: inherit;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  text-align: center;
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid #e5e7eb;
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 0.875rem;
  color: var(--neutral-color);
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .money-in-container {
    padding: 1rem;
    margin: 0;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .form-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
    text-align: center;
  }

  .form-actions {
    flex-direction: column;
  }

  .validation-grid {
    grid-template-columns: 1fr;
  }

  .fee-row {
    font-size: 0.8125rem;
  }
}

@media (max-width: 480px) {
  .money-in-container {
    padding: 0.75rem;
  }

  .form-title {
    font-size: 1.25rem;
  }

  .amount-input {
    font-size: 1rem;
  }

  .currency-prefix {
    font-size: 0.875rem;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .money-in-container {
    border-width: 2px;
    --shadow-medium: 0 4px 6px -1px rgb(0 0 0 / 0.3);
  }

  .form-input:focus,
  .form-select:focus,
  .form-textarea:focus {
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.4);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .money-in-container * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print Styles */
@media print {
  .money-in-container {
    box-shadow: none;
    border: 1px solid #000;
  }

  .btn,
  .loading-overlay,
  .validation-indicator {
    display: none !important;
  }

  .form-input,
  .form-select,
  .form-textarea {
    border: 1px solid #000;
  }
}