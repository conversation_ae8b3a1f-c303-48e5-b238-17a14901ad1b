import { Component, Input, Output, EventEmitter, computed, signal, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';

// Financial transaction interfaces
interface MoneyInTransaction {
  id?: string;
  amount: number;
  currency: string;
  source: string;
  sourceType: 'bank_transfer' | 'cash_deposit' | 'check' | 'wire' | 'ach' | 'other';
  description?: string;
  reference?: string;
  timestamp: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  fees?: number;
  exchangeRate?: number;
  metadata?: Record<string, any>;
}

interface MoneyInConfig {
  allowedCurrencies: string[];
  allowedSourceTypes: string[];
  minAmount: number;
  maxAmount: number;
  requireDescription: boolean;
  requireReference: boolean;
  enableFeeCalculation: boolean;
  enableExchangeRate: boolean;
  allowDecimalPlaces: number;
  defaultCurrency: string;
  autoFormatting: boolean;
  showValidationSummary: boolean;
}

interface MoneyInValidation {
  isValid: boolean;
  errors: string[];
  amountValid: boolean;
  sourceValid: boolean;
  currencyValid: boolean;
  limitsValid: boolean;
}

interface FinancialEvent {
  type: 'validation' | 'submit' | 'error' | 'format' | 'calculation';
  timestamp: string;
  details: any;
  severity: 'info' | 'warning' | 'error' | 'critical';
}

// Currency and source type definitions
const SUPPORTED_CURRENCIES = {
  USD: { symbol: '$', name: 'US Dollar', decimals: 2 },
  EUR: { symbol: '€', name: 'Euro', decimals: 2 },
  GBP: { symbol: '£', name: 'British Pound', decimals: 2 },
  JPY: { symbol: '¥', name: 'Japanese Yen', decimals: 0 },
  CAD: { symbol: 'C$', name: 'Canadian Dollar', decimals: 2 },
  AUD: { symbol: 'A$', name: 'Australian Dollar', decimals: 2 }
};

const SOURCE_TYPES = {
  bank_transfer: { name: 'Bank Transfer', icon: '🏦', feeRate: 0.001 },
  cash_deposit: { name: 'Cash Deposit', icon: '💵', feeRate: 0 },
  check: { name: 'Check Deposit', icon: '📋', feeRate: 0.005 },
  wire: { name: 'Wire Transfer', icon: '📡', feeRate: 0.015 },
  ach: { name: 'ACH Transfer', icon: '🔄', feeRate: 0.0025 },
  other: { name: 'Other', icon: '📄', feeRate: 0.01 }
};

@Component({
  selector: 'lib-money-in',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './money-in.component.html',
  styleUrl: './money-in.component.css'
})
export class MoneyInComponent implements OnInit, OnDestroy {
  // Standard UI inputs for LP-GO builder integration
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() disabled: boolean = false;

  // Money In specific inputs
  @Input() config: Partial<MoneyInConfig> = {};
  @Input() initialData: Partial<MoneyInTransaction> = {};
  @Input() showLabels: boolean = true;
  @Input() showValidation: boolean = true;
  @Input() enableAdvancedFeatures: boolean = false;
  @Input() darkMode: boolean = false;

  // Event outputs
  @Output() transactionCreated = new EventEmitter<MoneyInTransaction>();
  @Output() validationChange = new EventEmitter<MoneyInValidation>();
  @Output() financialEvent = new EventEmitter<FinancialEvent>();
  @Output() amountFormatted = new EventEmitter<{ raw: number, formatted: string }>();

  // Component state using Angular signals
  private destroy$ = new Subject<void>();
  
  moneyInForm!: FormGroup;
  isProcessing = signal(false);
  calculatedFees = signal<number>(0);
  formattedAmount = signal<string>('');
  
  // Validation state
  currentValidation = signal<MoneyInValidation>({
    isValid: false,
    errors: [],
    amountValid: false,
    sourceValid: false,
    currencyValid: false,
    limitsValid: false
  });

  // Default configuration
  private defaultConfig: MoneyInConfig = {
    allowedCurrencies: ['USD', 'EUR', 'GBP'],
    allowedSourceTypes: ['bank_transfer', 'cash_deposit', 'check', 'ach'],
    minAmount: 0.01,
    maxAmount: 50000,
    requireDescription: false,
    requireReference: false,
    enableFeeCalculation: true,
    enableExchangeRate: false,
    allowDecimalPlaces: 2,
    defaultCurrency: 'USD',
    autoFormatting: true,
    showValidationSummary: true
  };

  // Computed properties
  mergedConfig = computed(() => ({
    ...this.defaultConfig,
    ...this.config
  }));

  containerClasses = computed(() => {
    const base = 'money-in-container';
    const sizeClass = `size-${this.size}`;
    const variantClass = `variant-${this.variant}`;
    const roundedClass = `rounded-${this.rounded}`;
    const stateClasses = [
      this.disabled ? 'disabled' : '',
      this.darkMode ? 'dark' : '',
      this.isProcessing() ? 'processing' : '',
      this.currentValidation().isValid ? 'valid' : 'invalid'
    ].filter(Boolean);

    return [
      base,
      sizeClass,
      variantClass,
      roundedClass,
      ...stateClasses,
      this.className
    ].filter(Boolean).join(' ');
  });

  // Available options computed from config
  availableCurrencies = computed(() => 
    this.mergedConfig().allowedCurrencies.map(code => ({
      code,
      ...SUPPORTED_CURRENCIES[code as keyof typeof SUPPORTED_CURRENCIES]
    }))
  );

  availableSourceTypes = computed(() => 
    this.mergedConfig().allowedSourceTypes.map(type => ({
      type,
      ...SOURCE_TYPES[type as keyof typeof SOURCE_TYPES]
    }))
  );

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.initializeForm();
    this.setupValidation();
    this.emitFinancialEvent('validation', { action: 'component_initialized' }, 'info');
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    const config = this.mergedConfig();
    
    this.moneyInForm = this.fb.group({
      amount: [
        this.initialData.amount || '',
        [
          Validators.required,
          this.amountValidator.bind(this)
        ]
      ],
      currency: [
        this.initialData.currency || config.defaultCurrency,
        [Validators.required, this.currencyValidator.bind(this)]
      ],
      source: [
        this.initialData.source || '',
        [Validators.required, Validators.minLength(2)]
      ],
      sourceType: [
        this.initialData.sourceType || 'bank_transfer',
        [Validators.required, this.sourceTypeValidator.bind(this)]
      ],
      description: [
        this.initialData.description || '',
        config.requireDescription ? [Validators.required] : []
      ],
      reference: [
        this.initialData.reference || '',
        config.requireReference ? [Validators.required] : []
      ]
    });
  }

  private setupValidation(): void {
    // Real-time validation
    this.moneyInForm.valueChanges
      .pipe(
        debounceTime(300),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        this.performValidation();
        this.calculateFees();
        this.formatAmount();
      });
  }

  // Custom validators
  private amountValidator(control: AbstractControl): ValidationErrors | null {
    const value = parseFloat(control.value);
    const config = this.mergedConfig();
    
    if (isNaN(value)) return { invalidAmount: true };
    if (value < config.minAmount) return { belowMinimum: { min: config.minAmount, actual: value } };
    if (value > config.maxAmount) return { aboveMaximum: { max: config.maxAmount, actual: value } };
    
    const decimals = (value.toString().split('.')[1] || '').length;
    if (decimals > config.allowDecimalPlaces) {
      return { tooManyDecimals: { max: config.allowDecimalPlaces, actual: decimals } };
    }
    
    return null;
  }

  private currencyValidator(control: AbstractControl): ValidationErrors | null {
    const value = control.value;
    const allowedCurrencies = this.mergedConfig().allowedCurrencies;
    
    if (!allowedCurrencies.includes(value)) {
      return { invalidCurrency: { allowed: allowedCurrencies, actual: value } };
    }
    
    return null;
  }

  private sourceTypeValidator(control: AbstractControl): ValidationErrors | null {
    const value = control.value;
    const allowedTypes = this.mergedConfig().allowedSourceTypes;
    
    if (!allowedTypes.includes(value)) {
      return { invalidSourceType: { allowed: allowedTypes, actual: value } };
    }
    
    return null;
  }

  private performValidation(): void {
    const form = this.moneyInForm;
    const validation: MoneyInValidation = {
      isValid: form.valid,
      errors: this.getFormErrors(),
      amountValid: form.get('amount')?.valid || false,
      sourceValid: form.get('source')?.valid || false,
      currencyValid: form.get('currency')?.valid || false,
      limitsValid: !form.get('amount')?.errors?.['belowMinimum'] && !form.get('amount')?.errors?.['aboveMaximum']
    };

    this.currentValidation.set(validation);
    this.validationChange.emit(validation);
  }

  private getFormErrors(): string[] {
    const errors: string[] = [];
    
    Object.keys(this.moneyInForm.controls).forEach(key => {
      const control = this.moneyInForm.get(key);
      if (control?.errors) {
        Object.keys(control.errors).forEach(errorKey => {
          errors.push(`${key}.${errorKey}`);
        });
      }
    });

    return errors;
  }

  private calculateFees(): void {
    if (!this.mergedConfig().enableFeeCalculation) {
      this.calculatedFees.set(0);
      return;
    }

    const amount = parseFloat(this.moneyInForm.get('amount')?.value || '0');
    const sourceType = this.moneyInForm.get('sourceType')?.value;
    
    if (isNaN(amount) || !sourceType) {
      this.calculatedFees.set(0);
      return;
    }

    const sourceInfo = SOURCE_TYPES[sourceType as keyof typeof SOURCE_TYPES];
    const feeAmount = amount * sourceInfo.feeRate;
    
    this.calculatedFees.set(feeAmount);
    
    this.emitFinancialEvent('calculation', {
      action: 'fee_calculated',
      amount,
      sourceType,
      feeRate: sourceInfo.feeRate,
      feeAmount
    }, 'info');
  }

  private formatAmount(): void {
    const amount = parseFloat(this.moneyInForm.get('amount')?.value || '0');
    const currency = this.moneyInForm.get('currency')?.value || 'USD';
    
    if (isNaN(amount) || !this.mergedConfig().autoFormatting) {
      this.formattedAmount.set('');
      return;
    }

    const currencyInfo = SUPPORTED_CURRENCIES[currency as keyof typeof SUPPORTED_CURRENCIES];
    const formatted = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: currencyInfo.decimals,
      maximumFractionDigits: currencyInfo.decimals
    }).format(amount);

    this.formattedAmount.set(formatted);
    this.amountFormatted.emit({ raw: amount, formatted });
  }

  // Template helper methods
  getCurrencySymbol(currency: string): string {
    return SUPPORTED_CURRENCIES[currency as keyof typeof SUPPORTED_CURRENCIES]?.symbol || currency;
  }

  getSourceTypeInfo(sourceType: string) {
    return SOURCE_TYPES[sourceType as keyof typeof SOURCE_TYPES];
  }

  getTotalAmount(): number {
    const amount = parseFloat(this.moneyInForm.get('amount')?.value || '0');
    const fees = this.calculatedFees();
    return amount + fees;
  }

  getFormattedTotalAmount(): string {
    const total = this.getTotalAmount();
    const currency = this.moneyInForm.get('currency')?.value || 'USD';
    
    const currencyInfo = SUPPORTED_CURRENCIES[currency as keyof typeof SUPPORTED_CURRENCIES];
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: currencyInfo.decimals,
      maximumFractionDigits: currencyInfo.decimals
    }).format(total);
  }

  // Public methods
  submitTransaction(): void {
    if (!this.moneyInForm.valid || this.disabled) return;

    this.isProcessing.set(true);
    
    const formValue = this.moneyInForm.value;
    const transaction: MoneyInTransaction = {
      id: this.generateTransactionId(),
      amount: parseFloat(formValue.amount),
      currency: formValue.currency,
      source: formValue.source,
      sourceType: formValue.sourceType,
      description: formValue.description,
      reference: formValue.reference,
      timestamp: new Date().toISOString(),
      status: 'pending',
      fees: this.calculatedFees(),
      metadata: {
        formattedAmount: this.formattedAmount(),
        totalAmount: this.getTotalAmount(),
        submittedAt: new Date().toISOString()
      }
    };

    this.transactionCreated.emit(transaction);
    
    this.emitFinancialEvent('submit', {
      transactionId: transaction.id,
      amount: transaction.amount,
      currency: transaction.currency,
      sourceType: transaction.sourceType
    }, 'info');

    // Simulate processing
    setTimeout(() => {
      this.isProcessing.set(false);
    }, 2000);
  }

  resetForm(): void {
    this.moneyInForm.reset();
    this.isProcessing.set(false);
    this.calculatedFees.set(0);
    this.formattedAmount.set('');
    
    // Reset to default values
    this.moneyInForm.patchValue({
      currency: this.mergedConfig().defaultCurrency,
      sourceType: 'bank_transfer'
    });
  }

  // Utility methods
  private generateTransactionId(): string {
    return `MI_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  private emitFinancialEvent(type: FinancialEvent['type'], details: any, severity: FinancialEvent['severity']): void {
    const event: FinancialEvent = {
      type,
      timestamp: new Date().toISOString(),
      details,
      severity
    };

    this.financialEvent.emit(event);
  }

  // Accessibility helpers
  getAmountAriaLabel(): string {
    const validation = this.currentValidation();
    const hasError = !validation.amountValid && this.moneyInForm.get('amount')?.touched;
    return hasError ? 'Amount field has validation errors' : 'Transaction amount';
  }

  getFormStatusAriaLabel(): string {
    const validation = this.currentValidation();
    if (validation.isValid) return 'Form is valid and ready to submit';
    return `Form has ${validation.errors.length} validation errors`;
  }
}
