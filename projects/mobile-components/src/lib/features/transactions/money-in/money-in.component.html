<!-- Money In Transaction Component Template -->
<div [class]="containerClasses()" [attr.aria-label]="'Money in transaction form'" role="region">
  
  <!-- Form Header -->
  <div class="form-header" *ngIf="showLabels">
    <h3 class="form-title">Add Money In</h3>
    <div class="form-status" [attr.aria-label]="getFormStatusAriaLabel()">
      <span class="status-indicator" 
            [class]="currentValidation().isValid ? 'valid' : 'invalid'">
        {{ currentValidation().isValid ? '✓' : '⚠️' }}
      </span>
      <span class="status-text">
        {{ currentValidation().isValid ? 'Ready to submit' : 'Please check form' }}
      </span>
    </div>
  </div>

  <!-- Transaction Form -->
  <form [formGroup]="moneyInForm" 
        (ngSubmit)="submitTransaction()" 
        class="money-in-form"
        [class.processing]="isProcessing()"
        novalidate>

    <!-- Amount and Currency Row -->
    <div class="form-row amount-currency-row">
      <!-- Amount Field -->
      <div class="form-group amount-group">
        <label for="amount" class="form-label" *ngIf="showLabels">
          Amount
          <span class="required">*</span>
        </label>
        
        <div class="input-wrapper amount-wrapper">
          <span class="currency-prefix">
            {{ getCurrencySymbol(moneyInForm.get('currency')?.value || 'USD') }}
          </span>
          <input 
            id="amount"
            type="number"
            formControlName="amount"
            class="form-input amount-input"
            placeholder="0.00"
            [attr.aria-label]="getAmountAriaLabel()"
            [attr.aria-invalid]="moneyInForm.get('amount')?.invalid && moneyInForm.get('amount')?.touched"
            [attr.aria-describedby]="'amount-errors'"
            [step]="mergedConfig().allowDecimalPlaces === 0 ? '1' : '0.01'"
            [min]="mergedConfig().minAmount"
            [max]="mergedConfig().maxAmount"
            [disabled]="disabled">
          
          <!-- Validation Indicator -->
          <div class="validation-indicator">
            <span class="validation-check" 
                  [class.valid]="currentValidation().amountValid"
                  [class.invalid]="!currentValidation().amountValid && moneyInForm.get('amount')?.touched"
                  title="Amount Validation">
              {{ currentValidation().amountValid ? '✓' : '✗' }}
            </span>
          </div>
        </div>

        <!-- Formatted Amount Display -->
        <div class="formatted-display" *ngIf="formattedAmount() && mergedConfig().autoFormatting">
          <span class="formatted-text">{{ formattedAmount() }}</span>
        </div>
        
        <!-- Amount Errors -->
        <div class="form-errors" 
             id="amount-errors"
             *ngIf="moneyInForm.get('amount')?.touched && moneyInForm.get('amount')?.errors">
          <div class="error-message" *ngIf="moneyInForm.get('amount')?.errors?.['required']">
            Amount is required
          </div>
          <div class="error-message" *ngIf="moneyInForm.get('amount')?.errors?.['invalidAmount']">
            Please enter a valid amount
          </div>
          <div class="error-message" *ngIf="moneyInForm.get('amount')?.errors?.['belowMinimum']">
            Amount must be at least {{ getCurrencySymbol(moneyInForm.get('currency')?.value) }}{{ mergedConfig().minAmount }}
          </div>
          <div class="error-message" *ngIf="moneyInForm.get('amount')?.errors?.['aboveMaximum']">
            Amount cannot exceed {{ getCurrencySymbol(moneyInForm.get('currency')?.value) }}{{ mergedConfig().maxAmount }}
          </div>
          <div class="error-message" *ngIf="moneyInForm.get('amount')?.errors?.['tooManyDecimals']">
            Maximum {{ mergedConfig().allowDecimalPlaces }} decimal places allowed
          </div>
        </div>
      </div>

      <!-- Currency Field -->
      <div class="form-group currency-group">
        <label for="currency" class="form-label" *ngIf="showLabels">
          Currency
          <span class="required">*</span>
        </label>
        
        <div class="input-wrapper">
          <select 
            id="currency"
            formControlName="currency"
            class="form-select currency-select"
            [attr.aria-label]="'Transaction currency'"
            [disabled]="disabled">
            <option *ngFor="let currency of availableCurrencies()" 
                    [value]="currency.code">
              {{ currency.symbol }} {{ currency.code }} - {{ currency.name }}
            </option>
          </select>
        </div>
        
        <div class="form-errors" 
             *ngIf="moneyInForm.get('currency')?.touched && moneyInForm.get('currency')?.errors">
          <div class="error-message" *ngIf="moneyInForm.get('currency')?.errors?.['required']">
            Currency is required
          </div>
          <div class="error-message" *ngIf="moneyInForm.get('currency')?.errors?.['invalidCurrency']">
            Selected currency is not supported
          </div>
        </div>
      </div>
    </div>

    <!-- Source and Source Type Row -->
    <div class="form-row source-row">
      <!-- Source Field -->
      <div class="form-group source-group">
        <label for="source" class="form-label" *ngIf="showLabels">
          Source
          <span class="required">*</span>
        </label>
        
        <div class="input-wrapper">
          <input 
            id="source"
            type="text"
            formControlName="source"
            class="form-input source-input"
            placeholder="e.g., Bank of America, John Smith"
            [attr.aria-label]="'Money source'"
            [disabled]="disabled">
        </div>
        
        <div class="form-errors" 
             *ngIf="moneyInForm.get('source')?.touched && moneyInForm.get('source')?.errors">
          <div class="error-message" *ngIf="moneyInForm.get('source')?.errors?.['required']">
            Source is required
          </div>
          <div class="error-message" *ngIf="moneyInForm.get('source')?.errors?.['minlength']">
            Source must be at least 2 characters
          </div>
        </div>
      </div>

      <!-- Source Type Field -->
      <div class="form-group source-type-group">
        <label for="sourceType" class="form-label" *ngIf="showLabels">
          Source Type
          <span class="required">*</span>
        </label>
        
        <div class="input-wrapper">
          <select 
            id="sourceType"
            formControlName="sourceType"
            class="form-select source-type-select"
            [attr.aria-label]="'Source type'"
            [disabled]="disabled">
            <option *ngFor="let sourceType of availableSourceTypes()" 
                    [value]="sourceType.type">
              {{ sourceType.icon }} {{ sourceType.name }}
            </option>
          </select>
        </div>
        
        <div class="form-errors" 
             *ngIf="moneyInForm.get('sourceType')?.touched && moneyInForm.get('sourceType')?.errors">
          <div class="error-message" *ngIf="moneyInForm.get('sourceType')?.errors?.['required']">
            Source type is required
          </div>
          <div class="error-message" *ngIf="moneyInForm.get('sourceType')?.errors?.['invalidSourceType']">
            Selected source type is not allowed
          </div>
        </div>
      </div>
    </div>

    <!-- Optional Fields -->
    <div class="optional-fields" *ngIf="mergedConfig().requireDescription || mergedConfig().requireReference">
      
      <!-- Description Field -->
      <div class="form-group description-group" *ngIf="mergedConfig().requireDescription">
        <label for="description" class="form-label" *ngIf="showLabels">
          Description
          <span class="required">*</span>
        </label>
        
        <div class="input-wrapper">
          <textarea 
            id="description"
            formControlName="description"
            class="form-textarea description-textarea"
            placeholder="Transaction description..."
            rows="3"
            [attr.aria-label]="'Transaction description'"
            [disabled]="disabled"></textarea>
        </div>
        
        <div class="form-errors" 
             *ngIf="moneyInForm.get('description')?.touched && moneyInForm.get('description')?.errors">
          <div class="error-message" *ngIf="moneyInForm.get('description')?.errors?.['required']">
            Description is required
          </div>
        </div>
      </div>

      <!-- Reference Field -->
      <div class="form-group reference-group" *ngIf="mergedConfig().requireReference">
        <label for="reference" class="form-label" *ngIf="showLabels">
          Reference Number
          <span class="required">*</span>
        </label>
        
        <div class="input-wrapper">
          <input 
            id="reference"
            type="text"
            formControlName="reference"
            class="form-input reference-input"
            placeholder="Reference or transaction ID"
            [attr.aria-label]="'Reference number'"
            [disabled]="disabled">
        </div>
        
        <div class="form-errors" 
             *ngIf="moneyInForm.get('reference')?.touched && moneyInForm.get('reference')?.errors">
          <div class="error-message" *ngIf="moneyInForm.get('reference')?.errors?.['required']">
            Reference number is required
          </div>
        </div>
      </div>
    </div>

    <!-- Fee Calculation -->
    <div class="fee-calculation" *ngIf="mergedConfig().enableFeeCalculation && calculatedFees() > 0">
      <div class="fee-breakdown">
        <div class="fee-row">
          <span class="fee-label">Transaction Amount:</span>
          <span class="fee-value">{{ formattedAmount() }}</span>
        </div>
        <div class="fee-row">
          <span class="fee-label">Processing Fee:</span>
          <span class="fee-value">{{ getCurrencySymbol(moneyInForm.get('currency')?.value) }}{{ calculatedFees().toFixed(2) }}</span>
        </div>
        <div class="fee-row total-row">
          <span class="fee-label">Total Amount:</span>
          <span class="fee-value total-value">{{ getFormattedTotalAmount() }}</span>
        </div>
      </div>
    </div>

    <!-- Validation Summary -->
    <div class="validation-summary" *ngIf="showValidation && mergedConfig().showValidationSummary">
      <div class="validation-grid">
        <div class="validation-item" 
             [class.valid]="currentValidation().amountValid"
             [class.invalid]="!currentValidation().amountValid">
          <span class="validation-icon">{{ currentValidation().amountValid ? '✓' : '✗' }}</span>
          <span class="validation-text">Amount Valid</span>
        </div>
        <div class="validation-item" 
             [class.valid]="currentValidation().currencyValid"
             [class.invalid]="!currentValidation().currencyValid">
          <span class="validation-icon">{{ currentValidation().currencyValid ? '✓' : '✗' }}</span>
          <span class="validation-text">Currency Valid</span>
        </div>
        <div class="validation-item" 
             [class.valid]="currentValidation().sourceValid"
             [class.invalid]="!currentValidation().sourceValid">
          <span class="validation-icon">{{ currentValidation().sourceValid ? '✓' : '✗' }}</span>
          <span class="validation-text">Source Valid</span>
        </div>
        <div class="validation-item" 
             [class.valid]="currentValidation().limitsValid"
             [class.invalid]="!currentValidation().limitsValid">
          <span class="validation-icon">{{ currentValidation().limitsValid ? '✓' : '✗' }}</span>
          <span class="validation-text">Within Limits</span>
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="form-actions">
      <button 
        type="submit"
        class="btn btn-submit"
        [disabled]="!moneyInForm.valid || disabled || isProcessing()"
        [attr.aria-label]="'Submit money in transaction'"
        [class.loading]="isProcessing()">
        <span class="btn-text" *ngIf="!isProcessing()">
          Add Money In
        </span>
        <span class="btn-loading" *ngIf="isProcessing()">
          <span class="spinner"></span>
          Processing...
        </span>
      </button>
      
      <button 
        type="button"
        class="btn btn-reset"
        (click)="resetForm()"
        [disabled]="disabled || isProcessing()"
        [attr.aria-label]="'Reset form'">
        Reset
      </button>
    </div>

  </form>

  <!-- Loading Overlay -->
  <div class="loading-overlay" *ngIf="isProcessing()">
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <div class="loading-text">Processing your transaction...</div>
    </div>
  </div>

</div>
