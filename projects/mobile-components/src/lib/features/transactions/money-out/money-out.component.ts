import { Component, Input, Output, EventEmitter, computed, signal, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';

// Money Out Transaction interfaces
interface MoneyOutTransaction {
  id?: string;
  amount: number;
  currency: string;
  recipient: string;
  recipientType: 'bank_transfer' | 'cash_withdrawal' | 'check' | 'wire' | 'ach' | 'bill_payment' | 'other';
  description?: string;
  reference?: string;
  timestamp: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  fees?: number;
  exchangeRate?: number;
  metadata?: Record<string, any>;
}

interface MoneyOutConfig {
  allowedCurrencies: string[];
  allowedRecipientTypes: string[];
  minAmount: number;
  maxAmount: number;
  dailyLimit: number;
  requireDescription: boolean;
  requireReference: boolean;
  enableFeeCalculation: boolean;
  enableExchangeRate: boolean;
  allowDecimalPlaces: number;
  defaultCurrency: string;
  autoFormatting: boolean;
  requireConfirmation: boolean;
}

const RECIPIENT_TYPES = {
  bank_transfer: { name: 'Bank Transfer', icon: '🏦', feeRate: 0.002 },
  cash_withdrawal: { name: 'Cash Withdrawal', icon: '💵', feeRate: 0.01 },
  check: { name: 'Check Payment', icon: '📋', feeRate: 0.005 },
  wire: { name: 'Wire Transfer', icon: '📡', feeRate: 0.025 },
  ach: { name: 'ACH Transfer', icon: '🔄', feeRate: 0.003 },
  bill_payment: { name: 'Bill Payment', icon: '📄', feeRate: 0.001 },
  other: { name: 'Other', icon: '📤', feeRate: 0.015 }
};

@Component({
  selector: 'lib-money-out',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './money-out.component.html',
  styleUrl: './money-out.component.css'
})
export class MoneyOutComponent implements OnInit, OnDestroy {
  // Standard UI inputs for LP-GO builder integration
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() disabled: boolean = false;

  // Money Out specific inputs
  @Input() config: Partial<MoneyOutConfig> = {};
  @Input() initialData: Partial<MoneyOutTransaction> = {};
  @Input() showLabels: boolean = true;
  @Input() showValidation: boolean = true;
  @Input() darkMode: boolean = false;

  // Event outputs
  @Output() transactionCreated = new EventEmitter<MoneyOutTransaction>();
  @Output() confirmationRequired = new EventEmitter<MoneyOutTransaction>();

  private destroy$ = new Subject<void>();
  
  moneyOutForm!: FormGroup;
  isProcessing = signal(false);
  calculatedFees = signal<number>(0);
  formattedAmount = signal<string>('');
  showConfirmation = signal(false);

  // Default configuration
  private defaultConfig: MoneyOutConfig = {
    allowedCurrencies: ['USD', 'EUR', 'GBP'],
    allowedRecipientTypes: ['bank_transfer', 'ach', 'bill_payment', 'check'],
    minAmount: 1.00,
    maxAmount: 10000,
    dailyLimit: 25000,
    requireDescription: true,
    requireReference: false,
    enableFeeCalculation: true,
    enableExchangeRate: false,
    allowDecimalPlaces: 2,
    defaultCurrency: 'USD',
    autoFormatting: true,
    requireConfirmation: true
  };

  // Computed properties
  mergedConfig = computed(() => ({
    ...this.defaultConfig,
    ...this.config
  }));

  containerClasses = computed(() => {
    const base = 'money-out-container';
    const sizeClass = `size-${this.size}`;
    const variantClass = `variant-${this.variant}`;
    const roundedClass = `rounded-${this.rounded}`;
    const stateClasses = [
      this.disabled ? 'disabled' : '',
      this.darkMode ? 'dark' : '',
      this.isProcessing() ? 'processing' : '',
      this.showConfirmation() ? 'confirmation' : ''
    ].filter(Boolean);

    return [base, sizeClass, variantClass, roundedClass, ...stateClasses, this.className].filter(Boolean).join(' ');
  });

  availableRecipientTypes = computed(() => 
    this.mergedConfig().allowedRecipientTypes.map(type => ({
      type,
      ...RECIPIENT_TYPES[type as keyof typeof RECIPIENT_TYPES]
    }))
  );

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.initializeForm();
    this.setupValidation();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    const config = this.mergedConfig();
    
    this.moneyOutForm = this.fb.group({
      amount: [this.initialData.amount || '', [Validators.required, this.amountValidator.bind(this)]],
      currency: [this.initialData.currency || config.defaultCurrency, [Validators.required]],
      recipient: [this.initialData.recipient || '', [Validators.required, Validators.minLength(2)]],
      recipientType: [this.initialData.recipientType || 'bank_transfer', [Validators.required]],
      description: [this.initialData.description || '', config.requireDescription ? [Validators.required] : []],
      reference: [this.initialData.reference || '', config.requireReference ? [Validators.required] : []]
    });
  }

  private setupValidation(): void {
    this.moneyOutForm.valueChanges
      .pipe(debounceTime(300), takeUntil(this.destroy$))
      .subscribe(() => {
        this.calculateFees();
        this.formatAmount();
      });
  }

  private amountValidator(control: AbstractControl): ValidationErrors | null {
    const value = parseFloat(control.value);
    const config = this.mergedConfig();
    
    if (isNaN(value)) return { invalidAmount: true };
    if (value < config.minAmount) return { belowMinimum: { min: config.minAmount } };
    if (value > config.maxAmount) return { aboveMaximum: { max: config.maxAmount } };
    if (value > config.dailyLimit) return { exceedsDailyLimit: { limit: config.dailyLimit } };
    
    return null;
  }

  private calculateFees(): void {
    const amount = parseFloat(this.moneyOutForm.get('amount')?.value || '0');
    const recipientType = this.moneyOutForm.get('recipientType')?.value;
    
    if (isNaN(amount) || !recipientType) {
      this.calculatedFees.set(0);
      return;
    }

    const typeInfo = RECIPIENT_TYPES[recipientType as keyof typeof RECIPIENT_TYPES];
    const feeAmount = amount * typeInfo.feeRate;
    this.calculatedFees.set(feeAmount);
  }

  private formatAmount(): void {
    const amount = parseFloat(this.moneyOutForm.get('amount')?.value || '0');
    const currency = this.moneyOutForm.get('currency')?.value || 'USD';
    
    if (isNaN(amount)) {
      this.formattedAmount.set('');
      return;
    }

    const formatted = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);

    this.formattedAmount.set(formatted);
  }

  submitTransaction(): void {
    if (!this.moneyOutForm.valid || this.disabled) return;

    const transaction: MoneyOutTransaction = {
      id: `MO_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      ...this.moneyOutForm.value,
      amount: parseFloat(this.moneyOutForm.value.amount),
      timestamp: new Date().toISOString(),
      status: 'pending',
      fees: this.calculatedFees()
    };

    if (this.mergedConfig().requireConfirmation) {
      this.showConfirmation.set(true);
      this.confirmationRequired.emit(transaction);
    } else {
      this.processTransaction(transaction);
    }
  }

  processTransaction(transaction: MoneyOutTransaction): void {
    this.isProcessing.set(true);
    this.transactionCreated.emit(transaction);
    
    setTimeout(() => {
      this.isProcessing.set(false);
      this.showConfirmation.set(false);
    }, 2000);
  }

  resetForm(): void {
    this.moneyOutForm.reset();
    this.showConfirmation.set(false);
    this.isProcessing.set(false);
    this.calculatedFees.set(0);
    this.formattedAmount.set('');
  }

  getTotalAmount(): number {
    const amount = parseFloat(this.moneyOutForm.get('amount')?.value || '0');
    return amount + this.calculatedFees();
  }
}
