
/* Base Container Styles */
.money-out-container {
  @apply w-full max-w-md mx-auto bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Size Variants */
.money-out-container.size-xs {
  @apply max-w-xs text-xs;
}

.money-out-container.size-sm {
  @apply max-w-sm text-sm;
}

.money-out-container.size-md {
  @apply max-w-md text-base;
}

.money-out-container.size-lg {
  @apply max-w-lg text-lg;
}

.money-out-container.size-xl {
  @apply max-w-xl text-xl;
}

/* Variant Styles */
.money-out-container.variant-primary {
  @apply border-blue-300 bg-blue-50;
}

.money-out-container.variant-secondary {
  @apply border-gray-300 bg-gray-50;
}

.money-out-container.variant-success {
  @apply border-green-300 bg-green-50;
}

.money-out-container.variant-warning {
  @apply border-yellow-300 bg-yellow-50;
}

.money-out-container.variant-danger {
  @apply border-red-300 bg-red-50;
}

/* Rounded Variants */
.money-out-container.rounded-none {
  @apply rounded-none;
}

.money-out-container.rounded-sm {
  @apply rounded-sm;
}

.money-out-container.rounded-md {
  @apply rounded-md;
}

.money-out-container.rounded-lg {
  @apply rounded-lg;
}

.money-out-container.rounded-full {
  @apply rounded-full;
}

/* State Styles */
.money-out-container.disabled {
  @apply opacity-60 pointer-events-none;
}

.money-out-container.processing {
  @apply relative;
}

.money-out-container.confirmation {
  @apply relative;
}

/* Dark Mode */
.money-out-container.dark {
  @apply bg-gray-800 border-gray-600 text-white;
}

.money-out-container.dark .money-out-title {
  @apply text-white;
}

.money-out-container.dark .money-out-subtitle {
  @apply text-gray-300;
}

.money-out-container.dark .form-input,
.money-out-container.dark .form-textarea,
.money-out-container.dark .currency-select,
.money-out-container.dark .amount-input {
  @apply bg-gray-700 border-gray-600 text-white placeholder-gray-400;
}

.money-out-container.dark .form-label {
  @apply text-gray-300;
}

.money-out-container.dark .recipient-type-option {
  @apply bg-gray-700 border-gray-600;
}

.money-out-container.dark .recipient-type-option.selected {
  @apply bg-blue-600 border-blue-500;
}

.money-out-container.dark .transaction-summary {
  @apply bg-gray-700 border-gray-600;
}

.money-out-container.dark .confirmation-modal {
  @apply bg-gray-800 border-gray-600;
}

/* Header Section */
.money-out-header {
  @apply p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50;
}

.money-out-title {
  @apply text-xl font-semibold text-gray-900 mb-1;
}

.money-out-subtitle {
  @apply text-sm text-gray-600;
}

/* Form Styles */
.money-out-form {
  @apply p-4 space-y-4;
}

.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input,
.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200;
}

.form-input:disabled,
.form-textarea:disabled {
  @apply bg-gray-100 cursor-not-allowed;
}

/* Amount Section */
.amount-section {
  @apply space-y-3;
}

.amount-input-wrapper {
  @apply flex space-x-2;
}

.currency-select {
  @apply px-3 py-2 border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 flex-shrink-0;
  min-width: 80px;
}

.amount-input {
  @apply flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 text-right font-mono;
}

.amount-display {
  @apply flex items-center justify-between p-3 bg-gray-50 rounded-md border border-gray-200;
}

.formatted-amount {
  @apply text-lg font-semibold text-gray-900;
}

.fees-display {
  @apply text-sm text-gray-600;
}

/* Recipient Type Grid */
.recipient-type-grid {
  @apply grid grid-cols-2 gap-3;
}

.recipient-type-option {
  @apply relative cursor-pointer border-2 border-gray-200 rounded-lg p-3 hover:border-blue-300 transition-colors duration-200;
}

.recipient-type-option.selected {
  @apply border-blue-500 bg-blue-50;
}

.recipient-type-radio {
  @apply absolute opacity-0;
}

.recipient-type-content {
  @apply text-center space-y-1;
}

.recipient-type-icon {
  @apply block text-2xl;
}

.recipient-type-name {
  @apply block text-sm font-medium text-gray-900;
}

.recipient-type-fee {
  @apply block text-xs text-gray-500;
}

/* Transaction Summary */
.transaction-summary {
  @apply p-4 bg-gray-50 rounded-lg border border-gray-200 space-y-2;
}

.summary-row {
  @apply flex justify-between items-center;
}

.summary-row.total {
  @apply border-t border-gray-300 pt-2 font-semibold;
}

.summary-label {
  @apply text-gray-600;
}

.summary-value {
  @apply text-gray-900 font-medium;
}

.summary-value.fee {
  @apply text-orange-600;
}

/* Validation Errors */
.validation-errors {
  @apply space-y-1;
}

.error-message {
  @apply text-sm text-red-600 flex items-center;
}

.error-message::before {
  content: '⚠';
  @apply mr-1;
}

/* Form Actions */
.form-actions {
  @apply flex space-x-3 pt-4;
}

.btn {
  @apply flex-1 px-4 py-2 rounded-md font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed relative;
}

.btn.btn-secondary {
  @apply bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200 focus:ring-gray-500;
}

.btn.btn-primary {
  @apply bg-blue-600 text-white border border-transparent hover:bg-blue-700 focus:ring-blue-500;
}

.btn-text {
  @apply flex items-center justify-center;
}

.btn-spinner {
  @apply absolute inset-0 flex items-center justify-center;
}

.btn-spinner::after {
  content: '';
  @apply w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin;
}

/* Confirmation Modal */
.confirmation-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50;
}

.confirmation-modal {
  @apply bg-white rounded-lg shadow-xl max-w-sm w-full border border-gray-200 overflow-hidden;
}

.confirmation-header {
  @apply p-4 border-b border-gray-200 bg-gray-50;
}

.confirmation-title {
  @apply text-lg font-semibold text-gray-900;
}

.confirmation-content {
  @apply p-4;
}

.confirmation-details {
  @apply space-y-3;
}

.detail-row {
  @apply flex justify-between items-center;
}

.detail-row.total {
  @apply border-t border-gray-200 pt-3 font-semibold;
}

.detail-label {
  @apply text-gray-600;
}

.detail-value {
  @apply text-gray-900 font-medium;
}

.confirmation-actions {
  @apply p-4 border-t border-gray-200 flex space-x-3;
}

/* Processing Overlay */
.processing-overlay {
  @apply absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-40;
}

.processing-content {
  @apply text-center space-y-3;
}

.processing-spinner {
  @apply w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto;
}

.processing-text {
  @apply text-gray-600 font-medium;
}

/* Mobile Responsiveness */
@media (max-width: 640px) {
  .money-out-container {
    @apply max-w-full m-2;
  }
  
  .money-out-form {
    @apply p-3;
  }
  
  .recipient-type-grid {
    @apply grid-cols-1;
  }
  
  .form-actions {
    @apply flex-col space-x-0 space-y-2;
  }
  
  .btn {
    @apply w-full;
  }
}

/* Animation Utilities */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

.confirmation-modal {
  animation: fadeIn 0.3s ease-out;
}

.processing-overlay {
  animation: fadeIn 0.2s ease-out;
}

/* Focus Styles */
.form-input:focus,
.form-textarea:focus,
.currency-select:focus,
.amount-input:focus {
  @apply ring-2 ring-blue-500 border-blue-500;
}

.recipient-type-option:focus-within {
  @apply ring-2 ring-blue-500 border-blue-500;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .money-out-container {
    @apply border-2 border-black;
  }
  
  .form-input,
  .form-textarea,
  .currency-select,
  .amount-input {
    @apply border-2 border-gray-800;
  }
  
  .btn {
    @apply border-2;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .btn,
  .form-input,
  .form-textarea,
  .currency-select,
  .amount-input,
  .recipient-type-option {
    @apply transition-none;
  }
  
  .btn-spinner::after,
  .processing-spinner {
    @apply animate-none;
  }
  
  .confirmation-modal,
  .processing-overlay {
    animation: none;
  }
}

/* Print Styles */
@media print {
  .money-out-container {
    @apply shadow-none border border-black;
  }
  
  .confirmation-overlay,
  .processing-overlay {
    @apply hidden;
  }
  
  .btn {
    @apply border border-black;
  }
}