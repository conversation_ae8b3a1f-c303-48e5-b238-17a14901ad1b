<div [ngClass]="containerClasses()" [attr.aria-label]="'Money out transaction form'">
  <!-- Header Section -->
  <div class="money-out-header">
    <h2 class="money-out-title">Send Money</h2>
    <div class="money-out-subtitle">Transfer funds to external accounts</div>
  </div>

  <!-- Main Form -->
  <form [formGroup]="moneyOutForm" (ngSubmit)="submitTransaction()" class="money-out-form" novalidate>
    
    <!-- Amount Section -->
    <div class="form-group amount-section">
      <label *ngIf="showLabels" for="amount" class="form-label">Amount</label>
      <div class="amount-input-wrapper">
        <select formControlName="currency" class="currency-select" [disabled]="disabled" aria-label="Currency">
          <option *ngFor="let currency of mergedConfig().allowedCurrencies" [value]="currency">
            {{ currency }}
          </option>
        </select>
        <input
          id="amount"
          type="number"
          formControlName="amount"
          class="amount-input"
          [placeholder]="'Enter amount'"
          [disabled]="disabled"
          [step]="0.01"
          [min]="mergedConfig().minAmount"
          [max]="mergedConfig().maxAmount"
          aria-describedby="amount-help amount-errors"
          autocomplete="off"
        />
      </div>
      
      <!-- Amount Display -->
      <div class="amount-display" *ngIf="formattedAmount()">
        <span class="formatted-amount">{{ formattedAmount() }}</span>
        <span class="fees-display" *ngIf="calculatedFees() > 0">
          + {{ calculatedFees() | currency:moneyOutForm.get('currency')?.value:'symbol':'1.2-2' }} fee
        </span>
      </div>

      <!-- Amount Validation -->
      <div *ngIf="showValidation && moneyOutForm.get('amount')?.errors && moneyOutForm.get('amount')?.touched" 
           class="validation-errors" id="amount-errors" role="alert">
        <div *ngIf="moneyOutForm.get('amount')?.errors?.['required']" class="error-message">
          Amount is required
        </div>
        <div *ngIf="moneyOutForm.get('amount')?.errors?.['invalidAmount']" class="error-message">
          Please enter a valid amount
        </div>
        <div *ngIf="moneyOutForm.get('amount')?.errors?.['belowMinimum']" class="error-message">
          Minimum amount is {{ moneyOutForm.get('amount')?.errors?.['belowMinimum'].min | currency:moneyOutForm.get('currency')?.value }}
        </div>
        <div *ngIf="moneyOutForm.get('amount')?.errors?.['aboveMaximum']" class="error-message">
          Maximum amount is {{ moneyOutForm.get('amount')?.errors?.['aboveMaximum'].max | currency:moneyOutForm.get('currency')?.value }}
        </div>
        <div *ngIf="moneyOutForm.get('amount')?.errors?.['exceedsDailyLimit']" class="error-message">
          Exceeds daily limit of {{ moneyOutForm.get('amount')?.errors?.['exceedsDailyLimit'].limit | currency:moneyOutForm.get('currency')?.value }}
        </div>
      </div>
    </div>

    <!-- Recipient Section -->
    <div class="form-group recipient-section">
      <label *ngIf="showLabels" for="recipient" class="form-label">Recipient</label>
      <input
        id="recipient"
        type="text"
        formControlName="recipient"
        class="form-input"
        placeholder="Enter recipient name or account"
        [disabled]="disabled"
        aria-describedby="recipient-help recipient-errors"
        autocomplete="off"
      />
      <div *ngIf="showValidation && moneyOutForm.get('recipient')?.errors && moneyOutForm.get('recipient')?.touched" 
           class="validation-errors" id="recipient-errors" role="alert">
        <div *ngIf="moneyOutForm.get('recipient')?.errors?.['required']" class="error-message">
          Recipient is required
        </div>
        <div *ngIf="moneyOutForm.get('recipient')?.errors?.['minlength']" class="error-message">
          Recipient name must be at least 2 characters
        </div>
      </div>
    </div>

    <!-- Recipient Type Selection -->
    <div class="form-group recipient-type-section">
      <label *ngIf="showLabels" class="form-label">Transfer Method</label>
      <div class="recipient-type-grid">
        <label *ngFor="let type of availableRecipientTypes()" 
               class="recipient-type-option"
               [class.selected]="moneyOutForm.get('recipientType')?.value === type.type">
          <input
            type="radio"
            formControlName="recipientType"
            [value]="type.type"
            [disabled]="disabled"
            class="recipient-type-radio"
          />
          <div class="recipient-type-content">
            <span class="recipient-type-icon">{{ type.icon }}</span>
            <span class="recipient-type-name">{{ type.name }}</span>
            <span class="recipient-type-fee">{{ type.feeRate * 100 }}% fee</span>
          </div>
        </label>
      </div>
    </div>

    <!-- Description Section -->
    <div class="form-group description-section" *ngIf="mergedConfig().requireDescription">
      <label *ngIf="showLabels" for="description" class="form-label">Description</label>
      <textarea
        id="description"
        formControlName="description"
        class="form-textarea"
        placeholder="Enter transaction description"
        [disabled]="disabled"
        rows="3"
        aria-describedby="description-help description-errors"
      ></textarea>
      <div *ngIf="showValidation && moneyOutForm.get('description')?.errors && moneyOutForm.get('description')?.touched" 
           class="validation-errors" id="description-errors" role="alert">
        <div *ngIf="moneyOutForm.get('description')?.errors?.['required']" class="error-message">
          Description is required
        </div>
      </div>
    </div>

    <!-- Reference Section -->
    <div class="form-group reference-section" *ngIf="mergedConfig().requireReference">
      <label *ngIf="showLabels" for="reference" class="form-label">Reference Number</label>
      <input
        id="reference"
        type="text"
        formControlName="reference"
        class="form-input"
        placeholder="Enter reference number"
        [disabled]="disabled"
        aria-describedby="reference-help reference-errors"
        autocomplete="off"
      />
      <div *ngIf="showValidation && moneyOutForm.get('reference')?.errors && moneyOutForm.get('reference')?.touched" 
           class="validation-errors" id="reference-errors" role="alert">
        <div *ngIf="moneyOutForm.get('reference')?.errors?.['required']" class="error-message">
          Reference number is required
        </div>
      </div>
    </div>

    <!-- Transaction Summary -->
    <div class="transaction-summary" *ngIf="formattedAmount() && !showConfirmation()">
      <div class="summary-row">
        <span class="summary-label">Amount:</span>
        <span class="summary-value">{{ formattedAmount() }}</span>
      </div>
      <div class="summary-row" *ngIf="calculatedFees() > 0">
        <span class="summary-label">Fee:</span>
        <span class="summary-value fee">{{ calculatedFees() | currency:moneyOutForm.get('currency')?.value:'symbol':'1.2-2' }}</span>
      </div>
      <div class="summary-row total">
        <span class="summary-label">Total:</span>
        <span class="summary-value">{{ getTotalAmount() | currency:moneyOutForm.get('currency')?.value:'symbol':'1.2-2' }}</span>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="form-actions">
      <button
        type="button"
        class="btn btn-secondary"
        (click)="resetForm()"
        [disabled]="disabled || isProcessing()"
      >
        Reset
      </button>
      <button
        type="submit"
        class="btn btn-primary"
        [disabled]="!moneyOutForm.valid || disabled || isProcessing()"
        [attr.aria-busy]="isProcessing()"
      >
        <span *ngIf="!isProcessing()" class="btn-text">Send Money</span>
        <span *ngIf="isProcessing()" class="btn-text">Processing...</span>
        <div *ngIf="isProcessing()" class="btn-spinner" aria-hidden="true"></div>
      </button>
    </div>
  </form>

  <!-- Confirmation Modal -->
  <div *ngIf="showConfirmation()" class="confirmation-overlay" role="dialog" aria-modal="true" aria-labelledby="confirmation-title">
    <div class="confirmation-modal">
      <div class="confirmation-header">
        <h3 id="confirmation-title" class="confirmation-title">Confirm Transaction</h3>
      </div>
      
      <div class="confirmation-content">
        <div class="confirmation-details">
          <div class="detail-row">
            <span class="detail-label">Recipient:</span>
            <span class="detail-value">{{ moneyOutForm.get('recipient')?.value }}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Method:</span>
            <span class="detail-value">{{ (availableRecipientTypes() | json) }}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Amount:</span>
            <span class="detail-value">{{ formattedAmount() }}</span>
          </div>
          <div class="detail-row" *ngIf="calculatedFees() > 0">
            <span class="detail-label">Fee:</span>
            <span class="detail-value">{{ calculatedFees() | currency:moneyOutForm.get('currency')?.value:'symbol':'1.2-2' }}</span>
          </div>
          <div class="detail-row total">
            <span class="detail-label">Total:</span>
            <span class="detail-value">{{ getTotalAmount() | currency:moneyOutForm.get('currency')?.value:'symbol':'1.2-2' }}</span>
          </div>
        </div>
      </div>

      <div class="confirmation-actions">
        <button
          type="button"
          class="btn btn-secondary"
          (click)="showConfirmation.set(false)"
          [disabled]="isProcessing()"
        >
          Cancel
        </button>
        <button
          type="button"
          class="btn btn-primary"
          (click)="processTransaction(moneyOutForm.value)"
          [disabled]="isProcessing()"
        >
          <span *ngIf="!isProcessing()">Confirm Transfer</span>
          <span *ngIf="isProcessing()">Processing...</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Processing Overlay -->
  <div *ngIf="isProcessing() && !showConfirmation()" class="processing-overlay" role="status" aria-live="polite">
    <div class="processing-content">
      <div class="processing-spinner" aria-hidden="true"></div>
      <div class="processing-text">Processing your transaction...</div>
    </div>
  </div>
</div>
