import { Component, Input, Output, EventEmitter, computed, signal, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

// Transaction Summary interfaces
interface TransactionSummaryData {
  totalIncome: number;
  totalExpenses: number;
  netAmount: number;
  transactionCount: number;
  averageTransaction: number;
  currency: string;
  period: string;
  periodType: 'day' | 'week' | 'month' | 'quarter' | 'year' | 'custom';
  startDate: string;
  endDate: string;
  categoryBreakdown: CategorySummary[];
  topMerchants: MerchantSummary[];
  trends?: TrendData;
}

interface CategorySummary {
  category: string;
  amount: number;
  percentage: number;
  transactionCount: number;
  icon: string;
  color: string;
}

interface MerchantSummary {
  merchant: string;
  amount: number;
  transactionCount: number;
  category: string;
  percentage: number;
}

interface TrendData {
  previousPeriod: {
    income: number;
    expenses: number;
    net: number;
  };
  changes: {
    incomeChange: number;
    expensesChange: number;
    netChange: number;
  };
  percentageChanges: {
    incomePercentage: number;
    expensesPercentage: number;
    netPercentage: number;
  };
}

interface TransactionSummaryConfig {
  showIncome: boolean;
  showExpenses: boolean;
  showNetAmount: boolean;
  showTransactionCount: boolean;
  showAverageTransaction: boolean;
  showCategoryBreakdown: boolean;
  showTopMerchants: boolean;
  showTrends: boolean;
  showPercentages: boolean;
  maxCategories: number;
  maxMerchants: number;
  enableCharts: boolean;
  chartType: 'pie' | 'bar' | 'donut' | 'line';
  compactView: boolean;
  highlightChanges: boolean;
}

@Component({
  selector: 'lib-transaction-summary',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './transaction-summary.component.html',
  styleUrl: './transaction-summary.component.css'
})
export class TransactionSummaryComponent implements OnInit {
  // Standard UI inputs for LP-GO builder integration
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() disabled: boolean = false;

  // Transaction Summary specific inputs
  @Input() summaryData: TransactionSummaryData | null = null;
  @Input() config: Partial<TransactionSummaryConfig> = {};
  @Input() showLabels: boolean = true;
  @Input() darkMode: boolean = false;
  @Input() interactive: boolean = true;

  // Event outputs
  @Output() categoryClicked = new EventEmitter<CategorySummary>();
  @Output() merchantClicked = new EventEmitter<MerchantSummary>();
  @Output() periodChanged = new EventEmitter<{ startDate: string; endDate: string }>();
  @Output() refreshRequested = new EventEmitter<void>();

  isLoading = signal(false);
  selectedView = signal<'overview' | 'categories' | 'merchants' | 'trends'>('overview');

  // Default configuration
  private defaultConfig: TransactionSummaryConfig = {
    showIncome: true,
    showExpenses: true,
    showNetAmount: true,
    showTransactionCount: true,
    showAverageTransaction: true,
    showCategoryBreakdown: true,
    showTopMerchants: true,
    showTrends: true,
    showPercentages: true,
    maxCategories: 5,
    maxMerchants: 3,
    enableCharts: false,
    chartType: 'pie',
    compactView: false,
    highlightChanges: true
  };

  // Default summary data
  private defaultSummaryData: TransactionSummaryData = {
    totalIncome: 4750.00,
    totalExpenses: 3240.50,
    netAmount: 1509.50,
    transactionCount: 127,
    averageTransaction: 37.44,
    currency: 'USD',
    period: 'October 2024',
    periodType: 'month',
    startDate: '2024-10-01',
    endDate: '2024-10-31',
    categoryBreakdown: [
      { category: 'groceries', amount: 1245.30, percentage: 38.4, transactionCount: 28, icon: '🛒', color: 'green' },
      { category: 'restaurants', amount: 890.50, percentage: 27.5, transactionCount: 19, icon: '🍽️', color: 'orange' },
      { category: 'transport', amount: 567.20, percentage: 17.5, transactionCount: 12, icon: '🚗', color: 'blue' },
      { category: 'entertainment', amount: 345.80, percentage: 10.7, transactionCount: 8, icon: '🎬', color: 'purple' },
      { category: 'utilities', amount: 191.70, percentage: 5.9, transactionCount: 4, icon: '⚡', color: 'yellow' }
    ],
    topMerchants: [
      { merchant: 'Whole Foods Market', amount: 456.78, transactionCount: 8, category: 'groceries', percentage: 14.1 },
      { merchant: 'Starbucks', amount: 234.50, transactionCount: 15, category: 'restaurants', percentage: 7.2 },
      { merchant: 'Shell Gas Station', amount: 189.30, transactionCount: 6, category: 'transport', percentage: 5.8 }
    ],
    trends: {
      previousPeriod: {
        income: 4200.00,
        expenses: 2980.25,
        net: 1219.75
      },
      changes: {
        incomeChange: 550.00,
        expensesChange: 260.25,
        netChange: 289.75
      },
      percentageChanges: {
        incomePercentage: 13.1,
        expensesPercentage: 8.7,
        netPercentage: 23.8
      }
    }
  };

  // Computed properties
  mergedConfig = computed(() => ({
    ...this.defaultConfig,
    ...this.config
  }));

  displaySummaryData = computed(() => 
    this.summaryData || this.defaultSummaryData
  );

  containerClasses = computed(() => {
    const base = 'transaction-summary-container';
    const sizeClass = `size-${this.size}`;
    const variantClass = `variant-${this.variant}`;
    const roundedClass = `rounded-${this.rounded}`;
    const stateClasses = [
      this.disabled ? 'disabled' : '',
      this.darkMode ? 'dark' : '',
      this.isLoading() ? 'loading' : '',
      this.mergedConfig().compactView ? 'compact' : ''
    ].filter(Boolean);

    return [base, sizeClass, variantClass, roundedClass, ...stateClasses, this.className].filter(Boolean).join(' ');
  });

  displayCategories = computed(() => {
    const categories = this.displaySummaryData().categoryBreakdown;
    return categories.slice(0, this.mergedConfig().maxCategories);
  });

  displayMerchants = computed(() => {
    const merchants = this.displaySummaryData().topMerchants;
    return merchants.slice(0, this.mergedConfig().maxMerchants);
  });

  incomeChangeClass = computed(() => {
    if (!this.displaySummaryData().trends) return '';
    const change = this.displaySummaryData().trends!.percentageChanges.incomePercentage;
    return change > 0 ? 'positive' : change < 0 ? 'negative' : 'neutral';
  });

  expensesChangeClass = computed(() => {
    if (!this.displaySummaryData().trends) return '';
    const change = this.displaySummaryData().trends!.percentageChanges.expensesPercentage;
    return change > 0 ? 'negative' : change < 0 ? 'positive' : 'neutral';
  });

  netChangeClass = computed(() => {
    if (!this.displaySummaryData().trends) return '';
    const change = this.displaySummaryData().trends!.percentageChanges.netPercentage;
    return change > 0 ? 'positive' : change < 0 ? 'negative' : 'neutral';
  });

  ngOnInit(): void {
    // Component initialization
  }

  setActiveView(view: 'overview' | 'categories' | 'merchants' | 'trends'): void {
    if (this.disabled) return;
    this.selectedView.set(view);
  }

  onCategoryClick(category: CategorySummary): void {
    if (!this.interactive || this.disabled) return;
    this.categoryClicked.emit(category);
  }

  onMerchantClick(merchant: MerchantSummary): void {
    if (!this.interactive || this.disabled) return;
    this.merchantClicked.emit(merchant);
  }

  refreshData(): void {
    if (this.disabled) return;
    
    this.isLoading.set(true);
    this.refreshRequested.emit();
    
    // Simulate loading delay
    setTimeout(() => {
      this.isLoading.set(false);
    }, 1000);
  }

  formatCurrency(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  }

  formatPercentage(percentage: number): string {
    return `${percentage >= 0 ? '+' : ''}${percentage.toFixed(1)}%`;
  }

  getChangeIcon(percentage: number): string {
    if (percentage > 0) return '📈';
    if (percentage < 0) return '📉';
    return '➡️';
  }

  getProgressBarWidth(percentage: number): string {
    return `${Math.min(percentage, 100)}%`;
  }

  isPositiveChange(value: number): boolean {
    return value > 0;
  }

  isNegativeChange(value: number): boolean {
    return value < 0;
  }

  getCategoryColor(category: CategorySummary): string {
    const colorMap: Record<string, string> = {
      green: '#10B981',
      orange: '#F59E0B',
      blue: '#3B82F6',
      purple: '#8B5CF6',
      yellow: '#EAB308',
      red: '#EF4444',
      gray: '#6B7280'
    };
    return colorMap[category.color] || colorMap['gray'];
  }
}
