
/* Base Container Styles */
.transaction-summary-container {
  @apply w-full max-w-6xl mx-auto bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden relative;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Size Variants */
.transaction-summary-container.size-xs {
  @apply max-w-sm text-xs;
}

.transaction-summary-container.size-sm {
  @apply max-w-md text-sm;
}

.transaction-summary-container.size-md {
  @apply max-w-6xl text-base;
}

.transaction-summary-container.size-lg {
  @apply max-w-7xl text-lg;
}

.transaction-summary-container.size-xl {
  @apply max-w-full text-xl;
}

/* Variant Styles */
.transaction-summary-container.variant-primary {
  @apply border-blue-300 bg-blue-50;
}

.transaction-summary-container.variant-secondary {
  @apply border-gray-300 bg-gray-50;
}

.transaction-summary-container.variant-success {
  @apply border-green-300 bg-green-50;
}

.transaction-summary-container.variant-warning {
  @apply border-yellow-300 bg-yellow-50;
}

.transaction-summary-container.variant-danger {
  @apply border-red-300 bg-red-50;
}

/* Rounded Variants */
.transaction-summary-container.rounded-none {
  @apply rounded-none;
}

.transaction-summary-container.rounded-sm {
  @apply rounded-sm;
}

.transaction-summary-container.rounded-md {
  @apply rounded-md;
}

.transaction-summary-container.rounded-lg {
  @apply rounded-lg;
}

.transaction-summary-container.rounded-full {
  @apply rounded-full;
}

/* State Styles */
.transaction-summary-container.disabled {
  @apply opacity-60 pointer-events-none;
}

.transaction-summary-container.loading {
  @apply relative;
}

.transaction-summary-container.compact {
  @apply max-w-2xl;
}

.transaction-summary-container.compact .metrics-grid {
  @apply grid-cols-2 lg:grid-cols-3;
}

.transaction-summary-container.compact .metric-card {
  @apply p-3;
}

/* Dark Mode */
.transaction-summary-container.dark {
  @apply bg-gray-800 border-gray-600 text-white;
}

.transaction-summary-container.dark .summary-title {
  @apply text-white;
}

.transaction-summary-container.dark .summary-period {
  @apply text-gray-300;
}

.transaction-summary-container.dark .metric-card {
  @apply bg-gray-700 border-gray-600;
}

.transaction-summary-container.dark .category-card,
.transaction-summary-container.dark .merchant-item,
.transaction-summary-container.dark .trend-card {
  @apply bg-gray-700 border-gray-600;
}

.transaction-summary-container.dark .nav-tab {
  @apply text-gray-300 hover:text-white;
}

.transaction-summary-container.dark .nav-tab.active {
  @apply text-white border-white;
}

/* Header Section */
.summary-header {
  @apply p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50 flex justify-between items-start;
}

.header-content {
  @apply flex-1;
}

.summary-title {
  @apply text-2xl font-semibold text-gray-900 mb-1;
}

.summary-period {
  @apply text-sm text-gray-600;
}

.header-actions {
  @apply flex space-x-2;
}

/* Navigation */
.summary-navigation {
  @apply border-b border-gray-200 bg-gray-50;
}

.nav-tab {
  @apply px-6 py-3 text-sm font-medium text-gray-600 hover:text-gray-900 border-b-2 border-transparent hover:border-gray-300 transition-colors duration-200 focus:outline-none focus:text-blue-600 focus:border-blue-600;
}

.nav-tab.active {
  @apply text-blue-600 border-blue-600;
}

/* Content */
.summary-content {
  @apply p-6 space-y-6;
}

/* Metrics Grid */
.metrics-grid {
  @apply grid grid-cols-2 lg:grid-cols-5 gap-4;
}

.metric-card {
  @apply bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-200;
}

.metric-header {
  @apply flex items-center space-x-2 mb-2;
}

.metric-icon {
  @apply text-xl;
}

.metric-label {
  @apply text-sm font-medium text-gray-600;
}

.metric-value {
  @apply text-xl font-semibold text-gray-900 mb-1;
}

.metric-value.positive {
  @apply text-green-600;
}

.metric-value.negative {
  @apply text-red-600;
}

.metric-change {
  @apply text-sm;
}

.metric-change .positive {
  @apply text-green-600;
}

.metric-change .negative {
  @apply text-red-600;
}

.metric-change .neutral {
  @apply text-gray-600;
}

/* Section Headers */
.section-header {
  @apply mb-4;
}

.section-title {
  @apply text-lg font-semibold text-gray-900;
}

/* Quick Categories */
.quick-categories {
  @apply bg-gray-50 rounded-lg p-4;
}

.category-list {
  @apply space-y-2;
}

.category-item {
  @apply flex items-center justify-between p-2 hover:bg-gray-100 rounded-md transition-colors duration-200;
}

.category-item.interactive {
  @apply cursor-pointer;
}

.category-icon {
  @apply text-lg mr-3;
}

.category-name {
  @apply flex-1 text-sm font-medium text-gray-900;
}

.category-amount {
  @apply text-sm font-semibold text-gray-700;
}

/* Categories Grid */
.categories-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4;
}

.category-card {
  @apply bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all duration-200;
}

.category-card.interactive {
  @apply cursor-pointer hover:border-blue-300;
}

.category-header {
  @apply flex items-center space-x-3 mb-3;
}

.category-header .category-icon {
  @apply text-2xl;
}

.category-header .category-name {
  @apply text-base font-medium text-gray-900;
}

.category-card .category-amount {
  @apply text-lg font-semibold text-gray-900 mb-2;
}

.category-details {
  @apply flex justify-between items-center mb-3 text-sm text-gray-600;
}

.category-percentage {
  @apply font-medium;
}

.category-count {
  @apply text-xs;
}

.category-progress {
  @apply w-full bg-gray-200 rounded-full h-2 overflow-hidden;
}

.progress-bar {
  @apply h-full transition-all duration-500 ease-out;
}

/* Merchants List */
.merchants-list {
  @apply space-y-3;
}

.merchant-item {
  @apply bg-white border border-gray-200 rounded-lg p-4 flex items-center space-x-4 hover:shadow-md transition-all duration-200;
}

.merchant-item.interactive {
  @apply cursor-pointer hover:border-blue-300;
}

.merchant-rank {
  @apply w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold;
}

.merchant-info {
  @apply flex-1;
}

.merchant-name {
  @apply font-medium text-gray-900;
}

.merchant-category {
  @apply text-sm text-gray-600;
}

.merchant-stats {
  @apply text-right;
}

.merchant-amount {
  @apply font-semibold text-gray-900;
}

.merchant-count {
  @apply text-sm text-gray-600;
}

.merchant-percentage {
  @apply text-sm font-medium text-gray-700 bg-gray-100 px-2 py-1 rounded;
}

/* Trends Grid */
.trends-grid {
  @apply grid grid-cols-1 md:grid-cols-3 gap-4;
}

.trend-card {
  @apply bg-white border border-gray-200 rounded-lg p-4 text-center;
}

.trend-header {
  @apply flex items-center justify-between mb-3;
}

.trend-label {
  @apply text-sm font-medium text-gray-600;
}

.trend-icon {
  @apply text-lg;
}

.trend-values {
  @apply space-y-1;
}

.trend-amount {
  @apply text-lg font-semibold text-gray-900;
}

.trend-percentage {
  @apply text-sm font-medium;
}

.trend-percentage.positive {
  @apply text-green-600;
}

.trend-percentage.negative {
  @apply text-red-600;
}

.trend-percentage.neutral {
  @apply text-gray-600;
}

/* Button Styles */
.btn {
  @apply px-4 py-2 rounded-md font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2;
}

.btn.btn-secondary {
  @apply bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200 focus:ring-gray-500;
}

.btn-icon {
  @apply text-sm;
}

.btn-icon.spinning {
  @apply animate-spin;
}

.btn-text {
  @apply text-sm;
}

/* Loading Overlay */
.loading-overlay {
  @apply absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-40;
}

.loading-content {
  @apply text-center space-y-3;
}

.loading-spinner {
  @apply w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto;
}

.loading-text {
  @apply text-gray-600 font-medium;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .transaction-summary-container {
    @apply max-w-full m-2;
  }
  
  .summary-header {
    @apply flex-col items-start space-y-3 p-4;
  }
  
  .header-actions {
    @apply w-full justify-end;
  }
  
  .summary-navigation {
    @apply overflow-x-auto;
  }
  
  .nav-tab {
    @apply px-4 py-2 whitespace-nowrap;
  }
  
  .metrics-grid {
    @apply grid-cols-1 gap-3;
  }
  
  .categories-grid {
    @apply grid-cols-1;
  }
  
  .trends-grid {
    @apply grid-cols-1;
  }
  
  .merchant-item {
    @apply flex-col items-start space-x-0 space-y-2;
  }
  
  .merchant-rank {
    @apply self-start;
  }
  
  .merchant-stats {
    @apply text-left self-stretch;
  }
}

@media (max-width: 640px) {
  .summary-content {
    @apply p-3;
  }
  
  .metric-card {
    @apply p-3;
  }
  
  .category-card,
  .merchant-item,
  .trend-card {
    @apply p-3;
  }
}

/* Focus Styles */
.nav-tab:focus,
.category-item:focus,
.category-card:focus,
.merchant-item:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .transaction-summary-container {
    @apply border-2 border-black;
  }
  
  .metric-card,
  .category-card,
  .merchant-item,
  .trend-card {
    @apply border-2 border-gray-800;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .btn,
  .nav-tab,
  .metric-card,
  .category-item,
  .category-card,
  .merchant-item,
  .progress-bar {
    @apply transition-none;
  }
  
  .loading-spinner,
  .btn-icon.spinning {
    @apply animate-none;
  }
}

/* Print Styles */
@media print {
  .transaction-summary-container {
    @apply shadow-none border border-black;
  }
  
  .loading-overlay,
  .header-actions,
  .summary-navigation {
    @apply hidden;
  }
  
  .summary-content {
    @apply break-inside-avoid;
  }
  
  .metric-card,
  .category-card,
  .merchant-item,
  .trend-card {
    @apply border border-black;
  }
}

/* Animation Keyframes */
@keyframes slideIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.summary-content {
  animation: slideIn 0.3s ease-out;
}

/* Empty State */
.empty-state {
  @apply text-center py-12;
}

.empty-icon {
  @apply text-6xl mb-4;
}

.empty-title {
  @apply text-xl font-semibold text-gray-900 mb-2;
}

.empty-message {
  @apply text-gray-600;
}