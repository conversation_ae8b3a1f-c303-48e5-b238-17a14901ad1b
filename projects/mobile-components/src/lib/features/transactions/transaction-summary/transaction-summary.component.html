<div [ngClass]="containerClasses()" [attr.aria-label]="'Transaction summary for ' + displaySummaryData().period">
  <!-- Header Section -->
  <div class="summary-header">
    <div class="header-content">
      <h2 class="summary-title">Transaction Summary</h2>
      <div class="summary-period">{{ displaySummaryData().period }}</div>
    </div>
    
    <div class="header-actions">
      <button
        type="button"
        class="btn btn-secondary refresh-btn"
        (click)="refreshData()"
        [disabled]="disabled || isLoading()"
        [attr.aria-busy]="isLoading()"
      >
        <span class="btn-icon" [class.spinning]="isLoading()">🔄</span>
        <span class="btn-text" *ngIf="!mergedConfig().compactView">Refresh</span>
      </button>
    </div>
  </div>

  <!-- Navigation Tabs -->
  <div class="summary-navigation" *ngIf="!mergedConfig().compactView">
    <button
      *ngFor="let view of ['overview', 'categories', 'merchants', 'trends']"
      type="button"
      class="nav-tab"
      [class.active]="selectedView() === view"
      (click)="setActiveView(view)"
      [disabled]="disabled"
    >
      {{ view | titlecase }}
    </button>
  </div>

  <!-- Overview Section -->
  <div class="summary-content" *ngIf="selectedView() === 'overview' || mergedConfig().compactView">
    <!-- Key Metrics -->
    <div class="metrics-grid">
      <!-- Total Income -->
      <div class="metric-card income-card" *ngIf="mergedConfig().showIncome">
        <div class="metric-header">
          <span class="metric-icon">💰</span>
          <span class="metric-label" *ngIf="showLabels">Income</span>
        </div>
        <div class="metric-value positive">
          {{ formatCurrency(displaySummaryData().totalIncome, displaySummaryData().currency) }}
        </div>
        <div class="metric-change" *ngIf="mergedConfig().showTrends && displaySummaryData().trends">
          <span [class]="incomeChangeClass()">
            {{ getChangeIcon(displaySummaryData().trends!.percentageChanges.incomePercentage) }}
            {{ formatPercentage(displaySummaryData().trends!.percentageChanges.incomePercentage) }}
          </span>
        </div>
      </div>

      <!-- Total Expenses -->
      <div class="metric-card expenses-card" *ngIf="mergedConfig().showExpenses">
        <div class="metric-header">
          <span class="metric-icon">💸</span>
          <span class="metric-label" *ngIf="showLabels">Expenses</span>
        </div>
        <div class="metric-value negative">
          {{ formatCurrency(displaySummaryData().totalExpenses, displaySummaryData().currency) }}
        </div>
        <div class="metric-change" *ngIf="mergedConfig().showTrends && displaySummaryData().trends">
          <span [class]="expensesChangeClass()">
            {{ getChangeIcon(displaySummaryData().trends!.percentageChanges.expensesPercentage) }}
            {{ formatPercentage(displaySummaryData().trends!.percentageChanges.expensesPercentage) }}
          </span>
        </div>
      </div>

      <!-- Net Amount -->
      <div class="metric-card net-card" *ngIf="mergedConfig().showNetAmount">
        <div class="metric-header">
          <span class="metric-icon">📊</span>
          <span class="metric-label" *ngIf="showLabels">Net</span>
        </div>
        <div class="metric-value" [class.positive]="displaySummaryData().netAmount > 0" [class.negative]="displaySummaryData().netAmount < 0">
          {{ formatCurrency(displaySummaryData().netAmount, displaySummaryData().currency) }}
        </div>
        <div class="metric-change" *ngIf="mergedConfig().showTrends && displaySummaryData().trends">
          <span [class]="netChangeClass()">
            {{ getChangeIcon(displaySummaryData().trends!.percentageChanges.netPercentage) }}
            {{ formatPercentage(displaySummaryData().trends!.percentageChanges.netPercentage) }}
          </span>
        </div>
      </div>

      <!-- Transaction Count -->
      <div class="metric-card count-card" *ngIf="mergedConfig().showTransactionCount">
        <div class="metric-header">
          <span class="metric-icon">📋</span>
          <span class="metric-label" *ngIf="showLabels">Transactions</span>
        </div>
        <div class="metric-value">
          {{ displaySummaryData().transactionCount }}
        </div>
      </div>

      <!-- Average Transaction -->
      <div class="metric-card average-card" *ngIf="mergedConfig().showAverageTransaction">
        <div class="metric-header">
          <span class="metric-icon">📈</span>
          <span class="metric-label" *ngIf="showLabels">Average</span>
        </div>
        <div class="metric-value">
          {{ formatCurrency(displaySummaryData().averageTransaction, displaySummaryData().currency) }}
        </div>
      </div>
    </div>

    <!-- Quick Category Preview -->
    <div class="quick-categories" *ngIf="mergedConfig().showCategoryBreakdown && !mergedConfig().compactView">
      <h3 class="section-title">Top Categories</h3>
      <div class="category-list">
        <div
          *ngFor="let category of displayCategories().slice(0, 3)"
          class="category-item"
          (click)="onCategoryClick(category)"
          [class.interactive]="interactive"
        >
          <span class="category-icon">{{ category.icon }}</span>
          <span class="category-name">{{ category.category | titlecase }}</span>
          <span class="category-amount">{{ formatCurrency(category.amount, displaySummaryData().currency) }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Categories Section -->
  <div class="summary-content" *ngIf="selectedView() === 'categories' && !mergedConfig().compactView">
    <div class="section-header">
      <h3 class="section-title">Category Breakdown</h3>
    </div>
    
    <div class="categories-grid">
      <div
        *ngFor="let category of displayCategories()"
        class="category-card"
        (click)="onCategoryClick(category)"
        [class.interactive]="interactive"
      >
        <div class="category-header">
          <span class="category-icon">{{ category.icon }}</span>
          <span class="category-name">{{ category.category | titlecase }}</span>
        </div>
        
        <div class="category-amount">
          {{ formatCurrency(category.amount, displaySummaryData().currency) }}
        </div>
        
        <div class="category-details" *ngIf="mergedConfig().showPercentages">
          <div class="category-percentage">{{ category.percentage.toFixed(1) }}%</div>
          <div class="category-count">{{ category.transactionCount }} transactions</div>
        </div>
        
        <div class="category-progress">
          <div 
            class="progress-bar"
            [style.width]="getProgressBarWidth(category.percentage)"
            [style.background-color]="getCategoryColor(category)"
          ></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Merchants Section -->
  <div class="summary-content" *ngIf="selectedView() === 'merchants' && !mergedConfig().compactView">
    <div class="section-header">
      <h3 class="section-title">Top Merchants</h3>
    </div>
    
    <div class="merchants-list">
      <div
        *ngFor="let merchant of displayMerchants(); let i = index"
        class="merchant-item"
        (click)="onMerchantClick(merchant)"
        [class.interactive]="interactive"
      >
        <div class="merchant-rank">{{ i + 1 }}</div>
        <div class="merchant-info">
          <div class="merchant-name">{{ merchant.merchant }}</div>
          <div class="merchant-category">{{ merchant.category | titlecase }}</div>
        </div>
        <div class="merchant-stats">
          <div class="merchant-amount">{{ formatCurrency(merchant.amount, displaySummaryData().currency) }}</div>
          <div class="merchant-count">{{ merchant.transactionCount }} transactions</div>
        </div>
        <div class="merchant-percentage" *ngIf="mergedConfig().showPercentages">
          {{ merchant.percentage.toFixed(1) }}%
        </div>
      </div>
    </div>
  </div>

  <!-- Trends Section -->
  <div class="summary-content" *ngIf="selectedView() === 'trends' && !mergedConfig().compactView && displaySummaryData().trends">
    <div class="section-header">
      <h3 class="section-title">Trends & Changes</h3>
    </div>
    
    <div class="trends-grid">
      <div class="trend-card">
        <div class="trend-header">
          <span class="trend-label">Income Change</span>
          <span class="trend-icon">{{ getChangeIcon(displaySummaryData().trends!.percentageChanges.incomePercentage) }}</span>
        </div>
        <div class="trend-values">
          <div class="trend-amount">{{ formatCurrency(displaySummaryData().trends!.changes.incomeChange, displaySummaryData().currency) }}</div>
          <div class="trend-percentage" [class]="incomeChangeClass()">
            {{ formatPercentage(displaySummaryData().trends!.percentageChanges.incomePercentage) }}
          </div>
        </div>
      </div>

      <div class="trend-card">
        <div class="trend-header">
          <span class="trend-label">Expenses Change</span>
          <span class="trend-icon">{{ getChangeIcon(displaySummaryData().trends!.percentageChanges.expensesPercentage) }}</span>
        </div>
        <div class="trend-values">
          <div class="trend-amount">{{ formatCurrency(displaySummaryData().trends!.changes.expensesChange, displaySummaryData().currency) }}</div>
          <div class="trend-percentage" [class]="expensesChangeClass()">
            {{ formatPercentage(displaySummaryData().trends!.percentageChanges.expensesPercentage) }}
          </div>
        </div>
      </div>

      <div class="trend-card">
        <div class="trend-header">
          <span class="trend-label">Net Change</span>
          <span class="trend-icon">{{ getChangeIcon(displaySummaryData().trends!.percentageChanges.netPercentage) }}</span>
        </div>
        <div class="trend-values">
          <div class="trend-amount">{{ formatCurrency(displaySummaryData().trends!.changes.netChange, displaySummaryData().currency) }}</div>
          <div class="trend-percentage" [class]="netChangeClass()">
            {{ formatPercentage(displaySummaryData().trends!.percentageChanges.netPercentage) }}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" *ngIf="isLoading()" role="status" aria-live="polite">
    <div class="loading-content">
      <div class="loading-spinner" aria-hidden="true"></div>
      <div class="loading-text">Updating summary...</div>
    </div>
  </div>
</div>
