import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MoneyInComponent } from './money-in/money-in.component';
import { MoneyOutComponent } from './money-out/money-out.component';
import { StatementsComponent } from './statements/statements.component';
import { TransactionCompactComponent } from './transaction-compact/transaction-compact.component';
import { TransactionSummaryComponent } from './transaction-summary/transaction-summary.component';
import { TransactionsComponent } from './transactions/transactions.component';
import { TransactionsFiltersComponent } from './transactions-filters/transactions-filters.component';
import { TransactionsListPlaceloadComponent } from './transactions-list-placeload/transactions-list-placeload.component';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    MoneyInComponent,
    MoneyOutComponent,
    StatementsComponent,
    TransactionCompactComponent,
    TransactionSummaryComponent,
    TransactionsComponent,
    TransactionsFiltersComponent,
    TransactionsListPlaceloadComponent
  ],
  exports: [
    MoneyInComponent,
    MoneyOutComponent,
    StatementsComponent,
    TransactionCompactComponent,
    TransactionSummaryComponent,
    TransactionsComponent,
    TransactionsFiltersComponent,
    TransactionsListPlaceloadComponent
  ]
})
export class TransactionsModule { }
