<div [ngClass]="containerClasses()" [attr.aria-label]="'Financial statements list'">
  <!-- Header Section -->
  <div class="statements-header">
    <h2 class="statements-title">Statements</h2>
    <div class="statements-subtitle">View and download your financial statements</div>
    
    <!-- Actions -->
    <div class="header-actions">
      <button
        type="button"
        class="btn btn-secondary refresh-btn"
        (click)="refreshStatements()"
        [disabled]="disabled || isLoading()"
        [attr.aria-busy]="isLoading()"
      >
        <span class="btn-icon" [class.spinning]="isLoading()">🔄</span>
        <span class="btn-text">Refresh</span>
      </button>
    </div>
  </div>

  <!-- Filter Section -->
  <div class="statements-filters" *ngIf="mergedConfig().enableFilter">
    <form [formGroup]="filterForm" class="filter-form">
      <!-- Search Input -->
      <div class="filter-group" *ngIf="mergedConfig().enableSearch">
        <label for="search" class="filter-label" *ngIf="showLabels">Search</label>
        <div class="search-wrapper">
          <input
            id="search"
            type="text"
            formControlName="search"
            class="filter-input search-input"
            placeholder="Search statements..."
            [disabled]="disabled"
            autocomplete="off"
          />
          <span class="search-icon">🔍</span>
        </div>
      </div>

      <!-- Type Filter -->
      <div class="filter-group">
        <label for="type" class="filter-label" *ngIf="showLabels">Type</label>
        <select
          id="type"
          formControlName="type"
          class="filter-select"
          [disabled]="disabled"
          aria-label="Statement type filter"
        >
          <option value="all">All Types</option>
          <option *ngFor="let type of availableTypes()" [value]="type.type">
            {{ type.name }}
          </option>
        </select>
      </div>

      <!-- Date Range Filters -->
      <div class="filter-group date-group">
        <label class="filter-label" *ngIf="showLabels">Date Range</label>
        <div class="date-range-wrapper">
          <input
            type="date"
            formControlName="dateFrom"
            class="filter-input date-input"
            placeholder="From"
            [disabled]="disabled"
          />
          <span class="date-separator">to</span>
          <input
            type="date"
            formControlName="dateTo"
            class="filter-input date-input"
            placeholder="To"
            [disabled]="disabled"
          />
        </div>
      </div>
    </form>
  </div>

  <!-- Statements List -->
  <div class="statements-list" role="list">
    <div
      *ngFor="let statement of filteredStatements(); trackBy: trackByStatementId"
      class="statement-item"
      [class.expired]="isExpired(statement)"
      [class.generating]="statement.status === 'generating'"
      [class.failed]="statement.status === 'failed'"
      (click)="selectStatement(statement)"
      role="listitem"
      [attr.aria-label]="statement.title + ' - ' + getStatusText(statement.status)"
      tabindex="0"
      (keydown.enter)="selectStatement(statement)"
      (keydown.space)="selectStatement(statement)"
    >
      <!-- Statement Card -->
      <div class="statement-card">
        <!-- Status Indicator -->
        <div class="statement-status">
          <span class="status-icon" [title]="getStatusText(statement.status)">
            {{ getStatusIcon(statement.status) }}
          </span>
        </div>

        <!-- Main Content -->
        <div class="statement-content">
          <div class="statement-header">
            <h3 class="statement-title">{{ statement.title }}</h3>
            <div class="statement-period">{{ statement.period }}</div>
          </div>

          <div class="statement-details">
            <div class="detail-row" *ngIf="mergedConfig().showBalance">
              <span class="detail-label">Balance:</span>
              <span class="detail-value balance">
                {{ statement.balance | currency:statement.currency:'symbol':'1.2-2' }}
              </span>
            </div>
            
            <div class="detail-row" *ngIf="mergedConfig().showTransactionCount">
              <span class="detail-label">Transactions:</span>
              <span class="detail-value">{{ statement.transactionCount }}</span>
            </div>
            
            <div class="detail-row" *ngIf="mergedConfig().showGeneratedDate && statement.generatedAt">
              <span class="detail-label">Generated:</span>
              <span class="detail-value">{{ formatDate(statement.generatedAt) }}</span>
            </div>
            
            <div class="detail-row" *ngIf="statement.size">
              <span class="detail-label">Size:</span>
              <span class="detail-value">{{ formatFileSize(statement.size) }}</span>
            </div>
          </div>

          <!-- Expiry Warning -->
          <div class="expiry-warning" *ngIf="isExpired(statement)">
            <span class="warning-icon">⚠️</span>
            <span class="warning-text">This statement has expired</span>
          </div>
        </div>

        <!-- Actions -->
        <div class="statement-actions">
          <button
            *ngIf="mergedConfig().enableDownload && statement.status === 'ready'"
            type="button"
            class="btn btn-primary download-btn"
            (click)="downloadStatement(statement, $event)"
            [disabled]="disabled || isExpired(statement)"
            [attr.aria-label]="'Download ' + statement.title"
          >
            <span class="btn-icon">⬇️</span>
            <span class="btn-text">Download</span>
          </button>

          <button
            *ngIf="statement.status === 'failed'"
            type="button"
            class="btn btn-secondary retry-btn"
            (click)="selectStatement(statement)"
            [disabled]="disabled"
            [attr.aria-label]="'Retry generating ' + statement.title"
          >
            <span class="btn-icon">🔄</span>
            <span class="btn-text">Retry</span>
          </button>
        </div>
      </div>

      <!-- Progress Bar for Generating -->
      <div class="progress-bar" *ngIf="statement.status === 'generating'">
        <div class="progress-fill"></div>
      </div>
    </div>

    <!-- Empty State -->
    <div class="empty-state" *ngIf="filteredStatements().length === 0" role="status">
      <div class="empty-icon">📄</div>
      <div class="empty-title">No statements found</div>
      <div class="empty-message">
        <span *ngIf="searchTerm() || selectedType() !== 'all'">
          Try adjusting your filters or search terms.
        </span>
        <span *ngIf="!searchTerm() && selectedType() === 'all'">
          Statements will appear here once they are generated.
        </span>
      </div>
    </div>
  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" *ngIf="isLoading()" role="status" aria-live="polite">
    <div class="loading-content">
      <div class="loading-spinner" aria-hidden="true"></div>
      <div class="loading-text">Refreshing statements...</div>
    </div>
  </div>

  <!-- Export Options -->
  <div class="export-section" *ngIf="mergedConfig().enableExport">
    <div class="export-header">
      <h3 class="export-title">Export Options</h3>
    </div>
    <div class="export-formats">
      <button
        *ngFor="let format of mergedConfig().exportFormats"
        type="button"
        class="btn btn-outline export-btn"
        [disabled]="disabled || filteredStatements().length === 0"
        [attr.aria-label]="'Export as ' + format.toUpperCase()"
      >
        <span class="btn-icon">📤</span>
        <span class="btn-text">{{ format.toUpperCase() }}</span>
      </button>
    </div>
  </div>
</div>