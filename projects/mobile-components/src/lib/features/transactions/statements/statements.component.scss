/* filepath: /Users/<USER>/Projects/clients/lp-angular/projects/mobile-components/src/lib/features/transactions/statements/statements.component.scss */

/* Base Container Styles */
.statements-container {
  @apply w-full max-w-4xl mx-auto bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Size Variants */
.statements-container.size-xs {
  @apply max-w-xs text-xs;
}

.statements-container.size-sm {
  @apply max-w-sm text-sm;
}

.statements-container.size-md {
  @apply max-w-4xl text-base;
}

.statements-container.size-lg {
  @apply max-w-5xl text-lg;
}

.statements-container.size-xl {
  @apply max-w-6xl text-xl;
}

/* Variant Styles */
.statements-container.variant-primary {
  @apply border-blue-300 bg-blue-50;
}

.statements-container.variant-secondary {
  @apply border-gray-300 bg-gray-50;
}

.statements-container.variant-success {
  @apply border-green-300 bg-green-50;
}

.statements-container.variant-warning {
  @apply border-yellow-300 bg-yellow-50;
}

.statements-container.variant-danger {
  @apply border-red-300 bg-red-50;
}

/* Rounded Variants */
.statements-container.rounded-none {
  @apply rounded-none;
}

.statements-container.rounded-sm {
  @apply rounded-sm;
}

.statements-container.rounded-md {
  @apply rounded-md;
}

.statements-container.rounded-lg {
  @apply rounded-lg;
}

.statements-container.rounded-full {
  @apply rounded-full;
}

/* State Styles */
.statements-container.disabled {
  @apply opacity-60 pointer-events-none;
}

.statements-container.loading {
  @apply relative;
}

/* Dark Mode */
.statements-container.dark {
  @apply bg-gray-800 border-gray-600 text-white;
}

.statements-container.dark .statements-title {
  @apply text-white;
}

.statements-container.dark .statements-subtitle {
  @apply text-gray-300;
}

.statements-container.dark .filter-input,
.statements-container.dark .filter-select {
  @apply bg-gray-700 border-gray-600 text-white placeholder-gray-400;
}

.statements-container.dark .filter-label {
  @apply text-gray-300;
}

.statements-container.dark .statement-card {
  @apply bg-gray-700 border-gray-600;
}

.statements-container.dark .statement-title {
  @apply text-white;
}

.statements-container.dark .statement-period {
  @apply text-gray-300;
}

.statements-container.dark .detail-label {
  @apply text-gray-400;
}

.statements-container.dark .detail-value {
  @apply text-gray-200;
}

/* Header Section */
.statements-header {
  @apply p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50 flex justify-between items-start;
}

.statements-title {
  @apply text-2xl font-semibold text-gray-900 mb-1;
}

.statements-subtitle {
  @apply text-sm text-gray-600;
}

.header-actions {
  @apply flex space-x-2;
}

/* Filter Section */
.statements-filters {
  @apply p-4 border-b border-gray-200 bg-gray-50;
}

.filter-form {
  @apply grid grid-cols-1 md:grid-cols-4 gap-4;
}

.filter-group {
  @apply space-y-1;
}

.filter-group.date-group {
  @apply md:col-span-2;
}

.filter-label {
  @apply block text-sm font-medium text-gray-700;
}

.filter-input,
.filter-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200;
}

.search-wrapper {
  @apply relative;
}

.search-input {
  @apply pr-10;
}

.search-icon {
  @apply absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400;
}

.date-range-wrapper {
  @apply flex items-center space-x-2;
}

.date-input {
  @apply flex-1;
}

.date-separator {
  @apply text-gray-500 text-sm;
}

/* Statements List */
.statements-list {
  @apply p-4 space-y-4;
}

.statement-item {
  @apply cursor-pointer transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg;
}

.statement-item:hover .statement-card {
  @apply shadow-md border-blue-300;
}

.statement-item.expired {
  @apply opacity-75;
}

.statement-item.generating {
  @apply cursor-default;
}

.statement-item.failed {
  @apply opacity-80;
}

.statement-card {
  @apply bg-white border border-gray-200 rounded-lg p-4 shadow-sm transition-all duration-200 relative;
}

.statement-item.expired .statement-card {
  @apply border-orange-300 bg-orange-50;
}

.statement-item.generating .statement-card {
  @apply border-blue-300 bg-blue-50;
}

.statement-item.failed .statement-card {
  @apply border-red-300 bg-red-50;
}

/* Statement Content */
.statement-status {
  @apply absolute top-4 right-4;
}

.status-icon {
  @apply text-lg;
}

.statement-content {
  @apply pr-12;
}

.statement-header {
  @apply mb-3;
}

.statement-title {
  @apply text-lg font-semibold text-gray-900 mb-1;
}

.statement-period {
  @apply text-sm text-gray-600;
}

.statement-details {
  @apply grid grid-cols-2 gap-2 mb-3;
}

.detail-row {
  @apply flex justify-between items-center;
}

.detail-label {
  @apply text-sm text-gray-600;
}

.detail-value {
  @apply text-sm font-medium text-gray-900;
}

.detail-value.balance {
  @apply text-green-600 font-semibold;
}

/* Expiry Warning */
.expiry-warning {
  @apply flex items-center space-x-2 mt-2 p-2 bg-orange-100 border border-orange-300 rounded-md;
}

.warning-icon {
  @apply text-orange-500;
}

.warning-text {
  @apply text-sm text-orange-700 font-medium;
}

/* Statement Actions */
.statement-actions {
  @apply flex justify-end space-x-2 mt-4;
}

/* Progress Bar */
.progress-bar {
  @apply w-full bg-gray-200 rounded-full h-2 mt-2 overflow-hidden;
}

.progress-fill {
  @apply h-full bg-blue-500 rounded-full animate-pulse;
  width: 100%;
  animation: progressSlide 2s ease-in-out infinite;
}

@keyframes progressSlide {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Button Styles */
.btn {
  @apply px-4 py-2 rounded-md font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2;
}

.btn.btn-primary {
  @apply bg-blue-600 text-white border border-transparent hover:bg-blue-700 focus:ring-blue-500;
}

.btn.btn-secondary {
  @apply bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200 focus:ring-gray-500;
}

.btn.btn-outline {
  @apply bg-transparent text-blue-600 border border-blue-600 hover:bg-blue-50 focus:ring-blue-500;
}

.btn-icon {
  @apply text-sm;
}

.btn-icon.spinning {
  @apply animate-spin;
}

.btn-text {
  @apply text-sm;
}

/* Empty State */
.empty-state {
  @apply text-center py-12;
}

.empty-icon {
  @apply text-6xl mb-4;
}

.empty-title {
  @apply text-xl font-semibold text-gray-900 mb-2;
}

.empty-message {
  @apply text-gray-600;
}

/* Loading Overlay */
.loading-overlay {
  @apply absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-40;
}

.loading-content {
  @apply text-center space-y-3;
}

.loading-spinner {
  @apply w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto;
}

.loading-text {
  @apply text-gray-600 font-medium;
}

/* Export Section */
.export-section {
  @apply p-4 border-t border-gray-200 bg-gray-50;
}

.export-header {
  @apply mb-3;
}

.export-title {
  @apply text-lg font-semibold text-gray-900;
}

.export-formats {
  @apply flex flex-wrap gap-2;
}

.export-btn {
  @apply text-sm;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .statements-container {
    @apply max-w-full m-2;
  }
  
  .statements-header {
    @apply flex-col items-start space-y-3 p-4;
  }
  
  .header-actions {
    @apply w-full justify-end;
  }
  
  .filter-form {
    @apply grid-cols-1;
  }
  
  .filter-group.date-group {
    @apply col-span-1;
  }
  
  .date-range-wrapper {
    @apply flex-col space-x-0 space-y-2;
  }
  
  .statement-details {
    @apply grid-cols-1;
  }
  
  .statement-actions {
    @apply flex-col space-x-0 space-y-2;
  }
  
  .btn {
    @apply w-full justify-center;
  }
  
  .export-formats {
    @apply flex-col;
  }
}

/* Focus Styles */
.filter-input:focus,
.filter-select:focus {
  @apply ring-2 ring-blue-500 border-blue-500;
}

.statement-item:focus {
  @apply ring-2 ring-blue-500 ring-offset-2;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .statements-container {
    @apply border-2 border-black;
  }
  
  .statement-card {
    @apply border-2;
  }
  
  .filter-input,
  .filter-select {
    @apply border-2 border-gray-800;
  }
  
  .btn {
    @apply border-2;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .btn,
  .filter-input,
  .filter-select,
  .statement-item,
  .statement-card {
    @apply transition-none;
  }
  
  .loading-spinner,
  .btn-icon.spinning,
  .progress-fill {
    @apply animate-none;
  }
}

/* Print Styles */
@media print {
  .statements-container {
    @apply shadow-none border border-black;
  }
  
  .loading-overlay {
    @apply hidden;
  }
  
  .header-actions,
  .statements-filters,
  .statement-actions,
  .export-section {
    @apply hidden;
  }
  
  .statement-card {
    @apply border border-black;
  }
}