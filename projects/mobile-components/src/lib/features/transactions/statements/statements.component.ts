import { Component, Input, Output, EventEmitter, computed, signal, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';

// Statement interfaces
interface Statement {
  id: string;
  title: string;
  period: string;
  startDate: string;
  endDate: string;
  type: 'monthly' | 'quarterly' | 'annual' | 'adhoc' | 'custom';
  status: 'ready' | 'generating' | 'failed' | 'expired';
  size?: number;
  downloadUrl?: string;
  transactionCount: number;
  balance: number;
  currency: string;
  generatedAt: string;
  expiresAt?: string;
  metadata?: Record<string, any>;
}

interface StatementConfig {
  enableDownload: boolean;
  enableFilter: boolean;
  enableSearch: boolean;
  showBalance: boolean;
  showTransactionCount: boolean;
  showGeneratedDate: boolean;
  itemsPerPage: number;
  allowedTypes: string[];
  autoRefresh: boolean;
  refreshInterval: number;
  maxRetentionDays: number;
  enableExport: boolean;
  exportFormats: string[];
}

const STATEMENT_TYPES = {
  monthly: { name: 'Monthly Statement', icon: '📅', description: 'Regular monthly financial summary' },
  quarterly: { name: 'Quarterly Statement', icon: '📊', description: 'Quarterly financial report' },
  annual: { name: 'Annual Statement', icon: '📈', description: 'Annual financial summary' },
  adhoc: { name: 'Ad-hoc Statement', icon: '📋', description: 'Custom date range statement' },
  custom: { name: 'Custom Statement', icon: '⚙️', description: 'User-defined statement' }
};

@Component({
  selector: 'lib-statements',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './statements.component.html',
  styleUrl: './statements.component.scss'
})
export class StatementsComponent implements OnInit, OnDestroy {
  // Standard UI inputs for LP-GO builder integration
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() disabled: boolean = false;

  // Statements specific inputs
  @Input() config: Partial<StatementConfig> = {};
  @Input() statements: Statement[] = [];
  @Input() showLabels: boolean = true;
  @Input() showValidation: boolean = true;
  @Input() darkMode: boolean = false;

  // Event outputs
  @Output() statementSelected = new EventEmitter<Statement>();
  @Output() downloadRequested = new EventEmitter<Statement>();
  @Output() filterChanged = new EventEmitter<any>();
  @Output() refreshRequested = new EventEmitter<void>();

  private destroy$ = new Subject<void>();
  
  filterForm!: FormGroup;
  isLoading = signal(false);
  searchTerm = signal('');
  selectedType = signal<string>('all');
  currentPage = signal(1);

  // Default configuration
  private defaultConfig: StatementConfig = {
    enableDownload: true,
    enableFilter: true,
    enableSearch: true,
    showBalance: true,
    showTransactionCount: true,
    showGeneratedDate: true,
    itemsPerPage: 10,
    allowedTypes: ['monthly', 'quarterly', 'annual', 'adhoc'],
    autoRefresh: false,
    refreshInterval: 300000, // 5 minutes
    maxRetentionDays: 90,
    enableExport: true,
    exportFormats: ['pdf', 'csv', 'excel']
  };

  // Default statements data
  private defaultStatements: Statement[] = [
    {
      id: 'stmt_001',
      title: 'October 2024 Statement',
      period: 'October 2024',
      startDate: '2024-10-01',
      endDate: '2024-10-31',
      type: 'monthly',
      status: 'ready',
      size: 245632,
      downloadUrl: '#',
      transactionCount: 127,
      balance: 15420.50,
      currency: 'USD',
      generatedAt: '2024-11-01T09:00:00Z',
      expiresAt: '2025-01-30T23:59:59Z'
    },
    {
      id: 'stmt_002',
      title: 'September 2024 Statement',
      period: 'September 2024',
      startDate: '2024-09-01',
      endDate: '2024-09-30',
      type: 'monthly',
      status: 'ready',
      size: 189456,
      downloadUrl: '#',
      transactionCount: 98,
      balance: 12850.75,
      currency: 'USD',
      generatedAt: '2024-10-01T09:00:00Z',
      expiresAt: '2024-12-30T23:59:59Z'
    },
    {
      id: 'stmt_003',
      title: 'Q3 2024 Statement',
      period: 'Q3 2024',
      startDate: '2024-07-01',
      endDate: '2024-09-30',
      type: 'quarterly',
      status: 'ready',
      size: 567890,
      downloadUrl: '#',
      transactionCount: 342,
      balance: 18925.30,
      currency: 'USD',
      generatedAt: '2024-10-01T09:00:00Z',
      expiresAt: '2025-01-01T23:59:59Z'
    },
    {
      id: 'stmt_004',
      title: 'Custom Range Statement',
      period: 'Aug 15 - Sep 15, 2024',
      startDate: '2024-08-15',
      endDate: '2024-09-15',
      type: 'adhoc',
      status: 'generating',
      transactionCount: 67,
      balance: 9320.45,
      currency: 'USD',
      generatedAt: '2024-09-16T10:30:00Z'
    }
  ];

  // Computed properties
  mergedConfig = computed(() => ({
    ...this.defaultConfig,
    ...this.config
  }));

  displayStatements = computed(() => 
    this.statements.length > 0 ? this.statements : this.defaultStatements
  );

  containerClasses = computed(() => {
    const base = 'statements-container';
    const sizeClass = `size-${this.size}`;
    const variantClass = `variant-${this.variant}`;
    const roundedClass = `rounded-${this.rounded}`;
    const stateClasses = [
      this.disabled ? 'disabled' : '',
      this.darkMode ? 'dark' : '',
      this.isLoading() ? 'loading' : ''
    ].filter(Boolean);

    return [base, sizeClass, variantClass, roundedClass, ...stateClasses, this.className].filter(Boolean).join(' ');
  });

  filteredStatements = computed(() => {
    let filtered = this.displayStatements();
    
    // Apply search filter
    if (this.searchTerm()) {
      const term = this.searchTerm().toLowerCase();
      filtered = filtered.filter(stmt => 
        stmt.title.toLowerCase().includes(term) ||
        stmt.period.toLowerCase().includes(term) ||
        stmt.type.toLowerCase().includes(term)
      );
    }

    // Apply type filter
    if (this.selectedType() !== 'all') {
      filtered = filtered.filter(stmt => stmt.type === this.selectedType());
    }

    return filtered;
  });

  availableTypes = computed(() => 
    this.mergedConfig().allowedTypes.map(type => ({
      type,
      ...STATEMENT_TYPES[type as keyof typeof STATEMENT_TYPES]
    }))
  );

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.initializeFilter();
    this.setupAutoRefresh();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeFilter(): void {
    this.filterForm = this.fb.group({
      search: [''],
      type: ['all'],
      dateFrom: [''],
      dateTo: ['']
    });

    this.filterForm.valueChanges
      .pipe(debounceTime(300), takeUntil(this.destroy$))
      .subscribe(filters => {
        this.searchTerm.set(filters.search || '');
        this.selectedType.set(filters.type || 'all');
        this.filterChanged.emit(filters);
      });
  }

  private setupAutoRefresh(): void {
    if (this.mergedConfig().autoRefresh) {
      setInterval(() => {
        this.refreshStatements();
      }, this.mergedConfig().refreshInterval);
    }
  }

  selectStatement(statement: Statement): void {
    if (this.disabled || statement.status === 'generating') return;
    this.statementSelected.emit(statement);
  }

  downloadStatement(statement: Statement, event?: Event): void {
    if (event) {
      event.stopPropagation();
    }
    
    if (this.disabled || !this.mergedConfig().enableDownload || statement.status !== 'ready') return;
    
    this.downloadRequested.emit(statement);
  }

  refreshStatements(): void {
    if (this.disabled) return;
    
    this.isLoading.set(true);
    this.refreshRequested.emit();
    
    // Simulate loading delay
    setTimeout(() => {
      this.isLoading.set(false);
    }, 1000);
  }

  getStatusIcon(status: string): string {
    const icons = {
      ready: '✅',
      generating: '⏳',
      failed: '❌',
      expired: '🕐'
    };
    return icons[status as keyof typeof icons] || '📄';
  }

  getStatusText(status: string): string {
    const texts = {
      ready: 'Ready',
      generating: 'Generating...',
      failed: 'Failed',
      expired: 'Expired'
    };
    return texts[status as keyof typeof texts] || 'Unknown';
  }

  formatFileSize(bytes?: number): string {
    if (!bytes) return 'N/A';
    
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  isExpired(statement: Statement): boolean {
    if (!statement.expiresAt) return false;
    return new Date(statement.expiresAt) < new Date();
  }

  trackByStatementId(index: number, statement: Statement): string {
    return statement.id;
  }
}
