import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import Socials components
// Standalone (1)
import { SocialLinksComponent } from './social-links/social-links.component';

// Non-Standalone (0)
// None

@NgModule({
  declarations: [
    // Non-Standalone Components (0)
    // None
  ],
  imports: [
    CommonModule,
    IonicModule,
    // Standalone Components (1)
    SocialLinksComponent,
  ],
  exports: [
    // Standalone Components (1)
    SocialLinksComponent,
  ]
})
export class SocialsModule { }
