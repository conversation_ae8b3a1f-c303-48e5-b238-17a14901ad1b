import { Component, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { LssConfig } from 'lp-client-api';

@Component({
  selector: 'socials-customizer',
  templateUrl: './socials-customizer.component.html',
  styleUrls: ['./socials-customizer.component.css'],
  standalone: true,
  imports: [CommonModule, IonicModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class SocialsCustomizerComponent {
  constructor(public lssConfig: LssConfig) {}
}
