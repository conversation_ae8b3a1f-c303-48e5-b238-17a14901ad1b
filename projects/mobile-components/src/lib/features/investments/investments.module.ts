import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import Investments components
// Standalone (1)
import { InvestComponent } from './invest/invest.component';

// Non-Standalone (0)
// None

@NgModule({
  declarations: [
    // Non-Standalone Components (0)
    // None
  ],
  imports: [
    CommonModule,
    IonicModule,
    // Standalone Components (1)
    InvestComponent,
  ],
  exports: [
    // Standalone Components (1)
    InvestComponent,
  ]
})
export class InvestmentsModule { }
