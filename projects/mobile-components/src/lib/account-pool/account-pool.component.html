<div [ngClass]="containerClass">
  <!-- Loading and error states -->
  <p *ngIf="isLoading" class="text-center text-gray-500 my-4">Loading...</p>
  <p *ngIf="errorMessage" [ngClass]="errorClass">{{ errorMessage }}</p>
  <p *ngIf="successMessage" class="text-sm text-green-600 mt-1">{{ successMessage }}</p>

  <!-- Find Pool View - Default state -->
  <ng-container *ngIf="view === 'find'">
    <h2 [ngClass]="titleClass">Account Pool</h2>
    
    <div class="flex justify-center mb-6" style="gap: 1rem;">
      <button [ngClass]="buttonClass" (click)="changeView('create')">Create New Pool</button>
      <button [ngClass]="buttonClass" (click)="changeView('join')">Join Existing Pool</button>
    </div>
  </ng-container>

  <!-- Create Pool View -->
  <ng-container *ngIf="view === 'create'">
    <h2 [ngClass]="titleClass">Create Account Pool</h2>
    
    <form [formGroup]="createPoolForm" (ngSubmit)="createPool()">
      <div [ngClass]="formGroupClass">
        <label [ngClass]="labelClass" for="poolName">Pool Name</label>
        <input 
          [ngClass]="inputClass" 
          id="poolName"
          type="text" 
          formControlName="name" 
          placeholder="Enter pool name"
        >
        <p *ngIf="hasError(createPoolForm, 'name', 'required')" [ngClass]="errorClass">
          Pool name is required
        </p>
      </div>

      <div [ngClass]="formGroupClass">
        <label [ngClass]="labelClass" for="email">Email</label>
        <input 
          [ngClass]="inputClass" 
          id="email"
          type="email" 
          formControlName="email" 
          placeholder="Enter email"
        >
        <p *ngIf="hasError(createPoolForm, 'email', 'required')" [ngClass]="errorClass">
          Email is required
        </p>
        <p *ngIf="hasError(createPoolForm, 'email', 'email')" [ngClass]="errorClass">
          Please enter a valid email
        </p>
      </div>

      <div [ngClass]="formGroupClass">
        <label [ngClass]="labelClass" for="countryCode">Country Code</label>
        <input 
          [ngClass]="inputClass" 
          id="countryCode"
          type="text" 
          formControlName="countryCode" 
          placeholder="e.g. +27"
        >
        <p *ngIf="hasError(createPoolForm, 'countryCode', 'required')" [ngClass]="errorClass">
          Country code is required
        </p>
      </div>

      <div [ngClass]="formGroupClass">
        <label [ngClass]="labelClass" for="telephone">Telephone</label>
        <input 
          [ngClass]="inputClass" 
          id="telephone"
          type="tel" 
          formControlName="telephone" 
          placeholder="Enter telephone number"
        >
        <p *ngIf="hasError(createPoolForm, 'telephone', 'required')" [ngClass]="errorClass">
          Telephone is required
        </p>
      </div>

      <div [ngClass]="formGroupClass">
        <label [ngClass]="labelClass" for="split">Pool Split Type</label>
        <select 
          [ngClass]="inputClass" 
          id="split"
          formControlName="split"
        >
          <option value="">Select split type</option>
          <option *ngFor="let splitType of splitTypes" [value]="splitType.code">
            {{ splitType.description }}
          </option>
        </select>
        <p *ngIf="hasError(createPoolForm, 'split', 'required')" [ngClass]="errorClass">
          Split type is required
        </p>
      </div>

      <div class="flex space-x-4">
        <button type="submit" [ngClass]="buttonClass" [disabled]="isLoading">
          {{ isLoading ? 'Creating...' : 'Create Pool' }}
        </button>
        <button type="button" [ngClass]="buttonClass" (click)="changeView('find')">
          Cancel
        </button>
      </div>
    </form>
  </ng-container>

  <!-- Join Pool View -->
  <ng-container *ngIf="view === 'join'">
    <h2 [ngClass]="titleClass">Join Account Pool</h2>
    
    <form [formGroup]="joinPoolForm" (ngSubmit)="requestToJoinPool()">
      <div [ngClass]="formGroupClass">
        <label [ngClass]="labelClass" for="poolId">Pool ID</label>
        <input 
          [ngClass]="inputClass" 
          id="poolId"
          type="text" 
          formControlName="poolId" 
          placeholder="Enter pool ID"
        >
        <p *ngIf="hasError(joinPoolForm, 'poolId', 'required')" [ngClass]="errorClass">
          Pool ID is required
        </p>
        <p *ngIf="hasError(joinPoolForm, 'poolId', 'pattern')" [ngClass]="errorClass">
          Please enter a valid pool ID (numbers only)
        </p>
      </div>

      <div class="flex space-x-4">
        <button type="submit" [ngClass]="buttonClass" [disabled]="isLoading">
          {{ isLoading ? 'Requesting...' : 'Request to Join' }}
        </button>
        <button type="button" [ngClass]="buttonClass" (click)="changeView('find')">
          Cancel
        </button>
      </div>
    </form>
  </ng-container>

  <!-- Pool Details View -->
  <ng-container *ngIf="view === 'details' && poolInfo">
    <h2 [ngClass]="titleClass">{{ poolInfo.POOLNAME }}</h2>
    
    <div class="mb-6">
      <p class="font-medium">Pool ID: <span class="font-normal">{{ poolInfo.ENTITYID }}</span></p>
      <p class="font-medium">Status: <span class="font-normal">{{ formatStatus(poolInfo.STATUS) }}</span></p>
      <p class="font-medium">Total Units: <span class="font-normal">{{ poolInfo.TOTALUNITS }}</span></p>
      <p class="font-medium">Created: <span class="font-normal">{{ parseDate(poolInfo.BEGINDATE) | date:'mediumDate' }}</span></p>
    </div>

    <h3 class="text-lg font-semibold mb-2">Members</h3>
    
    <table [ngClass]="tableClass">
      <thead>
        <tr>
          <th [ngClass]="tableHeaderClass">Member</th>
          <th [ngClass]="tableHeaderClass">Type</th>
          <th [ngClass]="tableHeaderClass">Balance</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let member of poolMembers">
          <td [ngClass]="tableCellClass">
            <div class="member-name-container">
              <div class="member-name">{{ member.NAME || 'Unknown' }}</div>
              <div class="member-id">{{ member.MPACC.trim() }}</div>
            </div>
          </td>
          <td [ngClass]="tableCellClass">
            <span class="type-badge" [class.admin]="member.TYPE === 'ADMN'" [class.member]="member.TYPE !== 'ADMN'">
              {{ member.TYPE === 'ADMN' ? 'Administrator' : 'Member' }}
            </span>
          </td>
          <td [ngClass]="tableCellClass">{{ member.BALANCE }}</td>
        </tr>
      </tbody>
    </table>

    <!-- Admin only - Invite Member form -->
    <div *ngIf="isAdmin" class="mt-6">
      <h3 class="text-lg font-semibold mb-2">Invite Member</h3>
      
      <form [formGroup]="inviteMemberForm" (ngSubmit)="inviteMember()">
        <div [ngClass]="formGroupClass">
          <label [ngClass]="labelClass" for="membershipNumber">Membership Number</label>
          <input 
            [ngClass]="inputClass" 
            id="membershipNumber"
            type="text" 
            formControlName="membershipNumber" 
            placeholder="Enter membership number"
          >
          <p *ngIf="hasError(inviteMemberForm, 'membershipNumber', 'required')" [ngClass]="errorClass">
            Membership number is required
          </p>
          <p *ngIf="hasError(inviteMemberForm, 'membershipNumber', 'minlength')" [ngClass]="errorClass">
            Membership number must be at least 5 characters
          </p>
        </div>

        <button type="submit" [ngClass]="buttonClass" [disabled]="isLoading">
          {{ isLoading ? 'Sending...' : 'Send Invite' }}
        </button>
      </form>
    </div>

    <!-- Exit Pool Option -->
    <div class="mt-6">
      <button 
        class="text-red-600 hover:text-red-800 font-medium"
        (click)="exitPool()"
      >
        Exit Pool
      </button>
    </div>
  </ng-container>
</div>
