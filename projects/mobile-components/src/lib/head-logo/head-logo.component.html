<ion-grid>
  <ion-row class="center">
    <div>
      <ion-button
        (click)="showText()"
        *ngIf="type == 'balance' || type == 'membership'"
        expand="block"
        class="points"
        >{{ useText }}</ion-button
      >
      <div *ngIf="type == 'welcome'" class="welcome text-primary">
        <h5 class="center">Welcome</h5>
        <ion-text class="front-text center">
          {{ names }}
        </ion-text>
      </div>
      <a
        *ngIf="type == 'phone'"
        style="text-decoration: none"
        href="{{ 'tel:' + phone }}"
      >
        <ion-button (click)="showText()" expand="block" class="points">
          <ion-icon size="25px" class="icon-call" name="call"></ion-icon>

          {{ phone }}</ion-button
        >
      </a>
    </div>

    <div class="logo-col">
      <img class="logo" [src]="src" alt="" />
      <!-- <img
        class="logo"
        src="assets/images/resources_ffz1/icon-only.jpeg"
        alt=""
      /> -->
    </div>
  </ion-row>
</ion-grid>
