import { Component, Injector, Input, OnInit } from '@angular/core';

@Component({
  selector: 'lib-head-logo',
  templateUrl: './head-logo.component.html',
  styleUrls: ['./head-logo.component.css']
})
export class HeadLogoComponent implements OnInit {
  @Input() 
  names?: string;
  @Input()
  balance?: number;
  @Input()
  membership?: string;
  @Input()
  src?: string;
  @Input()
  phone?: string
  @Input()
  type?: string = 'balance'

typeOf = 'balance';
text?: string = this.balance ? 'Balance' + ' ' + this.balance : '0';

get useText (){
  let text = ''


    if(this.type  === 'balance'){
      text = this.balance ? 'Balance' + ' ' + this.balance : '0';


    }

    if(this.type === 'membership'){
      text = this.balance ? '#' + ' ' + this.membership : '0';
    }

  return text
}
  showText (){
    let typf = 'balance'
    if(this.typeOf === 'balance'){
      this.text = this.balance ? 'Balance' + ' ' + this.balance : '0';
      this.typeOf = 'membership'
      typf = 'membership';

    }

    if(this.typeOf === 'membership'){
      this.text = this.balance ? '#' + ' ' + this.membership : '0';
      this.typeOf = 'balance'
    }

  }

  ngOnInit(): void {
    this.showText()
  }
}

