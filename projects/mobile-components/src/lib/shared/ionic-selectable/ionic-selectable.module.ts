import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';

// This is a wrapper module for IonicSelectableModule
// When the actual IonicSelectableModule is properly installed,
// uncomment the import and add it to the imports and exports arrays

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule
    // IonicSelectableModule
  ],
  exports: [
    // IonicSelectableModule
  ]
})
export class IonicSelectableWrapperModule { }
