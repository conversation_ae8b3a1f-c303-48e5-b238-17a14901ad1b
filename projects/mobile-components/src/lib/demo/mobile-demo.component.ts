import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { 
  ButtonComponent,
  InputComponent,
  CardComponent,
  HeadingComponent,
  MobileGridComponent,
  MobileNavigationComponent,
  MobileModalComponent,
  MobileSwipeCardComponent
} from '../public-api';

interface NavItem {
  label: string;
  icon?: string;
  route?: string;
  active?: boolean;
  badge?: string | number;
}

@Component({
  selector: 'mobile-demo',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    InputComponent,
    CardComponent,
    HeadingComponent,
    MobileGridComponent,
    MobileNavigationComponent,
    MobileModalComponent,
    MobileSwipeCardComponent
  ],
  template: `
    <!-- Mobile Container -->
    <div class="mobile-container">
      
      <!-- Header -->
      <base-heading size="lg" className="mb-6">
        Mobile Components Demo
      </base-heading>

      <!-- Button Examples -->
      <section class="mb-8">
        <base-heading size="md" className="mb-4">Buttons</base-heading>
        <mobile-grid [columns]="2" gap="md">
          <base-button 
            variant="primary" 
            size="md"
            text="Primary Button"
            fullWidth="true">
          </base-button>
          
          <base-button 
            variant="secondary" 
            size="md"
            text="Secondary"
            fullWidth="true">
          </base-button>
          
          <base-button 
            variant="success" 
            size="md"
            text="Success"
            [loading]="isLoading"
            fullWidth="true"
            (clicked)="toggleLoading()">
          </base-button>
          
          <base-button 
            variant="danger" 
            size="md"
            text="Delete"
            fullWidth="true"
            [disabled]="isDisabled">
          </base-button>
        </mobile-grid>
      </section>

      <!-- Input Examples -->
      <section class="mb-8">
        <base-heading size="md" className="mb-4">Form Inputs</base-heading>
        <div class="space-y-4">
          <base-input
            label="Email Address"
            type="email"
            placeholder="Enter your email"
            size="md"
            [required]="true">
          </base-input>
          
          <base-input
            label="Password"
            type="password"
            placeholder="Enter password"
            size="md"
            [required]="true">
          </base-input>
          
          <base-input
            label="Phone Number"
            type="tel"
            placeholder="+****************"
            size="md">
          </base-input>
        </div>
      </section>

      <!-- Card Examples -->
      <section class="mb-8">
        <base-heading size="md" className="mb-4">Cards</base-heading>
        <div class="space-y-4">
          
          <!-- Regular Card -->
          <base-card shadow="true" className="mobile-card">
            <h3 class="text-lg font-semibold mb-2">Product Card</h3>
            <p class="text-gray-600 mb-4">This is a sample product description that shows how cards work on mobile devices.</p>
            <base-button variant="primary" size="sm" text="Learn More"></base-button>
          </base-card>

          <!-- Swipe Card -->
          <mobile-swipe-card
            title="John Doe"
            subtitle="Software Engineer"
            [leftAction]="{icon: 'fas fa-trash', action: deleteAction}"
            [rightAction]="{icon: 'fas fa-check', action: approveAction}"
            (cardClick)="showCardDetails()">
            <p>Swipe left to delete, right to approve, or tap to view details.</p>
          </mobile-swipe-card>

        </div>
      </section>

      <!-- Grid Layout -->
      <section class="mb-8">
        <base-heading size="md" className="mb-4">Responsive Grid</base-heading>
        <mobile-grid gap="md">
          <base-card *ngFor="let item of gridItems" 
                     className="mobile-card text-center p-4">
            <div class="text-2xl mb-2">{{ item.icon }}</div>
            <h4 class="font-semibold">{{ item.title }}</h4>
            <p class="text-sm text-gray-600">{{ item.description }}</p>
          </base-card>
        </mobile-grid>
      </section>

      <!-- Demo Controls -->
      <section class="mb-20">
        <base-heading size="md" className="mb-4">Demo Controls</base-heading>
        <div class="space-y-4">
          <base-button 
            variant="primary" 
            text="Show Modal"
            fullWidth="true"
            (clicked)="showModal = true">
          </base-button>
          
          <base-button 
            variant="secondary" 
            text="Toggle Loading"
            fullWidth="true"
            (clicked)="toggleLoading()">
          </base-button>
          
          <base-button 
            variant="secondary" 
            text="Toggle Disabled State"
            fullWidth="true"
            (clicked)="toggleDisabled()">
          </base-button>
        </div>
      </section>

    </div>

    <!-- Mobile Navigation -->
    <mobile-navigation 
      [items]="navItems"
      position="bottom"
      variant="default"
      [shadow]="true"
      (itemClick)="onNavItemClick($event)">
    </mobile-navigation>

    <!-- Mobile Modal -->
    <mobile-modal 
      [isOpen]="showModal"
      title="Mobile Modal Demo"
      [closable]="true"
      [draggable]="true"
      (closed)="showModal = false">
      
      <div class="space-y-4">
        <p>This is a mobile-optimized modal that slides up from the bottom on mobile devices and appears as a centered dialog on larger screens.</p>
        
        <base-input
          label="Sample Input"
          placeholder="Type something..."
          size="md">
        </base-input>
        
        <div class="flex space-x-2">
          <base-button 
            variant="secondary" 
            text="Cancel"
            className="flex-1"
            (clicked)="showModal = false">
          </base-button>
          <base-button 
            variant="primary" 
            text="Save"
            className="flex-1"
            (clicked)="saveModalData()">
          </base-button>
        </div>
      </div>
      
    </mobile-modal>
  `,
  styles: [`
    .space-y-4 > * + * {
      margin-top: 1rem;
    }
    
    .space-x-2 > * + * {
      margin-left: 0.5rem;
    }
    
    .flex {
      display: flex;
    }
    
    .flex-1 {
      flex: 1;
    }
  `]
})
export class MobileDemoComponent {
  
  // Component state
  showModal = false;
  isLoading = false;
  isDisabled = false;
  
  // Navigation items
  navItems: NavItem[] = [
    { label: 'Home', icon: 'fas fa-home', route: '/home', active: true },
    { label: 'Search', icon: 'fas fa-search', route: '/search' },
    { label: 'Favorites', icon: 'fas fa-heart', route: '/favorites', badge: '3' },
    { label: 'Profile', icon: 'fas fa-user', route: '/profile' },
    { label: 'Settings', icon: 'fas fa-cog', route: '/settings' }
  ];

  // Grid items for demonstration
  gridItems = [
    { icon: '📱', title: 'Mobile First', description: 'Optimized for mobile devices' },
    { icon: '🎨', title: 'Beautiful UI', description: 'Modern and clean design' },
    { icon: '⚡', title: 'Fast', description: 'Optimized performance' },
    { icon: '♿', title: 'Accessible', description: 'WCAG 2.1 compliant' },
    { icon: '🔧', title: 'Customizable', description: 'Flexible and configurable' },
    { icon: '📦', title: 'Modular', description: 'Use only what you need' }
  ];

  // Event handlers
  onNavItemClick(item: NavItem): void {
    console.log('Navigation item clicked:', item);
    // Update active state
    this.navItems.forEach(navItem => navItem.active = false);
    item.active = true;
  }

  showCardDetails(): void {
    console.log('Card clicked - showing details');
  }

  deleteAction = (): void => {
    console.log('Delete action triggered');
  }

  approveAction = (): void => {
    console.log('Approve action triggered');
  }

  toggleLoading(): void {
    this.isLoading = !this.isLoading;
    // Auto-reset after 3 seconds
    if (this.isLoading) {
      setTimeout(() => this.isLoading = false, 3000);
    }
  }

  toggleDisabled(): void {
    this.isDisabled = !this.isDisabled;
  }

  saveModalData(): void {
    console.log('Saving modal data...');
    this.showModal = false;
  }
}
