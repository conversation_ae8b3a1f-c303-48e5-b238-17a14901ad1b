import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { PagesLoginTheme1Component } from './pages-login-theme1.component';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule,
    PagesLoginTheme1Component
  ],
  exports: [
    PagesLoginTheme1Component
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class PagesLoginTheme1Module { }
