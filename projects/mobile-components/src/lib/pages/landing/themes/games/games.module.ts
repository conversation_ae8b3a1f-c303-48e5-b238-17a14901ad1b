import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { PagesLandingThemesGamesComponent } from './games.component';
import { GamesCardModule } from '../../../../features/games/games/components/games-card/games-card.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule,
    GamesCardModule,
    PagesLandingThemesGamesComponent
  ],
  exports: [
    PagesLandingThemesGamesComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class PagesLandingThemesGamesModule { }
