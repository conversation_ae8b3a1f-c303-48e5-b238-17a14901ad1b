import {
  Component,
  OnInit,
  ViewChild,
  ElementRef,
  AfterViewInit,
  QueryList,
  ViewChildren,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA
} from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { GamesCardComponent } from '../../../../features/games/games/components/games-card/games-card.component';
import { GamesHeaderComponent } from '../../../../features/games/games/components/games-header/games-header.component';
import { GameService } from 'lp-client-api';

interface Game {
  name: string;
  description: string;
  image: string;
  id: string; // Added id property for routing
}

interface Category {
  name: string;
  icon: string;
}

@Component({
  selector: 'pages-landing-themes-games',
  templateUrl: './games.component.html',
  styleUrls: ['./games.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    GamesCard<PERSON>omponent,
    GamesHeaderComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class PagesLandingThemesGamesComponent implements OnInit, AfterViewInit {
  @ViewChild('gameCarousel') gameCarousel!: ElementRef;
  @ViewChildren('categoryElement') categoryElements!: QueryList<ElementRef>;

  featuredGames: Game[] = [];

  gameCategories: Category[] = [
    { name: 'Arcade', icon: 'assets/images/games/categories/arcade.jpeg' },
    { name: 'Puzzle', icon: 'assets/images/games/categories/puzzles.jpeg' },
    { name: 'Strategy', icon: 'assets/images/games/categories/strategy.jpeg' },
    { name: 'Sports', icon: 'assets/images/games/categories/sports.jpeg' },
    { name: 'Word', icon: 'assets/images/games/categories/word.jpeg' },
    { name: 'Card', icon: 'assets/images/games/categories/cards.jpeg' },
  ];

  private currentSlide = 0;

  constructor(private router: Router, private gameService: GameService) {}

  ngOnInit() {
    console.log('Games Component ngOnInit!!!!!:');
    this.loadGames();
  }

  ngAfterViewInit() {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-fade-in-right');
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.1 }
    );

    this.categoryElements.forEach((el) => observer.observe(el.nativeElement));
  }

  /**
   * Get the image path for a game based on its type code
   * @param gameTypeCode The game type code (e.g., "WORDLE", "SNAKE")
   * @returns The image path for the game
   */
  private getGameImagePath(gameTypeCode: string): string {
    if (!gameTypeCode) {
      return 'assets/images/games/default.jpg';
    }

    // Convert to lowercase for consistency
    const code = gameTypeCode.toLowerCase();

    // Define known image extensions for specific games
    const imageExtensions: { [key: string]: string } = {
      'wordle': 'png',
      'flappy-bird': 'jpg',
      'tetris': 'jpg',
      'snake': 'jpg',
      '2048': 'jpg',
      'memory': 'jpg',
      'minesweeper': 'jpg',
      'sudoku': 'jpg',
      'tower-defense': 'jpg',
      'simon-says': 'jpg',
      'candy-crush': 'jpg',
      'chess': 'jpg',
      'hangman': 'jpg',
      'crossword': 'jpg',
      'poker': 'jpg'
    };

    // Use the specific extension if available, otherwise default to jpg
    const extension = imageExtensions[code] || 'jpg';
    return `assets/images/games/${code}.${extension}`;
  }

  private loadGames() {
    console.log('Loading games in Games Component...!!!!!!!!!');
    this.gameService.getAllGameConfigs().subscribe({
      next: (games) => {
        console.log('Games loaded successfully in Games Component:', games);
        // Map the API games to the required format
        this.featuredGames = games.map(game => {
          // Get the game type code (e.g., "WORDLE", "SNAKE", etc.)
          const gameTypeCode = game.gameType?.typeDescription?.code?.toLowerCase() || '';

          // Get the image path for this game
          const imagePath = this.getGameImagePath(gameTypeCode);

          console.log(`Game ${game.name} has type code ${gameTypeCode}, using image: ${imagePath}`);

          return {
            name: game.name,
            description: game.gameType?.typeDescription?.description || '',
            image: imagePath,
            id: game.id.toString()
          };
        });
      },
      error: (error) => {
        console.error('Error loading games in Games Component:', error);
        // Fallback to dummy data if API fails
        this.initializeDummyData();
      },
    });
  }

  private initializeDummyData() {
    // Use the same helper method for consistency
    this.featuredGames = [
      {
        name: 'Snake',
        description: 'Classic snake game',
        image: this.getGameImagePath('snake'),
        id: 'snake',
      },
      {
        name: 'Flappy Bird',
        description: 'Addictive flying game',
        image: this.getGameImagePath('flappy-bird'),
        id: 'flappy-bird',
      },
      {
        name: 'Tetris',
        description: 'Iconic puzzle game',
        image: this.getGameImagePath('tetris'),
        id: 'tetris',
      },
      {
        name: 'Wordle',
        description: 'Popular word guessing game',
        image: this.getGameImagePath('wordle'),
        id: 'wordle',
      },
      {
        name: '2048',
        description: 'Addictive number puzzle game',
        image: this.getGameImagePath('2048'),
        id: '2048',
      },
    ];
  }

  nextSlide() {
    this.currentSlide = (this.currentSlide + 1) % this.featuredGames.length;
    this.updateCarouselPosition();
  }

  prevSlide() {
    this.currentSlide =
      (this.currentSlide - 1 + this.featuredGames.length) %
      this.featuredGames.length;
    this.updateCarouselPosition();
  }

  private updateCarouselPosition() {
    const carousel = this.gameCarousel.nativeElement;
    carousel.style.transform = `translateX(-${this.currentSlide * 100}%)`;
  }

  navigateToCategory(category: string) {
    this.router.navigate(['/public/games/categories'], {
      queryParams: { category: category },
    });
  }

  navigateToGame(gameId: string) {
    this.router.navigate(['/public/games/single'], {
      queryParams: { game: gameId },
    });
  }
}
