<div class="flex overflow-y-auto flex-col min-h-screen bg-orange-200 pb-100">
  <div class="absolute inset-0 z-0 h-screen bg-orange-400 animated-bg"></div>
  <div class="overflow-y-auto absolute inset-0 z-10 space-y-4 min-h-screen">
    <games-header bgColor="orange"></games-header>

    <section class="p-4 featured-games animate-fade-in-left">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-3xl font-bold text-orange-100">Featured Games</h2>
        <a routerLink="/public/games/all"
          class="text-yellow-400 transition-colors duration-300 hover:text-yellow-300">View All</a>
      </div>
      <div class="overflow-hidden relative game-carousel">
        <div class="flex transition-transform duration-300 ease-in-out" #gameCarousel>
          <div *ngFor="let game of featuredGames" class="flex-shrink-0 w-full shadow-lg">
            <app-games-card [game]="game" bgColor="orange" borderColor="yellow" textColor="yellow"
              buttonColor="yellow"></app-games-card>

          </div>
        </div>
      </div>
    </section>

    <section class="px-6 py-2 game-categories">
      <h2 class="mb-8 text-3xl font-bold text-center text-orange-100">
        Game Categories
      </h2>
      <div class="grid grid-cols-2 gap-6 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6">
        <div *ngFor="let category of gameCategories"
          class="p-4 text-center bg-orange-500 rounded-lg shadow-md transition transform cursor-pointer hover:shadow-lg hover:bg-orange-600 hover:scale-105"
          (click)="navigateToCategory(category.name)" #categoryElement>
          <img [src]="category.icon" [alt]="category.name" class="mx-auto mb-4 w-20 h-20" />
          <h3 class="text-xl font-bold text-yellow-100">{{ category.name }}</h3>
        </div>
      </div>
    </section>

    <section class="px-6 py-16 mb-6 text-center bg-gradient-to-r from-orange-400 to-orange-600 cta-section">
      <h2 class="mb-6 text-4xl font-bold text-yellow-100">Ready to Play?</h2>
      <p class="mb-8 text-2xl text-white">
        Join millions of gamers and start your adventure today!
      </p>
      <button
        class="px-8 py-4 text-xl font-bold text-white bg-red-500 rounded-full shadow-lg transition transform hover:bg-red-600 hover:scale-105">
        Sign Up Now
      </button>
    </section>
  </div>
</div>