import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { LssConfig, GameService, Game as ApiGame } from 'lp-client-api';

// UI-specific game display properties
interface GameDisplay {
  description: string;
  image: string;
  genre: string;
}

// Combine API game type with UI display properties
interface GameViewModel {
  game: ApiGame;
  display: GameDisplay;
}

interface Store {
  id: number;
  name: string;
  distance: number;
  hasActivePromotion: boolean;
  image: string;
  promoCount: number;
}

@Component({
  selector: 'lib-games-home',
  templateUrl: './games-home.component.html',
  styleUrls: ['./games-home.component.css'],
  standalone: true,
  imports: [CommonModule, RouterModule, IonicModule]
})
export class GamesHomeComponent implements OnInit {
  @Input() public profile: any;

  public totalPoints: number = 1250;
  public games: GameViewModel[] = [];
  public nearestStores: Store[] = [];
  public userLevel: number = 1;
  public nextRewardThreshold: number = 1000;

  private readonly gameTypeDefaults: { [key: string]: GameDisplay } = {
    'TETRIS': {
      description: 'Classic block-stacking puzzle game',
      image: 'assets/images/games/tetris.jpeg',
      genre: 'Arcade Game'
    },
    'SNAKE': {
      description: 'Classic snake game',
      image: 'assets/images/games/snake.jpeg',
      genre: 'Arcade Game'
    },
    'SUDOKU': {
      description: 'Number placement puzzle',
      image: 'assets/images/games/sudoku.jpeg',
      genre: 'Puzzle Game'
    },
    'BLACKJACK': {
      description: 'Classic card game',
      image: 'assets/images/games/blackjack.jpeg',
      genre: 'Card Game'
    },
    'POKER': {
      description: 'Classic poker game',
      image: 'assets/images/games/poker.jpeg',
      genre: 'Card Game'
    }
  };

  constructor(
    protected readonly router: Router,
    public lssConfig: LssConfig,
    private gameService: GameService
  ) {}

  ngOnInit() {
    this.loadGames();
  }

  private loadGames() {
    console.log('Loading games...');
    this.gameService.getAllGameConfigs().subscribe({
      next: (apiGames) => {
        console.log('Games loaded successfully:', apiGames);
        this.games = apiGames.map((apiGame) => ({
          game: apiGame,
          display: this.getGameDisplay(apiGame)
        }));
      },
      error: (error) => {
        console.error('Error loading games:', error);
        this.initializeDummyData();
      },
    });
  }

  private getGameDisplay(game: ApiGame): GameDisplay {
    const defaultDisplay = {
      description: game.name,
      image: 'assets/images/games/default.jpeg',
      genre: 'Game'
    };

    // Extract game type from typeDescription string (format: "TETRIS - Tetris")
    const typeCode = game.typeDescription?.split(' - ')?.[0];
    if (!typeCode) {
      console.warn('No game type code found for game:', game.name);
      return defaultDisplay;
    }

    return this.gameTypeDefaults[typeCode] || defaultDisplay;
  }

  private initializeDummyData() {
    this.games = [
      {
        game: {
          id: 1,
          name: 'Tetris',
          startDate: '2024-01-01',
          endDate: '2024-12-31',
          owner: 1,
          status: 1,
          gameType: {
            id: 1,
            category: 362,
            categoryDescription: 'Business Games',
            type: 375,
            typeDescription: {
              code: 'TETRIS',
              description: 'Tetris'
            },
            createdBy: 'System',
            createdOn: new Date().toISOString(),
            version: 1
          },
          gameConfig: [
            {
              id: 1,
              difficulty: 'MEDIUM',
              frequency: 'DAILY',
              frequencyAttempts: 3,
            }
          ]
        },
        display: {
          description: 'Classic block-stacking puzzle game',
          image: 'assets/images/games/tetris.jpeg',
          genre: 'Arcade Game'
        }
      },
      {
        game: {
          id: 2,
          name: 'Snake',
          startDate: '2024-01-01',
          endDate: '2024-12-31',
          owner: 1,
          status: 1,
          gameType: {
            id: 2,
            category: 362,
            categoryDescription: 'Business Games',
            type: 376,
            typeDescription: {
              code: 'SNAKE',
              description: 'Snake'
            },
            createdBy: 'System',
            createdOn: new Date().toISOString(),
            version: 1
          },
          gameConfig: [
            {
              id: 2,
              difficulty: 'MEDIUM',
              frequency: 'DAILY',
              frequencyAttempts: 3,
            }
          ]
        },
        display: {
          description: 'Classic snake game',
          image: 'assets/images/games/snake.jpeg',
          genre: 'Arcade Game'
        }
      }
    ];

    this.nearestStores = [
      {
        id: 1,
        image: 'assets/images/stores/mica.png',
        name: 'Mica Centurion',
        distance: 1.2,
        hasActivePromotion: true,
        promoCount: 2,
      },
      {
        id: 2,
        image: 'assets/images/stores/sanparks.jpg',
        name: 'Sanparks',
        distance: 2.5,
        hasActivePromotion: false,
        promoCount: 0,
      },
    ];
  }

  public playGame(gameViewModel: GameViewModel) {
    const game = gameViewModel.game;
    console.log(`Playing ${game.name}`, game);
    localStorage.setItem('activeGame', JSON.stringify(game));
    
    // Extract game type from typeDescription string (format: "TETRIS - Tetris")
    const gameType = game.typeDescription?.split(' - ')?.[0]?.toLowerCase() || 'unknown';
    this.router.navigate(['/public/games/single'], {
      queryParams: { game: gameType },
    });
  }

  public viewStorePromotions(store: Store) {
    console.log(`Viewing promotions for ${store.name}`);
    // Implement navigation to store promotions page
    // this.router.navigate(['/stores', store.id, 'promotions']);
  }
}
