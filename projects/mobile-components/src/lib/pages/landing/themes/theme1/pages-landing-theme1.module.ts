import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { PagesLandingTheme1Component } from './pages-landing-theme1.component';
import { HeadLogoComponent } from '../../../../layout/head-logo/head-logo.component';


@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule,
    PagesLandingTheme1Component,
    HeadLogoComponent
  ],
  exports: [
    PagesLandingTheme1Component,
    HeadLogoComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class PagesLandingTheme1Module { }
