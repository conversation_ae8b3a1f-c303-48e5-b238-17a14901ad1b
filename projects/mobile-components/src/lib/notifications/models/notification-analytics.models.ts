/**
 * Notification analytics event types
 */
export enum NotificationEventType {
  /** Notification was delivered to the device */
  DELIVERED = 'notification_delivered',
  
  /** Notification was opened by the user */
  OPENED = 'notification_opened',
  
  /** Notification was dismissed by the user */
  DISMISSED = 'notification_dismissed',
  
  /** User clicked on an action button in the notification */
  ACTION_CLICKED = 'notification_action_clicked',
  
  /** User granted permission for notifications */
  PERMISSION_GRANTED = 'notification_permission_granted',
  
  /** User denied permission for notifications */
  PERMISSION_DENIED = 'notification_permission_denied',
  
  /** User enabled a notification group */
  GROUP_ENABLED = 'notification_group_enabled',
  
  /** User disabled a notification group */
  GROUP_DISABLED = 'notification_group_disabled'
}

/**
 * Notification analytics event data
 */
export interface NotificationAnalyticsEvent {
  /** Type of event */
  eventType: NotificationEventType;
  
  /** Unique identifier for the notification */
  notificationId: string;
  
  /** Timestamp when the event occurred */
  timestamp: number;
  
  /** User identifier (if available) */
  userId?: string;
  
  /** Device identifier (if available) */
  deviceId?: string;
  
  /** Platform (ios, android, web) */
  platform?: string;
  
  /** Notification group identifier */
  notificationGroup?: string;
  
  /** Additional data specific to the event */
  additionalData?: any;
}

/**
 * Notification analytics summary data
 */
export interface NotificationAnalyticsSummary {
  /** Total number of notifications delivered */
  deliveryCount: number;
  
  /** Total number of notifications opened */
  openCount: number;
  
  /** Total number of notifications dismissed */
  dismissCount: number;
  
  /** Total number of notification actions clicked */
  actionCount: number;
  
  /** Open rate (openCount / deliveryCount) */
  openRate: number;
  
  /** Delivery data by date */
  deliveryByDate: {
    date: string;
    count: number;
  }[];
  
  /** Open rate data by date */
  openRateByDate: {
    date: string;
    rate: number;
  }[];
  
  /** Performance by notification group */
  groupPerformance: {
    groupId: string;
    groupName: string;
    deliveryCount: number;
    openCount: number;
    openRate: number;
  }[];
}
