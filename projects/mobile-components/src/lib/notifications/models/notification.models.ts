/**
 * Notification preferences model
 * Contains user preferences for push notifications
 */
export interface NotificationPreferences {
  /** Whether notifications are enabled */
  enabled: boolean;
  /** Array of notification group IDs the user is subscribed to */
  groups: string[];
}

/**
 * Notification group model
 * Represents a category of notifications that users can subscribe to
 */
export interface NotificationGroup {
  /** Unique identifier for the notification group */
  id: string;
  /** Display name for the notification group */
  name: string;
  /** Description of what kind of notifications are in this group */
  description: string;
  /** Whether this group is enabled by default */
  defaultEnabled?: boolean;
  /** Optional icon name (Ionic icon) */
  icon?: string;
}

/**
 * Notification data model
 * Represents a notification received from FCM
 */
export interface NotificationData {
  /** Notification title */
  title: string;
  /** Notification body text */
  body: string;
  /** Optional image URL */
  image?: string;
  /** Optional deep link or action */
  action?: string;
  /** Optional additional data */
  data?: any;
}
