/**
 * Notification template model
 * Represents a reusable notification template that can be used in campaigns
 */
export interface NotificationTemplate {
  /** Unique identifier */
  id?: string;
  
  /** Template name (for admin use) */
  name: string;
  
  /** Notification title */
  title: string;
  
  /** Notification body */
  body: string;
  
  /** Optional image URL */
  image?: string;
  
  /** Optional action (deep link) */
  action?: string;
  
  /** Optional notification group ID */
  groupId?: string;
  
  /** Creation timestamp */
  createdAt?: number;
  
  /** Last update timestamp */
  updatedAt?: number;
}

/**
 * Campaign status enum
 */
export enum CampaignStatus {
  /** Draft campaign (not yet scheduled or sent) */
  DRAFT = 'draft',
  
  /** Scheduled campaign (will be sent at a future date) */
  SCHEDULED = 'scheduled',
  
  /** Campaign currently being sent */
  SENDING = 'sending',
  
  /** Campaign has been sent */
  SENT = 'sent',
  
  /** Campaign has been cancelled */
  CANCELLED = 'cancelled',
  
  /** Campaign failed to send */
  FAILED = 'failed'
}

/**
 * Notification campaign model
 * Represents a notification campaign that can be scheduled and sent to users
 */
export interface NotificationCampaign {
  /** Unique identifier */
  id?: string;
  
  /** Campaign name (for admin use) */
  name: string;
  
  /** Template ID */
  templateId: string;
  
  /** Template object (populated when retrieving campaign) */
  template?: NotificationTemplate;
  
  /** Target notification groups */
  targetGroups: string[];
  
  /** Target user segments */
  targetSegments?: string[];
  
  /** Scheduled timestamp (if scheduled) */
  scheduledAt?: number;
  
  /** Sent timestamp (if sent) */
  sentAt?: number;
  
  /** Campaign status */
  status: CampaignStatus;
  
  /** Campaign statistics */
  stats?: {
    /** Total notifications sent */
    total: number;
    
    /** Notifications delivered */
    delivered: number;
    
    /** Notifications opened */
    opened: number;
    
    /** Notification actions clicked */
    clicked: number;
  };
  
  /** Creation timestamp */
  createdAt?: number;
  
  /** Last update timestamp */
  updatedAt?: number;
}

/**
 * Operator type for segment criteria
 */
export type SegmentOperator = 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than';

/**
 * Segment criterion model
 * Represents a single criterion for a user segment
 */
export interface SegmentCriterion {
  /** Field to compare (e.g., 'age', 'location', 'lastActive') */
  field: string;
  
  /** Comparison operator */
  operator: SegmentOperator;
  
  /** Value to compare against */
  value: any;
}

/**
 * User segment model
 * Represents a segment of users based on specific criteria
 */
export interface UserSegment {
  /** Unique identifier */
  id?: string;
  
  /** Segment name */
  name: string;
  
  /** Segment description */
  description: string;
  
  /** Segment criteria (all criteria must match for a user to be in the segment) */
  criteria: SegmentCriterion[];
  
  /** Creation timestamp */
  createdAt?: number;
  
  /** Last update timestamp */
  updatedAt?: number;
}

/**
 * Campaign creation request model
 */
export interface CreateCampaignRequest {
  /** Campaign name */
  name: string;
  
  /** Template ID or template object */
  template: string | NotificationTemplate;
  
  /** Target notification groups */
  targetGroups: string[];
  
  /** Target user segments */
  targetSegments?: string[];
  
  /** Schedule timestamp (optional) */
  scheduledAt?: number;
}

/**
 * Campaign update request model
 */
export interface UpdateCampaignRequest {
  /** Campaign ID */
  id: string;
  
  /** Campaign name */
  name?: string;
  
  /** Template ID or template object */
  template?: string | NotificationTemplate;
  
  /** Target notification groups */
  targetGroups?: string[];
  
  /** Target user segments */
  targetSegments?: string[];
  
  /** Schedule timestamp */
  scheduledAt?: number;
}

/**
 * Campaign statistics model
 */
export interface CampaignStatistics {
  /** Campaign ID */
  campaignId: string;
  
  /** Campaign name */
  campaignName: string;
  
  /** Total notifications sent */
  totalSent: number;
  
  /** Notifications delivered */
  delivered: number;
  
  /** Notifications opened */
  opened: number;
  
  /** Notification actions clicked */
  clicked: number;
  
  /** Open rate (opened / delivered) */
  openRate: number;
  
  /** Click rate (clicked / opened) */
  clickRate: number;
  
  /** Delivery rate (delivered / totalSent) */
  deliveryRate: number;
  
  /** Statistics by device type */
  byDevice?: {
    /** Device type (ios, android, web) */
    deviceType: string;
    
    /** Count for this device type */
    count: number;
    
    /** Percentage of total */
    percentage: number;
  }[];
  
  /** Statistics by notification group */
  byGroup?: {
    /** Group ID */
    groupId: string;
    
    /** Group name */
    groupName: string;
    
    /** Count for this group */
    count: number;
    
    /** Percentage of total */
    percentage: number;
  }[];
}

/**
 * Overall notification statistics model
 */
export interface OverallStatistics {
  /** Total campaigns */
  totalCampaigns: number;
  
  /** Total notifications sent */
  totalSent: number;
  
  /** Total notifications delivered */
  totalDelivered: number;
  
  /** Total notifications opened */
  totalOpened: number;
  
  /** Total notification actions clicked */
  totalClicked: number;
  
  /** Average open rate */
  averageOpenRate: number;
  
  /** Average click rate */
  averageClickRate: number;
  
  /** Average delivery rate */
  averageDeliveryRate: number;
  
  /** Top performing campaigns */
  topCampaigns: {
    /** Campaign ID */
    id: string;
    
    /** Campaign name */
    name: string;
    
    /** Open rate */
    openRate: number;
  }[];
  
  /** Statistics by device type */
  byDevice: {
    /** Device type (ios, android, web) */
    deviceType: string;
    
    /** Count for this device type */
    count: number;
    
    /** Percentage of total */
    percentage: number;
  }[];
  
  /** Statistics by notification group */
  byGroup: {
    /** Group ID */
    groupId: string;
    
    /** Group name */
    groupName: string;
    
    /** Count for this group */
    count: number;
    
    /** Percentage of total */
    percentage: number;
  }[];
}
