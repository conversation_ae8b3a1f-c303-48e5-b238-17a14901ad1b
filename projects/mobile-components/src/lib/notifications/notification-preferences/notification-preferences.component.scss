.notification-preferences {
  padding: 16px;
  background-color: var(--ion-background-color, #ffffff);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;
}

.notification-header {
  margin-bottom: 24px;
  
  h2 {
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: var(--ion-text-color, #333333);
  }
  
  .description {
    font-size: 14px;
    color: var(--ion-color-medium, #92949c);
    margin: 0;
  }
}

.main-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, var(--ion-color-primary-shade, #3171e0), var(--ion-color-primary, #3880ff));
  border-radius: 12px;
  margin-bottom: 20px;
  color: white;
  
  .toggle-label {
    display: flex;
    align-items: center;
    
    ion-icon {
      font-size: 24px;
      margin-right: 12px;
    }
    
    .toggle-text {
      h3 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
      }
      
      p {
        margin: 0;
        font-size: 12px;
        opacity: 0.8;
      }
    }
  }
}

.notification-groups {
  &.disabled {
    opacity: 0.6;
  }
  
  .group-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid var(--ion-color-light, #f4f5f8);
    
    &:last-child {
      border-bottom: none;
    }
    
    .group-label {
      display: flex;
      align-items: center;
      
      ion-icon {
        font-size: 20px;
        margin-right: 12px;
        color: var(--ion-color-primary, #3880ff);
      }
      
      .group-text {
        h4 {
          margin: 0 0 4px 0;
          font-size: 15px;
          font-weight: 500;
          color: var(--ion-text-color, #333333);
        }
        
        p {
          margin: 0;
          font-size: 12px;
          color: var(--ion-color-medium, #92949c);
        }
      }
    }
  }
}

.notification-info {
  margin-top: 20px;
  padding: 12px;
  background-color: var(--ion-color-light, #f4f5f8);
  border-radius: 8px;
  
  p {
    margin: 0;
    font-size: 12px;
    color: var(--ion-color-medium, #92949c);
    display: flex;
    align-items: center;
    
    ion-icon {
      margin-right: 8px;
      font-size: 16px;
    }
  }
}

// Dark mode adjustments
:host-context(.dark-theme) {
  .notification-preferences {
    background-color: var(--ion-background-color, #121212);
  }
  
  .notification-groups .group-item {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
  
  .notification-info {
    background-color: rgba(255, 255, 255, 0.05);
  }
}
