import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { NotificationGroup, NotificationPreferences } from '../models/notification.models';
import { NotificationAnalyticsService } from '../services/notification-analytics.service';

@Component({
  selector: 'lib-notification-preferences',
  standalone: true,
  imports: [CommonModule, FormsModule, IonicModule],
  templateUrl: './notification-preferences.component.html',
  styleUrls: ['./notification-preferences.component.scss']
})
export class NotificationPreferencesComponent implements OnInit {
  @Input() groups: NotificationGroup[] = [];
  @Output() preferencesChanged = new EventEmitter<NotificationPreferences>();

  notificationsEnabled = false;
  selectedGroups: string[] = [];

  constructor(private analyticsService: NotificationAnalyticsService) {}

  ngOnInit() {
    // If no groups are provided, use default groups
    if (!this.groups || this.groups.length === 0) {
      this.groups = [
        {
          id: 'general',
          name: 'General Notifications',
          description: 'Important updates and announcements',
          defaultEnabled: true,
          icon: 'notifications-outline'
        },
        {
          id: 'promotions',
          name: 'Promotions & Offers',
          description: 'Special offers, discounts and promotions',
          defaultEnabled: true,
          icon: 'gift-outline'
        },
        {
          id: 'transactions',
          name: 'Transactions',
          description: 'Updates about your points and transactions',
          defaultEnabled: true,
          icon: 'card-outline'
        },
        {
          id: 'games',
          name: 'Games',
          description: 'Game-related notifications and rewards',
          defaultEnabled: true,
          icon: 'game-controller-outline'
        }
      ];
    }

    // Initialize with default values
    this.selectedGroups = this.groups
      .filter(group => group.defaultEnabled)
      .map(group => group.id);
  }

  /**
   * Set notification preferences from external source
   * @param preferences Notification preferences
   */
  setPreferences(preferences: NotificationPreferences) {
    this.notificationsEnabled = preferences.enabled;
    this.selectedGroups = [...preferences.groups];
  }

  /**
   * Toggle notifications on/off
   * @param event Toggle event
   */
  toggleNotifications(event: any) {
    const previousState = this.notificationsEnabled;
    this.notificationsEnabled = event.detail.checked;

    // Track permission change if it's a toggle from disabled to enabled
    if (!previousState && this.notificationsEnabled) {
      this.analyticsService.trackPermissionChange(true);
    }

    this.emitPreferences();
  }

  /**
   * Toggle a specific notification group
   * @param groupId Group ID
   * @param event Toggle event
   */
  toggleGroup(groupId: string, event: any) {
    const checked = event.detail.checked;

    if (checked && !this.selectedGroups.includes(groupId)) {
      this.selectedGroups.push(groupId);
      // Track group enabled
      this.analyticsService.trackGroupToggle(groupId, true);
    } else if (!checked && this.selectedGroups.includes(groupId)) {
      this.selectedGroups = this.selectedGroups.filter(id => id !== groupId);
      // Track group disabled
      this.analyticsService.trackGroupToggle(groupId, false);
    }

    this.emitPreferences();
  }

  /**
   * Check if a group is selected
   * @param groupId Group ID
   * @returns True if the group is selected
   */
  isGroupSelected(groupId: string): boolean {
    return this.selectedGroups.includes(groupId);
  }

  /**
   * Emit preferences changed event
   */
  private emitPreferences() {
    const preferences: NotificationPreferences = {
      enabled: this.notificationsEnabled,
      groups: [...this.selectedGroups]
    };

    this.preferencesChanged.emit(preferences);
  }
}
