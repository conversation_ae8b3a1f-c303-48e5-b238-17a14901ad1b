<div class="notification-preferences">
  <div class="notification-header">
    <h2>Notification Preferences</h2>
    <p class="description">Choose which notifications you want to receive</p>
  </div>
  
  <!-- Main toggle for all notifications -->
  <div class="main-toggle">
    <div class="toggle-label">
      <ion-icon name="notifications"></ion-icon>
      <div class="toggle-text">
        <h3>Push Notifications</h3>
        <p>Enable or disable all notifications</p>
      </div>
    </div>
    <ion-toggle 
      [checked]="notificationsEnabled" 
      (ionChange)="toggleNotifications($event)"
      color="primary">
    </ion-toggle>
  </div>
  
  <!-- Notification groups -->
  <div class="notification-groups" [class.disabled]="!notificationsEnabled">
    <div class="group-item" *ngFor="let group of groups">
      <div class="group-label">
        <ion-icon [name]="group.icon || 'notifications-outline'"></ion-icon>
        <div class="group-text">
          <h4>{{ group.name }}</h4>
          <p>{{ group.description }}</p>
        </div>
      </div>
      <ion-toggle 
        [checked]="isGroupSelected(group.id)" 
        (ionChange)="toggleGroup(group.id, $event)"
        [disabled]="!notificationsEnabled"
        color="primary">
      </ion-toggle>
    </div>
  </div>
  
  <!-- Information about notifications -->
  <div class="notification-info">
    <p>
      <ion-icon name="information-circle-outline"></ion-icon>
      You can change your notification preferences at any time
    </p>
  </div>
</div>
