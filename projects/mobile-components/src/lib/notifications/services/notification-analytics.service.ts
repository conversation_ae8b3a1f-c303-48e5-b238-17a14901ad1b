import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';
import { Preferences } from '@capacitor/preferences';
import { NotificationAnalyticsEvent, NotificationEventType, NotificationAnalyticsSummary } from '../models/notification-analytics.models';

/**
 * Service for tracking notification analytics
 */
@Injectable({
  providedIn: 'root'
})
export class NotificationAnalyticsService {
  private readonly STORAGE_KEY = 'notification_analytics_events';
  private readonly MAX_STORED_EVENTS = 1000;
  
  constructor(private platform: Platform) {}
  
  /**
   * Track a notification event
   * @param event Notification analytics event
   */
  async trackEvent(event: NotificationAnalyticsEvent): Promise<void> {
    try {
      // Add platform if not provided
      if (!event.platform) {
        event.platform = this.getPlatform();
      }
      
      // Store event locally
      await this.storeEvent(event);
      
      // Send event to backend (if available)
      this.sendEventToBackend(event);
      
      console.log('Notification analytics event tracked:', event);
    } catch (error) {
      console.error('Error tracking notification event:', error);
    }
  }
  
  /**
   * Track notification delivery
   * @param notificationId Notification ID
   * @param notificationGroup Notification group
   */
  async trackDelivery(notificationId: string, notificationGroup?: string): Promise<void> {
    await this.trackEvent({
      eventType: NotificationEventType.DELIVERED,
      notificationId,
      timestamp: Date.now(),
      notificationGroup
    });
  }
  
  /**
   * Track notification open
   * @param notificationId Notification ID
   * @param notificationGroup Notification group
   */
  async trackOpen(notificationId: string, notificationGroup?: string): Promise<void> {
    await this.trackEvent({
      eventType: NotificationEventType.OPENED,
      notificationId,
      timestamp: Date.now(),
      notificationGroup
    });
  }
  
  /**
   * Track notification dismissal
   * @param notificationId Notification ID
   * @param notificationGroup Notification group
   */
  async trackDismiss(notificationId: string, notificationGroup?: string): Promise<void> {
    await this.trackEvent({
      eventType: NotificationEventType.DISMISSED,
      notificationId,
      timestamp: Date.now(),
      notificationGroup
    });
  }
  
  /**
   * Track notification action click
   * @param notificationId Notification ID
   * @param action Action identifier
   * @param notificationGroup Notification group
   */
  async trackActionClick(notificationId: string, action: string, notificationGroup?: string): Promise<void> {
    await this.trackEvent({
      eventType: NotificationEventType.ACTION_CLICKED,
      notificationId,
      timestamp: Date.now(),
      notificationGroup,
      additionalData: { action }
    });
  }
  
  /**
   * Track permission change
   * @param granted Whether permission was granted
   */
  async trackPermissionChange(granted: boolean): Promise<void> {
    await this.trackEvent({
      eventType: granted ? NotificationEventType.PERMISSION_GRANTED : NotificationEventType.PERMISSION_DENIED,
      notificationId: `permission_${Date.now()}`,
      timestamp: Date.now()
    });
  }
  
  /**
   * Track notification group toggle
   * @param groupId Group ID
   * @param enabled Whether the group was enabled
   */
  async trackGroupToggle(groupId: string, enabled: boolean): Promise<void> {
    await this.trackEvent({
      eventType: enabled ? NotificationEventType.GROUP_ENABLED : NotificationEventType.GROUP_DISABLED,
      notificationId: `group_${groupId}_${Date.now()}`,
      timestamp: Date.now(),
      notificationGroup: groupId
    });
  }
  
  /**
   * Get analytics summary
   * @returns Analytics summary data
   */
  async getAnalyticsSummary(): Promise<NotificationAnalyticsSummary> {
    const events = await this.getStoredEvents();
    
    // Calculate summary data
    const deliveryCount = events.filter(e => e.eventType === NotificationEventType.DELIVERED).length;
    const openCount = events.filter(e => e.eventType === NotificationEventType.OPENED).length;
    const dismissCount = events.filter(e => e.eventType === NotificationEventType.DISMISSED).length;
    const actionCount = events.filter(e => e.eventType === NotificationEventType.ACTION_CLICKED).length;
    const openRate = deliveryCount > 0 ? openCount / deliveryCount : 0;
    
    // Group events by date
    const deliveryByDate = this.groupEventsByDate(events.filter(e => e.eventType === NotificationEventType.DELIVERED));
    
    // Calculate open rate by date
    const openRateByDate = this.calculateOpenRateByDate(events);
    
    // Calculate group performance
    const groupPerformance = this.calculateGroupPerformance(events);
    
    return {
      deliveryCount,
      openCount,
      dismissCount,
      actionCount,
      openRate,
      deliveryByDate,
      openRateByDate,
      groupPerformance
    };
  }
  
  /**
   * Clear all stored analytics data
   */
  async clearAnalyticsData(): Promise<void> {
    await Preferences.remove({ key: this.STORAGE_KEY });
  }
  
  /**
   * Get the current platform
   * @returns Platform string (ios, android, web)
   */
  private getPlatform(): string {
    if (this.platform.is('ios')) {
      return 'ios';
    } else if (this.platform.is('android')) {
      return 'android';
    } else {
      return 'web';
    }
  }
  
  /**
   * Store event in local storage
   * @param event Event to store
   */
  private async storeEvent(event: NotificationAnalyticsEvent): Promise<void> {
    try {
      // Get existing events
      const events = await this.getStoredEvents();
      
      // Add new event
      events.push(event);
      
      // Limit the number of stored events
      if (events.length > this.MAX_STORED_EVENTS) {
        events.splice(0, events.length - this.MAX_STORED_EVENTS);
      }
      
      // Save events
      await Preferences.set({
        key: this.STORAGE_KEY,
        value: JSON.stringify(events)
      });
    } catch (error) {
      console.error('Error storing notification event:', error);
    }
  }
  
  /**
   * Get stored events from local storage
   * @returns Array of stored events
   */
  private async getStoredEvents(): Promise<NotificationAnalyticsEvent[]> {
    try {
      const result = await Preferences.get({ key: this.STORAGE_KEY });
      if (result.value) {
        return JSON.parse(result.value);
      }
    } catch (error) {
      console.error('Error getting stored notification events:', error);
    }
    
    return [];
  }
  
  /**
   * Send event to backend
   * @param event Event to send
   */
  private sendEventToBackend(event: NotificationAnalyticsEvent): void {
    // This would be implemented to send events to a backend service
    // For now, we'll just log to console
    console.log('Would send event to backend:', event);
  }
  
  /**
   * Group events by date
   * @param events Events to group
   * @returns Events grouped by date
   */
  private groupEventsByDate(events: NotificationAnalyticsEvent[]): { date: string, count: number }[] {
    const groupedEvents: { [date: string]: number } = {};
    
    events.forEach(event => {
      const date = new Date(event.timestamp).toISOString().split('T')[0];
      groupedEvents[date] = (groupedEvents[date] || 0) + 1;
    });
    
    return Object.entries(groupedEvents).map(([date, count]) => ({ date, count }));
  }
  
  /**
   * Calculate open rate by date
   * @param events All events
   * @returns Open rate by date
   */
  private calculateOpenRateByDate(events: NotificationAnalyticsEvent[]): { date: string, rate: number }[] {
    const deliveriesByDate: { [date: string]: number } = {};
    const opensByDate: { [date: string]: number } = {};
    
    events.forEach(event => {
      const date = new Date(event.timestamp).toISOString().split('T')[0];
      
      if (event.eventType === NotificationEventType.DELIVERED) {
        deliveriesByDate[date] = (deliveriesByDate[date] || 0) + 1;
      } else if (event.eventType === NotificationEventType.OPENED) {
        opensByDate[date] = (opensByDate[date] || 0) + 1;
      }
    });
    
    return Object.keys(deliveriesByDate).map(date => ({
      date,
      rate: deliveriesByDate[date] > 0 ? (opensByDate[date] || 0) / deliveriesByDate[date] : 0
    }));
  }
  
  /**
   * Calculate performance by notification group
   * @param events All events
   * @returns Performance by group
   */
  private calculateGroupPerformance(events: NotificationAnalyticsEvent[]): { groupId: string, groupName: string, deliveryCount: number, openCount: number, openRate: number }[] {
    const groups: { [groupId: string]: { deliveryCount: number, openCount: number } } = {};
    
    events.forEach(event => {
      if (!event.notificationGroup) return;
      
      if (!groups[event.notificationGroup]) {
        groups[event.notificationGroup] = { deliveryCount: 0, openCount: 0 };
      }
      
      if (event.eventType === NotificationEventType.DELIVERED) {
        groups[event.notificationGroup].deliveryCount++;
      } else if (event.eventType === NotificationEventType.OPENED) {
        groups[event.notificationGroup].openCount++;
      }
    });
    
    return Object.entries(groups).map(([groupId, data]) => ({
      groupId,
      groupName: groupId, // Would be replaced with actual group name from a service
      deliveryCount: data.deliveryCount,
      openCount: data.openCount,
      openRate: data.deliveryCount > 0 ? data.openCount / data.deliveryCount : 0
    }));
  }
}
