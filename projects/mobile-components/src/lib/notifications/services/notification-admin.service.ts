import { Injectable } from '@angular/core';
import { Preferences } from '@capacitor/preferences';
import {
  NotificationTemplate,
  NotificationCampaign,
  UserSegment,
  CampaignStatus,
  CampaignStatistics,
  OverallStatistics
} from '../models/notification-admin.models';
import { NotificationAnalyticsService } from './notification-analytics.service';
import { MemberService } from 'lp-client-api';
import { Observable, from, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';

/**
 * Service for managing notification administration
 */
@Injectable({
  providedIn: 'root'
})
export class NotificationAdminService {
  private readonly TEMPLATES_STORAGE_KEY = 'notification_templates';
  private readonly CAMPAIGNS_STORAGE_KEY = 'notification_campaigns';
  private readonly SEGMENTS_STORAGE_KEY = 'notification_segments';

  constructor(
    private memberService: MemberService,
    private analyticsService: NotificationAnalyticsService
  ) {}

  /**
   * Get all notification templates
   * @returns Promise with array of notification templates
   */
  async getTemplates(): Promise<NotificationTemplate[]> {
    try {
      const result = await Preferences.get({ key: this.TEMPLATES_STORAGE_KEY });
      if (result.value) {
        return JSON.parse(result.value);
      }
    } catch (error) {
      console.error('Error getting notification templates', error);
    }

    return [];
  }

  /**
   * Get a notification template by ID
   * @param id Template ID
   * @returns Promise with notification template or null if not found
   */
  async getTemplate(id: string): Promise<NotificationTemplate | null> {
    try {
      const templates = await this.getTemplates();
      return templates.find(template => template.id === id) || null;
    } catch (error) {
      console.error('Error getting notification template', error);
      return null;
    }
  }

  /**
   * Create a new notification template
   * @param template Template to create
   * @returns Promise with created template
   */
  async createTemplate(template: NotificationTemplate): Promise<NotificationTemplate> {
    try {
      const templates = await this.getTemplates();

      // Generate ID if not provided
      if (!template.id) {
        template.id = this.generateId();
      }

      // Set timestamps
      template.createdAt = Date.now();
      template.updatedAt = Date.now();

      // Add to templates
      templates.push(template);

      // Save templates
      await Preferences.set({
        key: this.TEMPLATES_STORAGE_KEY,
        value: JSON.stringify(templates)
      });

      return template;
    } catch (error) {
      console.error('Error creating notification template', error);
      throw error;
    }
  }

  /**
   * Update an existing notification template
   * @param template Template to update
   * @returns Promise with updated template
   */
  async updateTemplate(template: NotificationTemplate): Promise<NotificationTemplate> {
    try {
      const templates = await this.getTemplates();
      const index = templates.findIndex(t => t.id === template.id);

      if (index === -1) {
        throw new Error(`Template with ID ${template.id} not found`);
      }

      // Update timestamp
      template.updatedAt = Date.now();

      // Update template
      templates[index] = template;

      // Save templates
      await Preferences.set({
        key: this.TEMPLATES_STORAGE_KEY,
        value: JSON.stringify(templates)
      });

      return template;
    } catch (error) {
      console.error('Error updating notification template', error);
      throw error;
    }
  }

  /**
   * Delete a notification template
   * @param id Template ID
   * @returns Promise with boolean indicating success
   */
  async deleteTemplate(id: string): Promise<boolean> {
    try {
      const templates = await this.getTemplates();
      const filteredTemplates = templates.filter(template => template.id !== id);

      if (filteredTemplates.length === templates.length) {
        return false; // No template was deleted
      }

      // Save templates
      await Preferences.set({
        key: this.TEMPLATES_STORAGE_KEY,
        value: JSON.stringify(filteredTemplates)
      });

      return true;
    } catch (error) {
      console.error('Error deleting notification template', error);
      return false;
    }
  }

  /**
   * Get all notification campaigns
   * @returns Promise with array of notification campaigns
   */
  async getCampaigns(): Promise<NotificationCampaign[]> {
    try {
      const result = await Preferences.get({ key: this.CAMPAIGNS_STORAGE_KEY });
      if (result.value) {
        const campaigns = JSON.parse(result.value);

        // Populate template objects
        const templates = await this.getTemplates();
        return campaigns.map((campaign: NotificationCampaign) => ({
          ...campaign,
          template: templates.find(template => template.id === campaign.templateId)
        }));
      }
    } catch (error) {
      console.error('Error getting notification campaigns', error);
    }

    return [];
  }

  /**
   * Get a notification campaign by ID
   * @param id Campaign ID
   * @returns Promise with notification campaign or null if not found
   */
  async getCampaign(id: string): Promise<NotificationCampaign | null> {
    try {
      const campaigns = await this.getCampaigns();
      return campaigns.find(campaign => campaign.id === id) || null;
    } catch (error) {
      console.error('Error getting notification campaign', error);
      return null;
    }
  }

  /**
   * Create a new notification campaign
   * @param campaign Campaign to create
   * @returns Promise with created campaign
   */
  async createCampaign(campaign: NotificationCampaign): Promise<NotificationCampaign> {
    try {
      const campaigns = await this.getCampaigns();

      // Generate ID if not provided
      if (!campaign.id) {
        campaign.id = this.generateId();
      }

      // Set timestamps and status
      campaign.createdAt = Date.now();
      campaign.updatedAt = Date.now();
      campaign.status = campaign.status || CampaignStatus.DRAFT;

      // Initialize stats
      campaign.stats = {
        total: 0,
        delivered: 0,
        opened: 0,
        clicked: 0
      };

      // Add to campaigns
      campaigns.push(campaign);

      // Save campaigns (without template objects)
      await Preferences.set({
        key: this.CAMPAIGNS_STORAGE_KEY,
        value: JSON.stringify(campaigns.map(c => ({
          ...c,
          template: undefined
        })))
      });

      return campaign;
    } catch (error) {
      console.error('Error creating notification campaign', error);
      throw error;
    }
  }

  /**
   * Update an existing notification campaign
   * @param campaign Campaign to update
   * @returns Promise with updated campaign
   */
  async updateCampaign(campaign: NotificationCampaign): Promise<NotificationCampaign> {
    try {
      const campaigns = await this.getCampaigns();
      const index = campaigns.findIndex(c => c.id === campaign.id);

      if (index === -1) {
        throw new Error(`Campaign with ID ${campaign.id} not found`);
      }

      // Update timestamp
      campaign.updatedAt = Date.now();

      // Update campaign
      campaigns[index] = {
        ...campaigns[index],
        ...campaign,
        template: campaign.template || campaigns[index].template
      };

      // Save campaigns (without template objects)
      await Preferences.set({
        key: this.CAMPAIGNS_STORAGE_KEY,
        value: JSON.stringify(campaigns.map(c => ({
          ...c,
          template: undefined
        })))
      });

      return campaigns[index];
    } catch (error) {
      console.error('Error updating notification campaign', error);
      throw error;
    }
  }

  /**
   * Delete a notification campaign
   * @param id Campaign ID
   * @returns Promise with boolean indicating success
   */
  async deleteCampaign(id: string): Promise<boolean> {
    try {
      const campaigns = await this.getCampaigns();
      const filteredCampaigns = campaigns.filter(campaign => campaign.id !== id);

      if (filteredCampaigns.length === campaigns.length) {
        return false; // No campaign was deleted
      }

      // Save campaigns
      await Preferences.set({
        key: this.CAMPAIGNS_STORAGE_KEY,
        value: JSON.stringify(filteredCampaigns.map(c => ({
          ...c,
          template: undefined
        })))
      });

      return true;
    } catch (error) {
      console.error('Error deleting notification campaign', error);
      return false;
    }
  }

  /**
   * Schedule a notification campaign
   * @param id Campaign ID
   * @param scheduledAt Timestamp to schedule the campaign
   * @returns Promise with updated campaign
   */
  async scheduleCampaign(id: string, scheduledAt: number): Promise<NotificationCampaign> {
    try {
      const campaign = await this.getCampaign(id);

      if (!campaign) {
        throw new Error(`Campaign with ID ${id} not found`);
      }

      // Update campaign
      campaign.scheduledAt = scheduledAt;
      campaign.status = CampaignStatus.SCHEDULED;

      // Save campaign
      return this.updateCampaign(campaign);
    } catch (error) {
      console.error('Error scheduling notification campaign', error);
      throw error;
    }
  }

  /**
   * Send a notification campaign immediately
   * @param id Campaign ID
   * @returns Promise with updated campaign
   */
  async sendCampaign(id: string): Promise<NotificationCampaign> {
    try {
      const campaign = await this.getCampaign(id);

      if (!campaign) {
        throw new Error(`Campaign with ID ${id} not found`);
      }

      // Update campaign status
      campaign.status = CampaignStatus.SENDING;
      await this.updateCampaign(campaign);

      // Send notification via MemberService
      const template = campaign.template || await this.getTemplate(campaign.templateId);

      if (!template) {
        throw new Error(`Template with ID ${campaign.templateId} not found`);
      }

      // Call backend API to send notification
      // TODO: Replace with actual API call when backend is ready
      // For now, simulate a successful response
      const mockResponse = {
        total: (campaign.targetGroups?.length || 0) + (campaign.targetSegments?.length || 0),
        success: true
      };

      // Update campaign with sent status
      this.updateCampaign({
        ...campaign,
        status: CampaignStatus.SENT,
        sentAt: Date.now(),
        stats: {
          total: mockResponse.total,
          delivered: 0, // Will be updated as notifications are delivered
          opened: 0,    // Will be updated as notifications are opened
          clicked: 0    // Will be updated as notification actions are clicked
        }
      });

      return campaign;
    } catch (error) {
      console.error('Error sending notification campaign', error);
      throw error;
    }
  }

  /**
   * Cancel a scheduled notification campaign
   * @param id Campaign ID
   * @returns Promise with updated campaign
   */
  async cancelCampaign(id: string): Promise<NotificationCampaign> {
    try {
      const campaign = await this.getCampaign(id);

      if (!campaign) {
        throw new Error(`Campaign with ID ${id} not found`);
      }

      if (campaign.status !== CampaignStatus.SCHEDULED) {
        throw new Error(`Campaign with ID ${id} is not scheduled`);
      }

      // Update campaign
      campaign.status = CampaignStatus.CANCELLED;

      // Save campaign
      return this.updateCampaign(campaign);
    } catch (error) {
      console.error('Error cancelling notification campaign', error);
      throw error;
    }
  }

  /**
   * Get all user segments
   * @returns Promise with array of user segments
   */
  async getSegments(): Promise<UserSegment[]> {
    try {
      const result = await Preferences.get({ key: this.SEGMENTS_STORAGE_KEY });
      if (result.value) {
        return JSON.parse(result.value);
      }
    } catch (error) {
      console.error('Error getting user segments', error);
    }

    return [];
  }

  /**
   * Get a user segment by ID
   * @param id Segment ID
   * @returns Promise with user segment or null if not found
   */
  async getSegment(id: string): Promise<UserSegment | null> {
    try {
      const segments = await this.getSegments();
      return segments.find(segment => segment.id === id) || null;
    } catch (error) {
      console.error('Error getting user segment', error);
      return null;
    }
  }

  /**
   * Create a new user segment
   * @param segment Segment to create
   * @returns Promise with created segment
   */
  async createSegment(segment: UserSegment): Promise<UserSegment> {
    try {
      const segments = await this.getSegments();

      // Generate ID if not provided
      if (!segment.id) {
        segment.id = this.generateId();
      }

      // Set timestamps
      segment.createdAt = Date.now();
      segment.updatedAt = Date.now();

      // Add to segments
      segments.push(segment);

      // Save segments
      await Preferences.set({
        key: this.SEGMENTS_STORAGE_KEY,
        value: JSON.stringify(segments)
      });

      return segment;
    } catch (error) {
      console.error('Error creating user segment', error);
      throw error;
    }
  }

  /**
   * Update an existing user segment
   * @param segment Segment to update
   * @returns Promise with updated segment
   */
  async updateSegment(segment: UserSegment): Promise<UserSegment> {
    try {
      const segments = await this.getSegments();
      const index = segments.findIndex(s => s.id === segment.id);

      if (index === -1) {
        throw new Error(`Segment with ID ${segment.id} not found`);
      }

      // Update timestamp
      segment.updatedAt = Date.now();

      // Update segment
      segments[index] = segment;

      // Save segments
      await Preferences.set({
        key: this.SEGMENTS_STORAGE_KEY,
        value: JSON.stringify(segments)
      });

      return segment;
    } catch (error) {
      console.error('Error updating user segment', error);
      throw error;
    }
  }

  /**
   * Delete a user segment
   * @param id Segment ID
   * @returns Promise with boolean indicating success
   */
  async deleteSegment(id: string): Promise<boolean> {
    try {
      const segments = await this.getSegments();
      const filteredSegments = segments.filter(segment => segment.id !== id);

      if (filteredSegments.length === segments.length) {
        return false; // No segment was deleted
      }

      // Save segments
      await Preferences.set({
        key: this.SEGMENTS_STORAGE_KEY,
        value: JSON.stringify(filteredSegments)
      });

      return true;
    } catch (error) {
      console.error('Error deleting user segment', error);
      return false;
    }
  }

  /**
   * Get statistics for a specific campaign
   * @param id Campaign ID
   * @returns Observable with campaign statistics
   */
  getCampaignStats(id: string): Observable<CampaignStatistics> {
    // TODO: Replace with actual API call when backend is ready
    // For now, return mock data
    return from(this.getCampaign(id)).pipe(
      map(campaign => {
        if (!campaign) {
          throw new Error(`Campaign with ID ${id} not found`);
        }

        const mockStats = {
          name: campaign.name,
          total: campaign.stats?.total || 0,
          delivered: campaign.stats?.delivered || 0,
          opened: campaign.stats?.opened || 0,
          clicked: campaign.stats?.clicked || 0,
          byDevice: [
            { deviceType: 'Android', count: Math.floor(Math.random() * 100), percentage: 0 },
            { deviceType: 'iOS', count: Math.floor(Math.random() * 100), percentage: 0 },
            { deviceType: 'Web', count: Math.floor(Math.random() * 50), percentage: 0 }
          ],
          byGroup: campaign.targetGroups?.map(group => ({
            groupId: group,
            groupName: group,
            count: Math.floor(Math.random() * 50),
            percentage: 0
          })) || []
        };

        return {
          campaignId: id,
          campaignName: mockStats.name || 'Unknown Campaign',
          totalSent: mockStats.total || 0,
          delivered: mockStats.delivered || 0,
          opened: mockStats.opened || 0,
          clicked: mockStats.clicked || 0,
          openRate: mockStats.delivered > 0 ? mockStats.opened / mockStats.delivered : 0,
          clickRate: mockStats.opened > 0 ? mockStats.clicked / mockStats.opened : 0,
          deliveryRate: mockStats.total > 0 ? mockStats.delivered / mockStats.total : 0,
          byDevice: mockStats.byDevice || [],
          byGroup: mockStats.byGroup || []
        };
      }),
      catchError(error => {
        console.error('Error getting campaign statistics', error);
        return of({
          campaignId: id,
          campaignName: 'Unknown Campaign',
          totalSent: 0,
          delivered: 0,
          opened: 0,
          clicked: 0,
          openRate: 0,
          clickRate: 0,
          deliveryRate: 0,
          byDevice: [],
          byGroup: []
        });
      })
    );
  }

  /**
   * Get overall notification statistics
   * @returns Observable with overall statistics
   */
  getOverallStats(): Observable<OverallStatistics> {
    return from(this.getCampaigns()).pipe(
      map(campaigns => {
        const sentCampaigns = campaigns.filter(c => c.status === CampaignStatus.SENT);
        const totalSent = sentCampaigns.reduce((sum, c) => sum + (c.stats?.total || 0), 0);
        const totalDelivered = sentCampaigns.reduce((sum, c) => sum + (c.stats?.delivered || 0), 0);
        const totalOpened = sentCampaigns.reduce((sum, c) => sum + (c.stats?.opened || 0), 0);
        const totalClicked = sentCampaigns.reduce((sum, c) => sum + (c.stats?.clicked || 0), 0);

        const averageOpenRate = totalDelivered > 0 ? totalOpened / totalDelivered : 0;
        const averageClickRate = totalOpened > 0 ? totalClicked / totalOpened : 0;
        const averageDeliveryRate = totalSent > 0 ? totalDelivered / totalSent : 0;

        // Calculate top campaigns by open rate
        const topCampaigns = sentCampaigns
          .filter(c => (c.stats?.delivered || 0) > 0)
          .map(c => ({
            id: c.id || '',
            name: c.name,
            openRate: (c.stats?.opened || 0) / (c.stats?.delivered || 1)
          }))
          .sort((a, b) => b.openRate - a.openRate)
          .slice(0, 5);

        return {
          totalCampaigns: sentCampaigns.length,
          totalSent,
          totalDelivered,
          totalOpened,
          totalClicked,
          averageOpenRate,
          averageClickRate,
          averageDeliveryRate,
          topCampaigns,
          byDevice: [],
          byGroup: []
        };
      }),
      catchError(error => {
        console.error('Error getting overall statistics', error);
        return of({
          totalCampaigns: 0,
          totalSent: 0,
          totalDelivered: 0,
          totalOpened: 0,
          totalClicked: 0,
          averageOpenRate: 0,
          averageClickRate: 0,
          averageDeliveryRate: 0,
          topCampaigns: [],
          byDevice: [],
          byGroup: []
        });
      })
    );
  }

  /**
   * Generate a unique ID
   * @returns Unique ID string
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }
}
