<div class="notification-preview-container">
  <div *ngIf="showDeviceFrame" class="preview-device" [ngClass]="deviceType">
    <div class="preview-notification">
      <div class="notification-header">
        <div class="app-icon"></div>
        <div class="app-info">
          <div class="app-name">LP App</div>
          <div class="notification-time">now</div>
        </div>
      </div>
      <div class="notification-content">
        <div class="notification-title">{{ getTitle() }}</div>
        <div class="notification-body">{{ getBody() }}</div>
        <div class="notification-image" *ngIf="hasImage()" [style.background-image]="'url(' + getImageUrl() + ')'"></div>
        <div class="notification-action" *ngIf="hasAction()">
          <ion-button fill="clear" size="small">
            <ion-icon name="open-outline" slot="start"></ion-icon>
            Open
          </ion-button>
        </div>
      </div>
    </div>
  </div>
  
  <div *ngIf="!showDeviceFrame" class="preview-notification-simple" [ngClass]="deviceType">
    <div class="notification-title">{{ getTitle() }}</div>
    <div class="notification-body">{{ getBody() }}</div>
    <div class="notification-image" *ngIf="hasImage()" [style.background-image]="'url(' + getImageUrl() + ')'"></div>
    <div class="notification-action" *ngIf="hasAction()">
      <ion-button fill="clear" size="small">
        <ion-icon name="open-outline" slot="start"></ion-icon>
        Open
      </ion-button>
    </div>
  </div>
</div>
