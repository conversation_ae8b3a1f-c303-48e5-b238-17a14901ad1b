.notification-preview-container {
  display: flex;
  justify-content: center;
}

.preview-device {
  width: 320px;
  padding: 16px;
  border-radius: 12px;
  
  &.ios {
    background: #f2f2f7;
    
    .preview-notification {
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    
    .notification-header {
      padding: 12px 16px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #e5e5ea;
    }
    
    .app-icon {
      width: 20px;
      height: 20px;
      background: var(--ion-color-primary);
      border-radius: 4px;
      margin-right: 8px;
    }
    
    .app-info {
      flex: 1;
    }
    
    .app-name {
      font-size: 13px;
      font-weight: 600;
      color: #333;
    }
    
    .notification-time {
      font-size: 12px;
      color: #8e8e93;
    }
    
    .notification-content {
      padding: 12px 16px;
    }
    
    .notification-title {
      font-size: 15px;
      font-weight: 600;
      margin-bottom: 4px;
      color: #333;
    }
    
    .notification-body {
      font-size: 14px;
      color: #666;
      line-height: 1.4;
    }
    
    .notification-image {
      margin-top: 12px;
      height: 160px;
      background-size: cover;
      background-position: center;
      border-radius: 8px;
    }
    
    .notification-action {
      margin-top: 8px;
      display: flex;
      justify-content: flex-end;
    }
  }
  
  &.android {
    background: #fafafa;
    
    .preview-notification {
      background: white;
      border-radius: 8px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
      overflow: hidden;
    }
    
    .notification-header {
      padding: 16px 16px 8px;
      display: flex;
      align-items: center;
    }
    
    .app-icon {
      width: 24px;
      height: 24px;
      background: var(--ion-color-primary);
      border-radius: 50%;
      margin-right: 12px;
    }
    
    .app-info {
      flex: 1;
    }
    
    .app-name {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
    
    .notification-time {
      font-size: 12px;
      color: #757575;
    }
    
    .notification-content {
      padding: 0 16px 16px;
    }
    
    .notification-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 4px;
      color: #212121;
    }
    
    .notification-body {
      font-size: 14px;
      color: #616161;
      line-height: 1.4;
    }
    
    .notification-image {
      margin-top: 12px;
      height: 160px;
      background-size: cover;
      background-position: center;
      border-radius: 4px;
    }
    
    .notification-action {
      margin-top: 8px;
      display: flex;
      justify-content: flex-end;
    }
  }
  
  &.web {
    background: #ffffff;
    
    .preview-notification {
      background: white;
      border-radius: 4px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
      overflow: hidden;
      border: 1px solid #e0e0e0;
    }
    
    .notification-header {
      padding: 12px 16px;
      display: flex;
      align-items: center;
      background: #f5f5f5;
      border-bottom: 1px solid #e0e0e0;
    }
    
    .app-icon {
      width: 16px;
      height: 16px;
      background: var(--ion-color-primary);
      border-radius: 2px;
      margin-right: 8px;
    }
    
    .app-info {
      flex: 1;
    }
    
    .app-name {
      font-size: 13px;
      font-weight: 600;
      color: #333;
    }
    
    .notification-time {
      font-size: 12px;
      color: #757575;
    }
    
    .notification-content {
      padding: 16px;
    }
    
    .notification-title {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #333;
    }
    
    .notification-body {
      font-size: 13px;
      color: #666;
      line-height: 1.4;
    }
    
    .notification-image {
      margin-top: 12px;
      height: 140px;
      background-size: cover;
      background-position: center;
      border-radius: 2px;
    }
    
    .notification-action {
      margin-top: 8px;
      display: flex;
      justify-content: flex-end;
    }
  }
}

.preview-notification-simple {
  width: 100%;
  padding: 16px;
  border-radius: 8px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  &.ios {
    border-radius: 12px;
    
    .notification-title {
      font-size: 15px;
      font-weight: 600;
      margin-bottom: 4px;
      color: #333;
    }
    
    .notification-body {
      font-size: 14px;
      color: #666;
      line-height: 1.4;
    }
    
    .notification-image {
      margin-top: 12px;
      height: 160px;
      background-size: cover;
      background-position: center;
      border-radius: 8px;
    }
  }
  
  &.android {
    border-radius: 8px;
    
    .notification-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 4px;
      color: #212121;
    }
    
    .notification-body {
      font-size: 14px;
      color: #616161;
      line-height: 1.4;
    }
    
    .notification-image {
      margin-top: 12px;
      height: 160px;
      background-size: cover;
      background-position: center;
      border-radius: 4px;
    }
  }
  
  &.web {
    border-radius: 4px;
    border: 1px solid #e0e0e0;
    
    .notification-title {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #333;
    }
    
    .notification-body {
      font-size: 13px;
      color: #666;
      line-height: 1.4;
    }
    
    .notification-image {
      margin-top: 12px;
      height: 140px;
      background-size: cover;
      background-position: center;
      border-radius: 2px;
    }
  }
  
  .notification-action {
    margin-top: 8px;
    display: flex;
    justify-content: flex-end;
  }
}

// Dark mode adjustments
:host-context(.dark-theme) {
  .preview-device {
    &.ios {
      background: #1c1c1e;
      
      .preview-notification {
        background: #2c2c2e;
      }
      
      .notification-header {
        border-bottom-color: #3a3a3c;
      }
      
      .app-name {
        color: #fff;
      }
      
      .notification-time {
        color: #8e8e93;
      }
      
      .notification-title {
        color: #fff;
      }
      
      .notification-body {
        color: #ebebf0;
      }
    }
    
    &.android {
      background: #121212;
      
      .preview-notification {
        background: #242424;
      }
      
      .app-name {
        color: #e0e0e0;
      }
      
      .notification-time {
        color: #9e9e9e;
      }
      
      .notification-title {
        color: #fff;
      }
      
      .notification-body {
        color: #bdbdbd;
      }
    }
    
    &.web {
      background: #202124;
      
      .preview-notification {
        background: #292a2d;
        border-color: #3c4043;
      }
      
      .notification-header {
        background: #202124;
        border-bottom-color: #3c4043;
      }
      
      .app-name {
        color: #e8eaed;
      }
      
      .notification-time {
        color: #9aa0a6;
      }
      
      .notification-title {
        color: #e8eaed;
      }
      
      .notification-body {
        color: #bdc1c6;
      }
    }
  }
  
  .preview-notification-simple {
    background: var(--ion-background-color-step-50);
    
    &.ios {
      .notification-title {
        color: #fff;
      }
      
      .notification-body {
        color: #ebebf0;
      }
    }
    
    &.android {
      .notification-title {
        color: #fff;
      }
      
      .notification-body {
        color: #bdbdbd;
      }
    }
    
    &.web {
      border-color: #3c4043;
      
      .notification-title {
        color: #e8eaed;
      }
      
      .notification-body {
        color: #bdc1c6;
      }
    }
  }
}
