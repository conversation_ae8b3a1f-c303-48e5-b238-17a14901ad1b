import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { NotificationTemplate } from '../models/notification-admin.models';

/**
 * Component for previewing how a notification will look
 */
@Component({
  selector: 'lib-notification-preview',
  standalone: true,
  imports: [CommonModule, IonicModule],
  templateUrl: './notification-preview.component.html',
  styleUrls: ['./notification-preview.component.scss']
})
export class NotificationPreviewComponent {
  /** Notification template to preview */
  @Input() notification: NotificationTemplate | null = null;
  
  /** Device type (ios, android, web) */
  @Input() deviceType: string = 'ios';
  
  /** Show full preview with device frame */
  @Input() showDeviceFrame: boolean = true;
  
  /**
   * Get notification title
   * @returns Notification title or placeholder
   */
  getTitle(): string {
    return this.notification?.title || 'Notification Title';
  }
  
  /**
   * Get notification body
   * @returns Notification body or placeholder
   */
  getBody(): string {
    return this.notification?.body || 'Notification body text goes here. This is how your notification will look when sent to users.';
  }
  
  /**
   * Get notification image URL
   * @returns Image URL or null
   */
  getImageUrl(): string | null {
    return this.notification?.image || null;
  }
  
  /**
   * Check if notification has an image
   * @returns True if notification has an image
   */
  hasImage(): boolean {
    return !!this.getImageUrl();
  }
  
  /**
   * Get notification action
   * @returns Action or null
   */
  getAction(): string | null {
    return this.notification?.action || null;
  }
  
  /**
   * Check if notification has an action
   * @returns True if notification has an action
   */
  hasAction(): boolean {
    return !!this.getAction();
  }
}
