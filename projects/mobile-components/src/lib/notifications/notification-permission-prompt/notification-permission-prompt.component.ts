import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule, ModalController } from '@ionic/angular';

@Component({
  selector: 'lib-notification-permission-prompt',
  standalone: true,
  imports: [CommonModule, IonicModule],
  templateUrl: './notification-permission-prompt.component.html',
  styleUrls: ['./notification-permission-prompt.component.scss']
})
export class NotificationPermissionPromptComponent {
  
  constructor(private modalController: ModalController) {}
  
  /**
   * Accept notifications and close the modal
   */
  accept() {
    this.modalController.dismiss(true);
  }
  
  /**
   * Decline notifications and close the modal
   */
  decline() {
    this.modalController.dismiss(false);
  }
  
  /**
   * Remind later and close the modal
   */
  remindLater() {
    this.modalController.dismiss('later');
  }
}
