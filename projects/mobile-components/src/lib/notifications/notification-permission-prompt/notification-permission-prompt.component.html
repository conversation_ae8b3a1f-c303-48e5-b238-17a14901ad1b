<div class="permission-prompt">
  <div class="prompt-header">
    <ion-icon name="notifications"></ion-icon>
    <h2>Enable Notifications</h2>
  </div>
  
  <div class="prompt-content">
    <p class="prompt-description">
      Stay updated with important information about your account, points, and exclusive offers.
    </p>
    
    <div class="benefits">
      <div class="benefit-item">
        <ion-icon name="gift-outline"></ion-icon>
        <div class="benefit-text">
          <h4>Special Offers</h4>
          <p>Be the first to know about promotions and discounts</p>
        </div>
      </div>
      
      <div class="benefit-item">
        <ion-icon name="card-outline"></ion-icon>
        <div class="benefit-text">
          <h4>Transaction Updates</h4>
          <p>Get notified about points earned and redeemed</p>
        </div>
      </div>
      
      <div class="benefit-item">
        <ion-icon name="game-controller-outline"></ion-icon>
        <div class="benefit-text">
          <h4>Game Rewards</h4>
          <p>Never miss a game reward or bonus opportunity</p>
        </div>
      </div>
    </div>
  </div>
  
  <div class="prompt-actions">
    <ion-button expand="block" color="primary" (click)="accept()">
      Enable Notifications
    </ion-button>
    
    <ion-button expand="block" fill="outline" color="medium" (click)="remindLater()">
      Remind Me Later
    </ion-button>
    
    <ion-button expand="block" fill="clear" color="medium" (click)="decline()">
      No Thanks
    </ion-button>
  </div>
</div>
