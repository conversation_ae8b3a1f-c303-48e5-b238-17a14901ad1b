.permission-prompt {
  padding: 24px;
  background-color: var(--ion-background-color, #ffffff);
  border-radius: 16px;
  max-width: 400px;
  margin: 0 auto;
}

.prompt-header {
  text-align: center;
  margin-bottom: 24px;
  
  ion-icon {
    font-size: 48px;
    color: var(--ion-color-primary, #3880ff);
    margin-bottom: 16px;
    background: linear-gradient(135deg, var(--ion-color-primary-shade, #3171e0), var(--ion-color-primary, #3880ff));
    border-radius: 50%;
    padding: 12px;
    color: white;
  }
  
  h2 {
    font-size: 22px;
    font-weight: 600;
    margin: 0;
    color: var(--ion-text-color, #333333);
  }
}

.prompt-content {
  margin-bottom: 24px;
  
  .prompt-description {
    text-align: center;
    font-size: 16px;
    line-height: 1.5;
    color: var(--ion-color-medium, #92949c);
    margin-bottom: 24px;
  }
}

.benefits {
  .benefit-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    ion-icon {
      font-size: 24px;
      margin-right: 16px;
      color: var(--ion-color-primary, #3880ff);
      margin-top: 2px;
    }
    
    .benefit-text {
      h4 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 500;
        color: var(--ion-text-color, #333333);
      }
      
      p {
        margin: 0;
        font-size: 14px;
        color: var(--ion-color-medium, #92949c);
        line-height: 1.4;
      }
    }
  }
}

.prompt-actions {
  ion-button {
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

// Dark mode adjustments
:host-context(.dark-theme) {
  .permission-prompt {
    background-color: var(--ion-background-color, #121212);
  }
}
