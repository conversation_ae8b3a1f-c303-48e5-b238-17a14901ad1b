import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { NotificationTemplate } from '../models/notification-admin.models';
import { NotificationGroup } from '../models/notification.models';

// Add $any helper for template type casting
declare global {
  interface Window {
    $any: (obj: any) => any;
  }
}
window.$any = (obj: any) => obj;

/**
 * Component for creating and editing notification templates
 */
@Component({
  selector: 'lib-notification-template-form',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, IonicModule],
  templateUrl: './notification-template-form.component.html',
  styleUrls: ['./notification-template-form.component.scss']
})
export class NotificationTemplateFormComponent implements OnInit {
  /** Template to edit (null for new template) */
  @Input() template: NotificationTemplate | null = null;

  /** Available notification groups */
  @Input() groups: NotificationGroup[] = [];

  /** Event emitted when template is saved */
  @Output() save = new EventEmitter<NotificationTemplate>();

  /** Event emitted when form is cancelled */
  @Output() cancel = new EventEmitter<void>();

  /** Form group for template */
  templateForm: FormGroup;

  /** Preview mode (ios, android, web) */
  previewMode: string = 'ios';

  constructor(private fb: FormBuilder) {
    this.templateForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(50)]],
      title: ['', [Validators.required, Validators.maxLength(100)]],
      body: ['', [Validators.required, Validators.maxLength(500)]],
      image: ['', Validators.pattern(/^(https?:\/\/.*|)$/)],
      action: [''],
      groupId: ['']
    });
  }

  /**
   * Initialize component
   */
  ngOnInit() {
    if (this.template) {
      this.templateForm.patchValue(this.template);
    }
  }

  /**
   * Handle form submission
   */
  onSubmit() {
    if (this.templateForm.valid) {
      const template: NotificationTemplate = {
        ...this.template,
        ...this.templateForm.value,
        updatedAt: Date.now()
      };

      if (!template.id) {
        template.createdAt = Date.now();
      }

      this.save.emit(template);
    } else {
      // Mark all fields as touched to show validation errors
      this.markFormGroupTouched(this.templateForm);
    }
  }

  /**
   * Handle form cancellation
   */
  onCancel() {
    this.cancel.emit();
  }

  /**
   * Change preview mode
   * @param mode Preview mode (ios, android, web)
   */
  changePreviewMode(mode: string) {
    this.previewMode = mode;
  }

  /**
   * Get preview notification
   * @returns Notification template for preview
   */
  getPreviewNotification(): NotificationTemplate {
    return {
      ...this.templateForm.value,
      id: 'preview',
      name: this.templateForm.value.name || 'Preview Template',
      title: this.templateForm.value.title || 'Preview Title',
      body: this.templateForm.value.body || 'Preview body text goes here. This is how your notification will look when sent to users.',
      createdAt: Date.now(),
      updatedAt: Date.now()
    };
  }

  /**
   * Check if form field is invalid
   * @param field Field name
   * @returns True if field is invalid and touched
   */
  isFieldInvalid(field: string): boolean {
    const formControl = this.templateForm.get(field);
    return formControl ? formControl.invalid && formControl.touched : false;
  }

  /**
   * Get error message for form field
   * @param field Field name
   * @returns Error message
   */
  getErrorMessage(field: string): string {
    const formControl = this.templateForm.get(field);

    if (!formControl) {
      return '';
    }

    if (formControl.hasError('required')) {
      return 'This field is required';
    }

    if (formControl.hasError('maxlength')) {
      const maxLength = formControl.getError('maxlength').requiredLength;
      return `Maximum length is ${maxLength} characters`;
    }

    if (formControl.hasError('pattern')) {
      return 'Please enter a valid URL or leave empty';
    }

    return '';
  }

  /**
   * Mark all controls in form group as touched
   * @param formGroup Form group
   */
  private markFormGroupTouched(formGroup: FormGroup) {
    Object.values(formGroup.controls).forEach(control => {
      control.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
}
