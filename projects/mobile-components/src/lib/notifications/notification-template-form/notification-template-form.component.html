<div class="template-form-container">
  <form [formGroup]="templateForm" (ngSubmit)="onSubmit()">
    <div class="form-section">
      <h2>Template Details</h2>

      <ion-item>
        <ion-label position="floating">Template Name <ion-text color="danger">*</ion-text></ion-label>
        <ion-input formControlName="name" placeholder="Enter a name for this template"></ion-input>
        <ion-note slot="error" *ngIf="isFieldInvalid('name')">{{ getErrorMessage('name') }}</ion-note>
      </ion-item>

      <ion-item>
        <ion-label position="floating">Notification Title <ion-text color="danger">*</ion-text></ion-label>
        <ion-input formControlName="title" placeholder="Enter notification title"></ion-input>
        <ion-note slot="error" *ngIf="isFieldInvalid('title')">{{ getErrorMessage('title') }}</ion-note>
      </ion-item>

      <ion-item>
        <ion-label position="floating">Notification Body <ion-text color="danger">*</ion-text></ion-label>
        <ion-textarea formControlName="body" placeholder="Enter notification body text" [rows]="4"></ion-textarea>
        <ion-note slot="error" *ngIf="isFieldInvalid('body')">{{ getErrorMessage('body') }}</ion-note>
      </ion-item>

      <ion-item>
        <ion-label position="floating">Image URL</ion-label>
        <ion-input formControlName="image" placeholder="https://example.com/image.jpg"></ion-input>
        <ion-note slot="error" *ngIf="isFieldInvalid('image')">{{ getErrorMessage('image') }}</ion-note>
      </ion-item>

      <ion-item>
        <ion-label position="floating">Action (Deep Link)</ion-label>
        <ion-input formControlName="action" placeholder="/secure/profile"></ion-input>
      </ion-item>

      <ion-item>
        <ion-label position="floating">Notification Group</ion-label>
        <ion-select formControlName="groupId" placeholder="Select a group">
          <ion-select-option value="">None</ion-select-option>
          <ion-select-option *ngFor="let group of groups" [value]="group.id">{{ group.name }}</ion-select-option>
        </ion-select>
      </ion-item>
    </div>

    <div class="form-section">
      <h2>Preview</h2>
      <div class="preview-mode-selector">
        <ion-segment [value]="previewMode" (ionChange)="changePreviewMode($any($event).detail.value)">
          <ion-segment-button value="ios">
            <ion-label>iOS</ion-label>
          </ion-segment-button>
          <ion-segment-button value="android">
            <ion-label>Android</ion-label>
          </ion-segment-button>
          <ion-segment-button value="web">
            <ion-label>Web</ion-label>
          </ion-segment-button>
        </ion-segment>
      </div>

      <div class="notification-preview">
        <div class="preview-device" [ngClass]="previewMode">
          <div class="preview-notification">
            <div class="notification-header">
              <div class="app-icon"></div>
              <div class="app-info">
                <div class="app-name">LP App</div>
                <div class="notification-time">now</div>
              </div>
            </div>
            <div class="notification-content">
              <div class="notification-title">{{ getPreviewNotification().title || 'Notification Title' }}</div>
              <div class="notification-body">{{ getPreviewNotification().body || 'Notification body text' }}</div>
              <div class="notification-image" *ngIf="getPreviewNotification().image" [style.background-image]="'url(' + getPreviewNotification().image + ')'"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="form-actions">
      <ion-button type="button" fill="outline" (click)="onCancel()">
        <ion-icon name="close-outline" slot="start"></ion-icon>
        Cancel
      </ion-button>
      <ion-button type="submit" [disabled]="templateForm.invalid">
        <ion-icon name="save-outline" slot="start"></ion-icon>
        Save Template
      </ion-button>
    </div>
  </form>
</div>
