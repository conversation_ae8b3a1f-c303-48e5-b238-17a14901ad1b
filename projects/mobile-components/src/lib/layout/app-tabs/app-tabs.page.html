<ion-tabs >
  <ion-tab-bar slot="bottom" mode="ios">
    <ion-tab-button tab="home" #home>
      <ion-icon [name]="home.selected ? 'home' : 'home-outline'"></ion-icon>
      <ion-label>Home</ion-label>
    </ion-tab-button>

    <ion-tab-button tab="stores" #stores>
      <ion-icon [name]="stores.selected ? 'storefront' : 'storefront-outline'"></ion-icon>
      <ion-label>Stores</ion-label>
    </ion-tab-button>

    <ion-tab-button tab="transactions" #transactions>
      <ion-icon [name]="transactions.selected ? 'pricetags' : 'pricetags-outline'"></ion-icon>
      <ion-label>Transactions</ion-label>
    </ion-tab-button>

    <ion-tab-button tab="virtualcard" #virtualcard>
      <ion-icon [name]="virtualcard.selected ? 'card' : 'card-outline'"></ion-icon>
      <ion-label>Virtual Card</ion-label>
    </ion-tab-button>

    <ion-tab-button tab="account" #account>
      <ion-icon [name]="account.selected ? 'person' : 'person-outline'"></ion-icon>
      <ion-label>Account</ion-label>
    </ion-tab-button>
  </ion-tab-bar>

  <div class="indicator"></div>
</ion-tabs>
