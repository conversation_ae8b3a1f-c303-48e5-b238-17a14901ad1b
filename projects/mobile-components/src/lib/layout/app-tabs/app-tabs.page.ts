import { Component, Optional, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonRouterOutlet, Platform, IonicModule } from '@ionic/angular';
import { App } from '@capacitor/app';
import { Router } from '@angular/router';
@Component({
  selector: 'app-tabs',
  templateUrl: 'app-tabs.page.html',
  styleUrls: ['app-tabs.page.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class AppTabsPage {
  constructor(
    protected readonly router: Router,
    private platform: Platform,
    @Optional() private routerOutlet: IonRouterOutlet
  ) {
    this.hardwareBackButton(); // Handle hardware back button to exit the application
  }

  hardwareBackButton(): void {
    this.platform.backButton.subscribeWithPriority(-1, () => {
      if (!this.routerOutlet.canGoBack()) {
        if (this.router.url.includes('/public/landing')) {
          App.exitApp();
        } else {
          this.router.navigate(['/public/landing']);
        }
      }
    });
  }

  ngOnDestroy(): void {}
}
