import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HomeComponent } from '../home/<USER>';
import { AppTabsPage } from './app-tabs.page';
import { AuthGuardService } from 'lp-client-api';
import { ProductsComponent } from '../../secure/products/products.component';
import { VirtualCardComponent } from '../../secure/virtualcard/virtualcard.component';
import { DashboardComponent } from '../../secure/dashboard/dashboard.component';
import { TransactionsComponent } from '../../secure/transactions/transactions.component';
import { StoresComponent } from '../stores/stores.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: '/public/landing',
    pathMatch: 'full',
  },
  {
    path: 'app',
    component: AppTabsPage,
    children: [
      {
        path: 'home',
        component: HomeComponent,
      },
      {
        path: 'stores',
        component: StoresComponent,
        canActivate: [AuthGuardService]
      },
      {
        path: 'virtualcard',
        component: VirtualCardComponent,
        canActivate: [AuthGuardService],
      },
      {
        path: 'account',
        component: DashboardComponent,
        canActivate: [AuthGuardService],
      },
      {
        path: 'transactions',
        component: TransactionsComponent,
        canActivate: [AuthGuardService],
      },
      {
        path: 'tab2',
        loadChildren: () =>
          import('../tab2/tab2.module').then((m) => m.Tab2PageModule),
      },
      {
        path: '',
        redirectTo: '/public/landing',
        pathMatch: 'full',
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
})
export class TabsPageRoutingModule {}
