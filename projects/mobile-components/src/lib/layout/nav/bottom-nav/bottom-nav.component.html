<div class="fixed right-0 bottom-0 left-0 bg-gray-100 border-t border-gray-500">
  <nav class="flex justify-around py-2">
    <a
      routerLink="/"
      routerLinkActive="active"
      class="flex flex-col items-center text-gray-500 transition-colors duration-200 hover:text-primary-500"
    >
      <i class="material-icons">home</i>
      <span class="text-xs">Home</span>
    </a>
    <a
      routerLink="/public/games/home"
      routerLinkActive="active"
      class="flex flex-col items-center text-gray-500 transition-colors duration-200 hover:text-primary-500"
    >
      <i class="material-icons">games</i>
      <span class="text-xs">Games</span>
    </a>
    <a
      routerLink="/add"
      routerLinkActive="active"
      class="flex flex-col items-center text-gray-500 transition-colors duration-200 hover:text-primary-500"
    >
      <i class="material-icons">add_circle</i>
      <span class="text-xs">Add</span>
    </a>
    <a
      routerLink="/favorites"
      routerLinkActive="active"
      class="flex flex-col items-center text-gray-500 transition-colors duration-200 hover:text-primary-500"
    >
      <i class="material-icons">favorite</i>
      <span class="text-xs">Favorites</span>
    </a>
    <a
      routerLink="/profile"
      routerLinkActive="active"
      class="flex flex-col items-center text-gray-500 transition-colors duration-200 hover:text-primary-500"
    >
      <i class="material-icons">person</i>
      <span class="text-xs">Profile</span>
    </a>
  </nav>
</div>
