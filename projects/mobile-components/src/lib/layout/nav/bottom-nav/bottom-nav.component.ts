import { Component } from '@angular/core';
import { Router } from '@angular/router'; // Add this import

@Component({
  selector: 'lib-bottom-nav',
  templateUrl: './bottom-nav.component.html',
  styleUrls: ['./bottom-nav.component.css'],
})
export class BottomNavComponent {
  constructor(private router: Router) {
    // Example usage of router to resolve the error
    this.router.navigate(['/home']); // Navigate to a default route
  }
}
