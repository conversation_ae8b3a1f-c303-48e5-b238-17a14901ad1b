/* Modern header styling */
header {
  background: linear-gradient(to right, #0a2463, #1e88e5, #0a2463) !important;
  backdrop-filter: blur(10px);
  border-bottom: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  padding: 12px 16px;
  position: relative;
  overflow: hidden;
  height: 70px;
  display: flex;
  align-items: center;
}

/* Background glow effect */
header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle at center,
    rgba(33, 150, 243, 0.3) 0%,
    rgba(33, 150, 243, 0) 70%
  );
  animation: rotate 15s linear infinite;
  z-index: 0;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Content container */
header > div {
  position: relative;
  z-index: 1;
}

/* Greeting text */
header p {
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.5px;
  color: rgba(255, 255, 255, 0.9);
}

/* Icon styling */
header ion-icon {
  font-size: 24px;
  color: white;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* Icon container */
.icon-container {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
}

/* Logo image */
header img {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

header img:hover {
  transform: scale(1.05);
  border-color: rgba(255, 255, 255, 0.4);
}

/* Dropdown menu - now created programmatically */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Custom dropdown menu */
.dropdown-menu-custom {
  background: linear-gradient(135deg, #1e3c72, #2a5298);
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  transform-origin: top right;
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
  animation: slideIn 0.3s ease-out forwards;
}

.dropdown-menu button {
  color: white;
  background: transparent;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dropdown-menu button:last-child {
  border-bottom: none;
}

.dropdown-menu button:hover {
  background: rgba(255, 255, 255, 0.1);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Highlight elements */
.highlight {
  color: #90caf9 !important;
  font-weight: 600;
}

/* Ripple effect on click */
header img::after, header button::after, .icon-container::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.6);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

/* Dropdown icon rotation */
.rotate-180 {
  transform: rotate(180deg);
}

header .nav-clicked::after {
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 1;
  }
  20% {
    transform: scale(25, 25);
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}