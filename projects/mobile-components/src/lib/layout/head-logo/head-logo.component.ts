import {
  Component,
  Injector,
  Input,
  OnInit,
  Output,
  EventEmitter,
  CUSTOM_ELEMENTS_SCHEMA,
  Optional,
  Renderer2,
  Inject
} from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { KeyCloakService } from 'lp-client-api';
import { CommonModule } from '@angular/common';
import { trigger, state, style, animate, transition } from '@angular/animations';
import { Router } from '@angular/router';

@Component({
  selector: 'lib-head-logo',
  templateUrl: './head-logo.component.html',
  styleUrls: ['./head-logo.component.css'],
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  animations: [
    trigger('menuAnimation', [
      state('open', style({
        opacity: 1,
        transform: 'translateY(0) scale(1)'
      })),
      state('closed', style({
        opacity: 0,
        transform: 'translateY(-10px) scale(0.95)'
      })),
      transition('closed => open', [
        animate('0.3s ease-out')
      ]),
      transition('open => closed', [
        animate('0.3s ease-in')
      ])
    ])
  ]
})
export class HeadLogoComponent implements OnInit {
  @Input()
  names?: string;
  @Input()
  balance?: number;
  @Input()
  membership?: string;
  @Input()
  src?: string;
  @Input()
  phone?: string;
  @Input()
  type?: string = 'balance';
  @Input() bgColor?: string;

  typeOf = 'balance';
  text?: string = this.balance ? 'Balance' + ' ' + this.balance : '0';

  isDropdownOpen = false;

  @Output() logoutClicked = new EventEmitter<void>();

  constructor(
    @Optional() private kc: KeyCloakService,
    private renderer: Renderer2,
    @Inject(DOCUMENT) private document: Document,
    private router: Router
  ) {}

  get useText() {
    let text = '';

    if (this.type === 'balance') {
      text = this.balance ? 'Balance' + ' ' + this.balance : '0';
    }

    if (this.type === 'membership') {
      text = this.balance ? '#' + ' ' + this.membership : '0';
    }

    return text;
  }
  showText() {
    let typf = 'balance';
    if (this.typeOf === 'balance') {
      this.text = this.balance ? 'Balance' + ' ' + this.balance : '0';
      this.typeOf = 'membership';
      typf = 'membership';
    }

    if (this.typeOf === 'membership') {
      this.text = this.balance ? '#' + ' ' + this.membership : '0';
      this.typeOf = 'balance';
    }
  }

  ngOnInit(): void {
    this.showText();

    // Handle the case where logo is provided in config instead of src
    if (!this.src) {
      // Try to get the icon from various possible sources
      if ((this as any).config && (this as any).config.logo) {
        this.src = (this as any).config.logo;
        console.log('HeadLogoComponent: Using logo from config.logo:', this.src);
      } else if ((this as any).config && (this as any).config.logoUrl) {
        this.src = (this as any).config.logoUrl;
        console.log('HeadLogoComponent: Using logo from config.logoUrl:', this.src);
      } else if ((this as any).logo) {
        this.src = (this as any).logo;
        console.log('HeadLogoComponent: Using logo from direct logo input:', this.src);
      } else {
        // Fallback to a default logo
        this.src = 'assets/images/logo.png';
        console.log('HeadLogoComponent: Using default logo path:', this.src);
      }
    }

    // Add a click handler to the document body to help debug
    this.renderer.listen(this.document.body, 'click', (event: MouseEvent) => {
      // Only log if it's a direct click on the body (not bubbled)
      if (event.target === this.document.body) {
        console.log('Body clicked at:', event.clientX, event.clientY);

        // Check if the dropdown exists
        const dropdown = this.document.querySelector('.dropdown-menu-custom');
        if (dropdown) {
          console.log('Dropdown exists:', dropdown);
        }
      }
    });
  }

  getGreeting(): string {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  }

  getBgColorClass(): string {
    const validColors = [
      'orange',
      'blue',
      'navy',
      'red',
      'green',
      'yellow',
      'purple',
      'pink',
      'indigo',
      'teal',
    ];
    return validColors.includes(this.bgColor?.toLowerCase() || '')
      ? `bg-${this.bgColor?.toLowerCase()}-500`
      : 'bg-blue-500';
  }

  // Create dropdown menu programmatically
  createDropdownMenu(event: MouseEvent): void {
    console.log('createDropdownMenu called');

    // Add click animation
    this.addClickAnimation(event);

    // Remove any existing dropdown
    const existingDropdown = this.document.querySelector('.dropdown-menu-custom');
    if (existingDropdown) {
      console.log('Removing existing dropdown');
      existingDropdown.remove();
      this.isDropdownOpen = false;
      return; // If we're removing an existing dropdown, don't create a new one
    }

    // Prevent event propagation to avoid immediate closing
    event.stopPropagation();
    console.log('Creating new dropdown');

    // Set dropdown state to open
    this.isDropdownOpen = true;

    // Get the position of the clicked element
    const target = event.currentTarget as HTMLElement;
    const rect = target.getBoundingClientRect();

    // Create dropdown element
    const dropdown = this.renderer.createElement('div');
    this.renderer.addClass(dropdown, 'dropdown-menu-custom');
    
    // Style the dropdown
    this.renderer.setStyle(dropdown, 'position', 'fixed');
    this.renderer.setStyle(dropdown, 'top', `${rect.bottom + 8}px`); // Add small gap
    this.renderer.setStyle(dropdown, 'left', `${rect.left - 200}px`); // Position further to the left of the logo
    this.renderer.setStyle(dropdown, 'min-width', '180px');
    this.renderer.setStyle(dropdown, 'background', 'linear-gradient(135deg, #2c3e50, #3498db)');
    this.renderer.setStyle(dropdown, 'border-radius', '8px');
    this.renderer.setStyle(dropdown, 'box-shadow', '0 4px 20px rgba(0, 0, 0, 0.2)');
    this.renderer.setStyle(dropdown, 'border', '1px solid rgba(255, 255, 255, 0.1)');
    this.renderer.setStyle(dropdown, 'z-index', '9999');
    this.renderer.setStyle(dropdown, 'opacity', '0');
    this.renderer.setStyle(dropdown, 'transform', 'translateY(-10px) translateX(50px)'); // Add horizontal slide
    this.renderer.setStyle(dropdown, 'transition', 'all 0.2s ease-out');

    // Create logout button
    const logoutBtn = this.renderer.createElement('button');
    this.renderer.addClass(logoutBtn, 'flex');
    this.renderer.addClass(logoutBtn, 'items-center');
    this.renderer.addClass(logoutBtn, 'w-full');
    this.renderer.setStyle(logoutBtn, 'padding', '12px 16px');
    this.renderer.setStyle(logoutBtn, 'color', 'white');
    this.renderer.setStyle(logoutBtn, 'cursor', 'pointer');
    this.renderer.setStyle(logoutBtn, 'background', 'transparent');
    this.renderer.setStyle(logoutBtn, 'border', 'none');
    this.renderer.setStyle(logoutBtn, 'font-size', '14px');
    this.renderer.setStyle(logoutBtn, 'transition', 'all 0.15s ease');

    // Create icon
    const icon = this.renderer.createElement('ion-icon');
    this.renderer.setAttribute(icon, 'name', 'log-out-outline');
    this.renderer.setStyle(icon, 'font-size', '18px');
    this.renderer.setStyle(icon, 'margin-right', '10px');

    // Create text
    const text = this.renderer.createElement('span');
    const textContent = this.renderer.createText('Logout');
    this.renderer.appendChild(text, textContent);

    // Add hover effect to button
    this.renderer.listen(logoutBtn, 'mouseenter', () => {
      this.renderer.setStyle(logoutBtn, 'background', 'rgba(255, 255, 255, 0.1)');
    });

    this.renderer.listen(logoutBtn, 'mouseleave', () => {
      this.renderer.setStyle(logoutBtn, 'background', 'transparent');
    });

    // Append elements
    this.renderer.appendChild(logoutBtn, icon);
    this.renderer.appendChild(logoutBtn, text);
    this.renderer.appendChild(dropdown, logoutBtn);

    // Add click event to logout button
    this.renderer.listen(logoutBtn, 'click', () => {
      console.log('Logout button clicked');
      this.logout();
      dropdown.remove();
    });

    // Add click event to document to close dropdown when clicking outside
    const documentClickListener = this.renderer.listen(this.document, 'click', (event: MouseEvent) => {
      if (!dropdown.contains(event.target as Node) && event.target !== target) {
        console.log('Clicked outside dropdown, removing');
        // Fade out animation before removing
        this.renderer.setStyle(dropdown, 'opacity', '0');
        this.renderer.setStyle(dropdown, 'transform', 'translateY(-10px)');
        setTimeout(() => {
          dropdown.remove();
          this.isDropdownOpen = false;
          documentClickListener(); // Remove the listener
        }, 200);
      }
    });

    // Append dropdown to body
    this.renderer.appendChild(this.document.body, dropdown);
    
    // Trigger entrance animation after a small delay
    setTimeout(() => {
      this.renderer.setStyle(dropdown, 'opacity', '1');
      this.renderer.setStyle(dropdown, 'transform', 'translateY(0) translateX(0)'); // Slide in from the right
    }, 50);

    console.log('Dropdown appended to body');
  }

  logout(): void {
    console.log('Logout method called');
    this.isDropdownOpen = false;
    localStorage.removeItem('lastWelcomeDate');
    localStorage.removeItem('hasSeenWelcome');

    // Use KeyCloakService for logout if available
    if (this.kc && this.kc.keycloak) {
      console.log('Logging out via KeyCloakService');
      this.kc.keycloak.logout();
    } else {
      console.log('KeyCloakService not available, emitting logoutClicked event');
      // Emit event so parent component can handle logout
      this.logoutClicked.emit();
    }
  }

  // Add click animation to elements
  addClickAnimation(event: MouseEvent): void {
    const target = event.currentTarget as HTMLElement;

    // Remove any existing animation class
    target.classList.remove('nav-clicked');

    // Force a reflow to restart the animation
    void target.offsetWidth;

    // Add the animation class
    target.classList.add('nav-clicked');

    // Remove the class after animation completes
    setTimeout(() => {
      target.classList.remove('nav-clicked');
    }, 600); // Match the animation duration
  }

  goBack(): void {
    window.history.back();
  }
}
