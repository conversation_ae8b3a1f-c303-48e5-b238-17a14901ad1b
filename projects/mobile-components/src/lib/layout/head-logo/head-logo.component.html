<header class="sticky top-0 z-50">
  <!-- Logo and Greeting -->
       <div class="icon-container cursor-pointer hover:opacity-70 transition-opacity" (click)="goBack()">
        <ion-icon name="backspace-outline" class="highlight"></ion-icon>
      </div>
  <div class="flex justify-between items-center w-full">
 
    <div class="flex items-center space-x-3" *ngIf="type === 'welcome'">
      <div class="icon-container">
        <ion-icon name="hand-left-outline" class="highlight"></ion-icon>
      </div>
      <p>
        {{ getGreeting() }}, <span class="highlight">{{ names }}</span>
      </p>
    </div>

    <div
      class="flex items-center space-x-3"
      *ngIf="type === 'balance' || type === 'membership'"
    >
      <div class="icon-container">
        <ion-icon name="wallet-outline" class="highlight"></ion-icon>
      </div>
      <p>
        <span class="highlight">{{ useText }}</span>
      </p>
    </div>

    <a
      class="flex items-center space-x-3"
      *ngIf="type === 'phone'"
      [href]="'tel:' + phone"
    >
      <div class="icon-container">
        <ion-icon name="call-outline" class="highlight"></ion-icon>
      </div>
      <p>
        <span class="highlight">{{ phone }}</span>
      </p>
    </a>

    <div class="relative">
      <img
        [src]="src"
        alt="Logo"
        class="cursor-pointer h-10 w-auto"
        (click)="createDropdownMenu($event)"
        style="border: 2px solid transparent; transition: all 0.3s ease;"
        onmouseover="this.style.border='2px solid #3498db'; this.style.transform='scale(1.05)'"
        onmouseout="this.style.border='2px solid transparent'; this.style.transform='scale(1)'"
      />
      <!-- Dropdown will be created programmatically -->
    </div>
  </div>
</header>
