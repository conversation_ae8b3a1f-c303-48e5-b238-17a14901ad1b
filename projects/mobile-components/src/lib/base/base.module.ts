import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Import mobile-optimized components
import { MobileGridComponent } from './mobile-grid/mobile-grid.component';
import { MobileNavigationComponent } from './mobile-navigation/mobile-navigation.component';
import { MobileModalComponent } from './mobile-modal/mobile-modal.component';
import { MobileSwipeCardComponent } from './mobile-swipe-card/mobile-swipe-card.component';

// Import ALL base components (standalone and non-standalone)
import { AccordianComponent } from './accordian/accordian.component'; // standalone
import { AutocompleteComponent } from './autocomplete/autocomplete.component'; // standalone
import { AutocompleteItemComponent } from './autocomplete-item/autocomplete-item.component'; // non-standalone
import { AvatarComponent } from './avatar/avatar.component'; // standalone
import { BreadcrumbComponent } from './breadcrumb/breadcrumb.component'; // standalone
import { ButtonComponent } from './button/button.component'; // standalone
import { ButtonActionComponent } from './button-action/button-action.component'; // standalone
import { ButtonCloseComponent } from './button-close/button-close.component'; // standalone
import { ButtonIconComponent } from './button-icon/button-icon.component'; // standalone
import { CardComponent } from './card/card.component'; // standalone
import { CheckboxComponent } from './checkbox/checkbox.component'; // standalone
import { CheckboxAnimatedComponent } from './checkbox-animated/checkbox-animated.component'; // standalone
// import { ContainerComponent } from './container/container.component'; // standalone
import { DatepickerComponent } from './datepicker/datepicker.component'; // standalone
import { DividerComponent } from './divider/divider.component'; // non-standalone
import { DropdownComponent } from './dropdown/dropdown.component'; // non-standalone
import { DropdownDividerComponent } from './dropdown-divider/dropdown-divider.component'; // non-standalone
import { DropdownItemComponent } from './dropdown-item/dropdown-item.component'; // non-standalone
import { HeadingComponent } from './heading/heading.component'; // standalone
import { IconComponent } from './icon/icon.component'; // standalone
import { ImageComponent } from './image/image.component'; // standalone
import { InputComponent } from './input/input.component'; // standalone
import { KbdComponent } from './kbd/kbd.component'; // non-standalone
import { LinkComponent } from './link/link.component'; // standalone
import { ListComponent } from './list/list.component'; // standalone
import { ListboxComponent } from './listbox/listbox.component'; // non-standalone
import { LogoComponent } from './logo/logo.component'; // standalone
import { MessageComponent } from './message/message.component'; // standalone
import { MobileComponent } from './mobile/mobile.component'; // standalone
import { ObjectComponent } from './object/object.component'; // non-standalone
import { PaginationComponent } from './pagination/pagination.component'; // standalone
import { ParagraphComponent } from './paragraph/paragraph.component'; // standalone
import { PictureComponent } from './picture/picture.component'; // standalone
import { PlaceholderPageComponent } from './placeholder-page/placeholder-page.component'; // standalone
import { BasePlaceloadComponent } from './placeload/placeload.component'; // standalone
import { ProgressComponent } from './progress/progress.component'; // standalone
import { ProseComponent } from './prose/prose.component'; // standalone
import { RadioComponent } from './radio/radio.component'; // standalone
import { SelectComponent } from './select/select.component'; // non-standalone
import { SnackComponent } from './snack/snack.component'; // non-standalone
import { SwitchBallComponent } from './switch-ball/switch-ball.component'; // non-standalone
import { SwitchThinComponent } from './switch-thin/switch-thin.component'; // non-standalone
import { TableComponent } from './table/table.component'; // non-standalone
import { TabsComponent } from './tabs/tabs.component'; // non-standalone
import { TagComponent } from './tag/tag.component'; // standalone
import { TextComponent } from './text/text.component'; // standalone
import { TextareaComponent } from './textarea/textarea.component'; // non-standalone



@NgModule({
  declarations: [
  ],
  imports: [
    CommonModule,
    IonicModule,
    AccordianComponent,
    AutocompleteComponent,
    AvatarComponent,
    BreadcrumbComponent,
    ButtonComponent,
    ButtonActionComponent,
    ButtonCloseComponent,
    ButtonIconComponent,
    CardComponent,
    CheckboxComponent,
    CheckboxAnimatedComponent,
    // ContainerComponent,
    DatepickerComponent,
    HeadingComponent,
    IconComponent,
    ImageComponent,
    InputComponent,
    LinkComponent,
    ListComponent,
    LogoComponent,
    MessageComponent,
    MobileComponent,
    PaginationComponent,
    ParagraphComponent,
    PictureComponent,
    PlaceholderPageComponent,
    BasePlaceloadComponent,
    ProgressComponent,
    ProseComponent,
    RadioComponent,
    TagComponent,
    TextComponent,
    AutocompleteItemComponent,
    DividerComponent,
    DropdownComponent,
    DropdownDividerComponent,
    DropdownItemComponent,
    KbdComponent,
    ListboxComponent,
    ObjectComponent,
    SelectComponent,
    SnackComponent,
    SwitchBallComponent,
    SwitchThinComponent,
    TableComponent,
    TabsComponent,
    TextareaComponent,
    MobileGridComponent,
    MobileNavigationComponent,
    MobileModalComponent,
    MobileSwipeCardComponent
  ],
  exports: [
    AccordianComponent,
    AutocompleteComponent,
    AvatarComponent,
    BreadcrumbComponent,
    ButtonComponent,
    ButtonActionComponent,
    ButtonCloseComponent,
    ButtonIconComponent,
    CardComponent,
    CheckboxComponent,
    CheckboxAnimatedComponent,
    // ContainerComponent,
    DatepickerComponent,
    HeadingComponent,
    IconComponent,
    ImageComponent,
    InputComponent,
    LinkComponent,
    ListComponent,
    LogoComponent,
    MessageComponent,
    MobileComponent,
    PaginationComponent,
    ParagraphComponent,
    PictureComponent,
    PlaceholderPageComponent,
    BasePlaceloadComponent,
    ProgressComponent,
    ProseComponent,
    RadioComponent,
    TagComponent,
    TextComponent,
    AutocompleteItemComponent,
    DividerComponent,
    DropdownComponent,
    DropdownDividerComponent,
    DropdownItemComponent,
    KbdComponent,
    ListboxComponent,
    ObjectComponent,
    SelectComponent,
    SnackComponent,
    SwitchBallComponent,
    SwitchThinComponent,
    TableComponent,
    TabsComponent,
    TextareaComponent,
    MobileGridComponent,
    MobileNavigationComponent,
    MobileModalComponent,
    MobileSwipeCardComponent
  ]
})
export class BaseModule { }
