import {
  Component,
  Input,
  ChangeDetectionStrategy,
  OnInit,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA
} from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'base-tag',
  templateUrl: './tag.component.html',
  styleUrls: ['./tag.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class TagComponent implements OnInit {
  /**
   * The variant of the tag.
   *
   * @since 2.0.0
   * @default 'solid'
   */
  @Input() variant: 'solid' | 'outline' | 'pastel' = 'solid';

  /**
   * The color of the tag.
   *
   * @default 'default'
   */
  @Input() color:
    | 'default'
    | 'default-contrast'
    | 'muted'
    | 'muted-contrast'
    | 'light'
    | 'dark'
    | 'black'
    | 'primary'
    | 'info'
    | 'success'
    | 'secondary'
    | 'warning'
    | 'danger' = 'default';

  /**
   * The radius of the tag.
   *
   * @since 2.0.0
   * @default 'lg'
   */
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'lg';

  /**
   * The size of the tag.
   *
   * @default 'md'
   */
  @Input() size: 'sm' | 'md' = 'md';

  /**
   * Determines when the tag should have a shadow.
   */
  @Input() shadow?: 'flat' | 'hover';

  @Input() item_id: string = Math.random().toString(36).substring(7);

  classes: string = '';

  private variants: Record<string, string> = {
    solid: 'nui-tag-solid',
    pastel: 'nui-tag-pastel',
    outline: 'nui-tag-outline',
  };

  private radiuses: Record<string, string> = {
    none: '',
    sm: 'nui-tag-rounded-sm',
    md: 'nui-tag-rounded-md',
    lg: 'nui-tag-rounded-lg',
    full: 'nui-tag-rounded-full',
  };

  private colors: Record<string, string> = {
    default: 'nui-tag-default',
    'default-contrast': 'nui-tag-default-contrast',
    muted: 'nui-tag-muted',
    'muted-contrast': 'nui-tag-muted-contrast',
    light: 'nui-tag-light',
    dark: 'nui-tag-dark',
    black: 'nui-tag-black',
    primary: 'nui-tag-primary',
    info: 'nui-tag-info',
    success: 'nui-tag-success',
    warning: 'nui-tag-warning',
    danger: 'nui-tag-danger',
    secondary: 'nui-tag-secondary',
  };

  private sizes: Record<string, string> = {
    sm: 'nui-tag-sm',
    md: 'nui-tag-md',
  };

  private shadows: Record<string, string> = {
    flat: 'nui-tag-shadow',
    hover: 'nui-tag-shadow-hover',
  };

  ngOnInit(): void {
    this.computeClasses();
  }

  ngOnChanges(): void {
    this.computeClasses();
  }

  private computeClasses(): void {
    this.classes = [
      'nui-tag',
      this.size ? this.sizes[this.size] : '',
      this.rounded ? this.radiuses[this.rounded] : '',
      this.variant ? this.variants[this.variant] : '',
      this.color ? this.colors[this.color] : '',
      this.shadow ? this.shadows[this.shadow] : '',
    ]
      .filter(Boolean)
      .join(' ');
  }
}
