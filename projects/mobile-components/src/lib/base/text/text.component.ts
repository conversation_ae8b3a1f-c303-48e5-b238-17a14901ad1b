import { Component, Input, Inject, OnInit, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, Optional } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'base-text',
  templateUrl: './text.component.html',
  styleUrls: ['./text.component.css'],
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class TextComponent implements OnInit {
  @Input() content?: string;
  @Input() className?: string;

  constructor(@Optional() @Inject('componentProperties') private properties: any) {}

  ngOnInit() {
    if (this.properties) {
      this.content = this.properties.content;
      this.className = this.properties.className;
    }
  }
}
