<!-- Enhanced Breadcrumb Component Template -->
<nav
  [class]="computedClasses"
  [attr.aria-label]="'Breadcrumb navigation'"
  role="navigation"
>
  <!-- Modern Breadcrumb Layout -->
  <ol class="flex items-center space-x-2">
    <!-- First Item (Home) -->
    <li *ngIf="visibleItems.length > 0" class="flex items-center">
      <a 
        *ngIf="visibleItems[0].to; else firstSpan"
        [routerLink]="visibleItems[0].to"
        class="flex items-center hover:text-blue-600 transition-colors"
      >
        <i *ngIf="showIcons && visibleItems[0].icon" [class]="visibleItems[0].icon" class="mr-1"></i>
        <span [class.sr-only]="visibleItems[0].hideLabel">{{ visibleItems[0].label }}</span>
      </a>
      <ng-template #firstSpan>
        <span class="flex items-center text-gray-500">
          <i *ngIf="showIcons && visibleItems[0].icon" [class]="visibleItems[0].icon" class="mr-1"></i>
          <span [class.sr-only]="visibleItems[0].hideLabel">{{ visibleItems[0].label }}</span>
        </span>
      </ng-template>
    </li>

    <!-- Collapsed Items Dropdown -->
    <li *ngIf="collapsedItems.length > 0 && showDropdown" class="flex items-center">
      <span class="text-gray-400 mx-2">{{ separator }}</span>
      <base-dropdown variant="default" size="md" [ngClass]="classes?.dropdown">
        <button type="button" class="text-gray-500 hover:text-gray-700 px-2 py-1 rounded">
          <i class="fas fa-ellipsis-h"></i>
        </button>
        <base-dropdown-item
          *ngFor="let item of collapsedItems"
          [routerLink]="item.to"
          class="flex items-center gap-x-1"
        >
          <i *ngIf="showIcons && item.icon" [class]="item.icon" class="mr-1"></i>
          {{ item.label }}
        </base-dropdown-item>
      </base-dropdown>
    </li>

    <!-- Remaining Visible Items -->
    <ng-container *ngFor="let item of visibleItems.slice(1); let i = index">
      <li class="flex items-center">
        <span class="text-gray-400 mx-2">{{ separator }}</span>
        <a 
          *ngIf="item.to; else itemSpan"
          [routerLink]="item.to"
          class="hover:text-blue-600 transition-colors"
          [class.text-gray-500]="i === visibleItems.slice(1).length - 1"
        >
          <i *ngIf="showIcons && item.icon" [class]="item.icon" class="mr-1"></i>
          <span [class.sr-only]="item.hideLabel">{{ item.label }}</span>
        </a>
        <ng-template #itemSpan>
          <span class="text-gray-500 font-medium" [attr.aria-current]="i === visibleItems.slice(1).length - 1 ? 'page' : null">
            <i *ngIf="showIcons && item.icon" [class]="item.icon" class="mr-1"></i>
            <span [class.sr-only]="item.hideLabel">{{ item.label }}</span>
          </span>
        </ng-template>
      </li>
    </ng-container>
  </ol>

  <!-- Legacy Support (Hidden by default, shown when legacy inputs are used) -->
  <div *ngIf="color && color !== 'default'" class="nui-breadcrumb" [ngClass]="[color && colors[color], classes?.wrapper]">
    <ul class="nui-breadcrumb-list" [ngClass]="classes?.list">
      <!-- Mobile dropdown -->
      <li class="nui-breadcrumb-item-mobile">
        <base-dropdown variant="default" size="md" [ngClass]="classes?.dropdown">
          <base-dropdown-item
            *ngFor="let item of computedItems().slice(0, computedItems().length - 1); let index = index"
            [routerLink]="item.to"
            class="flex items-center gap-x-1"
          >
            {{ item.label }}
          </base-dropdown-item>
        </base-dropdown>
      </li>
      
      <!-- Desktop breadcrumb items -->
      <ng-container *ngFor="let item of computedItems(); let index = index">
        <li
          class="nui-breadcrumb-item"
          [ngClass]="[
            index !== (computedItems()?.length ?? 0) - 1 ? 'hidden sm:flex' : 'flex',
            classes?.item ?? ''
          ]"
        >
          <a
            [routerLink]="item.to"
            class="nui-item-inner"
            [ngClass]="[item.to && 'nui-has-link']"
          >
            <base-icon
              *ngIf="item.icon"
              [icon]="item.icon"
              class="nui-item-icon"
              [ngClass]="item.iconClasses"
            ></base-icon>
            <span [ngClass]="[item.hideLabel && 'sr-only']">
              {{ item.label }}
            </span>
          </a>
        </li>
        <li class="nui-breadcrumb-item" *ngIf="index < (computedItems()?.length ?? 0) - 1">
          <div class="nui-item-inner">
            <span class="nui-item-text">
              <ng-content select="[slot='separator']"></ng-content>
            </span>
          </div>
        </li>
      </ng-container>
    </ul>
  </div>
</nav>
