import { Component, Input, Output, EventEmitter, OnInit, ContentChild, ElementRef } from '@angular/core';
import { Ng<PERSON>lass, NgSwitch, NgSwitchCase } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'base-button-action',
  templateUrl: './button-action.component.html',
  styleUrls: ['./button-action.component.css'],
  standalone: true,
  imports: [NgClass, NgSwitch, NgSwitchCase, RouterLink]
})
export class ButtonActionComponent implements OnInit {
  // Standard inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'ghost' | 'outline' = 'primary';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() text: string = 'Action';
  @Input() to?: string;
  @Input() href?: string;
  @Input() rel: string = '';
  @Input() target: string = '';
  @Input() type: 'button' | 'submit' | 'reset' = 'button';
  @Input() loading: boolean = false;
  @Input() disabled: boolean = false;
  @Input() icon?: string;
  @Input() iconPosition: 'left' | 'right' = 'left';
  @Input() fullWidth: boolean = false;

  // Legacy inputs for backward compatibility
  @Input() item_id: string = Math.random().toString(36).substring(7);
  @Input() style?: 'solid' | 'outline' | 'ghost';
  @Input() color?: 'default' | 'default-contrast' | 'primary' | 'info' | 'success' | 'warning' | 'danger' | 'none';

  // Events
  @Output() clicked = new EventEmitter<Event>();

  is: string = 'button';

  constructor(private elementRef: ElementRef) {}

  ngOnInit() {
    this.determineElementType();
  }

  get hasContent(): boolean {
    const element = this.elementRef.nativeElement;
    return element && element.children.length > 0;
  }

  get computedClasses(): string {
    const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed select-none';
    
    const sizeClasses = {
      xs: 'px-2 py-1 text-xs min-h-[24px]',
      sm: 'px-3 py-1.5 text-sm min-h-[32px]',
      md: 'px-4 py-2 text-sm min-h-[40px]',
      lg: 'px-6 py-3 text-base min-h-[48px]',
      xl: 'px-8 py-4 text-lg min-h-[56px]'
    };

    const variantClasses = {
      default: 'bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 border border-gray-200',
      primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm hover:shadow-md',
      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500 shadow-sm hover:shadow-md',
      success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 shadow-sm hover:shadow-md',
      warning: 'bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 shadow-sm hover:shadow-md',
      danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-sm hover:shadow-md',
      ghost: 'bg-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-500',
      outline: 'border border-gray-300 bg-transparent text-gray-700 hover:bg-gray-50 focus:ring-gray-500'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const widthClasses = this.fullWidth ? 'w-full' : '';
    const loadingClasses = this.loading ? 'opacity-75 cursor-wait' : '';

    // Legacy class support
    const legacyClasses = this.getLegacyClasses();

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      widthClasses,
      loadingClasses,
      legacyClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  private getLegacyClasses(): string {
    // Support legacy style and color inputs
    const legacyStyles = {
      solid: 'shadow-sm hover:shadow-md',
      outline: 'border border-current bg-transparent hover:bg-current hover:text-white',
      ghost: 'bg-transparent hover:bg-gray-100'
    };

    const legacyColors = {
      'default': 'text-gray-700',
      'default-contrast': 'text-gray-900',
      'primary': 'text-blue-600',
      'info': 'text-blue-500',
      'success': 'text-green-600',
      'warning': 'text-yellow-600',
      'danger': 'text-red-600',
      'none': ''
    };

    const styleClass = this.style ? legacyStyles[this.style] || '' : '';
    const colorClass = this.color ? legacyColors[this.color] || '' : '';

    return [styleClass, colorClass].filter(Boolean).join(' ');
  }

  private determineElementType() {
    if (this.to) {
      this.is = 'router-link';
    } else if (this.href) {
      this.is = 'a';
    } else {
      this.is = 'button';
    }
  }

  onClick(event: Event): void {
    if (!this.disabled && !this.loading) {
      this.clicked.emit(event);
    }
  }
}
