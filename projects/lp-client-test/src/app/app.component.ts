import { Compo<PERSON>, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { Router, ActivationStart, RouterOutlet, RouterModule } from '@angular/router';
import { Platform, IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';

import { LocationStrategy } from '@angular/common';

import { LoadingController, MenuController } from '@ionic/angular';
import {
  AbstractService,
  KeyCloakService,
  LogService,
  LssConfig,
  MemberProfile,
  MemberService,
} from 'lp-client-api';
import { App, URLOpenListenerEvent } from '@capacitor/app';
import { environment } from '../environments/environment';
import { Preferences } from '@capacitor/preferences';

interface NavItem {
  path: string;
  icon: string;
  label: string;
  exact: boolean;
}

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule, RouterModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class AppComponent implements OnInit, OnDestroy {
  @ViewChild(RouterOutlet)
  outlet: RouterOutlet = new RouterOutlet();

  private routerSubscription: Subscription = new Subscription();

  navItems: NavItem[] = [];
  count?: any = 0;
  loading?: HTMLIonLoadingElement;
  slowLoad = false;
  environment = environment;

  _currentMenu = { icon: '', label: '', path: '' };

  mainNavItems: NavItem[] = [];
  moreNavItems: NavItem[] = [];

  isMoreMenuOpen = false;

  constructor(
    private menu: MenuController,
    private kc: KeyCloakService,
    protected readonly router: Router,
    private memberService: MemberService,
    private logService: LogService,
    private platform: Platform,
    private loadingCtrl: LoadingController,
    public lssConfig: LssConfig,
    private LocationStrategy: LocationStrategy
  ) {
    console.log('[AppComponent] Constructor called');
    console.log('[AppComponent] LssConfig:', this.lssConfig);
    this.platform.ready().then(() => {
      if (this.platform.is('hybrid')) {
        App.addListener('appStateChange', ({ isActive }) => {
          console.log('App state changed. Is active?', isActive);
          if (isActive && this.kc.authSuccess) {
            if (this.kc.keycloak?.tokenParsed) {
              if (this.kc.keycloak.tokenParsed.exp) {
                if (
                  this.kc.keycloak.tokenParsed.exp <
                  new Date().getTime() / 1000
                ) {
                  this.kc.authed().then((authed: boolean) => {
                    if (!authed) {
                      console.log('Go to login screen');
                    }
                  });
                }
              }
            }
          }
        });
        App.addListener('appUrlOpen', (data) => {
          console.log('App opened with URL:', data);
        });
      }
    });
  }

  ngOnInit() {
    console.log('[AppComponent] ngOnInit called');
    this.deactivateOutletOnRouteChange();
    this.generateMenu();
    this.kc.authStatus.subscribe((data) => {
      console.log('this.kc.authStatus', data);
      if (data != null && data.eventName === 'init') {
        this.loadingCtrl
          .create({
            message: 'Loading Authentication',
            duration: 10000,
          })
          .then((data) => {
            console.log('Loading created');
            if (!this.slowLoad) {
              this.loading = data;
              this.loading.present();
            } else {
              this.slowLoad = false;
            }
          });
      } else {
        this.slowLoad = true;
        if (this.loading) {
          this.loading.dismiss();
        }
      }
      if (data != null) {
        if (data.eventName === 'login') {
          if (this.kc.keycloak) {
            Preferences.set({
              key: 'login',
              value: JSON.stringify({
                refreshToken: this.kc.keycloak.refreshToken,
                idToken: this.kc.keycloak.idToken,
                token: this.kc.keycloak.token,
              }),
            }).then(() => {
              console.log('Token Login');
            });
          }
          this.getNotificationCount();
          this.memberService
            .getProfile()
            .subscribe((profile: MemberProfile) => {
              // Handle post-login navigation for various public pages
              const currentUrl = this.router.url;
              console.log('Post-login navigation check. Current URL:', currentUrl);

              if (currentUrl === '/public/signup' ||
                  currentUrl === '/public/login' ||
                  currentUrl === '/public/home' ||
                  currentUrl === '/' ||
                  currentUrl.startsWith('/public/login')) {
                console.log('Redirecting authenticated user to dashboard');
                this.router.navigate(['/secure/dashboard']);
              }
            });

          this.generateMenu();
        } else if (data.eventName === 'logout') {
          Preferences.remove({ key: 'login' }).then(() => {
            console.log('Token Logout');
          });
        }
      }
    });
    this.generateMenu();
  }

  deactivateOutletOnRouteChange(): void {
    this.routerSubscription = this.router.events.subscribe((e) => {
      if (e instanceof ActivationStart && e.snapshot.outlet === 'primary') {
        if (this.outlet && this.outlet.isActivated) {
          this.outlet.deactivate();
        }
      }
    });
  }

  ngOnDestroy(): void {
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
    this.kc.authStatus.unsubscribe();
  }

  generateMenu(): void {
    // console.log('========== DEBUGGING NAVIGATION CONFIGURATION ==========');
    // console.log('1. Raw environment object:', this.environment);
    // console.log('2. lssConfig from environment:', this.environment.lssConfig);
    // console.log('3. Navigation from environment:', this.environment.lssConfig?.navigation);

    // console.log('4. LssConfig service object:', this.lssConfig);
    // console.log('5. Navigation from LssConfig:', this.lssConfig?.navigation);

    // Use the navigation data directly from the environment object
    // since we know it has the correct configuration
    const nav = this.environment.lssConfig?.navigation || this.lssConfig?.navigation;
    const routes = nav?.routes || [];

    // console.log('6. Routes array:', routes);

    // Map the route properties to the format expected by the template
    this.mainNavItems = routes.filter((rt: any) => rt.main).map((route: any) => ({
      path: route.path,
      icon: route.icon,
      label: route.label,
      exact: route.exact || route.path === '/' || route.path === '/public/home'
    }));

    this.navItems = routes.filter((rt: any) => rt.sidebar).map((route: any) => ({
      path: route.path,
      icon: route.icon,
      label: route.label,
      exact: route.exact || route.path === '/' || route.path === '/public/home'
    }));

    console.log('7. Filtered navItems:', this.navItems);

    this.moreNavItems = routes.filter((rt: any) => rt.more).map((route: any) => ({
      path: route.path,
      icon: route.icon,
      label: route.label,
      exact: route.exact || route.path === '/' || route.path === '/public/home'
    }));

    // console.log('8. Filtered mainNavItems:', this.mainNavItems);
    // console.log('9. Filtered moreNavItems:', this.moreNavItems);
    // console.log('========== END DEBUGGING ==========');
  }

  openMenu() {
    this.menu.open('end');
  }

  get loggedin(): boolean | undefined {
    return this.kc.authSuccess;
  }

  get pageTitle(): string {
    let url = this.router.url;
    if (url === '/') {
      url = 'pages/home';
    } else {
      url = url.substring(1);
    }
    if (this._currentMenu == null || url !== this._currentMenu.path) {
      this._currentMenu = this.navItems.filter(
        (filter) => url === filter.path
      )[0];
    }
    return url;
  }

  get pageText(): string {
    let url = this.router.url;
    let text: any = '';
    if (url === '/public/storedetail') return 'Store Detail';
    if (url == '/secure/security') return 'Security';
    if (url == '/secure/profile') return 'Profile';

    text = this.navItems.filter((filter) => url === filter.path)[0];
    return text ? text.label : 'Home';
  }

  close(menuItem: any) {
    this.menu.close();
    this._currentMenu = menuItem;
  }
  back(): void {
    if (this.LocationStrategy.historyGo) this.LocationStrategy.historyGo(-1);
  }
  login() {
    if (!this.kc.authSuccess) {
      this.kc.keycloak?.login().then((data) => {
        console.log(data);
      });
    }
  }

  logout() {
    if (this.kc.authSuccess) {
      this.kc.keycloak?.logout();
    }
  }

  getNotificationCount() {
    // Use .then() to check the resolved value of the promise
    this.kc.authed().then(isAuthed => {
      if (isAuthed) {
        this.memberService
          .getNotificationsCount()
          .subscribe({
            error: (error) => {
              console.log(error.message);
            },
            next: (body: any) => {
              console.log('body', body.count);
              if (body !== undefined) {
                this.count = body.count;
              }
            },
          });
      }
    });
  }

  get showMenuBack(): boolean | undefined {
    return this.router.url.includes('/public/landing') ? false : true;
  }

  toggleMoreMenu(): void {
    this.isMoreMenuOpen = !this.isMoreMenuOpen;
  }

  // Navigation animation methods
  addClickAnimation(event: Event): void {
    const target = event.currentTarget as HTMLElement;
    target.classList.add('clicked');
    setTimeout(() => {
      target.classList.remove('clicked');
    }, 200);
  }

  updateIndicator(index: number): void {
    const indicator = document.querySelector('.nav-indicator') as HTMLElement;
    if (indicator) {
      const navItems = document.querySelectorAll('.nav-item');
      const activeItem = navItems[index] as HTMLElement;
      if (activeItem) {
        const itemRect = activeItem.getBoundingClientRect();
        const navRect = activeItem.parentElement?.getBoundingClientRect();
        if (navRect) {
          const left = itemRect.left - navRect.left + (itemRect.width / 2) - 20;
          indicator.style.transform = `translateX(${left}px)`;
        }
      }
    }
  }
}
