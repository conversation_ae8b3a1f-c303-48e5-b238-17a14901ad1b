<ion-app id="app" class="ion-page app-background bg-app">
  <ion-content id="app-content" class="app-background bg-app ion-padding">
    <ion-router-outlet></ion-router-outlet>
  </ion-content>
  <!-- Bottom Navigation Bar -->
  <div class="fixed right-0 bottom-0 left-0 bottom-nav">
    <nav class="flex justify-around relative">
      <!-- Floating Indicator -->
      <div class="nav-indicator"></div>

      <!-- Main Nav Items -->
      <a
        *ngFor="let item of mainNavItems; let i = index"
        [routerLink]="item.path"
        [routerLinkActiveOptions]="{ exact: item.exact }"
        routerLinkActive="active"
        #rla="routerLinkActive"
        (click)="addClickAnimation($event); updateIndicator(i)"
        class="text-white nav-item"
        [attr.data-index]="i"
      >
        <div class="transition-all">
          <ion-icon [name]="item.icon"></ion-icon>
        </div>
        <span>{{ item.label }}</span>
      </a>

      <!-- More Menu Button -->
      <button
        (click)="toggleMoreMenu()"
        class="text-white nav-item"
        [attr.data-index]="mainNavItems.length"
      >
        <div class="transition-all">
          <ion-icon name="ellipsis-horizontal"></ion-icon>
        </div>
        <span>More</span>
      </button>

      <!-- More Menu Popup -->
      <div
        *ngIf="isMoreMenuOpen"
        class="
          absolute
          bottom-full
          right-0
          mb-2
          w-48
          bg-gray-800
          rounded-lg
          shadow-lg
          overflow-hidden
          z-50
        "
      >
        <a
          *ngFor="let item of moreNavItems"
          [routerLink]="item.path"
          routerLinkActive="active"
          #rla="routerLinkActive"
          (click)="isMoreMenuOpen = false"
          class="
            flex
            items-center
            px-4
            py-3
            text-gray-200
            hover:bg-gray-700
            transition-colors
            duration-200
          "
        >
          <ion-icon
            [name]="item.icon"
            [ngClass]="{ 'text-blue-400': rla.isActive }"
            class="mr-3"
          ></ion-icon>
          <span [ngClass]="{ 'text-blue-400': rla.isActive }">{{
            item.label
          }}</span>
        </a>
      </div>
    </nav>
  </div>
</ion-app>
