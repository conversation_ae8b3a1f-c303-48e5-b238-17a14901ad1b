/* Bottom navigation styling matching lp-client */
:host {
  router-outlet + .bottom-nav {
    display: block;
  }
}

/* Bottom navigation bar */
.bottom-nav {
  backdrop-filter: blur(10px);
  background: linear-gradient(to right, #0a2463, #1e88e5, #0a2463) !important;
  border-top: none;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  transition: transform 0.3s ease-in-out;
  padding: 8px 0;
  height: 80px;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

/* Background glow effect */
.bottom-nav::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle at center,
    rgba(33, 150, 243, 0.3) 0%,
    rgba(33, 150, 243, 0) 70%
  );
  animation: rotate 15s linear infinite;
  z-index: -1;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Navigation items */
.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  text-decoration: none;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  min-width: 60px;
  border: none;
  background: none;
  cursor: pointer;
}

.nav-item:hover {
  color: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
}

.nav-item.active {
  color: #ffffff;
  transform: translateY(-4px);
}

.nav-item.active::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: #ffffff;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

.nav-item ion-icon {
  font-size: 24px;
  margin-bottom: 4px;
  transition: all 0.3s ease;
}

.nav-item.active ion-icon {
  transform: scale(1.1);
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.5));
}

.nav-item span {
  font-size: 11px;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

/* Click animation */
.nav-item.clicked {
  transform: scale(0.95) translateY(-2px);
}

/* Floating indicator */
.nav-indicator {
  position: absolute;
  bottom: -2px;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #64b5f6, #2196f3, #1976d2);
  border-radius: 2px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 10px rgba(33, 150, 243, 0.6);
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .bottom-nav {
    height: 70px;
    padding: 6px 0;
  }

  .nav-item {
    padding: 6px 8px;
    min-width: 50px;
  }

  .nav-item ion-icon {
    font-size: 20px;
  }

  .nav-item span {
    font-size: 10px;
  }
}

/* Hide navigation on certain pages */
.hide-nav .bottom-nav {
  transform: translateY(100%);
}