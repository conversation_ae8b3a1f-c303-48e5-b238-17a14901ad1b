import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { forkJoin, firstValueFrom } from 'rxjs';
import { App, AppInfo } from '@capacitor/app';
import { GetResult, Preferences } from '@capacitor/preferences';
import {
  KeyCloakService,
  LssConfig,
} from 'lp-client-api';
import { deepmerge } from 'deepmerge-ts';
import { Device, DeviceInfo } from '@capacitor/device';
import { Platform } from '@ionic/angular';

interface ConfigResponse {
  version: number;
  deviceDetails: {
    appVersion: string;
    appId: string;
    confVersion: number;
    operatingSystem: string;
    osVersion: string;
    platform: string;
    webViewVersion: string;
    deviceId: string;
    languageCode: string;
    env: string;
  };
  [key: string]: any;
}

/**
 *
 * This is an application specific service so it will not reside in the normal API project.
 */
@Injectable({
  providedIn: 'root',
})
export class ConfigService {
  private config: LssConfig;
  private _client: string = '';
  private _env: string = '';
  private _realm: string = '';
  private _isProd: boolean = false;
  // Construct URL dynamically from environment
  private _configUrl: string;
  private _loadMoreDetails: boolean = false;

  constructor(
    private httpClient: HttpClient,
    private keycloak: KeyCloakService,
    @Inject('environment') private env: any,
    private platform: Platform
  ) {
    console.log('------- ConfigService CONSTRUCTOR v0.7 -------');
    console.log('[ConfigService Constructor] Raw injected environment:', this.env);
    if (this.env && this.env.lssConfig) {
      console.log('[ConfigService Constructor] lssConfig from injected env:', JSON.stringify(this.env.lssConfig));
      if (this.env.lssConfig.games) {
        console.log('[ConfigService Constructor] Games config from injected env.lssConfig.games:', JSON.stringify(this.env.lssConfig.games));
      } else {
        console.warn('[ConfigService Constructor] No games config (this.env.lssConfig.games) in injected lssConfig.');
      }
    } else {
       console.warn('[ConfigService Constructor] No lssConfig (this.env.lssConfig) found in injected environment.');
    }
    this.config = this.env.lssConfig as LssConfig;
    this._configUrl = `https://connect-dev.loyaltyplus.aero/public/loyaltyapi/1.0.0/mobile/config`;
  }

  get sysConfig(): LssConfig {
    return this.config;
  }

  loadConfig() {
    const hostname = window.location.hostname;
    const isLocalhost = hostname === 'localhost' || hostname === '127.0.0.1';

    if (isLocalhost) {
      console.log('[ConfigService] Running on localhost. Using local environment config only.');
      // Use a deep copy to avoid modifying the original environment object
      this.config = JSON.parse(JSON.stringify(this.env.lssConfig));
      console.log('[ConfigService] Local config loaded:', this.config);

      // Initialize Keycloak with local config
      return this.setAuthConfig().then(() => {
        console.log('[ConfigService] Local config initialization complete');
        return this.config;
      }).catch((error) => {
        console.error('[ConfigService] Error initializing local config:', error);
        // Don't throw error for localhost - continue with local config
        return this.config;
      });
    } else {
      console.log('[ConfigService] Running on non-localhost. Fetching remote config.');
      return forkJoin([this.loadConfigInternal()]);
    }
  }

  get isMobile(): boolean{
    let mobile = false;
    if(this.platform.is('pwa') || this.platform.is('mobileweb')|| this.platform.is('desktop')) {
      mobile = false;
    } else {
      mobile = true;
    }
    return mobile;
  } 
  private async getAppVersion() {
    try {
      // Check if running on web platform
      if (!this.isMobile) {
        // Running in browser, don't try to use native App plugin
        return {
          version: this.config.appVersion || '0.0.1',
          id: this.config.appCode ? this.config.appCode : 'DEFAULT',
        };
      }

      const appInfo = await App.getInfo();
      if (appInfo && appInfo.version) {
        return appInfo;
      }
    } catch (e: any) {
      console.error('Error getting app version:', e);
      // Suppress the error message in the console for web platform
      if (e && e.toString && e.toString().includes('Not implemented on web')) {
        console.log('App version check skipped for web platform');
      }
    }
    return {
      version: this.config.appVersion || '0.0.1',
      id: this.config.appCode ? this.config.appCode : 'DEFAULT',
    };
  }

  private async loadConfigInternal() {
    // Explicitly capture the games config from the injected environment at the start.
    const localEnvLssConfig = this.env.lssConfig as LssConfig;
    const definitiveGamesConfig = localEnvLssConfig?.games;

    if (definitiveGamesConfig) {
        console.log('[ConfigService loadConfigInternal] Definitive games config from local env at start:', JSON.stringify(definitiveGamesConfig));
    } else {
        console.warn('[ConfigService loadConfigInternal] No definitive games config from local env at start (this.env.lssConfig.games was falsy).');
    }

    try {
      const [storredConfig, info, deviceInfo, lang, deviceId] =
        await Promise.all([
          Preferences.get({ key: 'config' }),
          this.getAppVersion(),
          Device.getInfo(),
          Device.getLanguageCode(),
          Device.getId(),
        ]);

      this._isProd = info.version.endsWith('0');
      let configLoad = this.buildConfigToSend(
        storredConfig,
        deviceInfo,
        info,
        deviceId.identifier,
        lang.value
      );

      const response = await firstValueFrom<HttpResponse<ConfigResponse>>(
        this.httpClient.post<ConfigResponse>(
          this._configUrl,
          configLoad.deviceDetails,
          {
            observe: 'response',
          }
        )
      );

      if (response.status === 200 && response.body) {
        if (this._loadMoreDetails){
          const webLoadConfig = response.body[0];
          const webClientDetails = response.body[1];
          webLoadConfig.deviceDetails = configLoad.deviceDetails;
          webLoadConfig.deviceDetails.confVersion = response.body.version;
          configLoad = webLoadConfig;
          this._realm = webClientDetails.realm;
          this._env = webClientDetails.env;
          this._client = webClientDetails.client;
          this.setHostname(window.location.hostname);
        }else{
          response.body.deviceDetails = configLoad.deviceDetails;
          response.body.deviceDetails.confVersion = response.body.version;
          configLoad = response.body;
        }
        await Preferences.set({
          key: 'config',
          value: JSON.stringify(configLoad),
        });
      }

      if (configLoad.version !== 0) {
        // Preserve navigation, pages, GAMES, and icon configuration from environment
        const originalNavigation = this.config.navigation;
        const environmentPages = this.config.pages; // Store environment pages
        const environmentGames = this.config.games; // Store environment games
        const environmentIcon = (this.config as any).icon; // Store environment icon

        // Remove pages from both configs before merge to prevent conflicts
        // if ('pages' in configLoad) {
        //   delete configLoad.pages;
        // }
        // if ('pages' in this.config) {
        //   delete this.config.pages;
        // }

        // override the config settings we need (excluding pages).
        this.config = deepmerge(configLoad, this.config) as LssConfig;

        // Restore navigation and pages from environment
        this.config.navigation = originalNavigation;
        this.config.pages = environmentPages;
        this.config.games = environmentGames; // Restore environment games
        (this.config as any).icon = environmentIcon; // Restore icon
      }

      // Update Keycloak configuration if necessary
      await this.setAuthConfig();
      return this.config;
    } catch (error) {
      console.error('Error loading config:', error);
      throw error;
    }
  }

  private async setAuthConfig() {
    console.log('[ConfigService] Entering setAuthConfig. Checking this.config.authConfig:', this.config?.authConfig); 
    if (this.config.authConfig) {
      console.log('[ConfigService] authConfig found. Checking initOptions:', this.config.authConfig.initOptions); 
      const initOptions = this.config.authConfig.initOptions;
      if (initOptions) {
        console.log('[ConfigService] initOptions found. Entering try block.'); 
        try {
          console.log('[ConfigService] Attempting to load tokens from Preferences...'); 
          const data = await Preferences.get({ key: 'login' });
          console.log('[ConfigService] Preferences.get result:', data); 
          if (data.value) {
            console.log('[ConfigService] Found stored tokens. Parsing...'); 
            const tokenDetails = JSON.parse(data.value);
            initOptions.token = tokenDetails.token;
            initOptions.refreshToken = tokenDetails.refreshToken;
            console.log('[ConfigService] Tokens applied to initOptions.'); 
          } else {
             console.log('[ConfigService] No stored tokens found in Preferences.'); 
          }
          // Log before init
          console.log('[ConfigService] Final authConfig before keycloak.init():', JSON.stringify(this.config.authConfig)); 
          console.log('Keycloak init : ', this.config.authConfig); 
          // THE CALL
          await this.keycloak.init({ 
            config: {
              url: this.config.authConfig.url,
              realm: this.config.authConfig.realm,
              clientId: this.config.authConfig.clientId,
            },
            initOptions,
          });
          console.log('[ConfigService] keycloak.init() apparently succeeded.'); 
        } catch (error) { // Catch block for Preferences or keycloak.init() failure
          console.error('[ConfigService] Error within setAuthConfig try block:', error); 
          throw error; // Rethrows the error
        }
      } else {
         console.log('[ConfigService] initOptions missing or falsy. keycloak.init() will not be called.'); 
      }
    } else {
       console.log('[ConfigService] this.config.authConfig missing or falsy. keycloak.init() will not be called.'); 
    }
  }

  private setClientEnv(host: string) {
    console.log('Host: ', host);
    this._env = host.split('.')[0];
    if (this._env.endsWith('prep')) {
      this._env = 'prep';
    } else if (this._env.endsWith('qa')) {
      this._env = 'qa';
    } else if (this._env.endsWith('dev')) {
      this._env = 'dev';
    } else {
      this._env = '';
    }
  }
  private cleanClient() {
    if (this._client === 'mica') {
      this._client = 'rmic';
    }
  }
  private setClient(host: string) {
    console.log('Host set client: ', host);
    this._client = host.split('.')[0].replace(this._env, '');
    this.cleanClient();
    if (this._client === 'rmic') {
      this._realm = 'Mica';
    } else if (this._client === 'ffz1') {
      this._realm = 'DemoZ1';
    } else if (this._client === 'ffz1dev') {
      this._realm = 'DemoZ1';
    } else if (this._client === 'ffz1qa') {
      this._realm = 'DemoZ1';
    }
  }
  private setClientForDevice(appId: string) {
    let idSplit = appId.split('.');
    if (this.env.client) {
      this._client = this.env.client;
    } else {
      this._client = idSplit[idSplit.length - 1];
    }
    this.cleanClient();
  }
  private setHostname(hostnameParm: string) {
    console.log('This is on a server : ', this.config.hostName);
    this.setClientEnv(hostnameParm);
    this.setClient(hostnameParm);
    let hostname: string = hostnameParm || 'unknown';
    this.config.appBaseUrl = this.config.appBaseUrl?.replace(
      '{hostname}',
      hostname
    );
    this.config.apiBaseUrl = this.config.apiBaseUrl?.replace(
      '{hostname}',
      hostname
    );
    this.config.identityBaseUrl = this.config.apiBaseUrl?.replace(
      '{identityBaseUrl}',
      hostname
    );
    if (this.config.authConfig) {
      this.config.authConfig.realm = this._realm;
      this.config.authConfig.url = this.config.authConfig.url?.replace(
        '{env}',
        this._env
      );
      this.config.authConfig.issuer = this.config.authConfig.issuer
        ?.replace('{env}', this._env)
        .replace('{client}', this._realm);
      this.config.authConfig.realm = this.config.authConfig.realm?.replace(
        '{client}',
        this._realm
      );
    }
  }
  /*
  private doWebWork(): boolean {
    if (this.config.hostName) {
      this.setHostname(this.config.hostName);
      return this._env.length == 0;
    }
    return false;
  }*/

  private buildConfigToSend(
    storredConfig: GetResult,
    deviceInfo: DeviceInfo,
    info: AppInfo | { version: string; id: string },
    identifier: string,
    lang: string
  ) {
    let configLoad = storredConfig.value
      ? JSON.parse(storredConfig.value)
      : { version: 0 };
    if (!this.isMobile) {
      this.config.hostName = window.location.href.split('/')[2];
      //localhost must run a client specific config
      if (!this.config.hostName.startsWith('localhost')) {
        //this._isProd = this.doWebWork();
       this._configUrl = `/public/loyaltyapi/1.0.0/mobile/config`;
       this._loadMoreDetails = true;
      } else {
        this._isProd = false;
        if (this.env.client) {
          this._env = this.env.env;
          this.setClient(this.env.client);
        } else {
          //default if no client specified in the config and this is localhost.
          this.setHostname('rmicdev.loyaltyplus.aero');
        }
      }
    } else {
      //this needs to change for the app.
      this.config.hostName = window.location.href.split('/')[2];
      this._env = this.env.env;
      this.setClientForDevice(info.id);
      this._isProd = this.env.production;
      //let configLoad: any = {"version":0};
      if (!this._isProd) {
        //this is not a live app connect to dev.
        this._configUrl = `https://connect-dev.loyaltyplus.aero/public/loyaltyapi/1.0.0/mobile/config`;
      }
    }


    console.log("+++++CONFIG URL+++++", this._configUrl);
    configLoad.deviceDetails = {
      appVersion: info.version,
      appId: this._client,
      confVersion: configLoad.version,
      operatingSystem: deviceInfo.operatingSystem,
      osVersion: deviceInfo.osVersion,
      platform: deviceInfo.platform,
      webViewVersion: deviceInfo.webViewVersion,
      deviceId: identifier,
      languageCode: lang,
      env: this._env,
    };
    console.log("+++++CONFIG LOAD+++++", configLoad);
    return configLoad;
  }
}
