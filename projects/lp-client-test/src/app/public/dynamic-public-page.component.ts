import { Component, Injector, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ConfigService } from '../services/config.service';
import { DynamicComponentLoaderService } from '../shared/dynamic-component-loader.service';
import { PageConfigurationValidationService } from '../shared/page-configuration-validation.service';
import { CommonModule } from '@angular/common';
import { ComponentsModule } from '@projects/mobile-components';
import { KeyCloakService, MemberService } from 'lp-client-api';
import { BaseDynamicPageComponent } from '../shared/base-dynamic-page.component';

@Component({
  selector: 'app-dynamic-public-page',
  template: `<div [ngClass]="pageClass" class="dynamic-components-container">
    <ng-template #container></ng-template>
  </div>`,
  styles: [`
    .dynamic-components-container {
      display: flex;
      flex-direction: column;
      gap: 0; /* Remove gaps between flex items */
    }

    /* This ensures components are rendered without default margins */
    .dynamic-components-container > * {
      margin: 0;
    }
  `],
  standalone: true,
  imports: [CommonModule, ComponentsModule]
})
export class DynamicPublicPageComponent extends BaseDynamicPageComponent {
  protected override debug = true; // Enable debug for troubleshooting

  constructor(
    route: ActivatedRoute,
    router: Router,
    loader: DynamicComponentLoaderService,
    injector: Injector,
    configService: ConfigService,
    cdr: ChangeDetectorRef,
    memberService: MemberService,
    kc: KeyCloakService,
    pageValidator: PageConfigurationValidationService
  ) {
    super(route, router, loader, injector, configService, cdr, memberService, kc, pageValidator);
  }

  // Implement abstract methods from base class
  protected getComponentName(): string {
    return 'DynamicPublicPage';
  }

  protected getDefaultPage(): string {
    return 'landing';
  }

  // Override page security check for public pages
  protected override checkPageSecurity(pageConfig: any): boolean {
    // Public pages can handle both secure and non-secure pages
    if (pageConfig.secure && !this.isAuthenticated) {
      if (this.debug) console.log(`[${this.getComponentName()}] Secure page requested, but user not authenticated. Redirecting to login.`);
      this.router.navigate(['/public/login']);
      return false; // Stop loading components
    }
    return true;
  }

  // Override page not found handling for public pages
  protected override handlePageNotFound(pageId: string): void {
    console.warn(`[${this.getComponentName()}] No configuration found for pageId: ${pageId}. Redirecting to landing.`);
    this.router.navigate(['/public/landing']);
  }

  // Override post-login navigation for public pages
  protected override handlePostLoginNavigation(): void {
    const currentUrl = this.router.url;
    console.log(`[${this.getComponentName()}] Post-login navigation check. Current URL:`, currentUrl);

    // If user is on login, signup, landing, or home pages after authentication, redirect to dashboard
    if (currentUrl === '/public/login' ||
        currentUrl === '/public/signup' ||
        currentUrl === '/public/landing' ||
        currentUrl === '/public/home' ||
        currentUrl === '/' ||
        currentUrl.startsWith('/public/login')) {
      console.log(`[${this.getComponentName()}] Redirecting authenticated user from ${currentUrl} to dashboard`);
      this.router.navigate(['/secure/dashboard']);
    }
  }
}

