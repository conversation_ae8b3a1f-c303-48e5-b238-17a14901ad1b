import { <PERSON>mpo<PERSON>, Injector, On<PERSON>nit, On<PERSON><PERSON>roy, ViewChild, ViewContainerRef, ChangeDetectorRef, Directive } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ComponentsModule } from '@projects/mobile-components';
import { KeyCloakService, MemberProfile, MemberService, LssConfig } from 'lp-client-api';
import { ConfigService } from '../services/config.service';
import { DynamicComponentLoaderService } from './dynamic-component-loader.service';
import { PageConfigurationValidationService } from './page-configuration-validation.service';
import { Subscription } from 'rxjs';
import { PageConfiguration, ValidationOptions } from '../../environments/page-configuration.types';

// Define interface for component configuration
export interface ComponentConfig {
  type: string;
  showWhen?: 'authenticated' | 'anonymous' | 'always';
  inputs?: { [key: string]: any };
  config?: { [key: string]: any }; // Support legacy config property
}

@Directive()
export abstract class BaseDynamicPageComponent implements OnInit, OnDestroy {
  @ViewChild('container', { read: ViewContainerRef, static: true }) container!: ViewContainerRef;
  pageClass: string = '';

  // Initialize profile with default empty values to prevent null reference errors
  profile: any = {
    givenNames: '',
    surname: '',
    email: '',
    mobileNumber: '',
    newMembershipNumber: '',
    currentBalance: 0,
    membershipStatus: '',
    avatarUrl: ''
  };

  protected isInitialLoad = true;
  protected debug = false; // Can be overridden by subclasses
  public isAuthenticated = false;
  private authSubscription: Subscription | null = null;
  private profileSubscription: Subscription | null = null;

  constructor(
    protected route: ActivatedRoute,
    protected router: Router,
    public loader: DynamicComponentLoaderService,
    protected injector: Injector,
    protected configService: ConfigService,
    protected cdr: ChangeDetectorRef,
    protected memberService: MemberService,
    public kc: KeyCloakService,
    protected pageValidator: PageConfigurationValidationService
  ) {
    // Initial setting - but will be updated via subscription
    this.isAuthenticated = this.kc.authSuccess;
    console.log(`[${this.getComponentName()}] CONSTRUCTOR - Initial isAuthenticated: ${this.isAuthenticated}`);
  }

  ngOnInit(): void {
    // Subscribe to authentication status changes
    this.authSubscription = this.kc.authStatus.subscribe(status => {
      const newAuthState = !!status.eventData;
      console.log(`[${this.getComponentName()}] Auth status event: ${status.eventName}, value: ${newAuthState}`);

      if (this.isAuthenticated !== newAuthState) {
        console.log(`[${this.getComponentName()}] Auth status changed from ${this.isAuthenticated} to ${newAuthState}`);
        this.isAuthenticated = newAuthState;

        // If logged in, load profile data and handle post-login navigation
        if (newAuthState) {
          console.log(`[${this.getComponentName()}] User authenticated, loading profile data...`);
          this.memberService.getProfile().subscribe({
            next: (profile) => {
              console.log(`[${this.getComponentName()}] Profile loaded successfully:`, profile);
              // Profile will be updated through profileSubject subscription

              // Handle post-login navigation
              this.handlePostLoginNavigation();
            },
            error: (err) => {
              console.error(`[${this.getComponentName()}] Error loading profile:`, err);
            }
          });
        } else {
          // Clear profile on logout
          this.profile = {
            givenNames: '',
            surname: '',
            email: '',
            mobileNumber: '',
            newMembershipNumber: '',
            currentBalance: 0,
            membershipStatus: '',
            avatarUrl: ''
          };
          console.log(`[${this.getComponentName()}] User logged out, profile cleared`);

          // Handle logout navigation - can be overridden by subclasses
          this.handleLogoutNavigation();
        }

        // Reload components when auth status changes
        this.loadPageComponents();
      }
    });

    // Subscribe to profile updates
    this.profileSubscription = this.memberService.profileSubject.subscribe({
      next: (profile) => {
        console.log(`[${this.getComponentName()}] Received profile update:`, profile);
        if (profile) {
          // Merge the profile data with our pre-initialized profile to ensure all fields exist
          this.profile = { ...this.profile, ...profile };
          console.log(`[${this.getComponentName()}] Profile updated:`, this.profile);
          // Re-evaluate components now that the profile has changed
          this.loadPageComponents();
        }
      },
      error: (err) => console.error(`[${this.getComponentName()}] Error receiving profile update:`, err)
    });

    // Debug log initial state
    console.log(`[${this.getComponentName()}] INIT - Auth status: ${this.isAuthenticated}, kc.authSuccess: ${this.kc.authSuccess}`);

    // Register all known components
    this.registerAllComponents();

    // Handle authentication check and initial load
    this.handleInitialLoad();
  }

  // Abstract method to get component name for logging
  protected abstract getComponentName(): string;

  // Abstract method to get default page when no pageId is provided
  protected abstract getDefaultPage(): string;

  // Method to handle logout navigation - can be overridden by subclasses
  protected handleLogoutNavigation(): void {
    // Default implementation - can be overridden
    console.log(`[${this.getComponentName()}] Default logout navigation`);
  }

  // Method to handle post-login navigation - can be overridden by subclasses
  protected handlePostLoginNavigation(): void {
    // Default implementation - can be overridden by subclasses
    console.log(`[${this.getComponentName()}] Default post-login navigation`);
  }

  // Method to handle initial load - can be overridden by subclasses
  protected handleInitialLoad(): void {
    // Check authentication and handle initial load
    this.isInitialLoad = false;
    if (this.kc.authSuccess) {
      if (this.debug) console.log(`[${this.getComponentName()}] User already authenticated, retrieving profile...`);
      this.memberService
        .getProfile()
        .subscribe((profile: MemberProfile) => {
          console.log(`[${this.getComponentName()}] Profile retrieved on initial load:`, profile);
        });
    } else {
      // Load components on first load
      this.loadPageComponents();
    }
  }

  // Register all known components with the dynamic loader service
  protected registerAllComponents(): void {
    try {
      if (this.debug) console.log(`[${this.getComponentName()}] Waiting for ComponentRegistry auto-registration to complete`);

      // Check if ComponentRegistry has already auto-registered components
      this.waitForAutoRegistration().then(() => {
        if (this.debug) {
          console.log(`[${this.getComponentName()}] ComponentRegistry auto-registration complete`);
          this.loader.logRegisteredComponents();
        }

        // After auto-registration is complete, load the page components
        this.loadPageComponents();
      }).catch(error => {
        console.error(`[${this.getComponentName()}] Error waiting for auto-registration:`, error);
        // Fallback: try to load page components anyway
        this.loadPageComponents();
      });
    } catch (error) {
      console.error(`[${this.getComponentName()}] Error in registerAllComponents:`, error);
      // Fallback: try to load page components anyway
      this.loadPageComponents();
    }
  }

  // Wait for ComponentRegistry auto-registration to complete
  private async waitForAutoRegistration(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Check if components are already registered
      const registeredComponents = this.loader.componentRegistryService.getComponentNames();
      if (registeredComponents.length > 0) {
        if (this.debug) console.log(`[${this.getComponentName()}] Auto-registration already complete: ${registeredComponents.length} components`);
        resolve();
        return;
      }

      // Wait for registration changes with timeout
      const timeout = setTimeout(() => {
        console.warn(`[${this.getComponentName()}] Auto-registration timeout - proceeding anyway`);
        resolve();
      }, 5000); // 5 second timeout

      // Subscribe to registration changes
      const subscription = this.loader.componentRegistryService.registrations$.subscribe(registrations => {
        if (registrations.length > 0) {
          if (this.debug) console.log(`[${this.getComponentName()}] Auto-registration detected: ${registrations.length} components`);
          clearTimeout(timeout);
          subscription.unsubscribe();
          resolve();
        }
      });
    });
  }

  protected loadPageComponents(): void {
    console.log(`[${this.getComponentName()}] loadPageComponents called. Auth Status:`, this.isAuthenticated);
    this.route.paramMap.subscribe(params => {
      let pageId = params.get('page');
      const subpage = params.get('subpage');
      const subsubpage = params.get('subsubpage');

      // Handle nested paths like 'games/home', 'app/stores', etc.
      if (pageId && subpage && subsubpage) {
        pageId = `${pageId}/${subpage}/${subsubpage}`;
        if (this.debug) console.log(`[${this.getComponentName()}] Constructed triple nested pageId: ${pageId}`);
      } else if (pageId && subpage) {
        pageId = `${pageId}/${subpage}`;
        if (this.debug) console.log(`[${this.getComponentName()}] Constructed nested pageId: ${pageId}`);
      }

      if (this.debug) console.log(`[${this.getComponentName()}] Loading components for pageId: ${pageId}`);

      // Fallback to default page if no pageId is provided in the route
      if (!pageId) {
        pageId = this.getDefaultPage();
        if (this.debug) console.log(`[${this.getComponentName()}] No pageId in route, defaulting to '${pageId}'`);
      }

      // Clear existing components
      this.container.clear();

      const pagesConfig = this.configService.sysConfig.pages;
      if (!Array.isArray(pagesConfig)) {
        console.error(`[${this.getComponentName()}] Pages configuration is not an array:`, pagesConfig);
        return; // Exit if pages config is not the expected array
      }

      // Validate pages configuration if in debug mode
      if (this.debug) {
        const validationOptions: ValidationOptions = {
          validateComponents: true,
          validateNavigation: false,
          showWarnings: true
        };

        const validationResult = this.pageValidator.validatePages(pagesConfig, validationOptions);

        if (!validationResult.isValid) {
          console.error(`[${this.getComponentName()}] Page configuration validation failed:`, validationResult.errors);
          validationResult.errors.forEach(error => console.error(`[${this.getComponentName()}] Validation Error: ${error}`));
        }

        if (validationResult.warnings.length > 0) {
          console.warn(`[${this.getComponentName()}] Page configuration warnings:`, validationResult.warnings);
          validationResult.warnings.forEach(warning => console.warn(`[${this.getComponentName()}] Validation Warning: ${warning}`));
        }

        console.log(`[${this.getComponentName()}] ${this.pageValidator.getValidationStats(validationResult)}`);
      }

      // Find the page configuration in the array by path
      const pageConfig = pagesConfig.find(page => page.path === pageId);

      if (pageConfig) {
        if (this.debug) console.log(`[${this.getComponentName()}] Page config found:`, pageConfig);
        this.pageClass = pageConfig.class || ''; // Set page class

        // Add debug log before the check
        if (this.debug) console.log(`[${this.getComponentName()}] Security Check: pageConfig.secure=${pageConfig.secure}, this.isAuthenticated=${this.isAuthenticated}`);

        // Check security requirements - can be overridden by subclasses
        if (!this.checkPageSecurity(pageConfig)) {
          return; // Stop loading components if security check fails
        }

        // Load components specified in the config
        pageConfig.components.forEach((componentConfig: ComponentConfig) => {
          if (this.debug) console.log(`[${this.getComponentName()}] Processing component config:`, componentConfig);

          let shouldLoad = false;
          const showWhen = componentConfig.showWhen;

          // Add log for evaluating showWhen
          if(this.debug) console.log(`[${this.getComponentName()}] Evaluating component ${componentConfig.type}: showWhen='${showWhen}', isAuthenticated=${this.isAuthenticated}`);

          if (showWhen === 'authenticated' && this.isAuthenticated) {
            shouldLoad = true;
            if (this.debug) console.log(`[${this.getComponentName()}] Condition met: showWhen='authenticated', user is authenticated.`);
          } else if (showWhen === 'anonymous' && !this.isAuthenticated) {
            shouldLoad = true;
            if (this.debug) console.log(`[${this.getComponentName()}] Condition met: showWhen='anonymous', user is anonymous.`);
          } else if (!showWhen || showWhen === 'always') { // If showWhen is not defined or 'always', always load
            shouldLoad = true;
            if (this.debug) console.log(`[${this.getComponentName()}] Condition met: showWhen is '${showWhen}' or not defined.`);
          } else {
             if (this.debug) console.log(`[${this.getComponentName()}] Condition NOT met for component ${componentConfig.type}: showWhen='${showWhen}', isAuthenticated=${this.isAuthenticated}. Skipping load.`);
          }

          if (shouldLoad) {
            if (this.debug) console.log(`[${this.getComponentName()}] Loading component: ${componentConfig.type}`);
            this.loadComponent(componentConfig, this.container);
          } else {
            if (this.debug) console.log(`[${this.getComponentName()}] Skipping component load based on showWhen condition: ${componentConfig.type}`);
          }
        });
      } else {
        console.warn(`[${this.getComponentName()}] No configuration found for pageId: ${pageId}. Available pages:`, pagesConfig.map(p => p.path));
        this.handlePageNotFound(pageId);
      }
      this.cdr.detectChanges(); // Ensure UI updates after loading components
    });
  }

  // Method to check page security - can be overridden by subclasses
  protected checkPageSecurity(pageConfig: any): boolean {
    // Default implementation - can be overridden
    return true;
  }

  // Method to handle page not found - can be overridden by subclasses
  protected handlePageNotFound(pageId: string): void {
    // Default implementation - navigate to the default page for this component type
    const defaultPage = this.getDefaultPage();
    console.warn(`[${this.getComponentName()}] Page not found: ${pageId}. Redirecting to default page: ${defaultPage}`);
    
    // Determine the route prefix based on the component type
    const routePrefix = this.getComponentName().toLowerCase().includes('public') ? '/public' : '/secure';
    this.router.navigate([`${routePrefix}/${defaultPage}`]);
  }

  // Helper method to check if a profile is complete enough for component display
  protected hasValidProfile(): boolean {
    const isValid = !!this.profile && !!this.profile.givenNames && !!this.profile.surname && !!this.profile.newMembershipNumber;
    console.log(`[${this.getComponentName()}] hasValidProfile check:`, isValid, 'Profile:', this.profile);
    return isValid;
  }

  // Component loading helper method
  protected loadComponent(componentConfig: ComponentConfig, container: ViewContainerRef): void {
    if (!componentConfig || !componentConfig.type) {
      console.error(`[${this.getComponentName()}] Invalid component configuration:`, componentConfig);
      return;
    }

    // For components that need profile data, verify we have a valid profile
    if (componentConfig.showWhen === 'authenticated' &&
        (componentConfig.inputs && componentConfig.inputs['profile'] === 'profile' ||
         componentConfig.type.includes('Dashboard') || componentConfig.type.includes('Profile')) &&
        !this.hasValidProfile()) {
      console.warn(`[${this.getComponentName()}] Skipping ${componentConfig.type} - waiting for valid profile data`);
      return;
    }

    try {
      const componentType = this.loader.resolveComponent(componentConfig.type);
      if (!componentType) {
        console.error(`[${this.getComponentName()}] Component type '${componentConfig.type}' not registered or not found.`);
        return;
      }

      // Create injector with specific providers if needed, or use default
      const componentInjector = Injector.create({
        providers: [
          { provide: ActivatedRoute, useValue: this.route },
          { provide: Router, useValue: this.router },
          { provide: ConfigService, useValue: this.configService },
          { provide: MemberService, useValue: this.memberService },
          { provide: KeyCloakService, useValue: this.kc }
        ],
        parent: this.injector
      });

      // Create the component instance with the injector
      const componentRef = container.createComponent(componentType, { injector: componentInjector });

      // Apply the inputs to the component instance (support both 'inputs' and 'config' properties)
      const inputsToProcess = componentConfig.inputs || componentConfig.config || {};
      if (inputsToProcess && typeof inputsToProcess === 'object') {
        for (const [key, value] of Object.entries(inputsToProcess)) {
          // Check for special input values that should be replaced with actual instances
          let inputValue = value;

          // Handle special string values that should be replaced with actual services/objects
          if (typeof value === 'string') {
            switch (value) {
              case 'kc':
                inputValue = this.kc;
                if (this.debug) console.log(`[${this.getComponentName()}] Replacing 'kc' with KeyCloakService instance`);
                break;
              case 'profile':
                inputValue = this.profile;
                if (this.debug) console.log(`[${this.getComponentName()}] Replacing 'profile' with profile object:`, this.profile);
                break;
              case 'router':
                inputValue = this.router;
                if (this.debug) console.log(`[${this.getComponentName()}] Replacing 'router' with Router instance`);
                break;
              case 'memberService':
                inputValue = this.memberService;
                if (this.debug) console.log(`[${this.getComponentName()}] Replacing 'memberService' with MemberService instance`);
                break;
              case 'lssConfig':
                inputValue = this.configService.sysConfig;
                if (this.debug) console.log(`[${this.getComponentName()}] Replacing 'lssConfig' with sysConfig object`);
                break;
              default:
                // Keep the original string value
                break;
            }
          }

          // Assign the processed input value to the component
          componentRef.instance[key] = inputValue;
          if (this.debug) console.log(`[${this.getComponentName()}] Set input '${key}' on component '${componentConfig.type}'`);
        }
      }

      // Subscribe to actionEvent if it exists
      if (componentRef.instance && (componentRef.instance as any).actionEvent) {
        (componentRef.instance as any).actionEvent.subscribe((action: string) => {
          this.handleComponentAction(action);
        });
      }

      // Detect changes to ensure inputs are applied
      componentRef.changeDetectorRef.detectChanges();

      if (this.debug) console.log(`[${this.getComponentName()}] Successfully created and configured component: ${componentConfig.type}`);
    } catch (error) {
      console.error(`[${this.getComponentName()}] Error creating component ${componentConfig.type}:`, error);
    }
  }

  // Handle component actions
  protected handleComponentAction(action: string): void {
    if (this.debug) console.log(`[${this.getComponentName()}] Handling action: ${action}`);
    switch (action) {
      case 'logout':
        console.log(`[${this.getComponentName()}] Logout action received`);
        this.kc.logout();
        break;
      case 'viewTransactions':
        console.log(`[${this.getComponentName()}] Navigate to transactions`);
        this.router.navigate(['/secure/transactions']);
        break;
      case 'downloadStatement':
        console.log(`[${this.getComponentName()}] Download statement action`);
        // Implement download statement logic
        break;
      case 'getStarted':
        console.log(`[${this.getComponentName()}] Get started action`);
        this.router.navigate(['/public/register']);
        break;
      case 'openTermsLink':
        console.log(`[${this.getComponentName()}] Open terms link action`);
        // Implement terms link opening logic
        break;
      case 'resendOtp':
        console.log(`[${this.getComponentName()}] Resend OTP action`);
        // Implement OTP resend logic
        break;
      default:
        console.warn(`[${this.getComponentName()}] Unhandled action: ${action}`);
    }
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }
    if (this.profileSubscription) {
      this.profileSubscription.unsubscribe();
    }
  }
}
