import { Injectable, Type, ComponentRef } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

// Interface for component registration metadata
export interface ComponentRegistration {
  name: string;
  component: Type<any>;
  registeredAt: Date;
  source: 'static' | 'dynamic' | 'lazy';
  validated: boolean;
}

// Interface for component validation result
export interface ComponentValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

@Injectable({
  providedIn: 'root'
})
export class ComponentRegistryService {
  private components = new Map<string, ComponentRegistration>();
  private registrationSubject = new BehaviorSubject<ComponentRegistration[]>([]);
  private debug = true; // Enable debug logging for development

  constructor() {
    console.log('[ComponentRegistry] Service initialized');
    this.autoRegisterComponents();
  }

  // Observable for component registration changes
  get registrations$(): Observable<ComponentRegistration[]> {
    return this.registrationSubject.asObservable();
  }

  // Get all registered components
  get registeredComponents(): ComponentRegistration[] {
    return Array.from(this.components.values());
  }

  // Auto-register all page-section components from mobile-components library
  private async autoRegisterComponents(): Promise<void> {
    try {
      if (this.debug) console.log('[ComponentRegistry] Auto-registering components from mobile-components');

      // Import mobile-components library
      const components = await import('@projects/mobile-components');

      // List of all known components from mobile-components library
      const componentNames = [
        // Base components
        'ButtonComponent',
        'AccordianComponent',
        'HeadLogoComponent',
        'InputComponent',
        'CardComponent',
        'ListComponent',
        'MessageComponent',
        'ParagraphComponent',
        'CheckboxComponent',
        'TextComponent',
        'ImageComponent',
        'IconComponent',
        'LinkComponent',
        'HeadingComponent',
        'AvatarComponent',
        'BreadcrumbComponent',
        'ButtonActionComponent',
        'ButtonCloseComponent',
        'ButtonIconComponent',
        'CheckboxAnimatedComponent',
        'DatepickerComponent',
        'LogoComponent',
        'MobileComponent',
        'PaginationComponent',
        'PictureComponent',
        'PlaceholderPageComponent',
        'BasePlaceloadComponent',
        'ProgressComponent',
        'ProseComponent',
        'RadioComponent',
        'TagComponent',
        'AutocompleteComponent',
        'AutocompleteItemComponent',
        'DividerComponent',
        'DropdownComponent',
        'DropdownDividerComponent',
        'DropdownItemComponent',
        'KbdComponent',
        'ListboxComponent',

        // Form components
        'SignupComponent',
        'ValidateComponent',
        'OtpValidatorComponent',
        'OtpComponent',
        'AddressComponent',
        'InputFileComponent',
        'InputNumberComponent',
        'InputFilePathComponent',
        'RadioHeadlessComponent',
        'CheckboxHeadlessComponent',
        'CountriesSelectComponent',
        'IndustrySelectComponent',
        'InputFileHeadlessComponent',

        // Page components
        'PagesLandingTheme1Component',
        'PagesLoginTheme1Component',
        'PagesLandingThemesGamesComponent',
        'ProfileDetailsComponent',
        'DynamicDashboardComponent',
        'HomeComponent',
        'PagesCustomizerComponent',

        // Games components
        'GamesComponent',
        'GamesSingleComponent',
        'AllGamesComponent',
        'CategoriesComponent',
        'GamesCategoriesComponent',
        'DashboardComponent',
        'FavouritesComponent',
        'GamesHomeComponent',
        'HowToPlayComponent',

        // Page-section components
        'HomeHeaderComponent',
        'HomeNavigationComponent',
        'HomeActionsComponent',
        'DashboardHeaderComponent',
        'DashboardWelcomeComponent',
        'DashboardQuickActionsComponent',
        'DashboardSummaryComponent',
        'ProfileHeaderComponent',
        'ProfileFormComponent',
        'ProfileSettingsComponent',
        'ProfileHelpComponent',

        // Widget components
        'CompanyOverviewComponent',
        'ButtonGroupComponent',
        'FlexTableStartComponent',
        'ImageGalleryComponent',
        'FilterComponent',
        'TabbedContentComponent',
        'IconTextComponent',
        'FlexTableCellComponent',
        'IconBoxComponent',
        'SearchCompactComponent',
        'ModalMediumTierComponent',
        'AuthorsListCompactComponent',
        'AvatarGroupComponent',
        'ImageLinksComponent',
        'MenuIconListComponent',
        'ListItemComponent',
        'MapMarkerComponent',
        'CardFiltersComponent',
        'ActionTextComponent',
        'TimelineCompactComponent',
        'ModalFooterComponent',
        'WelcomeComponent',
        'InfoImageComponent',
        'ModalLargeTierComponent',
        'PlaceholderMinimalComponent',
        'InfoBadgesComponent',
        'FeaturesComponent',
        'VcardRightComponent',
        'ModalSmallTierComponent',
        'FlexTableHeadingComponent',
        'FlexTableRowComponent',
        'FlexTableWrapperComponent',
        'FollowersCompactComponent',
        'InboxMessageComponent',
        'VideoCompactComponent',
        'TagListCompactComponent',
        'FileListTabbedComponent',
        'AvatarGroupIdComponent',
        'CommentListCompactComponent',
        'FullscreenDropfileComponent',
        'PlaceholderCompactComponent',
        'ProgressCircleComponent',
        'AccountBalanceComponent',
        'DynamicListComponent',
        'FlexTableComponent',
        'FocusLoopComponent',
        'ListboxItemComponent',
        'QuillComponent',
        'SearchComponent',
        'SearchTagComponent',

        // Layout components
        'LayoutComponent',
        'SidebarComponent',
        'HeaderComponent',
        'FooterComponent',
        'NavigationComponent',

        // Feature components
        'FeatureComponent',
        'FeatureListComponent',
        'FeatureCardComponent',

        // Additional components that might be available
        'LoadingComponent',
        'ErrorComponent',
        'NotificationComponent',
        'ContactusComponent',
        'TransactionsComponent',
        'VirtualCardComponent',
        'ProfileComponent'
      ];

      let registeredCount = 0;
      let failedCount = 0;

      for (const componentName of componentNames) {
        if ((components as any)[componentName]) {
          const registration = this.registerComponent(
            componentName,
            (components as any)[componentName],
            'static'
          );
          if (registration.validated) {
            registeredCount++;
          } else {
            failedCount++;
          }
        } else {
          if (this.debug) console.warn(`[ComponentRegistry] Component ${componentName} not found in mobile-components`);
        }
      }

      console.log(`[ComponentRegistry] Auto-registration complete: ${registeredCount} registered, ${failedCount} failed`);
      this.notifyRegistrationChange();

    } catch (error) {
      console.error('[ComponentRegistry] Error during auto-registration:', error);
    }
  }

  // Register a component with type-safe validation
  registerComponent(
    name: string,
    component: Type<any>,
    source: 'static' | 'dynamic' | 'lazy' = 'dynamic'
  ): ComponentRegistration {
    if (!name || !component) {
      throw new Error('[ComponentRegistry] Component name and type are required');
    }

    // Validate the component
    const validation = this.validateComponent(component);

    const registration: ComponentRegistration = {
      name,
      component,
      registeredAt: new Date(),
      source,
      validated: validation.isValid
    };

    // Log validation results
    if (!validation.isValid) {
      console.error(`[ComponentRegistry] Component ${name} failed validation:`, validation.errors);
    }
    if (validation.warnings.length > 0) {
      console.warn(`[ComponentRegistry] Component ${name} validation warnings:`, validation.warnings);
    }

    // Register the component
    this.components.set(name, registration);

    if (this.debug) {
      console.log(`[ComponentRegistry] Registered component: ${name} (${source}, valid: ${validation.isValid})`);
    }

    this.notifyRegistrationChange();
    return registration;
  }

  // Unregister a component
  unregisterComponent(name: string): boolean {
    const existed = this.components.has(name);
    this.components.delete(name);

    if (existed) {
      if (this.debug) console.log(`[ComponentRegistry] Unregistered component: ${name}`);
      this.notifyRegistrationChange();
    }

    return existed;
  }

  // Get a registered component by name
  getComponent(name: string): Type<any> | null {
    const registration = this.components.get(name);
    return registration ? registration.component : null;
  }

  // Check if a component is registered
  isRegistered(name: string): boolean {
    return this.components.has(name);
  }

  // Get component registration details
  getRegistration(name: string): ComponentRegistration | null {
    return this.components.get(name) || null;
  }

  // Validate a component type
  private validateComponent(component: Type<any>): ComponentValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Check if it's a valid Angular component
      if (!component) {
        errors.push('Component is null or undefined');
        return { isValid: false, errors, warnings };
      }

      // Check if it has Angular component metadata
      const metadata = (component as any).__annotations__ || (component as any).ɵcmp;
      if (!metadata) {
        warnings.push('Component may not have Angular metadata (could be a standalone component)');
      }

      // Check if component has a constructor
      if (typeof component !== 'function') {
        errors.push('Component is not a constructor function');
      }

      // Additional validation can be added here
      // For example, checking for required inputs, outputs, etc.

    } catch (error) {
      errors.push(`Validation error: ${error}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  // Register components from dynamic import
  async registerFromImport(importPromise: Promise<any>, source: 'dynamic' | 'lazy' = 'dynamic'): Promise<ComponentRegistration[]> {
    try {
      const components = await importPromise;
      const registrations: ComponentRegistration[] = [];

      for (const [name, component] of Object.entries(components)) {
        if (this.isComponentType(component)) {
          const registration = this.registerComponent(name, component as Type<any>, source);
          registrations.push(registration);
        }
      }

      return registrations;
    } catch (error) {
      console.error('[ComponentRegistry] Error registering from import:', error);
      return [];
    }
  }

  // Check if an object is a component type
  private isComponentType(obj: any): boolean {
    return typeof obj === 'function' &&
           (obj.prototype || obj.__annotations__ || obj.ɵcmp);
  }

  // Get registration statistics
  getStats(): { total: number; validated: number; bySource: Record<string, number> } {
    const registrations = this.registeredComponents;
    const bySource: Record<string, number> = {};

    registrations.forEach(reg => {
      bySource[reg.source] = (bySource[reg.source] || 0) + 1;
    });

    return {
      total: registrations.length,
      validated: registrations.filter(r => r.validated).length,
      bySource
    };
  }

  // Clear all registrations (useful for testing)
  clearAll(): void {
    this.components.clear();
    this.notifyRegistrationChange();
    if (this.debug) console.log('[ComponentRegistry] All components cleared');
  }

  // Get list of component names
  getComponentNames(): string[] {
    return Array.from(this.components.keys());
  }

  // Search for components by name pattern
  searchComponents(pattern: string): ComponentRegistration[] {
    const regex = new RegExp(pattern, 'i');
    return this.registeredComponents.filter(reg => regex.test(reg.name));
  }

  // Enable/disable debug logging
  setDebugMode(enabled: boolean): void {
    this.debug = enabled;
    console.log(`[ComponentRegistry] Debug mode ${enabled ? 'enabled' : 'disabled'}`);
  }

  // Notify subscribers of registration changes
  private notifyRegistrationChange(): void {
    this.registrationSubject.next(this.registeredComponents);
  }

  // Development helper: log all registered components
  logRegisteredComponents(): void {
    console.group('[ComponentRegistry] Registered Components');
    this.registeredComponents.forEach(reg => {
      console.log(`${reg.name}: ${reg.source} (${reg.validated ? 'valid' : 'invalid'}) - ${reg.registeredAt.toISOString()}`);
    });
    console.groupEnd();
  }
}
