import { Component, Injector, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ComponentsModule } from '@projects/mobile-components';
import { KeyCloakService, MemberService } from 'lp-client-api';
import { ConfigService } from '../services/config.service';
import { DynamicComponentLoaderService } from '../shared/dynamic-component-loader.service';
import { PageConfigurationValidationService } from '../shared/page-configuration-validation.service';
import { AuthService } from '../shared/auth.service';
import { BaseDynamicPageComponent } from '../shared/base-dynamic-page.component';

@Component({
  selector: 'app-dynamic-secure-page',
  template: `<div [ngClass]="pageClass" class="dynamic-components-container">
    <ng-template #container></ng-template>
  </div>`,
  styles: [`
    .dynamic-components-container {
      display: flex;
      flex-direction: column;
      gap: 0; /* Remove gaps between flex items */
    }

    /* This ensures components are rendered without default margins */
    .dynamic-components-container > * {
      margin: 0;
    }
  `],
  standalone: true,
  imports: [CommonModule, ComponentsModule]
})
export class DynamicSecurePageComponent extends BaseDynamicPageComponent {
  protected override debug = true; // Enable debug for secure pages

  constructor(
    route: ActivatedRoute,
    router: Router,
    loader: DynamicComponentLoaderService,
    injector: Injector,
    configService: ConfigService,
    cdr: ChangeDetectorRef,
    memberService: MemberService,
    kc: KeyCloakService,
    pageValidator: PageConfigurationValidationService,
    private authService: AuthService
  ) {
    super(route, router, loader, injector, configService, cdr, memberService, kc, pageValidator);
  }

  // Implement abstract methods from base class
  protected getComponentName(): string {
    return 'DynamicSecurePage';
  }

  protected getDefaultPage(): string {
    return 'dashboard';
  }

  // Override logout navigation for secure pages
  protected override handleLogoutNavigation(): void {
    console.log(`[${this.getComponentName()}] User logged out, redirecting to login`);
    this.router.navigate(['/public/login']);
  }

  // Override initial load handling for secure pages
  protected override handleInitialLoad(): void {
    // Check authentication and redirect if not authenticated
    if (!this.isAuthenticated) {
      console.log(`[${this.getComponentName()}] User not authenticated, redirecting to login`);
      this.router.navigate(['/public/login']);
      return;
    }

    // Call parent implementation for authenticated users
    super.handleInitialLoad();
  }

  // Override page security check for secure pages
  protected override checkPageSecurity(pageConfig: any): boolean {
    // Secure pages should only be accessible when authenticated
    if (pageConfig.secure && !this.isAuthenticated) {
      if (this.debug) console.log(`[${this.getComponentName()}] Condition MET: Secure page requested, but user not authenticated. Redirecting to login.`);
      this.router.navigate(['/public/login']);
      return false; // Stop loading components
    }
    return true;
  }

  // Override page not found handling for secure pages
  protected override handlePageNotFound(pageId: string): void {
    console.warn(`[${this.getComponentName()}] No configuration found for pageId: ${pageId}. Redirecting to dashboard.`);
    this.router.navigate(['/secure/dashboard']);
  }
}
