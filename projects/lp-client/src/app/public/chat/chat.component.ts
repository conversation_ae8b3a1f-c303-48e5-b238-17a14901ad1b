import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Observable, of } from 'rxjs';
// Remove the ChatService interface from here
import { ChatService } from '../../services/chat.service'; // Import the actual service
// Remove the local Message interface
import { Message } from '../../models/message.interface'; // Add this import

@Component({
  selector: 'app-chat',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './chat.component.html',
  styleUrl: './chat.component.css',
})
export class ChatComponent implements OnInit {
  messages$: Observable<Message[]> = of([]);
  newMessage: string = '';

  constructor(private chatService: ChatService) {}

  ngOnInit() {
    this.messages$ = this.chatService.getMessages();
  }

  sendMessage() {
    if (this.newMessage.trim()) {
      this.chatService.sendMessage(this.newMessage.trim());
      this.newMessage = '';
    }
  }
}
