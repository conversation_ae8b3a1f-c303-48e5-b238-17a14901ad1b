import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { GamesSingleComponent } from '@projects/mobile-components';
import { GameService } from 'lp-client-api';
import { Game } from 'lp-client-api';

@Component({
  selector: 'app-single',
  templateUrl: './single.component.html',
  styleUrls: ['./single.component.scss'],
  standalone: true,
  imports: [CommonModule, GamesSingleComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class SingleComponent implements OnInit {
  public gameParam: string | null = null;

  constructor(
    private route: ActivatedRoute,
    private gameService: GameService
  ) {}

  ngOnInit() {
    console.log('Single game component initialized');

    // Get the game parameter from the URL
    this.route.queryParams.subscribe(params => {
      this.gameParam = params['game'];
      console.log('Game parameter from URL:', this.gameParam);

      if (this.gameParam) {
        // If we have a game parameter, set it as the active game
        this.setActiveGame(this.gameParam);
      } else {
        // If no game parameter, try to get the active game from localStorage
        const gameData = localStorage.getItem('activeGame');
        if (gameData) {
          try {
            const game = JSON.parse(gameData);
            console.log('Retrieved active game from localStorage:', game);
            this.gameService.setActiveGame(game);
          } catch (error) {
            console.error('Error parsing active game from localStorage:', error);
          }
        }
      }
    });
  }

  private setActiveGame(gameCode: string) {
    console.log('Setting active game in single component:', gameCode);

    // Normalize the game code (handle both hyphenated and non-hyphenated codes)
    const normalizedGameCode = gameCode.replace(/-/g, '').toLowerCase();

    // Get all games from the service
    this.gameService.getAllGameConfigs().subscribe({
      next: (games: Game[]) => {
        console.log('Available games:', games);

        // Find the game with the matching code
        const matchingGame = games.find(game => {
          const gameTypeCode = game.gameType.typeDescription.code.replace(/-/g, '').toLowerCase();
          return gameTypeCode === normalizedGameCode;
        });

        if (matchingGame) {
          console.log('Found matching game:', matchingGame);

          // Set the active game in the service
          this.gameService.setActiveGame(matchingGame);

          // Store the active game in localStorage for persistence
          localStorage.setItem('activeGame', JSON.stringify(matchingGame));
        } else {
          console.error('No matching game found for code:', normalizedGameCode);
        }
      },
      error: (error: any) => {
        console.error('Error loading games:', error);
      }
    });
  }
}
