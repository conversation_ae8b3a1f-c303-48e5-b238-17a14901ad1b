import { Component, Injector, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AbstractComponent, AllGamesComponent as MobileGamesAllComponent } from '@projects/mobile-components';
import { GameService, Game } from 'lp-client-api';
import { Router } from '@angular/router';

interface GameViewModel {
  game: Game;
  display: GameDisplay;
}

interface GameDisplay {
  description: string;
  image: string;
  genre: string;
  difficulty: string;
  frequency: string;
  attempts: number;
}

@Component({
  selector: 'app-all',
  templateUrl: './all.component.html',
  styleUrls: ['./all.component.scss'],
  standalone: true,
  imports: [CommonModule, MobileGamesAllComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AllGamesComponent extends AbstractComponent implements OnInit {
  public games: GameViewModel[] = [];

  constructor(
    injector: Injector,
    private gameService: GameService,
    private router: Router
  ) {
    super(injector);
  }

  ngOnInit() {
    this.loadGames();
  }

  private loadGames() {
    this.gameService.getAllGameConfigs().subscribe({
      next: (apiGames) => {
        console.log('Games loaded successfully:', apiGames);
        console.log(
          'First game structure:',
          JSON.stringify(apiGames[0], null, 2)
        );

        this.games = apiGames.map((game) => {
          console.log(`Processing game ${game.name}:`, {
            id: game.id,
            gameType: game.gameType,
            gameConfig: game.gameConfig,
          });

          return {
            game,
            display: this.getGameDisplay(game),
          };
        });
      },
      error: (error) => {
        console.error('Error loading games:', error);
      },
    });
  }

  private getGameDisplay(game: Game): GameDisplay {
    const typeCode = game.gameType?.typeDescription?.code || 'default';
    const participation = game.gameParticipation?.[0];

    return {
      description: game.gameType?.typeDescription?.description || '',
      image:
        game.backgroundImage ||
        `assets/images/games/${typeCode.toLowerCase()}.jpg`,
      genre: game.gameType?.categoryDescription || 'Unknown',
      difficulty: participation?.difficulty || 'MEDIUM',
      frequency: participation?.frequency || 'DAILY',
      attempts: participation?.frequencyAttempts || 3,
    };
  }

  public playGame(gameViewModel: GameViewModel) {
    const game = gameViewModel.game;
    console.log(`Playing ${game.name}`, game);

    // Validate basic game info
    if (!game.id || !game.gameType?.typeDescription?.code) {
      console.error('Invalid game configuration - missing basic fields:', {
        id: game.id,
        typeCode: game.gameType?.typeDescription?.code,
      });
      return;
    }

    // Get the game type code for routing
    const typeCode = game.gameType.typeDescription.code.toLowerCase();
    console.log('Game type code:', typeCode);

    // Store the game in localStorage and set as active
    try {
      localStorage.setItem('activeGame', JSON.stringify(game));
      localStorage.removeItem('currentGameInstanceId'); // Clear any existing instance
      this.gameService.setActiveGame(game);

      // Navigate to dashboard with the correct game type
      this.router.navigate(['/public/games/dashboard'], {
        queryParams: { game: typeCode },
      });
    } catch (error) {
      console.error('Error setting active game:', error);
    }
  }
}
