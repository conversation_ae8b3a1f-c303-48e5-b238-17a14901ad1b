import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { ActivatedRoute, Router } from '@angular/router';
import { GameService } from 'lp-client-api';
import { Game, GameConfigs, GameInstance } from 'lp-client-api';
import { GamesHeaderComponent, GameLoaderDashboardComponent } from '@projects/mobile-components';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  standalone: true,
  imports: [CommonModule, GameLoaderDashboardComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class DashboardComponent implements OnInit {
  constructor() {}

  ngOnInit() {
    console.log('Dashboard component initialized');
  }

  // private async initializeGame() {
  //   try {
  //     if (!this.game) {
  //       throw new Error('No game data found');
  //     }

  //     // Get game config for the current game
  //     const gameConfig = this.game.gameConfig[0];
  //     if (!gameConfig) {
  //       throw new Error('No game configuration found');
  //     }

  //     this.gameConfig = gameConfig;

  //     // First try to get existing instances
  //     try {
  //       const accountConfigs = await firstValueFrom(
  //         this.gameService.getAccountGameConfigs(this.MEMKEY)
  //       );

  //       // Find matching config and its active instance
  //       const matchingConfig = accountConfigs.find(
  //         config => config.gameConfig === gameConfig.id
  //       );

  //       if (matchingConfig?.gameInstance) {
  //         // Get the full instance details
  //         this.activeGameInstance = await firstValueFrom(
  //           this.gameService.getGameInstance(gameConfig.id, matchingConfig.gameInstance)
  //         );
  //         console.log('Found existing game instance:', this.activeGameInstance);
  //       } else {
  //         // If no instance exists, create a new one
  //         this.activeGameInstance = await firstValueFrom(
  //           this.gameService.createGameInstance(gameConfig.id)
  //         );
  //         console.log('Created new game instance:', this.activeGameInstance);
  //       }
  //     } catch (instanceError) {
  //       console.error('Error getting/creating game instance:', instanceError);
  //       throw new Error('Failed to initialize game instance');
  //     }

  //     this.loading = false;
  //   } catch (error: any) {
  //     this.error = error?.message || 'Failed to initialize game';
  //     this.loading = false;
  //     console.error('Game initialization error:', error);
  //   }
  // }

  // public async sendGameEvent(eventType: string, eventData: any) {
  //   if (!this.gameConfig || !this.activeGameInstance) return;

  //   try {
  //     await firstValueFrom(
  //       this.gameService.createGameEvent(
  //         this.gameConfig.id,
  //         this.activeGameInstance.id,
  //         {
  //           level: 1,
  //           score: eventData.score || 0,
  //           duration: eventData.duration || 0,
  //           state: eventType,
  //           payload: JSON.stringify(eventData)
  //         }
  //       )
  //     );
  //   } catch (error: any) {
  //     console.error('Failed to send game event:', error);
  //   }
  // }
}
