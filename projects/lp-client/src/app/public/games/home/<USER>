import { Component, Injector, ChangeDetectorRef } from '@angular/core';
import { AbstractComponent } from '@projects/mobile-components';
import { PagesLandingThemesGamesComponent } from '@projects/mobile-components';

import {
  KeyCloakService,
  MemberProfile,
  MemberService,
  LssConfig,
} from 'lp-client-api';
import { Router } from '@angular/router';

@Component({
  selector: 'pages-games-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  standalone: true,
  imports: [PagesLandingThemesGamesComponent],
})
export class GamesHomeComponent extends AbstractComponent {
  constructor(injector: Injector) {
    super(injector);
  }
}
