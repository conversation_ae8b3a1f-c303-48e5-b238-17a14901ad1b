import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { PushNotificationService } from '../../services/push-notification.service';
import { NotificationData, NotificationAnalyticsService } from '@projects/mobile-components';

@Component({
  selector: 'app-notification-toast',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <div *ngIf="notification" class="notification-toast">
      <div class="notification-header">
        <h3>{{ notification.title }}</h3>
        <ion-icon name="close-outline" (click)="dismissNotification()"></ion-icon>
      </div>
      <p>{{ notification.body }}</p>

      <!-- Show image if available -->
      <img *ngIf="notification.image" [src]="notification.image" class="notification-image" alt="Notification image">

      <!-- Show action buttons if available -->
      <div *ngIf="notification.data?.action" class="notification-actions">
        <ion-button size="small" fill="clear" (click)="handleAction()">
          {{ notification.data?.actionText || 'View' }}
        </ion-button>
      </div>
    </div>
  `,
  styles: [`
    .notification-toast {
      position: fixed;
      top: 20px;
      right: 20px;
      background: white;
      padding: 15px;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000;
      max-width: 320px;
      animation: slideIn 0.3s ease-out forwards;
    }

    .notification-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--ion-text-color, #333333);
    }

    ion-icon {
      font-size: 20px;
      cursor: pointer;
      color: var(--ion-color-medium, #92949c);
    }

    p {
      margin: 0 0 10px 0;
      font-size: 14px;
      color: var(--ion-color-medium-shade, #808289);
      line-height: 1.4;
    }

    .notification-image {
      width: 100%;
      border-radius: 8px;
      margin-bottom: 10px;
      max-height: 150px;
      object-fit: cover;
    }

    .notification-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 5px;
    }

    @keyframes slideIn {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
      .notification-toast {
        background: #1c1c1e;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      }
    }
  `]
})
export class NotificationToastComponent implements OnInit, OnDestroy {
  notification: NotificationData | null = null;
  private subscription: Subscription | null = null;
  private autoHideTimeout: any = null;

  constructor(
    private pushNotificationService: PushNotificationService,
    private router: Router,
    private analyticsService: NotificationAnalyticsService
  ) {}

  ngOnInit() {
    this.subscription = this.pushNotificationService.notifications$.subscribe(notification => {
      if (notification) {
        // Clear any existing timeout
        if (this.autoHideTimeout) {
          clearTimeout(this.autoHideTimeout);
        }

        this.notification = notification;

        // Auto-hide after 7 seconds (increased from 5 to give more time to read rich notifications)
        this.autoHideTimeout = setTimeout(() => {
          this.dismissNotification();
        }, 7000);
      }
    });
  }

  /**
   * Dismiss the notification
   */
  dismissNotification() {
    if (this.notification?.data?.id) {
      // Track notification dismissal
      this.analyticsService.trackDismiss(
        this.notification.data.id,
        this.notification.data?.group
      );
    }

    this.notification = null;
    if (this.autoHideTimeout) {
      clearTimeout(this.autoHideTimeout);
      this.autoHideTimeout = null;
    }
  }

  /**
   * Handle notification action click
   */
  handleAction() {
    if (this.notification?.data?.action) {
      // Track action click
      if (this.notification.data?.id) {
        this.analyticsService.trackActionClick(
          this.notification.data.id,
          this.notification.data.action,
          this.notification.data?.group
        );
      }

      // Navigate to the specified route
      this.router.navigateByUrl(this.notification.data.action);

      // Dismiss the notification after navigation
      this.dismissNotification();
    }
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }

    if (this.autoHideTimeout) {
      clearTimeout(this.autoHideTimeout);
    }
  }
}
