import { Component, Injector, OnInit, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import {
  LPMemberEntityTools,
  MemberProfile,
  MemberService,
  ValidationService,
  KeyCloakService,
  Address,
  LssConfig,
  CountryItem,
  SystemService,
} from 'lp-client-api';
import { environment } from '../../../environments/environment';
import { Router } from '@angular/router';
import { AbstractFormComponent } from 'mobile-components';
import { map, Observable, of, tap } from 'rxjs';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss'],
})
export class ProfileComponent extends AbstractFormComponent<MemberProfile> {
  profile?: MemberProfile;
  addr!: Address;
  profileForm: FormGroup;
  validated: boolean = false;
  favourite: any = {};
  favourite_id: string = '';
  birthDate?: any = '';
  status_loading: boolean = true;
  phoneTogether: any = '+27';
  idType: string = 'nationalId'; // Default to national ID
  countries: Observable<CountryItem[]> = of([]); // Initialize countries observable
  countryList: CountryItem[] = []; // Add this to store the actual country list
  selectedCountryName: string = ''; // Add this to store the selected country name

  static emptyForm = {
    phone: LPMemberEntityTools.getIONTelephoneFromLPTel(
      LPMemberEntityTools.getEmptyPhone('CELL')
    ),
    address_POST: LPMemberEntityTools.getEmptyAddress('POST'),
    givenNames: '',
    surname: '',
    nationalIdNum: '',
    passortNum: '',
    emailAddress: '',
    title: '',
    gender: '',
    preferenceList: [],
  };

  constructor(
    injector: Injector,
    private memberService: MemberService,
    public override _formValidations: ValidationService,
    public override _formBuilder: FormBuilder,
    private kc: KeyCloakService,
    protected readonly router: Router,
    public lssConfig: LssConfig,
    public override _systemService: SystemService
  ) {
    super(injector);

    this.profileForm = this._formBuilder.group({
      phone: [
        LPMemberEntityTools.getIONTelephoneFromLPTel(
          LPMemberEntityTools.getEmptyPhone('CELL')
        ),
        [],
      ],
      givenNames: [''],
      title: [''],
      gender: [''],
      surname: ['', [Validators.required, Validators.minLength(3)]],
      nationalIdNum: [''],
      passortNum: ['', [Validators.minLength(6), Validators.maxLength(9)]],
      birthDate: ['', []],
      membershipNumber: [
        '',
        [
          Validators.pattern('^[0-9]*$'),
          Validators.minLength(16),
          Validators.maxLength(16),
        ],
      ],
      emailAddress: [
        '',
        [
          Validators.required,
          Validators.pattern('^[a-z0-9._%+-]+@[a-z0-9.-]+.[a-z]{2,4}$'),
        ],
      ],
      // Add passport-specific form controls
      issueCountry: [{value: '', disabled: true}],
      expiryDate: [{value: '', disabled: true}],
    });
    
    // Initialize countries observable and store the list
    this.countries = this._systemService.listCountries('').pipe(
      tap((countryData) => {
        this.countryList = countryData;
      })
    );
  }
  environment = environment;

  ionViewWillEnter() {
    this.loading = true;

    this.showLoadingModal('Fetching updated Account').then(() => {
      this.memberService
        .getProfile(this.kc.lpUniueReference, true)
        .subscribe((data) => {
          this.dismissLoadingModal();
          console.log('data-----issueCountry', data.issueCountry);
          Object.entries(data).forEach(([key, value]) => {
            console.log(`${key}: ${value}`);
          });
          this.profile = data;
          if (data) {
            let tel = null;
            if (data.personTelephone) {
              for (let i = 0; i < data.personTelephone.length; i++) {
                if (data.personTelephone[i].telephoneType == 'CELL')
                  tel = data.personTelephone[i];
              }
              if (tel) {
                let pl = {
                  internationalNumber:
                    tel.countryCode + ' ' + tel.telephoneNumber?.substring(1),
                  nationalNumber: tel.telephoneNumber,
                  isoCode: 'za',
                  dialCode: tel.countryCode,
                };
                this.phoneTogether = pl.internationalNumber;
                if (data.preferenceList) {
                  for (let i = 0; i < data.preferenceList.length; i++) {
                    console.log(
                      'data.preferenceList[i].level1',
                      data.preferenceList[i].level1
                    );
                    if (data.preferenceList[i].level1 == 'PART')
                      this.favourite_id = data.preferenceList[i].level2;
                  }
                }
                this.profileForm.controls['phone'].patchValue(pl);
              }
            }

            if (data.personAddress) {
              if (data.personAddress.length > 0) {
                let addr = data.personAddress.filter(
                  (item: Address) => item.addressType == 'POST'
                )[0];
                this.addr = addr;
              }
            }

            // Set ID type based on data
            if (data.nationalIdNum) {
              this.idType = 'nationalId';
            } else if (data.passortNum) {
              this.idType = 'passport';
              
              // If passport, also populate passport country and expiry date
              if (data.issueCountry) {
                const formControl = this.profileForm.get('issueCountry');
                if (formControl) {
                  formControl.setValue(data.issueCountry);
                  
                  // Find the country name and store it
                  this._systemService.listCountries('').subscribe(countries => {
                    const countryMatch = countries.find(c => 
                      (this.lssConfig.useISO ? c.isoCode : c.code) === data.issueCountry
                    );
                    if (countryMatch) {
                      this.selectedCountryName = countryMatch.country;
                    } else {
                      this.selectedCountryName = data.issueCountry ?? ''; // Fallback to empty string
                    }
                  });
                }
              }
              
              if (data.expiryDate) {
                // Format the date - ensure we handle both string and Date objects
                let expiryDateStr: string;
                if (typeof data.expiryDate === 'string') {
                  expiryDateStr = data.expiryDate;
                } else if (data.expiryDate instanceof Date) {
                  expiryDateStr = data.expiryDate.toISOString();
                } else {
                  expiryDateStr = new Date(data.expiryDate).toISOString();
                }
                // Get just the date part for the input field
                const formattedDate = expiryDateStr.split('T')[0];
                this.profileForm.get('expiryDate')?.setValue(formattedDate);
              }
            }

            this.profileForm.patchValue(data);
            this.loading = false;
            this.detectChanges();
          }
        });
    });
  }

  // Simplify the getCountryName method to use the stored country list
  getCountryName(countryCode: string): string {
    if (!countryCode || !this.countryList.length) return '';
    
    const foundCountry = this.countryList.find(c => 
      (this.lssConfig.useISO ? c.isoCode : c.code) === countryCode
    );
    return foundCountry ? foundCountry.country : countryCode;
  }

  get hasDate() {
    this.formData = this.getFormValues();
    let date;
    if (this.formData?.birthDate) {
      let newD: any = this.formData?.birthDate;
      date = newD.split('T')[0];
    }
    return date;
  }

  updateAddress(data: any) {
    this.favourite = data;
  }
  todaysDate12YearsAgo() {
    let date: any = new Date();
    date.setFullYear(date.getFullYear() - 12);
    //format date to yyyy-mm-dd
    date = date.toISOString().split('T')[0];
    return date;
  }
  // Add method for passport expiry date min value
  todaysDate(): string {
    return new Date().toISOString().split('T')[0];
  }
  override get form(): any {
    return this.profileForm.controls;
  }
  get isValid(): boolean {
    return this.profileForm.valid && this.hasValidId();
  }

  hasValidId(): boolean {
    const nationalId = this.profileForm.get('nationalIdNum')?.value;
    const passortNum = this.profileForm.get('passortNum')?.value;
    
    if (this.idType === 'nationalId') {
      return nationalId && nationalId.length === 13;
    } else if (this.idType === 'passport') {
      return passortNum && passortNum.length >= 6 && passortNum.length <= 9;
    }
    
    return false;
  }

  doLoad() {
    if (!this.profileForm.valid) {
      this.profileForm.markAllAsTouched();
      return;
    }
    this.profileForm.value.personAddress = [
      {
        addressType: 'POST',
        addressTypeDesc: '',
        line1: this.profileForm.value.address_POST.line1,
        line2: this.profileForm.value.address_POST.line2,
        line3: '',
        suburb: this.profileForm.value.address_POST.place,
        city: this.profileForm.value.address_POST.city.value,
        district: this.profileForm.value.address_POST.district,
        province: this.profileForm.value.address_POST.province,
        country: this.profileForm.value.address_POST.country,
        countryDesc: '',
        postalCode: this.profileForm.value.address_POST.postalCode,
        mailable: true,
        returnToSenderCount: '',
        mailPreference: true,
        landmark: '',
      },
    ];

    let payload: any = this.profileForm.value;
    
    // Clear the ID field that's not being used
    if (this.idType === 'nationalId') {
      payload.passortNum = '';
      payload.issueCountry = '';
      payload.expiryDate = '';
    } else if (this.idType === 'passport') {
      payload.nationalIdNum = '';
      
      // Format passport expiry date if needed
      if (payload.expiryDate) {
        payload.expiryDate = payload.expiryDate + 'T00:00:00';
      }
    }
    
    if (payload.phone) {
      payload.personTelephone = [
        {
          telephoneType: 'CELL',
          countryCode: payload.phone.dialCode,
          telephoneCode: '',
          telephoneNumber: payload.phone.nationalNumber,
          telephoneExtension: '',
          phoneable: '',
          smsable: '',
        },
      ];
    }

    payload.preferenceList = [
      {
        level1: 'PART',
        level2: this.favourite.partnerId,
      },
    ];
    if (payload.birthDate) payload.birthDate = payload.birthDate + 'T00:00:00';
    else if (payload.birthDate === '') delete payload.birthDate;

    console.log('payload', payload);

    this.showLoadingModal('Updating your account').then(() => {
      this.memberService.update(this.kc.lpUniueReference, payload).subscribe({
        error: (error) => {
          console.log('error', error);
          console.log('error', error.message);

          this.dismissLoadingModal();
          this.presentToast({
            message: 'Oops, something went wrong!',
            color: 'danger',
            position: 'bottom',
          }).then();

          // this._formState = this._formStateType.fail;
          // this.error = error.error.detail;
        },
        next: (body) => {
          console.log('BOdy', body);
          this.dismissLoadingModal();
          this.presentToast({
            message: 'Your account has been updated',
            position: 'bottom',
          }).then();
          this.profileForm = body;
          this.router.navigate(['/app/account']);
        },
      });
    });
    // });
  }
}
