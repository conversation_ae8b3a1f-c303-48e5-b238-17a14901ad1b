#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Component definitions
const layoutComponents = [
  'app-layout-switcher', 'bottom-nav', 'head-logo', 'header', 'layout', 'navigation-top', 'sidebar'
];

const pageSectionComponents = [
  'dashboard-header', 'dashboard-quick-actions', 'dashboard-summary', 'dashboard-welcome',
  'home-actions', 'home-header', 'home-navigation', 'profile-form', 'profile-header', 
  'profile-help', 'profile-settings'
];

// Component categorization
const layoutCategories = {
  'app-layout-switcher': { category: 'layout-control', complexity: 5, priority: 'Medium' },
  'bottom-nav': { category: 'navigation', complexity: 5, priority: 'High' },
  'head-logo': { category: 'branding', complexity: 3, priority: 'Medium' },
  'header': { category: 'layout', complexity: 4, priority: 'High' },
  'layout': { category: 'layout', complexity: 6, priority: 'High' },
  'navigation-top': { category: 'navigation', complexity: 5, priority: 'High' },
  'sidebar': { category: 'navigation', complexity: 6, priority: 'High' }
};

const pageSectionCategories = {
  'dashboard-header': { category: 'page-header', complexity: 4, priority: 'High' },
  'dashboard-quick-actions': { category: 'action-section', complexity: 5, priority: 'High' },
  'dashboard-summary': { category: 'content-section', complexity: 6, priority: 'High' },
  'dashboard-welcome': { category: 'content-section', complexity: 4, priority: 'Medium' },
  'home-actions': { category: 'action-section', complexity: 5, priority: 'High' },
  'home-header': { category: 'page-header', complexity: 4, priority: 'High' },
  'home-navigation': { category: 'navigation-section', complexity: 5, priority: 'High' },
  'profile-form': { category: 'form-section', complexity: 7, priority: 'High' },
  'profile-header': { category: 'page-header', complexity: 4, priority: 'Medium' },
  'profile-help': { category: 'content-section', complexity: 3, priority: 'Low' },
  'profile-settings': { category: 'settings-section', complexity: 6, priority: 'Medium' }
};

// Story template generator
function generateStoryContent(componentName, index, prefix, directory, categories) {
  const storyNumber = String(index + 1).padStart(3, '0');
  const titleCase = componentName.split('-').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');
  
  const component = categories[componentName] || { category: 'layout', complexity: 4, priority: 'Medium' };
  const directoryName = directory === 'layout' ? 'Layout' : 'Page Section';

  return `# Story ${prefix}-${storyNumber}: Enhance ${titleCase} ${directoryName} Component

## User Story
As a developer using the LP-GO builder, I want the ${titleCase} ${directoryName.toLowerCase()} component to have full Tailwind class customization and proper input properties so that I can configure and style it dynamically.

## Current State Analysis
- Component needs comprehensive enhancement for dynamic usage
- Missing proper @Input() properties for data binding
- Lacks Tailwind class customization inputs
- Requires default values for visual preview
- Category: ${component.category}

## Acceptance Criteria
- [ ] Add comprehensive @Input() properties for all configurable data
- [ ] Add \`className\` input for custom Tailwind classes
- [ ] Add \`size\` input with appropriate size options
- [ ] Add \`variant\` input for color/style schemes
- [ ] Add \`rounded\` input for border radius options
- [ ] Implement meaningful default values for preview
- [ ] Add proper TypeScript typing for all inputs
- [ ] Update component template with computed classes
- [ ] Ensure component is standalone
- [ ] Add proper module registration if needed
- [ ] Implement responsive design considerations

## Required Standard Inputs
\`\`\`typescript
@Input() className: string = '';
@Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
@Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
@Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
@Input() sticky: boolean = false;
@Input() fixed: boolean = false;
@Input() hidden: boolean = false;
// Additional component-specific inputs to be defined during implementation
\`\`\`

## ${directoryName}-Specific Requirements
- Implement proper layout structure and positioning
- Support responsive design patterns
- Handle layout breakpoints appropriately
- Ensure proper z-index management
- Support theme switching and customization
- Handle accessibility for navigation and layout

## Component-Specific Requirements
- Analyze existing component template and functionality
- Define appropriate data inputs based on component purpose
- Implement component-specific logic and features
- Add event outputs for user interactions
- Ensure proper integration with layout systems

## Default Visual Appearance
- Component should render with meaningful default data
- All styling should have sensible defaults
- Visual preview should represent component's purpose clearly
- Layout should work across different screen sizes

## Implementation Tasks
1. Convert to standalone component (if not already)
2. Add all required @Input() properties
3. Implement computed classes for Tailwind customization
4. Update template with proper data binding
5. Add default values for all inputs
6. Update TypeScript types and interfaces
7. Implement responsive design features
8. Add accessibility features
9. Test component rendering and functionality
10. Update module registration if needed

## Definition of Done
- Component uses @Input() decorators exclusively
- All Tailwind class inputs are functional
- Default values provide meaningful visual preview
- TypeScript compilation successful without errors
- Component renders correctly in isolation
- Responsive design works correctly
- Accessibility requirements met
- Unit tests pass
- Component follows established patterns from enhanced components

---
**Story Points**: ${component.complexity}  
**Priority**: ${component.priority}  
**Category**: ${component.category}  
**Component Path**: \`/projects/mobile-components/src/lib/${directory}/${componentName}/\``;
}

// Generate layout stories
function generateDirectoryStories(components, prefix, directory, categories) {
  const storiesDir = `/Users/<USER>/Projects/clients/lp-angular/docs/stories/${directory}-enhancement`;
  
  if (!fs.existsSync(storiesDir)) {
    fs.mkdirSync(storiesDir, { recursive: true });
  }

  components.forEach((componentName, index) => {
    const storyNumber = String(index + 1).padStart(3, '0');
    const filename = `${prefix}-${storyNumber}-${componentName}.md`;
    const filepath = path.join(storiesDir, filename);
    const content = generateStoryContent(componentName, index, prefix, directory, categories);
    
    try {
      fs.writeFileSync(filepath, content);
      console.log(`Created story: ${filename}`);
    } catch (error) {
      console.error(`Error creating ${filename}:`, error.message);
    }
  });

  console.log(`\n✅ Generated ${components.length} ${directory} enhancement stories`);
  console.log(`📁 Stories saved to: ${storiesDir}`);
  
  const categoryStats = {};
  components.forEach(name => {
    const component = categories[name] || { category: 'layout' };
    categoryStats[component.category] = (categoryStats[component.category] || 0) + 1;
  });

  console.log(`\n📊 ${directory} Story Distribution:`);
  Object.entries(categoryStats).forEach(([category, count]) => {
    console.log(`   ${category}: ${count} stories`);
  });
}

// Generate stories for both directories
console.log('Generating Layout Component Stories...');
generateDirectoryStories(layoutComponents, 'MCL', 'layout', layoutCategories);

console.log('\n' + '='.repeat(50));
console.log('Generating Page Section Component Stories...');
generateDirectoryStories(pageSectionComponents, 'MCPS', 'page-sections', pageSectionCategories);

console.log('\n' + '='.repeat(50));
console.log('📊 Combined Summary:');
console.log(`Layout components: ${layoutComponents.length} stories`);
console.log(`Page section components: ${pageSectionComponents.length} stories`);
console.log(`Total: ${layoutComponents.length + pageSectionComponents.length} stories`);