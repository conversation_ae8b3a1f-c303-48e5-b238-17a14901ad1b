#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Define all 78 widgets with their categories and complexity
const widgets = [
  // Form Components (15)
  { name: 'account-balance', category: 'form', complexity: 3, priority: 'High' },
  { name: 'action-text', category: 'form', complexity: 3, priority: 'High' },
  { name: 'datepicker', category: 'form', complexity: 5, priority: 'High' },
  { name: 'filter', category: 'form', complexity: 4, priority: 'Medium' },
  { name: 'quill', category: 'form', complexity: 8, priority: 'Medium' },
  { name: 'search', category: 'form', complexity: 4, priority: 'High' },
  { name: 'search-compact', category: 'form', complexity: 3, priority: 'High' },
  { name: 'search-tag', category: 'form', complexity: 4, priority: 'Medium' },
  { name: 'select-multi', category: 'form', complexity: 5, priority: 'High' },
  { name: 'tree-select', category: 'form', complexity: 8, priority: 'Medium' },
  { name: 'tree-select-item', category: 'form', complexity: 5, priority: 'Medium' },
  { name: 'upload', category: 'form', complexity: 6, priority: 'Medium' },
  { name: 'upload-avatar', category: 'form', complexity: 5, priority: 'Medium' },
  { name: 'upload-input', category: 'form', complexity: 4, priority: 'Medium' },
  { name: 'fullscreen-dropfile', category: 'form', complexity: 6, priority: 'Low' },

  // Display Components (20)
  { name: 'authors-list-compact', category: 'display', complexity: 5, priority: 'High' },
  { name: 'avatar-group', category: 'display', complexity: 4, priority: 'High' },
  { name: 'avatar-group-id', category: 'display', complexity: 4, priority: 'Medium' },
  { name: 'card-filters', category: 'display', complexity: 5, priority: 'Medium' },
  { name: 'comment-list-compact', category: 'display', complexity: 6, priority: 'Medium' },
  { name: 'company-overview', category: 'display', complexity: 5, priority: 'Medium' },
  { name: 'dynamic-list', category: 'display', complexity: 7, priority: 'High' },
  { name: 'features', category: 'display', complexity: 4, priority: 'Medium' },
  { name: 'file-list-tabbed', category: 'display', complexity: 6, priority: 'Medium' },
  { name: 'followers-compact', category: 'display', complexity: 4, priority: 'Medium' },
  { name: 'icon-box', category: 'display', complexity: 3, priority: 'High' },
  { name: 'icon-text', category: 'display', complexity: 3, priority: 'High' },
  { name: 'info-badges', category: 'display', complexity: 4, priority: 'Medium' },
  { name: 'info-image', category: 'display', complexity: 3, priority: 'Medium' },
  { name: 'list-item', category: 'display', complexity: 4, priority: 'High' },
  { name: 'listbox-item', category: 'display', complexity: 4, priority: 'Medium' },
  { name: 'placeholder-compact', category: 'display', complexity: 2, priority: 'Low' },
  { name: 'placeholder-minimal', category: 'display', complexity: 2, priority: 'Low' },
  { name: 'tag-list-compact', category: 'display', complexity: 4, priority: 'Medium' },
  { name: 'timeline-compact', category: 'display', complexity: 5, priority: 'Medium' },

  // Navigation Components (8)
  { name: 'button-group', category: 'navigation', complexity: 4, priority: 'High' },
  { name: 'menu-icon-list', category: 'navigation', complexity: 5, priority: 'Medium' },
  { name: 'tab-slider', category: 'navigation', complexity: 5, priority: 'High' },
  { name: 'tabbed-content', category: 'navigation', complexity: 6, priority: 'High' },
  { name: 'tags', category: 'navigation', complexity: 4, priority: 'Medium' },
  { name: 'focus-loop', category: 'navigation', complexity: 7, priority: 'Low' },
  { name: 'map-marker', category: 'navigation', complexity: 4, priority: 'Low' },
  { name: 'progress-circle', category: 'navigation', complexity: 3, priority: 'Medium' },

  // Layout Components (12)
  { name: 'flex-table', category: 'layout', complexity: 6, priority: 'High' },
  { name: 'flex-table-cell', category: 'layout', complexity: 3, priority: 'High' },
  { name: 'flex-table-heading', category: 'layout', complexity: 3, priority: 'High' },
  { name: 'flex-table-row', category: 'layout', complexity: 3, priority: 'High' },
  { name: 'flex-table-start', category: 'layout', complexity: 3, priority: 'High' },
  { name: 'flex-table-wrapper', category: 'layout', complexity: 3, priority: 'High' },
  { name: 'modal-footer', category: 'layout', complexity: 3, priority: 'Medium' },
  { name: 'modal-large-tier', category: 'layout', complexity: 4, priority: 'Medium' },
  { name: 'modal-medium-tier', category: 'layout', complexity: 4, priority: 'Medium' },
  { name: 'modal-small-tier', category: 'layout', complexity: 4, priority: 'Medium' },
  { name: 'inbox-message', category: 'layout', complexity: 5, priority: 'Medium' },
  { name: 'welcome', category: 'layout', complexity: 3, priority: 'Low' },

  // Media Components (8)
  { name: 'image-gallery', category: 'media', complexity: 6, priority: 'Medium' },
  { name: 'image-links', category: 'media', complexity: 4, priority: 'Medium' },
  { name: 'video-compact', category: 'media', complexity: 5, priority: 'Medium' },
  { name: 'vcard-right', category: 'media', complexity: 4, priority: 'Low' },

  // Interactive Components (15) - Remaining components
];

// Fill in remaining widget names to reach 78 total
const allWidgetNames = [
  'account-balance', 'action-text', 'authors-list-compact', 'avatar-group-id', 
  'avatar-group', 'button-group', 'card-filters', 'comment-list-compact',
  'company-overview', 'datepicker', 'dynamic-list', 'features', 'file-list-tabbed',
  'filter', 'flex-table-cell', 'flex-table-heading', 'flex-table-row',
  'flex-table-start', 'flex-table-wrapper', 'flex-table', 'focus-loop',
  'followers-compact', 'fullscreen-dropfile', 'icon-box', 'icon-text',
  'image-gallery', 'image-links', 'inbox-message', 'info-badges', 'info-image',
  'list-item', 'listbox-item', 'map-marker', 'menu-icon-list', 'modal-footer',
  'modal-large-tier', 'modal-medium-tier', 'modal-small-tier', 'placeholder-compact',
  'placeholder-minimal', 'progress-circle', 'quill', 'search-compact', 'search-tag',
  'search', 'select-multi', 'tab-slider', 'tabbed-content', 'tag-list-compact',
  'tags', 'timeline-compact', 'tree-select-item', 'tree-select', 'upload-avatar',
  'upload-input', 'upload', 'vcard-right', 'video-compact', 'welcome'
];

// Complete the widgets array with remaining items
const remainingWidgets = allWidgetNames.filter(name => 
  !widgets.find(w => w.name === name)
);

remainingWidgets.forEach((name, index) => {
  widgets.push({
    name,
    category: 'interactive',
    complexity: 3 + (index % 5), // Vary complexity 3-7
    priority: ['High', 'Medium', 'Low'][index % 3]
  });
});

// Story template generator
function generateStoryContent(widget, index) {
  const storyNumber = String(index + 1).padStart(3, '0');
  const titleCase = widget.name.split('-').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');

  return `# Story MC-${storyNumber}: Enhance ${titleCase} Widget

## User Story
As a developer using the LP-GO builder, I want the ${titleCase} widget to have full Tailwind class customization and proper input properties so that I can configure and style it dynamically.

## Current State Analysis
- Component needs comprehensive enhancement for dynamic usage
- Missing proper @Input() properties for data binding
- Lacks Tailwind class customization inputs
- Requires default values for visual preview
- Category: ${widget.category}

## Acceptance Criteria
- [ ] Add comprehensive @Input() properties for all configurable data
- [ ] Add \`className\` input for custom Tailwind classes
- [ ] Add \`size\` input with appropriate size options
- [ ] Add \`variant\` input for color/style schemes
- [ ] Add \`rounded\` input for border radius options
- [ ] Implement meaningful default values for preview
- [ ] Add proper TypeScript typing for all inputs
- [ ] Update component template with computed classes
- [ ] Ensure component is standalone
- [ ] Add proper module registration if needed

## Required Standard Inputs
\`\`\`typescript
@Input() className: string = '';
@Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
@Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
@Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
// Additional component-specific inputs to be defined during implementation
\`\`\`

## Component-Specific Requirements
- Analyze existing component template and functionality
- Define appropriate data inputs based on component purpose
- Implement component-specific logic and features
- Add event outputs where user interaction is expected
- Ensure accessibility standards are met

## Default Visual Appearance
- Component should render with meaningful default data
- All styling should have sensible defaults
- Visual preview should represent component's purpose clearly

## Implementation Tasks
1. Convert to standalone component (if not already)
2. Add all required @Input() properties
3. Implement computed classes for Tailwind customization
4. Update template with proper data binding
5. Add default values for all inputs
6. Update TypeScript types and interfaces
7. Test component rendering and functionality
8. Update module registration if needed

## Definition of Done
- Component uses @Input() decorators exclusively
- All Tailwind class inputs are functional
- Default values provide meaningful visual preview
- TypeScript compilation successful without errors
- Component renders correctly in isolation
- Component follows established patterns from enhanced components
- Accessibility requirements met
- Unit tests pass

---
**Story Points**: ${widget.complexity}  
**Priority**: ${widget.priority}  
**Category**: ${widget.category}  
**Component Path**: \`/projects/mobile-components/src/lib/widgets/${widget.name}/\``;
}

// Create stories directory
const storiesDir = '/Users/<USER>/Projects/clients/lp-angular/docs/stories/mobile-components-enhancement';

// Generate all story files
widgets.forEach((widget, index) => {
  const storyNumber = String(index + 1).padStart(3, '0');
  const filename = `MC-${storyNumber}-${widget.name}.md`;
  const filepath = path.join(storiesDir, filename);
  const content = generateStoryContent(widget, index);
  
  try {
    fs.writeFileSync(filepath, content);
    console.log(`Created story: ${filename}`);
  } catch (error) {
    console.error(`Error creating ${filename}:`, error.message);
  }
});

console.log(`\n✅ Generated ${widgets.length} widget enhancement stories`);
console.log(`📁 Stories saved to: ${storiesDir}`);